1d45085a9cbcdb378ea459128a57af0d
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_9gk90rihy() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\native\\GPUAccelerationManager.ts";
  var hash = "b0ec66d220bfcde4b1d7d88c517e719ce85638e1";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\native\\GPUAccelerationManager.ts",
    statementMap: {
      "0": {
        start: {
          line: 83,
          column: 52
        },
        end: {
          line: 83,
          column: 56
        }
      },
      "1": {
        start: {
          line: 84,
          column: 40
        },
        end: {
          line: 84,
          column: 42
        }
      },
      "2": {
        start: {
          line: 85,
          column: 57
        },
        end: {
          line: 85,
          column: 66
        }
      },
      "3": {
        start: {
          line: 86,
          column: 103
        },
        end: {
          line: 86,
          column: 105
        }
      },
      "4": {
        start: {
          line: 88,
          column: 55
        },
        end: {
          line: 88,
          column: 64
        }
      },
      "5": {
        start: {
          line: 90,
          column: 42
        },
        end: {
          line: 90,
          column: 43
        }
      },
      "6": {
        start: {
          line: 91,
          column: 38
        },
        end: {
          line: 91,
          column: 55
        }
      },
      "7": {
        start: {
          line: 92,
          column: 43
        },
        end: {
          line: 92,
          column: 46
        }
      },
      "8": {
        start: {
          line: 95,
          column: 4
        },
        end: {
          line: 95,
          column: 63
        }
      },
      "9": {
        start: {
          line: 96,
          column: 4
        },
        end: {
          line: 96,
          column: 37
        }
      },
      "10": {
        start: {
          line: 103,
          column: 4
        },
        end: {
          line: 120,
          column: 5
        }
      },
      "11": {
        start: {
          line: 105,
          column: 6
        },
        end: {
          line: 105,
          column: 64
        }
      },
      "12": {
        start: {
          line: 108,
          column: 6
        },
        end: {
          line: 108,
          column: 44
        }
      },
      "13": {
        start: {
          line: 111,
          column: 6
        },
        end: {
          line: 111,
          column: 40
        }
      },
      "14": {
        start: {
          line: 114,
          column: 6
        },
        end: {
          line: 114,
          column: 40
        }
      },
      "15": {
        start: {
          line: 116,
          column: 6
        },
        end: {
          line: 116,
          column: 71
        }
      },
      "16": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 117,
          column: 61
        }
      },
      "17": {
        start: {
          line: 119,
          column: 6
        },
        end: {
          line: 119,
          column: 77
        }
      },
      "18": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 159,
          column: 5
        }
      },
      "19": {
        start: {
          line: 128,
          column: 24
        },
        end: {
          line: 128,
          column: 34
        }
      },
      "20": {
        start: {
          line: 131,
          column: 6
        },
        end: {
          line: 133,
          column: 7
        }
      },
      "21": {
        start: {
          line: 132,
          column: 8
        },
        end: {
          line: 132,
          column: 60
        }
      },
      "22": {
        start: {
          line: 136,
          column: 21
        },
        end: {
          line: 136,
          column: 50
        }
      },
      "23": {
        start: {
          line: 140,
          column: 6
        },
        end: {
          line: 146,
          column: 7
        }
      },
      "24": {
        start: {
          line: 141,
          column: 8
        },
        end: {
          line: 141,
          column: 47
        }
      },
      "25": {
        start: {
          line: 142,
          column: 13
        },
        end: {
          line: 146,
          column: 7
        }
      },
      "26": {
        start: {
          line: 143,
          column: 8
        },
        end: {
          line: 143,
          column: 47
        }
      },
      "27": {
        start: {
          line: 145,
          column: 8
        },
        end: {
          line: 145,
          column: 71
        }
      },
      "28": {
        start: {
          line: 149,
          column: 6
        },
        end: {
          line: 149,
          column: 49
        }
      },
      "29": {
        start: {
          line: 151,
          column: 24
        },
        end: {
          line: 151,
          column: 46
        }
      },
      "30": {
        start: {
          line: 152,
          column: 6
        },
        end: {
          line: 152,
          column: 75
        }
      },
      "31": {
        start: {
          line: 154,
          column: 6
        },
        end: {
          line: 154,
          column: 20
        }
      },
      "32": {
        start: {
          line: 157,
          column: 6
        },
        end: {
          line: 157,
          column: 62
        }
      },
      "33": {
        start: {
          line: 158,
          column: 6
        },
        end: {
          line: 158,
          column: 49
        }
      },
      "34": {
        start: {
          line: 166,
          column: 37
        },
        end: {
          line: 186,
          column: 5
        }
      },
      "35": {
        start: {
          line: 188,
          column: 4
        },
        end: {
          line: 188,
          column: 54
        }
      },
      "36": {
        start: {
          line: 195,
          column: 37
        },
        end: {
          line: 215,
          column: 5
        }
      },
      "37": {
        start: {
          line: 207,
          column: 62
        },
        end: {
          line: 207,
          column: 67
        }
      },
      "38": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 217,
          column: 54
        }
      },
      "39": {
        start: {
          line: 232,
          column: 24
        },
        end: {
          line: 232,
          column: 59
        }
      },
      "40": {
        start: {
          line: 233,
          column: 28
        },
        end: {
          line: 233,
          column: 77
        }
      },
      "41": {
        start: {
          line: 233,
          column: 56
        },
        end: {
          line: 233,
          column: 76
        }
      },
      "42": {
        start: {
          line: 235,
          column: 33
        },
        end: {
          line: 237,
          column: 9
        }
      },
      "43": {
        start: {
          line: 236,
          column: 47
        },
        end: {
          line: 236,
          column: 79
        }
      },
      "44": {
        start: {
          line: 239,
          column: 27
        },
        end: {
          line: 241,
          column: 9
        }
      },
      "45": {
        start: {
          line: 243,
          column: 23
        },
        end: {
          line: 245,
          column: 9
        }
      },
      "46": {
        start: {
          line: 244,
          column: 47
        },
        end: {
          line: 244,
          column: 88
        }
      },
      "47": {
        start: {
          line: 247,
          column: 22
        },
        end: {
          line: 249,
          column: 9
        }
      },
      "48": {
        start: {
          line: 251,
          column: 4
        },
        end: {
          line: 259,
          column: 6
        }
      },
      "49": {
        start: {
          line: 266,
          column: 4
        },
        end: {
          line: 289,
          column: 5
        }
      },
      "50": {
        start: {
          line: 267,
          column: 6
        },
        end: {
          line: 267,
          column: 51
        }
      },
      "51": {
        start: {
          line: 270,
          column: 22
        },
        end: {
          line: 270,
          column: 53
        }
      },
      "52": {
        start: {
          line: 273,
          column: 6
        },
        end: {
          line: 275,
          column: 7
        }
      },
      "53": {
        start: {
          line: 274,
          column: 8
        },
        end: {
          line: 274,
          column: 41
        }
      },
      "54": {
        start: {
          line: 278,
          column: 6
        },
        end: {
          line: 280,
          column: 7
        }
      },
      "55": {
        start: {
          line: 279,
          column: 8
        },
        end: {
          line: 279,
          column: 42
        }
      },
      "56": {
        start: {
          line: 283,
          column: 6
        },
        end: {
          line: 283,
          column: 40
        }
      },
      "57": {
        start: {
          line: 285,
          column: 6
        },
        end: {
          line: 285,
          column: 60
        }
      },
      "58": {
        start: {
          line: 288,
          column: 6
        },
        end: {
          line: 288,
          column: 66
        }
      },
      "59": {
        start: {
          line: 296,
          column: 46
        },
        end: {
          line: 309,
          column: 5
        }
      },
      "60": {
        start: {
          line: 311,
          column: 4
        },
        end: {
          line: 311,
          column: 28
        }
      },
      "61": {
        start: {
          line: 316,
          column: 26
        },
        end: {
          line: 321,
          column: 5
        }
      },
      "62": {
        start: {
          line: 323,
          column: 4
        },
        end: {
          line: 327,
          column: 5
        }
      },
      "63": {
        start: {
          line: 324,
          column: 21
        },
        end: {
          line: 324,
          column: 64
        }
      },
      "64": {
        start: {
          line: 325,
          column: 6
        },
        end: {
          line: 325,
          column: 32
        }
      },
      "65": {
        start: {
          line: 326,
          column: 6
        },
        end: {
          line: 326,
          column: 51
        }
      },
      "66": {
        start: {
          line: 332,
          column: 4
        },
        end: {
          line: 334,
          column: 5
        }
      },
      "67": {
        start: {
          line: 333,
          column: 6
        },
        end: {
          line: 333,
          column: 19
        }
      },
      "68": {
        start: {
          line: 337,
          column: 4
        },
        end: {
          line: 339,
          column: 5
        }
      },
      "69": {
        start: {
          line: 338,
          column: 6
        },
        end: {
          line: 338,
          column: 19
        }
      },
      "70": {
        start: {
          line: 342,
          column: 27
        },
        end: {
          line: 342,
          column: 122
        }
      },
      "71": {
        start: {
          line: 343,
          column: 4
        },
        end: {
          line: 345,
          column: 5
        }
      },
      "72": {
        start: {
          line: 344,
          column: 6
        },
        end: {
          line: 344,
          column: 19
        }
      },
      "73": {
        start: {
          line: 347,
          column: 4
        },
        end: {
          line: 347,
          column: 16
        }
      },
      "74": {
        start: {
          line: 351,
          column: 4
        },
        end: {
          line: 353,
          column: 5
        }
      },
      "75": {
        start: {
          line: 352,
          column: 6
        },
        end: {
          line: 352,
          column: 19
        }
      },
      "76": {
        start: {
          line: 356,
          column: 4
        },
        end: {
          line: 358,
          column: 5
        }
      },
      "77": {
        start: {
          line: 357,
          column: 6
        },
        end: {
          line: 357,
          column: 18
        }
      },
      "78": {
        start: {
          line: 361,
          column: 31
        },
        end: {
          line: 361,
          column: 85
        }
      },
      "79": {
        start: {
          line: 362,
          column: 4
        },
        end: {
          line: 364,
          column: 5
        }
      },
      "80": {
        start: {
          line: 363,
          column: 6
        },
        end: {
          line: 363,
          column: 19
        }
      },
      "81": {
        start: {
          line: 367,
          column: 30
        },
        end: {
          line: 367,
          column: 41
        }
      },
      "82": {
        start: {
          line: 368,
          column: 4
        },
        end: {
          line: 370,
          column: 5
        }
      },
      "83": {
        start: {
          line: 369,
          column: 6
        },
        end: {
          line: 369,
          column: 19
        }
      },
      "84": {
        start: {
          line: 373,
          column: 24
        },
        end: {
          line: 373,
          column: 54
        }
      },
      "85": {
        start: {
          line: 374,
          column: 4
        },
        end: {
          line: 376,
          column: 5
        }
      },
      "86": {
        start: {
          line: 375,
          column: 6
        },
        end: {
          line: 375,
          column: 19
        }
      },
      "87": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 378,
          column: 16
        }
      },
      "88": {
        start: {
          line: 382,
          column: 22
        },
        end: {
          line: 382,
          column: 32
        }
      },
      "89": {
        start: {
          line: 384,
          column: 4
        },
        end: {
          line: 433,
          column: 5
        }
      },
      "90": {
        start: {
          line: 386,
          column: 26
        },
        end: {
          line: 386,
          column: 84
        }
      },
      "91": {
        start: {
          line: 387,
          column: 27
        },
        end: {
          line: 387,
          column: 87
        }
      },
      "92": {
        start: {
          line: 390,
          column: 6
        },
        end: {
          line: 390,
          column: 57
        }
      },
      "93": {
        start: {
          line: 393,
          column: 21
        },
        end: {
          line: 393,
          column: 55
        }
      },
      "94": {
        start: {
          line: 394,
          column: 6
        },
        end: {
          line: 396,
          column: 7
        }
      },
      "95": {
        start: {
          line: 395,
          column: 8
        },
        end: {
          line: 395,
          column: 83
        }
      },
      "96": {
        start: {
          line: 399,
          column: 24
        },
        end: {
          line: 399,
          column: 93
        }
      },
      "97": {
        start: {
          line: 402,
          column: 21
        },
        end: {
          line: 402,
          column: 87
        }
      },
      "98": {
        start: {
          line: 405,
          column: 6
        },
        end: {
          line: 405,
          column: 46
        }
      },
      "99": {
        start: {
          line: 406,
          column: 6
        },
        end: {
          line: 406,
          column: 47
        }
      },
      "100": {
        start: {
          line: 408,
          column: 28
        },
        end: {
          line: 408,
          column: 50
        }
      },
      "101": {
        start: {
          line: 410,
          column: 6
        },
        end: {
          line: 422,
          column: 8
        }
      },
      "102": {
        start: {
          line: 425,
          column: 6
        },
        end: {
          line: 425,
          column: 52
        }
      },
      "103": {
        start: {
          line: 428,
          column: 6
        },
        end: {
          line: 430,
          column: 7
        }
      },
      "104": {
        start: {
          line: 429,
          column: 8
        },
        end: {
          line: 429,
          column: 45
        }
      },
      "105": {
        start: {
          line: 432,
          column: 6
        },
        end: {
          line: 432,
          column: 18
        }
      },
      "106": {
        start: {
          line: 437,
          column: 22
        },
        end: {
          line: 437,
          column: 32
        }
      },
      "107": {
        start: {
          line: 439,
          column: 4
        },
        end: {
          line: 459,
          column: 5
        }
      },
      "108": {
        start: {
          line: 441,
          column: 21
        },
        end: {
          line: 441,
          column: 58
        }
      },
      "109": {
        start: {
          line: 442,
          column: 28
        },
        end: {
          line: 442,
          column: 50
        }
      },
      "110": {
        start: {
          line: 444,
          column: 6
        },
        end: {
          line: 455,
          column: 8
        }
      },
      "111": {
        start: {
          line: 458,
          column: 6
        },
        end: {
          line: 458,
          column: 18
        }
      },
      "112": {
        start: {
          line: 464,
          column: 27
        },
        end: {
          line: 464,
          column: 67
        }
      },
      "113": {
        start: {
          line: 465,
          column: 4
        },
        end: {
          line: 465,
          column: 70
        }
      },
      "114": {
        start: {
          line: 465,
          column: 33
        },
        end: {
          line: 465,
          column: 68
        }
      },
      "115": {
        start: {
          line: 468,
          column: 4
        },
        end: {
          line: 468,
          column: 57
        }
      },
      "116": {
        start: {
          line: 472,
          column: 21
        },
        end: {
          line: 472,
          column: 69
        }
      },
      "117": {
        start: {
          line: 473,
          column: 23
        },
        end: {
          line: 473,
          column: 49
        }
      },
      "118": {
        start: {
          line: 475,
          column: 28
        },
        end: {
          line: 481,
          column: 5
        }
      },
      "119": {
        start: {
          line: 483,
          column: 4
        },
        end: {
          line: 483,
          column: 71
        }
      },
      "120": {
        start: {
          line: 487,
          column: 30
        },
        end: {
          line: 487,
          column: 45
        }
      },
      "121": {
        start: {
          line: 488,
          column: 23
        },
        end: {
          line: 488,
          column: 82
        }
      },
      "122": {
        start: {
          line: 490,
          column: 4
        },
        end: {
          line: 501,
          column: 5
        }
      },
      "123": {
        start: {
          line: 492,
          column: 8
        },
        end: {
          line: 492,
          column: 39
        }
      },
      "124": {
        start: {
          line: 494,
          column: 8
        },
        end: {
          line: 494,
          column: 33
        }
      },
      "125": {
        start: {
          line: 496,
          column: 8
        },
        end: {
          line: 496,
          column: 32
        }
      },
      "126": {
        start: {
          line: 498,
          column: 8
        },
        end: {
          line: 498,
          column: 64
        }
      },
      "127": {
        start: {
          line: 500,
          column: 8
        },
        end: {
          line: 500,
          column: 32
        }
      },
      "128": {
        start: {
          line: 505,
          column: 21
        },
        end: {
          line: 505,
          column: 47
        }
      },
      "129": {
        start: {
          line: 506,
          column: 4
        },
        end: {
          line: 506,
          column: 61
        }
      },
      "130": {
        start: {
          line: 511,
          column: 24
        },
        end: {
          line: 511,
          column: 52
        }
      },
      "131": {
        start: {
          line: 512,
          column: 4
        },
        end: {
          line: 512,
          column: 59
        }
      },
      "132": {
        start: {
          line: 517,
          column: 4
        },
        end: {
          line: 517,
          column: 57
        }
      },
      "133": {
        start: {
          line: 517,
          column: 33
        },
        end: {
          line: 517,
          column: 55
        }
      },
      "134": {
        start: {
          line: 522,
          column: 4
        },
        end: {
          line: 522,
          column: 57
        }
      },
      "135": {
        start: {
          line: 522,
          column: 33
        },
        end: {
          line: 522,
          column: 55
        }
      },
      "136": {
        start: {
          line: 523,
          column: 4
        },
        end: {
          line: 523,
          column: 33
        }
      },
      "137": {
        start: {
          line: 527,
          column: 4
        },
        end: {
          line: 540,
          column: 6
        }
      },
      "138": {
        start: {
          line: 544,
          column: 4
        },
        end: {
          line: 548,
          column: 7
        }
      },
      "139": {
        start: {
          line: 551,
          column: 4
        },
        end: {
          line: 553,
          column: 5
        }
      },
      "140": {
        start: {
          line: 552,
          column: 6
        },
        end: {
          line: 552,
          column: 38
        }
      },
      "141": {
        start: {
          line: 558,
          column: 4
        },
        end: {
          line: 560,
          column: 12
        }
      },
      "142": {
        start: {
          line: 559,
          column: 6
        },
        end: {
          line: 559,
          column: 33
        }
      },
      "143": {
        start: {
          line: 564,
          column: 4
        },
        end: {
          line: 566,
          column: 5
        }
      },
      "144": {
        start: {
          line: 565,
          column: 6
        },
        end: {
          line: 565,
          column: 13
        }
      },
      "145": {
        start: {
          line: 569,
          column: 4
        },
        end: {
          line: 572,
          column: 7
        }
      },
      "146": {
        start: {
          line: 570,
          column: 28
        },
        end: {
          line: 570,
          column: 71
        }
      },
      "147": {
        start: {
          line: 571,
          column: 6
        },
        end: {
          line: 571,
          column: 67
        }
      },
      "148": {
        start: {
          line: 575,
          column: 17
        },
        end: {
          line: 575,
          column: 42
        }
      },
      "149": {
        start: {
          line: 576,
          column: 4
        },
        end: {
          line: 584,
          column: 5
        }
      },
      "150": {
        start: {
          line: 577,
          column: 6
        },
        end: {
          line: 577,
          column: 49
        }
      },
      "151": {
        start: {
          line: 579,
          column: 6
        },
        end: {
          line: 583,
          column: 7
        }
      },
      "152": {
        start: {
          line: 580,
          column: 8
        },
        end: {
          line: 580,
          column: 44
        }
      },
      "153": {
        start: {
          line: 582,
          column: 8
        },
        end: {
          line: 582,
          column: 48
        }
      },
      "154": {
        start: {
          line: 589,
          column: 4
        },
        end: {
          line: 589,
          column: 55
        }
      },
      "155": {
        start: {
          line: 594,
          column: 4
        },
        end: {
          line: 594,
          column: 44
        }
      },
      "156": {
        start: {
          line: 599,
          column: 4
        },
        end: {
          line: 601,
          column: 14
        }
      },
      "157": {
        start: {
          line: 600,
          column: 6
        },
        end: {
          line: 600,
          column: 35
        }
      },
      "158": {
        start: {
          line: 605,
          column: 20
        },
        end: {
          line: 605,
          column: 51
        }
      },
      "159": {
        start: {
          line: 608,
          column: 4
        },
        end: {
          line: 615,
          column: 7
        }
      },
      "160": {
        start: {
          line: 624,
          column: 29
        },
        end: {
          line: 624,
          column: 30
        }
      },
      "161": {
        start: {
          line: 625,
          column: 74
        },
        end: {
          line: 625,
          column: 83
        }
      },
      "162": {
        start: {
          line: 628,
          column: 4
        },
        end: {
          line: 628,
          column: 26
        }
      },
      "163": {
        start: {
          line: 632,
          column: 4
        },
        end: {
          line: 634,
          column: 5
        }
      },
      "164": {
        start: {
          line: 633,
          column: 6
        },
        end: {
          line: 633,
          column: 45
        }
      },
      "165": {
        start: {
          line: 636,
          column: 15
        },
        end: {
          line: 636,
          column: 79
        }
      },
      "166": {
        start: {
          line: 637,
          column: 4
        },
        end: {
          line: 637,
          column: 62
        }
      },
      "167": {
        start: {
          line: 638,
          column: 4
        },
        end: {
          line: 638,
          column: 26
        }
      },
      "168": {
        start: {
          line: 640,
          column: 4
        },
        end: {
          line: 640,
          column: 14
        }
      },
      "169": {
        start: {
          line: 644,
          column: 23
        },
        end: {
          line: 644,
          column: 47
        }
      },
      "170": {
        start: {
          line: 645,
          column: 4
        },
        end: {
          line: 648,
          column: 5
        }
      },
      "171": {
        start: {
          line: 646,
          column: 6
        },
        end: {
          line: 646,
          column: 39
        }
      },
      "172": {
        start: {
          line: 647,
          column: 6
        },
        end: {
          line: 647,
          column: 34
        }
      },
      "173": {
        start: {
          line: 652,
          column: 4
        },
        end: {
          line: 652,
          column: 50
        }
      },
      "174": {
        start: {
          line: 657,
          column: 27
        },
        end: {
          line: 657,
          column: 46
        }
      },
      "175": {
        start: {
          line: 659,
          column: 4
        },
        end: {
          line: 663,
          column: 5
        }
      },
      "176": {
        start: {
          line: 660,
          column: 6
        },
        end: {
          line: 662,
          column: 7
        }
      },
      "177": {
        start: {
          line: 661,
          column: 8
        },
        end: {
          line: 661,
          column: 28
        }
      },
      "178": {
        start: {
          line: 667,
          column: 30
        },
        end: {
          line: 668,
          column: 54
        }
      },
      "179": {
        start: {
          line: 668,
          column: 22
        },
        end: {
          line: 668,
          column: 53
        }
      },
      "180": {
        start: {
          line: 670,
          column: 20
        },
        end: {
          line: 670,
          column: 21
        }
      },
      "181": {
        start: {
          line: 671,
          column: 4
        },
        end: {
          line: 678,
          column: 5
        }
      },
      "182": {
        start: {
          line: 672,
          column: 6
        },
        end: {
          line: 672,
          column: 26
        }
      },
      "183": {
        start: {
          line: 673,
          column: 6
        },
        end: {
          line: 673,
          column: 35
        }
      },
      "184": {
        start: {
          line: 675,
          column: 6
        },
        end: {
          line: 677,
          column: 7
        }
      },
      "185": {
        start: {
          line: 676,
          column: 8
        },
        end: {
          line: 676,
          column: 14
        }
      },
      "186": {
        start: {
          line: 688,
          column: 26
        },
        end: {
          line: 688,
          column: 31
        }
      },
      "187": {
        start: {
          line: 691,
          column: 4
        },
        end: {
          line: 691,
          column: 21
        }
      },
      "188": {
        start: {
          line: 692,
          column: 4
        },
        end: {
          line: 692,
          column: 21
        }
      },
      "189": {
        start: {
          line: 697,
          column: 4
        },
        end: {
          line: 697,
          column: 30
        }
      },
      "190": {
        start: {
          line: 698,
          column: 4
        },
        end: {
          line: 698,
          column: 60
        }
      },
      "191": {
        start: {
          line: 706,
          column: 4
        },
        end: {
          line: 708,
          column: 5
        }
      },
      "192": {
        start: {
          line: 707,
          column: 6
        },
        end: {
          line: 707,
          column: 70
        }
      },
      "193": {
        start: {
          line: 711,
          column: 26
        },
        end: {
          line: 711,
          column: 49
        }
      },
      "194": {
        start: {
          line: 712,
          column: 4
        },
        end: {
          line: 712,
          column: 69
        }
      },
      "195": {
        start: {
          line: 712,
          column: 33
        },
        end: {
          line: 712,
          column: 67
        }
      },
      "196": {
        start: {
          line: 714,
          column: 4
        },
        end: {
          line: 717,
          column: 6
        }
      },
      "197": {
        start: {
          line: 722,
          column: 38
        },
        end: {
          line: 722,
          column: 66
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 94,
            column: 2
          },
          end: {
            line: 94,
            column: 3
          }
        },
        loc: {
          start: {
            line: 94,
            column: 16
          },
          end: {
            line: 97,
            column: 3
          }
        },
        line: 94
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 102,
            column: 2
          },
          end: {
            line: 102,
            column: 3
          }
        },
        loc: {
          start: {
            line: 102,
            column: 59
          },
          end: {
            line: 121,
            column: 3
          }
        },
        line: 102
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 126,
            column: 2
          },
          end: {
            line: 126,
            column: 3
          }
        },
        loc: {
          start: {
            line: 126,
            column: 70
          },
          end: {
            line: 160,
            column: 3
          }
        },
        line: 126
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 165,
            column: 2
          },
          end: {
            line: 165,
            column: 3
          }
        },
        loc: {
          start: {
            line: 165,
            column: 70
          },
          end: {
            line: 189,
            column: 3
          }
        },
        line: 165
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 194,
            column: 2
          },
          end: {
            line: 194,
            column: 3
          }
        },
        loc: {
          start: {
            line: 194,
            column: 70
          },
          end: {
            line: 218,
            column: 3
          }
        },
        line: 194
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 207,
            column: 52
          },
          end: {
            line: 207,
            column: 53
          }
        },
        loc: {
          start: {
            line: 207,
            column: 62
          },
          end: {
            line: 207,
            column: 67
          }
        },
        line: 207
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 223,
            column: 2
          },
          end: {
            line: 223,
            column: 3
          }
        },
        loc: {
          start: {
            line: 231,
            column: 4
          },
          end: {
            line: 260,
            column: 3
          }
        },
        line: 231
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 233,
            column: 47
          },
          end: {
            line: 233,
            column: 48
          }
        },
        loc: {
          start: {
            line: 233,
            column: 56
          },
          end: {
            line: 233,
            column: 76
          }
        },
        line: 233
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 236,
            column: 31
          },
          end: {
            line: 236,
            column: 32
          }
        },
        loc: {
          start: {
            line: 236,
            column: 47
          },
          end: {
            line: 236,
            column: 79
          }
        },
        line: 236
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 244,
            column: 31
          },
          end: {
            line: 244,
            column: 32
          }
        },
        loc: {
          start: {
            line: 244,
            column: 47
          },
          end: {
            line: 244,
            column: 88
          }
        },
        line: 244
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 265,
            column: 2
          },
          end: {
            line: 265,
            column: 3
          }
        },
        loc: {
          start: {
            line: 265,
            column: 48
          },
          end: {
            line: 290,
            column: 3
          }
        },
        line: 265
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 294,
            column: 2
          },
          end: {
            line: 294,
            column: 3
          }
        },
        loc: {
          start: {
            line: 294,
            column: 66
          },
          end: {
            line: 312,
            column: 3
          }
        },
        line: 294
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 314,
            column: 2
          },
          end: {
            line: 314,
            column: 3
          }
        },
        loc: {
          start: {
            line: 314,
            column: 58
          },
          end: {
            line: 328,
            column: 3
          }
        },
        line: 314
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 330,
            column: 2
          },
          end: {
            line: 330,
            column: 3
          }
        },
        loc: {
          start: {
            line: 330,
            column: 58
          },
          end: {
            line: 348,
            column: 3
          }
        },
        line: 330
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 350,
            column: 2
          },
          end: {
            line: 350,
            column: 3
          }
        },
        loc: {
          start: {
            line: 350,
            column: 66
          },
          end: {
            line: 379,
            column: 3
          }
        },
        line: 350
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 381,
            column: 2
          },
          end: {
            line: 381,
            column: 3
          }
        },
        loc: {
          start: {
            line: 381,
            column: 72
          },
          end: {
            line: 434,
            column: 3
          }
        },
        line: 381
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 436,
            column: 2
          },
          end: {
            line: 436,
            column: 3
          }
        },
        loc: {
          start: {
            line: 436,
            column: 72
          },
          end: {
            line: 460,
            column: 3
          }
        },
        line: 436
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 462,
            column: 2
          },
          end: {
            line: 462,
            column: 3
          }
        },
        loc: {
          start: {
            line: 462,
            column: 78
          },
          end: {
            line: 469,
            column: 3
          }
        },
        line: 462
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 465,
            column: 22
          },
          end: {
            line: 465,
            column: 23
          }
        },
        loc: {
          start: {
            line: 465,
            column: 33
          },
          end: {
            line: 465,
            column: 68
          }
        },
        line: 465
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 471,
            column: 2
          },
          end: {
            line: 471,
            column: 3
          }
        },
        loc: {
          start: {
            line: 471,
            column: 81
          },
          end: {
            line: 484,
            column: 3
          }
        },
        line: 471
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 486,
            column: 2
          },
          end: {
            line: 486,
            column: 3
          }
        },
        loc: {
          start: {
            line: 486,
            column: 75
          },
          end: {
            line: 502,
            column: 3
          }
        },
        line: 486
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 504,
            column: 2
          },
          end: {
            line: 504,
            column: 3
          }
        },
        loc: {
          start: {
            line: 504,
            column: 80
          },
          end: {
            line: 507,
            column: 3
          }
        },
        line: 504
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 509,
            column: 2
          },
          end: {
            line: 509,
            column: 3
          }
        },
        loc: {
          start: {
            line: 509,
            column: 44
          },
          end: {
            line: 513,
            column: 3
          }
        },
        line: 509
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 515,
            column: 2
          },
          end: {
            line: 515,
            column: 3
          }
        },
        loc: {
          start: {
            line: 515,
            column: 76
          },
          end: {
            line: 518,
            column: 3
          }
        },
        line: 515
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 517,
            column: 22
          },
          end: {
            line: 517,
            column: 23
          }
        },
        loc: {
          start: {
            line: 517,
            column: 33
          },
          end: {
            line: 517,
            column: 55
          }
        },
        line: 517
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 520,
            column: 2
          },
          end: {
            line: 520,
            column: 3
          }
        },
        loc: {
          start: {
            line: 520,
            column: 80
          },
          end: {
            line: 524,
            column: 3
          }
        },
        line: 520
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 522,
            column: 22
          },
          end: {
            line: 522,
            column: 23
          }
        },
        loc: {
          start: {
            line: 522,
            column: 33
          },
          end: {
            line: 522,
            column: 55
          }
        },
        line: 522
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 526,
            column: 2
          },
          end: {
            line: 526,
            column: 3
          }
        },
        loc: {
          start: {
            line: 526,
            column: 74
          },
          end: {
            line: 541,
            column: 3
          }
        },
        line: 526
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 543,
            column: 2
          },
          end: {
            line: 543,
            column: 3
          }
        },
        loc: {
          start: {
            line: 543,
            column: 82
          },
          end: {
            line: 554,
            column: 3
          }
        },
        line: 543
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 556,
            column: 2
          },
          end: {
            line: 556,
            column: 3
          }
        },
        loc: {
          start: {
            line: 556,
            column: 45
          },
          end: {
            line: 561,
            column: 3
          }
        },
        line: 556
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 558,
            column: 16
          },
          end: {
            line: 558,
            column: 17
          }
        },
        loc: {
          start: {
            line: 558,
            column: 22
          },
          end: {
            line: 560,
            column: 5
          }
        },
        line: 558
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 563,
            column: 2
          },
          end: {
            line: 563,
            column: 3
          }
        },
        loc: {
          start: {
            line: 563,
            column: 53
          },
          end: {
            line: 585,
            column: 3
          }
        },
        line: 563
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 569,
            column: 27
          },
          end: {
            line: 569,
            column: 28
          }
        },
        loc: {
          start: {
            line: 569,
            column: 37
          },
          end: {
            line: 572,
            column: 5
          }
        },
        line: 569
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 587,
            column: 2
          },
          end: {
            line: 587,
            column: 3
          }
        },
        loc: {
          start: {
            line: 587,
            column: 54
          },
          end: {
            line: 590,
            column: 3
          }
        },
        line: 587
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 592,
            column: 2
          },
          end: {
            line: 592,
            column: 3
          }
        },
        loc: {
          start: {
            line: 592,
            column: 54
          },
          end: {
            line: 595,
            column: 3
          }
        },
        line: 592
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 597,
            column: 2
          },
          end: {
            line: 597,
            column: 3
          }
        },
        loc: {
          start: {
            line: 597,
            column: 45
          },
          end: {
            line: 602,
            column: 3
          }
        },
        line: 597
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 599,
            column: 16
          },
          end: {
            line: 599,
            column: 17
          }
        },
        loc: {
          start: {
            line: 599,
            column: 22
          },
          end: {
            line: 601,
            column: 5
          }
        },
        line: 599
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 604,
            column: 2
          },
          end: {
            line: 604,
            column: 3
          }
        },
        loc: {
          start: {
            line: 604,
            column: 40
          },
          end: {
            line: 616,
            column: 3
          }
        },
        line: 604
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 627,
            column: 2
          },
          end: {
            line: 627,
            column: 3
          }
        },
        loc: {
          start: {
            line: 627,
            column: 28
          },
          end: {
            line: 629,
            column: 3
          }
        },
        line: 627
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 631,
            column: 2
          },
          end: {
            line: 631,
            column: 3
          }
        },
        loc: {
          start: {
            line: 631,
            column: 48
          },
          end: {
            line: 641,
            column: 3
          }
        },
        line: 631
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 643,
            column: 2
          },
          end: {
            line: 643,
            column: 3
          }
        },
        loc: {
          start: {
            line: 643,
            column: 40
          },
          end: {
            line: 649,
            column: 3
          }
        },
        line: 643
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 651,
            column: 2
          },
          end: {
            line: 651,
            column: 3
          }
        },
        loc: {
          start: {
            line: 651,
            column: 31
          },
          end: {
            line: 653,
            column: 3
          }
        },
        line: 651
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 655,
            column: 2
          },
          end: {
            line: 655,
            column: 3
          }
        },
        loc: {
          start: {
            line: 655,
            column: 34
          },
          end: {
            line: 664,
            column: 3
          }
        },
        line: 655
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 666,
            column: 2
          },
          end: {
            line: 666,
            column: 3
          }
        },
        loc: {
          start: {
            line: 666,
            column: 75
          },
          end: {
            line: 679,
            column: 3
          }
        },
        line: 666
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 668,
            column: 12
          },
          end: {
            line: 668,
            column: 13
          }
        },
        loc: {
          start: {
            line: 668,
            column: 22
          },
          end: {
            line: 668,
            column: 53
          }
        },
        line: 668
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 690,
            column: 2
          },
          end: {
            line: 690,
            column: 3
          }
        },
        loc: {
          start: {
            line: 690,
            column: 42
          },
          end: {
            line: 693,
            column: 3
          }
        },
        line: 690
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 695,
            column: 2
          },
          end: {
            line: 695,
            column: 3
          }
        },
        loc: {
          start: {
            line: 695,
            column: 36
          },
          end: {
            line: 699,
            column: 3
          }
        },
        line: 695
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 701,
            column: 2
          },
          end: {
            line: 701,
            column: 3
          }
        },
        loc: {
          start: {
            line: 705,
            column: 56
          },
          end: {
            line: 718,
            column: 3
          }
        },
        line: 705
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 712,
            column: 22
          },
          end: {
            line: 712,
            column: 23
          }
        },
        loc: {
          start: {
            line: 712,
            column: 33
          },
          end: {
            line: 712,
            column: 67
          }
        },
        line: 712
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 131,
            column: 6
          },
          end: {
            line: 133,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 131,
            column: 6
          },
          end: {
            line: 133,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 131
      },
      "1": {
        loc: {
          start: {
            line: 140,
            column: 6
          },
          end: {
            line: 146,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 140,
            column: 6
          },
          end: {
            line: 146,
            column: 7
          }
        }, {
          start: {
            line: 142,
            column: 13
          },
          end: {
            line: 146,
            column: 7
          }
        }],
        line: 140
      },
      "2": {
        loc: {
          start: {
            line: 140,
            column: 10
          },
          end: {
            line: 140,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 140,
            column: 10
          },
          end: {
            line: 140,
            column: 16
          }
        }, {
          start: {
            line: 140,
            column: 20
          },
          end: {
            line: 140,
            column: 57
          }
        }],
        line: 140
      },
      "3": {
        loc: {
          start: {
            line: 142,
            column: 13
          },
          end: {
            line: 146,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 13
          },
          end: {
            line: 146,
            column: 7
          }
        }, {
          start: {
            line: 144,
            column: 13
          },
          end: {
            line: 146,
            column: 7
          }
        }],
        line: 142
      },
      "4": {
        loc: {
          start: {
            line: 235,
            column: 33
          },
          end: {
            line: 237,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 236,
            column: 8
          },
          end: {
            line: 236,
            column: 108
          }
        }, {
          start: {
            line: 237,
            column: 8
          },
          end: {
            line: 237,
            column: 9
          }
        }],
        line: 235
      },
      "5": {
        loc: {
          start: {
            line: 239,
            column: 27
          },
          end: {
            line: 241,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 240,
            column: 8
          },
          end: {
            line: 240,
            column: 77
          }
        }, {
          start: {
            line: 241,
            column: 8
          },
          end: {
            line: 241,
            column: 9
          }
        }],
        line: 239
      },
      "6": {
        loc: {
          start: {
            line: 243,
            column: 23
          },
          end: {
            line: 245,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 244,
            column: 8
          },
          end: {
            line: 244,
            column: 117
          }
        }, {
          start: {
            line: 245,
            column: 8
          },
          end: {
            line: 245,
            column: 9
          }
        }],
        line: 243
      },
      "7": {
        loc: {
          start: {
            line: 247,
            column: 22
          },
          end: {
            line: 249,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 248,
            column: 8
          },
          end: {
            line: 248,
            column: 80
          }
        }, {
          start: {
            line: 249,
            column: 8
          },
          end: {
            line: 249,
            column: 9
          }
        }],
        line: 247
      },
      "8": {
        loc: {
          start: {
            line: 273,
            column: 6
          },
          end: {
            line: 275,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 273,
            column: 6
          },
          end: {
            line: 275,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 273
      },
      "9": {
        loc: {
          start: {
            line: 278,
            column: 6
          },
          end: {
            line: 280,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 278,
            column: 6
          },
          end: {
            line: 280,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 278
      },
      "10": {
        loc: {
          start: {
            line: 297,
            column: 14
          },
          end: {
            line: 297,
            column: 58
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 297,
            column: 38
          },
          end: {
            line: 297,
            column: 45
          }
        }, {
          start: {
            line: 297,
            column: 48
          },
          end: {
            line: 297,
            column: 58
          }
        }],
        line: 297
      },
      "11": {
        loc: {
          start: {
            line: 298,
            column: 13
          },
          end: {
            line: 298,
            column: 68
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 298,
            column: 37
          },
          end: {
            line: 298,
            column: 53
          }
        }, {
          start: {
            line: 298,
            column: 56
          },
          end: {
            line: 298,
            column: 68
          }
        }],
        line: 298
      },
      "12": {
        loc: {
          start: {
            line: 299,
            column: 20
          },
          end: {
            line: 299,
            column: 49
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 299,
            column: 44
          },
          end: {
            line: 299,
            column: 45
          }
        }, {
          start: {
            line: 299,
            column: 48
          },
          end: {
            line: 299,
            column: 49
          }
        }],
        line: 299
      },
      "13": {
        loc: {
          start: {
            line: 300,
            column: 16
          },
          end: {
            line: 300,
            column: 51
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 300,
            column: 40
          },
          end: {
            line: 300,
            column: 44
          }
        }, {
          start: {
            line: 300,
            column: 47
          },
          end: {
            line: 300,
            column: 51
          }
        }],
        line: 300
      },
      "14": {
        loc: {
          start: {
            line: 303,
            column: 21
          },
          end: {
            line: 303,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 303,
            column: 45
          },
          end: {
            line: 303,
            column: 54
          }
        }, {
          start: {
            line: 303,
            column: 57
          },
          end: {
            line: 303,
            column: 80
          }
        }],
        line: 303
      },
      "15": {
        loc: {
          start: {
            line: 305,
            column: 16
          },
          end: {
            line: 305,
            column: 51
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 305,
            column: 40
          },
          end: {
            line: 305,
            column: 44
          }
        }, {
          start: {
            line: 305,
            column: 47
          },
          end: {
            line: 305,
            column: 51
          }
        }],
        line: 305
      },
      "16": {
        loc: {
          start: {
            line: 306,
            column: 19
          },
          end: {
            line: 306,
            column: 52
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 306,
            column: 43
          },
          end: {
            line: 306,
            column: 46
          }
        }, {
          start: {
            line: 306,
            column: 49
          },
          end: {
            line: 306,
            column: 52
          }
        }],
        line: 306
      },
      "17": {
        loc: {
          start: {
            line: 307,
            column: 20
          },
          end: {
            line: 307,
            column: 55
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 307,
            column: 44
          },
          end: {
            line: 307,
            column: 48
          }
        }, {
          start: {
            line: 307,
            column: 51
          },
          end: {
            line: 307,
            column: 55
          }
        }],
        line: 307
      },
      "18": {
        loc: {
          start: {
            line: 332,
            column: 4
          },
          end: {
            line: 334,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 332,
            column: 4
          },
          end: {
            line: 334,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 332
      },
      "19": {
        loc: {
          start: {
            line: 332,
            column: 8
          },
          end: {
            line: 332,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 332,
            column: 8
          },
          end: {
            line: 332,
            column: 16
          }
        }, {
          start: {
            line: 332,
            column: 20
          },
          end: {
            line: 332,
            column: 30
          }
        }, {
          start: {
            line: 332,
            column: 34
          },
          end: {
            line: 332,
            column: 50
          }
        }],
        line: 332
      },
      "20": {
        loc: {
          start: {
            line: 337,
            column: 4
          },
          end: {
            line: 339,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 337,
            column: 4
          },
          end: {
            line: 339,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 337
      },
      "21": {
        loc: {
          start: {
            line: 343,
            column: 4
          },
          end: {
            line: 345,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 343,
            column: 4
          },
          end: {
            line: 345,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 343
      },
      "22": {
        loc: {
          start: {
            line: 351,
            column: 4
          },
          end: {
            line: 353,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 351,
            column: 4
          },
          end: {
            line: 353,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 351
      },
      "23": {
        loc: {
          start: {
            line: 356,
            column: 4
          },
          end: {
            line: 358,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 356,
            column: 4
          },
          end: {
            line: 358,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 356
      },
      "24": {
        loc: {
          start: {
            line: 362,
            column: 4
          },
          end: {
            line: 364,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 362,
            column: 4
          },
          end: {
            line: 364,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 362
      },
      "25": {
        loc: {
          start: {
            line: 368,
            column: 4
          },
          end: {
            line: 370,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 368,
            column: 4
          },
          end: {
            line: 370,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 368
      },
      "26": {
        loc: {
          start: {
            line: 374,
            column: 4
          },
          end: {
            line: 376,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 374,
            column: 4
          },
          end: {
            line: 376,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 374
      },
      "27": {
        loc: {
          start: {
            line: 394,
            column: 6
          },
          end: {
            line: 396,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 394,
            column: 6
          },
          end: {
            line: 396,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 394
      },
      "28": {
        loc: {
          start: {
            line: 419,
            column: 22
          },
          end: {
            line: 419,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 419,
            column: 22
          },
          end: {
            line: 419,
            column: 42
          }
        }, {
          start: {
            line: 419,
            column: 46
          },
          end: {
            line: 419,
            column: 49
          }
        }],
        line: 419
      },
      "29": {
        loc: {
          start: {
            line: 428,
            column: 6
          },
          end: {
            line: 430,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 428,
            column: 6
          },
          end: {
            line: 430,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 428
      },
      "30": {
        loc: {
          start: {
            line: 473,
            column: 23
          },
          end: {
            line: 473,
            column: 49
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 473,
            column: 40
          },
          end: {
            line: 473,
            column: 43
          }
        }, {
          start: {
            line: 473,
            column: 46
          },
          end: {
            line: 473,
            column: 49
          }
        }],
        line: 473
      },
      "31": {
        loc: {
          start: {
            line: 483,
            column: 36
          },
          end: {
            line: 483,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 483,
            column: 36
          },
          end: {
            line: 483,
            column: 62
          }
        }, {
          start: {
            line: 483,
            column: 66
          },
          end: {
            line: 483,
            column: 69
          }
        }],
        line: 483
      },
      "32": {
        loc: {
          start: {
            line: 490,
            column: 4
          },
          end: {
            line: 501,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 491,
            column: 6
          },
          end: {
            line: 492,
            column: 39
          }
        }, {
          start: {
            line: 493,
            column: 6
          },
          end: {
            line: 494,
            column: 33
          }
        }, {
          start: {
            line: 495,
            column: 6
          },
          end: {
            line: 496,
            column: 32
          }
        }, {
          start: {
            line: 497,
            column: 6
          },
          end: {
            line: 498,
            column: 64
          }
        }, {
          start: {
            line: 499,
            column: 6
          },
          end: {
            line: 500,
            column: 32
          }
        }],
        line: 490
      },
      "33": {
        loc: {
          start: {
            line: 537,
            column: 17
          },
          end: {
            line: 537,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 537,
            column: 17
          },
          end: {
            line: 537,
            column: 30
          }
        }, {
          start: {
            line: 537,
            column: 34
          },
          end: {
            line: 537,
            column: 49
          }
        }],
        line: 537
      },
      "34": {
        loc: {
          start: {
            line: 551,
            column: 4
          },
          end: {
            line: 553,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 551,
            column: 4
          },
          end: {
            line: 553,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 551
      },
      "35": {
        loc: {
          start: {
            line: 564,
            column: 4
          },
          end: {
            line: 566,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 564,
            column: 4
          },
          end: {
            line: 566,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 564
      },
      "36": {
        loc: {
          start: {
            line: 564,
            column: 8
          },
          end: {
            line: 564,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 564,
            column: 8
          },
          end: {
            line: 564,
            column: 38
          }
        }, {
          start: {
            line: 564,
            column: 42
          },
          end: {
            line: 564,
            column: 99
          }
        }],
        line: 564
      },
      "37": {
        loc: {
          start: {
            line: 576,
            column: 4
          },
          end: {
            line: 584,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 576,
            column: 4
          },
          end: {
            line: 584,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 576
      },
      "38": {
        loc: {
          start: {
            line: 632,
            column: 4
          },
          end: {
            line: 634,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 632,
            column: 4
          },
          end: {
            line: 634,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 632
      },
      "39": {
        loc: {
          start: {
            line: 645,
            column: 4
          },
          end: {
            line: 648,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 645,
            column: 4
          },
          end: {
            line: 648,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 645
      },
      "40": {
        loc: {
          start: {
            line: 660,
            column: 6
          },
          end: {
            line: 662,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 660,
            column: 6
          },
          end: {
            line: 662,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 660
      },
      "41": {
        loc: {
          start: {
            line: 675,
            column: 6
          },
          end: {
            line: 677,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 675,
            column: 6
          },
          end: {
            line: 677,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 675
      },
      "42": {
        loc: {
          start: {
            line: 706,
            column: 4
          },
          end: {
            line: 708,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 706,
            column: 4
          },
          end: {
            line: 708,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 706
      },
      "43": {
        loc: {
          start: {
            line: 716,
            column: 16
          },
          end: {
            line: 716,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 716,
            column: 47
          },
          end: {
            line: 716,
            column: 73
          }
        }, {
          start: {
            line: 716,
            column: 76
          },
          end: {
            line: 716,
            column: 85
          }
        }],
        line: 716
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b0ec66d220bfcde4b1d7d88c517e719ce85638e1"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_9gk90rihy = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_9gk90rihy();
import { Platform } from 'react-native';
import { performanceMonitor } from "../../utils/performance";
var GPUAccelerationManager = function () {
  function GPUAccelerationManager() {
    _classCallCheck(this, GPUAccelerationManager);
    this.gpuCapabilities = (cov_9gk90rihy().s[0]++, null);
    this.computeQueue = (cov_9gk90rihy().s[1]++, []);
    this.activeComputations = (cov_9gk90rihy().s[2]++, new Map());
    this.performanceHistory = (cov_9gk90rihy().s[3]++, []);
    this.computeShaders = (cov_9gk90rihy().s[4]++, new Map());
    this.MAX_CONCURRENT_TASKS = (cov_9gk90rihy().s[5]++, 4);
    this.MEMORY_POOL_SIZE = (cov_9gk90rihy().s[6]++, 256 * 1024 * 1024);
    this.PERFORMANCE_THRESHOLD = (cov_9gk90rihy().s[7]++, 0.8);
    cov_9gk90rihy().f[0]++;
    cov_9gk90rihy().s[8]++;
    this.memoryPool = new GPUMemoryPool(this.MEMORY_POOL_SIZE);
    cov_9gk90rihy().s[9]++;
    this.initializeGPUAcceleration();
  }
  return _createClass(GPUAccelerationManager, [{
    key: "initializeGPUAcceleration",
    value: (function () {
      var _initializeGPUAcceleration = _asyncToGenerator(function* () {
        cov_9gk90rihy().f[1]++;
        cov_9gk90rihy().s[10]++;
        try {
          cov_9gk90rihy().s[11]++;
          this.gpuCapabilities = yield this.detectGPUCapabilities();
          cov_9gk90rihy().s[12]++;
          yield this.initializeComputeShaders();
          cov_9gk90rihy().s[13]++;
          this.startComputeQueueProcessor();
          cov_9gk90rihy().s[14]++;
          this.startPerformanceMonitoring();
          cov_9gk90rihy().s[15]++;
          console.log('GPU Acceleration Manager initialized successfully');
          cov_9gk90rihy().s[16]++;
          console.log('GPU Capabilities:', this.gpuCapabilities);
        } catch (error) {
          cov_9gk90rihy().s[17]++;
          console.error('Failed to initialize GPU Acceleration Manager:', error);
        }
      });
      function initializeGPUAcceleration() {
        return _initializeGPUAcceleration.apply(this, arguments);
      }
      return initializeGPUAcceleration;
    }())
  }, {
    key: "executeComputeTask",
    value: (function () {
      var _executeComputeTask = _asyncToGenerator(function* (task) {
        cov_9gk90rihy().f[2]++;
        cov_9gk90rihy().s[18]++;
        try {
          var _this$gpuCapabilities;
          var startTime = (cov_9gk90rihy().s[19]++, Date.now());
          cov_9gk90rihy().s[20]++;
          if (!this.validateComputeTask(task)) {
            cov_9gk90rihy().b[0][0]++;
            cov_9gk90rihy().s[21]++;
            throw new Error(`Invalid compute task: ${task.id}`);
          } else {
            cov_9gk90rihy().b[0][1]++;
          }
          var useGPU = (cov_9gk90rihy().s[22]++, yield this.shouldUseGPU(task));
          var result;
          cov_9gk90rihy().s[23]++;
          if ((cov_9gk90rihy().b[2][0]++, useGPU) && (cov_9gk90rihy().b[2][1]++, (_this$gpuCapabilities = this.gpuCapabilities) != null && _this$gpuCapabilities.supportsCompute)) {
            cov_9gk90rihy().b[1][0]++;
            cov_9gk90rihy().s[24]++;
            result = yield this.executeOnGPU(task);
          } else {
            cov_9gk90rihy().b[1][1]++;
            cov_9gk90rihy().s[25]++;
            if (task.constraints.fallbackToCPU) {
              cov_9gk90rihy().b[3][0]++;
              cov_9gk90rihy().s[26]++;
              result = yield this.executeOnCPU(task);
            } else {
              cov_9gk90rihy().b[3][1]++;
              cov_9gk90rihy().s[27]++;
              throw new Error('GPU not available and CPU fallback disabled');
            }
          }
          cov_9gk90rihy().s[28]++;
          this.trackComputePerformance(task, result);
          var totalTime = (cov_9gk90rihy().s[29]++, Date.now() - startTime);
          cov_9gk90rihy().s[30]++;
          performanceMonitor.trackDatabaseQuery('gpu_compute_task', totalTime);
          cov_9gk90rihy().s[31]++;
          return result;
        } catch (error) {
          cov_9gk90rihy().s[32]++;
          console.error('Failed to execute compute task:', error);
          cov_9gk90rihy().s[33]++;
          return this.createErrorResult(task, error);
        }
      });
      function executeComputeTask(_x) {
        return _executeComputeTask.apply(this, arguments);
      }
      return executeComputeTask;
    }())
  }, {
    key: "analyzeVideo",
    value: (function () {
      var _analyzeVideo = _asyncToGenerator(function* (task) {
        cov_9gk90rihy().f[3]++;
        var computeTask = (cov_9gk90rihy().s[34]++, {
          id: `video_analysis_${Date.now()}`,
          type: 'video_analysis',
          priority: 'high',
          data: {
            input: task.videoData,
            parameters: {
              analysisType: task.analysisType,
              frameRate: task.frameRate,
              resolution: task.resolution,
              outputFormat: task.outputFormat
            },
            expectedOutputSize: this.estimateVideoAnalysisOutputSize(task)
          },
          constraints: {
            maxExecutionTime: 30000,
            maxMemoryUsage: 512 * 1024 * 1024,
            requiresGPU: true,
            fallbackToCPU: true
          }
        });
        cov_9gk90rihy().s[35]++;
        return yield this.executeComputeTask(computeTask);
      });
      function analyzeVideo(_x2) {
        return _analyzeVideo.apply(this, arguments);
      }
      return analyzeVideo;
    }())
  }, {
    key: "runMLInference",
    value: (function () {
      var _runMLInference = _asyncToGenerator(function* (task) {
        cov_9gk90rihy().f[4]++;
        var computeTask = (cov_9gk90rihy().s[36]++, {
          id: `ml_inference_${Date.now()}`,
          type: 'ml_inference',
          priority: 'medium',
          data: {
            input: task.inputTensor.buffer,
            parameters: {
              modelId: task.modelId,
              inputShape: task.inputShape,
              outputShape: task.outputShape,
              precision: task.precision
            },
            expectedOutputSize: task.outputShape.reduce(function (a, b) {
              cov_9gk90rihy().f[5]++;
              cov_9gk90rihy().s[37]++;
              return a * b;
            }, 1) * 4
          },
          constraints: {
            maxExecutionTime: 5000,
            maxMemoryUsage: 128 * 1024 * 1024,
            requiresGPU: false,
            fallbackToCPU: true
          }
        });
        cov_9gk90rihy().s[38]++;
        return yield this.executeComputeTask(computeTask);
      });
      function runMLInference(_x3) {
        return _runMLInference.apply(this, arguments);
      }
      return runMLInference;
    }())
  }, {
    key: "getGPUPerformanceMetrics",
    value: function getGPUPerformanceMetrics() {
      cov_9gk90rihy().f[6]++;
      var recentTasks = (cov_9gk90rihy().s[39]++, this.performanceHistory.slice(-100));
      var successfulTasks = (cov_9gk90rihy().s[40]++, recentTasks.filter(function (entry) {
        cov_9gk90rihy().f[7]++;
        cov_9gk90rihy().s[41]++;
        return entry.result.success;
      }));
      var averageExecutionTime = (cov_9gk90rihy().s[42]++, successfulTasks.length > 0 ? (cov_9gk90rihy().b[4][0]++, successfulTasks.reduce(function (sum, entry) {
        cov_9gk90rihy().f[8]++;
        cov_9gk90rihy().s[43]++;
        return sum + entry.result.executionTime;
      }, 0) / successfulTasks.length) : (cov_9gk90rihy().b[4][1]++, 0));
      var tasksPerSecond = (cov_9gk90rihy().s[44]++, recentTasks.length > 0 ? (cov_9gk90rihy().b[5][0]++, recentTasks.length / ((Date.now() - recentTasks[0].timestamp) / 1000)) : (cov_9gk90rihy().b[5][1]++, 0));
      var efficiency = (cov_9gk90rihy().s[45]++, successfulTasks.length > 0 ? (cov_9gk90rihy().b[6][0]++, successfulTasks.reduce(function (sum, entry) {
        cov_9gk90rihy().f[9]++;
        cov_9gk90rihy().s[46]++;
        return sum + entry.result.performance.efficiency;
      }, 0) / successfulTasks.length) : (cov_9gk90rihy().b[6][1]++, 0));
      var errorRate = (cov_9gk90rihy().s[47]++, recentTasks.length > 0 ? (cov_9gk90rihy().b[7][0]++, (recentTasks.length - successfulTasks.length) / recentTasks.length * 100) : (cov_9gk90rihy().b[7][1]++, 0));
      cov_9gk90rihy().s[48]++;
      return {
        capabilities: this.gpuCapabilities,
        utilization: this.calculateGPUUtilization(),
        memoryUsage: this.memoryPool.getUsagePercentage(),
        averageExecutionTime: averageExecutionTime,
        tasksPerSecond: tasksPerSecond,
        efficiency: efficiency,
        errorRate: errorRate
      };
    }
  }, {
    key: "optimizeGPUPerformance",
    value: (function () {
      var _optimizeGPUPerformance = _asyncToGenerator(function* () {
        cov_9gk90rihy().f[10]++;
        cov_9gk90rihy().s[49]++;
        try {
          cov_9gk90rihy().s[50]++;
          console.log('Optimizing GPU performance...');
          var metrics = (cov_9gk90rihy().s[51]++, this.getGPUPerformanceMetrics());
          cov_9gk90rihy().s[52]++;
          if (metrics.memoryUsage > 80) {
            cov_9gk90rihy().b[8][0]++;
            cov_9gk90rihy().s[53]++;
            yield this.memoryPool.optimize();
          } else {
            cov_9gk90rihy().b[8][1]++;
          }
          cov_9gk90rihy().s[54]++;
          if (metrics.efficiency < this.PERFORMANCE_THRESHOLD) {
            cov_9gk90rihy().b[9][0]++;
            cov_9gk90rihy().s[55]++;
            yield this.optimizeComputeQueue();
          } else {
            cov_9gk90rihy().b[9][1]++;
          }
          cov_9gk90rihy().s[56]++;
          yield this.updateComputeShaders();
          cov_9gk90rihy().s[57]++;
          console.log('GPU performance optimization completed');
        } catch (error) {
          cov_9gk90rihy().s[58]++;
          console.error('Failed to optimize GPU performance:', error);
        }
      });
      function optimizeGPUPerformance() {
        return _optimizeGPUPerformance.apply(this, arguments);
      }
      return optimizeGPUPerformance;
    }())
  }, {
    key: "detectGPUCapabilities",
    value: function () {
      var _detectGPUCapabilities = _asyncToGenerator(function* () {
        cov_9gk90rihy().f[11]++;
        var mockCapabilities = (cov_9gk90rihy().s[59]++, {
          vendor: Platform.OS === 'ios' ? (cov_9gk90rihy().b[10][0]++, 'Apple') : (cov_9gk90rihy().b[10][1]++, 'Qualcomm'),
          model: Platform.OS === 'ios' ? (cov_9gk90rihy().b[11][0]++, 'A15 Bionic GPU') : (cov_9gk90rihy().b[11][1]++, 'Adreno 660'),
          computeUnits: Platform.OS === 'ios' ? (cov_9gk90rihy().b[12][0]++, 5) : (cov_9gk90rihy().b[12][1]++, 8),
          memoryMB: Platform.OS === 'ios' ? (cov_9gk90rihy().b[13][0]++, 6144) : (cov_9gk90rihy().b[13][1]++, 8192),
          maxTextureSize: 16384,
          supportsCompute: true,
          supportedAPIs: Platform.OS === 'ios' ? (cov_9gk90rihy().b[14][0]++, ['Metal']) : (cov_9gk90rihy().b[14][1]++, ['Vulkan', 'OpenGL ES']),
          performance: {
            gflops: Platform.OS === 'ios' ? (cov_9gk90rihy().b[15][0]++, 2600) : (cov_9gk90rihy().b[15][1]++, 2100),
            bandwidth: Platform.OS === 'ios' ? (cov_9gk90rihy().b[16][0]++, 238) : (cov_9gk90rihy().b[16][1]++, 204),
            efficiency: Platform.OS === 'ios' ? (cov_9gk90rihy().b[17][0]++, 0.85) : (cov_9gk90rihy().b[17][1]++, 0.78)
          }
        });
        cov_9gk90rihy().s[60]++;
        return mockCapabilities;
      });
      function detectGPUCapabilities() {
        return _detectGPUCapabilities.apply(this, arguments);
      }
      return detectGPUCapabilities;
    }()
  }, {
    key: "initializeComputeShaders",
    value: function () {
      var _initializeComputeShaders = _asyncToGenerator(function* () {
        cov_9gk90rihy().f[12]++;
        var shaderConfigs = (cov_9gk90rihy().s[61]++, [{
          name: 'video_analysis',
          type: 'video_analysis'
        }, {
          name: 'ml_inference',
          type: 'ml_inference'
        }, {
          name: 'image_processing',
          type: 'image_processing'
        }, {
          name: 'physics_simulation',
          type: 'physics_simulation'
        }]);
        cov_9gk90rihy().s[62]++;
        for (var config of shaderConfigs) {
          var shader = (cov_9gk90rihy().s[63]++, new ComputeShader(config.name, config.type));
          cov_9gk90rihy().s[64]++;
          yield shader.initialize();
          cov_9gk90rihy().s[65]++;
          this.computeShaders.set(config.name, shader);
        }
      });
      function initializeComputeShaders() {
        return _initializeComputeShaders.apply(this, arguments);
      }
      return initializeComputeShaders;
    }()
  }, {
    key: "validateComputeTask",
    value: function validateComputeTask(task) {
      cov_9gk90rihy().f[13]++;
      cov_9gk90rihy().s[66]++;
      if ((cov_9gk90rihy().b[19][0]++, !task.id) || (cov_9gk90rihy().b[19][1]++, !task.type) || (cov_9gk90rihy().b[19][2]++, !task.data.input)) {
        cov_9gk90rihy().b[18][0]++;
        cov_9gk90rihy().s[67]++;
        return false;
      } else {
        cov_9gk90rihy().b[18][1]++;
      }
      cov_9gk90rihy().s[68]++;
      if (task.data.expectedOutputSize > this.MEMORY_POOL_SIZE) {
        cov_9gk90rihy().b[20][0]++;
        cov_9gk90rihy().s[69]++;
        return false;
      } else {
        cov_9gk90rihy().b[20][1]++;
      }
      var supportedTypes = (cov_9gk90rihy().s[70]++, ['video_analysis', 'ml_inference', 'image_processing', 'physics_simulation', 'data_processing']);
      cov_9gk90rihy().s[71]++;
      if (!supportedTypes.includes(task.type)) {
        cov_9gk90rihy().b[21][0]++;
        cov_9gk90rihy().s[72]++;
        return false;
      } else {
        cov_9gk90rihy().b[21][1]++;
      }
      cov_9gk90rihy().s[73]++;
      return true;
    }
  }, {
    key: "shouldUseGPU",
    value: function () {
      var _shouldUseGPU = _asyncToGenerator(function* (task) {
        var _this$gpuCapabilities2;
        cov_9gk90rihy().f[14]++;
        cov_9gk90rihy().s[74]++;
        if (!((_this$gpuCapabilities2 = this.gpuCapabilities) != null && _this$gpuCapabilities2.supportsCompute)) {
          cov_9gk90rihy().b[22][0]++;
          cov_9gk90rihy().s[75]++;
          return false;
        } else {
          cov_9gk90rihy().b[22][1]++;
        }
        cov_9gk90rihy().s[76]++;
        if (task.constraints.requiresGPU) {
          cov_9gk90rihy().b[23][0]++;
          cov_9gk90rihy().s[77]++;
          return true;
        } else {
          cov_9gk90rihy().b[23][1]++;
        }
        var gpuBeneficialTypes = (cov_9gk90rihy().s[78]++, ['video_analysis', 'ml_inference', 'image_processing']);
        cov_9gk90rihy().s[79]++;
        if (!gpuBeneficialTypes.includes(task.type)) {
          cov_9gk90rihy().b[24][0]++;
          cov_9gk90rihy().s[80]++;
          return false;
        } else {
          cov_9gk90rihy().b[24][1]++;
        }
        var dataSizeThreshold = (cov_9gk90rihy().s[81]++, 1024 * 1024);
        cov_9gk90rihy().s[82]++;
        if (task.data.input.byteLength < dataSizeThreshold) {
          cov_9gk90rihy().b[25][0]++;
          cov_9gk90rihy().s[83]++;
          return false;
        } else {
          cov_9gk90rihy().b[25][1]++;
        }
        var utilization = (cov_9gk90rihy().s[84]++, this.calculateGPUUtilization());
        cov_9gk90rihy().s[85]++;
        if (utilization > 90) {
          cov_9gk90rihy().b[26][0]++;
          cov_9gk90rihy().s[86]++;
          return false;
        } else {
          cov_9gk90rihy().b[26][1]++;
        }
        cov_9gk90rihy().s[87]++;
        return true;
      });
      function shouldUseGPU(_x4) {
        return _shouldUseGPU.apply(this, arguments);
      }
      return shouldUseGPU;
    }()
  }, {
    key: "executeOnGPU",
    value: function () {
      var _executeOnGPU = _asyncToGenerator(function* (task) {
        cov_9gk90rihy().f[15]++;
        var startTime = (cov_9gk90rihy().s[88]++, Date.now());
        cov_9gk90rihy().s[89]++;
        try {
          var inputBuffer = (cov_9gk90rihy().s[90]++, yield this.memoryPool.allocate(task.data.input.byteLength));
          var outputBuffer = (cov_9gk90rihy().s[91]++, yield this.memoryPool.allocate(task.data.expectedOutputSize));
          cov_9gk90rihy().s[92]++;
          yield this.copyToGPU(task.data.input, inputBuffer);
          var shader = (cov_9gk90rihy().s[93]++, this.computeShaders.get(task.type));
          cov_9gk90rihy().s[94]++;
          if (!shader) {
            cov_9gk90rihy().b[27][0]++;
            cov_9gk90rihy().s[95]++;
            throw new Error(`No compute shader available for task type: ${task.type}`);
          } else {
            cov_9gk90rihy().b[27][1]++;
          }
          var gpuResult = (cov_9gk90rihy().s[96]++, yield shader.execute(inputBuffer, outputBuffer, task.data.parameters));
          var output = (cov_9gk90rihy().s[97]++, yield this.copyFromGPU(outputBuffer, task.data.expectedOutputSize));
          cov_9gk90rihy().s[98]++;
          yield this.memoryPool.free(inputBuffer);
          cov_9gk90rihy().s[99]++;
          yield this.memoryPool.free(outputBuffer);
          var executionTime = (cov_9gk90rihy().s[100]++, Date.now() - startTime);
          cov_9gk90rihy().s[101]++;
          return {
            taskId: task.id,
            success: true,
            output: output,
            executionTime: executionTime,
            memoryUsed: task.data.input.byteLength + task.data.expectedOutputSize,
            processingUnit: 'gpu',
            performance: {
              throughput: this.calculateThroughput(task, executionTime),
              efficiency: (cov_9gk90rihy().b[28][0]++, gpuResult.efficiency) || (cov_9gk90rihy().b[28][1]++, 0.8),
              accuracy: gpuResult.accuracy
            }
          };
        } catch (error) {
          cov_9gk90rihy().s[102]++;
          console.error('GPU execution failed:', error);
          cov_9gk90rihy().s[103]++;
          if (task.constraints.fallbackToCPU) {
            cov_9gk90rihy().b[29][0]++;
            cov_9gk90rihy().s[104]++;
            return yield this.executeOnCPU(task);
          } else {
            cov_9gk90rihy().b[29][1]++;
          }
          cov_9gk90rihy().s[105]++;
          throw error;
        }
      });
      function executeOnGPU(_x5) {
        return _executeOnGPU.apply(this, arguments);
      }
      return executeOnGPU;
    }()
  }, {
    key: "executeOnCPU",
    value: function () {
      var _executeOnCPU = _asyncToGenerator(function* (task) {
        cov_9gk90rihy().f[16]++;
        var startTime = (cov_9gk90rihy().s[106]++, Date.now());
        cov_9gk90rihy().s[107]++;
        try {
          var output = (cov_9gk90rihy().s[108]++, yield this.simulateCPUExecution(task));
          var executionTime = (cov_9gk90rihy().s[109]++, Date.now() - startTime);
          cov_9gk90rihy().s[110]++;
          return {
            taskId: task.id,
            success: true,
            output: output,
            executionTime: executionTime,
            memoryUsed: task.data.input.byteLength + task.data.expectedOutputSize,
            processingUnit: 'cpu',
            performance: {
              throughput: this.calculateThroughput(task, executionTime),
              efficiency: 0.6
            }
          };
        } catch (error) {
          cov_9gk90rihy().s[111]++;
          throw error;
        }
      });
      function executeOnCPU(_x6) {
        return _executeOnCPU.apply(this, arguments);
      }
      return executeOnCPU;
    }()
  }, {
    key: "simulateCPUExecution",
    value: function () {
      var _simulateCPUExecution = _asyncToGenerator(function* (task) {
        cov_9gk90rihy().f[17]++;
        var processingTime = (cov_9gk90rihy().s[112]++, this.estimateProcessingTime(task, 'cpu'));
        cov_9gk90rihy().s[113]++;
        yield new Promise(function (resolve) {
          cov_9gk90rihy().f[18]++;
          cov_9gk90rihy().s[114]++;
          return setTimeout(resolve, processingTime);
        });
        cov_9gk90rihy().s[115]++;
        return new ArrayBuffer(task.data.expectedOutputSize);
      });
      function simulateCPUExecution(_x7) {
        return _simulateCPUExecution.apply(this, arguments);
      }
      return simulateCPUExecution;
    }()
  }, {
    key: "estimateProcessingTime",
    value: function estimateProcessingTime(task, unit) {
      cov_9gk90rihy().f[19]++;
      var baseTime = (cov_9gk90rihy().s[116]++, task.data.input.byteLength / (1024 * 1024) * 100);
      var multiplier = (cov_9gk90rihy().s[117]++, unit === 'gpu' ? (cov_9gk90rihy().b[30][0]++, 0.3) : (cov_9gk90rihy().b[30][1]++, 1.0));
      var typeMultipliers = (cov_9gk90rihy().s[118]++, {
        'video_analysis': 3.0,
        'ml_inference': 2.0,
        'image_processing': 1.5,
        'physics_simulation': 2.5,
        'data_processing': 1.0
      });
      cov_9gk90rihy().s[119]++;
      return baseTime * multiplier * ((cov_9gk90rihy().b[31][0]++, typeMultipliers[task.type]) || (cov_9gk90rihy().b[31][1]++, 1.0));
    }
  }, {
    key: "estimateVideoAnalysisOutputSize",
    value: function estimateVideoAnalysisOutputSize(task) {
      cov_9gk90rihy().f[20]++;
      var _ref = (cov_9gk90rihy().s[120]++, task.resolution),
        width = _ref.width,
        height = _ref.height;
      var frameCount = (cov_9gk90rihy().s[121]++, Math.ceil(task.videoData.byteLength / (width * height * 3)));
      cov_9gk90rihy().s[122]++;
      switch (task.outputFormat) {
        case 'keypoints':
          cov_9gk90rihy().b[32][0]++;
          cov_9gk90rihy().s[123]++;
          return frameCount * 33 * 3 * 4;
        case 'annotations':
          cov_9gk90rihy().b[32][1]++;
          cov_9gk90rihy().s[124]++;
          return frameCount * 1024;
        case 'metrics':
          cov_9gk90rihy().b[32][2]++;
          cov_9gk90rihy().s[125]++;
          return frameCount * 256;
        case 'highlights':
          cov_9gk90rihy().b[32][3]++;
          cov_9gk90rihy().s[126]++;
          return Math.ceil(frameCount * 0.1) * width * height * 3;
        default:
          cov_9gk90rihy().b[32][4]++;
          cov_9gk90rihy().s[127]++;
          return frameCount * 512;
      }
    }
  }, {
    key: "calculateThroughput",
    value: function calculateThroughput(task, executionTime) {
      cov_9gk90rihy().f[21]++;
      var dataSize = (cov_9gk90rihy().s[128]++, task.data.input.byteLength);
      cov_9gk90rihy().s[129]++;
      return dataSize / 1024 / 1024 / (executionTime / 1000);
    }
  }, {
    key: "calculateGPUUtilization",
    value: function calculateGPUUtilization() {
      cov_9gk90rihy().f[22]++;
      var activeTasks = (cov_9gk90rihy().s[130]++, this.activeComputations.size);
      cov_9gk90rihy().s[131]++;
      return activeTasks / this.MAX_CONCURRENT_TASKS * 100;
    }
  }, {
    key: "copyToGPU",
    value: function () {
      var _copyToGPU = _asyncToGenerator(function* (data, gpuBuffer) {
        cov_9gk90rihy().f[23]++;
        cov_9gk90rihy().s[132]++;
        yield new Promise(function (resolve) {
          cov_9gk90rihy().f[24]++;
          cov_9gk90rihy().s[133]++;
          return setTimeout(resolve, 1);
        });
      });
      function copyToGPU(_x8, _x9) {
        return _copyToGPU.apply(this, arguments);
      }
      return copyToGPU;
    }()
  }, {
    key: "copyFromGPU",
    value: function () {
      var _copyFromGPU = _asyncToGenerator(function* (gpuBuffer, size) {
        cov_9gk90rihy().f[25]++;
        cov_9gk90rihy().s[134]++;
        yield new Promise(function (resolve) {
          cov_9gk90rihy().f[26]++;
          cov_9gk90rihy().s[135]++;
          return setTimeout(resolve, 1);
        });
        cov_9gk90rihy().s[136]++;
        return new ArrayBuffer(size);
      });
      function copyFromGPU(_x0, _x1) {
        return _copyFromGPU.apply(this, arguments);
      }
      return copyFromGPU;
    }()
  }, {
    key: "createErrorResult",
    value: function createErrorResult(task, error) {
      cov_9gk90rihy().f[27]++;
      cov_9gk90rihy().s[137]++;
      return {
        taskId: task.id,
        success: false,
        output: null,
        executionTime: 0,
        memoryUsed: 0,
        processingUnit: 'cpu',
        performance: {
          throughput: 0,
          efficiency: 0
        },
        error: {
          code: 'EXECUTION_ERROR',
          message: (cov_9gk90rihy().b[33][0]++, error.message) || (cov_9gk90rihy().b[33][1]++, 'Unknown error'),
          fallbackUsed: false
        }
      };
    }
  }, {
    key: "trackComputePerformance",
    value: function trackComputePerformance(task, result) {
      cov_9gk90rihy().f[28]++;
      cov_9gk90rihy().s[138]++;
      this.performanceHistory.push({
        timestamp: Date.now(),
        task: task,
        result: result
      });
      cov_9gk90rihy().s[139]++;
      if (this.performanceHistory.length > 1000) {
        cov_9gk90rihy().b[34][0]++;
        cov_9gk90rihy().s[140]++;
        this.performanceHistory.shift();
      } else {
        cov_9gk90rihy().b[34][1]++;
      }
    }
  }, {
    key: "startComputeQueueProcessor",
    value: function startComputeQueueProcessor() {
      var _this = this;
      cov_9gk90rihy().f[29]++;
      cov_9gk90rihy().s[141]++;
      setInterval(function () {
        cov_9gk90rihy().f[30]++;
        cov_9gk90rihy().s[142]++;
        _this.processComputeQueue();
      }, 100);
    }
  }, {
    key: "processComputeQueue",
    value: function () {
      var _processComputeQueue = _asyncToGenerator(function* () {
        cov_9gk90rihy().f[31]++;
        cov_9gk90rihy().s[143]++;
        if ((cov_9gk90rihy().b[36][0]++, this.computeQueue.length === 0) || (cov_9gk90rihy().b[36][1]++, this.activeComputations.size >= this.MAX_CONCURRENT_TASKS)) {
          cov_9gk90rihy().b[35][0]++;
          cov_9gk90rihy().s[144]++;
          return;
        } else {
          cov_9gk90rihy().b[35][1]++;
        }
        cov_9gk90rihy().s[145]++;
        this.computeQueue.sort(function (a, b) {
          cov_9gk90rihy().f[32]++;
          var priorityOrder = (cov_9gk90rihy().s[146]++, {
            critical: 4,
            high: 3,
            medium: 2,
            low: 1
          });
          cov_9gk90rihy().s[147]++;
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        });
        var task = (cov_9gk90rihy().s[148]++, this.computeQueue.shift());
        cov_9gk90rihy().s[149]++;
        if (task) {
          cov_9gk90rihy().b[37][0]++;
          cov_9gk90rihy().s[150]++;
          this.activeComputations.set(task.id, task);
          cov_9gk90rihy().s[151]++;
          try {
            cov_9gk90rihy().s[152]++;
            yield this.executeComputeTask(task);
          } finally {
            cov_9gk90rihy().s[153]++;
            this.activeComputations.delete(task.id);
          }
        } else {
          cov_9gk90rihy().b[37][1]++;
        }
      });
      function processComputeQueue() {
        return _processComputeQueue.apply(this, arguments);
      }
      return processComputeQueue;
    }()
  }, {
    key: "optimizeComputeQueue",
    value: function () {
      var _optimizeComputeQueue = _asyncToGenerator(function* () {
        cov_9gk90rihy().f[33]++;
        cov_9gk90rihy().s[154]++;
        console.log('Optimizing compute queue processing');
      });
      function optimizeComputeQueue() {
        return _optimizeComputeQueue.apply(this, arguments);
      }
      return optimizeComputeQueue;
    }()
  }, {
    key: "updateComputeShaders",
    value: function () {
      var _updateComputeShaders = _asyncToGenerator(function* () {
        cov_9gk90rihy().f[34]++;
        cov_9gk90rihy().s[155]++;
        console.log('Updating compute shaders');
      });
      function updateComputeShaders() {
        return _updateComputeShaders.apply(this, arguments);
      }
      return updateComputeShaders;
    }()
  }, {
    key: "startPerformanceMonitoring",
    value: function startPerformanceMonitoring() {
      var _this2 = this;
      cov_9gk90rihy().f[35]++;
      cov_9gk90rihy().s[156]++;
      setInterval(function () {
        cov_9gk90rihy().f[36]++;
        cov_9gk90rihy().s[157]++;
        _this2.monitorGPUPerformance();
      }, 30000);
    }
  }, {
    key: "monitorGPUPerformance",
    value: function monitorGPUPerformance() {
      cov_9gk90rihy().f[37]++;
      var metrics = (cov_9gk90rihy().s[158]++, this.getGPUPerformanceMetrics());
      cov_9gk90rihy().s[159]++;
      console.log('GPU Performance Metrics:', {
        utilization: `${metrics.utilization.toFixed(1)}%`,
        memoryUsage: `${metrics.memoryUsage.toFixed(1)}%`,
        averageExecutionTime: `${metrics.averageExecutionTime.toFixed(1)}ms`,
        tasksPerSecond: metrics.tasksPerSecond.toFixed(2),
        efficiency: `${(metrics.efficiency * 100).toFixed(1)}%`,
        errorRate: `${metrics.errorRate.toFixed(1)}%`
      });
    }
  }]);
}();
var GPUMemoryPool = function () {
  function GPUMemoryPool(size) {
    _classCallCheck(this, GPUMemoryPool);
    this.usedSize = (cov_9gk90rihy().s[160]++, 0);
    this.allocations = (cov_9gk90rihy().s[161]++, new Map());
    cov_9gk90rihy().f[38]++;
    cov_9gk90rihy().s[162]++;
    this.totalSize = size;
  }
  return _createClass(GPUMemoryPool, [{
    key: "allocate",
    value: function () {
      var _allocate = _asyncToGenerator(function* (size) {
        cov_9gk90rihy().f[39]++;
        cov_9gk90rihy().s[163]++;
        if (this.usedSize + size > this.totalSize) {
          cov_9gk90rihy().b[38][0]++;
          cov_9gk90rihy().s[164]++;
          yield this.freeOldestAllocations(size);
        } else {
          cov_9gk90rihy().b[38][1]++;
        }
        var id = (cov_9gk90rihy().s[165]++, `alloc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
        cov_9gk90rihy().s[166]++;
        this.allocations.set(id, {
          size: size,
          timestamp: Date.now()
        });
        cov_9gk90rihy().s[167]++;
        this.usedSize += size;
        cov_9gk90rihy().s[168]++;
        return id;
      });
      function allocate(_x10) {
        return _allocate.apply(this, arguments);
      }
      return allocate;
    }()
  }, {
    key: "free",
    value: function () {
      var _free = _asyncToGenerator(function* (id) {
        cov_9gk90rihy().f[40]++;
        var allocation = (cov_9gk90rihy().s[169]++, this.allocations.get(id));
        cov_9gk90rihy().s[170]++;
        if (allocation) {
          cov_9gk90rihy().b[39][0]++;
          cov_9gk90rihy().s[171]++;
          this.usedSize -= allocation.size;
          cov_9gk90rihy().s[172]++;
          this.allocations.delete(id);
        } else {
          cov_9gk90rihy().b[39][1]++;
        }
      });
      function free(_x11) {
        return _free.apply(this, arguments);
      }
      return free;
    }()
  }, {
    key: "getUsagePercentage",
    value: function getUsagePercentage() {
      cov_9gk90rihy().f[41]++;
      cov_9gk90rihy().s[173]++;
      return this.usedSize / this.totalSize * 100;
    }
  }, {
    key: "optimize",
    value: function () {
      var _optimize = _asyncToGenerator(function* () {
        cov_9gk90rihy().f[42]++;
        var fiveMinutesAgo = (cov_9gk90rihy().s[174]++, Date.now() - 300000);
        cov_9gk90rihy().s[175]++;
        for (var _ref2 of this.allocations.entries()) {
          var _ref3 = _slicedToArray(_ref2, 2);
          var id = _ref3[0];
          var allocation = _ref3[1];
          cov_9gk90rihy().s[176]++;
          if (allocation.timestamp < fiveMinutesAgo) {
            cov_9gk90rihy().b[40][0]++;
            cov_9gk90rihy().s[177]++;
            yield this.free(id);
          } else {
            cov_9gk90rihy().b[40][1]++;
          }
        }
      });
      function optimize() {
        return _optimize.apply(this, arguments);
      }
      return optimize;
    }()
  }, {
    key: "freeOldestAllocations",
    value: function () {
      var _freeOldestAllocations = _asyncToGenerator(function* (requiredSize) {
        cov_9gk90rihy().f[43]++;
        var sortedAllocations = (cov_9gk90rihy().s[178]++, Array.from(this.allocations.entries()).sort(function (a, b) {
          cov_9gk90rihy().f[44]++;
          cov_9gk90rihy().s[179]++;
          return a[1].timestamp - b[1].timestamp;
        }));
        var freedSize = (cov_9gk90rihy().s[180]++, 0);
        cov_9gk90rihy().s[181]++;
        for (var _ref4 of sortedAllocations) {
          var _ref5 = _slicedToArray(_ref4, 2);
          var id = _ref5[0];
          var allocation = _ref5[1];
          cov_9gk90rihy().s[182]++;
          yield this.free(id);
          cov_9gk90rihy().s[183]++;
          freedSize += allocation.size;
          cov_9gk90rihy().s[184]++;
          if (freedSize >= requiredSize) {
            cov_9gk90rihy().b[41][0]++;
            cov_9gk90rihy().s[185]++;
            break;
          } else {
            cov_9gk90rihy().b[41][1]++;
          }
        }
      });
      function freeOldestAllocations(_x12) {
        return _freeOldestAllocations.apply(this, arguments);
      }
      return freeOldestAllocations;
    }()
  }]);
}();
var ComputeShader = function () {
  function ComputeShader(name, type) {
    _classCallCheck(this, ComputeShader);
    this.isInitialized = (cov_9gk90rihy().s[186]++, false);
    cov_9gk90rihy().f[45]++;
    cov_9gk90rihy().s[187]++;
    this.name = name;
    cov_9gk90rihy().s[188]++;
    this.type = type;
  }
  return _createClass(ComputeShader, [{
    key: "initialize",
    value: function () {
      var _initialize = _asyncToGenerator(function* () {
        cov_9gk90rihy().f[46]++;
        cov_9gk90rihy().s[189]++;
        this.isInitialized = true;
        cov_9gk90rihy().s[190]++;
        console.log(`Initialized compute shader: ${this.name}`);
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }()
  }, {
    key: "execute",
    value: function () {
      var _execute = _asyncToGenerator(function* (inputBuffer, outputBuffer, parameters) {
        cov_9gk90rihy().f[47]++;
        cov_9gk90rihy().s[191]++;
        if (!this.isInitialized) {
          cov_9gk90rihy().b[42][0]++;
          cov_9gk90rihy().s[192]++;
          throw new Error(`Compute shader not initialized: ${this.name}`);
        } else {
          cov_9gk90rihy().b[42][1]++;
        }
        var executionTime = (cov_9gk90rihy().s[193]++, Math.random() * 50 + 10);
        cov_9gk90rihy().s[194]++;
        yield new Promise(function (resolve) {
          cov_9gk90rihy().f[48]++;
          cov_9gk90rihy().s[195]++;
          return setTimeout(resolve, executionTime);
        });
        cov_9gk90rihy().s[196]++;
        return {
          efficiency: 0.8 + Math.random() * 0.15,
          accuracy: this.type === 'ml_inference' ? (cov_9gk90rihy().b[43][0]++, 0.9 + Math.random() * 0.09) : (cov_9gk90rihy().b[43][1]++, undefined)
        };
      });
      function execute(_x13, _x14, _x15) {
        return _execute.apply(this, arguments);
      }
      return execute;
    }()
  }]);
}();
export var gpuAccelerationManager = (cov_9gk90rihy().s[197]++, new GPUAccelerationManager());
export default gpuAccelerationManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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