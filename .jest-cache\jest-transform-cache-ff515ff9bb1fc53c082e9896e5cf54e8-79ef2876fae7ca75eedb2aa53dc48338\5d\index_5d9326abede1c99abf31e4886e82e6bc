54731ba9e3ac65e7b4802d7741a38e63
"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = useStable;
var React = _interopRequireWildcard(require("react"));
var UNINITIALIZED = typeof Symbol === 'function' && typeof Symbol() === 'symbol' ? Symbol() : Object.freeze({});
function useStable(getInitialValue) {
  var ref = React.useRef(UNINITIALIZED);
  if (ref.current === UNINITIALIZED) {
    ref.current = getInitialValue();
  }
  return ref.current;
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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