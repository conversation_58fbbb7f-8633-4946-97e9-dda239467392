{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "get", "getEnforcing", "_invariant", "name", "module"], "sources": ["TurboModuleRegistry.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.get = get;\nexports.getEnforcing = getEnforcing;\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nfunction get(name) {\n  return null;\n}\nfunction getEnforcing(name) {\n  var module = get(name);\n  (0, _invariant.default)(module != null, \"TurboModuleRegistry.getEnforcing(...): '\" + name + \"' could not be found. \" + 'Verify that a module by this name is registered in the native binary.');\n  return module;\n}"], "mappings": "AAUA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,GAAG,GAAGA,GAAG;AACjBF,OAAO,CAACG,YAAY,GAAGA,YAAY;AACnC,IAAIC,UAAU,GAAGP,sBAAsB,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,SAASI,GAAGA,CAACG,IAAI,EAAE;EACjB,OAAO,IAAI;AACb;AACA,SAASF,YAAYA,CAACE,IAAI,EAAE;EAC1B,IAAIC,MAAM,GAAGJ,GAAG,CAACG,IAAI,CAAC;EACtB,CAAC,CAAC,EAAED,UAAU,CAACL,OAAO,EAAEO,MAAM,IAAI,IAAI,EAAE,0CAA0C,GAAGD,IAAI,GAAG,wBAAwB,GAAG,uEAAuE,CAAC;EAC/L,OAAOC,MAAM;AACf", "ignoreList": []}