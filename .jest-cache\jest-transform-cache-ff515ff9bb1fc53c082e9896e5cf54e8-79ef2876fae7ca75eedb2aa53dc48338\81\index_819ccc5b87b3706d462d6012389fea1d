37711974cc5c26c8c8d3955cce5e1f5a
'use strict';

exports.__esModule = true;
exports.computeWindowedRenderLimits = computeWindowedRenderLimits;
exports.elementsThatOverlapOffsets = elementsThatOverlapOffsets;
exports.keyExtractor = keyExtractor;
exports.newRangeCount = newRangeCount;
function elementsThatOverlapOffsets(offsets, props, getFrameMetrics, zoomScale) {
  if (zoomScale === void 0) {
    zoomScale = 1;
  }
  var itemCount = props.getItemCount(props.data);
  var result = [];
  for (var offsetIndex = 0; offsetIndex < offsets.length; offsetIndex++) {
    var currentOffset = offsets[offsetIndex];
    var left = 0;
    var right = itemCount - 1;
    while (left <= right) {
      var mid = left + (right - left >>> 1);
      var frame = getFrameMetrics(mid, props);
      var scaledOffsetStart = frame.offset * zoomScale;
      var scaledOffsetEnd = (frame.offset + frame.length) * zoomScale;
      if (mid === 0 && currentOffset < scaledOffsetStart || mid !== 0 && currentOffset <= scaledOffsetStart) {
        right = mid - 1;
      } else if (currentOffset > scaledOffsetEnd) {
        left = mid + 1;
      } else {
        result[offsetIndex] = mid;
        break;
      }
    }
  }
  return result;
}
function newRangeCount(prev, next) {
  return next.last - next.first + 1 - Math.max(0, 1 + Math.min(next.last, prev.last) - Math.max(next.first, prev.first));
}
function computeWindowedRenderLimits(props, maxToRenderPerBatch, windowSize, prev, getFrameMetricsApprox, scrollMetrics) {
  var itemCount = props.getItemCount(props.data);
  if (itemCount === 0) {
    return {
      first: 0,
      last: -1
    };
  }
  var offset = scrollMetrics.offset,
    velocity = scrollMetrics.velocity,
    visibleLength = scrollMetrics.visibleLength,
    _scrollMetrics$zoomSc = scrollMetrics.zoomScale,
    zoomScale = _scrollMetrics$zoomSc === void 0 ? 1 : _scrollMetrics$zoomSc;
  var visibleBegin = Math.max(0, offset);
  var visibleEnd = visibleBegin + visibleLength;
  var overscanLength = (windowSize - 1) * visibleLength;
  var leadFactor = 0.5;
  var fillPreference = velocity > 1 ? 'after' : velocity < -1 ? 'before' : 'none';
  var overscanBegin = Math.max(0, visibleBegin - (1 - leadFactor) * overscanLength);
  var overscanEnd = Math.max(0, visibleEnd + leadFactor * overscanLength);
  var lastItemOffset = getFrameMetricsApprox(itemCount - 1, props).offset * zoomScale;
  if (lastItemOffset < overscanBegin) {
    return {
      first: Math.max(0, itemCount - 1 - maxToRenderPerBatch),
      last: itemCount - 1
    };
  }
  var _elementsThatOverlapO = elementsThatOverlapOffsets([overscanBegin, visibleBegin, visibleEnd, overscanEnd], props, getFrameMetricsApprox, zoomScale),
    overscanFirst = _elementsThatOverlapO[0],
    first = _elementsThatOverlapO[1],
    last = _elementsThatOverlapO[2],
    overscanLast = _elementsThatOverlapO[3];
  overscanFirst = overscanFirst == null ? 0 : overscanFirst;
  first = first == null ? Math.max(0, overscanFirst) : first;
  overscanLast = overscanLast == null ? itemCount - 1 : overscanLast;
  last = last == null ? Math.min(overscanLast, first + maxToRenderPerBatch - 1) : last;
  var visible = {
    first: first,
    last: last
  };
  var newCellCount = newRangeCount(prev, visible);
  while (true) {
    if (first <= overscanFirst && last >= overscanLast) {
      break;
    }
    var maxNewCells = newCellCount >= maxToRenderPerBatch;
    var firstWillAddMore = first <= prev.first || first > prev.last;
    var firstShouldIncrement = first > overscanFirst && (!maxNewCells || !firstWillAddMore);
    var lastWillAddMore = last >= prev.last || last < prev.first;
    var lastShouldIncrement = last < overscanLast && (!maxNewCells || !lastWillAddMore);
    if (maxNewCells && !firstShouldIncrement && !lastShouldIncrement) {
      break;
    }
    if (firstShouldIncrement && !(fillPreference === 'after' && lastShouldIncrement && lastWillAddMore)) {
      if (firstWillAddMore) {
        newCellCount++;
      }
      first--;
    }
    if (lastShouldIncrement && !(fillPreference === 'before' && firstShouldIncrement && firstWillAddMore)) {
      if (lastWillAddMore) {
        newCellCount++;
      }
      last++;
    }
  }
  if (!(last >= first && first >= 0 && last < itemCount && first >= overscanFirst && last <= overscanLast && first <= visible.first && last >= visible.last)) {
    throw new Error('Bad window calculation ' + JSON.stringify({
      first: first,
      last: last,
      itemCount: itemCount,
      overscanFirst: overscanFirst,
      overscanLast: overscanLast,
      visible: visible
    }));
  }
  return {
    first: first,
    last: last
  };
}
function keyExtractor(item, index) {
  if (typeof item === 'object' && (item == null ? void 0 : item.key) != null) {
    return item.key;
  }
  if (typeof item === 'object' && (item == null ? void 0 : item.id) != null) {
    return item.id;
  }
  return String(index);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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