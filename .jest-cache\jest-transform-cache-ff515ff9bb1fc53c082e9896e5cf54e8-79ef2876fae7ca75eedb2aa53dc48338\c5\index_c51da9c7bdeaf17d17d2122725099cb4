157e91ecb384452e72a4c86265e1d6e3
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _ModalPortal = _interopRequireDefault(require("./ModalPortal"));
var _ModalAnimation = _interopRequireDefault(require("./ModalAnimation"));
var _ModalContent = _interopRequireDefault(require("./ModalContent"));
var _ModalFocusTrap = _interopRequireDefault(require("./ModalFocusTrap"));
var _excluded = ["animationType", "children", "onDismiss", "onRequestClose", "onShow", "transparent", "visible"];
var uniqueModalIdentifier = 0;
var activeModalStack = [];
var activeModalListeners = {};
function notifyActiveModalListeners() {
  if (activeModalStack.length === 0) {
    return;
  }
  var activeModalId = activeModalStack[activeModalStack.length - 1];
  activeModalStack.forEach(function (modalId) {
    if (modalId in activeModalListeners) {
      activeModalListeners[modalId](modalId === activeModalId);
    }
  });
}
function removeActiveModal(modalId) {
  if (modalId in activeModalListeners) {
    activeModalListeners[modalId](false);
    delete activeModalListeners[modalId];
  }
  var index = activeModalStack.indexOf(modalId);
  if (index !== -1) {
    activeModalStack.splice(index, 1);
    notifyActiveModalListeners();
  }
}
function addActiveModal(modalId, listener) {
  removeActiveModal(modalId);
  activeModalStack.push(modalId);
  activeModalListeners[modalId] = listener;
  notifyActiveModalListeners();
}
var Modal = React.forwardRef(function (props, forwardedRef) {
  var animationType = props.animationType,
    children = props.children,
    onDismiss = props.onDismiss,
    onRequestClose = props.onRequestClose,
    onShow = props.onShow,
    transparent = props.transparent,
    _props$visible = props.visible,
    visible = _props$visible === void 0 ? true : _props$visible,
    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  var modalId = React.useMemo(function () {
    return uniqueModalIdentifier++;
  }, []);
  var _React$useState = React.useState(false),
    isActive = _React$useState[0],
    setIsActive = _React$useState[1];
  var onDismissCallback = React.useCallback(function () {
    removeActiveModal(modalId);
    if (onDismiss) {
      onDismiss();
    }
  }, [modalId, onDismiss]);
  var onShowCallback = React.useCallback(function () {
    addActiveModal(modalId, setIsActive);
    if (onShow) {
      onShow();
    }
  }, [modalId, onShow]);
  React.useEffect(function () {
    return function () {
      return removeActiveModal(modalId);
    };
  }, [modalId]);
  return React.createElement(_ModalPortal.default, null, React.createElement(_ModalAnimation.default, {
    animationType: animationType,
    onDismiss: onDismissCallback,
    onShow: onShowCallback,
    visible: visible
  }, React.createElement(_ModalFocusTrap.default, {
    active: isActive
  }, React.createElement(_ModalContent.default, (0, _extends2.default)({}, rest, {
    active: isActive,
    onRequestClose: onRequestClose,
    ref: forwardedRef,
    transparent: transparent
  }), children))));
});
var _default = exports.default = Modal;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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