db3d355615d937b7778e78bb6f1e26e4
"use strict";

exports.__esModule = true;
exports.default = void 0;
var CSS_UNIT_RE = /^[+-]?\d*(?:\.\d+)?(?:[Ee][+-]?\d+)?(%|\w*)/;
var getUnit = function getUnit(str) {
  return str.match(CSS_UNIT_RE)[1];
};
var isNumeric = function isNumeric(n) {
  return !isNaN(parseFloat(n)) && isFinite(n);
};
var multiplyStyleLengthValue = function multiplyStyleLengthValue(value, multiple) {
  if (typeof value === 'string') {
    var number = parseFloat(value) * multiple;
    var unit = getUnit(value);
    return "" + number + unit;
  } else if (isNumeric(value)) {
    return value * multiple;
  }
};
var _default = exports.default = multiplyStyleLengthValue;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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