37bfcaaf22e77c974559f29278cf0191
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _Platform = _interopRequireDefault(require("../../../exports/Platform"));
var _AnimatedFlatList = _interopRequireDefault(require("./components/AnimatedFlatList"));
var _AnimatedImage = _interopRequireDefault(require("./components/AnimatedImage"));
var _AnimatedScrollView = _interopRequireDefault(require("./components/AnimatedScrollView"));
var _AnimatedSectionList = _interopRequireDefault(require("./components/AnimatedSectionList"));
var _AnimatedText = _interopRequireDefault(require("./components/AnimatedText"));
var _AnimatedView = _interopRequireDefault(require("./components/AnimatedView"));
var _AnimatedMock = _interopRequireDefault(require("./AnimatedMock"));
var _AnimatedImplementation = _interopRequireDefault(require("./AnimatedImplementation"));
var Animated = _Platform.default.isTesting ? _AnimatedMock.default : _AnimatedImplementation.default;
var _default = exports.default = (0, _objectSpread2.default)({
  FlatList: _AnimatedFlatList.default,
  Image: _AnimatedImage.default,
  ScrollView: _AnimatedScrollView.default,
  SectionList: _AnimatedSectionList.default,
  Text: _AnimatedText.default,
  View: _AnimatedView.default
}, Animated);
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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