8b5c751bab46a149988442460486ad52
function cov_26b5a2ib5r() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\ui\\EmptyState.tsx";
  var hash = "661f2139682a9f235cd36b465a8b074dcc2f4217";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\ui\\EmptyState.tsx",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 29,
          column: 2
        },
        end: {
          line: 47,
          column: 4
        }
      },
      "2": {
        start: {
          line: 50,
          column: 15
        },
        end: {
          line: 109,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "EmptyState",
        decl: {
          start: {
            line: 22,
            column: 24
          },
          end: {
            line: 22,
            column: 34
          }
        },
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 28
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 26,
            column: 2
          },
          end: {
            line: 26,
            column: 28
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 28
          }
        }],
        line: 26
      },
      "1": {
        loc: {
          start: {
            line: 27,
            column: 2
          },
          end: {
            line: 27,
            column: 17
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 27,
            column: 12
          },
          end: {
            line: 27,
            column: 17
          }
        }],
        line: 27
      },
      "2": {
        loc: {
          start: {
            line: 30,
            column: 36
          },
          end: {
            line: 30,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 36
          },
          end: {
            line: 30,
            column: 43
          }
        }, {
          start: {
            line: 30,
            column: 47
          },
          end: {
            line: 30,
            column: 70
          }
        }],
        line: 30
      },
      "3": {
        loc: {
          start: {
            line: 32,
            column: 44
          },
          end: {
            line: 32,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 44
          },
          end: {
            line: 32,
            column: 51
          }
        }, {
          start: {
            line: 32,
            column: 55
          },
          end: {
            line: 32,
            column: 73
          }
        }],
        line: 32
      },
      "4": {
        loc: {
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 41
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 33,
            column: 34
          },
          end: {
            line: 33,
            column: 36
          }
        }, {
          start: {
            line: 33,
            column: 39
          },
          end: {
            line: 33,
            column: 41
          }
        }],
        line: 33
      },
      "5": {
        loc: {
          start: {
            line: 35,
            column: 36
          },
          end: {
            line: 35,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 35,
            column: 36
          },
          end: {
            line: 35,
            column: 43
          }
        }, {
          start: {
            line: 35,
            column: 47
          },
          end: {
            line: 35,
            column: 66
          }
        }],
        line: 35
      },
      "6": {
        loc: {
          start: {
            line: 36,
            column: 38
          },
          end: {
            line: 36,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 36,
            column: 38
          },
          end: {
            line: 36,
            column: 45
          }
        }, {
          start: {
            line: 36,
            column: 49
          },
          end: {
            line: 36,
            column: 70
          }
        }],
        line: 36
      },
      "7": {
        loc: {
          start: {
            line: 37,
            column: 9
          },
          end: {
            line: 44,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 9
          },
          end: {
            line: 37,
            column: 17
          }
        }, {
          start: {
            line: 38,
            column: 10
          },
          end: {
            line: 43,
            column: 12
          }
        }],
        line: 37
      },
      "8": {
        loc: {
          start: {
            line: 41,
            column: 41
          },
          end: {
            line: 41,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 41
          },
          end: {
            line: 41,
            column: 48
          }
        }, {
          start: {
            line: 41,
            column: 52
          },
          end: {
            line: 41,
            column: 72
          }
        }],
        line: 41
      },
      "9": {
        loc: {
          start: {
            line: 42,
            column: 18
          },
          end: {
            line: 42,
            column: 46
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 42,
            column: 28
          },
          end: {
            line: 42,
            column: 35
          }
        }, {
          start: {
            line: 42,
            column: 38
          },
          end: {
            line: 42,
            column: 46
          }
        }],
        line: 42
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "661f2139682a9f235cd36b465a8b074dcc2f4217"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_26b5a2ib5r = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_26b5a2ib5r();
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Target } from 'lucide-react-native';
import Button from "./Button";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_26b5a2ib5r().s[0]++, {
  primary: '#23ba16',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb'
});
export default function EmptyState(_ref) {
  var title = _ref.title,
    message = _ref.message,
    onAction = _ref.onAction,
    _ref$actionText = _ref.actionText,
    actionText = _ref$actionText === void 0 ? (cov_26b5a2ib5r().b[0][0]++, 'Get Started') : _ref$actionText,
    _ref$compact = _ref.compact,
    compact = _ref$compact === void 0 ? (cov_26b5a2ib5r().b[1][0]++, false) : _ref$compact;
  cov_26b5a2ib5r().f[0]++;
  cov_26b5a2ib5r().s[1]++;
  return _jsx(View, {
    style: [styles.container, (cov_26b5a2ib5r().b[2][0]++, compact) && (cov_26b5a2ib5r().b[2][1]++, styles.compactContainer)],
    children: _jsxs(View, {
      style: styles.content,
      children: [_jsx(View, {
        style: [styles.iconContainer, (cov_26b5a2ib5r().b[3][0]++, compact) && (cov_26b5a2ib5r().b[3][1]++, styles.compactIcon)],
        children: _jsx(Target, {
          size: compact ? (cov_26b5a2ib5r().b[4][0]++, 32) : (cov_26b5a2ib5r().b[4][1]++, 48),
          color: colors.primary
        })
      }), _jsx(Text, {
        style: [styles.title, (cov_26b5a2ib5r().b[5][0]++, compact) && (cov_26b5a2ib5r().b[5][1]++, styles.compactTitle)],
        children: title
      }), _jsx(Text, {
        style: [styles.message, (cov_26b5a2ib5r().b[6][0]++, compact) && (cov_26b5a2ib5r().b[6][1]++, styles.compactMessage)],
        children: message
      }), (cov_26b5a2ib5r().b[7][0]++, onAction) && (cov_26b5a2ib5r().b[7][1]++, _jsx(Button, {
        title: actionText,
        onPress: onAction,
        style: [styles.actionButton, (cov_26b5a2ib5r().b[8][0]++, compact) && (cov_26b5a2ib5r().b[8][1]++, styles.compactButton)].filter(Boolean),
        size: compact ? (cov_26b5a2ib5r().b[9][0]++, 'small') : (cov_26b5a2ib5r().b[9][1]++, 'medium')
      }))]
    })
  });
}
var styles = (cov_26b5a2ib5r().s[2]++, StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24
  },
  compactContainer: {
    flex: 0,
    paddingVertical: 32
  },
  content: {
    alignItems: 'center',
    maxWidth: 280
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24
  },
  compactIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: 16
  },
  title: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: colors.dark,
    marginBottom: 8,
    textAlign: 'center'
  },
  compactTitle: {
    fontSize: 16,
    marginBottom: 6
  },
  message: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24
  },
  compactMessage: {
    fontSize: 14,
    marginBottom: 16
  },
  actionButton: {
    minWidth: 120
  },
  compactButton: {
    minWidth: 100
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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