import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react-native';
import Button from '@/components/ui/Button';
import { sentryService } from '@/services/monitoring/SentryService';
import { postHogService } from '@/services/analytics/PostHogService';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

const colors = {
  primary: '#23ba16',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  red: '#ef4444',
  yellow: '#f59e0b',
};

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to crash reporting service (e.g., Sentry, Crashlytics)
    this.logErrorToService(error, errorInfo);
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    try {
      const errorReport = {
        errorId: this.state.errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'mobile-app',
        url: typeof window !== 'undefined' ? window.location?.href : 'mobile-app',
      };

      console.log('Error Report:', errorReport);

      // Send to Sentry error monitoring
      if (sentryService.isReady()) {
        const eventId = sentryService.captureException(error, {
          tags: {
            error_boundary: 'true',
            error_id: this.state.errorId,
          },
          extra: {
            componentStack: errorInfo.componentStack,
            errorReport,
          },
          level: 'error',
        });

        console.log('Error sent to Sentry with ID:', eventId);
      }

      // Track error in analytics
      if (postHogService.isReady()) {
        postHogService.trackError('error_boundary', error.message, 'component_error');
      }

      // Add breadcrumb for future errors
      if (sentryService.isReady()) {
        sentryService.addBreadcrumb({
          message: `Error boundary caught: ${error.message}`,
          category: 'error',
          level: 'error',
          data: {
            errorId: this.state.errorId,
            componentStack: errorInfo.componentStack,
          },
        });
      }
    } catch (loggingError) {
      console.error('Failed to log error:', loggingError);
    }
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  private handleGoHome = () => {
    // In a real app with navigation, you would navigate to home
    // router.replace('/');
    this.handleRetry();
  };

  private handleReportBug = () => {
    const { error, errorInfo, errorId } = this.state;

    const bugReport = {
      errorId,
      message: error?.message || 'Unknown error',
      stack: error?.stack || 'No stack trace',
      componentStack: errorInfo?.componentStack || 'No component stack',
      timestamp: new Date().toISOString(),
    };

    Alert.alert(
      'Report Bug',
      'Thank you for helping us improve AceMind! The error details have been collected and sent to our monitoring system.',
      [
        {
          text: 'Send Additional Report',
          onPress: () => {
            // Send additional context to Sentry
            if (sentryService.isReady() && error) {
              sentryService.captureMessage(
                `User reported bug: ${error.message}`,
                'info',
                {
                  tags: {
                    user_reported: 'true',
                    error_id: errorId,
                  },
                  extra: bugReport,
                }
              );
            }

            // Track user feedback in analytics
            if (postHogService.isReady()) {
              postHogService.capture('error_reported_by_user', {
                error_id: errorId,
                error_message: error?.message,
              });
            }

            console.log('Bug Report:', bugReport);
            Alert.alert('Success', 'Additional bug report sent successfully!');
          },
        },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  private renderErrorDetails = () => {
    const { error, errorInfo } = this.state;
    
    if (!this.props.showDetails) return null;

    return (
      <View style={styles.detailsContainer}>
        <TouchableOpacity style={styles.detailsHeader}>
          <Text style={styles.detailsTitle}>Error Details</Text>
          <Bug size={16} color={colors.gray} />
        </TouchableOpacity>
        
        <ScrollView style={styles.detailsScroll} showsVerticalScrollIndicator={false}>
          <View style={styles.errorSection}>
            <Text style={styles.errorSectionTitle}>Error Message:</Text>
            <Text style={styles.errorText}>{error?.message || 'Unknown error'}</Text>
          </View>
          
          {error?.stack && (
            <View style={styles.errorSection}>
              <Text style={styles.errorSectionTitle}>Stack Trace:</Text>
              <Text style={styles.errorText}>{error.stack}</Text>
            </View>
          )}
          
          {errorInfo?.componentStack && (
            <View style={styles.errorSection}>
              <Text style={styles.errorSectionTitle}>Component Stack:</Text>
              <Text style={styles.errorText}>{errorInfo.componentStack}</Text>
            </View>
          )}
        </ScrollView>
      </View>
    );
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <View style={styles.container}>
          <LinearGradient
            colors={['#ef4444', '#dc2626', '#b91c1c']}
            style={styles.gradient}
          >
            <View style={styles.content}>
              <View style={styles.iconContainer}>
                <AlertTriangle size={64} color={colors.white} />
              </View>
              
              <Text style={styles.title}>Oops! Something went wrong</Text>
              <Text style={styles.subtitle}>
                We're sorry for the inconvenience. The app encountered an unexpected error.
              </Text>
              
              <View style={styles.errorInfo}>
                <Text style={styles.errorId}>Error ID: {this.state.errorId}</Text>
                <Text style={styles.errorMessage}>
                  {this.state.error?.message || 'Unknown error occurred'}
                </Text>
              </View>

              <View style={styles.actions}>
                <Button
                  title="Try Again"
                  onPress={this.handleRetry}
                  style={styles.retryButton}
                  icon={<RefreshCw size={20} color={colors.white} />}
                />
                
                <Button
                  title="Go to Home"
                  onPress={this.handleGoHome}
                  style={styles.homeButton}
                  variant="outline"
                  icon={<Home size={20} color={colors.white} />}
                />
                
                <Button
                  title="Report Bug"
                  onPress={this.handleReportBug}
                  style={styles.reportButton}
                  variant="outline"
                  icon={<Bug size={20} color={colors.white} />}
                />
              </View>

              {this.renderErrorDetails()}
              
              <View style={styles.footer}>
                <Text style={styles.footerText}>
                  If this problem persists, please contact our support team.
                </Text>
              </View>
            </View>
          </LinearGradient>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  iconContainer: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.white,
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  errorInfo: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    width: '100%',
  },
  errorId: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: 8,
    fontFamily: 'monospace',
  },
  errorMessage: {
    fontSize: 14,
    color: colors.white,
    fontWeight: '500',
  },
  actions: {
    width: '100%',
    gap: 12,
  },
  retryButton: {
    backgroundColor: colors.white,
  },
  homeButton: {
    borderColor: colors.white,
  },
  reportButton: {
    borderColor: colors.white,
  },
  detailsContainer: {
    width: '100%',
    marginTop: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    maxHeight: 200,
  },
  detailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
  },
  detailsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.white,
  },
  detailsScroll: {
    flex: 1,
    padding: 12,
  },
  errorSection: {
    marginBottom: 16,
  },
  errorSectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 4,
  },
  errorText: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: 'monospace',
    lineHeight: 16,
  },
  footer: {
    marginTop: 24,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  footerText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
});

export default ErrorBoundary;
