86bafc6521487dd944e507c95e8369d2
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.matchRecordingService = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _VideoRecordingService = require("../video/VideoRecordingService");
var _MatchRepository = require("../database/MatchRepository");
var _FileUploadService = require("../storage/FileUploadService");
var _performance = require("../../../utils/performance");
function cov_2k9ta5zj0b() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\services\\match\\MatchRecordingService.ts";
  var hash = "de6c751edc0452f88f5950953225c4841ba4ba64";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\services\\match\\MatchRecordingService.ts",
    statementMap: {
      "0": {
        start: {
          line: 30,
          column: 48
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "1": {
        start: {
          line: 31,
          column: 73
        },
        end: {
          line: 31,
          column: 75
        }
      },
      "2": {
        start: {
          line: 32,
          column: 60
        },
        end: {
          line: 32,
          column: 62
        }
      },
      "3": {
        start: {
          line: 33,
          column: 56
        },
        end: {
          line: 33,
          column: 60
        }
      },
      "4": {
        start: {
          line: 34,
          column: 48
        },
        end: {
          line: 34,
          column: 52
        }
      },
      "5": {
        start: {
          line: 35,
          column: 52
        },
        end: {
          line: 35,
          column: 56
        }
      },
      "6": {
        start: {
          line: 44,
          column: 4
        },
        end: {
          line: 118,
          column: 5
        }
      },
      "7": {
        start: {
          line: 45,
          column: 6
        },
        end: {
          line: 45,
          column: 56
        }
      },
      "8": {
        start: {
          line: 48,
          column: 6
        },
        end: {
          line: 48,
          column: 43
        }
      },
      "9": {
        start: {
          line: 51,
          column: 6
        },
        end: {
          line: 53,
          column: 7
        }
      },
      "10": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 52,
          column: 74
        }
      },
      "11": {
        start: {
          line: 56,
          column: 45
        },
        end: {
          line: 67,
          column: 7
        }
      },
      "12": {
        start: {
          line: 70,
          column: 36
        },
        end: {
          line: 82,
          column: 7
        }
      },
      "13": {
        start: {
          line: 85,
          column: 6
        },
        end: {
          line: 87,
          column: 7
        }
      },
      "14": {
        start: {
          line: 86,
          column: 8
        },
        end: {
          line: 86,
          column: 72
        }
      },
      "15": {
        start: {
          line: 90,
          column: 25
        },
        end: {
          line: 90,
          column: 71
        }
      },
      "16": {
        start: {
          line: 91,
          column: 6
        },
        end: {
          line: 93,
          column: 7
        }
      },
      "17": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 92,
          column: 80
        }
      },
      "18": {
        start: {
          line: 95,
          column: 6
        },
        end: {
          line: 95,
          column: 45
        }
      },
      "19": {
        start: {
          line: 96,
          column: 6
        },
        end: {
          line: 96,
          column: 61
        }
      },
      "20": {
        start: {
          line: 99,
          column: 6
        },
        end: {
          line: 99,
          column: 46
        }
      },
      "21": {
        start: {
          line: 101,
          column: 6
        },
        end: {
          line: 101,
          column: 36
        }
      },
      "22": {
        start: {
          line: 102,
          column: 6
        },
        end: {
          line: 102,
          column: 36
        }
      },
      "23": {
        start: {
          line: 105,
          column: 6
        },
        end: {
          line: 105,
          column: 27
        }
      },
      "24": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 107,
          column: 54
        }
      },
      "25": {
        start: {
          line: 108,
          column: 6
        },
        end: {
          line: 108,
          column: 21
        }
      },
      "26": {
        start: {
          line: 110,
          column: 6
        },
        end: {
          line: 110,
          column: 63
        }
      },
      "27": {
        start: {
          line: 113,
          column: 6
        },
        end: {
          line: 115,
          column: 7
        }
      },
      "28": {
        start: {
          line: 114,
          column: 8
        },
        end: {
          line: 114,
          column: 42
        }
      },
      "29": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 117,
          column: 18
        }
      },
      "30": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 132,
          column: 5
        }
      },
      "31": {
        start: {
          line: 131,
          column: 6
        },
        end: {
          line: 131,
          column: 49
        }
      },
      "32": {
        start: {
          line: 134,
          column: 4
        },
        end: {
          line: 201,
          column: 5
        }
      },
      "33": {
        start: {
          line: 135,
          column: 22
        },
        end: {
          line: 135,
          column: 41
        }
      },
      "34": {
        start: {
          line: 136,
          column: 25
        },
        end: {
          line: 136,
          column: 43
        }
      },
      "35": {
        start: {
          line: 137,
          column: 26
        },
        end: {
          line: 137,
          column: 45
        }
      },
      "36": {
        start: {
          line: 140,
          column: 35
        },
        end: {
          line: 148,
          column: 7
        }
      },
      "37": {
        start: {
          line: 151,
          column: 27
        },
        end: {
          line: 157,
          column: 7
        }
      },
      "38": {
        start: {
          line: 160,
          column: 6
        },
        end: {
          line: 160,
          column: 65
        }
      },
      "39": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 163,
          column: 41
        }
      },
      "40": {
        start: {
          line: 164,
          column: 6
        },
        end: {
          line: 164,
          column: 57
        }
      },
      "41": {
        start: {
          line: 167,
          column: 26
        },
        end: {
          line: 167,
          column: 79
        }
      },
      "42": {
        start: {
          line: 168,
          column: 28
        },
        end: {
          line: 168,
          column: 98
        }
      },
      "43": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 182,
          column: 7
        }
      },
      "44": {
        start: {
          line: 171,
          column: 8
        },
        end: {
          line: 171,
          column: 29
        }
      },
      "45": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 172,
          column: 32
        }
      },
      "46": {
        start: {
          line: 173,
          column: 13
        },
        end: {
          line: 182,
          column: 7
        }
      },
      "47": {
        start: {
          line: 175,
          column: 29
        },
        end: {
          line: 178,
          column: 9
        }
      },
      "48": {
        start: {
          line: 179,
          column: 8
        },
        end: {
          line: 181,
          column: 9
        }
      },
      "49": {
        start: {
          line: 180,
          column: 10
        },
        end: {
          line: 180,
          column: 32
        }
      },
      "50": {
        start: {
          line: 184,
          column: 6
        },
        end: {
          line: 194,
          column: 7
        }
      },
      "51": {
        start: {
          line: 185,
          column: 8
        },
        end: {
          line: 185,
          column: 30
        }
      },
      "52": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 193,
          column: 9
        }
      },
      "53": {
        start: {
          line: 189,
          column: 10
        },
        end: {
          line: 189,
          column: 58
        }
      },
      "54": {
        start: {
          line: 191,
          column: 10
        },
        end: {
          line: 191,
          column: 70
        }
      },
      "55": {
        start: {
          line: 196,
          column: 6
        },
        end: {
          line: 196,
          column: 34
        }
      },
      "56": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 197,
          column: 36
        }
      },
      "57": {
        start: {
          line: 199,
          column: 6
        },
        end: {
          line: 199,
          column: 51
        }
      },
      "58": {
        start: {
          line: 200,
          column: 6
        },
        end: {
          line: 200,
          column: 18
        }
      },
      "59": {
        start: {
          line: 208,
          column: 4
        },
        end: {
          line: 210,
          column: 5
        }
      },
      "60": {
        start: {
          line: 209,
          column: 6
        },
        end: {
          line: 209,
          column: 13
        }
      },
      "61": {
        start: {
          line: 212,
          column: 4
        },
        end: {
          line: 232,
          column: 5
        }
      },
      "62": {
        start: {
          line: 213,
          column: 6
        },
        end: {
          line: 213,
          column: 42
        }
      },
      "63": {
        start: {
          line: 214,
          column: 6
        },
        end: {
          line: 214,
          column: 50
        }
      },
      "64": {
        start: {
          line: 215,
          column: 6
        },
        end: {
          line: 215,
          column: 50
        }
      },
      "65": {
        start: {
          line: 218,
          column: 6
        },
        end: {
          line: 220,
          column: 7
        }
      },
      "66": {
        start: {
          line: 219,
          column: 8
        },
        end: {
          line: 219,
          column: 53
        }
      },
      "67": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 227,
          column: 7
        }
      },
      "68": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 223,
          column: 68
        }
      },
      "69": {
        start: {
          line: 225,
          column: 8
        },
        end: {
          line: 225,
          column: 68
        }
      },
      "70": {
        start: {
          line: 228,
          column: 6
        },
        end: {
          line: 228,
          column: 36
        }
      },
      "71": {
        start: {
          line: 230,
          column: 6
        },
        end: {
          line: 230,
          column: 53
        }
      },
      "72": {
        start: {
          line: 231,
          column: 6
        },
        end: {
          line: 231,
          column: 18
        }
      },
      "73": {
        start: {
          line: 239,
          column: 4
        },
        end: {
          line: 241,
          column: 5
        }
      },
      "74": {
        start: {
          line: 240,
          column: 6
        },
        end: {
          line: 240,
          column: 13
        }
      },
      "75": {
        start: {
          line: 243,
          column: 4
        },
        end: {
          line: 265,
          column: 5
        }
      },
      "76": {
        start: {
          line: 244,
          column: 28
        },
        end: {
          line: 244,
          column: 71
        }
      },
      "77": {
        start: {
          line: 245,
          column: 6
        },
        end: {
          line: 245,
          column: 63
        }
      },
      "78": {
        start: {
          line: 246,
          column: 6
        },
        end: {
          line: 246,
          column: 43
        }
      },
      "79": {
        start: {
          line: 247,
          column: 6
        },
        end: {
          line: 247,
          column: 41
        }
      },
      "80": {
        start: {
          line: 248,
          column: 6
        },
        end: {
          line: 248,
          column: 53
        }
      },
      "81": {
        start: {
          line: 251,
          column: 6
        },
        end: {
          line: 253,
          column: 7
        }
      },
      "82": {
        start: {
          line: 252,
          column: 8
        },
        end: {
          line: 252,
          column: 54
        }
      },
      "83": {
        start: {
          line: 255,
          column: 6
        },
        end: {
          line: 260,
          column: 7
        }
      },
      "84": {
        start: {
          line: 256,
          column: 8
        },
        end: {
          line: 256,
          column: 68
        }
      },
      "85": {
        start: {
          line: 258,
          column: 8
        },
        end: {
          line: 258,
          column: 68
        }
      },
      "86": {
        start: {
          line: 261,
          column: 6
        },
        end: {
          line: 261,
          column: 36
        }
      },
      "87": {
        start: {
          line: 263,
          column: 6
        },
        end: {
          line: 263,
          column: 54
        }
      },
      "88": {
        start: {
          line: 264,
          column: 6
        },
        end: {
          line: 264,
          column: 18
        }
      },
      "89": {
        start: {
          line: 272,
          column: 4
        },
        end: {
          line: 274,
          column: 5
        }
      },
      "90": {
        start: {
          line: 273,
          column: 6
        },
        end: {
          line: 273,
          column: 49
        }
      },
      "91": {
        start: {
          line: 276,
          column: 4
        },
        end: {
          line: 337,
          column: 5
        }
      },
      "92": {
        start: {
          line: 277,
          column: 6
        },
        end: {
          line: 277,
          column: 54
        }
      },
      "93": {
        start: {
          line: 279,
          column: 22
        },
        end: {
          line: 279,
          column: 41
        }
      },
      "94": {
        start: {
          line: 280,
          column: 22
        },
        end: {
          line: 280,
          column: 32
        }
      },
      "95": {
        start: {
          line: 281,
          column: 28
        },
        end: {
          line: 281,
          column: 99
        }
      },
      "96": {
        start: {
          line: 284,
          column: 6
        },
        end: {
          line: 284,
          column: 64
        }
      },
      "97": {
        start: {
          line: 285,
          column: 6
        },
        end: {
          line: 285,
          column: 73
        }
      },
      "98": {
        start: {
          line: 286,
          column: 6
        },
        end: {
          line: 286,
          column: 41
        }
      },
      "99": {
        start: {
          line: 289,
          column: 6
        },
        end: {
          line: 317,
          column: 7
        }
      },
      "100": {
        start: {
          line: 290,
          column: 28
        },
        end: {
          line: 290,
          column: 71
        }
      },
      "101": {
        start: {
          line: 293,
          column: 29
        },
        end: {
          line: 295,
          column: 10
        }
      },
      "102": {
        start: {
          line: 297,
          column: 8
        },
        end: {
          line: 316,
          column: 9
        }
      },
      "103": {
        start: {
          line: 298,
          column: 10
        },
        end: {
          line: 298,
          column: 57
        }
      },
      "104": {
        start: {
          line: 299,
          column: 10
        },
        end: {
          line: 299,
          column: 68
        }
      },
      "105": {
        start: {
          line: 300,
          column: 10
        },
        end: {
          line: 300,
          column: 68
        }
      },
      "106": {
        start: {
          line: 303,
          column: 10
        },
        end: {
          line: 315,
          column: 11
        }
      },
      "107": {
        start: {
          line: 304,
          column: 36
        },
        end: {
          line: 310,
          column: 13
        }
      },
      "108": {
        start: {
          line: 312,
          column: 12
        },
        end: {
          line: 314,
          column: 13
        }
      },
      "109": {
        start: {
          line: 313,
          column: 14
        },
        end: {
          line: 313,
          column: 73
        }
      },
      "110": {
        start: {
          line: 320,
          column: 6
        },
        end: {
          line: 320,
          column: 83
        }
      },
      "111": {
        start: {
          line: 323,
          column: 6
        },
        end: {
          line: 323,
          column: 54
        }
      },
      "112": {
        start: {
          line: 326,
          column: 25
        },
        end: {
          line: 326,
          column: 45
        }
      },
      "113": {
        start: {
          line: 329,
          column: 6
        },
        end: {
          line: 329,
          column: 33
        }
      },
      "114": {
        start: {
          line: 330,
          column: 6
        },
        end: {
          line: 330,
          column: 36
        }
      },
      "115": {
        start: {
          line: 332,
          column: 6
        },
        end: {
          line: 332,
          column: 52
        }
      },
      "116": {
        start: {
          line: 333,
          column: 6
        },
        end: {
          line: 333,
          column: 24
        }
      },
      "117": {
        start: {
          line: 335,
          column: 6
        },
        end: {
          line: 335,
          column: 51
        }
      },
      "118": {
        start: {
          line: 336,
          column: 6
        },
        end: {
          line: 336,
          column: 18
        }
      },
      "119": {
        start: {
          line: 344,
          column: 4
        },
        end: {
          line: 346,
          column: 5
        }
      },
      "120": {
        start: {
          line: 345,
          column: 6
        },
        end: {
          line: 345,
          column: 13
        }
      },
      "121": {
        start: {
          line: 348,
          column: 4
        },
        end: {
          line: 366,
          column: 5
        }
      },
      "122": {
        start: {
          line: 350,
          column: 6
        },
        end: {
          line: 352,
          column: 7
        }
      },
      "123": {
        start: {
          line: 351,
          column: 8
        },
        end: {
          line: 351,
          column: 52
        }
      },
      "124": {
        start: {
          line: 355,
          column: 6
        },
        end: {
          line: 355,
          column: 53
        }
      },
      "125": {
        start: {
          line: 358,
          column: 6
        },
        end: {
          line: 358,
          column: 66
        }
      },
      "126": {
        start: {
          line: 361,
          column: 6
        },
        end: {
          line: 361,
          column: 33
        }
      },
      "127": {
        start: {
          line: 362,
          column: 6
        },
        end: {
          line: 362,
          column: 36
        }
      },
      "128": {
        start: {
          line: 364,
          column: 6
        },
        end: {
          line: 364,
          column: 54
        }
      },
      "129": {
        start: {
          line: 365,
          column: 6
        },
        end: {
          line: 365,
          column: 18
        }
      },
      "130": {
        start: {
          line: 373,
          column: 4
        },
        end: {
          line: 373,
          column: 31
        }
      },
      "131": {
        start: {
          line: 380,
          column: 4
        },
        end: {
          line: 380,
          column: 41
        }
      },
      "132": {
        start: {
          line: 387,
          column: 4
        },
        end: {
          line: 387,
          column: 78
        }
      },
      "133": {
        start: {
          line: 387,
          column: 62
        },
        end: {
          line: 387,
          column: 76
        }
      },
      "134": {
        start: {
          line: 394,
          column: 4
        },
        end: {
          line: 394,
          column: 39
        }
      },
      "135": {
        start: {
          line: 401,
          column: 4
        },
        end: {
          line: 401,
          column: 74
        }
      },
      "136": {
        start: {
          line: 401,
          column: 58
        },
        end: {
          line: 401,
          column: 72
        }
      },
      "137": {
        start: {
          line: 407,
          column: 4
        },
        end: {
          line: 409,
          column: 5
        }
      },
      "138": {
        start: {
          line: 408,
          column: 6
        },
        end: {
          line: 408,
          column: 51
        }
      },
      "139": {
        start: {
          line: 410,
          column: 4
        },
        end: {
          line: 412,
          column: 5
        }
      },
      "140": {
        start: {
          line: 411,
          column: 6
        },
        end: {
          line: 411,
          column: 45
        }
      },
      "141": {
        start: {
          line: 413,
          column: 4
        },
        end: {
          line: 415,
          column: 5
        }
      },
      "142": {
        start: {
          line: 414,
          column: 6
        },
        end: {
          line: 414,
          column: 48
        }
      },
      "143": {
        start: {
          line: 416,
          column: 4
        },
        end: {
          line: 418,
          column: 5
        }
      },
      "144": {
        start: {
          line: 417,
          column: 6
        },
        end: {
          line: 417,
          column: 50
        }
      },
      "145": {
        start: {
          line: 419,
          column: 4
        },
        end: {
          line: 421,
          column: 5
        }
      },
      "146": {
        start: {
          line: 420,
          column: 6
        },
        end: {
          line: 420,
          column: 51
        }
      },
      "147": {
        start: {
          line: 425,
          column: 20
        },
        end: {
          line: 425,
          column: 50
        }
      },
      "148": {
        start: {
          line: 426,
          column: 4
        },
        end: {
          line: 432,
          column: 6
        }
      },
      "149": {
        start: {
          line: 436,
          column: 4
        },
        end: {
          line: 460,
          column: 6
        }
      },
      "150": {
        start: {
          line: 472,
          column: 25
        },
        end: {
          line: 472,
          column: 44
        }
      },
      "151": {
        start: {
          line: 475,
          column: 4
        },
        end: {
          line: 483,
          column: 5
        }
      },
      "152": {
        start: {
          line: 476,
          column: 6
        },
        end: {
          line: 482,
          column: 9
        }
      },
      "153": {
        start: {
          line: 485,
          column: 23
        },
        end: {
          line: 485,
          column: 55
        }
      },
      "154": {
        start: {
          line: 488,
          column: 4
        },
        end: {
          line: 493,
          column: 5
        }
      },
      "155": {
        start: {
          line: 495,
          column: 4
        },
        end: {
          line: 495,
          column: 24
        }
      },
      "156": {
        start: {
          line: 499,
          column: 4
        },
        end: {
          line: 499,
          column: 35
        }
      },
      "157": {
        start: {
          line: 501,
          column: 4
        },
        end: {
          line: 503,
          column: 5
        }
      },
      "158": {
        start: {
          line: 502,
          column: 6
        },
        end: {
          line: 502,
          column: 34
        }
      },
      "159": {
        start: {
          line: 505,
          column: 4
        },
        end: {
          line: 521,
          column: 5
        }
      },
      "160": {
        start: {
          line: 507,
          column: 8
        },
        end: {
          line: 507,
          column: 26
        }
      },
      "161": {
        start: {
          line: 508,
          column: 8
        },
        end: {
          line: 508,
          column: 14
        }
      },
      "162": {
        start: {
          line: 510,
          column: 8
        },
        end: {
          line: 510,
          column: 34
        }
      },
      "163": {
        start: {
          line: 511,
          column: 8
        },
        end: {
          line: 511,
          column: 14
        }
      },
      "164": {
        start: {
          line: 513,
          column: 8
        },
        end: {
          line: 513,
          column: 29
        }
      },
      "165": {
        start: {
          line: 514,
          column: 8
        },
        end: {
          line: 514,
          column: 14
        }
      },
      "166": {
        start: {
          line: 516,
          column: 8
        },
        end: {
          line: 516,
          column: 36
        }
      },
      "167": {
        start: {
          line: 517,
          column: 8
        },
        end: {
          line: 517,
          column: 14
        }
      },
      "168": {
        start: {
          line: 519,
          column: 8
        },
        end: {
          line: 519,
          column: 34
        }
      },
      "169": {
        start: {
          line: 520,
          column: 8
        },
        end: {
          line: 520,
          column: 14
        }
      },
      "170": {
        start: {
          line: 529,
          column: 4
        },
        end: {
          line: 533,
          column: 5
        }
      },
      "171": {
        start: {
          line: 530,
          column: 6
        },
        end: {
          line: 532,
          column: 8
        }
      },
      "172": {
        start: {
          line: 535,
          column: 4
        },
        end: {
          line: 537,
          column: 5
        }
      },
      "173": {
        start: {
          line: 536,
          column: 6
        },
        end: {
          line: 536,
          column: 98
        }
      },
      "174": {
        start: {
          line: 540,
          column: 4
        },
        end: {
          line: 544,
          column: 5
        }
      },
      "175": {
        start: {
          line: 541,
          column: 6
        },
        end: {
          line: 543,
          column: 8
        }
      },
      "176": {
        start: {
          line: 547,
          column: 4
        },
        end: {
          line: 547,
          column: 61
        }
      },
      "177": {
        start: {
          line: 554,
          column: 22
        },
        end: {
          line: 554,
          column: 35
        }
      },
      "178": {
        start: {
          line: 555,
          column: 26
        },
        end: {
          line: 555,
          column: 43
        }
      },
      "179": {
        start: {
          line: 558,
          column: 4
        },
        end: {
          line: 560,
          column: 5
        }
      },
      "180": {
        start: {
          line: 559,
          column: 6
        },
        end: {
          line: 559,
          column: 18
        }
      },
      "181": {
        start: {
          line: 561,
          column: 4
        },
        end: {
          line: 563,
          column: 5
        }
      },
      "182": {
        start: {
          line: 562,
          column: 6
        },
        end: {
          line: 562,
          column: 18
        }
      },
      "183": {
        start: {
          line: 564,
          column: 4
        },
        end: {
          line: 566,
          column: 5
        }
      },
      "184": {
        start: {
          line: 565,
          column: 6
        },
        end: {
          line: 565,
          column: 18
        }
      },
      "185": {
        start: {
          line: 568,
          column: 4
        },
        end: {
          line: 568,
          column: 17
        }
      },
      "186": {
        start: {
          line: 577,
          column: 4
        },
        end: {
          line: 577,
          column: 17
        }
      },
      "187": {
        start: {
          line: 584,
          column: 22
        },
        end: {
          line: 584,
          column: 52
        }
      },
      "188": {
        start: {
          line: 585,
          column: 4
        },
        end: {
          line: 585,
          column: 69
        }
      },
      "189": {
        start: {
          line: 591,
          column: 4
        },
        end: {
          line: 644,
          column: 5
        }
      },
      "190": {
        start: {
          line: 593,
          column: 24
        },
        end: {
          line: 611,
          column: 7
        }
      },
      "191": {
        start: {
          line: 614,
          column: 21
        },
        end: {
          line: 614,
          column: 22
        }
      },
      "192": {
        start: {
          line: 615,
          column: 26
        },
        end: {
          line: 615,
          column: 27
        }
      },
      "193": {
        start: {
          line: 617,
          column: 6
        },
        end: {
          line: 638,
          column: 7
        }
      },
      "194": {
        start: {
          line: 618,
          column: 8
        },
        end: {
          line: 637,
          column: 9
        }
      },
      "195": {
        start: {
          line: 619,
          column: 25
        },
        end: {
          line: 619,
          column: 69
        }
      },
      "196": {
        start: {
          line: 621,
          column: 10
        },
        end: {
          line: 628,
          column: 11
        }
      },
      "197": {
        start: {
          line: 622,
          column: 12
        },
        end: {
          line: 624,
          column: 13
        }
      },
      "198": {
        start: {
          line: 623,
          column: 14
        },
        end: {
          line: 623,
          column: 61
        }
      },
      "199": {
        start: {
          line: 625,
          column: 12
        },
        end: {
          line: 625,
          column: 23
        }
      },
      "200": {
        start: {
          line: 626,
          column: 12
        },
        end: {
          line: 626,
          column: 79
        }
      },
      "201": {
        start: {
          line: 626,
          column: 41
        },
        end: {
          line: 626,
          column: 77
        }
      },
      "202": {
        start: {
          line: 627,
          column: 12
        },
        end: {
          line: 627,
          column: 21
        }
      },
      "203": {
        start: {
          line: 630,
          column: 10
        },
        end: {
          line: 630,
          column: 88
        }
      },
      "204": {
        start: {
          line: 632,
          column: 10
        },
        end: {
          line: 632,
          column: 21
        }
      },
      "205": {
        start: {
          line: 633,
          column: 10
        },
        end: {
          line: 635,
          column: 11
        }
      },
      "206": {
        start: {
          line: 634,
          column: 12
        },
        end: {
          line: 634,
          column: 24
        }
      },
      "207": {
        start: {
          line: 636,
          column: 10
        },
        end: {
          line: 636,
          column: 77
        }
      },
      "208": {
        start: {
          line: 636,
          column: 39
        },
        end: {
          line: 636,
          column: 75
        }
      },
      "209": {
        start: {
          line: 640,
          column: 6
        },
        end: {
          line: 640,
          column: 81
        }
      },
      "210": {
        start: {
          line: 642,
          column: 6
        },
        end: {
          line: 642,
          column: 62
        }
      },
      "211": {
        start: {
          line: 643,
          column: 6
        },
        end: {
          line: 643,
          column: 69
        }
      },
      "212": {
        start: {
          line: 648,
          column: 4
        },
        end: {
          line: 686,
          column: 5
        }
      },
      "213": {
        start: {
          line: 649,
          column: 6
        },
        end: {
          line: 651,
          column: 7
        }
      },
      "214": {
        start: {
          line: 650,
          column: 8
        },
        end: {
          line: 650,
          column: 59
        }
      },
      "215": {
        start: {
          line: 653,
          column: 25
        },
        end: {
          line: 658,
          column: 7
        }
      },
      "216": {
        start: {
          line: 661,
          column: 6
        },
        end: {
          line: 670,
          column: 7
        }
      },
      "217": {
        start: {
          line: 662,
          column: 8
        },
        end: {
          line: 662,
          column: 92
        }
      },
      "218": {
        start: {
          line: 663,
          column: 8
        },
        end: {
          line: 665,
          column: 10
        }
      },
      "219": {
        start: {
          line: 666,
          column: 8
        },
        end: {
          line: 666,
          column: 76
        }
      },
      "220": {
        start: {
          line: 667,
          column: 8
        },
        end: {
          line: 667,
          column: 90
        }
      },
      "221": {
        start: {
          line: 668,
          column: 8
        },
        end: {
          line: 668,
          column: 50
        }
      },
      "222": {
        start: {
          line: 669,
          column: 8
        },
        end: {
          line: 669,
          column: 52
        }
      },
      "223": {
        start: {
          line: 672,
          column: 21
        },
        end: {
          line: 672,
          column: 76
        }
      },
      "224": {
        start: {
          line: 674,
          column: 6
        },
        end: {
          line: 676,
          column: 7
        }
      },
      "225": {
        start: {
          line: 675,
          column: 8
        },
        end: {
          line: 675,
          column: 38
        }
      },
      "226": {
        start: {
          line: 679,
          column: 6
        },
        end: {
          line: 682,
          column: 8
        }
      },
      "227": {
        start: {
          line: 684,
          column: 6
        },
        end: {
          line: 684,
          column: 64
        }
      },
      "228": {
        start: {
          line: 685,
          column: 6
        },
        end: {
          line: 685,
          column: 52
        }
      },
      "229": {
        start: {
          line: 693,
          column: 4
        },
        end: {
          line: 695,
          column: 5
        }
      },
      "230": {
        start: {
          line: 694,
          column: 6
        },
        end: {
          line: 694,
          column: 19
        }
      },
      "231": {
        start: {
          line: 697,
          column: 4
        },
        end: {
          line: 699,
          column: 18
        }
      },
      "232": {
        start: {
          line: 698,
          column: 18
        },
        end: {
          line: 698,
          column: 57
        }
      },
      "233": {
        start: {
          line: 706,
          column: 4
        },
        end: {
          line: 710,
          column: 5
        }
      },
      "234": {
        start: {
          line: 707,
          column: 6
        },
        end: {
          line: 707,
          column: 19
        }
      },
      "235": {
        start: {
          line: 708,
          column: 11
        },
        end: {
          line: 710,
          column: 5
        }
      },
      "236": {
        start: {
          line: 709,
          column: 6
        },
        end: {
          line: 709,
          column: 20
        }
      },
      "237": {
        start: {
          line: 711,
          column: 4
        },
        end: {
          line: 711,
          column: 18
        }
      },
      "238": {
        start: {
          line: 715,
          column: 4
        },
        end: {
          line: 715,
          column: 78
        }
      },
      "239": {
        start: {
          line: 719,
          column: 4
        },
        end: {
          line: 719,
          column: 76
        }
      },
      "240": {
        start: {
          line: 723,
          column: 4
        },
        end: {
          line: 723,
          column: 77
        }
      },
      "241": {
        start: {
          line: 723,
          column: 46
        },
        end: {
          line: 723,
          column: 75
        }
      },
      "242": {
        start: {
          line: 727,
          column: 4
        },
        end: {
          line: 729,
          column: 5
        }
      },
      "243": {
        start: {
          line: 728,
          column: 6
        },
        end: {
          line: 728,
          column: 90
        }
      },
      "244": {
        start: {
          line: 728,
          column: 46
        },
        end: {
          line: 728,
          column: 88
        }
      },
      "245": {
        start: {
          line: 736,
          column: 4
        },
        end: {
          line: 749,
          column: 5
        }
      },
      "246": {
        start: {
          line: 738,
          column: 6
        },
        end: {
          line: 740,
          column: 7
        }
      },
      "247": {
        start: {
          line: 739,
          column: 8
        },
        end: {
          line: 739,
          column: 42
        }
      },
      "248": {
        start: {
          line: 743,
          column: 6
        },
        end: {
          line: 743,
          column: 45
        }
      },
      "249": {
        start: {
          line: 746,
          column: 6
        },
        end: {
          line: 746,
          column: 37
        }
      },
      "250": {
        start: {
          line: 748,
          column: 6
        },
        end: {
          line: 748,
          column: 60
        }
      },
      "251": {
        start: {
          line: 757,
          column: 4
        },
        end: {
          line: 759,
          column: 5
        }
      },
      "252": {
        start: {
          line: 758,
          column: 6
        },
        end: {
          line: 758,
          column: 39
        }
      },
      "253": {
        start: {
          line: 762,
          column: 4
        },
        end: {
          line: 764,
          column: 14
        }
      },
      "254": {
        start: {
          line: 763,
          column: 6
        },
        end: {
          line: 763,
          column: 42
        }
      },
      "255": {
        start: {
          line: 771,
          column: 4
        },
        end: {
          line: 792,
          column: 5
        }
      },
      "256": {
        start: {
          line: 772,
          column: 20
        },
        end: {
          line: 772,
          column: 55
        }
      },
      "257": {
        start: {
          line: 773,
          column: 6
        },
        end: {
          line: 775,
          column: 7
        }
      },
      "258": {
        start: {
          line: 774,
          column: 8
        },
        end: {
          line: 774,
          column: 15
        }
      },
      "259": {
        start: {
          line: 778,
          column: 22
        },
        end: {
          line: 778,
          column: 32
        }
      },
      "260": {
        start: {
          line: 779,
          column: 6
        },
        end: {
          line: 779,
          column: 46
        }
      },
      "261": {
        start: {
          line: 781,
          column: 6
        },
        end: {
          line: 789,
          column: 7
        }
      },
      "262": {
        start: {
          line: 782,
          column: 8
        },
        end: {
          line: 788,
          column: 9
        }
      },
      "263": {
        start: {
          line: 783,
          column: 10
        },
        end: {
          line: 783,
          column: 50
        }
      },
      "264": {
        start: {
          line: 785,
          column: 10
        },
        end: {
          line: 785,
          column: 57
        }
      },
      "265": {
        start: {
          line: 787,
          column: 10
        },
        end: {
          line: 787,
          column: 60
        }
      },
      "266": {
        start: {
          line: 791,
          column: 6
        },
        end: {
          line: 791,
          column: 59
        }
      },
      "267": {
        start: {
          line: 799,
          column: 4
        },
        end: {
          line: 811,
          column: 5
        }
      },
      "268": {
        start: {
          line: 801,
          column: 8
        },
        end: {
          line: 801,
          column: 54
        }
      },
      "269": {
        start: {
          line: 802,
          column: 8
        },
        end: {
          line: 802,
          column: 14
        }
      },
      "270": {
        start: {
          line: 805,
          column: 8
        },
        end: {
          line: 805,
          column: 14
        }
      },
      "271": {
        start: {
          line: 808,
          column: 8
        },
        end: {
          line: 808,
          column: 14
        }
      },
      "272": {
        start: {
          line: 810,
          column: 8
        },
        end: {
          line: 810,
          column: 58
        }
      },
      "273": {
        start: {
          line: 819,
          column: 4
        },
        end: {
          line: 821,
          column: 5
        }
      },
      "274": {
        start: {
          line: 820,
          column: 6
        },
        end: {
          line: 820,
          column: 43
        }
      },
      "275": {
        start: {
          line: 824,
          column: 4
        },
        end: {
          line: 832,
          column: 15
        }
      },
      "276": {
        start: {
          line: 825,
          column: 6
        },
        end: {
          line: 831,
          column: 7
        }
      },
      "277": {
        start: {
          line: 826,
          column: 8
        },
        end: {
          line: 830,
          column: 9
        }
      },
      "278": {
        start: {
          line: 827,
          column: 10
        },
        end: {
          line: 827,
          column: 70
        }
      },
      "279": {
        start: {
          line: 829,
          column: 10
        },
        end: {
          line: 829,
          column: 52
        }
      },
      "280": {
        start: {
          line: 839,
          column: 4
        },
        end: {
          line: 862,
          column: 5
        }
      },
      "281": {
        start: {
          line: 840,
          column: 6
        },
        end: {
          line: 859,
          column: 7
        }
      },
      "282": {
        start: {
          line: 842,
          column: 8
        },
        end: {
          line: 844,
          column: 9
        }
      },
      "283": {
        start: {
          line: 843,
          column: 10
        },
        end: {
          line: 843,
          column: 54
        }
      },
      "284": {
        start: {
          line: 847,
          column: 8
        },
        end: {
          line: 850,
          column: 9
        }
      },
      "285": {
        start: {
          line: 848,
          column: 10
        },
        end: {
          line: 848,
          column: 47
        }
      },
      "286": {
        start: {
          line: 849,
          column: 10
        },
        end: {
          line: 849,
          column: 39
        }
      },
      "287": {
        start: {
          line: 852,
          column: 8
        },
        end: {
          line: 855,
          column: 9
        }
      },
      "288": {
        start: {
          line: 853,
          column: 10
        },
        end: {
          line: 853,
          column: 43
        }
      },
      "289": {
        start: {
          line: 854,
          column: 10
        },
        end: {
          line: 854,
          column: 35
        }
      },
      "290": {
        start: {
          line: 858,
          column: 8
        },
        end: {
          line: 858,
          column: 35
        }
      },
      "291": {
        start: {
          line: 861,
          column: 6
        },
        end: {
          line: 861,
          column: 57
        }
      },
      "292": {
        start: {
          line: 867,
          column: 37
        },
        end: {
          line: 867,
          column: 64
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 40,
            column: 2
          },
          end: {
            line: 40,
            column: 3
          }
        },
        loc: {
          start: {
            line: 43,
            column: 27
          },
          end: {
            line: 119,
            column: 3
          }
        },
        line: 43
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 124,
            column: 2
          },
          end: {
            line: 124,
            column: 3
          }
        },
        loc: {
          start: {
            line: 129,
            column: 19
          },
          end: {
            line: 202,
            column: 3
          }
        },
        line: 129
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 207,
            column: 2
          },
          end: {
            line: 207,
            column: 3
          }
        },
        loc: {
          start: {
            line: 207,
            column: 36
          },
          end: {
            line: 233,
            column: 3
          }
        },
        line: 207
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 238,
            column: 2
          },
          end: {
            line: 238,
            column: 3
          }
        },
        loc: {
          start: {
            line: 238,
            column: 37
          },
          end: {
            line: 266,
            column: 3
          }
        },
        line: 238
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 271,
            column: 2
          },
          end: {
            line: 271,
            column: 3
          }
        },
        loc: {
          start: {
            line: 271,
            column: 44
          },
          end: {
            line: 338,
            column: 3
          }
        },
        line: 271
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 343,
            column: 2
          },
          end: {
            line: 343,
            column: 3
          }
        },
        loc: {
          start: {
            line: 343,
            column: 37
          },
          end: {
            line: 367,
            column: 3
          }
        },
        line: 343
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 372,
            column: 2
          },
          end: {
            line: 372,
            column: 3
          }
        },
        loc: {
          start: {
            line: 372,
            column: 43
          },
          end: {
            line: 374,
            column: 3
          }
        },
        line: 372
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 379,
            column: 2
          },
          end: {
            line: 379,
            column: 3
          }
        },
        loc: {
          start: {
            line: 379,
            column: 77
          },
          end: {
            line: 381,
            column: 3
          }
        },
        line: 379
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 386,
            column: 2
          },
          end: {
            line: 386,
            column: 3
          }
        },
        loc: {
          start: {
            line: 386,
            column: 80
          },
          end: {
            line: 388,
            column: 3
          }
        },
        line: 386
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 387,
            column: 57
          },
          end: {
            line: 387,
            column: 58
          }
        },
        loc: {
          start: {
            line: 387,
            column: 62
          },
          end: {
            line: 387,
            column: 76
          }
        },
        line: 387
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 393,
            column: 2
          },
          end: {
            line: 393,
            column: 3
          }
        },
        loc: {
          start: {
            line: 393,
            column: 64
          },
          end: {
            line: 395,
            column: 3
          }
        },
        line: 393
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 400,
            column: 2
          },
          end: {
            line: 400,
            column: 3
          }
        },
        loc: {
          start: {
            line: 400,
            column: 67
          },
          end: {
            line: 402,
            column: 3
          }
        },
        line: 400
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 401,
            column: 53
          },
          end: {
            line: 401,
            column: 54
          }
        },
        loc: {
          start: {
            line: 401,
            column: 58
          },
          end: {
            line: 401,
            column: 72
          }
        },
        line: 401
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 406,
            column: 2
          },
          end: {
            line: 406,
            column: 3
          }
        },
        loc: {
          start: {
            line: 406,
            column: 63
          },
          end: {
            line: 422,
            column: 3
          }
        },
        line: 406
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 424,
            column: 2
          },
          end: {
            line: 424,
            column: 3
          }
        },
        loc: {
          start: {
            line: 424,
            column: 54
          },
          end: {
            line: 433,
            column: 3
          }
        },
        line: 424
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 435,
            column: 2
          },
          end: {
            line: 435,
            column: 3
          }
        },
        loc: {
          start: {
            line: 435,
            column: 64
          },
          end: {
            line: 461,
            column: 3
          }
        },
        line: 435
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 463,
            column: 2
          },
          end: {
            line: 463,
            column: 3
          }
        },
        loc: {
          start: {
            line: 469,
            column: 16
          },
          end: {
            line: 496,
            column: 3
          }
        },
        line: 469
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 498,
            column: 2
          },
          end: {
            line: 498,
            column: 3
          }
        },
        loc: {
          start: {
            line: 498,
            column: 80
          },
          end: {
            line: 522,
            column: 3
          }
        },
        line: 498
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 527,
            column: 2
          },
          end: {
            line: 527,
            column: 3
          }
        },
        loc: {
          start: {
            line: 527,
            column: 89
          },
          end: {
            line: 548,
            column: 3
          }
        },
        line: 527
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 553,
            column: 2
          },
          end: {
            line: 553,
            column: 3
          }
        },
        loc: {
          start: {
            line: 553,
            column: 48
          },
          end: {
            line: 569,
            column: 3
          }
        },
        line: 553
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 574,
            column: 2
          },
          end: {
            line: 574,
            column: 3
          }
        },
        loc: {
          start: {
            line: 574,
            column: 69
          },
          end: {
            line: 578,
            column: 3
          }
        },
        line: 574
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 583,
            column: 2
          },
          end: {
            line: 583,
            column: 3
          }
        },
        loc: {
          start: {
            line: 583,
            column: 70
          },
          end: {
            line: 586,
            column: 3
          }
        },
        line: 583
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 590,
            column: 2
          },
          end: {
            line: 590,
            column: 3
          }
        },
        loc: {
          start: {
            line: 590,
            column: 118
          },
          end: {
            line: 645,
            column: 3
          }
        },
        line: 590
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 626,
            column: 30
          },
          end: {
            line: 626,
            column: 31
          }
        },
        loc: {
          start: {
            line: 626,
            column: 41
          },
          end: {
            line: 626,
            column: 77
          }
        },
        line: 626
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 636,
            column: 28
          },
          end: {
            line: 636,
            column: 29
          }
        },
        loc: {
          start: {
            line: 636,
            column: 39
          },
          end: {
            line: 636,
            column: 75
          }
        },
        line: 636
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 647,
            column: 2
          },
          end: {
            line: 647,
            column: 3
          }
        },
        loc: {
          start: {
            line: 647,
            column: 86
          },
          end: {
            line: 687,
            column: 3
          }
        },
        line: 647
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 692,
            column: 2
          },
          end: {
            line: 692,
            column: 3
          }
        },
        loc: {
          start: {
            line: 692,
            column: 62
          },
          end: {
            line: 700,
            column: 3
          }
        },
        line: 692
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 698,
            column: 11
          },
          end: {
            line: 698,
            column: 12
          }
        },
        loc: {
          start: {
            line: 698,
            column: 18
          },
          end: {
            line: 698,
            column: 57
          }
        },
        line: 698
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 705,
            column: 2
          },
          end: {
            line: 705,
            column: 3
          }
        },
        loc: {
          start: {
            line: 705,
            column: 91
          },
          end: {
            line: 712,
            column: 3
          }
        },
        line: 705
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 714,
            column: 2
          },
          end: {
            line: 714,
            column: 3
          }
        },
        loc: {
          start: {
            line: 714,
            column: 38
          },
          end: {
            line: 716,
            column: 3
          }
        },
        line: 714
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 718,
            column: 2
          },
          end: {
            line: 718,
            column: 3
          }
        },
        loc: {
          start: {
            line: 718,
            column: 36
          },
          end: {
            line: 720,
            column: 3
          }
        },
        line: 718
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 722,
            column: 2
          },
          end: {
            line: 722,
            column: 3
          }
        },
        loc: {
          start: {
            line: 722,
            column: 41
          },
          end: {
            line: 724,
            column: 3
          }
        },
        line: 722
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 723,
            column: 34
          },
          end: {
            line: 723,
            column: 35
          }
        },
        loc: {
          start: {
            line: 723,
            column: 46
          },
          end: {
            line: 723,
            column: 75
          }
        },
        line: 723
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 726,
            column: 2
          },
          end: {
            line: 726,
            column: 3
          }
        },
        loc: {
          start: {
            line: 726,
            column: 39
          },
          end: {
            line: 730,
            column: 3
          }
        },
        line: 726
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 728,
            column: 34
          },
          end: {
            line: 728,
            column: 35
          }
        },
        loc: {
          start: {
            line: 728,
            column: 46
          },
          end: {
            line: 728,
            column: 88
          }
        },
        line: 728
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 735,
            column: 2
          },
          end: {
            line: 735,
            column: 3
          }
        },
        loc: {
          start: {
            line: 735,
            column: 50
          },
          end: {
            line: 750,
            column: 3
          }
        },
        line: 735
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 755,
            column: 2
          },
          end: {
            line: 755,
            column: 3
          }
        },
        loc: {
          start: {
            line: 755,
            column: 50
          },
          end: {
            line: 765,
            column: 3
          }
        },
        line: 755
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 762,
            column: 36
          },
          end: {
            line: 762,
            column: 37
          }
        },
        loc: {
          start: {
            line: 762,
            column: 48
          },
          end: {
            line: 764,
            column: 5
          }
        },
        line: 762
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 770,
            column: 2
          },
          end: {
            line: 770,
            column: 3
          }
        },
        loc: {
          start: {
            line: 770,
            column: 64
          },
          end: {
            line: 793,
            column: 3
          }
        },
        line: 770
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 798,
            column: 2
          },
          end: {
            line: 798,
            column: 3
          }
        },
        loc: {
          start: {
            line: 798,
            column: 65
          },
          end: {
            line: 812,
            column: 3
          }
        },
        line: 798
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 817,
            column: 2
          },
          end: {
            line: 817,
            column: 3
          }
        },
        loc: {
          start: {
            line: 817,
            column: 32
          },
          end: {
            line: 833,
            column: 3
          }
        },
        line: 817
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 824,
            column: 40
          },
          end: {
            line: 824,
            column: 41
          }
        },
        loc: {
          start: {
            line: 824,
            column: 52
          },
          end: {
            line: 832,
            column: 5
          }
        },
        line: 824
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 838,
            column: 2
          },
          end: {
            line: 838,
            column: 3
          }
        },
        loc: {
          start: {
            line: 838,
            column: 54
          },
          end: {
            line: 863,
            column: 3
          }
        },
        line: 838
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 51,
            column: 6
          },
          end: {
            line: 53,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 6
          },
          end: {
            line: 53,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "1": {
        loc: {
          start: {
            line: 85,
            column: 6
          },
          end: {
            line: 87,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 85,
            column: 6
          },
          end: {
            line: 87,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 85
      },
      "2": {
        loc: {
          start: {
            line: 91,
            column: 6
          },
          end: {
            line: 93,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 6
          },
          end: {
            line: 93,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "3": {
        loc: {
          start: {
            line: 92,
            column: 24
          },
          end: {
            line: 92,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 92,
            column: 24
          },
          end: {
            line: 92,
            column: 40
          }
        }, {
          start: {
            line: 92,
            column: 44
          },
          end: {
            line: 92,
            column: 78
          }
        }],
        line: 92
      },
      "4": {
        loc: {
          start: {
            line: 113,
            column: 6
          },
          end: {
            line: 115,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 6
          },
          end: {
            line: 115,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "5": {
        loc: {
          start: {
            line: 126,
            column: 4
          },
          end: {
            line: 126,
            column: 89
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 126,
            column: 81
          },
          end: {
            line: 126,
            column: 89
          }
        }],
        line: 126
      },
      "6": {
        loc: {
          start: {
            line: 130,
            column: 4
          },
          end: {
            line: 132,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 4
          },
          end: {
            line: 132,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "7": {
        loc: {
          start: {
            line: 143,
            column: 19
          },
          end: {
            line: 143,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 143,
            column: 44
          },
          end: {
            line: 143,
            column: 55
          }
        }, {
          start: {
            line: 143,
            column: 58
          },
          end: {
            line: 143,
            column: 67
          }
        }],
        line: 143
      },
      "8": {
        loc: {
          start: {
            line: 170,
            column: 6
          },
          end: {
            line: 182,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 170,
            column: 6
          },
          end: {
            line: 182,
            column: 7
          }
        }, {
          start: {
            line: 173,
            column: 13
          },
          end: {
            line: 182,
            column: 7
          }
        }],
        line: 170
      },
      "9": {
        loc: {
          start: {
            line: 170,
            column: 10
          },
          end: {
            line: 170,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 170,
            column: 10
          },
          end: {
            line: 170,
            column: 21
          }
        }, {
          start: {
            line: 170,
            column: 25
          },
          end: {
            line: 170,
            column: 39
          }
        }],
        line: 170
      },
      "10": {
        loc: {
          start: {
            line: 173,
            column: 13
          },
          end: {
            line: 182,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 173,
            column: 13
          },
          end: {
            line: 182,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 173
      },
      "11": {
        loc: {
          start: {
            line: 179,
            column: 8
          },
          end: {
            line: 181,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 179,
            column: 8
          },
          end: {
            line: 181,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 179
      },
      "12": {
        loc: {
          start: {
            line: 184,
            column: 6
          },
          end: {
            line: 194,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 184,
            column: 6
          },
          end: {
            line: 194,
            column: 7
          }
        }, {
          start: {
            line: 186,
            column: 13
          },
          end: {
            line: 194,
            column: 7
          }
        }],
        line: 184
      },
      "13": {
        loc: {
          start: {
            line: 208,
            column: 4
          },
          end: {
            line: 210,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 208,
            column: 4
          },
          end: {
            line: 210,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 208
      },
      "14": {
        loc: {
          start: {
            line: 208,
            column: 8
          },
          end: {
            line: 208,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 208,
            column: 8
          },
          end: {
            line: 208,
            column: 28
          }
        }, {
          start: {
            line: 208,
            column: 32
          },
          end: {
            line: 208,
            column: 60
          }
        }],
        line: 208
      },
      "15": {
        loc: {
          start: {
            line: 218,
            column: 6
          },
          end: {
            line: 220,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 218,
            column: 6
          },
          end: {
            line: 220,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 218
      },
      "16": {
        loc: {
          start: {
            line: 239,
            column: 4
          },
          end: {
            line: 241,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 239,
            column: 4
          },
          end: {
            line: 241,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 239
      },
      "17": {
        loc: {
          start: {
            line: 239,
            column: 8
          },
          end: {
            line: 239,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 239,
            column: 8
          },
          end: {
            line: 239,
            column: 28
          }
        }, {
          start: {
            line: 239,
            column: 32
          },
          end: {
            line: 239,
            column: 61
          }
        }],
        line: 239
      },
      "18": {
        loc: {
          start: {
            line: 251,
            column: 6
          },
          end: {
            line: 253,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 251,
            column: 6
          },
          end: {
            line: 253,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 251
      },
      "19": {
        loc: {
          start: {
            line: 272,
            column: 4
          },
          end: {
            line: 274,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 272,
            column: 4
          },
          end: {
            line: 274,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 272
      },
      "20": {
        loc: {
          start: {
            line: 289,
            column: 6
          },
          end: {
            line: 317,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 289,
            column: 6
          },
          end: {
            line: 317,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 289
      },
      "21": {
        loc: {
          start: {
            line: 294,
            column: 29
          },
          end: {
            line: 294,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 294,
            column: 29
          },
          end: {
            line: 294,
            column: 45
          }
        }, {
          start: {
            line: 294,
            column: 49
          },
          end: {
            line: 294,
            column: 55
          }
        }],
        line: 294
      },
      "22": {
        loc: {
          start: {
            line: 297,
            column: 8
          },
          end: {
            line: 316,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 297,
            column: 8
          },
          end: {
            line: 316,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 297
      },
      "23": {
        loc: {
          start: {
            line: 303,
            column: 10
          },
          end: {
            line: 315,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 303,
            column: 10
          },
          end: {
            line: 315,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 303
      },
      "24": {
        loc: {
          start: {
            line: 308,
            column: 35
          },
          end: {
            line: 308,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 308,
            column: 35
          },
          end: {
            line: 308,
            column: 51
          }
        }, {
          start: {
            line: 308,
            column: 55
          },
          end: {
            line: 308,
            column: 61
          }
        }],
        line: 308
      },
      "25": {
        loc: {
          start: {
            line: 312,
            column: 12
          },
          end: {
            line: 314,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 312,
            column: 12
          },
          end: {
            line: 314,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 312
      },
      "26": {
        loc: {
          start: {
            line: 344,
            column: 4
          },
          end: {
            line: 346,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 344,
            column: 4
          },
          end: {
            line: 346,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 344
      },
      "27": {
        loc: {
          start: {
            line: 350,
            column: 6
          },
          end: {
            line: 352,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 350,
            column: 6
          },
          end: {
            line: 352,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 350
      },
      "28": {
        loc: {
          start: {
            line: 407,
            column: 4
          },
          end: {
            line: 409,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 407,
            column: 4
          },
          end: {
            line: 409,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 407
      },
      "29": {
        loc: {
          start: {
            line: 410,
            column: 4
          },
          end: {
            line: 412,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 410,
            column: 4
          },
          end: {
            line: 412,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 410
      },
      "30": {
        loc: {
          start: {
            line: 413,
            column: 4
          },
          end: {
            line: 415,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 413,
            column: 4
          },
          end: {
            line: 415,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 413
      },
      "31": {
        loc: {
          start: {
            line: 416,
            column: 4
          },
          end: {
            line: 418,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 416,
            column: 4
          },
          end: {
            line: 418,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 416
      },
      "32": {
        loc: {
          start: {
            line: 419,
            column: 4
          },
          end: {
            line: 421,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 419,
            column: 4
          },
          end: {
            line: 421,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 419
      },
      "33": {
        loc: {
          start: {
            line: 425,
            column: 20
          },
          end: {
            line: 425,
            column: 50
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 425,
            column: 45
          },
          end: {
            line: 425,
            column: 46
          }
        }, {
          start: {
            line: 425,
            column: 49
          },
          end: {
            line: 425,
            column: 50
          }
        }],
        line: 425
      },
      "34": {
        loc: {
          start: {
            line: 488,
            column: 4
          },
          end: {
            line: 493,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 488,
            column: 4
          },
          end: {
            line: 493,
            column: 5
          }
        }, {
          start: {
            line: 491,
            column: 11
          },
          end: {
            line: 493,
            column: 5
          }
        }],
        line: 488
      },
      "35": {
        loc: {
          start: {
            line: 501,
            column: 4
          },
          end: {
            line: 503,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 501,
            column: 4
          },
          end: {
            line: 503,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 501
      },
      "36": {
        loc: {
          start: {
            line: 505,
            column: 4
          },
          end: {
            line: 521,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 506,
            column: 6
          },
          end: {
            line: 508,
            column: 14
          }
        }, {
          start: {
            line: 509,
            column: 6
          },
          end: {
            line: 511,
            column: 14
          }
        }, {
          start: {
            line: 512,
            column: 6
          },
          end: {
            line: 514,
            column: 14
          }
        }, {
          start: {
            line: 515,
            column: 6
          },
          end: {
            line: 517,
            column: 14
          }
        }, {
          start: {
            line: 518,
            column: 6
          },
          end: {
            line: 520,
            column: 14
          }
        }],
        line: 505
      },
      "37": {
        loc: {
          start: {
            line: 529,
            column: 4
          },
          end: {
            line: 533,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 529,
            column: 4
          },
          end: {
            line: 533,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 529
      },
      "38": {
        loc: {
          start: {
            line: 535,
            column: 4
          },
          end: {
            line: 537,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 535,
            column: 4
          },
          end: {
            line: 537,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 535
      },
      "39": {
        loc: {
          start: {
            line: 540,
            column: 4
          },
          end: {
            line: 544,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 540,
            column: 4
          },
          end: {
            line: 544,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 540
      },
      "40": {
        loc: {
          start: {
            line: 547,
            column: 25
          },
          end: {
            line: 547,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 547,
            column: 25
          },
          end: {
            line: 547,
            column: 54
          }
        }, {
          start: {
            line: 547,
            column: 58
          },
          end: {
            line: 547,
            column: 60
          }
        }],
        line: 547
      },
      "41": {
        loc: {
          start: {
            line: 558,
            column: 4
          },
          end: {
            line: 560,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 558,
            column: 4
          },
          end: {
            line: 560,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 558
      },
      "42": {
        loc: {
          start: {
            line: 558,
            column: 8
          },
          end: {
            line: 558,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 558,
            column: 8
          },
          end: {
            line: 558,
            column: 22
          }
        }, {
          start: {
            line: 558,
            column: 26
          },
          end: {
            line: 558,
            column: 56
          }
        }],
        line: 558
      },
      "43": {
        loc: {
          start: {
            line: 561,
            column: 4
          },
          end: {
            line: 563,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 561,
            column: 4
          },
          end: {
            line: 563,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 561
      },
      "44": {
        loc: {
          start: {
            line: 561,
            column: 8
          },
          end: {
            line: 561,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 561,
            column: 8
          },
          end: {
            line: 561,
            column: 26
          }
        }, {
          start: {
            line: 561,
            column: 30
          },
          end: {
            line: 561,
            column: 60
          }
        }],
        line: 561
      },
      "45": {
        loc: {
          start: {
            line: 564,
            column: 4
          },
          end: {
            line: 566,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 564,
            column: 4
          },
          end: {
            line: 566,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 564
      },
      "46": {
        loc: {
          start: {
            line: 564,
            column: 8
          },
          end: {
            line: 564,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 564,
            column: 9
          },
          end: {
            line: 564,
            column: 24
          }
        }, {
          start: {
            line: 564,
            column: 28
          },
          end: {
            line: 564,
            column: 47
          }
        }, {
          start: {
            line: 564,
            column: 53
          },
          end: {
            line: 564,
            column: 72
          }
        }, {
          start: {
            line: 564,
            column: 76
          },
          end: {
            line: 564,
            column: 91
          }
        }],
        line: 564
      },
      "47": {
        loc: {
          start: {
            line: 584,
            column: 22
          },
          end: {
            line: 584,
            column: 52
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 584,
            column: 47
          },
          end: {
            line: 584,
            column: 48
          }
        }, {
          start: {
            line: 584,
            column: 51
          },
          end: {
            line: 584,
            column: 52
          }
        }],
        line: 584
      },
      "48": {
        loc: {
          start: {
            line: 585,
            column: 11
          },
          end: {
            line: 585,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 585,
            column: 11
          },
          end: {
            line: 585,
            column: 37
          }
        }, {
          start: {
            line: 585,
            column: 41
          },
          end: {
            line: 585,
            column: 68
          }
        }],
        line: 585
      },
      "49": {
        loc: {
          start: {
            line: 597,
            column: 20
          },
          end: {
            line: 597,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 597,
            column: 20
          },
          end: {
            line: 597,
            column: 44
          }
        }, {
          start: {
            line: 597,
            column: 48
          },
          end: {
            line: 597,
            column: 58
          }
        }],
        line: 597
      },
      "50": {
        loc: {
          start: {
            line: 621,
            column: 10
          },
          end: {
            line: 628,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 621,
            column: 10
          },
          end: {
            line: 628,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 621
      },
      "51": {
        loc: {
          start: {
            line: 622,
            column: 12
          },
          end: {
            line: 624,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 622,
            column: 12
          },
          end: {
            line: 624,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 622
      },
      "52": {
        loc: {
          start: {
            line: 633,
            column: 10
          },
          end: {
            line: 635,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 633,
            column: 10
          },
          end: {
            line: 635,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 633
      },
      "53": {
        loc: {
          start: {
            line: 649,
            column: 6
          },
          end: {
            line: 651,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 649,
            column: 6
          },
          end: {
            line: 651,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 649
      },
      "54": {
        loc: {
          start: {
            line: 661,
            column: 6
          },
          end: {
            line: 670,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 661,
            column: 6
          },
          end: {
            line: 670,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 661
      },
      "55": {
        loc: {
          start: {
            line: 661,
            column: 10
          },
          end: {
            line: 661,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 661,
            column: 10
          },
          end: {
            line: 661,
            column: 38
          }
        }, {
          start: {
            line: 661,
            column: 42
          },
          end: {
            line: 661,
            column: 64
          }
        }],
        line: 661
      },
      "56": {
        loc: {
          start: {
            line: 674,
            column: 6
          },
          end: {
            line: 676,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 674,
            column: 6
          },
          end: {
            line: 676,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 674
      },
      "57": {
        loc: {
          start: {
            line: 674,
            column: 10
          },
          end: {
            line: 674,
            column: 32
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 674,
            column: 10
          },
          end: {
            line: 674,
            column: 16
          }
        }, {
          start: {
            line: 674,
            column: 20
          },
          end: {
            line: 674,
            column: 32
          }
        }],
        line: 674
      },
      "58": {
        loc: {
          start: {
            line: 693,
            column: 4
          },
          end: {
            line: 695,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 693,
            column: 4
          },
          end: {
            line: 695,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 693
      },
      "59": {
        loc: {
          start: {
            line: 693,
            column: 8
          },
          end: {
            line: 693,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 693,
            column: 8
          },
          end: {
            line: 693,
            column: 19
          }
        }, {
          start: {
            line: 693,
            column: 23
          },
          end: {
            line: 693,
            column: 46
          }
        }],
        line: 693
      },
      "60": {
        loc: {
          start: {
            line: 706,
            column: 4
          },
          end: {
            line: 710,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 706,
            column: 4
          },
          end: {
            line: 710,
            column: 5
          }
        }, {
          start: {
            line: 708,
            column: 11
          },
          end: {
            line: 710,
            column: 5
          }
        }],
        line: 706
      },
      "61": {
        loc: {
          start: {
            line: 708,
            column: 11
          },
          end: {
            line: 710,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 708,
            column: 11
          },
          end: {
            line: 710,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 708
      },
      "62": {
        loc: {
          start: {
            line: 727,
            column: 4
          },
          end: {
            line: 729,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 727,
            column: 4
          },
          end: {
            line: 729,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 727
      },
      "63": {
        loc: {
          start: {
            line: 738,
            column: 6
          },
          end: {
            line: 740,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 738,
            column: 6
          },
          end: {
            line: 740,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 738
      },
      "64": {
        loc: {
          start: {
            line: 757,
            column: 4
          },
          end: {
            line: 759,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 757,
            column: 4
          },
          end: {
            line: 759,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 757
      },
      "65": {
        loc: {
          start: {
            line: 773,
            column: 6
          },
          end: {
            line: 775,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 773,
            column: 6
          },
          end: {
            line: 775,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 773
      },
      "66": {
        loc: {
          start: {
            line: 773,
            column: 10
          },
          end: {
            line: 773,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 773,
            column: 10
          },
          end: {
            line: 773,
            column: 16
          }
        }, {
          start: {
            line: 773,
            column: 20
          },
          end: {
            line: 773,
            column: 38
          }
        }],
        line: 773
      },
      "67": {
        loc: {
          start: {
            line: 799,
            column: 4
          },
          end: {
            line: 811,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 800,
            column: 6
          },
          end: {
            line: 802,
            column: 14
          }
        }, {
          start: {
            line: 803,
            column: 6
          },
          end: {
            line: 805,
            column: 14
          }
        }, {
          start: {
            line: 806,
            column: 6
          },
          end: {
            line: 808,
            column: 14
          }
        }, {
          start: {
            line: 809,
            column: 6
          },
          end: {
            line: 810,
            column: 58
          }
        }],
        line: 799
      },
      "68": {
        loc: {
          start: {
            line: 819,
            column: 4
          },
          end: {
            line: 821,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 819,
            column: 4
          },
          end: {
            line: 821,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 819
      },
      "69": {
        loc: {
          start: {
            line: 825,
            column: 6
          },
          end: {
            line: 831,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 825,
            column: 6
          },
          end: {
            line: 831,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 825
      },
      "70": {
        loc: {
          start: {
            line: 840,
            column: 6
          },
          end: {
            line: 859,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 840,
            column: 6
          },
          end: {
            line: 859,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 840
      },
      "71": {
        loc: {
          start: {
            line: 842,
            column: 8
          },
          end: {
            line: 844,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 842,
            column: 8
          },
          end: {
            line: 844,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 842
      },
      "72": {
        loc: {
          start: {
            line: 847,
            column: 8
          },
          end: {
            line: 850,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 847,
            column: 8
          },
          end: {
            line: 850,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 847
      },
      "73": {
        loc: {
          start: {
            line: 852,
            column: 8
          },
          end: {
            line: 855,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 852,
            column: 8
          },
          end: {
            line: 855,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 852
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0,
      "274": 0,
      "275": 0,
      "276": 0,
      "277": 0,
      "278": 0,
      "279": 0,
      "280": 0,
      "281": 0,
      "282": 0,
      "283": 0,
      "284": 0,
      "285": 0,
      "286": 0,
      "287": 0,
      "288": 0,
      "289": 0,
      "290": 0,
      "291": 0,
      "292": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0, 0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0, 0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0, 0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "de6c751edc0452f88f5950953225c4841ba4ba64"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2k9ta5zj0b = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2k9ta5zj0b();
var MatchRecordingService = function () {
  function MatchRecordingService() {
    (0, _classCallCheck2.default)(this, MatchRecordingService);
    this.currentSession = (cov_2k9ta5zj0b().s[0]++, null);
    this.sessionListeners = (cov_2k9ta5zj0b().s[1]++, []);
    this.scoreListeners = (cov_2k9ta5zj0b().s[2]++, []);
    this.offlineSyncQueue = (cov_2k9ta5zj0b().s[3]++, null);
    this.syncInterval = (cov_2k9ta5zj0b().s[4]++, null);
    this.autoSaveInterval = (cov_2k9ta5zj0b().s[5]++, null);
  }
  return (0, _createClass2.default)(MatchRecordingService, [{
    key: "startMatch",
    value: (function () {
      var _startMatch = (0, _asyncToGenerator2.default)(function* (metadata, options) {
        cov_2k9ta5zj0b().f[0]++;
        cov_2k9ta5zj0b().s[6]++;
        try {
          cov_2k9ta5zj0b().s[7]++;
          _performance.performanceMonitor.start('match_recording_start');
          cov_2k9ta5zj0b().s[8]++;
          this.validateMatchMetadata(metadata);
          cov_2k9ta5zj0b().s[9]++;
          if (this.currentSession) {
            cov_2k9ta5zj0b().b[0][0]++;
            cov_2k9ta5zj0b().s[10]++;
            throw new Error('Another match recording is already in progress');
          } else {
            cov_2k9ta5zj0b().b[0][1]++;
          }
          var matchRecording = (cov_2k9ta5zj0b().s[11]++, {
            id: `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            metadata: Object.assign({}, metadata, {
              startTime: new Date().toISOString()
            }),
            score: this.initializeScore(metadata.matchFormat),
            statistics: this.initializeStatistics(metadata.userId),
            status: 'recording',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          });
          var session = (cov_2k9ta5zj0b().s[12]++, {
            id: this.generateSessionId(),
            match: matchRecording,
            currentSet: 1,
            currentGame: 1,
            isRecording: true,
            isPaused: false,
            startTime: Date.now(),
            pausedTime: 0,
            totalPausedDuration: 0,
            videoRecordingActive: options.enableVideoRecording,
            autoScoreDetection: options.enableAutoScoreDetection
          });
          cov_2k9ta5zj0b().s[13]++;
          if (options.enableVideoRecording) {
            cov_2k9ta5zj0b().b[1][0]++;
            cov_2k9ta5zj0b().s[14]++;
            yield _VideoRecordingService.videoRecordingService.startRecording(options.videoConfig);
          } else {
            cov_2k9ta5zj0b().b[1][1]++;
          }
          var savedMatch = (cov_2k9ta5zj0b().s[15]++, yield this.saveMatchToDatabase(matchRecording));
          cov_2k9ta5zj0b().s[16]++;
          if (!savedMatch.success) {
            cov_2k9ta5zj0b().b[2][0]++;
            cov_2k9ta5zj0b().s[17]++;
            throw new Error((cov_2k9ta5zj0b().b[3][0]++, savedMatch.error) || (cov_2k9ta5zj0b().b[3][1]++, 'Failed to save match to database'));
          } else {
            cov_2k9ta5zj0b().b[2][1]++;
          }
          cov_2k9ta5zj0b().s[18]++;
          session.match.id = savedMatch.data.id;
          cov_2k9ta5zj0b().s[19]++;
          session.match.databaseId = savedMatch.data.databaseId;
          cov_2k9ta5zj0b().s[20]++;
          this.setupOfflineSync(session.match.id);
          cov_2k9ta5zj0b().s[21]++;
          this.currentSession = session;
          cov_2k9ta5zj0b().s[22]++;
          this.notifySessionListeners();
          cov_2k9ta5zj0b().s[23]++;
          this.startAutoSave();
          cov_2k9ta5zj0b().s[24]++;
          _performance.performanceMonitor.end('match_recording_start');
          cov_2k9ta5zj0b().s[25]++;
          return session;
        } catch (error) {
          cov_2k9ta5zj0b().s[26]++;
          console.error('Failed to start match recording:', error);
          cov_2k9ta5zj0b().s[27]++;
          if (this.currentSession) {
            cov_2k9ta5zj0b().b[4][0]++;
            cov_2k9ta5zj0b().s[28]++;
            yield this.cleanupFailedSession();
          } else {
            cov_2k9ta5zj0b().b[4][1]++;
          }
          cov_2k9ta5zj0b().s[29]++;
          throw error;
        }
      });
      function startMatch(_x, _x2) {
        return _startMatch.apply(this, arguments);
      }
      return startMatch;
    }())
  }, {
    key: "addPoint",
    value: (function () {
      var _addPoint = (0, _asyncToGenerator2.default)(function* (winner) {
        var eventType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2k9ta5zj0b().b[5][0]++, 'normal');
        var shotType = arguments.length > 2 ? arguments[2] : undefined;
        var courtPosition = arguments.length > 3 ? arguments[3] : undefined;
        cov_2k9ta5zj0b().f[1]++;
        cov_2k9ta5zj0b().s[30]++;
        if (!this.currentSession) {
          cov_2k9ta5zj0b().b[6][0]++;
          cov_2k9ta5zj0b().s[31]++;
          throw new Error('No active match session');
        } else {
          cov_2k9ta5zj0b().b[6][1]++;
        }
        cov_2k9ta5zj0b().s[32]++;
        try {
          var session = (cov_2k9ta5zj0b().s[33]++, this.currentSession);
          var currentSet = (cov_2k9ta5zj0b().s[34]++, session.currentSet);
          var currentGame = (cov_2k9ta5zj0b().s[35]++, session.currentGame);
          var gameEvent = (cov_2k9ta5zj0b().s[36]++, {
            id: this.generateEventId(),
            timestamp: Date.now(),
            eventType: eventType === 'normal' ? (cov_2k9ta5zj0b().b[7][0]++, 'point_won') : (cov_2k9ta5zj0b().b[7][1]++, eventType),
            player: winner,
            shotType: shotType,
            courtPosition: courtPosition,
            description: `Point won by ${winner}`
          });
          var updatedScore = (cov_2k9ta5zj0b().s[37]++, this.updateScore(session.match.score, currentSet, currentGame, winner, gameEvent));
          cov_2k9ta5zj0b().s[38]++;
          this.updateStatistics(session.match.statistics, gameEvent);
          cov_2k9ta5zj0b().s[39]++;
          session.match.score = updatedScore;
          cov_2k9ta5zj0b().s[40]++;
          session.match.updatedAt = new Date().toISOString();
          var setComplete = (cov_2k9ta5zj0b().s[41]++, this.isSetComplete(updatedScore.sets[currentSet - 1]));
          var matchComplete = (cov_2k9ta5zj0b().s[42]++, this.isMatchComplete(updatedScore, session.match.metadata.matchFormat));
          cov_2k9ta5zj0b().s[43]++;
          if ((cov_2k9ta5zj0b().b[9][0]++, setComplete) && (cov_2k9ta5zj0b().b[9][1]++, !matchComplete)) {
            cov_2k9ta5zj0b().b[8][0]++;
            cov_2k9ta5zj0b().s[44]++;
            session.currentSet++;
            cov_2k9ta5zj0b().s[45]++;
            session.currentGame = 1;
          } else {
            cov_2k9ta5zj0b().b[8][1]++;
            cov_2k9ta5zj0b().s[46]++;
            if (!setComplete) {
              cov_2k9ta5zj0b().b[10][0]++;
              var gameComplete = (cov_2k9ta5zj0b().s[47]++, this.isGameComplete(updatedScore.sets[currentSet - 1], currentGame));
              cov_2k9ta5zj0b().s[48]++;
              if (gameComplete) {
                cov_2k9ta5zj0b().b[11][0]++;
                cov_2k9ta5zj0b().s[49]++;
                session.currentGame++;
              } else {
                cov_2k9ta5zj0b().b[11][1]++;
              }
            } else {
              cov_2k9ta5zj0b().b[10][1]++;
            }
          }
          cov_2k9ta5zj0b().s[50]++;
          if (matchComplete) {
            cov_2k9ta5zj0b().b[12][0]++;
            cov_2k9ta5zj0b().s[51]++;
            yield this.endMatch();
          } else {
            cov_2k9ta5zj0b().b[12][1]++;
            cov_2k9ta5zj0b().s[52]++;
            try {
              cov_2k9ta5zj0b().s[53]++;
              yield this.updateMatchInDatabase(session.match);
            } catch (error) {
              cov_2k9ta5zj0b().s[54]++;
              console.error('Failed to update match in database:', error);
            }
          }
          cov_2k9ta5zj0b().s[55]++;
          this.notifyScoreListeners();
          cov_2k9ta5zj0b().s[56]++;
          this.notifySessionListeners();
        } catch (error) {
          cov_2k9ta5zj0b().s[57]++;
          console.error('Failed to add point:', error);
          cov_2k9ta5zj0b().s[58]++;
          throw error;
        }
      });
      function addPoint(_x3) {
        return _addPoint.apply(this, arguments);
      }
      return addPoint;
    }())
  }, {
    key: "pauseMatch",
    value: (function () {
      var _pauseMatch = (0, _asyncToGenerator2.default)(function* () {
        cov_2k9ta5zj0b().f[2]++;
        cov_2k9ta5zj0b().s[59]++;
        if ((cov_2k9ta5zj0b().b[14][0]++, !this.currentSession) || (cov_2k9ta5zj0b().b[14][1]++, this.currentSession.isPaused)) {
          cov_2k9ta5zj0b().b[13][0]++;
          cov_2k9ta5zj0b().s[60]++;
          return;
        } else {
          cov_2k9ta5zj0b().b[13][1]++;
        }
        cov_2k9ta5zj0b().s[61]++;
        try {
          cov_2k9ta5zj0b().s[62]++;
          this.currentSession.isPaused = true;
          cov_2k9ta5zj0b().s[63]++;
          this.currentSession.pausedTime = Date.now();
          cov_2k9ta5zj0b().s[64]++;
          this.currentSession.match.status = 'paused';
          cov_2k9ta5zj0b().s[65]++;
          if (this.currentSession.videoRecordingActive) {
            cov_2k9ta5zj0b().b[15][0]++;
            cov_2k9ta5zj0b().s[66]++;
            yield _VideoRecordingService.videoRecordingService.pauseRecording();
          } else {
            cov_2k9ta5zj0b().b[15][1]++;
          }
          cov_2k9ta5zj0b().s[67]++;
          try {
            cov_2k9ta5zj0b().s[68]++;
            yield this.updateMatchInDatabase(this.currentSession.match);
          } catch (error) {
            cov_2k9ta5zj0b().s[69]++;
            console.error('Failed to update match in database:', error);
          }
          cov_2k9ta5zj0b().s[70]++;
          this.notifySessionListeners();
        } catch (error) {
          cov_2k9ta5zj0b().s[71]++;
          console.error('Failed to pause match:', error);
          cov_2k9ta5zj0b().s[72]++;
          throw error;
        }
      });
      function pauseMatch() {
        return _pauseMatch.apply(this, arguments);
      }
      return pauseMatch;
    }())
  }, {
    key: "resumeMatch",
    value: (function () {
      var _resumeMatch = (0, _asyncToGenerator2.default)(function* () {
        cov_2k9ta5zj0b().f[3]++;
        cov_2k9ta5zj0b().s[73]++;
        if ((cov_2k9ta5zj0b().b[17][0]++, !this.currentSession) || (cov_2k9ta5zj0b().b[17][1]++, !this.currentSession.isPaused)) {
          cov_2k9ta5zj0b().b[16][0]++;
          cov_2k9ta5zj0b().s[74]++;
          return;
        } else {
          cov_2k9ta5zj0b().b[16][1]++;
        }
        cov_2k9ta5zj0b().s[75]++;
        try {
          var pauseDuration = (cov_2k9ta5zj0b().s[76]++, Date.now() - this.currentSession.pausedTime);
          cov_2k9ta5zj0b().s[77]++;
          this.currentSession.totalPausedDuration += pauseDuration;
          cov_2k9ta5zj0b().s[78]++;
          this.currentSession.isPaused = false;
          cov_2k9ta5zj0b().s[79]++;
          this.currentSession.pausedTime = 0;
          cov_2k9ta5zj0b().s[80]++;
          this.currentSession.match.status = 'recording';
          cov_2k9ta5zj0b().s[81]++;
          if (this.currentSession.videoRecordingActive) {
            cov_2k9ta5zj0b().b[18][0]++;
            cov_2k9ta5zj0b().s[82]++;
            yield _VideoRecordingService.videoRecordingService.resumeRecording();
          } else {
            cov_2k9ta5zj0b().b[18][1]++;
          }
          cov_2k9ta5zj0b().s[83]++;
          try {
            cov_2k9ta5zj0b().s[84]++;
            yield this.updateMatchInDatabase(this.currentSession.match);
          } catch (error) {
            cov_2k9ta5zj0b().s[85]++;
            console.error('Failed to update match in database:', error);
          }
          cov_2k9ta5zj0b().s[86]++;
          this.notifySessionListeners();
        } catch (error) {
          cov_2k9ta5zj0b().s[87]++;
          console.error('Failed to resume match:', error);
          cov_2k9ta5zj0b().s[88]++;
          throw error;
        }
      });
      function resumeMatch() {
        return _resumeMatch.apply(this, arguments);
      }
      return resumeMatch;
    }())
  }, {
    key: "endMatch",
    value: (function () {
      var _endMatch = (0, _asyncToGenerator2.default)(function* () {
        cov_2k9ta5zj0b().f[4]++;
        cov_2k9ta5zj0b().s[89]++;
        if (!this.currentSession) {
          cov_2k9ta5zj0b().b[19][0]++;
          cov_2k9ta5zj0b().s[90]++;
          throw new Error('No active match session');
        } else {
          cov_2k9ta5zj0b().b[19][1]++;
        }
        cov_2k9ta5zj0b().s[91]++;
        try {
          cov_2k9ta5zj0b().s[92]++;
          _performance.performanceMonitor.start('match_recording_end');
          var session = (cov_2k9ta5zj0b().s[93]++, this.currentSession);
          var endTime = (cov_2k9ta5zj0b().s[94]++, Date.now());
          var totalDuration = (cov_2k9ta5zj0b().s[95]++, (endTime - session.startTime - session.totalPausedDuration) / 1000 / 60);
          cov_2k9ta5zj0b().s[96]++;
          session.match.metadata.endTime = new Date().toISOString();
          cov_2k9ta5zj0b().s[97]++;
          session.match.metadata.durationMinutes = Math.round(totalDuration);
          cov_2k9ta5zj0b().s[98]++;
          session.match.status = 'completed';
          cov_2k9ta5zj0b().s[99]++;
          if (session.videoRecordingActive) {
            cov_2k9ta5zj0b().b[20][0]++;
            var videoResult = (cov_2k9ta5zj0b().s[100]++, yield _VideoRecordingService.videoRecordingService.stopRecording());
            var uploadResult = (cov_2k9ta5zj0b().s[101]++, yield _FileUploadService.fileUploadService.uploadVideo(videoResult.uri, {
              folder: `matches/${(cov_2k9ta5zj0b().b[21][0]++, session.match.id) || (cov_2k9ta5zj0b().b[21][1]++, 'temp')}`
            }));
            cov_2k9ta5zj0b().s[102]++;
            if (uploadResult.data) {
              cov_2k9ta5zj0b().b[22][0]++;
              cov_2k9ta5zj0b().s[103]++;
              session.match.videoUrl = uploadResult.data.url;
              cov_2k9ta5zj0b().s[104]++;
              session.match.videoDurationSeconds = videoResult.duration;
              cov_2k9ta5zj0b().s[105]++;
              session.match.videoFileSizeBytes = uploadResult.data.size;
              cov_2k9ta5zj0b().s[106]++;
              if (videoResult.thumbnail) {
                cov_2k9ta5zj0b().b[23][0]++;
                var thumbnailResult = (cov_2k9ta5zj0b().s[107]++, yield _FileUploadService.fileUploadService.uploadThumbnail(videoResult.uri, videoResult.thumbnail, {
                  folder: `matches/${(cov_2k9ta5zj0b().b[24][0]++, session.match.id) || (cov_2k9ta5zj0b().b[24][1]++, 'temp')}/thumbnails`
                }));
                cov_2k9ta5zj0b().s[108]++;
                if (thumbnailResult.data) {
                  cov_2k9ta5zj0b().b[25][0]++;
                  cov_2k9ta5zj0b().s[109]++;
                  session.match.videoThumbnailUrl = thumbnailResult.data.url;
                } else {
                  cov_2k9ta5zj0b().b[25][1]++;
                }
              } else {
                cov_2k9ta5zj0b().b[23][1]++;
              }
            } else {
              cov_2k9ta5zj0b().b[22][1]++;
            }
          } else {
            cov_2k9ta5zj0b().b[20][1]++;
          }
          cov_2k9ta5zj0b().s[110]++;
          this.calculateFinalStatistics(session.match.statistics, session.match.score);
          cov_2k9ta5zj0b().s[111]++;
          yield this.updateMatchInDatabase(session.match);
          var finalMatch = (cov_2k9ta5zj0b().s[112]++, Object.assign({}, session.match));
          cov_2k9ta5zj0b().s[113]++;
          this.currentSession = null;
          cov_2k9ta5zj0b().s[114]++;
          this.notifySessionListeners();
          cov_2k9ta5zj0b().s[115]++;
          _performance.performanceMonitor.end('match_recording_end');
          cov_2k9ta5zj0b().s[116]++;
          return finalMatch;
        } catch (error) {
          cov_2k9ta5zj0b().s[117]++;
          console.error('Failed to end match:', error);
          cov_2k9ta5zj0b().s[118]++;
          throw error;
        }
      });
      function endMatch() {
        return _endMatch.apply(this, arguments);
      }
      return endMatch;
    }())
  }, {
    key: "cancelMatch",
    value: (function () {
      var _cancelMatch = (0, _asyncToGenerator2.default)(function* () {
        cov_2k9ta5zj0b().f[5]++;
        cov_2k9ta5zj0b().s[119]++;
        if (!this.currentSession) {
          cov_2k9ta5zj0b().b[26][0]++;
          cov_2k9ta5zj0b().s[120]++;
          return;
        } else {
          cov_2k9ta5zj0b().b[26][1]++;
        }
        cov_2k9ta5zj0b().s[121]++;
        try {
          cov_2k9ta5zj0b().s[122]++;
          if (this.currentSession.videoRecordingActive) {
            cov_2k9ta5zj0b().b[27][0]++;
            cov_2k9ta5zj0b().s[123]++;
            yield _VideoRecordingService.videoRecordingService.stopRecording();
          } else {
            cov_2k9ta5zj0b().b[27][1]++;
          }
          cov_2k9ta5zj0b().s[124]++;
          this.currentSession.match.status = 'cancelled';
          cov_2k9ta5zj0b().s[125]++;
          yield this.updateMatchInDatabase(this.currentSession.match);
          cov_2k9ta5zj0b().s[126]++;
          this.currentSession = null;
          cov_2k9ta5zj0b().s[127]++;
          this.notifySessionListeners();
        } catch (error) {
          cov_2k9ta5zj0b().s[128]++;
          console.error('Failed to cancel match:', error);
          cov_2k9ta5zj0b().s[129]++;
          throw error;
        }
      });
      function cancelMatch() {
        return _cancelMatch.apply(this, arguments);
      }
      return cancelMatch;
    }())
  }, {
    key: "getCurrentSession",
    value: function getCurrentSession() {
      cov_2k9ta5zj0b().f[6]++;
      cov_2k9ta5zj0b().s[130]++;
      return this.currentSession;
    }
  }, {
    key: "addSessionListener",
    value: function addSessionListener(listener) {
      cov_2k9ta5zj0b().f[7]++;
      cov_2k9ta5zj0b().s[131]++;
      this.sessionListeners.push(listener);
    }
  }, {
    key: "removeSessionListener",
    value: function removeSessionListener(listener) {
      cov_2k9ta5zj0b().f[8]++;
      cov_2k9ta5zj0b().s[132]++;
      this.sessionListeners = this.sessionListeners.filter(function (l) {
        cov_2k9ta5zj0b().f[9]++;
        cov_2k9ta5zj0b().s[133]++;
        return l !== listener;
      });
    }
  }, {
    key: "addScoreListener",
    value: function addScoreListener(listener) {
      cov_2k9ta5zj0b().f[10]++;
      cov_2k9ta5zj0b().s[134]++;
      this.scoreListeners.push(listener);
    }
  }, {
    key: "removeScoreListener",
    value: function removeScoreListener(listener) {
      cov_2k9ta5zj0b().f[11]++;
      cov_2k9ta5zj0b().s[135]++;
      this.scoreListeners = this.scoreListeners.filter(function (l) {
        cov_2k9ta5zj0b().f[12]++;
        cov_2k9ta5zj0b().s[136]++;
        return l !== listener;
      });
    }
  }, {
    key: "validateMatchMetadata",
    value: function validateMatchMetadata(metadata) {
      var _metadata$opponentNam;
      cov_2k9ta5zj0b().f[13]++;
      cov_2k9ta5zj0b().s[137]++;
      if (!((_metadata$opponentNam = metadata.opponentName) != null && _metadata$opponentNam.trim())) {
        cov_2k9ta5zj0b().b[28][0]++;
        cov_2k9ta5zj0b().s[138]++;
        throw new Error('Opponent name is required');
      } else {
        cov_2k9ta5zj0b().b[28][1]++;
      }
      cov_2k9ta5zj0b().s[139]++;
      if (!metadata.userId) {
        cov_2k9ta5zj0b().b[29][0]++;
        cov_2k9ta5zj0b().s[140]++;
        throw new Error('User ID is required');
      } else {
        cov_2k9ta5zj0b().b[29][1]++;
      }
      cov_2k9ta5zj0b().s[141]++;
      if (!metadata.matchType) {
        cov_2k9ta5zj0b().b[30][0]++;
        cov_2k9ta5zj0b().s[142]++;
        throw new Error('Match type is required');
      } else {
        cov_2k9ta5zj0b().b[30][1]++;
      }
      cov_2k9ta5zj0b().s[143]++;
      if (!metadata.matchFormat) {
        cov_2k9ta5zj0b().b[31][0]++;
        cov_2k9ta5zj0b().s[144]++;
        throw new Error('Match format is required');
      } else {
        cov_2k9ta5zj0b().b[31][1]++;
      }
      cov_2k9ta5zj0b().s[145]++;
      if (!metadata.surface) {
        cov_2k9ta5zj0b().b[32][0]++;
        cov_2k9ta5zj0b().s[146]++;
        throw new Error('Court surface is required');
      } else {
        cov_2k9ta5zj0b().b[32][1]++;
      }
    }
  }, {
    key: "initializeScore",
    value: function initializeScore(format) {
      cov_2k9ta5zj0b().f[14]++;
      var maxSets = (cov_2k9ta5zj0b().s[147]++, format === 'best_of_5' ? (cov_2k9ta5zj0b().b[33][0]++, 5) : (cov_2k9ta5zj0b().b[33][1]++, 3));
      cov_2k9ta5zj0b().s[148]++;
      return {
        sets: [],
        finalScore: '',
        result: 'win',
        setsWon: 0,
        setsLost: 0
      };
    }
  }, {
    key: "initializeStatistics",
    value: function initializeStatistics(userId) {
      cov_2k9ta5zj0b().f[15]++;
      cov_2k9ta5zj0b().s[149]++;
      return {
        matchId: '',
        userId: userId,
        aces: 0,
        doubleFaults: 0,
        firstServesIn: 0,
        firstServesAttempted: 0,
        firstServePointsWon: 0,
        secondServePointsWon: 0,
        firstServeReturnPointsWon: 0,
        secondServeReturnPointsWon: 0,
        breakPointsConverted: 0,
        breakPointsFaced: 0,
        winners: 0,
        unforcedErrors: 0,
        forcedErrors: 0,
        totalPointsWon: 0,
        totalPointsPlayed: 0,
        netPointsAttempted: 0,
        netPointsWon: 0,
        forehandWinners: 0,
        backhandWinners: 0,
        forehandErrors: 0,
        backhandErrors: 0
      };
    }
  }, {
    key: "updateScore",
    value: function updateScore(currentScore, setNumber, gameNumber, winner, event) {
      cov_2k9ta5zj0b().f[16]++;
      var updatedScore = (cov_2k9ta5zj0b().s[150]++, Object.assign({}, currentScore));
      cov_2k9ta5zj0b().s[151]++;
      while (updatedScore.sets.length < setNumber) {
        cov_2k9ta5zj0b().s[152]++;
        updatedScore.sets.push({
          setNumber: updatedScore.sets.length + 1,
          userGames: 0,
          opponentGames: 0,
          isTiebreak: false,
          isCompleted: false
        });
      }
      var currentSet = (cov_2k9ta5zj0b().s[153]++, updatedScore.sets[setNumber - 1]);
      cov_2k9ta5zj0b().s[154]++;
      if (winner === 'user') {
        cov_2k9ta5zj0b().b[34][0]++;
      } else {
        cov_2k9ta5zj0b().b[34][1]++;
      }
      cov_2k9ta5zj0b().s[155]++;
      return updatedScore;
    }
  }, {
    key: "updateStatistics",
    value: function updateStatistics(statistics, event) {
      cov_2k9ta5zj0b().f[17]++;
      cov_2k9ta5zj0b().s[156]++;
      statistics.totalPointsPlayed++;
      cov_2k9ta5zj0b().s[157]++;
      if (event.player === 'user') {
        cov_2k9ta5zj0b().b[35][0]++;
        cov_2k9ta5zj0b().s[158]++;
        statistics.totalPointsWon++;
      } else {
        cov_2k9ta5zj0b().b[35][1]++;
      }
      cov_2k9ta5zj0b().s[159]++;
      switch (event.eventType) {
        case 'ace':
          cov_2k9ta5zj0b().b[36][0]++;
          cov_2k9ta5zj0b().s[160]++;
          statistics.aces++;
          cov_2k9ta5zj0b().s[161]++;
          break;
        case 'double_fault':
          cov_2k9ta5zj0b().b[36][1]++;
          cov_2k9ta5zj0b().s[162]++;
          statistics.doubleFaults++;
          cov_2k9ta5zj0b().s[163]++;
          break;
        case 'winner':
          cov_2k9ta5zj0b().b[36][2]++;
          cov_2k9ta5zj0b().s[164]++;
          statistics.winners++;
          cov_2k9ta5zj0b().s[165]++;
          break;
        case 'unforced_error':
          cov_2k9ta5zj0b().b[36][3]++;
          cov_2k9ta5zj0b().s[166]++;
          statistics.unforcedErrors++;
          cov_2k9ta5zj0b().s[167]++;
          break;
        case 'forced_error':
          cov_2k9ta5zj0b().b[36][4]++;
          cov_2k9ta5zj0b().s[168]++;
          statistics.forcedErrors++;
          cov_2k9ta5zj0b().s[169]++;
          break;
      }
    }
  }, {
    key: "calculateFinalStatistics",
    value: function calculateFinalStatistics(statistics, score) {
      var _this$currentSession;
      cov_2k9ta5zj0b().f[18]++;
      cov_2k9ta5zj0b().s[170]++;
      if (statistics.firstServesAttempted > 0) {
        cov_2k9ta5zj0b().b[37][0]++;
        cov_2k9ta5zj0b().s[171]++;
        statistics.firstServePercentage = Math.round(statistics.firstServesIn / statistics.firstServesAttempted * 100);
      } else {
        cov_2k9ta5zj0b().b[37][1]++;
      }
      cov_2k9ta5zj0b().s[172]++;
      if (statistics.breakPointsFaced > 0) {
        cov_2k9ta5zj0b().b[38][0]++;
        cov_2k9ta5zj0b().s[173]++;
        statistics.breakPointsSaved = statistics.breakPointsFaced - statistics.breakPointsConverted;
      } else {
        cov_2k9ta5zj0b().b[38][1]++;
      }
      cov_2k9ta5zj0b().s[174]++;
      if (statistics.totalPointsPlayed > 0) {
        cov_2k9ta5zj0b().b[39][0]++;
        cov_2k9ta5zj0b().s[175]++;
        statistics.pointWinPercentage = Math.round(statistics.totalPointsWon / statistics.totalPointsPlayed * 100);
      } else {
        cov_2k9ta5zj0b().b[39][1]++;
      }
      cov_2k9ta5zj0b().s[176]++;
      statistics.matchId = (cov_2k9ta5zj0b().b[40][0]++, (_this$currentSession = this.currentSession) == null ? void 0 : _this$currentSession.match.id) || (cov_2k9ta5zj0b().b[40][1]++, '');
    }
  }, {
    key: "isSetComplete",
    value: function isSetComplete(set) {
      cov_2k9ta5zj0b().f[19]++;
      var userGames = (cov_2k9ta5zj0b().s[177]++, set.userGames);
      var opponentGames = (cov_2k9ta5zj0b().s[178]++, set.opponentGames);
      cov_2k9ta5zj0b().s[179]++;
      if ((cov_2k9ta5zj0b().b[42][0]++, userGames >= 6) && (cov_2k9ta5zj0b().b[42][1]++, userGames - opponentGames >= 2)) {
        cov_2k9ta5zj0b().b[41][0]++;
        cov_2k9ta5zj0b().s[180]++;
        return true;
      } else {
        cov_2k9ta5zj0b().b[41][1]++;
      }
      cov_2k9ta5zj0b().s[181]++;
      if ((cov_2k9ta5zj0b().b[44][0]++, opponentGames >= 6) && (cov_2k9ta5zj0b().b[44][1]++, opponentGames - userGames >= 2)) {
        cov_2k9ta5zj0b().b[43][0]++;
        cov_2k9ta5zj0b().s[182]++;
        return true;
      } else {
        cov_2k9ta5zj0b().b[43][1]++;
      }
      cov_2k9ta5zj0b().s[183]++;
      if ((cov_2k9ta5zj0b().b[46][0]++, userGames === 7) && (cov_2k9ta5zj0b().b[46][1]++, opponentGames === 6) || (cov_2k9ta5zj0b().b[46][2]++, opponentGames === 7) && (cov_2k9ta5zj0b().b[46][3]++, userGames === 6)) {
        cov_2k9ta5zj0b().b[45][0]++;
        cov_2k9ta5zj0b().s[184]++;
        return true;
      } else {
        cov_2k9ta5zj0b().b[45][1]++;
      }
      cov_2k9ta5zj0b().s[185]++;
      return false;
    }
  }, {
    key: "isGameComplete",
    value: function isGameComplete(set, gameNumber) {
      cov_2k9ta5zj0b().f[20]++;
      cov_2k9ta5zj0b().s[186]++;
      return false;
    }
  }, {
    key: "isMatchComplete",
    value: function isMatchComplete(score, format) {
      cov_2k9ta5zj0b().f[21]++;
      var setsToWin = (cov_2k9ta5zj0b().s[187]++, format === 'best_of_5' ? (cov_2k9ta5zj0b().b[47][0]++, 3) : (cov_2k9ta5zj0b().b[47][1]++, 2));
      cov_2k9ta5zj0b().s[188]++;
      return (cov_2k9ta5zj0b().b[48][0]++, score.setsWon >= setsToWin) || (cov_2k9ta5zj0b().b[48][1]++, score.setsLost >= setsToWin);
    }
  }, {
    key: "saveMatchToDatabase",
    value: function () {
      var _saveMatchToDatabase = (0, _asyncToGenerator2.default)(function* (match) {
        cov_2k9ta5zj0b().f[22]++;
        cov_2k9ta5zj0b().s[189]++;
        try {
          var matchData = (cov_2k9ta5zj0b().s[190]++, {
            id: match.id,
            user_id: match.metadata.userId,
            opponent_name: match.metadata.opponentName,
            match_type: (cov_2k9ta5zj0b().b[49][0]++, match.metadata.matchType) || (cov_2k9ta5zj0b().b[49][1]++, 'friendly'),
            match_format: match.metadata.matchFormat,
            surface: match.metadata.surface,
            location: match.metadata.location,
            court_name: match.metadata.courtName,
            weather_conditions: match.metadata.weather,
            temperature: match.metadata.temperature,
            match_date: new Date(match.metadata.startTime).toISOString().split('T')[0],
            start_time: new Date(match.metadata.startTime).toTimeString().split(' ')[0],
            status: match.status,
            current_score: JSON.stringify(match.score),
            statistics: JSON.stringify(match.statistics),
            created_at: match.createdAt,
            updated_at: match.updatedAt
          });
          var attempts = (cov_2k9ta5zj0b().s[191]++, 0);
          var maxAttempts = (cov_2k9ta5zj0b().s[192]++, 3);
          cov_2k9ta5zj0b().s[193]++;
          while (attempts < maxAttempts) {
            cov_2k9ta5zj0b().s[194]++;
            try {
              var _result$data;
              var result = (cov_2k9ta5zj0b().s[195]++, yield _MatchRepository.matchRepository.createMatch(matchData));
              cov_2k9ta5zj0b().s[196]++;
              if (result.error) {
                cov_2k9ta5zj0b().b[50][0]++;
                cov_2k9ta5zj0b().s[197]++;
                if (attempts === maxAttempts - 1) {
                  cov_2k9ta5zj0b().b[51][0]++;
                  cov_2k9ta5zj0b().s[198]++;
                  return {
                    success: false,
                    error: result.error
                  };
                } else {
                  cov_2k9ta5zj0b().b[51][1]++;
                }
                cov_2k9ta5zj0b().s[199]++;
                attempts++;
                cov_2k9ta5zj0b().s[200]++;
                yield new Promise(function (resolve) {
                  cov_2k9ta5zj0b().f[23]++;
                  cov_2k9ta5zj0b().s[201]++;
                  return setTimeout(resolve, 1000 * attempts);
                });
                cov_2k9ta5zj0b().s[202]++;
                continue;
              } else {
                cov_2k9ta5zj0b().b[50][1]++;
              }
              cov_2k9ta5zj0b().s[203]++;
              return {
                success: true,
                data: {
                  id: match.id,
                  databaseId: (_result$data = result.data) == null ? void 0 : _result$data.id
                }
              };
            } catch (error) {
              cov_2k9ta5zj0b().s[204]++;
              attempts++;
              cov_2k9ta5zj0b().s[205]++;
              if (attempts === maxAttempts) {
                cov_2k9ta5zj0b().b[52][0]++;
                cov_2k9ta5zj0b().s[206]++;
                throw error;
              } else {
                cov_2k9ta5zj0b().b[52][1]++;
              }
              cov_2k9ta5zj0b().s[207]++;
              yield new Promise(function (resolve) {
                cov_2k9ta5zj0b().f[24]++;
                cov_2k9ta5zj0b().s[208]++;
                return setTimeout(resolve, 1000 * attempts);
              });
            }
          }
          cov_2k9ta5zj0b().s[209]++;
          return {
            success: false,
            error: 'Failed to save after multiple attempts'
          };
        } catch (error) {
          cov_2k9ta5zj0b().s[210]++;
          console.error('Error saving match to database:', error);
          cov_2k9ta5zj0b().s[211]++;
          return {
            success: false,
            error: 'Database connection failed'
          };
        }
      });
      function saveMatchToDatabase(_x4) {
        return _saveMatchToDatabase.apply(this, arguments);
      }
      return saveMatchToDatabase;
    }()
  }, {
    key: "updateMatchInDatabase",
    value: function () {
      var _updateMatchInDatabase = (0, _asyncToGenerator2.default)(function* (match) {
        cov_2k9ta5zj0b().f[25]++;
        cov_2k9ta5zj0b().s[212]++;
        try {
          cov_2k9ta5zj0b().s[213]++;
          if (!match.id) {
            cov_2k9ta5zj0b().b[53][0]++;
            cov_2k9ta5zj0b().s[214]++;
            throw new Error('Match ID is required for update');
          } else {
            cov_2k9ta5zj0b().b[53][1]++;
          }
          var updateData = (cov_2k9ta5zj0b().s[215]++, {
            current_score: JSON.stringify(match.score),
            statistics: JSON.stringify(match.statistics),
            status: match.status,
            updated_at: new Date().toISOString()
          });
          cov_2k9ta5zj0b().s[216]++;
          if ((cov_2k9ta5zj0b().b[55][0]++, match.status === 'completed') && (cov_2k9ta5zj0b().b[55][1]++, match.metadata.endTime)) {
            cov_2k9ta5zj0b().b[54][0]++;
            cov_2k9ta5zj0b().s[217]++;
            updateData.end_time = new Date(match.metadata.endTime).toTimeString().split(' ')[0];
            cov_2k9ta5zj0b().s[218]++;
            updateData.duration_minutes = Math.round((new Date(match.metadata.endTime).getTime() - new Date(match.metadata.startTime).getTime()) / (1000 * 60));
            cov_2k9ta5zj0b().s[219]++;
            updateData.final_score = this.generateFinalScoreString(match.score);
            cov_2k9ta5zj0b().s[220]++;
            updateData.result = this.determineMatchResult(match.score, match.metadata.userId);
            cov_2k9ta5zj0b().s[221]++;
            updateData.sets_won = match.score.setsWon;
            cov_2k9ta5zj0b().s[222]++;
            updateData.sets_lost = match.score.setsLost;
          } else {
            cov_2k9ta5zj0b().b[54][1]++;
          }
          var result = (cov_2k9ta5zj0b().s[223]++, yield _MatchRepository.matchRepository.updateMatch(match.id, updateData));
          cov_2k9ta5zj0b().s[224]++;
          if ((cov_2k9ta5zj0b().b[57][0]++, result) && (cov_2k9ta5zj0b().b[57][1]++, result.error)) {
            cov_2k9ta5zj0b().b[56][0]++;
            cov_2k9ta5zj0b().s[225]++;
            throw new Error(result.error);
          } else {
            cov_2k9ta5zj0b().b[56][1]++;
          }
          cov_2k9ta5zj0b().s[226]++;
          return Object.assign({}, match, {
            updatedAt: new Date().toISOString()
          });
        } catch (error) {
          cov_2k9ta5zj0b().s[227]++;
          console.error('Error updating match in database:', error);
          cov_2k9ta5zj0b().s[228]++;
          throw new Error('Database connection failed');
        }
      });
      function updateMatchInDatabase(_x5) {
        return _updateMatchInDatabase.apply(this, arguments);
      }
      return updateMatchInDatabase;
    }()
  }, {
    key: "generateFinalScoreString",
    value: function generateFinalScoreString(score) {
      cov_2k9ta5zj0b().f[26]++;
      cov_2k9ta5zj0b().s[229]++;
      if ((cov_2k9ta5zj0b().b[59][0]++, !score.sets) || (cov_2k9ta5zj0b().b[59][1]++, score.sets.length === 0)) {
        cov_2k9ta5zj0b().b[58][0]++;
        cov_2k9ta5zj0b().s[230]++;
        return '0-0';
      } else {
        cov_2k9ta5zj0b().b[58][1]++;
      }
      cov_2k9ta5zj0b().s[231]++;
      return score.sets.map(function (set) {
        cov_2k9ta5zj0b().f[27]++;
        cov_2k9ta5zj0b().s[232]++;
        return `${set.userGames}-${set.opponentGames}`;
      }).join(', ');
    }
  }, {
    key: "determineMatchResult",
    value: function determineMatchResult(score, userId) {
      cov_2k9ta5zj0b().f[28]++;
      cov_2k9ta5zj0b().s[233]++;
      if (score.setsWon > score.setsLost) {
        cov_2k9ta5zj0b().b[60][0]++;
        cov_2k9ta5zj0b().s[234]++;
        return 'win';
      } else {
        cov_2k9ta5zj0b().b[60][1]++;
        cov_2k9ta5zj0b().s[235]++;
        if (score.setsLost > score.setsWon) {
          cov_2k9ta5zj0b().b[61][0]++;
          cov_2k9ta5zj0b().s[236]++;
          return 'loss';
        } else {
          cov_2k9ta5zj0b().b[61][1]++;
        }
      }
      cov_2k9ta5zj0b().s[237]++;
      return 'draw';
    }
  }, {
    key: "generateSessionId",
    value: function generateSessionId() {
      cov_2k9ta5zj0b().f[29]++;
      cov_2k9ta5zj0b().s[238]++;
      return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "generateEventId",
    value: function generateEventId() {
      cov_2k9ta5zj0b().f[30]++;
      cov_2k9ta5zj0b().s[239]++;
      return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "notifySessionListeners",
    value: function notifySessionListeners() {
      var _this = this;
      cov_2k9ta5zj0b().f[31]++;
      cov_2k9ta5zj0b().s[240]++;
      this.sessionListeners.forEach(function (listener) {
        cov_2k9ta5zj0b().f[32]++;
        cov_2k9ta5zj0b().s[241]++;
        return listener(_this.currentSession);
      });
    }
  }, {
    key: "notifyScoreListeners",
    value: function notifyScoreListeners() {
      var _this2 = this;
      cov_2k9ta5zj0b().f[33]++;
      cov_2k9ta5zj0b().s[242]++;
      if (this.currentSession) {
        cov_2k9ta5zj0b().b[62][0]++;
        cov_2k9ta5zj0b().s[243]++;
        this.scoreListeners.forEach(function (listener) {
          cov_2k9ta5zj0b().f[34]++;
          cov_2k9ta5zj0b().s[244]++;
          return listener(_this2.currentSession.match.score);
        });
      } else {
        cov_2k9ta5zj0b().b[62][1]++;
      }
    }
  }, {
    key: "setupOfflineSync",
    value: function setupOfflineSync(matchId) {
      cov_2k9ta5zj0b().f[35]++;
      cov_2k9ta5zj0b().s[245]++;
      try {
        cov_2k9ta5zj0b().s[246]++;
        if (!this.offlineSyncQueue) {
          cov_2k9ta5zj0b().b[63][0]++;
          cov_2k9ta5zj0b().s[247]++;
          this.offlineSyncQueue = new Map();
        } else {
          cov_2k9ta5zj0b().b[63][1]++;
        }
        cov_2k9ta5zj0b().s[248]++;
        this.offlineSyncQueue.set(matchId, []);
        cov_2k9ta5zj0b().s[249]++;
        this.startOfflineSync(matchId);
      } catch (error) {
        cov_2k9ta5zj0b().s[250]++;
        console.error('Failed to setup offline sync:', error);
      }
    }
  }, {
    key: "startOfflineSync",
    value: function startOfflineSync(matchId) {
      var _this3 = this;
      cov_2k9ta5zj0b().f[36]++;
      cov_2k9ta5zj0b().s[251]++;
      if (this.syncInterval) {
        cov_2k9ta5zj0b().b[64][0]++;
        cov_2k9ta5zj0b().s[252]++;
        clearInterval(this.syncInterval);
      } else {
        cov_2k9ta5zj0b().b[64][1]++;
      }
      cov_2k9ta5zj0b().s[253]++;
      this.syncInterval = setInterval((0, _asyncToGenerator2.default)(function* () {
        cov_2k9ta5zj0b().f[37]++;
        cov_2k9ta5zj0b().s[254]++;
        yield _this3.syncOfflineData(matchId);
      }), 30000);
    }
  }, {
    key: "syncOfflineData",
    value: (function () {
      var _syncOfflineData = (0, _asyncToGenerator2.default)(function* (matchId) {
        cov_2k9ta5zj0b().f[38]++;
        cov_2k9ta5zj0b().s[255]++;
        try {
          var _this$offlineSyncQueu, _this$offlineSyncQueu2;
          var queue = (cov_2k9ta5zj0b().s[256]++, (_this$offlineSyncQueu = this.offlineSyncQueue) == null ? void 0 : _this$offlineSyncQueu.get(matchId));
          cov_2k9ta5zj0b().s[257]++;
          if ((cov_2k9ta5zj0b().b[66][0]++, !queue) || (cov_2k9ta5zj0b().b[66][1]++, queue.length === 0)) {
            cov_2k9ta5zj0b().b[65][0]++;
            cov_2k9ta5zj0b().s[258]++;
            return;
          } else {
            cov_2k9ta5zj0b().b[65][1]++;
          }
          var updates = (cov_2k9ta5zj0b().s[259]++, (0, _toConsumableArray2.default)(queue));
          cov_2k9ta5zj0b().s[260]++;
          (_this$offlineSyncQueu2 = this.offlineSyncQueue) == null || _this$offlineSyncQueu2.set(matchId, []);
          cov_2k9ta5zj0b().s[261]++;
          for (var update of updates) {
            cov_2k9ta5zj0b().s[262]++;
            try {
              cov_2k9ta5zj0b().s[263]++;
              yield this.processOfflineUpdate(update);
            } catch (error) {
              var _this$offlineSyncQueu3;
              cov_2k9ta5zj0b().s[264]++;
              console.error('Failed to sync update:', error);
              cov_2k9ta5zj0b().s[265]++;
              (_this$offlineSyncQueu3 = this.offlineSyncQueue) == null || (_this$offlineSyncQueu3 = _this$offlineSyncQueu3.get(matchId)) == null || _this$offlineSyncQueu3.push(update);
            }
          }
        } catch (error) {
          cov_2k9ta5zj0b().s[266]++;
          console.error('Failed to sync offline data:', error);
        }
      });
      function syncOfflineData(_x6) {
        return _syncOfflineData.apply(this, arguments);
      }
      return syncOfflineData;
    }())
  }, {
    key: "processOfflineUpdate",
    value: (function () {
      var _processOfflineUpdate = (0, _asyncToGenerator2.default)(function* (update) {
        cov_2k9ta5zj0b().f[39]++;
        cov_2k9ta5zj0b().s[267]++;
        switch (update.type) {
          case 'match_update':
            cov_2k9ta5zj0b().b[67][0]++;
            cov_2k9ta5zj0b().s[268]++;
            yield this.updateMatchInDatabase(update.data);
            cov_2k9ta5zj0b().s[269]++;
            break;
          case 'score_update':
            cov_2k9ta5zj0b().b[67][1]++;
            cov_2k9ta5zj0b().s[270]++;
            break;
          case 'statistics_update':
            cov_2k9ta5zj0b().b[67][2]++;
            cov_2k9ta5zj0b().s[271]++;
            break;
          default:
            cov_2k9ta5zj0b().b[67][3]++;
            cov_2k9ta5zj0b().s[272]++;
            console.warn('Unknown update type:', update.type);
        }
      });
      function processOfflineUpdate(_x7) {
        return _processOfflineUpdate.apply(this, arguments);
      }
      return processOfflineUpdate;
    }())
  }, {
    key: "startAutoSave",
    value: function startAutoSave() {
      var _this4 = this;
      cov_2k9ta5zj0b().f[40]++;
      cov_2k9ta5zj0b().s[273]++;
      if (this.autoSaveInterval) {
        cov_2k9ta5zj0b().b[68][0]++;
        cov_2k9ta5zj0b().s[274]++;
        clearInterval(this.autoSaveInterval);
      } else {
        cov_2k9ta5zj0b().b[68][1]++;
      }
      cov_2k9ta5zj0b().s[275]++;
      this.autoSaveInterval = setInterval((0, _asyncToGenerator2.default)(function* () {
        cov_2k9ta5zj0b().f[41]++;
        cov_2k9ta5zj0b().s[276]++;
        if (_this4.currentSession) {
          cov_2k9ta5zj0b().b[69][0]++;
          cov_2k9ta5zj0b().s[277]++;
          try {
            cov_2k9ta5zj0b().s[278]++;
            yield _this4.updateMatchInDatabase(_this4.currentSession.match);
          } catch (error) {
            cov_2k9ta5zj0b().s[279]++;
            console.error('Auto-save failed:', error);
          }
        } else {
          cov_2k9ta5zj0b().b[69][1]++;
        }
      }), 120000);
    }
  }, {
    key: "cleanupFailedSession",
    value: (function () {
      var _cleanupFailedSession = (0, _asyncToGenerator2.default)(function* () {
        cov_2k9ta5zj0b().f[42]++;
        cov_2k9ta5zj0b().s[280]++;
        try {
          cov_2k9ta5zj0b().s[281]++;
          if (this.currentSession) {
            cov_2k9ta5zj0b().b[70][0]++;
            cov_2k9ta5zj0b().s[282]++;
            if (this.currentSession.videoRecordingActive) {
              cov_2k9ta5zj0b().b[71][0]++;
              cov_2k9ta5zj0b().s[283]++;
              yield _VideoRecordingService.videoRecordingService.stopRecording();
            } else {
              cov_2k9ta5zj0b().b[71][1]++;
            }
            cov_2k9ta5zj0b().s[284]++;
            if (this.autoSaveInterval) {
              cov_2k9ta5zj0b().b[72][0]++;
              cov_2k9ta5zj0b().s[285]++;
              clearInterval(this.autoSaveInterval);
              cov_2k9ta5zj0b().s[286]++;
              this.autoSaveInterval = null;
            } else {
              cov_2k9ta5zj0b().b[72][1]++;
            }
            cov_2k9ta5zj0b().s[287]++;
            if (this.syncInterval) {
              cov_2k9ta5zj0b().b[73][0]++;
              cov_2k9ta5zj0b().s[288]++;
              clearInterval(this.syncInterval);
              cov_2k9ta5zj0b().s[289]++;
              this.syncInterval = null;
            } else {
              cov_2k9ta5zj0b().b[73][1]++;
            }
            cov_2k9ta5zj0b().s[290]++;
            this.currentSession = null;
          } else {
            cov_2k9ta5zj0b().b[70][1]++;
          }
        } catch (error) {
          cov_2k9ta5zj0b().s[291]++;
          console.error('Failed to cleanup session:', error);
        }
      });
      function cleanupFailedSession() {
        return _cleanupFailedSession.apply(this, arguments);
      }
      return cleanupFailedSession;
    }())
  }]);
}();
var matchRecordingService = exports.matchRecordingService = (cov_2k9ta5zj0b().s[292]++, new MatchRecordingService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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