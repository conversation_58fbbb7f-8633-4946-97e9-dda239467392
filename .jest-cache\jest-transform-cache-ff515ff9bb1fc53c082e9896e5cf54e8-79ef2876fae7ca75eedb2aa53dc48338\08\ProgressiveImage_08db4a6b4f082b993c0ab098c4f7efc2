b66fc06f3906a2433cbbefc316b16cf8
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_ugn79b25n() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\optimized\\ProgressiveImage.tsx";
  var hash = "21711e189db40bfeccf93376859947b7623717f7";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\optimized\\ProgressiveImage.tsx",
    statementMap: {
      "0": {
        start: {
          line: 50,
          column: 65
        },
        end: {
          line: 382,
          column: 1
        }
      },
      "1": {
        start: {
          line: 70,
          column: 34
        },
        end: {
          line: 70,
          column: 49
        }
      },
      "2": {
        start: {
          line: 71,
          column: 36
        },
        end: {
          line: 71,
          column: 51
        }
      },
      "3": {
        start: {
          line: 72,
          column: 34
        },
        end: {
          line: 72,
          column: 49
        }
      },
      "4": {
        start: {
          line: 73,
          column: 34
        },
        end: {
          line: 73,
          column: 49
        }
      },
      "5": {
        start: {
          line: 74,
          column: 48
        },
        end: {
          line: 74,
          column: 64
        }
      },
      "6": {
        start: {
          line: 75,
          column: 48
        },
        end: {
          line: 75,
          column: 86
        }
      },
      "7": {
        start: {
          line: 77,
          column: 19
        },
        end: {
          line: 77,
          column: 56
        }
      },
      "8": {
        start: {
          line: 78,
          column: 30
        },
        end: {
          line: 78,
          column: 67
        }
      },
      "9": {
        start: {
          line: 79,
          column: 23
        },
        end: {
          line: 79,
          column: 41
        }
      },
      "10": {
        start: {
          line: 80,
          column: 24
        },
        end: {
          line: 80,
          column: 41
        }
      },
      "11": {
        start: {
          line: 83,
          column: 21
        },
        end: {
          line: 83,
          column: 45
        }
      },
      "12": {
        start: {
          line: 86,
          column: 30
        },
        end: {
          line: 149,
          column: 65
        }
      },
      "13": {
        start: {
          line: 87,
          column: 4
        },
        end: {
          line: 90,
          column: 5
        }
      },
      "14": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 88,
          column: 33
        }
      },
      "15": {
        start: {
          line: 89,
          column: 6
        },
        end: {
          line: 89,
          column: 13
        }
      },
      "16": {
        start: {
          line: 92,
          column: 24
        },
        end: {
          line: 92,
          column: 34
        }
      },
      "17": {
        start: {
          line: 93,
          column: 23
        },
        end: {
          line: 93,
          column: 34
        }
      },
      "18": {
        start: {
          line: 95,
          column: 4
        },
        end: {
          line: 148,
          column: 5
        }
      },
      "19": {
        start: {
          line: 97,
          column: 6
        },
        end: {
          line: 114,
          column: 7
        }
      },
      "20": {
        start: {
          line: 99,
          column: 8
        },
        end: {
          line: 103,
          column: 11
        }
      },
      "21": {
        start: {
          line: 104,
          column: 13
        },
        end: {
          line: 114,
          column: 7
        }
      },
      "22": {
        start: {
          line: 106,
          column: 27
        },
        end: {
          line: 106,
          column: 67
        }
      },
      "23": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 113,
          column: 9
        }
      },
      "24": {
        start: {
          line: 108,
          column: 10
        },
        end: {
          line: 112,
          column: 13
        }
      },
      "25": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 127,
          column: 7
        }
      },
      "26": {
        start: {
          line: 118,
          column: 22
        },
        end: {
          line: 118,
          column: 62
        }
      },
      "27": {
        start: {
          line: 119,
          column: 23
        },
        end: {
          line: 119,
          column: 44
        }
      },
      "28": {
        start: {
          line: 121,
          column: 8
        },
        end: {
          line: 126,
          column: 11
        }
      },
      "29": {
        start: {
          line: 130,
          column: 23
        },
        end: {
          line: 130,
          column: 56
        }
      },
      "30": {
        start: {
          line: 131,
          column: 24
        },
        end: {
          line: 131,
          column: 72
        }
      },
      "31": {
        start: {
          line: 133,
          column: 6
        },
        end: {
          line: 143,
          column: 7
        }
      },
      "32": {
        start: {
          line: 134,
          column: 8
        },
        end: {
          line: 134,
          column: 47
        }
      },
      "33": {
        start: {
          line: 136,
          column: 8
        },
        end: {
          line: 136,
          column: 50
        }
      },
      "34": {
        start: {
          line: 138,
          column: 8
        },
        end: {
          line: 142,
          column: 11
        }
      },
      "35": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 146,
          column: 56
        }
      },
      "36": {
        start: {
          line: 147,
          column: 6
        },
        end: {
          line: 147,
          column: 33
        }
      },
      "37": {
        start: {
          line: 152,
          column: 36
        },
        end: {
          line: 165,
          column: 12
        }
      },
      "38": {
        start: {
          line: 153,
          column: 4
        },
        end: {
          line: 153,
          column: 47
        }
      },
      "39": {
        start: {
          line: 153,
          column: 40
        },
        end: {
          line: 153,
          column: 47
        }
      },
      "40": {
        start: {
          line: 157,
          column: 28
        },
        end: {
          line: 160,
          column: 5
        }
      },
      "41": {
        start: {
          line: 159,
          column: 6
        },
        end: {
          line: 159,
          column: 24
        }
      },
      "42": {
        start: {
          line: 163,
          column: 18
        },
        end: {
          line: 163,
          column: 50
        }
      },
      "43": {
        start: {
          line: 164,
          column: 4
        },
        end: {
          line: 164,
          column: 37
        }
      },
      "44": {
        start: {
          line: 164,
          column: 17
        },
        end: {
          line: 164,
          column: 36
        }
      },
      "45": {
        start: {
          line: 168,
          column: 26
        },
        end: {
          line: 173,
          column: 19
        }
      },
      "46": {
        start: {
          line: 169,
          column: 4
        },
        end: {
          line: 169,
          column: 39
        }
      },
      "47": {
        start: {
          line: 170,
          column: 4
        },
        end: {
          line: 170,
          column: 23
        }
      },
      "48": {
        start: {
          line: 171,
          column: 4
        },
        end: {
          line: 171,
          column: 23
        }
      },
      "49": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 172,
          column: 20
        }
      },
      "50": {
        start: {
          line: 176,
          column: 21
        },
        end: {
          line: 204,
          column: 59
        }
      },
      "51": {
        start: {
          line: 177,
          column: 21
        },
        end: {
          line: 177,
          column: 55
        }
      },
      "52": {
        start: {
          line: 179,
          column: 4
        },
        end: {
          line: 179,
          column: 22
        }
      },
      "53": {
        start: {
          line: 180,
          column: 4
        },
        end: {
          line: 180,
          column: 24
        }
      },
      "54": {
        start: {
          line: 183,
          column: 4
        },
        end: {
          line: 183,
          column: 66
        }
      },
      "55": {
        start: {
          line: 185,
          column: 4
        },
        end: {
          line: 187,
          column: 5
        }
      },
      "56": {
        start: {
          line: 186,
          column: 6
        },
        end: {
          line: 186,
          column: 53
        }
      },
      "57": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 201,
          column: 15
        }
      },
      "58": {
        start: {
          line: 203,
          column: 4
        },
        end: {
          line: 203,
          column: 15
        }
      },
      "59": {
        start: {
          line: 207,
          column: 22
        },
        end: {
          line: 215,
          column: 15
        }
      },
      "60": {
        start: {
          line: 208,
          column: 4
        },
        end: {
          line: 208,
          column: 22
        }
      },
      "61": {
        start: {
          line: 209,
          column: 4
        },
        end: {
          line: 209,
          column: 24
        }
      },
      "62": {
        start: {
          line: 211,
          column: 4
        },
        end: {
          line: 211,
          column: 46
        }
      },
      "63": {
        start: {
          line: 212,
          column: 4
        },
        end: {
          line: 212,
          column: 63
        }
      },
      "64": {
        start: {
          line: 214,
          column: 4
        },
        end: {
          line: 214,
          column: 21
        }
      },
      "65": {
        start: {
          line: 218,
          column: 29
        },
        end: {
          line: 230,
          column: 23
        }
      },
      "66": {
        start: {
          line: 219,
          column: 4
        },
        end: {
          line: 219,
          column: 52
        }
      },
      "67": {
        start: {
          line: 219,
          column: 45
        },
        end: {
          line: 219,
          column: 52
        }
      },
      "68": {
        start: {
          line: 221,
          column: 4
        },
        end: {
          line: 229,
          column: 6
        }
      },
      "69": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 224,
          column: 46
        }
      },
      "70": {
        start: {
          line: 227,
          column: 8
        },
        end: {
          line: 227,
          column: 63
        }
      },
      "71": {
        start: {
          line: 233,
          column: 2
        },
        end: {
          line: 235,
          column: 28
        }
      },
      "72": {
        start: {
          line: 234,
          column: 4
        },
        end: {
          line: 234,
          column: 26
        }
      },
      "73": {
        start: {
          line: 237,
          column: 2
        },
        end: {
          line: 240,
          column: 34
        }
      },
      "74": {
        start: {
          line: 238,
          column: 20
        },
        end: {
          line: 238,
          column: 47
        }
      },
      "75": {
        start: {
          line: 239,
          column: 4
        },
        end: {
          line: 239,
          column: 19
        }
      },
      "76": {
        start: {
          line: 242,
          column: 2
        },
        end: {
          line: 246,
          column: 68
        }
      },
      "77": {
        start: {
          line: 243,
          column: 4
        },
        end: {
          line: 245,
          column: 5
        }
      },
      "78": {
        start: {
          line: 244,
          column: 6
        },
        end: {
          line: 244,
          column: 27
        }
      },
      "79": {
        start: {
          line: 249,
          column: 34
        },
        end: {
          line: 260,
          column: 48
        }
      },
      "80": {
        start: {
          line: 250,
          column: 4
        },
        end: {
          line: 250,
          column: 55
        }
      },
      "81": {
        start: {
          line: 250,
          column: 36
        },
        end: {
          line: 250,
          column: 55
        }
      },
      "82": {
        start: {
          line: 252,
          column: 27
        },
        end: {
          line: 252,
          column: 67
        }
      },
      "83": {
        start: {
          line: 253,
          column: 24
        },
        end: {
          line: 253,
          column: 70
        }
      },
      "84": {
        start: {
          line: 255,
          column: 4
        },
        end: {
          line: 259,
          column: 6
        }
      },
      "85": {
        start: {
          line: 263,
          column: 28
        },
        end: {
          line: 298,
          column: 3
        }
      },
      "86": {
        start: {
          line: 264,
          column: 4
        },
        end: {
          line: 280,
          column: 5
        }
      },
      "87": {
        start: {
          line: 265,
          column: 6
        },
        end: {
          line: 279,
          column: 8
        }
      },
      "88": {
        start: {
          line: 282,
          column: 4
        },
        end: {
          line: 297,
          column: 6
        }
      },
      "89": {
        start: {
          line: 301,
          column: 33
        },
        end: {
          line: 318,
          column: 3
        }
      },
      "90": {
        start: {
          line: 302,
          column: 4
        },
        end: {
          line: 302,
          column: 32
        }
      },
      "91": {
        start: {
          line: 302,
          column: 20
        },
        end: {
          line: 302,
          column: 32
        }
      },
      "92": {
        start: {
          line: 304,
          column: 4
        },
        end: {
          line: 317,
          column: 6
        }
      },
      "93": {
        start: {
          line: 321,
          column: 2
        },
        end: {
          line: 332,
          column: 3
        }
      },
      "94": {
        start: {
          line: 322,
          column: 4
        },
        end: {
          line: 331,
          column: 6
        }
      },
      "95": {
        start: {
          line: 334,
          column: 2
        },
        end: {
          line: 381,
          column: 4
        }
      },
      "96": {
        start: {
          line: 389,
          column: 2
        },
        end: {
          line: 403,
          column: 3
        }
      },
      "97": {
        start: {
          line: 390,
          column: 19
        },
        end: {
          line: 390,
          column: 31
        }
      },
      "98": {
        start: {
          line: 392,
          column: 4
        },
        end: {
          line: 396,
          column: 7
        }
      },
      "99": {
        start: {
          line: 393,
          column: 6
        },
        end: {
          line: 395,
          column: 7
        }
      },
      "100": {
        start: {
          line: 394,
          column: 8
        },
        end: {
          line: 394,
          column: 52
        }
      },
      "101": {
        start: {
          line: 398,
          column: 4
        },
        end: {
          line: 398,
          column: 29
        }
      },
      "102": {
        start: {
          line: 401,
          column: 4
        },
        end: {
          line: 401,
          column: 55
        }
      },
      "103": {
        start: {
          line: 402,
          column: 4
        },
        end: {
          line: 402,
          column: 15
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 50,
            column: 65
          },
          end: {
            line: 50,
            column: 66
          }
        },
        loc: {
          start: {
            line: 69,
            column: 6
          },
          end: {
            line: 382,
            column: 1
          }
        },
        line: 69
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 86,
            column: 42
          },
          end: {
            line: 86,
            column: 43
          }
        },
        loc: {
          start: {
            line: 86,
            column: 54
          },
          end: {
            line: 149,
            column: 3
          }
        },
        line: 86
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 152,
            column: 48
          },
          end: {
            line: 152,
            column: 49
          }
        },
        loc: {
          start: {
            line: 152,
            column: 54
          },
          end: {
            line: 165,
            column: 3
          }
        },
        line: 152
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 157,
            column: 28
          },
          end: {
            line: 157,
            column: 29
          }
        },
        loc: {
          start: {
            line: 157,
            column: 34
          },
          end: {
            line: 160,
            column: 5
          }
        },
        line: 157
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 164,
            column: 11
          },
          end: {
            line: 164,
            column: 12
          }
        },
        loc: {
          start: {
            line: 164,
            column: 17
          },
          end: {
            line: 164,
            column: 36
          }
        },
        line: 164
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 168,
            column: 38
          },
          end: {
            line: 168,
            column: 39
          }
        },
        loc: {
          start: {
            line: 168,
            column: 44
          },
          end: {
            line: 173,
            column: 3
          }
        },
        line: 168
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 176,
            column: 33
          },
          end: {
            line: 176,
            column: 34
          }
        },
        loc: {
          start: {
            line: 176,
            column: 39
          },
          end: {
            line: 204,
            column: 3
          }
        },
        line: 176
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 207,
            column: 34
          },
          end: {
            line: 207,
            column: 35
          }
        },
        loc: {
          start: {
            line: 207,
            column: 50
          },
          end: {
            line: 215,
            column: 3
          }
        },
        line: 207
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 218,
            column: 41
          },
          end: {
            line: 218,
            column: 42
          }
        },
        loc: {
          start: {
            line: 218,
            column: 47
          },
          end: {
            line: 230,
            column: 3
          }
        },
        line: 218
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 223,
            column: 6
          },
          end: {
            line: 223,
            column: 7
          }
        },
        loc: {
          start: {
            line: 223,
            column: 25
          },
          end: {
            line: 225,
            column: 7
          }
        },
        line: 223
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 226,
            column: 6
          },
          end: {
            line: 226,
            column: 7
          }
        },
        loc: {
          start: {
            line: 226,
            column: 17
          },
          end: {
            line: 228,
            column: 7
          }
        },
        line: 226
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 233,
            column: 12
          },
          end: {
            line: 233,
            column: 13
          }
        },
        loc: {
          start: {
            line: 233,
            column: 18
          },
          end: {
            line: 235,
            column: 3
          }
        },
        line: 233
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 237,
            column: 12
          },
          end: {
            line: 237,
            column: 13
          }
        },
        loc: {
          start: {
            line: 237,
            column: 18
          },
          end: {
            line: 240,
            column: 3
          }
        },
        line: 237
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 242,
            column: 12
          },
          end: {
            line: 242,
            column: 13
          }
        },
        loc: {
          start: {
            line: 242,
            column: 18
          },
          end: {
            line: 246,
            column: 3
          }
        },
        line: 242
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 249,
            column: 46
          },
          end: {
            line: 249,
            column: 47
          }
        },
        loc: {
          start: {
            line: 249,
            column: 64
          },
          end: {
            line: 260,
            column: 3
          }
        },
        line: 249
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 263,
            column: 28
          },
          end: {
            line: 263,
            column: 29
          }
        },
        loc: {
          start: {
            line: 263,
            column: 34
          },
          end: {
            line: 298,
            column: 3
          }
        },
        line: 263
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 301,
            column: 33
          },
          end: {
            line: 301,
            column: 34
          }
        },
        loc: {
          start: {
            line: 301,
            column: 39
          },
          end: {
            line: 318,
            column: 3
          }
        },
        line: 301
      },
      "17": {
        name: "addImageParams",
        decl: {
          start: {
            line: 385,
            column: 9
          },
          end: {
            line: 385,
            column: 23
          }
        },
        loc: {
          start: {
            line: 388,
            column: 10
          },
          end: {
            line: 404,
            column: 1
          }
        },
        line: 388
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 392,
            column: 35
          },
          end: {
            line: 392,
            column: 36
          }
        },
        loc: {
          start: {
            line: 392,
            column: 53
          },
          end: {
            line: 396,
            column: 5
          }
        },
        line: 392
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 55,
            column: 2
          },
          end: {
            line: 55,
            column: 17
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 55,
            column: 15
          },
          end: {
            line: 55,
            column: 17
          }
        }],
        line: 55
      },
      "1": {
        loc: {
          start: {
            line: 56,
            column: 2
          },
          end: {
            line: 56,
            column: 20
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 56,
            column: 17
          },
          end: {
            line: 56,
            column: 20
          }
        }],
        line: 56
      },
      "2": {
        loc: {
          start: {
            line: 57,
            column: 2
          },
          end: {
            line: 57,
            column: 14
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 57,
            column: 12
          },
          end: {
            line: 57,
            column: 14
          }
        }],
        line: 57
      },
      "3": {
        loc: {
          start: {
            line: 58,
            column: 2
          },
          end: {
            line: 58,
            column: 21
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 58,
            column: 13
          },
          end: {
            line: 58,
            column: 21
          }
        }],
        line: 58
      },
      "4": {
        loc: {
          start: {
            line: 59,
            column: 2
          },
          end: {
            line: 59,
            column: 13
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 59,
            column: 9
          },
          end: {
            line: 59,
            column: 13
          }
        }],
        line: 59
      },
      "5": {
        loc: {
          start: {
            line: 60,
            column: 2
          },
          end: {
            line: 60,
            column: 17
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 60,
            column: 14
          },
          end: {
            line: 60,
            column: 17
          }
        }],
        line: 60
      },
      "6": {
        loc: {
          start: {
            line: 64,
            column: 2
          },
          end: {
            line: 64,
            column: 22
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 64,
            column: 15
          },
          end: {
            line: 64,
            column: 22
          }
        }],
        line: 64
      },
      "7": {
        loc: {
          start: {
            line: 65,
            column: 2
          },
          end: {
            line: 65,
            column: 20
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 65,
            column: 16
          },
          end: {
            line: 65,
            column: 20
          }
        }],
        line: 65
      },
      "8": {
        loc: {
          start: {
            line: 87,
            column: 4
          },
          end: {
            line: 90,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 87,
            column: 4
          },
          end: {
            line: 90,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 87
      },
      "9": {
        loc: {
          start: {
            line: 97,
            column: 6
          },
          end: {
            line: 114,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 97,
            column: 6
          },
          end: {
            line: 114,
            column: 7
          }
        }, {
          start: {
            line: 104,
            column: 13
          },
          end: {
            line: 114,
            column: 7
          }
        }],
        line: 97
      },
      "10": {
        loc: {
          start: {
            line: 97,
            column: 10
          },
          end: {
            line: 97,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 97,
            column: 10
          },
          end: {
            line: 97,
            column: 21
          }
        }, {
          start: {
            line: 97,
            column: 25
          },
          end: {
            line: 97,
            column: 50
          }
        }],
        line: 97
      },
      "11": {
        loc: {
          start: {
            line: 104,
            column: 13
          },
          end: {
            line: 114,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 104,
            column: 13
          },
          end: {
            line: 114,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 104
      },
      "12": {
        loc: {
          start: {
            line: 104,
            column: 17
          },
          end: {
            line: 104,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 104,
            column: 17
          },
          end: {
            line: 104,
            column: 28
          }
        }, {
          start: {
            line: 104,
            column: 32
          },
          end: {
            line: 104,
            column: 53
          }
        }],
        line: 104
      },
      "13": {
        loc: {
          start: {
            line: 107,
            column: 8
          },
          end: {
            line: 113,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 107,
            column: 8
          },
          end: {
            line: 113,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 107
      },
      "14": {
        loc: {
          start: {
            line: 117,
            column: 6
          },
          end: {
            line: 127,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 6
          },
          end: {
            line: 127,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "15": {
        loc: {
          start: {
            line: 117,
            column: 10
          },
          end: {
            line: 117,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 117,
            column: 10
          },
          end: {
            line: 117,
            column: 15
          }
        }, {
          start: {
            line: 117,
            column: 19
          },
          end: {
            line: 117,
            column: 44
          }
        }],
        line: 117
      },
      "16": {
        loc: {
          start: {
            line: 118,
            column: 22
          },
          end: {
            line: 118,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 118,
            column: 22
          },
          end: {
            line: 118,
            column: 42
          }
        }, {
          start: {
            line: 118,
            column: 46
          },
          end: {
            line: 118,
            column: 62
          }
        }],
        line: 118
      },
      "17": {
        loc: {
          start: {
            line: 122,
            column: 33
          },
          end: {
            line: 122,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 122,
            column: 33
          },
          end: {
            line: 122,
            column: 49
          }
        }, {
          start: {
            line: 122,
            column: 53
          },
          end: {
            line: 122,
            column: 54
          }
        }],
        line: 122
      },
      "18": {
        loc: {
          start: {
            line: 123,
            column: 13
          },
          end: {
            line: 123,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 123,
            column: 22
          },
          end: {
            line: 123,
            column: 66
          }
        }, {
          start: {
            line: 123,
            column: 69
          },
          end: {
            line: 123,
            column: 78
          }
        }],
        line: 123
      },
      "19": {
        loc: {
          start: {
            line: 123,
            column: 43
          },
          end: {
            line: 123,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 123,
            column: 43
          },
          end: {
            line: 123,
            column: 59
          }
        }, {
          start: {
            line: 123,
            column: 63
          },
          end: {
            line: 123,
            column: 64
          }
        }],
        line: 123
      },
      "20": {
        loc: {
          start: {
            line: 125,
            column: 15
          },
          end: {
            line: 125,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 125,
            column: 15
          },
          end: {
            line: 125,
            column: 31
          }
        }, {
          start: {
            line: 125,
            column: 35
          },
          end: {
            line: 125,
            column: 36
          }
        }],
        line: 125
      },
      "21": {
        loc: {
          start: {
            line: 133,
            column: 6
          },
          end: {
            line: 143,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 6
          },
          end: {
            line: 143,
            column: 7
          }
        }, {
          start: {
            line: 135,
            column: 13
          },
          end: {
            line: 143,
            column: 7
          }
        }],
        line: 133
      },
      "22": {
        loc: {
          start: {
            line: 153,
            column: 4
          },
          end: {
            line: 153,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 153,
            column: 4
          },
          end: {
            line: 153,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 153
      },
      "23": {
        loc: {
          start: {
            line: 153,
            column: 8
          },
          end: {
            line: 153,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 153,
            column: 8
          },
          end: {
            line: 153,
            column: 13
          }
        }, {
          start: {
            line: 153,
            column: 17
          },
          end: {
            line: 153,
            column: 38
          }
        }],
        line: 153
      },
      "24": {
        loc: {
          start: {
            line: 185,
            column: 4
          },
          end: {
            line: 187,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 185,
            column: 4
          },
          end: {
            line: 187,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 185
      },
      "25": {
        loc: {
          start: {
            line: 219,
            column: 4
          },
          end: {
            line: 219,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 4
          },
          end: {
            line: 219,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 219
      },
      "26": {
        loc: {
          start: {
            line: 243,
            column: 4
          },
          end: {
            line: 245,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 243,
            column: 4
          },
          end: {
            line: 245,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 243
      },
      "27": {
        loc: {
          start: {
            line: 243,
            column: 8
          },
          end: {
            line: 243,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 243,
            column: 8
          },
          end: {
            line: 243,
            column: 16
          }
        }, {
          start: {
            line: 243,
            column: 20
          },
          end: {
            line: 243,
            column: 29
          }
        }, {
          start: {
            line: 243,
            column: 33
          },
          end: {
            line: 243,
            column: 43
          }
        }, {
          start: {
            line: 243,
            column: 47
          },
          end: {
            line: 243,
            column: 56
          }
        }],
        line: 243
      },
      "28": {
        loc: {
          start: {
            line: 250,
            column: 4
          },
          end: {
            line: 250,
            column: 55
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 250,
            column: 4
          },
          end: {
            line: 250,
            column: 55
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 250
      },
      "29": {
        loc: {
          start: {
            line: 250,
            column: 8
          },
          end: {
            line: 250,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 250,
            column: 8
          },
          end: {
            line: 250,
            column: 24
          }
        }, {
          start: {
            line: 250,
            column: 28
          },
          end: {
            line: 250,
            column: 34
          }
        }],
        line: 250
      },
      "30": {
        loc: {
          start: {
            line: 250,
            column: 43
          },
          end: {
            line: 250,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 250,
            column: 43
          },
          end: {
            line: 250,
            column: 48
          }
        }, {
          start: {
            line: 250,
            column: 52
          },
          end: {
            line: 250,
            column: 54
          }
        }],
        line: 250
      },
      "31": {
        loc: {
          start: {
            line: 252,
            column: 27
          },
          end: {
            line: 252,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 252,
            column: 27
          },
          end: {
            line: 252,
            column: 47
          }
        }, {
          start: {
            line: 252,
            column: 51
          },
          end: {
            line: 252,
            column: 67
          }
        }],
        line: 252
      },
      "32": {
        loc: {
          start: {
            line: 264,
            column: 4
          },
          end: {
            line: 280,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 264,
            column: 4
          },
          end: {
            line: 280,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 264
      },
      "33": {
        loc: {
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 302,
            column: 32
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 302,
            column: 32
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 302
      },
      "34": {
        loc: {
          start: {
            line: 321,
            column: 2
          },
          end: {
            line: 332,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 321,
            column: 2
          },
          end: {
            line: 332,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 321
      },
      "35": {
        loc: {
          start: {
            line: 363,
            column: 7
          },
          end: {
            line: 379,
            column: 7
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 363,
            column: 7
          },
          end: {
            line: 363,
            column: 15
          }
        }, {
          start: {
            line: 364,
            column: 8
          },
          end: {
            line: 378,
            column: 15
          }
        }],
        line: 363
      },
      "36": {
        loc: {
          start: {
            line: 393,
            column: 6
          },
          end: {
            line: 395,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 393,
            column: 6
          },
          end: {
            line: 395,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 393
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0],
      "4": [0],
      "5": [0],
      "6": [0],
      "7": [0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "21711e189db40bfeccf93376859947b7623717f7"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_ugn79b25n = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_ugn79b25n();
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, Image, Animated, Dimensions, Platform } from 'react-native';
import { advancedCacheManager } from "../../services/caching/AdvancedCacheManager";
import { performanceMonitor } from "../../utils/performance";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
cov_ugn79b25n().s[0]++;
export var ProgressiveImage = function ProgressiveImage(_ref) {
  var source = _ref.source,
    style = _ref.style,
    containerStyle = _ref.containerStyle,
    placeholder = _ref.placeholder,
    _ref$blurRadius = _ref.blurRadius,
    blurRadius = _ref$blurRadius === void 0 ? (cov_ugn79b25n().b[0][0]++, 10) : _ref$blurRadius,
    _ref$fadeDuration = _ref.fadeDuration,
    fadeDuration = _ref$fadeDuration === void 0 ? (cov_ugn79b25n().b[1][0]++, 300) : _ref$fadeDuration,
    _ref$quality = _ref.quality,
    quality = _ref$quality === void 0 ? (cov_ugn79b25n().b[2][0]++, 80) : _ref$quality,
    _ref$priority = _ref.priority,
    priority = _ref$priority === void 0 ? (cov_ugn79b25n().b[3][0]++, 'medium') : _ref$priority,
    _ref$lazy = _ref.lazy,
    lazy = _ref$lazy === void 0 ? (cov_ugn79b25n().b[4][0]++, true) : _ref$lazy,
    _ref$threshold = _ref.threshold,
    threshold = _ref$threshold === void 0 ? (cov_ugn79b25n().b[5][0]++, 100) : _ref$threshold,
    onLoad = _ref.onLoad,
    onError = _ref.onError,
    onLoadStart = _ref.onLoadStart,
    _ref$resizeMode = _ref.resizeMode,
    resizeMode = _ref$resizeMode === void 0 ? (cov_ugn79b25n().b[6][0]++, 'cover') : _ref$resizeMode,
    _ref$webpSupport = _ref.webpSupport,
    webpSupport = _ref$webpSupport === void 0 ? (cov_ugn79b25n().b[7][0]++, true) : _ref$webpSupport,
    sizes = _ref.sizes,
    srcSet = _ref.srcSet,
    alt = _ref.alt;
  cov_ugn79b25n().f[0]++;
  var _ref2 = (cov_ugn79b25n().s[1]++, useState(false)),
    _ref3 = _slicedToArray(_ref2, 2),
    isLoaded = _ref3[0],
    setIsLoaded = _ref3[1];
  var _ref4 = (cov_ugn79b25n().s[2]++, useState(false)),
    _ref5 = _slicedToArray(_ref4, 2),
    isLoading = _ref5[0],
    setIsLoading = _ref5[1];
  var _ref6 = (cov_ugn79b25n().s[3]++, useState(false)),
    _ref7 = _slicedToArray(_ref6, 2),
    hasError = _ref7[0],
    setHasError = _ref7[1];
  var _ref8 = (cov_ugn79b25n().s[4]++, useState(!lazy)),
    _ref9 = _slicedToArray(_ref8, 2),
    isInView = _ref9[0],
    setIsInView = _ref9[1];
  var _ref0 = (cov_ugn79b25n().s[5]++, useState(source)),
    _ref1 = _slicedToArray(_ref0, 2),
    optimizedSource = _ref1[0],
    setOptimizedSource = _ref1[1];
  var _ref10 = (cov_ugn79b25n().s[6]++, useState(null)),
    _ref11 = _slicedToArray(_ref10, 2),
    imageDimensions = _ref11[0],
    setImageDimensions = _ref11[1];
  var fadeAnim = (cov_ugn79b25n().s[7]++, useRef(new Animated.Value(0)).current);
  var placeholderFadeAnim = (cov_ugn79b25n().s[8]++, useRef(new Animated.Value(1)).current);
  var containerRef = (cov_ugn79b25n().s[9]++, useRef(null));
  var loadStartTime = (cov_ugn79b25n().s[10]++, useRef(0));
  var screenData = (cov_ugn79b25n().s[11]++, Dimensions.get('window'));
  var optimizeImageSource = (cov_ugn79b25n().s[12]++, useCallback(_asyncToGenerator(function* () {
    cov_ugn79b25n().f[1]++;
    cov_ugn79b25n().s[13]++;
    if (typeof source === 'number') {
      cov_ugn79b25n().b[8][0]++;
      cov_ugn79b25n().s[14]++;
      setOptimizedSource(source);
      cov_ugn79b25n().s[15]++;
      return;
    } else {
      cov_ugn79b25n().b[8][1]++;
    }
    var originalUri = (cov_ugn79b25n().s[16]++, source.uri);
    var optimizedUri = (cov_ugn79b25n().s[17]++, originalUri);
    cov_ugn79b25n().s[18]++;
    try {
      cov_ugn79b25n().s[19]++;
      if ((cov_ugn79b25n().b[10][0]++, webpSupport) && (cov_ugn79b25n().b[10][1]++, Platform.OS === 'android')) {
        cov_ugn79b25n().b[9][0]++;
        cov_ugn79b25n().s[20]++;
        optimizedUri = addImageParams(originalUri, {
          format: 'webp',
          quality: quality,
          auto: 'format'
        });
      } else {
        cov_ugn79b25n().b[9][1]++;
        cov_ugn79b25n().s[21]++;
        if ((cov_ugn79b25n().b[12][0]++, webpSupport) && (cov_ugn79b25n().b[12][1]++, Platform.OS === 'ios')) {
          cov_ugn79b25n().b[11][0]++;
          var iosVersion = (cov_ugn79b25n().s[22]++, parseInt(Platform.Version, 10));
          cov_ugn79b25n().s[23]++;
          if (iosVersion >= 14) {
            cov_ugn79b25n().b[13][0]++;
            cov_ugn79b25n().s[24]++;
            optimizedUri = addImageParams(originalUri, {
              format: 'webp',
              quality: quality,
              auto: 'format'
            });
          } else {
            cov_ugn79b25n().b[13][1]++;
          }
        } else {
          cov_ugn79b25n().b[11][1]++;
        }
      }
      cov_ugn79b25n().s[25]++;
      if ((cov_ugn79b25n().b[15][0]++, style) && (cov_ugn79b25n().b[15][1]++, typeof style === 'object')) {
        cov_ugn79b25n().b[14][0]++;
        var width = (cov_ugn79b25n().s[26]++, (cov_ugn79b25n().b[16][0]++, style.width) || (cov_ugn79b25n().b[16][1]++, screenData.width));
        var height = (cov_ugn79b25n().s[27]++, style.height);
        cov_ugn79b25n().s[28]++;
        optimizedUri = addImageParams(optimizedUri, {
          w: Math.round(width * ((cov_ugn79b25n().b[17][0]++, screenData.scale) || (cov_ugn79b25n().b[17][1]++, 1))),
          h: height ? (cov_ugn79b25n().b[18][0]++, Math.round(height * ((cov_ugn79b25n().b[19][0]++, screenData.scale) || (cov_ugn79b25n().b[19][1]++, 1)))) : (cov_ugn79b25n().b[18][1]++, undefined),
          fit: 'crop',
          dpr: (cov_ugn79b25n().b[20][0]++, screenData.scale) || (cov_ugn79b25n().b[20][1]++, 1)
        });
      } else {
        cov_ugn79b25n().b[14][1]++;
      }
      var cacheKey = (cov_ugn79b25n().s[29]++, `optimized_image_${optimizedUri}`);
      var cachedUri = (cov_ugn79b25n().s[30]++, yield advancedCacheManager.get(cacheKey));
      cov_ugn79b25n().s[31]++;
      if (cachedUri) {
        cov_ugn79b25n().b[21][0]++;
        cov_ugn79b25n().s[32]++;
        setOptimizedSource({
          uri: cachedUri
        });
      } else {
        cov_ugn79b25n().b[21][1]++;
        cov_ugn79b25n().s[33]++;
        setOptimizedSource({
          uri: optimizedUri
        });
        cov_ugn79b25n().s[34]++;
        yield advancedCacheManager.set(cacheKey, optimizedUri, {
          ttl: 86400000,
          priority: priority,
          tags: ['image_optimization']
        });
      }
    } catch (error) {
      cov_ugn79b25n().s[35]++;
      console.warn('Image optimization failed:', error);
      cov_ugn79b25n().s[36]++;
      setOptimizedSource(source);
    }
  }), [source, webpSupport, quality, style, screenData, priority]));
  var setupIntersectionObserver = (cov_ugn79b25n().s[37]++, useCallback(function () {
    cov_ugn79b25n().f[2]++;
    cov_ugn79b25n().s[38]++;
    if ((cov_ugn79b25n().b[23][0]++, !lazy) || (cov_ugn79b25n().b[23][1]++, !containerRef.current)) {
      cov_ugn79b25n().b[22][0]++;
      cov_ugn79b25n().s[39]++;
      return;
    } else {
      cov_ugn79b25n().b[22][1]++;
    }
    cov_ugn79b25n().s[40]++;
    var checkVisibility = function checkVisibility() {
      cov_ugn79b25n().f[3]++;
      cov_ugn79b25n().s[41]++;
      setIsInView(true);
    };
    var timer = (cov_ugn79b25n().s[42]++, setTimeout(checkVisibility, 100));
    cov_ugn79b25n().s[43]++;
    return function () {
      cov_ugn79b25n().f[4]++;
      cov_ugn79b25n().s[44]++;
      return clearTimeout(timer);
    };
  }, [lazy]));
  var handleLoadStart = (cov_ugn79b25n().s[45]++, useCallback(function () {
    cov_ugn79b25n().f[5]++;
    cov_ugn79b25n().s[46]++;
    loadStartTime.current = Date.now();
    cov_ugn79b25n().s[47]++;
    setIsLoading(true);
    cov_ugn79b25n().s[48]++;
    setHasError(false);
    cov_ugn79b25n().s[49]++;
    onLoadStart == null || onLoadStart();
  }, [onLoadStart]));
  var handleLoad = (cov_ugn79b25n().s[50]++, useCallback(function () {
    cov_ugn79b25n().f[6]++;
    var loadTime = (cov_ugn79b25n().s[51]++, Date.now() - loadStartTime.current);
    cov_ugn79b25n().s[52]++;
    setIsLoaded(true);
    cov_ugn79b25n().s[53]++;
    setIsLoading(false);
    cov_ugn79b25n().s[54]++;
    performanceMonitor.trackDatabaseQuery('image_load', loadTime);
    cov_ugn79b25n().s[55]++;
    if (loadTime > 3000) {
      cov_ugn79b25n().b[24][0]++;
      cov_ugn79b25n().s[56]++;
      console.warn(`Slow image load: ${loadTime}ms`);
    } else {
      cov_ugn79b25n().b[24][1]++;
    }
    cov_ugn79b25n().s[57]++;
    Animated.parallel([Animated.timing(fadeAnim, {
      toValue: 1,
      duration: fadeDuration,
      useNativeDriver: true
    }), Animated.timing(placeholderFadeAnim, {
      toValue: 0,
      duration: fadeDuration,
      useNativeDriver: true
    })]).start();
    cov_ugn79b25n().s[58]++;
    onLoad == null || onLoad();
  }, [fadeAnim, placeholderFadeAnim, fadeDuration, onLoad]));
  var handleError = (cov_ugn79b25n().s[59]++, useCallback(function (error) {
    cov_ugn79b25n().f[7]++;
    cov_ugn79b25n().s[60]++;
    setHasError(true);
    cov_ugn79b25n().s[61]++;
    setIsLoading(false);
    cov_ugn79b25n().s[62]++;
    console.error('Image load error:', error);
    cov_ugn79b25n().s[63]++;
    performanceMonitor.trackDatabaseError('image_load', error);
    cov_ugn79b25n().s[64]++;
    onError == null || onError(error);
  }, [onError]));
  var getImageDimensions = (cov_ugn79b25n().s[65]++, useCallback(function () {
    cov_ugn79b25n().f[8]++;
    cov_ugn79b25n().s[66]++;
    if (typeof optimizedSource === 'number') {
      cov_ugn79b25n().b[25][0]++;
      cov_ugn79b25n().s[67]++;
      return;
    } else {
      cov_ugn79b25n().b[25][1]++;
    }
    cov_ugn79b25n().s[68]++;
    Image.getSize(optimizedSource.uri, function (width, height) {
      cov_ugn79b25n().f[9]++;
      cov_ugn79b25n().s[69]++;
      setImageDimensions({
        width: width,
        height: height
      });
    }, function (error) {
      cov_ugn79b25n().f[10]++;
      cov_ugn79b25n().s[70]++;
      console.warn('Failed to get image dimensions:', error);
    });
  }, [optimizedSource]));
  cov_ugn79b25n().s[71]++;
  useEffect(function () {
    cov_ugn79b25n().f[11]++;
    cov_ugn79b25n().s[72]++;
    optimizeImageSource();
  }, [optimizeImageSource]);
  cov_ugn79b25n().s[73]++;
  useEffect(function () {
    cov_ugn79b25n().f[12]++;
    var cleanup = (cov_ugn79b25n().s[74]++, setupIntersectionObserver());
    cov_ugn79b25n().s[75]++;
    return cleanup;
  }, [setupIntersectionObserver]);
  cov_ugn79b25n().s[76]++;
  useEffect(function () {
    cov_ugn79b25n().f[13]++;
    cov_ugn79b25n().s[77]++;
    if ((cov_ugn79b25n().b[27][0]++, isInView) && (cov_ugn79b25n().b[27][1]++, !isLoaded) && (cov_ugn79b25n().b[27][2]++, !isLoading) && (cov_ugn79b25n().b[27][3]++, !hasError)) {
      cov_ugn79b25n().b[26][0]++;
      cov_ugn79b25n().s[78]++;
      getImageDimensions();
    } else {
      cov_ugn79b25n().b[26][1]++;
    }
  }, [isInView, isLoaded, isLoading, hasError, getImageDimensions]);
  var getResponsiveDimensions = (cov_ugn79b25n().s[79]++, useCallback(function () {
    cov_ugn79b25n().f[14]++;
    cov_ugn79b25n().s[80]++;
    if ((cov_ugn79b25n().b[29][0]++, !imageDimensions) || (cov_ugn79b25n().b[29][1]++, !style)) {
      cov_ugn79b25n().b[28][0]++;
      cov_ugn79b25n().s[81]++;
      return (cov_ugn79b25n().b[30][0]++, style) || (cov_ugn79b25n().b[30][1]++, {});
    } else {
      cov_ugn79b25n().b[28][1]++;
    }
    var containerWidth = (cov_ugn79b25n().s[82]++, (cov_ugn79b25n().b[31][0]++, style.width) || (cov_ugn79b25n().b[31][1]++, screenData.width));
    var aspectRatio = (cov_ugn79b25n().s[83]++, imageDimensions.width / imageDimensions.height);
    cov_ugn79b25n().s[84]++;
    return Object.assign({}, style, {
      width: containerWidth,
      height: containerWidth / aspectRatio
    });
  }, [imageDimensions, style, screenData.width]));
  cov_ugn79b25n().s[85]++;
  var renderPlaceholder = function renderPlaceholder() {
    cov_ugn79b25n().f[15]++;
    cov_ugn79b25n().s[86]++;
    if (!placeholder) {
      cov_ugn79b25n().b[32][0]++;
      cov_ugn79b25n().s[87]++;
      return _jsx(Animated.View, {
        style: [{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: '#f0f0f0',
          opacity: placeholderFadeAnim
        }]
      });
    } else {
      cov_ugn79b25n().b[32][1]++;
    }
    cov_ugn79b25n().s[88]++;
    return _jsx(Animated.Image, {
      source: placeholder,
      style: [getResponsiveDimensions(), {
        position: 'absolute',
        top: 0,
        left: 0,
        opacity: placeholderFadeAnim
      }],
      blurRadius: blurRadius,
      resizeMode: resizeMode
    });
  };
  cov_ugn79b25n().s[89]++;
  var renderLoadingIndicator = function renderLoadingIndicator() {
    cov_ugn79b25n().f[16]++;
    cov_ugn79b25n().s[90]++;
    if (!isLoading) {
      cov_ugn79b25n().b[33][0]++;
      cov_ugn79b25n().s[91]++;
      return null;
    } else {
      cov_ugn79b25n().b[33][1]++;
    }
    cov_ugn79b25n().s[92]++;
    return _jsx(View, {
      style: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: [{
          translateX: -10
        }, {
          translateY: -10
        }],
        width: 20,
        height: 20,
        backgroundColor: 'rgba(0,0,0,0.5)',
        borderRadius: 10
      }
    });
  };
  cov_ugn79b25n().s[93]++;
  if (!isInView) {
    cov_ugn79b25n().b[34][0]++;
    cov_ugn79b25n().s[94]++;
    return _jsx(View, {
      ref: containerRef,
      style: [containerStyle, style, {
        backgroundColor: '#f0f0f0'
      }]
    });
  } else {
    cov_ugn79b25n().b[34][1]++;
  }
  cov_ugn79b25n().s[95]++;
  return _jsxs(View, {
    ref: containerRef,
    style: [containerStyle, {
      position: 'relative'
    }],
    children: [renderPlaceholder(), _jsx(Animated.Image, {
      source: optimizedSource,
      style: [getResponsiveDimensions(), {
        opacity: fadeAnim
      }],
      resizeMode: resizeMode,
      onLoadStart: handleLoadStart,
      onLoad: handleLoad,
      onError: handleError,
      accessible: !!alt,
      accessibilityLabel: alt
    }), renderLoadingIndicator(), (cov_ugn79b25n().b[35][0]++, hasError) && (cov_ugn79b25n().b[35][1]++, _jsx(View, {
      style: [getResponsiveDimensions(), {
        position: 'absolute',
        top: 0,
        left: 0,
        backgroundColor: '#f5f5f5',
        justifyContent: 'center',
        alignItems: 'center'
      }]
    }))]
  });
};
function addImageParams(url, params) {
  cov_ugn79b25n().f[17]++;
  cov_ugn79b25n().s[96]++;
  try {
    var urlObj = (cov_ugn79b25n().s[97]++, new URL(url));
    cov_ugn79b25n().s[98]++;
    Object.entries(params).forEach(function (_ref13) {
      var _ref14 = _slicedToArray(_ref13, 2),
        key = _ref14[0],
        value = _ref14[1];
      cov_ugn79b25n().f[18]++;
      cov_ugn79b25n().s[99]++;
      if (value !== undefined) {
        cov_ugn79b25n().b[36][0]++;
        cov_ugn79b25n().s[100]++;
        urlObj.searchParams.set(key, String(value));
      } else {
        cov_ugn79b25n().b[36][1]++;
      }
    });
    cov_ugn79b25n().s[101]++;
    return urlObj.toString();
  } catch (error) {
    cov_ugn79b25n().s[102]++;
    console.warn('Failed to add image params:', error);
    cov_ugn79b25n().s[103]++;
    return url;
  }
}
export default ProgressiveImage;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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