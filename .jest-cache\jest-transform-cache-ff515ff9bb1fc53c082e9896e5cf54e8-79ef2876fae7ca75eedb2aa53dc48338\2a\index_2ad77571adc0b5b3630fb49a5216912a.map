{"version": 3, "names": ["exports", "__esModule", "default", "emptyFunction", "StatusBar", "setBackgroundColor", "setBarStyle", "setHidden", "setNetworkActivityIndicatorVisible", "setTranslucent", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar emptyFunction = () => {};\nfunction StatusBar() {\n  return null;\n}\nStatusBar.setBackgroundColor = emptyFunction;\nStatusBar.setBarStyle = emptyFunction;\nStatusBar.setHidden = emptyFunction;\nStatusBar.setNetworkActivityIndicatorVisible = emptyFunction;\nStatusBar.setTranslucent = emptyFunction;\nvar _default = exports.default = StatusBar;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAUxB,IAAIC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS,CAAC,CAAC;AAC5B,SAASC,SAASA,CAAA,EAAG;EACnB,OAAO,IAAI;AACb;AACAA,SAAS,CAACC,kBAAkB,GAAGF,aAAa;AAC5CC,SAAS,CAACE,WAAW,GAAGH,aAAa;AACrCC,SAAS,CAACG,SAAS,GAAGJ,aAAa;AACnCC,SAAS,CAACI,kCAAkC,GAAGL,aAAa;AAC5DC,SAAS,CAACK,cAAc,GAAGN,aAAa;AACxC,IAAIO,QAAQ,GAAGV,OAAO,CAACE,OAAO,GAAGE,SAAS;AAC1CO,MAAM,CAACX,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}