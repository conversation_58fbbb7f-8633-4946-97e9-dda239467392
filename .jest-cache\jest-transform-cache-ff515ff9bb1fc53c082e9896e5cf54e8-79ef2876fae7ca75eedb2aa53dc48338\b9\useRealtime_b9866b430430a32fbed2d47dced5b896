f1083ef1ef2976032d861c60b6bea7bf
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_tl12vljt0() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useRealtime.ts";
  var hash = "5b119bd0ba7acfd8d4becc60903e5cd1a63e1b64";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useRealtime.ts",
    statementMap: {
      "0": {
        start: {
          line: 16,
          column: 19
        },
        end: {
          line: 16,
          column: 28
        }
      },
      "1": {
        start: {
          line: 17,
          column: 44
        },
        end: {
          line: 17,
          column: 80
        }
      },
      "2": {
        start: {
          line: 18,
          column: 50
        },
        end: {
          line: 18,
          column: 89
        }
      },
      "3": {
        start: {
          line: 19,
          column: 40
        },
        end: {
          line: 19,
          column: 55
        }
      },
      "4": {
        start: {
          line: 21,
          column: 2
        },
        end: {
          line: 58,
          column: 13
        }
      },
      "5": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 25,
          column: 5
        }
      },
      "6": {
        start: {
          line: 23,
          column: 6
        },
        end: {
          line: 23,
          column: 28
        }
      },
      "7": {
        start: {
          line: 24,
          column: 6
        },
        end: {
          line: 24,
          column: 13
        }
      },
      "8": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 36,
          column: 6
        }
      },
      "9": {
        start: {
          line: 34,
          column: 8
        },
        end: {
          line: 34,
          column: 58
        }
      },
      "10": {
        start: {
          line: 34,
          column: 33
        },
        end: {
          line: 34,
          column: 56
        }
      },
      "11": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 44,
          column: 6
        }
      },
      "12": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 56
        }
      },
      "13": {
        start: {
          line: 42,
          column: 36
        },
        end: {
          line: 42,
          column: 54
        }
      },
      "14": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 46,
          column: 25
        }
      },
      "15": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 57,
          column: 6
        }
      },
      "16": {
        start: {
          line: 50,
          column: 6
        },
        end: {
          line: 52,
          column: 7
        }
      },
      "17": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 34
        }
      },
      "18": {
        start: {
          line: 53,
          column: 6
        },
        end: {
          line: 55,
          column: 7
        }
      },
      "19": {
        start: {
          line: 54,
          column: 8
        },
        end: {
          line: 54,
          column: 30
        }
      },
      "20": {
        start: {
          line: 56,
          column: 6
        },
        end: {
          line: 56,
          column: 28
        }
      },
      "21": {
        start: {
          line: 60,
          column: 30
        },
        end: {
          line: 71,
          column: 12
        }
      },
      "22": {
        start: {
          line: 64,
          column: 4
        },
        end: {
          line: 64,
          column: 22
        }
      },
      "23": {
        start: {
          line: 64,
          column: 15
        },
        end: {
          line: 64,
          column: 22
        }
      },
      "24": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 70,
          column: 5
        }
      },
      "25": {
        start: {
          line: 67,
          column: 6
        },
        end: {
          line: 67,
          column: 72
        }
      },
      "26": {
        start: {
          line: 69,
          column: 6
        },
        end: {
          line: 69,
          column: 62
        }
      },
      "27": {
        start: {
          line: 73,
          column: 29
        },
        end: {
          line: 85,
          column: 12
        }
      },
      "28": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 78,
          column: 22
        }
      },
      "29": {
        start: {
          line: 78,
          column: 15
        },
        end: {
          line: 78,
          column: 22
        }
      },
      "30": {
        start: {
          line: 80,
          column: 4
        },
        end: {
          line: 84,
          column: 5
        }
      },
      "31": {
        start: {
          line: 81,
          column: 6
        },
        end: {
          line: 81,
          column: 78
        }
      },
      "32": {
        start: {
          line: 83,
          column: 6
        },
        end: {
          line: 83,
          column: 59
        }
      },
      "33": {
        start: {
          line: 87,
          column: 29
        },
        end: {
          line: 89,
          column: 8
        }
      },
      "34": {
        start: {
          line: 88,
          column: 4
        },
        end: {
          line: 88,
          column: 25
        }
      },
      "35": {
        start: {
          line: 91,
          column: 32
        },
        end: {
          line: 93,
          column: 8
        }
      },
      "36": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 92,
          column: 28
        }
      },
      "37": {
        start: {
          line: 95,
          column: 2
        },
        end: {
          line: 103,
          column: 4
        }
      },
      "38": {
        start: {
          line: 108,
          column: 19
        },
        end: {
          line: 108,
          column: 28
        }
      },
      "39": {
        start: {
          line: 109,
          column: 38
        },
        end: {
          line: 109,
          column: 57
        }
      },
      "40": {
        start: {
          line: 111,
          column: 2
        },
        end: {
          line: 122,
          column: 13
        }
      },
      "41": {
        start: {
          line: 112,
          column: 4
        },
        end: {
          line: 112,
          column: 22
        }
      },
      "42": {
        start: {
          line: 112,
          column: 15
        },
        end: {
          line: 112,
          column: 22
        }
      },
      "43": {
        start: {
          line: 114,
          column: 24
        },
        end: {
          line: 119,
          column: 5
        }
      },
      "44": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 117,
          column: 29
        }
      },
      "45": {
        start: {
          line: 121,
          column: 4
        },
        end: {
          line: 121,
          column: 23
        }
      },
      "46": {
        start: {
          line: 124,
          column: 2
        },
        end: {
          line: 124,
          column: 20
        }
      },
      "47": {
        start: {
          line: 129,
          column: 19
        },
        end: {
          line: 129,
          column: 28
        }
      },
      "48": {
        start: {
          line: 130,
          column: 40
        },
        end: {
          line: 130,
          column: 59
        }
      },
      "49": {
        start: {
          line: 132,
          column: 2
        },
        end: {
          line: 143,
          column: 13
        }
      },
      "50": {
        start: {
          line: 133,
          column: 4
        },
        end: {
          line: 133,
          column: 22
        }
      },
      "51": {
        start: {
          line: 133,
          column: 15
        },
        end: {
          line: 133,
          column: 22
        }
      },
      "52": {
        start: {
          line: 135,
          column: 24
        },
        end: {
          line: 140,
          column: 5
        }
      },
      "53": {
        start: {
          line: 138,
          column: 8
        },
        end: {
          line: 138,
          column: 51
        }
      },
      "54": {
        start: {
          line: 138,
          column: 31
        },
        end: {
          line: 138,
          column: 49
        }
      },
      "55": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 142,
          column: 23
        }
      },
      "56": {
        start: {
          line: 145,
          column: 27
        },
        end: {
          line: 147,
          column: 8
        }
      },
      "57": {
        start: {
          line: 146,
          column: 4
        },
        end: {
          line: 146,
          column: 23
        }
      },
      "58": {
        start: {
          line: 149,
          column: 2
        },
        end: {
          line: 152,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "useRealtime",
        decl: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 27
          }
        },
        loc: {
          start: {
            line: 15,
            column: 49
          },
          end: {
            line: 104,
            column: 1
          }
        },
        line: 15
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 21,
            column: 12
          },
          end: {
            line: 21,
            column: 13
          }
        },
        loc: {
          start: {
            line: 21,
            column: 18
          },
          end: {
            line: 58,
            column: 3
          }
        },
        line: 21
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 33,
            column: 6
          },
          end: {
            line: 33,
            column: 7
          }
        },
        loc: {
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 35,
            column: 7
          }
        },
        line: 33
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 34,
            column: 25
          },
          end: {
            line: 34,
            column: 26
          }
        },
        loc: {
          start: {
            line: 34,
            column: 33
          },
          end: {
            line: 34,
            column: 56
          }
        },
        line: 34
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 41,
            column: 6
          },
          end: {
            line: 41,
            column: 7
          }
        },
        loc: {
          start: {
            line: 41,
            column: 19
          },
          end: {
            line: 43,
            column: 7
          }
        },
        line: 41
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 42,
            column: 28
          },
          end: {
            line: 42,
            column: 29
          }
        },
        loc: {
          start: {
            line: 42,
            column: 36
          },
          end: {
            line: 42,
            column: 54
          }
        },
        line: 42
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 49,
            column: 11
          },
          end: {
            line: 49,
            column: 12
          }
        },
        loc: {
          start: {
            line: 49,
            column: 17
          },
          end: {
            line: 57,
            column: 5
          }
        },
        line: 49
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 60,
            column: 42
          },
          end: {
            line: 60,
            column: 43
          }
        },
        loc: {
          start: {
            line: 63,
            column: 7
          },
          end: {
            line: 71,
            column: 3
          }
        },
        line: 63
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 73,
            column: 41
          },
          end: {
            line: 73,
            column: 42
          }
        },
        loc: {
          start: {
            line: 77,
            column: 7
          },
          end: {
            line: 85,
            column: 3
          }
        },
        line: 77
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 87,
            column: 41
          },
          end: {
            line: 87,
            column: 42
          }
        },
        loc: {
          start: {
            line: 87,
            column: 47
          },
          end: {
            line: 89,
            column: 3
          }
        },
        line: 87
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 91,
            column: 44
          },
          end: {
            line: 91,
            column: 45
          }
        },
        loc: {
          start: {
            line: 91,
            column: 50
          },
          end: {
            line: 93,
            column: 3
          }
        },
        line: 91
      },
      "11": {
        name: "useRealtimeSkillStats",
        decl: {
          start: {
            line: 107,
            column: 16
          },
          end: {
            line: 107,
            column: 37
          }
        },
        loc: {
          start: {
            line: 107,
            column: 40
          },
          end: {
            line: 125,
            column: 1
          }
        },
        line: 107
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 111,
            column: 12
          },
          end: {
            line: 111,
            column: 13
          }
        },
        loc: {
          start: {
            line: 111,
            column: 18
          },
          end: {
            line: 122,
            column: 3
          }
        },
        line: 111
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 116,
            column: 6
          },
          end: {
            line: 116,
            column: 7
          }
        },
        loc: {
          start: {
            line: 116,
            column: 17
          },
          end: {
            line: 118,
            column: 7
          }
        },
        line: 116
      },
      "14": {
        name: "useRealtimeTrainingSessions",
        decl: {
          start: {
            line: 128,
            column: 16
          },
          end: {
            line: 128,
            column: 43
          }
        },
        loc: {
          start: {
            line: 128,
            column: 46
          },
          end: {
            line: 153,
            column: 1
          }
        },
        line: 128
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 132,
            column: 12
          },
          end: {
            line: 132,
            column: 13
          }
        },
        loc: {
          start: {
            line: 132,
            column: 18
          },
          end: {
            line: 143,
            column: 3
          }
        },
        line: 132
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 137,
            column: 6
          },
          end: {
            line: 137,
            column: 7
          }
        },
        loc: {
          start: {
            line: 137,
            column: 19
          },
          end: {
            line: 139,
            column: 7
          }
        },
        line: 137
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 138,
            column: 23
          },
          end: {
            line: 138,
            column: 24
          }
        },
        loc: {
          start: {
            line: 138,
            column: 31
          },
          end: {
            line: 138,
            column: 49
          }
        },
        line: 138
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 145,
            column: 39
          },
          end: {
            line: 145,
            column: 40
          }
        },
        loc: {
          start: {
            line: 145,
            column: 45
          },
          end: {
            line: 147,
            column: 3
          }
        },
        line: 145
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 25,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 25,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "1": {
        loc: {
          start: {
            line: 50,
            column: 6
          },
          end: {
            line: 52,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 6
          },
          end: {
            line: 52,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "2": {
        loc: {
          start: {
            line: 53,
            column: 6
          },
          end: {
            line: 55,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 53,
            column: 6
          },
          end: {
            line: 55,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 53
      },
      "3": {
        loc: {
          start: {
            line: 64,
            column: 4
          },
          end: {
            line: 64,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 64,
            column: 4
          },
          end: {
            line: 64,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 64
      },
      "4": {
        loc: {
          start: {
            line: 78,
            column: 4
          },
          end: {
            line: 78,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 4
          },
          end: {
            line: 78,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 78
      },
      "5": {
        loc: {
          start: {
            line: 112,
            column: 4
          },
          end: {
            line: 112,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 112,
            column: 4
          },
          end: {
            line: 112,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 112
      },
      "6": {
        loc: {
          start: {
            line: 133,
            column: 4
          },
          end: {
            line: 133,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 4
          },
          end: {
            line: 133,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 133
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5b119bd0ba7acfd8d4becc60903e5cd1a63e1b64"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_tl12vljt0 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_tl12vljt0();
import { useEffect, useState, useCallback } from 'react';
import { realtimeService } from "../services/realtime";
import { useAuth } from "../contexts/AuthContext";
export function useRealtime() {
  cov_tl12vljt0().f[0]++;
  var _ref = (cov_tl12vljt0().s[0]++, useAuth()),
    user = _ref.user;
  var _ref2 = (cov_tl12vljt0().s[1]++, useState([])),
    _ref3 = _slicedToArray(_ref2, 2),
    notifications = _ref3[0],
    setNotifications = _ref3[1];
  var _ref4 = (cov_tl12vljt0().s[2]++, useState([])),
    _ref5 = _slicedToArray(_ref4, 2),
    coachingMessages = _ref5[0],
    setCoachingMessages = _ref5[1];
  var _ref6 = (cov_tl12vljt0().s[3]++, useState(false)),
    _ref7 = _slicedToArray(_ref6, 2),
    isConnected = _ref7[0],
    setIsConnected = _ref7[1];
  cov_tl12vljt0().s[4]++;
  useEffect(function () {
    cov_tl12vljt0().f[1]++;
    cov_tl12vljt0().s[5]++;
    if (!user) {
      cov_tl12vljt0().b[0][0]++;
      cov_tl12vljt0().s[6]++;
      setIsConnected(false);
      cov_tl12vljt0().s[7]++;
      return;
    } else {
      cov_tl12vljt0().b[0][1]++;
    }
    var notificationUnsubscribe;
    var coachingUnsubscribe;
    cov_tl12vljt0().s[8]++;
    notificationUnsubscribe = realtimeService.subscribeToNotifications(user.id, function (notification) {
      cov_tl12vljt0().f[2]++;
      cov_tl12vljt0().s[9]++;
      setNotifications(function (prev) {
        cov_tl12vljt0().f[3]++;
        cov_tl12vljt0().s[10]++;
        return [notification].concat(_toConsumableArray(prev));
      });
    });
    cov_tl12vljt0().s[11]++;
    coachingUnsubscribe = realtimeService.subscribeToCoaching(user.id, function (message) {
      cov_tl12vljt0().f[4]++;
      cov_tl12vljt0().s[12]++;
      setCoachingMessages(function (prev) {
        cov_tl12vljt0().f[5]++;
        cov_tl12vljt0().s[13]++;
        return [message].concat(_toConsumableArray(prev));
      });
    });
    cov_tl12vljt0().s[14]++;
    setIsConnected(true);
    cov_tl12vljt0().s[15]++;
    return function () {
      cov_tl12vljt0().f[6]++;
      cov_tl12vljt0().s[16]++;
      if (notificationUnsubscribe) {
        cov_tl12vljt0().b[1][0]++;
        cov_tl12vljt0().s[17]++;
        notificationUnsubscribe();
      } else {
        cov_tl12vljt0().b[1][1]++;
      }
      cov_tl12vljt0().s[18]++;
      if (coachingUnsubscribe) {
        cov_tl12vljt0().b[2][0]++;
        cov_tl12vljt0().s[19]++;
        coachingUnsubscribe();
      } else {
        cov_tl12vljt0().b[2][1]++;
      }
      cov_tl12vljt0().s[20]++;
      setIsConnected(false);
    };
  }, [user]);
  var sendCoachingMessage = (cov_tl12vljt0().s[21]++, useCallback(function () {
    var _ref8 = _asyncToGenerator(function* (message, type) {
      cov_tl12vljt0().f[7]++;
      cov_tl12vljt0().s[22]++;
      if (!user) {
        cov_tl12vljt0().b[3][0]++;
        cov_tl12vljt0().s[23]++;
        return;
      } else {
        cov_tl12vljt0().b[3][1]++;
      }
      cov_tl12vljt0().s[24]++;
      try {
        cov_tl12vljt0().s[25]++;
        yield realtimeService.sendCoachingMessage(user.id, message, type);
      } catch (error) {
        cov_tl12vljt0().s[26]++;
        console.error('Error sending coaching message:', error);
      }
    });
    return function (_x, _x2) {
      return _ref8.apply(this, arguments);
    };
  }(), [user]));
  var createNotification = (cov_tl12vljt0().s[27]++, useCallback(function () {
    var _ref9 = _asyncToGenerator(function* (type, title, message) {
      cov_tl12vljt0().f[8]++;
      cov_tl12vljt0().s[28]++;
      if (!user) {
        cov_tl12vljt0().b[4][0]++;
        cov_tl12vljt0().s[29]++;
        return;
      } else {
        cov_tl12vljt0().b[4][1]++;
      }
      cov_tl12vljt0().s[30]++;
      try {
        cov_tl12vljt0().s[31]++;
        yield realtimeService.createNotification(user.id, type, title, message);
      } catch (error) {
        cov_tl12vljt0().s[32]++;
        console.error('Error creating notification:', error);
      }
    });
    return function (_x3, _x4, _x5) {
      return _ref9.apply(this, arguments);
    };
  }(), [user]));
  var clearNotifications = (cov_tl12vljt0().s[33]++, useCallback(function () {
    cov_tl12vljt0().f[9]++;
    cov_tl12vljt0().s[34]++;
    setNotifications([]);
  }, []));
  var clearCoachingMessages = (cov_tl12vljt0().s[35]++, useCallback(function () {
    cov_tl12vljt0().f[10]++;
    cov_tl12vljt0().s[36]++;
    setCoachingMessages([]);
  }, []));
  cov_tl12vljt0().s[37]++;
  return {
    notifications: notifications,
    coachingMessages: coachingMessages,
    isConnected: isConnected,
    sendCoachingMessage: sendCoachingMessage,
    createNotification: createNotification,
    clearNotifications: clearNotifications,
    clearCoachingMessages: clearCoachingMessages
  };
}
export function useRealtimeSkillStats() {
  cov_tl12vljt0().f[11]++;
  var _ref0 = (cov_tl12vljt0().s[38]++, useAuth()),
    user = _ref0.user;
  var _ref1 = (cov_tl12vljt0().s[39]++, useState(null)),
    _ref10 = _slicedToArray(_ref1, 2),
    skillStats = _ref10[0],
    setSkillStats = _ref10[1];
  cov_tl12vljt0().s[40]++;
  useEffect(function () {
    cov_tl12vljt0().f[12]++;
    cov_tl12vljt0().s[41]++;
    if (!user) {
      cov_tl12vljt0().b[5][0]++;
      cov_tl12vljt0().s[42]++;
      return;
    } else {
      cov_tl12vljt0().b[5][1]++;
    }
    var unsubscribe = (cov_tl12vljt0().s[43]++, realtimeService.subscribeToSkillUpdates(user.id, function (stats) {
      cov_tl12vljt0().f[13]++;
      cov_tl12vljt0().s[44]++;
      setSkillStats(stats);
    }));
    cov_tl12vljt0().s[45]++;
    return unsubscribe;
  }, [user]);
  cov_tl12vljt0().s[46]++;
  return skillStats;
}
export function useRealtimeTrainingSessions() {
  cov_tl12vljt0().f[14]++;
  var _ref11 = (cov_tl12vljt0().s[47]++, useAuth()),
    user = _ref11.user;
  var _ref12 = (cov_tl12vljt0().s[48]++, useState([])),
    _ref13 = _slicedToArray(_ref12, 2),
    newSessions = _ref13[0],
    setNewSessions = _ref13[1];
  cov_tl12vljt0().s[49]++;
  useEffect(function () {
    cov_tl12vljt0().f[15]++;
    cov_tl12vljt0().s[50]++;
    if (!user) {
      cov_tl12vljt0().b[6][0]++;
      cov_tl12vljt0().s[51]++;
      return;
    } else {
      cov_tl12vljt0().b[6][1]++;
    }
    var unsubscribe = (cov_tl12vljt0().s[52]++, realtimeService.subscribeToTrainingSessions(user.id, function (session) {
      cov_tl12vljt0().f[16]++;
      cov_tl12vljt0().s[53]++;
      setNewSessions(function (prev) {
        cov_tl12vljt0().f[17]++;
        cov_tl12vljt0().s[54]++;
        return [session].concat(_toConsumableArray(prev));
      });
    }));
    cov_tl12vljt0().s[55]++;
    return unsubscribe;
  }, [user]);
  var clearNewSessions = (cov_tl12vljt0().s[56]++, useCallback(function () {
    cov_tl12vljt0().f[18]++;
    cov_tl12vljt0().s[57]++;
    setNewSessions([]);
  }, []));
  cov_tl12vljt0().s[58]++;
  return {
    newSessions: newSessions,
    clearNewSessions: clearNewSessions
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZUNhbGxiYWNrIiwicmVhbHRpbWVTZXJ2aWNlIiwidXNlQXV0aCIsInVzZVJlYWx0aW1lIiwiY292X3RsMTJ2bGp0MCIsImYiLCJfcmVmIiwicyIsInVzZXIiLCJfcmVmMiIsIl9yZWYzIiwiX3NsaWNlZFRvQXJyYXkiLCJub3RpZmljYXRpb25zIiwic2V0Tm90aWZpY2F0aW9ucyIsIl9yZWY0IiwiX3JlZjUiLCJjb2FjaGluZ01lc3NhZ2VzIiwic2V0Q29hY2hpbmdNZXNzYWdlcyIsIl9yZWY2IiwiX3JlZjciLCJpc0Nvbm5lY3RlZCIsInNldElzQ29ubmVjdGVkIiwiYiIsIm5vdGlmaWNhdGlvblVuc3Vic2NyaWJlIiwiY29hY2hpbmdVbnN1YnNjcmliZSIsInN1YnNjcmliZVRvTm90aWZpY2F0aW9ucyIsImlkIiwibm90aWZpY2F0aW9uIiwicHJldiIsImNvbmNhdCIsIl90b0NvbnN1bWFibGVBcnJheSIsInN1YnNjcmliZVRvQ29hY2hpbmciLCJtZXNzYWdlIiwic2VuZENvYWNoaW5nTWVzc2FnZSIsIl9yZWY4IiwiX2FzeW5jVG9HZW5lcmF0b3IiLCJ0eXBlIiwiZXJyb3IiLCJjb25zb2xlIiwiX3giLCJfeDIiLCJhcHBseSIsImFyZ3VtZW50cyIsImNyZWF0ZU5vdGlmaWNhdGlvbiIsIl9yZWY5IiwidGl0bGUiLCJfeDMiLCJfeDQiLCJfeDUiLCJjbGVhck5vdGlmaWNhdGlvbnMiLCJjbGVhckNvYWNoaW5nTWVzc2FnZXMiLCJ1c2VSZWFsdGltZVNraWxsU3RhdHMiLCJfcmVmMCIsIl9yZWYxIiwiX3JlZjEwIiwic2tpbGxTdGF0cyIsInNldFNraWxsU3RhdHMiLCJ1bnN1YnNjcmliZSIsInN1YnNjcmliZVRvU2tpbGxVcGRhdGVzIiwic3RhdHMiLCJ1c2VSZWFsdGltZVRyYWluaW5nU2Vzc2lvbnMiLCJfcmVmMTEiLCJfcmVmMTIiLCJfcmVmMTMiLCJuZXdTZXNzaW9ucyIsInNldE5ld1Nlc3Npb25zIiwic3Vic2NyaWJlVG9UcmFpbmluZ1Nlc3Npb25zIiwic2Vzc2lvbiIsImNsZWFyTmV3U2Vzc2lvbnMiXSwic291cmNlcyI6WyJ1c2VSZWFsdGltZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHJlYWx0aW1lU2VydmljZSwgUmVhbHRpbWVOb3RpZmljYXRpb24sIFJlYWx0aW1lQ29hY2hpbmdNZXNzYWdlIH0gZnJvbSAnQC9zZXJ2aWNlcy9yZWFsdGltZSc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5cbmludGVyZmFjZSBVc2VSZWFsdGltZVJldHVybiB7XG4gIG5vdGlmaWNhdGlvbnM6IFJlYWx0aW1lTm90aWZpY2F0aW9uW107XG4gIGNvYWNoaW5nTWVzc2FnZXM6IFJlYWx0aW1lQ29hY2hpbmdNZXNzYWdlW107XG4gIGlzQ29ubmVjdGVkOiBib29sZWFuO1xuICBzZW5kQ29hY2hpbmdNZXNzYWdlOiAobWVzc2FnZTogc3RyaW5nLCB0eXBlOiAnZW5jb3VyYWdlbWVudCcgfCAnY29ycmVjdGlvbicgfCAndGlwJykgPT4gUHJvbWlzZTx2b2lkPjtcbiAgY3JlYXRlTm90aWZpY2F0aW9uOiAodHlwZTogJ2FjaGlldmVtZW50JyB8ICd0aXAnIHwgJ3JlbWluZGVyJyB8ICdtYXRjaF9yZXN1bHQnLCB0aXRsZTogc3RyaW5nLCBtZXNzYWdlOiBzdHJpbmcpID0+IFByb21pc2U8dm9pZD47XG4gIGNsZWFyTm90aWZpY2F0aW9uczogKCkgPT4gdm9pZDtcbiAgY2xlYXJDb2FjaGluZ01lc3NhZ2VzOiAoKSA9PiB2b2lkO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlUmVhbHRpbWUoKTogVXNlUmVhbHRpbWVSZXR1cm4ge1xuICBjb25zdCB7IHVzZXIgfSA9IHVzZUF1dGgoKTtcbiAgY29uc3QgW25vdGlmaWNhdGlvbnMsIHNldE5vdGlmaWNhdGlvbnNdID0gdXNlU3RhdGU8UmVhbHRpbWVOb3RpZmljYXRpb25bXT4oW10pO1xuICBjb25zdCBbY29hY2hpbmdNZXNzYWdlcywgc2V0Q29hY2hpbmdNZXNzYWdlc10gPSB1c2VTdGF0ZTxSZWFsdGltZUNvYWNoaW5nTWVzc2FnZVtdPihbXSk7XG4gIGNvbnN0IFtpc0Nvbm5lY3RlZCwgc2V0SXNDb25uZWN0ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICBzZXRJc0Nvbm5lY3RlZChmYWxzZSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgbGV0IG5vdGlmaWNhdGlvblVuc3Vic2NyaWJlOiAoKCkgPT4gdm9pZCkgfCB1bmRlZmluZWQ7XG4gICAgbGV0IGNvYWNoaW5nVW5zdWJzY3JpYmU6ICgoKSA9PiB2b2lkKSB8IHVuZGVmaW5lZDtcblxuICAgIC8vIFN1YnNjcmliZSB0byBub3RpZmljYXRpb25zXG4gICAgbm90aWZpY2F0aW9uVW5zdWJzY3JpYmUgPSByZWFsdGltZVNlcnZpY2Uuc3Vic2NyaWJlVG9Ob3RpZmljYXRpb25zKFxuICAgICAgdXNlci5pZCxcbiAgICAgIChub3RpZmljYXRpb24pID0+IHtcbiAgICAgICAgc2V0Tm90aWZpY2F0aW9ucyhwcmV2ID0+IFtub3RpZmljYXRpb24sIC4uLnByZXZdKTtcbiAgICAgIH1cbiAgICApO1xuXG4gICAgLy8gU3Vic2NyaWJlIHRvIGNvYWNoaW5nIG1lc3NhZ2VzXG4gICAgY29hY2hpbmdVbnN1YnNjcmliZSA9IHJlYWx0aW1lU2VydmljZS5zdWJzY3JpYmVUb0NvYWNoaW5nKFxuICAgICAgdXNlci5pZCxcbiAgICAgIChtZXNzYWdlKSA9PiB7XG4gICAgICAgIHNldENvYWNoaW5nTWVzc2FnZXMocHJldiA9PiBbbWVzc2FnZSwgLi4ucHJldl0pO1xuICAgICAgfVxuICAgICk7XG5cbiAgICBzZXRJc0Nvbm5lY3RlZCh0cnVlKTtcblxuICAgIC8vIENsZWFudXAgZnVuY3Rpb25cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaWYgKG5vdGlmaWNhdGlvblVuc3Vic2NyaWJlKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvblVuc3Vic2NyaWJlKCk7XG4gICAgICB9XG4gICAgICBpZiAoY29hY2hpbmdVbnN1YnNjcmliZSkge1xuICAgICAgICBjb2FjaGluZ1Vuc3Vic2NyaWJlKCk7XG4gICAgICB9XG4gICAgICBzZXRJc0Nvbm5lY3RlZChmYWxzZSk7XG4gICAgfTtcbiAgfSwgW3VzZXJdKTtcblxuICBjb25zdCBzZW5kQ29hY2hpbmdNZXNzYWdlID0gdXNlQ2FsbGJhY2soYXN5bmMgKFxuICAgIG1lc3NhZ2U6IHN0cmluZyxcbiAgICB0eXBlOiAnZW5jb3VyYWdlbWVudCcgfCAnY29ycmVjdGlvbicgfCAndGlwJ1xuICApID0+IHtcbiAgICBpZiAoIXVzZXIpIHJldHVybjtcbiAgICBcbiAgICB0cnkge1xuICAgICAgYXdhaXQgcmVhbHRpbWVTZXJ2aWNlLnNlbmRDb2FjaGluZ01lc3NhZ2UodXNlci5pZCwgbWVzc2FnZSwgdHlwZSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNlbmRpbmcgY29hY2hpbmcgbWVzc2FnZTonLCBlcnJvcik7XG4gICAgfVxuICB9LCBbdXNlcl0pO1xuXG4gIGNvbnN0IGNyZWF0ZU5vdGlmaWNhdGlvbiA9IHVzZUNhbGxiYWNrKGFzeW5jIChcbiAgICB0eXBlOiAnYWNoaWV2ZW1lbnQnIHwgJ3RpcCcgfCAncmVtaW5kZXInIHwgJ21hdGNoX3Jlc3VsdCcsXG4gICAgdGl0bGU6IHN0cmluZyxcbiAgICBtZXNzYWdlOiBzdHJpbmdcbiAgKSA9PiB7XG4gICAgaWYgKCF1c2VyKSByZXR1cm47XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHJlYWx0aW1lU2VydmljZS5jcmVhdGVOb3RpZmljYXRpb24odXNlci5pZCwgdHlwZSwgdGl0bGUsIG1lc3NhZ2UpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBub3RpZmljYXRpb246JywgZXJyb3IpO1xuICAgIH1cbiAgfSwgW3VzZXJdKTtcblxuICBjb25zdCBjbGVhck5vdGlmaWNhdGlvbnMgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgc2V0Tm90aWZpY2F0aW9ucyhbXSk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBjbGVhckNvYWNoaW5nTWVzc2FnZXMgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgc2V0Q29hY2hpbmdNZXNzYWdlcyhbXSk7XG4gIH0sIFtdKTtcblxuICByZXR1cm4ge1xuICAgIG5vdGlmaWNhdGlvbnMsXG4gICAgY29hY2hpbmdNZXNzYWdlcyxcbiAgICBpc0Nvbm5lY3RlZCxcbiAgICBzZW5kQ29hY2hpbmdNZXNzYWdlLFxuICAgIGNyZWF0ZU5vdGlmaWNhdGlvbixcbiAgICBjbGVhck5vdGlmaWNhdGlvbnMsXG4gICAgY2xlYXJDb2FjaGluZ01lc3NhZ2VzLFxuICB9O1xufVxuXG4vLyBIb29rIGZvciBza2lsbCBzdGF0cyByZWFsLXRpbWUgdXBkYXRlc1xuZXhwb3J0IGZ1bmN0aW9uIHVzZVJlYWx0aW1lU2tpbGxTdGF0cygpIHtcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IFtza2lsbFN0YXRzLCBzZXRTa2lsbFN0YXRzXSA9IHVzZVN0YXRlPGFueT4obnVsbCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIXVzZXIpIHJldHVybjtcblxuICAgIGNvbnN0IHVuc3Vic2NyaWJlID0gcmVhbHRpbWVTZXJ2aWNlLnN1YnNjcmliZVRvU2tpbGxVcGRhdGVzKFxuICAgICAgdXNlci5pZCxcbiAgICAgIChzdGF0cykgPT4ge1xuICAgICAgICBzZXRTa2lsbFN0YXRzKHN0YXRzKTtcbiAgICAgIH1cbiAgICApO1xuXG4gICAgcmV0dXJuIHVuc3Vic2NyaWJlO1xuICB9LCBbdXNlcl0pO1xuXG4gIHJldHVybiBza2lsbFN0YXRzO1xufVxuXG4vLyBIb29rIGZvciB0cmFpbmluZyBzZXNzaW9uIHJlYWwtdGltZSB1cGRhdGVzXG5leHBvcnQgZnVuY3Rpb24gdXNlUmVhbHRpbWVUcmFpbmluZ1Nlc3Npb25zKCkge1xuICBjb25zdCB7IHVzZXIgfSA9IHVzZUF1dGgoKTtcbiAgY29uc3QgW25ld1Nlc3Npb25zLCBzZXROZXdTZXNzaW9uc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCF1c2VyKSByZXR1cm47XG5cbiAgICBjb25zdCB1bnN1YnNjcmliZSA9IHJlYWx0aW1lU2VydmljZS5zdWJzY3JpYmVUb1RyYWluaW5nU2Vzc2lvbnMoXG4gICAgICB1c2VyLmlkLFxuICAgICAgKHNlc3Npb24pID0+IHtcbiAgICAgICAgc2V0TmV3U2Vzc2lvbnMocHJldiA9PiBbc2Vzc2lvbiwgLi4ucHJldl0pO1xuICAgICAgfVxuICAgICk7XG5cbiAgICByZXR1cm4gdW5zdWJzY3JpYmU7XG4gIH0sIFt1c2VyXSk7XG5cbiAgY29uc3QgY2xlYXJOZXdTZXNzaW9ucyA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXROZXdTZXNzaW9ucyhbXSk7XG4gIH0sIFtdKTtcblxuICByZXR1cm4ge1xuICAgIG5ld1Nlc3Npb25zLFxuICAgIGNsZWFyTmV3U2Vzc2lvbnMsXG4gIH07XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsU0FBU0EsU0FBUyxFQUFFQyxRQUFRLEVBQUVDLFdBQVcsUUFBUSxPQUFPO0FBQ3hELFNBQVNDLGVBQWU7QUFDeEIsU0FBU0MsT0FBTztBQVloQixPQUFPLFNBQVNDLFdBQVdBLENBQUEsRUFBc0I7RUFBQUMsYUFBQSxHQUFBQyxDQUFBO0VBQy9DLElBQUFDLElBQUEsSUFBQUYsYUFBQSxHQUFBRyxDQUFBLE9BQWlCTCxPQUFPLENBQUMsQ0FBQztJQUFsQk0sSUFBSSxHQUFBRixJQUFBLENBQUpFLElBQUk7RUFDWixJQUFBQyxLQUFBLElBQUFMLGFBQUEsR0FBQUcsQ0FBQSxPQUEwQ1IsUUFBUSxDQUF5QixFQUFFLENBQUM7SUFBQVcsS0FBQSxHQUFBQyxjQUFBLENBQUFGLEtBQUE7SUFBdkVHLGFBQWEsR0FBQUYsS0FBQTtJQUFFRyxnQkFBZ0IsR0FBQUgsS0FBQTtFQUN0QyxJQUFBSSxLQUFBLElBQUFWLGFBQUEsR0FBQUcsQ0FBQSxPQUFnRFIsUUFBUSxDQUE0QixFQUFFLENBQUM7SUFBQWdCLEtBQUEsR0FBQUosY0FBQSxDQUFBRyxLQUFBO0lBQWhGRSxnQkFBZ0IsR0FBQUQsS0FBQTtJQUFFRSxtQkFBbUIsR0FBQUYsS0FBQTtFQUM1QyxJQUFBRyxLQUFBLElBQUFkLGFBQUEsR0FBQUcsQ0FBQSxPQUFzQ1IsUUFBUSxDQUFDLEtBQUssQ0FBQztJQUFBb0IsS0FBQSxHQUFBUixjQUFBLENBQUFPLEtBQUE7SUFBOUNFLFdBQVcsR0FBQUQsS0FBQTtJQUFFRSxjQUFjLEdBQUFGLEtBQUE7RUFBb0JmLGFBQUEsR0FBQUcsQ0FBQTtFQUV0RFQsU0FBUyxDQUFDLFlBQU07SUFBQU0sYUFBQSxHQUFBQyxDQUFBO0lBQUFELGFBQUEsR0FBQUcsQ0FBQTtJQUNkLElBQUksQ0FBQ0MsSUFBSSxFQUFFO01BQUFKLGFBQUEsR0FBQWtCLENBQUE7TUFBQWxCLGFBQUEsR0FBQUcsQ0FBQTtNQUNUYyxjQUFjLENBQUMsS0FBSyxDQUFDO01BQUNqQixhQUFBLEdBQUFHLENBQUE7TUFDdEI7SUFDRixDQUFDO01BQUFILGFBQUEsR0FBQWtCLENBQUE7SUFBQTtJQUVELElBQUlDLHVCQUFpRDtJQUNyRCxJQUFJQyxtQkFBNkM7SUFBQ3BCLGFBQUEsR0FBQUcsQ0FBQTtJQUdsRGdCLHVCQUF1QixHQUFHdEIsZUFBZSxDQUFDd0Isd0JBQXdCLENBQ2hFakIsSUFBSSxDQUFDa0IsRUFBRSxFQUNQLFVBQUNDLFlBQVksRUFBSztNQUFBdkIsYUFBQSxHQUFBQyxDQUFBO01BQUFELGFBQUEsR0FBQUcsQ0FBQTtNQUNoQk0sZ0JBQWdCLENBQUMsVUFBQWUsSUFBSSxFQUFJO1FBQUF4QixhQUFBLEdBQUFDLENBQUE7UUFBQUQsYUFBQSxHQUFBRyxDQUFBO1FBQUEsUUFBQ29CLFlBQVksRUFBQUUsTUFBQSxDQUFBQyxrQkFBQSxDQUFLRixJQUFJO01BQUEsQ0FBQyxDQUFDO0lBQ25ELENBQ0YsQ0FBQztJQUFDeEIsYUFBQSxHQUFBRyxDQUFBO0lBR0ZpQixtQkFBbUIsR0FBR3ZCLGVBQWUsQ0FBQzhCLG1CQUFtQixDQUN2RHZCLElBQUksQ0FBQ2tCLEVBQUUsRUFDUCxVQUFDTSxPQUFPLEVBQUs7TUFBQTVCLGFBQUEsR0FBQUMsQ0FBQTtNQUFBRCxhQUFBLEdBQUFHLENBQUE7TUFDWFUsbUJBQW1CLENBQUMsVUFBQVcsSUFBSSxFQUFJO1FBQUF4QixhQUFBLEdBQUFDLENBQUE7UUFBQUQsYUFBQSxHQUFBRyxDQUFBO1FBQUEsUUFBQ3lCLE9BQU8sRUFBQUgsTUFBQSxDQUFBQyxrQkFBQSxDQUFLRixJQUFJO01BQUEsQ0FBQyxDQUFDO0lBQ2pELENBQ0YsQ0FBQztJQUFDeEIsYUFBQSxHQUFBRyxDQUFBO0lBRUZjLGNBQWMsQ0FBQyxJQUFJLENBQUM7SUFBQ2pCLGFBQUEsR0FBQUcsQ0FBQTtJQUdyQixPQUFPLFlBQU07TUFBQUgsYUFBQSxHQUFBQyxDQUFBO01BQUFELGFBQUEsR0FBQUcsQ0FBQTtNQUNYLElBQUlnQix1QkFBdUIsRUFBRTtRQUFBbkIsYUFBQSxHQUFBa0IsQ0FBQTtRQUFBbEIsYUFBQSxHQUFBRyxDQUFBO1FBQzNCZ0IsdUJBQXVCLENBQUMsQ0FBQztNQUMzQixDQUFDO1FBQUFuQixhQUFBLEdBQUFrQixDQUFBO01BQUE7TUFBQWxCLGFBQUEsR0FBQUcsQ0FBQTtNQUNELElBQUlpQixtQkFBbUIsRUFBRTtRQUFBcEIsYUFBQSxHQUFBa0IsQ0FBQTtRQUFBbEIsYUFBQSxHQUFBRyxDQUFBO1FBQ3ZCaUIsbUJBQW1CLENBQUMsQ0FBQztNQUN2QixDQUFDO1FBQUFwQixhQUFBLEdBQUFrQixDQUFBO01BQUE7TUFBQWxCLGFBQUEsR0FBQUcsQ0FBQTtNQUNEYyxjQUFjLENBQUMsS0FBSyxDQUFDO0lBQ3ZCLENBQUM7RUFDSCxDQUFDLEVBQUUsQ0FBQ2IsSUFBSSxDQUFDLENBQUM7RUFFVixJQUFNeUIsbUJBQW1CLElBQUE3QixhQUFBLEdBQUFHLENBQUEsUUFBR1AsV0FBVztJQUFBLElBQUFrQyxLQUFBLEdBQUFDLGlCQUFBLENBQUMsV0FDdENILE9BQWUsRUFDZkksSUFBNEMsRUFDekM7TUFBQWhDLGFBQUEsR0FBQUMsQ0FBQTtNQUFBRCxhQUFBLEdBQUFHLENBQUE7TUFDSCxJQUFJLENBQUNDLElBQUksRUFBRTtRQUFBSixhQUFBLEdBQUFrQixDQUFBO1FBQUFsQixhQUFBLEdBQUFHLENBQUE7UUFBQTtNQUFNLENBQUM7UUFBQUgsYUFBQSxHQUFBa0IsQ0FBQTtNQUFBO01BQUFsQixhQUFBLEdBQUFHLENBQUE7TUFFbEIsSUFBSTtRQUFBSCxhQUFBLEdBQUFHLENBQUE7UUFDRixNQUFNTixlQUFlLENBQUNnQyxtQkFBbUIsQ0FBQ3pCLElBQUksQ0FBQ2tCLEVBQUUsRUFBRU0sT0FBTyxFQUFFSSxJQUFJLENBQUM7TUFDbkUsQ0FBQyxDQUFDLE9BQU9DLEtBQUssRUFBRTtRQUFBakMsYUFBQSxHQUFBRyxDQUFBO1FBQ2QrQixPQUFPLENBQUNELEtBQUssQ0FBQyxpQ0FBaUMsRUFBRUEsS0FBSyxDQUFDO01BQ3pEO0lBQ0YsQ0FBQztJQUFBLGlCQUFBRSxFQUFBLEVBQUFDLEdBQUE7TUFBQSxPQUFBTixLQUFBLENBQUFPLEtBQUEsT0FBQUMsU0FBQTtJQUFBO0VBQUEsS0FBRSxDQUFDbEMsSUFBSSxDQUFDLENBQUM7RUFFVixJQUFNbUMsa0JBQWtCLElBQUF2QyxhQUFBLEdBQUFHLENBQUEsUUFBR1AsV0FBVztJQUFBLElBQUE0QyxLQUFBLEdBQUFULGlCQUFBLENBQUMsV0FDckNDLElBQXlELEVBQ3pEUyxLQUFhLEVBQ2JiLE9BQWUsRUFDWjtNQUFBNUIsYUFBQSxHQUFBQyxDQUFBO01BQUFELGFBQUEsR0FBQUcsQ0FBQTtNQUNILElBQUksQ0FBQ0MsSUFBSSxFQUFFO1FBQUFKLGFBQUEsR0FBQWtCLENBQUE7UUFBQWxCLGFBQUEsR0FBQUcsQ0FBQTtRQUFBO01BQU0sQ0FBQztRQUFBSCxhQUFBLEdBQUFrQixDQUFBO01BQUE7TUFBQWxCLGFBQUEsR0FBQUcsQ0FBQTtNQUVsQixJQUFJO1FBQUFILGFBQUEsR0FBQUcsQ0FBQTtRQUNGLE1BQU1OLGVBQWUsQ0FBQzBDLGtCQUFrQixDQUFDbkMsSUFBSSxDQUFDa0IsRUFBRSxFQUFFVSxJQUFJLEVBQUVTLEtBQUssRUFBRWIsT0FBTyxDQUFDO01BQ3pFLENBQUMsQ0FBQyxPQUFPSyxLQUFLLEVBQUU7UUFBQWpDLGFBQUEsR0FBQUcsQ0FBQTtRQUNkK0IsT0FBTyxDQUFDRCxLQUFLLENBQUMsOEJBQThCLEVBQUVBLEtBQUssQ0FBQztNQUN0RDtJQUNGLENBQUM7SUFBQSxpQkFBQVMsR0FBQSxFQUFBQyxHQUFBLEVBQUFDLEdBQUE7TUFBQSxPQUFBSixLQUFBLENBQUFILEtBQUEsT0FBQUMsU0FBQTtJQUFBO0VBQUEsS0FBRSxDQUFDbEMsSUFBSSxDQUFDLENBQUM7RUFFVixJQUFNeUMsa0JBQWtCLElBQUE3QyxhQUFBLEdBQUFHLENBQUEsUUFBR1AsV0FBVyxDQUFDLFlBQU07SUFBQUksYUFBQSxHQUFBQyxDQUFBO0lBQUFELGFBQUEsR0FBQUcsQ0FBQTtJQUMzQ00sZ0JBQWdCLENBQUMsRUFBRSxDQUFDO0VBQ3RCLENBQUMsRUFBRSxFQUFFLENBQUM7RUFFTixJQUFNcUMscUJBQXFCLElBQUE5QyxhQUFBLEdBQUFHLENBQUEsUUFBR1AsV0FBVyxDQUFDLFlBQU07SUFBQUksYUFBQSxHQUFBQyxDQUFBO0lBQUFELGFBQUEsR0FBQUcsQ0FBQTtJQUM5Q1UsbUJBQW1CLENBQUMsRUFBRSxDQUFDO0VBQ3pCLENBQUMsRUFBRSxFQUFFLENBQUM7RUFBQ2IsYUFBQSxHQUFBRyxDQUFBO0VBRVAsT0FBTztJQUNMSyxhQUFhLEVBQWJBLGFBQWE7SUFDYkksZ0JBQWdCLEVBQWhCQSxnQkFBZ0I7SUFDaEJJLFdBQVcsRUFBWEEsV0FBVztJQUNYYSxtQkFBbUIsRUFBbkJBLG1CQUFtQjtJQUNuQlUsa0JBQWtCLEVBQWxCQSxrQkFBa0I7SUFDbEJNLGtCQUFrQixFQUFsQkEsa0JBQWtCO0lBQ2xCQyxxQkFBcUIsRUFBckJBO0VBQ0YsQ0FBQztBQUNIO0FBR0EsT0FBTyxTQUFTQyxxQkFBcUJBLENBQUEsRUFBRztFQUFBL0MsYUFBQSxHQUFBQyxDQUFBO0VBQ3RDLElBQUErQyxLQUFBLElBQUFoRCxhQUFBLEdBQUFHLENBQUEsUUFBaUJMLE9BQU8sQ0FBQyxDQUFDO0lBQWxCTSxJQUFJLEdBQUE0QyxLQUFBLENBQUo1QyxJQUFJO0VBQ1osSUFBQTZDLEtBQUEsSUFBQWpELGFBQUEsR0FBQUcsQ0FBQSxRQUFvQ1IsUUFBUSxDQUFNLElBQUksQ0FBQztJQUFBdUQsTUFBQSxHQUFBM0MsY0FBQSxDQUFBMEMsS0FBQTtJQUFoREUsVUFBVSxHQUFBRCxNQUFBO0lBQUVFLGFBQWEsR0FBQUYsTUFBQTtFQUF3QmxELGFBQUEsR0FBQUcsQ0FBQTtFQUV4RFQsU0FBUyxDQUFDLFlBQU07SUFBQU0sYUFBQSxHQUFBQyxDQUFBO0lBQUFELGFBQUEsR0FBQUcsQ0FBQTtJQUNkLElBQUksQ0FBQ0MsSUFBSSxFQUFFO01BQUFKLGFBQUEsR0FBQWtCLENBQUE7TUFBQWxCLGFBQUEsR0FBQUcsQ0FBQTtNQUFBO0lBQU0sQ0FBQztNQUFBSCxhQUFBLEdBQUFrQixDQUFBO0lBQUE7SUFFbEIsSUFBTW1DLFdBQVcsSUFBQXJELGFBQUEsR0FBQUcsQ0FBQSxRQUFHTixlQUFlLENBQUN5RCx1QkFBdUIsQ0FDekRsRCxJQUFJLENBQUNrQixFQUFFLEVBQ1AsVUFBQ2lDLEtBQUssRUFBSztNQUFBdkQsYUFBQSxHQUFBQyxDQUFBO01BQUFELGFBQUEsR0FBQUcsQ0FBQTtNQUNUaUQsYUFBYSxDQUFDRyxLQUFLLENBQUM7SUFDdEIsQ0FDRixDQUFDO0lBQUN2RCxhQUFBLEdBQUFHLENBQUE7SUFFRixPQUFPa0QsV0FBVztFQUNwQixDQUFDLEVBQUUsQ0FBQ2pELElBQUksQ0FBQyxDQUFDO0VBQUNKLGFBQUEsR0FBQUcsQ0FBQTtFQUVYLE9BQU9nRCxVQUFVO0FBQ25CO0FBR0EsT0FBTyxTQUFTSywyQkFBMkJBLENBQUEsRUFBRztFQUFBeEQsYUFBQSxHQUFBQyxDQUFBO0VBQzVDLElBQUF3RCxNQUFBLElBQUF6RCxhQUFBLEdBQUFHLENBQUEsUUFBaUJMLE9BQU8sQ0FBQyxDQUFDO0lBQWxCTSxJQUFJLEdBQUFxRCxNQUFBLENBQUpyRCxJQUFJO0VBQ1osSUFBQXNELE1BQUEsSUFBQTFELGFBQUEsR0FBQUcsQ0FBQSxRQUFzQ1IsUUFBUSxDQUFRLEVBQUUsQ0FBQztJQUFBZ0UsTUFBQSxHQUFBcEQsY0FBQSxDQUFBbUQsTUFBQTtJQUFsREUsV0FBVyxHQUFBRCxNQUFBO0lBQUVFLGNBQWMsR0FBQUYsTUFBQTtFQUF3QjNELGFBQUEsR0FBQUcsQ0FBQTtFQUUxRFQsU0FBUyxDQUFDLFlBQU07SUFBQU0sYUFBQSxHQUFBQyxDQUFBO0lBQUFELGFBQUEsR0FBQUcsQ0FBQTtJQUNkLElBQUksQ0FBQ0MsSUFBSSxFQUFFO01BQUFKLGFBQUEsR0FBQWtCLENBQUE7TUFBQWxCLGFBQUEsR0FBQUcsQ0FBQTtNQUFBO0lBQU0sQ0FBQztNQUFBSCxhQUFBLEdBQUFrQixDQUFBO0lBQUE7SUFFbEIsSUFBTW1DLFdBQVcsSUFBQXJELGFBQUEsR0FBQUcsQ0FBQSxRQUFHTixlQUFlLENBQUNpRSwyQkFBMkIsQ0FDN0QxRCxJQUFJLENBQUNrQixFQUFFLEVBQ1AsVUFBQ3lDLE9BQU8sRUFBSztNQUFBL0QsYUFBQSxHQUFBQyxDQUFBO01BQUFELGFBQUEsR0FBQUcsQ0FBQTtNQUNYMEQsY0FBYyxDQUFDLFVBQUFyQyxJQUFJLEVBQUk7UUFBQXhCLGFBQUEsR0FBQUMsQ0FBQTtRQUFBRCxhQUFBLEdBQUFHLENBQUE7UUFBQSxRQUFDNEQsT0FBTyxFQUFBdEMsTUFBQSxDQUFBQyxrQkFBQSxDQUFLRixJQUFJO01BQUEsQ0FBQyxDQUFDO0lBQzVDLENBQ0YsQ0FBQztJQUFDeEIsYUFBQSxHQUFBRyxDQUFBO0lBRUYsT0FBT2tELFdBQVc7RUFDcEIsQ0FBQyxFQUFFLENBQUNqRCxJQUFJLENBQUMsQ0FBQztFQUVWLElBQU00RCxnQkFBZ0IsSUFBQWhFLGFBQUEsR0FBQUcsQ0FBQSxRQUFHUCxXQUFXLENBQUMsWUFBTTtJQUFBSSxhQUFBLEdBQUFDLENBQUE7SUFBQUQsYUFBQSxHQUFBRyxDQUFBO0lBQ3pDMEQsY0FBYyxDQUFDLEVBQUUsQ0FBQztFQUNwQixDQUFDLEVBQUUsRUFBRSxDQUFDO0VBQUM3RCxhQUFBLEdBQUFHLENBQUE7RUFFUCxPQUFPO0lBQ0x5RCxXQUFXLEVBQVhBLFdBQVc7SUFDWEksZ0JBQWdCLEVBQWhCQTtFQUNGLENBQUM7QUFDSCIsImlnbm9yZUxpc3QiOltdfQ==