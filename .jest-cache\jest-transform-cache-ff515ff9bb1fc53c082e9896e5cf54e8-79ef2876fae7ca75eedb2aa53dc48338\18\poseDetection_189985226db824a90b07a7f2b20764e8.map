{"version": 3, "names": ["PoseDetectionService", "_classCallCheck", "modelLoaded", "cov_2iu4jdw5go", "s", "landmarkNames", "_createClass", "key", "value", "_initializeModel", "_asyncToGenerator", "f", "console", "log", "Promise", "resolve", "setTimeout", "error", "initializeModel", "apply", "arguments", "_detectPoses", "videoFrames", "b", "poses", "i", "length", "frame", "pose", "detectPoseInFrame", "push", "generateMockPoses", "detectPoses", "_x", "analyzeTechnique", "biomechanics", "analyzeBiomechanics", "scores", "calculateTechniqueScores", "insights", "generateInsights", "recommendations", "generateRecommendations", "getFallbackAnalysis", "_analyzeLivePose", "imageData", "Date", "now", "feedback", "corrections", "generateLiveFeedback", "generateLiveCorrections", "analyzeLivePose", "_x2", "compareWithIdeal", "userPose", "shotType", "idealPose", "getIdealPose", "similarity", "calculatePoseSimilarity", "differences", "identifyDifferences", "improvements", "suggestImprovements", "_detectPoseInFrame", "timestamp", "landmarks", "generateRealisticLandmarks", "detectShotType", "shotPhase", "detectShotPhase", "confidence", "Math", "random", "_x3", "_x4", "map", "name", "index", "x", "y", "z", "visibility", "rightWrist", "find", "l", "leftWrist", "rightShoulder", "armHeight", "armExtension", "abs", "phase", "floor", "phases", "<PERSON><PERSON><PERSON><PERSON>", "groundForce", "legDrive", "hipRotation", "torsoRotation", "shoulderRotation", "wristSnap", "balance", "centerOfGravity", "stability", "weightTransfer", "timing", "preparationTime", "contactTime", "followThroughTime", "totalTime", "preparationPoses", "filter", "p", "contactPoses", "follow<PERSON><PERSON><PERSON><PERSON><PERSON>s", "preparation", "scorePosePhase", "contact", "follow<PERSON><PERSON><PERSON>", "footwork", "bodyPosition", "racquet<PERSON><PERSON>", "overall", "round", "avgConfidence", "reduce", "sum", "generateIdealLandmarks", "totalDifference", "comparisons", "for<PERSON>ach", "userLandmark", "idealLandmark", "distance", "sqrt", "pow", "avgDifference", "max", "min", "diff", "frameCount", "poseDetectionService"], "sources": ["poseDetection.ts"], "sourcesContent": ["// Pose Detection Service for Tennis Technique Analysis\n// This service simulates MediaPipe pose detection for tennis movements\n\nexport interface PoseLandmark {\n  x: number;\n  y: number;\n  z: number;\n  visibility: number;\n  name: string;\n}\n\nexport interface TennisPose {\n  landmarks: PoseLandmark[];\n  timestamp: number;\n  confidence: number;\n  shotPhase: 'preparation' | 'contact' | 'follow_through' | 'recovery';\n  shotType: 'forehand' | 'backhand' | 'serve' | 'volley' | 'overhead' | 'unknown';\n}\n\nexport interface BiomechanicalAnalysis {\n  kineticChain: {\n    groundForce: number;\n    legDrive: number;\n    hipRotation: number;\n    torsoRotation: number;\n    shoulderRotation: number;\n    armExtension: number;\n    wristSnap: number;\n  };\n  balance: {\n    centerOfGravity: { x: number; y: number };\n    stability: number;\n    weightTransfer: number;\n  };\n  timing: {\n    preparationTime: number;\n    contactTime: number;\n    followThroughTime: number;\n    totalTime: number;\n  };\n}\n\nexport interface TechniqueScores {\n  overall: number;\n  preparation: number;\n  contact: number;\n  followThrough: number;\n  footwork: number;\n  bodyPosition: number;\n  racquetPath: number;\n}\n\nclass PoseDetectionService {\n  private modelLoaded = false;\n  private landmarkNames = [\n    'nose', 'left_eye_inner', 'left_eye', 'left_eye_outer',\n    'right_eye_inner', 'right_eye', 'right_eye_outer',\n    'left_ear', 'right_ear', 'mouth_left', 'mouth_right',\n    'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',\n    'left_wrist', 'right_wrist', 'left_pinky', 'right_pinky',\n    'left_index', 'right_index', 'left_thumb', 'right_thumb',\n    'left_hip', 'right_hip', 'left_knee', 'right_knee',\n    'left_ankle', 'right_ankle', 'left_heel', 'right_heel',\n    'left_foot_index', 'right_foot_index'\n  ];\n\n  /**\n   * Initialize the pose detection model\n   */\n  async initializeModel(): Promise<boolean> {\n    try {\n      // In a real implementation, this would load MediaPipe model\n      console.log('Initializing pose detection model...');\n      \n      // Simulate model loading\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      this.modelLoaded = true;\n      console.log('Pose detection model loaded successfully');\n      return true;\n    } catch (error) {\n      console.error('Failed to load pose detection model:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Detect poses in video frames\n   */\n  async detectPoses(videoFrames: ImageData[]): Promise<TennisPose[]> {\n    if (!this.modelLoaded) {\n      await this.initializeModel();\n    }\n\n    try {\n      const poses: TennisPose[] = [];\n      \n      for (let i = 0; i < videoFrames.length; i++) {\n        const frame = videoFrames[i];\n        const pose = await this.detectPoseInFrame(frame, i * 33.33); // 30fps\n        if (pose) {\n          poses.push(pose);\n        }\n      }\n\n      return poses;\n    } catch (error) {\n      console.error('Pose detection error:', error);\n      return this.generateMockPoses(videoFrames.length);\n    }\n  }\n\n  /**\n   * Analyze tennis technique from poses\n   */\n  analyzeTechnique(poses: TennisPose[]): {\n    biomechanics: BiomechanicalAnalysis;\n    scores: TechniqueScores;\n    insights: string[];\n    recommendations: string[];\n  } {\n    try {\n      const biomechanics = this.analyzeBiomechanics(poses);\n      const scores = this.calculateTechniqueScores(poses, biomechanics);\n      const insights = this.generateInsights(poses, biomechanics, scores);\n      const recommendations = this.generateRecommendations(scores, insights);\n\n      return {\n        biomechanics,\n        scores,\n        insights,\n        recommendations,\n      };\n    } catch (error) {\n      console.error('Technique analysis error:', error);\n      return this.getFallbackAnalysis();\n    }\n  }\n\n  /**\n   * Real-time pose analysis for live coaching\n   */\n  async analyzeLivePose(imageData: ImageData): Promise<{\n    pose: TennisPose | null;\n    feedback: string[];\n    corrections: string[];\n  }> {\n    try {\n      const pose = await this.detectPoseInFrame(imageData, Date.now());\n      \n      if (!pose) {\n        return {\n          pose: null,\n          feedback: ['Unable to detect pose clearly'],\n          corrections: ['Ensure good lighting and clear view'],\n        };\n      }\n\n      const feedback = this.generateLiveFeedback(pose);\n      const corrections = this.generateLiveCorrections(pose);\n\n      return {\n        pose,\n        feedback,\n        corrections,\n      };\n    } catch (error) {\n      console.error('Live pose analysis error:', error);\n      return {\n        pose: null,\n        feedback: ['Analysis temporarily unavailable'],\n        corrections: ['Continue practicing'],\n      };\n    }\n  }\n\n  /**\n   * Compare poses with ideal technique\n   */\n  compareWithIdeal(userPose: TennisPose, shotType: string): {\n    similarity: number;\n    differences: string[];\n    improvements: string[];\n  } {\n    const idealPose = this.getIdealPose(shotType);\n    const similarity = this.calculatePoseSimilarity(userPose, idealPose);\n    const differences = this.identifyDifferences(userPose, idealPose);\n    const improvements = this.suggestImprovements(differences);\n\n    return {\n      similarity,\n      differences,\n      improvements,\n    };\n  }\n\n  // Private methods\n\n  private async detectPoseInFrame(imageData: ImageData, timestamp: number): Promise<TennisPose | null> {\n    // Simulate pose detection processing\n    await new Promise(resolve => setTimeout(resolve, 10));\n\n    // Generate realistic tennis pose landmarks\n    const landmarks = this.generateRealisticLandmarks();\n    const shotType = this.detectShotType(landmarks);\n    const shotPhase = this.detectShotPhase(landmarks, timestamp);\n\n    return {\n      landmarks,\n      timestamp,\n      confidence: 0.85 + Math.random() * 0.1,\n      shotPhase,\n      shotType,\n    };\n  }\n\n  private generateRealisticLandmarks(): PoseLandmark[] {\n    return this.landmarkNames.map((name, index) => {\n      // Generate realistic coordinates for tennis pose\n      let x, y, z;\n      \n      switch (name) {\n        case 'nose':\n          x = 0.5; y = 0.2; z = 0;\n          break;\n        case 'left_shoulder':\n          x = 0.4; y = 0.35; z = -0.1;\n          break;\n        case 'right_shoulder':\n          x = 0.6; y = 0.35; z = -0.1;\n          break;\n        case 'left_wrist':\n          x = 0.3 + Math.random() * 0.2; y = 0.5 + Math.random() * 0.2; z = -0.2;\n          break;\n        case 'right_wrist':\n          x = 0.7 + Math.random() * 0.2; y = 0.5 + Math.random() * 0.2; z = -0.2;\n          break;\n        case 'left_hip':\n          x = 0.45; y = 0.6; z = 0;\n          break;\n        case 'right_hip':\n          x = 0.55; y = 0.6; z = 0;\n          break;\n        case 'left_knee':\n          x = 0.43; y = 0.8; z = 0.1;\n          break;\n        case 'right_knee':\n          x = 0.57; y = 0.8; z = 0.1;\n          break;\n        default:\n          x = 0.5 + (Math.random() - 0.5) * 0.3;\n          y = 0.5 + (Math.random() - 0.5) * 0.6;\n          z = (Math.random() - 0.5) * 0.2;\n      }\n\n      return {\n        x,\n        y,\n        z,\n        visibility: 0.8 + Math.random() * 0.2,\n        name,\n      };\n    });\n  }\n\n  private detectShotType(landmarks: PoseLandmark[]): TennisPose['shotType'] {\n    const rightWrist = landmarks.find(l => l.name === 'right_wrist');\n    const leftWrist = landmarks.find(l => l.name === 'left_wrist');\n    const rightShoulder = landmarks.find(l => l.name === 'right_shoulder');\n\n    if (!rightWrist || !leftWrist || !rightShoulder) {\n      return 'unknown';\n    }\n\n    // Simple heuristics for shot detection\n    const armHeight = rightWrist.y;\n    const armExtension = Math.abs(rightWrist.x - rightShoulder.x);\n\n    if (armHeight < 0.3) return 'serve';\n    if (armHeight < 0.4) return 'overhead';\n    if (armExtension < 0.15) return 'volley';\n    if (rightWrist.x > rightShoulder.x) return 'forehand';\n    return 'backhand';\n  }\n\n  private detectShotPhase(landmarks: PoseLandmark[], timestamp: number): TennisPose['shotPhase'] {\n    // Simulate shot phase detection based on arm position and timing\n    const phase = Math.floor((timestamp / 1000) % 4);\n    const phases: TennisPose['shotPhase'][] = ['preparation', 'contact', 'follow_through', 'recovery'];\n    return phases[phase];\n  }\n\n  private analyzeBiomechanics(poses: TennisPose[]): BiomechanicalAnalysis {\n    // Analyze kinetic chain efficiency\n    const kineticChain = {\n      groundForce: 75 + Math.random() * 20,\n      legDrive: 70 + Math.random() * 25,\n      hipRotation: 80 + Math.random() * 15,\n      torsoRotation: 85 + Math.random() * 10,\n      shoulderRotation: 78 + Math.random() * 18,\n      armExtension: 82 + Math.random() * 15,\n      wristSnap: 75 + Math.random() * 20,\n    };\n\n    // Analyze balance and stability\n    const balance = {\n      centerOfGravity: { x: 0.5, y: 0.6 },\n      stability: 80 + Math.random() * 15,\n      weightTransfer: 75 + Math.random() * 20,\n    };\n\n    // Analyze timing\n    const timing = {\n      preparationTime: 800 + Math.random() * 200,\n      contactTime: 50 + Math.random() * 20,\n      followThroughTime: 400 + Math.random() * 100,\n      totalTime: 1250 + Math.random() * 300,\n    };\n\n    return {\n      kineticChain,\n      balance,\n      timing,\n    };\n  }\n\n  private calculateTechniqueScores(poses: TennisPose[], biomechanics: BiomechanicalAnalysis): TechniqueScores {\n    const preparationPoses = poses.filter(p => p.shotPhase === 'preparation');\n    const contactPoses = poses.filter(p => p.shotPhase === 'contact');\n    const followThroughPoses = poses.filter(p => p.shotPhase === 'follow_through');\n\n    const preparation = this.scorePosePhase(preparationPoses);\n    const contact = this.scorePosePhase(contactPoses);\n    const followThrough = this.scorePosePhase(followThroughPoses);\n    const footwork = biomechanics.balance.stability;\n    const bodyPosition = (biomechanics.kineticChain.hipRotation + biomechanics.kineticChain.torsoRotation) / 2;\n    const racquetPath = (biomechanics.kineticChain.armExtension + biomechanics.kineticChain.wristSnap) / 2;\n\n    const overall = (preparation + contact + followThrough + footwork + bodyPosition + racquetPath) / 6;\n\n    return {\n      overall: Math.round(overall),\n      preparation: Math.round(preparation),\n      contact: Math.round(contact),\n      followThrough: Math.round(followThrough),\n      footwork: Math.round(footwork),\n      bodyPosition: Math.round(bodyPosition),\n      racquetPath: Math.round(racquetPath),\n    };\n  }\n\n  private scorePosePhase(poses: TennisPose[]): number {\n    if (poses.length === 0) return 70;\n    \n    const avgConfidence = poses.reduce((sum, pose) => sum + pose.confidence, 0) / poses.length;\n    return (avgConfidence * 100) + Math.random() * 10;\n  }\n\n  private generateInsights(poses: TennisPose[], biomechanics: BiomechanicalAnalysis, scores: TechniqueScores): string[] {\n    const insights: string[] = [];\n\n    if (scores.overall > 85) {\n      insights.push('Excellent overall technique demonstrated');\n    }\n\n    if (biomechanics.kineticChain.legDrive < 70) {\n      insights.push('Limited leg drive - focus on using lower body power');\n    }\n\n    if (biomechanics.balance.stability < 75) {\n      insights.push('Balance could be improved for more consistent shots');\n    }\n\n    if (scores.contact > 90) {\n      insights.push('Outstanding contact point consistency');\n    }\n\n    return insights;\n  }\n\n  private generateRecommendations(scores: TechniqueScores, insights: string[]): string[] {\n    const recommendations: string[] = [];\n\n    if (scores.preparation < 70) {\n      recommendations.push('Work on early preparation and racquet take-back');\n    }\n\n    if (scores.footwork < 75) {\n      recommendations.push('Practice footwork drills for better court positioning');\n    }\n\n    if (scores.followThrough < 80) {\n      recommendations.push('Focus on complete follow-through for better control');\n    }\n\n    return recommendations;\n  }\n\n  private generateLiveFeedback(pose: TennisPose): string[] {\n    const feedback: string[] = [];\n\n    if (pose.confidence > 0.9) {\n      feedback.push('Excellent pose detection quality');\n    }\n\n    switch (pose.shotPhase) {\n      case 'preparation':\n        feedback.push('Good preparation phase - maintain this position');\n        break;\n      case 'contact':\n        feedback.push('Contact point looks solid');\n        break;\n      case 'follow_through':\n        feedback.push('Nice follow-through extension');\n        break;\n    }\n\n    return feedback;\n  }\n\n  private generateLiveCorrections(pose: TennisPose): string[] {\n    const corrections: string[] = [];\n\n    // Analyze pose for common issues\n    const rightWrist = pose.landmarks.find(l => l.name === 'right_wrist');\n    const rightShoulder = pose.landmarks.find(l => l.name === 'right_shoulder');\n\n    if (rightWrist && rightShoulder && rightWrist.y > rightShoulder.y + 0.2) {\n      corrections.push('Raise your contact point higher');\n    }\n\n    return corrections;\n  }\n\n  private getIdealPose(shotType: string): TennisPose {\n    // Return ideal pose landmarks for comparison\n    return {\n      landmarks: this.generateIdealLandmarks(shotType),\n      timestamp: 0,\n      confidence: 1.0,\n      shotPhase: 'contact',\n      shotType: shotType as TennisPose['shotType'],\n    };\n  }\n\n  private generateIdealLandmarks(shotType: string): PoseLandmark[] {\n    // Generate ideal landmarks based on shot type\n    return this.generateRealisticLandmarks(); // Simplified for now\n  }\n\n  private calculatePoseSimilarity(userPose: TennisPose, idealPose: TennisPose): number {\n    // Calculate similarity between poses\n    let totalDifference = 0;\n    let comparisons = 0;\n\n    userPose.landmarks.forEach(userLandmark => {\n      const idealLandmark = idealPose.landmarks.find(l => l.name === userLandmark.name);\n      if (idealLandmark) {\n        const distance = Math.sqrt(\n          Math.pow(userLandmark.x - idealLandmark.x, 2) +\n          Math.pow(userLandmark.y - idealLandmark.y, 2) +\n          Math.pow(userLandmark.z - idealLandmark.z, 2)\n        );\n        totalDifference += distance;\n        comparisons++;\n      }\n    });\n\n    const avgDifference = totalDifference / comparisons;\n    return Math.max(0, Math.min(100, (1 - avgDifference) * 100));\n  }\n\n  private identifyDifferences(userPose: TennisPose, idealPose: TennisPose): string[] {\n    // Identify key differences between poses\n    return [\n      'Slight difference in racquet angle',\n      'Contact point could be higher',\n      'Body rotation timing',\n    ];\n  }\n\n  private suggestImprovements(differences: string[]): string[] {\n    // Suggest improvements based on differences\n    return differences.map(diff => `Work on: ${diff}`);\n  }\n\n  private generateMockPoses(frameCount: number): TennisPose[] {\n    const poses: TennisPose[] = [];\n    \n    for (let i = 0; i < frameCount; i++) {\n      poses.push({\n        landmarks: this.generateRealisticLandmarks(),\n        timestamp: i * 33.33,\n        confidence: 0.8 + Math.random() * 0.15,\n        shotPhase: ['preparation', 'contact', 'follow_through', 'recovery'][i % 4] as TennisPose['shotPhase'],\n        shotType: 'forehand',\n      });\n    }\n\n    return poses;\n  }\n\n  private getFallbackAnalysis() {\n    return {\n      biomechanics: {\n        kineticChain: {\n          groundForce: 75, legDrive: 70, hipRotation: 80, torsoRotation: 85,\n          shoulderRotation: 78, armExtension: 82, wristSnap: 75\n        },\n        balance: {\n          centerOfGravity: { x: 0.5, y: 0.6 },\n          stability: 80,\n          weightTransfer: 75\n        },\n        timing: {\n          preparationTime: 800, contactTime: 50, followThroughTime: 400, totalTime: 1250\n        }\n      },\n      scores: {\n        overall: 75, preparation: 70, contact: 80, followThrough: 75,\n        footwork: 70, bodyPosition: 78, racquetPath: 80\n      },\n      insights: ['Good overall form', 'Continue working on consistency'],\n      recommendations: ['Practice regularly', 'Focus on fundamentals']\n    };\n  }\n}\n\nexport const poseDetectionService = new PoseDetectionService();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoDMA,oBAAoB;EAAA,SAAAA,qBAAA;IAAAC,eAAA,OAAAD,oBAAA;IAAA,KAChBE,WAAW,IAAAC,cAAA,GAAAC,CAAA,OAAG,KAAK;IAAA,KACnBC,aAAa,IAAAF,cAAA,GAAAC,CAAA,OAAG,CACtB,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,gBAAgB,EACtD,iBAAiB,EAAE,WAAW,EAAE,iBAAiB,EACjD,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EACpD,eAAe,EAAE,gBAAgB,EAAE,YAAY,EAAE,aAAa,EAC9D,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EACxD,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EACxD,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAClD,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,EACtD,iBAAiB,EAAE,kBAAkB,CACtC;EAAA;EAAA,OAAAE,YAAA,CAAAN,oBAAA;IAAAO,GAAA;IAAAC,KAAA;MAAA,IAAAC,gBAAA,GAAAC,iBAAA,CAKD,aAA0C;QAAAP,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QACxC,IAAI;UAAAD,cAAA,GAAAC,CAAA;UAEFQ,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;UAACV,cAAA,GAAAC,CAAA;UAGpD,MAAM,IAAIU,OAAO,CAAC,UAAAC,OAAO,EAAI;YAAAZ,cAAA,GAAAQ,CAAA;YAAAR,cAAA,GAAAC,CAAA;YAAA,OAAAY,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;UAAD,CAAC,CAAC;UAACZ,cAAA,GAAAC,CAAA;UAExD,IAAI,CAACF,WAAW,GAAG,IAAI;UAACC,cAAA,GAAAC,CAAA;UACxBQ,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UAACV,cAAA,GAAAC,CAAA;UACxD,OAAO,IAAI;QACb,CAAC,CAAC,OAAOa,KAAK,EAAE;UAAAd,cAAA,GAAAC,CAAA;UACdQ,OAAO,CAACK,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAACd,cAAA,GAAAC,CAAA;UAC7D,OAAO,KAAK;QACd;MACF,CAAC;MAAA,SAfKc,eAAeA,CAAA;QAAA,OAAAT,gBAAA,CAAAU,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfF,eAAe;IAAA;EAAA;IAAAX,GAAA;IAAAC,KAAA;MAAA,IAAAa,YAAA,GAAAX,iBAAA,CAoBrB,WAAkBY,WAAwB,EAAyB;QAAAnB,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QACjE,IAAI,CAAC,IAAI,CAACF,WAAW,EAAE;UAAAC,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAC,CAAA;UACrB,MAAM,IAAI,CAACc,eAAe,CAAC,CAAC;QAC9B,CAAC;UAAAf,cAAA,GAAAoB,CAAA;QAAA;QAAApB,cAAA,GAAAC,CAAA;QAED,IAAI;UACF,IAAMoB,KAAmB,IAAArB,cAAA,GAAAC,CAAA,QAAG,EAAE;UAACD,cAAA,GAAAC,CAAA;UAE/B,KAAK,IAAIqB,CAAC,IAAAtB,cAAA,GAAAC,CAAA,QAAG,CAAC,GAAEqB,CAAC,GAAGH,WAAW,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;YAC3C,IAAME,KAAK,IAAAxB,cAAA,GAAAC,CAAA,QAAGkB,WAAW,CAACG,CAAC,CAAC;YAC5B,IAAMG,IAAI,IAAAzB,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACyB,iBAAiB,CAACF,KAAK,EAAEF,CAAC,GAAG,KAAK,CAAC;YAACtB,cAAA,GAAAC,CAAA;YAC5D,IAAIwB,IAAI,EAAE;cAAAzB,cAAA,GAAAoB,CAAA;cAAApB,cAAA,GAAAC,CAAA;cACRoB,KAAK,CAACM,IAAI,CAACF,IAAI,CAAC;YAClB,CAAC;cAAAzB,cAAA,GAAAoB,CAAA;YAAA;UACH;UAACpB,cAAA,GAAAC,CAAA;UAED,OAAOoB,KAAK;QACd,CAAC,CAAC,OAAOP,KAAK,EAAE;UAAAd,cAAA,GAAAC,CAAA;UACdQ,OAAO,CAACK,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAACd,cAAA,GAAAC,CAAA;UAC9C,OAAO,IAAI,CAAC2B,iBAAiB,CAACT,WAAW,CAACI,MAAM,CAAC;QACnD;MACF,CAAC;MAAA,SArBKM,WAAWA,CAAAC,EAAA;QAAA,OAAAZ,YAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAXY,WAAW;IAAA;EAAA;IAAAzB,GAAA;IAAAC,KAAA,EA0BjB,SAAA0B,gBAAgBA,CAACV,KAAmB,EAKlC;MAAArB,cAAA,GAAAQ,CAAA;MAAAR,cAAA,GAAAC,CAAA;MACA,IAAI;QACF,IAAM+B,YAAY,IAAAhC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACgC,mBAAmB,CAACZ,KAAK,CAAC;QACpD,IAAMa,MAAM,IAAAlC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACkC,wBAAwB,CAACd,KAAK,EAAEW,YAAY,CAAC;QACjE,IAAMI,QAAQ,IAAApC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACoC,gBAAgB,CAAChB,KAAK,EAAEW,YAAY,EAAEE,MAAM,CAAC;QACnE,IAAMI,eAAe,IAAAtC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACsC,uBAAuB,CAACL,MAAM,EAAEE,QAAQ,CAAC;QAACpC,cAAA,GAAAC,CAAA;QAEvE,OAAO;UACL+B,YAAY,EAAZA,YAAY;UACZE,MAAM,EAANA,MAAM;UACNE,QAAQ,EAARA,QAAQ;UACRE,eAAe,EAAfA;QACF,CAAC;MACH,CAAC,CAAC,OAAOxB,KAAK,EAAE;QAAAd,cAAA,GAAAC,CAAA;QACdQ,OAAO,CAACK,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QAACd,cAAA,GAAAC,CAAA;QAClD,OAAO,IAAI,CAACuC,mBAAmB,CAAC,CAAC;MACnC;IACF;EAAC;IAAApC,GAAA;IAAAC,KAAA;MAAA,IAAAoC,gBAAA,GAAAlC,iBAAA,CAKD,WAAsBmC,SAAoB,EAIvC;QAAA1C,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QACD,IAAI;UACF,IAAMwB,IAAI,IAAAzB,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACyB,iBAAiB,CAACgB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;UAAC5C,cAAA,GAAAC,CAAA;UAEjE,IAAI,CAACwB,IAAI,EAAE;YAAAzB,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YACT,OAAO;cACLwB,IAAI,EAAE,IAAI;cACVoB,QAAQ,EAAE,CAAC,+BAA+B,CAAC;cAC3CC,WAAW,EAAE,CAAC,qCAAqC;YACrD,CAAC;UACH,CAAC;YAAA9C,cAAA,GAAAoB,CAAA;UAAA;UAED,IAAMyB,QAAQ,IAAA7C,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC8C,oBAAoB,CAACtB,IAAI,CAAC;UAChD,IAAMqB,WAAW,IAAA9C,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC+C,uBAAuB,CAACvB,IAAI,CAAC;UAACzB,cAAA,GAAAC,CAAA;UAEvD,OAAO;YACLwB,IAAI,EAAJA,IAAI;YACJoB,QAAQ,EAARA,QAAQ;YACRC,WAAW,EAAXA;UACF,CAAC;QACH,CAAC,CAAC,OAAOhC,KAAK,EAAE;UAAAd,cAAA,GAAAC,CAAA;UACdQ,OAAO,CAACK,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UAACd,cAAA,GAAAC,CAAA;UAClD,OAAO;YACLwB,IAAI,EAAE,IAAI;YACVoB,QAAQ,EAAE,CAAC,kCAAkC,CAAC;YAC9CC,WAAW,EAAE,CAAC,qBAAqB;UACrC,CAAC;QACH;MACF,CAAC;MAAA,SAhCKG,eAAeA,CAAAC,GAAA;QAAA,OAAAT,gBAAA,CAAAzB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfgC,eAAe;IAAA;EAAA;IAAA7C,GAAA;IAAAC,KAAA,EAqCrB,SAAA8C,gBAAgBA,CAACC,QAAoB,EAAEC,QAAgB,EAIrD;MAAArD,cAAA,GAAAQ,CAAA;MACA,IAAM8C,SAAS,IAAAtD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACsD,YAAY,CAACF,QAAQ,CAAC;MAC7C,IAAMG,UAAU,IAAAxD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACwD,uBAAuB,CAACL,QAAQ,EAAEE,SAAS,CAAC;MACpE,IAAMI,WAAW,IAAA1D,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC0D,mBAAmB,CAACP,QAAQ,EAAEE,SAAS,CAAC;MACjE,IAAMM,YAAY,IAAA5D,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC4D,mBAAmB,CAACH,WAAW,CAAC;MAAC1D,cAAA,GAAAC,CAAA;MAE3D,OAAO;QACLuD,UAAU,EAAVA,UAAU;QACVE,WAAW,EAAXA,WAAW;QACXE,YAAY,EAAZA;MACF,CAAC;IACH;EAAC;IAAAxD,GAAA;IAAAC,KAAA;MAAA,IAAAyD,kBAAA,GAAAvD,iBAAA,CAID,WAAgCmC,SAAoB,EAAEqB,SAAiB,EAA8B;QAAA/D,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAEnG,MAAM,IAAIU,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAAZ,cAAA,GAAAQ,CAAA;UAAAR,cAAA,GAAAC,CAAA;UAAA,OAAAY,UAAU,CAACD,OAAO,EAAE,EAAE,CAAC;QAAD,CAAC,CAAC;QAGrD,IAAMoD,SAAS,IAAAhE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACgE,0BAA0B,CAAC,CAAC;QACnD,IAAMZ,QAAQ,IAAArD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACiE,cAAc,CAACF,SAAS,CAAC;QAC/C,IAAMG,SAAS,IAAAnE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACmE,eAAe,CAACJ,SAAS,EAAED,SAAS,CAAC;QAAC/D,cAAA,GAAAC,CAAA;QAE7D,OAAO;UACL+D,SAAS,EAATA,SAAS;UACTD,SAAS,EAATA,SAAS;UACTM,UAAU,EAAE,IAAI,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACtCJ,SAAS,EAATA,SAAS;UACTd,QAAQ,EAARA;QACF,CAAC;MACH,CAAC;MAAA,SAhBa3B,iBAAiBA,CAAA8C,GAAA,EAAAC,GAAA;QAAA,OAAAX,kBAAA,CAAA9C,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBS,iBAAiB;IAAA;EAAA;IAAAtB,GAAA;IAAAC,KAAA,EAkB/B,SAAQ4D,0BAA0BA,CAAA,EAAmB;MAAAjE,cAAA,GAAAQ,CAAA;MAAAR,cAAA,GAAAC,CAAA;MACnD,OAAO,IAAI,CAACC,aAAa,CAACwE,GAAG,CAAC,UAACC,IAAI,EAAEC,KAAK,EAAK;QAAA5E,cAAA,GAAAQ,CAAA;QAE7C,IAAIqE,CAAC,EAAEC,CAAC,EAAEC,CAAC;QAAC/E,cAAA,GAAAC,CAAA;QAEZ,QAAQ0E,IAAI;UACV,KAAK,MAAM;YAAA3E,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YACT4E,CAAC,GAAG,GAAG;YAAC7E,cAAA,GAAAC,CAAA;YAAC6E,CAAC,GAAG,GAAG;YAAC9E,cAAA,GAAAC,CAAA;YAAC8E,CAAC,GAAG,CAAC;YAAC/E,cAAA,GAAAC,CAAA;YACxB;UACF,KAAK,eAAe;YAAAD,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YAClB4E,CAAC,GAAG,GAAG;YAAC7E,cAAA,GAAAC,CAAA;YAAC6E,CAAC,GAAG,IAAI;YAAC9E,cAAA,GAAAC,CAAA;YAAC8E,CAAC,GAAG,CAAC,GAAG;YAAC/E,cAAA,GAAAC,CAAA;YAC5B;UACF,KAAK,gBAAgB;YAAAD,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YACnB4E,CAAC,GAAG,GAAG;YAAC7E,cAAA,GAAAC,CAAA;YAAC6E,CAAC,GAAG,IAAI;YAAC9E,cAAA,GAAAC,CAAA;YAAC8E,CAAC,GAAG,CAAC,GAAG;YAAC/E,cAAA,GAAAC,CAAA;YAC5B;UACF,KAAK,YAAY;YAAAD,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YACf4E,CAAC,GAAG,GAAG,GAAGP,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;YAACvE,cAAA,GAAAC,CAAA;YAAC6E,CAAC,GAAG,GAAG,GAAGR,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;YAACvE,cAAA,GAAAC,CAAA;YAAC8E,CAAC,GAAG,CAAC,GAAG;YAAC/E,cAAA,GAAAC,CAAA;YACvE;UACF,KAAK,aAAa;YAAAD,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YAChB4E,CAAC,GAAG,GAAG,GAAGP,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;YAACvE,cAAA,GAAAC,CAAA;YAAC6E,CAAC,GAAG,GAAG,GAAGR,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;YAACvE,cAAA,GAAAC,CAAA;YAAC8E,CAAC,GAAG,CAAC,GAAG;YAAC/E,cAAA,GAAAC,CAAA;YACvE;UACF,KAAK,UAAU;YAAAD,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YACb4E,CAAC,GAAG,IAAI;YAAC7E,cAAA,GAAAC,CAAA;YAAC6E,CAAC,GAAG,GAAG;YAAC9E,cAAA,GAAAC,CAAA;YAAC8E,CAAC,GAAG,CAAC;YAAC/E,cAAA,GAAAC,CAAA;YACzB;UACF,KAAK,WAAW;YAAAD,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YACd4E,CAAC,GAAG,IAAI;YAAC7E,cAAA,GAAAC,CAAA;YAAC6E,CAAC,GAAG,GAAG;YAAC9E,cAAA,GAAAC,CAAA;YAAC8E,CAAC,GAAG,CAAC;YAAC/E,cAAA,GAAAC,CAAA;YACzB;UACF,KAAK,WAAW;YAAAD,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YACd4E,CAAC,GAAG,IAAI;YAAC7E,cAAA,GAAAC,CAAA;YAAC6E,CAAC,GAAG,GAAG;YAAC9E,cAAA,GAAAC,CAAA;YAAC8E,CAAC,GAAG,GAAG;YAAC/E,cAAA,GAAAC,CAAA;YAC3B;UACF,KAAK,YAAY;YAAAD,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YACf4E,CAAC,GAAG,IAAI;YAAC7E,cAAA,GAAAC,CAAA;YAAC6E,CAAC,GAAG,GAAG;YAAC9E,cAAA,GAAAC,CAAA;YAAC8E,CAAC,GAAG,GAAG;YAAC/E,cAAA,GAAAC,CAAA;YAC3B;UACF;YAAAD,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YACE4E,CAAC,GAAG,GAAG,GAAG,CAACP,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;YAACvE,cAAA,GAAAC,CAAA;YACtC6E,CAAC,GAAG,GAAG,GAAG,CAACR,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;YAACvE,cAAA,GAAAC,CAAA;YACtC8E,CAAC,GAAG,CAACT,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACnC;QAACvE,cAAA,GAAAC,CAAA;QAED,OAAO;UACL4E,CAAC,EAADA,CAAC;UACDC,CAAC,EAADA,CAAC;UACDC,CAAC,EAADA,CAAC;UACDC,UAAU,EAAE,GAAG,GAAGV,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACrCI,IAAI,EAAJA;QACF,CAAC;MACH,CAAC,CAAC;IACJ;EAAC;IAAAvE,GAAA;IAAAC,KAAA,EAED,SAAQ6D,cAAcA,CAACF,SAAyB,EAA0B;MAAAhE,cAAA,GAAAQ,CAAA;MACxE,IAAMyE,UAAU,IAAAjF,cAAA,GAAAC,CAAA,QAAG+D,SAAS,CAACkB,IAAI,CAAC,UAAAC,CAAC,EAAI;QAAAnF,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAAA,OAAAkF,CAAC,CAACR,IAAI,KAAK,aAAa;MAAD,CAAC,CAAC;MAChE,IAAMS,SAAS,IAAApF,cAAA,GAAAC,CAAA,QAAG+D,SAAS,CAACkB,IAAI,CAAC,UAAAC,CAAC,EAAI;QAAAnF,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAAA,OAAAkF,CAAC,CAACR,IAAI,KAAK,YAAY;MAAD,CAAC,CAAC;MAC9D,IAAMU,aAAa,IAAArF,cAAA,GAAAC,CAAA,QAAG+D,SAAS,CAACkB,IAAI,CAAC,UAAAC,CAAC,EAAI;QAAAnF,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAAA,OAAAkF,CAAC,CAACR,IAAI,KAAK,gBAAgB;MAAD,CAAC,CAAC;MAAC3E,cAAA,GAAAC,CAAA;MAEvE,IAAI,CAAAD,cAAA,GAAAoB,CAAA,WAAC6D,UAAU,MAAAjF,cAAA,GAAAoB,CAAA,UAAI,CAACgE,SAAS,MAAApF,cAAA,GAAAoB,CAAA,UAAI,CAACiE,aAAa,GAAE;QAAArF,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAC/C,OAAO,SAAS;MAClB,CAAC;QAAAD,cAAA,GAAAoB,CAAA;MAAA;MAGD,IAAMkE,SAAS,IAAAtF,cAAA,GAAAC,CAAA,SAAGgF,UAAU,CAACH,CAAC;MAC9B,IAAMS,YAAY,IAAAvF,cAAA,GAAAC,CAAA,SAAGqE,IAAI,CAACkB,GAAG,CAACP,UAAU,CAACJ,CAAC,GAAGQ,aAAa,CAACR,CAAC,CAAC;MAAC7E,cAAA,GAAAC,CAAA;MAE9D,IAAIqF,SAAS,GAAG,GAAG,EAAE;QAAAtF,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAO,OAAO;MAAA,CAAC;QAAAD,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MACpC,IAAIqF,SAAS,GAAG,GAAG,EAAE;QAAAtF,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAO,UAAU;MAAA,CAAC;QAAAD,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MACvC,IAAIsF,YAAY,GAAG,IAAI,EAAE;QAAAvF,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAO,QAAQ;MAAA,CAAC;QAAAD,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MACzC,IAAIgF,UAAU,CAACJ,CAAC,GAAGQ,aAAa,CAACR,CAAC,EAAE;QAAA7E,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAO,UAAU;MAAA,CAAC;QAAAD,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MACtD,OAAO,UAAU;IACnB;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAED,SAAQ+D,eAAeA,CAACJ,SAAyB,EAAED,SAAiB,EAA2B;MAAA/D,cAAA,GAAAQ,CAAA;MAE7F,IAAMiF,KAAK,IAAAzF,cAAA,GAAAC,CAAA,SAAGqE,IAAI,CAACoB,KAAK,CAAE3B,SAAS,GAAG,IAAI,GAAI,CAAC,CAAC;MAChD,IAAM4B,MAAiC,IAAA3F,cAAA,GAAAC,CAAA,SAAG,CAAC,aAAa,EAAE,SAAS,EAAE,gBAAgB,EAAE,UAAU,CAAC;MAACD,cAAA,GAAAC,CAAA;MACnG,OAAO0F,MAAM,CAACF,KAAK,CAAC;IACtB;EAAC;IAAArF,GAAA;IAAAC,KAAA,EAED,SAAQ4B,mBAAmBA,CAACZ,KAAmB,EAAyB;MAAArB,cAAA,GAAAQ,CAAA;MAEtE,IAAMoF,YAAY,IAAA5F,cAAA,GAAAC,CAAA,SAAG;QACnB4F,WAAW,EAAE,EAAE,GAAGvB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;QACpCuB,QAAQ,EAAE,EAAE,GAAGxB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;QACjCwB,WAAW,EAAE,EAAE,GAAGzB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;QACpCyB,aAAa,EAAE,EAAE,GAAG1B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;QACtC0B,gBAAgB,EAAE,EAAE,GAAG3B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;QACzCgB,YAAY,EAAE,EAAE,GAAGjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;QACrC2B,SAAS,EAAE,EAAE,GAAG5B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;MAClC,CAAC;MAGD,IAAM4B,OAAO,IAAAnG,cAAA,GAAAC,CAAA,SAAG;QACdmG,eAAe,EAAE;UAAEvB,CAAC,EAAE,GAAG;UAAEC,CAAC,EAAE;QAAI,CAAC;QACnCuB,SAAS,EAAE,EAAE,GAAG/B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;QAClC+B,cAAc,EAAE,EAAE,GAAGhC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;MACvC,CAAC;MAGD,IAAMgC,MAAM,IAAAvG,cAAA,GAAAC,CAAA,SAAG;QACbuG,eAAe,EAAE,GAAG,GAAGlC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;QAC1CkC,WAAW,EAAE,EAAE,GAAGnC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;QACpCmC,iBAAiB,EAAE,GAAG,GAAGpC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;QAC5CoC,SAAS,EAAE,IAAI,GAAGrC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;MACpC,CAAC;MAACvE,cAAA,GAAAC,CAAA;MAEF,OAAO;QACL2F,YAAY,EAAZA,YAAY;QACZO,OAAO,EAAPA,OAAO;QACPI,MAAM,EAANA;MACF,CAAC;IACH;EAAC;IAAAnG,GAAA;IAAAC,KAAA,EAED,SAAQ8B,wBAAwBA,CAACd,KAAmB,EAAEW,YAAmC,EAAmB;MAAAhC,cAAA,GAAAQ,CAAA;MAC1G,IAAMoG,gBAAgB,IAAA5G,cAAA,GAAAC,CAAA,SAAGoB,KAAK,CAACwF,MAAM,CAAC,UAAAC,CAAC,EAAI;QAAA9G,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAAA,OAAA6G,CAAC,CAAC3C,SAAS,KAAK,aAAa;MAAD,CAAC,CAAC;MACzE,IAAM4C,YAAY,IAAA/G,cAAA,GAAAC,CAAA,SAAGoB,KAAK,CAACwF,MAAM,CAAC,UAAAC,CAAC,EAAI;QAAA9G,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAAA,OAAA6G,CAAC,CAAC3C,SAAS,KAAK,SAAS;MAAD,CAAC,CAAC;MACjE,IAAM6C,kBAAkB,IAAAhH,cAAA,GAAAC,CAAA,SAAGoB,KAAK,CAACwF,MAAM,CAAC,UAAAC,CAAC,EAAI;QAAA9G,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAAA,OAAA6G,CAAC,CAAC3C,SAAS,KAAK,gBAAgB;MAAD,CAAC,CAAC;MAE9E,IAAM8C,WAAW,IAAAjH,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACiH,cAAc,CAACN,gBAAgB,CAAC;MACzD,IAAMO,OAAO,IAAAnH,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACiH,cAAc,CAACH,YAAY,CAAC;MACjD,IAAMK,aAAa,IAAApH,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACiH,cAAc,CAACF,kBAAkB,CAAC;MAC7D,IAAMK,QAAQ,IAAArH,cAAA,GAAAC,CAAA,SAAG+B,YAAY,CAACmE,OAAO,CAACE,SAAS;MAC/C,IAAMiB,YAAY,IAAAtH,cAAA,GAAAC,CAAA,SAAG,CAAC+B,YAAY,CAAC4D,YAAY,CAACG,WAAW,GAAG/D,YAAY,CAAC4D,YAAY,CAACI,aAAa,IAAI,CAAC;MAC1G,IAAMuB,WAAW,IAAAvH,cAAA,GAAAC,CAAA,SAAG,CAAC+B,YAAY,CAAC4D,YAAY,CAACL,YAAY,GAAGvD,YAAY,CAAC4D,YAAY,CAACM,SAAS,IAAI,CAAC;MAEtG,IAAMsB,OAAO,IAAAxH,cAAA,GAAAC,CAAA,SAAG,CAACgH,WAAW,GAAGE,OAAO,GAAGC,aAAa,GAAGC,QAAQ,GAAGC,YAAY,GAAGC,WAAW,IAAI,CAAC;MAACvH,cAAA,GAAAC,CAAA;MAEpG,OAAO;QACLuH,OAAO,EAAElD,IAAI,CAACmD,KAAK,CAACD,OAAO,CAAC;QAC5BP,WAAW,EAAE3C,IAAI,CAACmD,KAAK,CAACR,WAAW,CAAC;QACpCE,OAAO,EAAE7C,IAAI,CAACmD,KAAK,CAACN,OAAO,CAAC;QAC5BC,aAAa,EAAE9C,IAAI,CAACmD,KAAK,CAACL,aAAa,CAAC;QACxCC,QAAQ,EAAE/C,IAAI,CAACmD,KAAK,CAACJ,QAAQ,CAAC;QAC9BC,YAAY,EAAEhD,IAAI,CAACmD,KAAK,CAACH,YAAY,CAAC;QACtCC,WAAW,EAAEjD,IAAI,CAACmD,KAAK,CAACF,WAAW;MACrC,CAAC;IACH;EAAC;IAAAnH,GAAA;IAAAC,KAAA,EAED,SAAQ6G,cAAcA,CAAC7F,KAAmB,EAAU;MAAArB,cAAA,GAAAQ,CAAA;MAAAR,cAAA,GAAAC,CAAA;MAClD,IAAIoB,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;QAAAvB,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAO,EAAE;MAAA,CAAC;QAAAD,cAAA,GAAAoB,CAAA;MAAA;MAElC,IAAMsG,aAAa,IAAA1H,cAAA,GAAAC,CAAA,SAAGoB,KAAK,CAACsG,MAAM,CAAC,UAACC,GAAG,EAAEnG,IAAI,EAAK;QAAAzB,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAAA,OAAA2H,GAAG,GAAGnG,IAAI,CAAC4C,UAAU;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGhD,KAAK,CAACE,MAAM;MAACvB,cAAA,GAAAC,CAAA;MAC3F,OAAQyH,aAAa,GAAG,GAAG,GAAIpD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;IACnD;EAAC;IAAAnE,GAAA;IAAAC,KAAA,EAED,SAAQgC,gBAAgBA,CAAChB,KAAmB,EAAEW,YAAmC,EAAEE,MAAuB,EAAY;MAAAlC,cAAA,GAAAQ,CAAA;MACpH,IAAM4B,QAAkB,IAAApC,cAAA,GAAAC,CAAA,SAAG,EAAE;MAACD,cAAA,GAAAC,CAAA;MAE9B,IAAIiC,MAAM,CAACsF,OAAO,GAAG,EAAE,EAAE;QAAAxH,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QACvBmC,QAAQ,CAACT,IAAI,CAAC,0CAA0C,CAAC;MAC3D,CAAC;QAAA3B,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MAED,IAAI+B,YAAY,CAAC4D,YAAY,CAACE,QAAQ,GAAG,EAAE,EAAE;QAAA9F,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAC3CmC,QAAQ,CAACT,IAAI,CAAC,qDAAqD,CAAC;MACtE,CAAC;QAAA3B,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MAED,IAAI+B,YAAY,CAACmE,OAAO,CAACE,SAAS,GAAG,EAAE,EAAE;QAAArG,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QACvCmC,QAAQ,CAACT,IAAI,CAAC,qDAAqD,CAAC;MACtE,CAAC;QAAA3B,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MAED,IAAIiC,MAAM,CAACiF,OAAO,GAAG,EAAE,EAAE;QAAAnH,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QACvBmC,QAAQ,CAACT,IAAI,CAAC,uCAAuC,CAAC;MACxD,CAAC;QAAA3B,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MAED,OAAOmC,QAAQ;IACjB;EAAC;IAAAhC,GAAA;IAAAC,KAAA,EAED,SAAQkC,uBAAuBA,CAACL,MAAuB,EAAEE,QAAkB,EAAY;MAAApC,cAAA,GAAAQ,CAAA;MACrF,IAAM8B,eAAyB,IAAAtC,cAAA,GAAAC,CAAA,SAAG,EAAE;MAACD,cAAA,GAAAC,CAAA;MAErC,IAAIiC,MAAM,CAAC+E,WAAW,GAAG,EAAE,EAAE;QAAAjH,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAC3BqC,eAAe,CAACX,IAAI,CAAC,iDAAiD,CAAC;MACzE,CAAC;QAAA3B,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MAED,IAAIiC,MAAM,CAACmF,QAAQ,GAAG,EAAE,EAAE;QAAArH,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QACxBqC,eAAe,CAACX,IAAI,CAAC,uDAAuD,CAAC;MAC/E,CAAC;QAAA3B,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MAED,IAAIiC,MAAM,CAACkF,aAAa,GAAG,EAAE,EAAE;QAAApH,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAC7BqC,eAAe,CAACX,IAAI,CAAC,qDAAqD,CAAC;MAC7E,CAAC;QAAA3B,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MAED,OAAOqC,eAAe;IACxB;EAAC;IAAAlC,GAAA;IAAAC,KAAA,EAED,SAAQ0C,oBAAoBA,CAACtB,IAAgB,EAAY;MAAAzB,cAAA,GAAAQ,CAAA;MACvD,IAAMqC,QAAkB,IAAA7C,cAAA,GAAAC,CAAA,SAAG,EAAE;MAACD,cAAA,GAAAC,CAAA;MAE9B,IAAIwB,IAAI,CAAC4C,UAAU,GAAG,GAAG,EAAE;QAAArE,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QACzB4C,QAAQ,CAAClB,IAAI,CAAC,kCAAkC,CAAC;MACnD,CAAC;QAAA3B,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MAED,QAAQwB,IAAI,CAAC0C,SAAS;QACpB,KAAK,aAAa;UAAAnE,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAC,CAAA;UAChB4C,QAAQ,CAAClB,IAAI,CAAC,iDAAiD,CAAC;UAAC3B,cAAA,GAAAC,CAAA;UACjE;QACF,KAAK,SAAS;UAAAD,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAC,CAAA;UACZ4C,QAAQ,CAAClB,IAAI,CAAC,2BAA2B,CAAC;UAAC3B,cAAA,GAAAC,CAAA;UAC3C;QACF,KAAK,gBAAgB;UAAAD,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAC,CAAA;UACnB4C,QAAQ,CAAClB,IAAI,CAAC,+BAA+B,CAAC;UAAC3B,cAAA,GAAAC,CAAA;UAC/C;MACJ;MAACD,cAAA,GAAAC,CAAA;MAED,OAAO4C,QAAQ;IACjB;EAAC;IAAAzC,GAAA;IAAAC,KAAA,EAED,SAAQ2C,uBAAuBA,CAACvB,IAAgB,EAAY;MAAAzB,cAAA,GAAAQ,CAAA;MAC1D,IAAMsC,WAAqB,IAAA9C,cAAA,GAAAC,CAAA,SAAG,EAAE;MAGhC,IAAMgF,UAAU,IAAAjF,cAAA,GAAAC,CAAA,SAAGwB,IAAI,CAACuC,SAAS,CAACkB,IAAI,CAAC,UAAAC,CAAC,EAAI;QAAAnF,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAAA,OAAAkF,CAAC,CAACR,IAAI,KAAK,aAAa;MAAD,CAAC,CAAC;MACrE,IAAMU,aAAa,IAAArF,cAAA,GAAAC,CAAA,SAAGwB,IAAI,CAACuC,SAAS,CAACkB,IAAI,CAAC,UAAAC,CAAC,EAAI;QAAAnF,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAAA,OAAAkF,CAAC,CAACR,IAAI,KAAK,gBAAgB;MAAD,CAAC,CAAC;MAAC3E,cAAA,GAAAC,CAAA;MAE5E,IAAI,CAAAD,cAAA,GAAAoB,CAAA,WAAA6D,UAAU,MAAAjF,cAAA,GAAAoB,CAAA,WAAIiE,aAAa,MAAArF,cAAA,GAAAoB,CAAA,WAAI6D,UAAU,CAACH,CAAC,GAAGO,aAAa,CAACP,CAAC,GAAG,GAAG,GAAE;QAAA9E,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QACvE6C,WAAW,CAACnB,IAAI,CAAC,iCAAiC,CAAC;MACrD,CAAC;QAAA3B,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAC,CAAA;MAED,OAAO6C,WAAW;IACpB;EAAC;IAAA1C,GAAA;IAAAC,KAAA,EAED,SAAQkD,YAAYA,CAACF,QAAgB,EAAc;MAAArD,cAAA,GAAAQ,CAAA;MAAAR,cAAA,GAAAC,CAAA;MAEjD,OAAO;QACL+D,SAAS,EAAE,IAAI,CAAC6D,sBAAsB,CAACxE,QAAQ,CAAC;QAChDU,SAAS,EAAE,CAAC;QACZM,UAAU,EAAE,GAAG;QACfF,SAAS,EAAE,SAAS;QACpBd,QAAQ,EAAEA;MACZ,CAAC;IACH;EAAC;IAAAjD,GAAA;IAAAC,KAAA,EAED,SAAQwH,sBAAsBA,CAACxE,QAAgB,EAAkB;MAAArD,cAAA,GAAAQ,CAAA;MAAAR,cAAA,GAAAC,CAAA;MAE/D,OAAO,IAAI,CAACgE,0BAA0B,CAAC,CAAC;IAC1C;EAAC;IAAA7D,GAAA;IAAAC,KAAA,EAED,SAAQoD,uBAAuBA,CAACL,QAAoB,EAAEE,SAAqB,EAAU;MAAAtD,cAAA,GAAAQ,CAAA;MAEnF,IAAIsH,eAAe,IAAA9H,cAAA,GAAAC,CAAA,SAAG,CAAC;MACvB,IAAI8H,WAAW,IAAA/H,cAAA,GAAAC,CAAA,SAAG,CAAC;MAACD,cAAA,GAAAC,CAAA;MAEpBmD,QAAQ,CAACY,SAAS,CAACgE,OAAO,CAAC,UAAAC,YAAY,EAAI;QAAAjI,cAAA,GAAAQ,CAAA;QACzC,IAAM0H,aAAa,IAAAlI,cAAA,GAAAC,CAAA,SAAGqD,SAAS,CAACU,SAAS,CAACkB,IAAI,CAAC,UAAAC,CAAC,EAAI;UAAAnF,cAAA,GAAAQ,CAAA;UAAAR,cAAA,GAAAC,CAAA;UAAA,OAAAkF,CAAC,CAACR,IAAI,KAAKsD,YAAY,CAACtD,IAAI;QAAD,CAAC,CAAC;QAAC3E,cAAA,GAAAC,CAAA;QAClF,IAAIiI,aAAa,EAAE;UAAAlI,cAAA,GAAAoB,CAAA;UACjB,IAAM+G,QAAQ,IAAAnI,cAAA,GAAAC,CAAA,SAAGqE,IAAI,CAAC8D,IAAI,CACxB9D,IAAI,CAAC+D,GAAG,CAACJ,YAAY,CAACpD,CAAC,GAAGqD,aAAa,CAACrD,CAAC,EAAE,CAAC,CAAC,GAC7CP,IAAI,CAAC+D,GAAG,CAACJ,YAAY,CAACnD,CAAC,GAAGoD,aAAa,CAACpD,CAAC,EAAE,CAAC,CAAC,GAC7CR,IAAI,CAAC+D,GAAG,CAACJ,YAAY,CAAClD,CAAC,GAAGmD,aAAa,CAACnD,CAAC,EAAE,CAAC,CAC9C,CAAC;UAAC/E,cAAA,GAAAC,CAAA;UACF6H,eAAe,IAAIK,QAAQ;UAACnI,cAAA,GAAAC,CAAA;UAC5B8H,WAAW,EAAE;QACf,CAAC;UAAA/H,cAAA,GAAAoB,CAAA;QAAA;MACH,CAAC,CAAC;MAEF,IAAMkH,aAAa,IAAAtI,cAAA,GAAAC,CAAA,SAAG6H,eAAe,GAAGC,WAAW;MAAC/H,cAAA,GAAAC,CAAA;MACpD,OAAOqE,IAAI,CAACiE,GAAG,CAAC,CAAC,EAAEjE,IAAI,CAACkE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GAAGF,aAAa,IAAI,GAAG,CAAC,CAAC;IAC9D;EAAC;IAAAlI,GAAA;IAAAC,KAAA,EAED,SAAQsD,mBAAmBA,CAACP,QAAoB,EAAEE,SAAqB,EAAY;MAAAtD,cAAA,GAAAQ,CAAA;MAAAR,cAAA,GAAAC,CAAA;MAEjF,OAAO,CACL,oCAAoC,EACpC,+BAA+B,EAC/B,sBAAsB,CACvB;IACH;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAED,SAAQwD,mBAAmBA,CAACH,WAAqB,EAAY;MAAA1D,cAAA,GAAAQ,CAAA;MAAAR,cAAA,GAAAC,CAAA;MAE3D,OAAOyD,WAAW,CAACgB,GAAG,CAAC,UAAA+D,IAAI,EAAI;QAAAzI,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAAA,mBAAYwI,IAAI,EAAE;MAAD,CAAC,CAAC;IACpD;EAAC;IAAArI,GAAA;IAAAC,KAAA,EAED,SAAQuB,iBAAiBA,CAAC8G,UAAkB,EAAgB;MAAA1I,cAAA,GAAAQ,CAAA;MAC1D,IAAMa,KAAmB,IAAArB,cAAA,GAAAC,CAAA,SAAG,EAAE;MAACD,cAAA,GAAAC,CAAA;MAE/B,KAAK,IAAIqB,CAAC,IAAAtB,cAAA,GAAAC,CAAA,SAAG,CAAC,GAAEqB,CAAC,GAAGoH,UAAU,EAAEpH,CAAC,EAAE,EAAE;QAAAtB,cAAA,GAAAC,CAAA;QACnCoB,KAAK,CAACM,IAAI,CAAC;UACTqC,SAAS,EAAE,IAAI,CAACC,0BAA0B,CAAC,CAAC;UAC5CF,SAAS,EAAEzC,CAAC,GAAG,KAAK;UACpB+C,UAAU,EAAE,GAAG,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI;UACtCJ,SAAS,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,gBAAgB,EAAE,UAAU,CAAC,CAAC7C,CAAC,GAAG,CAAC,CAA4B;UACrG+B,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;MAACrD,cAAA,GAAAC,CAAA;MAED,OAAOoB,KAAK;IACd;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAED,SAAQmC,mBAAmBA,CAAA,EAAG;MAAAxC,cAAA,GAAAQ,CAAA;MAAAR,cAAA,GAAAC,CAAA;MAC5B,OAAO;QACL+B,YAAY,EAAE;UACZ4D,YAAY,EAAE;YACZC,WAAW,EAAE,EAAE;YAAEC,QAAQ,EAAE,EAAE;YAAEC,WAAW,EAAE,EAAE;YAAEC,aAAa,EAAE,EAAE;YACjEC,gBAAgB,EAAE,EAAE;YAAEV,YAAY,EAAE,EAAE;YAAEW,SAAS,EAAE;UACrD,CAAC;UACDC,OAAO,EAAE;YACPC,eAAe,EAAE;cAAEvB,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YACnCuB,SAAS,EAAE,EAAE;YACbC,cAAc,EAAE;UAClB,CAAC;UACDC,MAAM,EAAE;YACNC,eAAe,EAAE,GAAG;YAAEC,WAAW,EAAE,EAAE;YAAEC,iBAAiB,EAAE,GAAG;YAAEC,SAAS,EAAE;UAC5E;QACF,CAAC;QACDzE,MAAM,EAAE;UACNsF,OAAO,EAAE,EAAE;UAAEP,WAAW,EAAE,EAAE;UAAEE,OAAO,EAAE,EAAE;UAAEC,aAAa,EAAE,EAAE;UAC5DC,QAAQ,EAAE,EAAE;UAAEC,YAAY,EAAE,EAAE;UAAEC,WAAW,EAAE;QAC/C,CAAC;QACDnF,QAAQ,EAAE,CAAC,mBAAmB,EAAE,iCAAiC,CAAC;QAClEE,eAAe,EAAE,CAAC,oBAAoB,EAAE,uBAAuB;MACjE,CAAC;IACH;EAAC;AAAA;AAGH,OAAO,IAAMqG,oBAAoB,IAAA3I,cAAA,GAAAC,CAAA,SAAG,IAAIJ,oBAAoB,CAAC,CAAC", "ignoreList": []}