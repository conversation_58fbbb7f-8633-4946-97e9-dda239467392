{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_AccessibilityUtil", "_createDOMProps", "_react", "_useLocale", "createElement", "component", "props", "options", "accessibilityComponent", "constructor", "String", "propsToAccessibilityComponent", "Component", "domProps", "element", "elementWithLocaleProvider", "dir", "LocaleProvider", "children", "direction", "locale", "lang", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _AccessibilityUtil = _interopRequireDefault(require(\"../../modules/AccessibilityUtil\"));\nvar _createDOMProps = _interopRequireDefault(require(\"../../modules/createDOMProps\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _useLocale = require(\"../../modules/useLocale\");\nvar createElement = (component, props, options) => {\n  // Use equivalent platform elements where possible.\n  var accessibilityComponent;\n  if (component && component.constructor === String) {\n    accessibilityComponent = _AccessibilityUtil.default.propsToAccessibilityComponent(props);\n  }\n  var Component = accessibilityComponent || component;\n  var domProps = (0, _createDOMProps.default)(Component, props, options);\n  var element = /*#__PURE__*/_react.default.createElement(Component, domProps);\n\n  // Update locale context if element's writing direction prop changes\n  var elementWithLocaleProvider = domProps.dir ? /*#__PURE__*/_react.default.createElement(_useLocale.LocaleProvider, {\n    children: element,\n    direction: domProps.dir,\n    locale: domProps.lang\n  }) : element;\n  return elementWithLocaleProvider;\n};\nvar _default = exports.default = createElement;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAUZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,kBAAkB,GAAGL,sBAAsB,CAACC,OAAO,kCAAkC,CAAC,CAAC;AAC3F,IAAIK,eAAe,GAAGN,sBAAsB,CAACC,OAAO,+BAA+B,CAAC,CAAC;AACrF,IAAIM,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIO,UAAU,GAAGP,OAAO,0BAA0B,CAAC;AACnD,IAAIQ,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAK;EAEjD,IAAIC,sBAAsB;EAC1B,IAAIH,SAAS,IAAIA,SAAS,CAACI,WAAW,KAAKC,MAAM,EAAE;IACjDF,sBAAsB,GAAGR,kBAAkB,CAACH,OAAO,CAACc,6BAA6B,CAACL,KAAK,CAAC;EAC1F;EACA,IAAIM,SAAS,GAAGJ,sBAAsB,IAAIH,SAAS;EACnD,IAAIQ,QAAQ,GAAG,CAAC,CAAC,EAAEZ,eAAe,CAACJ,OAAO,EAAEe,SAAS,EAAEN,KAAK,EAAEC,OAAO,CAAC;EACtE,IAAIO,OAAO,GAAgBZ,MAAM,CAACL,OAAO,CAACO,aAAa,CAACQ,SAAS,EAAEC,QAAQ,CAAC;EAG5E,IAAIE,yBAAyB,GAAGF,QAAQ,CAACG,GAAG,GAAgBd,MAAM,CAACL,OAAO,CAACO,aAAa,CAACD,UAAU,CAACc,cAAc,EAAE;IAClHC,QAAQ,EAAEJ,OAAO;IACjBK,SAAS,EAAEN,QAAQ,CAACG,GAAG;IACvBI,MAAM,EAAEP,QAAQ,CAACQ;EACnB,CAAC,CAAC,GAAGP,OAAO;EACZ,OAAOC,yBAAyB;AAClC,CAAC;AACD,IAAIO,QAAQ,GAAGxB,OAAO,CAACD,OAAO,GAAGO,aAAa;AAC9CmB,MAAM,CAACzB,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}