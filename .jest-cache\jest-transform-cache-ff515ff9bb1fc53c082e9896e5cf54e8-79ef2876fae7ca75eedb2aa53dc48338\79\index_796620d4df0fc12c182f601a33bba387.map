{"version": 3, "names": ["exports", "__esModule", "touchProps", "styleProps", "mouseProps", "keyboardProps", "focusProps", "defaultProps", "clickProps", "accessibilityProps", "children", "dataSet", "dir", "id", "ref", "suppressHydrationWarning", "tabIndex", "testID", "focusable", "nativeID", "role", "accessibilityActiveDescendant", "accessibilityAtomic", "accessibilityAutoComplete", "accessibilityBusy", "accessibilityChecked", "accessibilityColumnCount", "accessibilityColumnIndex", "accessibilityColumnSpan", "accessibilityControls", "accessibilityCurrent", "accessibilityDescribedBy", "accessibilityDetails", "accessibilityDisabled", "accessibilityErrorMessage", "accessibilityExpanded", "accessibilityFlowTo", "accessibilityHasPopup", "accessibilityHidden", "accessibilityInvalid", "accessibilityKeyShortcuts", "accessibilityLabel", "accessibilityLabelledBy", "accessibilityLevel", "accessibilityLiveRegion", "accessibilityModal", "accessibilityMultiline", "accessibilityMultiSelectable", "accessibilityOrientation", "accessibilityOwns", "accessibilityPlaceholder", "accessibilityPosInSet", "accessibilityPressed", "accessibilityReadOnly", "accessibilityRequired", "accessibilityRole", "accessibilityRoleDescription", "accessibilityRowCount", "accessibilityRowIndex", "accessibilityRowSpan", "accessibilitySelected", "accessibilitySetSize", "accessibilitySort", "accessibilityValueMax", "accessibilityValueMin", "accessibilityValueNow", "accessibilityValueText", "onClick", "onAuxClick", "onContextMenu", "onGotPointerCapture", "onLostPointerCapture", "onPointerCancel", "onPointerDown", "onPointerEnter", "onPointerMove", "onPointerLeave", "onPointerOut", "onPointerOver", "onPointerUp", "onBlur", "onFocus", "onKeyDown", "onKeyDownCapture", "onKeyUp", "onKeyUpCapture", "onMouseDown", "onMouseEnter", "onMouseLeave", "onMouseMove", "onMouseOver", "onMouseOut", "onMouseUp", "onTouchCancel", "onTouchCancelCapture", "onTouchEnd", "onTouchEndCapture", "onTouchMove", "onTouchMoveCapture", "onTouchStart", "onTouchStartCapture", "style"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.touchProps = exports.styleProps = exports.mouseProps = exports.keyboardProps = exports.focusProps = exports.defaultProps = exports.clickProps = exports.accessibilityProps = void 0;\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar defaultProps = exports.defaultProps = {\n  children: true,\n  dataSet: true,\n  dir: true,\n  id: true,\n  ref: true,\n  suppressHydrationWarning: true,\n  tabIndex: true,\n  testID: true,\n  // @deprecated\n  focusable: true,\n  nativeID: true\n};\nvar accessibilityProps = exports.accessibilityProps = {\n  'aria-activedescendant': true,\n  'aria-atomic': true,\n  'aria-autocomplete': true,\n  'aria-busy': true,\n  'aria-checked': true,\n  'aria-colcount': true,\n  'aria-colindex': true,\n  'aria-colspan': true,\n  'aria-controls': true,\n  'aria-current': true,\n  'aria-describedby': true,\n  'aria-details': true,\n  'aria-disabled': true,\n  'aria-errormessage': true,\n  'aria-expanded': true,\n  'aria-flowto': true,\n  'aria-haspopup': true,\n  'aria-hidden': true,\n  'aria-invalid': true,\n  'aria-keyshortcuts': true,\n  'aria-label': true,\n  'aria-labelledby': true,\n  'aria-level': true,\n  'aria-live': true,\n  'aria-modal': true,\n  'aria-multiline': true,\n  'aria-multiselectable': true,\n  'aria-orientation': true,\n  'aria-owns': true,\n  'aria-placeholder': true,\n  'aria-posinset': true,\n  'aria-pressed': true,\n  'aria-readonly': true,\n  'aria-required': true,\n  role: true,\n  'aria-roledescription': true,\n  'aria-rowcount': true,\n  'aria-rowindex': true,\n  'aria-rowspan': true,\n  'aria-selected': true,\n  'aria-setsize': true,\n  'aria-sort': true,\n  'aria-valuemax': true,\n  'aria-valuemin': true,\n  'aria-valuenow': true,\n  'aria-valuetext': true,\n  // @deprecated\n  accessibilityActiveDescendant: true,\n  accessibilityAtomic: true,\n  accessibilityAutoComplete: true,\n  accessibilityBusy: true,\n  accessibilityChecked: true,\n  accessibilityColumnCount: true,\n  accessibilityColumnIndex: true,\n  accessibilityColumnSpan: true,\n  accessibilityControls: true,\n  accessibilityCurrent: true,\n  accessibilityDescribedBy: true,\n  accessibilityDetails: true,\n  accessibilityDisabled: true,\n  accessibilityErrorMessage: true,\n  accessibilityExpanded: true,\n  accessibilityFlowTo: true,\n  accessibilityHasPopup: true,\n  accessibilityHidden: true,\n  accessibilityInvalid: true,\n  accessibilityKeyShortcuts: true,\n  accessibilityLabel: true,\n  accessibilityLabelledBy: true,\n  accessibilityLevel: true,\n  accessibilityLiveRegion: true,\n  accessibilityModal: true,\n  accessibilityMultiline: true,\n  accessibilityMultiSelectable: true,\n  accessibilityOrientation: true,\n  accessibilityOwns: true,\n  accessibilityPlaceholder: true,\n  accessibilityPosInSet: true,\n  accessibilityPressed: true,\n  accessibilityReadOnly: true,\n  accessibilityRequired: true,\n  accessibilityRole: true,\n  accessibilityRoleDescription: true,\n  accessibilityRowCount: true,\n  accessibilityRowIndex: true,\n  accessibilityRowSpan: true,\n  accessibilitySelected: true,\n  accessibilitySetSize: true,\n  accessibilitySort: true,\n  accessibilityValueMax: true,\n  accessibilityValueMin: true,\n  accessibilityValueNow: true,\n  accessibilityValueText: true\n};\nvar clickProps = exports.clickProps = {\n  onClick: true,\n  onAuxClick: true,\n  onContextMenu: true,\n  onGotPointerCapture: true,\n  onLostPointerCapture: true,\n  onPointerCancel: true,\n  onPointerDown: true,\n  onPointerEnter: true,\n  onPointerMove: true,\n  onPointerLeave: true,\n  onPointerOut: true,\n  onPointerOver: true,\n  onPointerUp: true\n};\nvar focusProps = exports.focusProps = {\n  onBlur: true,\n  onFocus: true\n};\nvar keyboardProps = exports.keyboardProps = {\n  onKeyDown: true,\n  onKeyDownCapture: true,\n  onKeyUp: true,\n  onKeyUpCapture: true\n};\nvar mouseProps = exports.mouseProps = {\n  onMouseDown: true,\n  onMouseEnter: true,\n  onMouseLeave: true,\n  onMouseMove: true,\n  onMouseOver: true,\n  onMouseOut: true,\n  onMouseUp: true\n};\nvar touchProps = exports.touchProps = {\n  onTouchCancel: true,\n  onTouchCancelCapture: true,\n  onTouchEnd: true,\n  onTouchEndCapture: true,\n  onTouchMove: true,\n  onTouchMoveCapture: true,\n  onTouchStart: true,\n  onTouchStartCapture: true\n};\nvar styleProps = exports.styleProps = {\n  style: true\n};"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,UAAU,GAAGF,OAAO,CAACG,UAAU,GAAGH,OAAO,CAACI,UAAU,GAAGJ,OAAO,CAACK,aAAa,GAAGL,OAAO,CAACM,UAAU,GAAGN,OAAO,CAACO,YAAY,GAAGP,OAAO,CAACQ,UAAU,GAAGR,OAAO,CAACS,kBAAkB,GAAG,KAAK,CAAC;AAU3L,IAAIF,YAAY,GAAGP,OAAO,CAACO,YAAY,GAAG;EACxCG,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,IAAI;EACbC,GAAG,EAAE,IAAI;EACTC,EAAE,EAAE,IAAI;EACRC,GAAG,EAAE,IAAI;EACTC,wBAAwB,EAAE,IAAI;EAC9BC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,IAAI;EAEZC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIV,kBAAkB,GAAGT,OAAO,CAACS,kBAAkB,GAAG;EACpD,uBAAuB,EAAE,IAAI;EAC7B,aAAa,EAAE,IAAI;EACnB,mBAAmB,EAAE,IAAI;EACzB,WAAW,EAAE,IAAI;EACjB,cAAc,EAAE,IAAI;EACpB,eAAe,EAAE,IAAI;EACrB,eAAe,EAAE,IAAI;EACrB,cAAc,EAAE,IAAI;EACpB,eAAe,EAAE,IAAI;EACrB,cAAc,EAAE,IAAI;EACpB,kBAAkB,EAAE,IAAI;EACxB,cAAc,EAAE,IAAI;EACpB,eAAe,EAAE,IAAI;EACrB,mBAAmB,EAAE,IAAI;EACzB,eAAe,EAAE,IAAI;EACrB,aAAa,EAAE,IAAI;EACnB,eAAe,EAAE,IAAI;EACrB,aAAa,EAAE,IAAI;EACnB,cAAc,EAAE,IAAI;EACpB,mBAAmB,EAAE,IAAI;EACzB,YAAY,EAAE,IAAI;EAClB,iBAAiB,EAAE,IAAI;EACvB,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,YAAY,EAAE,IAAI;EAClB,gBAAgB,EAAE,IAAI;EACtB,sBAAsB,EAAE,IAAI;EAC5B,kBAAkB,EAAE,IAAI;EACxB,WAAW,EAAE,IAAI;EACjB,kBAAkB,EAAE,IAAI;EACxB,eAAe,EAAE,IAAI;EACrB,cAAc,EAAE,IAAI;EACpB,eAAe,EAAE,IAAI;EACrB,eAAe,EAAE,IAAI;EACrBW,IAAI,EAAE,IAAI;EACV,sBAAsB,EAAE,IAAI;EAC5B,eAAe,EAAE,IAAI;EACrB,eAAe,EAAE,IAAI;EACrB,cAAc,EAAE,IAAI;EACpB,eAAe,EAAE,IAAI;EACrB,cAAc,EAAE,IAAI;EACpB,WAAW,EAAE,IAAI;EACjB,eAAe,EAAE,IAAI;EACrB,eAAe,EAAE,IAAI;EACrB,eAAe,EAAE,IAAI;EACrB,gBAAgB,EAAE,IAAI;EAEtBC,6BAA6B,EAAE,IAAI;EACnCC,mBAAmB,EAAE,IAAI;EACzBC,yBAAyB,EAAE,IAAI;EAC/BC,iBAAiB,EAAE,IAAI;EACvBC,oBAAoB,EAAE,IAAI;EAC1BC,wBAAwB,EAAE,IAAI;EAC9BC,wBAAwB,EAAE,IAAI;EAC9BC,uBAAuB,EAAE,IAAI;EAC7BC,qBAAqB,EAAE,IAAI;EAC3BC,oBAAoB,EAAE,IAAI;EAC1BC,wBAAwB,EAAE,IAAI;EAC9BC,oBAAoB,EAAE,IAAI;EAC1BC,qBAAqB,EAAE,IAAI;EAC3BC,yBAAyB,EAAE,IAAI;EAC/BC,qBAAqB,EAAE,IAAI;EAC3BC,mBAAmB,EAAE,IAAI;EACzBC,qBAAqB,EAAE,IAAI;EAC3BC,mBAAmB,EAAE,IAAI;EACzBC,oBAAoB,EAAE,IAAI;EAC1BC,yBAAyB,EAAE,IAAI;EAC/BC,kBAAkB,EAAE,IAAI;EACxBC,uBAAuB,EAAE,IAAI;EAC7BC,kBAAkB,EAAE,IAAI;EACxBC,uBAAuB,EAAE,IAAI;EAC7BC,kBAAkB,EAAE,IAAI;EACxBC,sBAAsB,EAAE,IAAI;EAC5BC,4BAA4B,EAAE,IAAI;EAClCC,wBAAwB,EAAE,IAAI;EAC9BC,iBAAiB,EAAE,IAAI;EACvBC,wBAAwB,EAAE,IAAI;EAC9BC,qBAAqB,EAAE,IAAI;EAC3BC,oBAAoB,EAAE,IAAI;EAC1BC,qBAAqB,EAAE,IAAI;EAC3BC,qBAAqB,EAAE,IAAI;EAC3BC,iBAAiB,EAAE,IAAI;EACvBC,4BAA4B,EAAE,IAAI;EAClCC,qBAAqB,EAAE,IAAI;EAC3BC,qBAAqB,EAAE,IAAI;EAC3BC,oBAAoB,EAAE,IAAI;EAC1BC,qBAAqB,EAAE,IAAI;EAC3BC,oBAAoB,EAAE,IAAI;EAC1BC,iBAAiB,EAAE,IAAI;EACvBC,qBAAqB,EAAE,IAAI;EAC3BC,qBAAqB,EAAE,IAAI;EAC3BC,qBAAqB,EAAE,IAAI;EAC3BC,sBAAsB,EAAE;AAC1B,CAAC;AACD,IAAI1D,UAAU,GAAGR,OAAO,CAACQ,UAAU,GAAG;EACpC2D,OAAO,EAAE,IAAI;EACbC,UAAU,EAAE,IAAI;EAChBC,aAAa,EAAE,IAAI;EACnBC,mBAAmB,EAAE,IAAI;EACzBC,oBAAoB,EAAE,IAAI;EAC1BC,eAAe,EAAE,IAAI;EACrBC,aAAa,EAAE,IAAI;EACnBC,cAAc,EAAE,IAAI;EACpBC,aAAa,EAAE,IAAI;EACnBC,cAAc,EAAE,IAAI;EACpBC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,IAAI;EACnBC,WAAW,EAAE;AACf,CAAC;AACD,IAAIzE,UAAU,GAAGN,OAAO,CAACM,UAAU,GAAG;EACpC0E,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE;AACX,CAAC;AACD,IAAI5E,aAAa,GAAGL,OAAO,CAACK,aAAa,GAAG;EAC1C6E,SAAS,EAAE,IAAI;EACfC,gBAAgB,EAAE,IAAI;EACtBC,OAAO,EAAE,IAAI;EACbC,cAAc,EAAE;AAClB,CAAC;AACD,IAAIjF,UAAU,GAAGJ,OAAO,CAACI,UAAU,GAAG;EACpCkF,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE;AACb,CAAC;AACD,IAAI1F,UAAU,GAAGF,OAAO,CAACE,UAAU,GAAG;EACpC2F,aAAa,EAAE,IAAI;EACnBC,oBAAoB,EAAE,IAAI;EAC1BC,UAAU,EAAE,IAAI;EAChBC,iBAAiB,EAAE,IAAI;EACvBC,WAAW,EAAE,IAAI;EACjBC,kBAAkB,EAAE,IAAI;EACxBC,YAAY,EAAE,IAAI;EAClBC,mBAAmB,EAAE;AACvB,CAAC;AACD,IAAIjG,UAAU,GAAGH,OAAO,CAACG,UAAU,GAAG;EACpCkG,KAAK,EAAE;AACT,CAAC", "ignoreList": []}