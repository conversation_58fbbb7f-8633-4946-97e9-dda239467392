{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_interopRequireDefault", "default", "exports", "__esModule", "_bezier2", "ease", "Easing", "key", "value", "step0", "n", "step1", "linear", "t", "bezier", "quad", "cubic", "poly", "Math", "pow", "sin", "cos", "PI", "circle", "sqrt", "exp", "elastic", "bounciness", "p", "back", "s", "bounce", "_t", "_t2", "t2", "x1", "y1", "x2", "y2", "in", "easing", "out", "inOut", "_default", "module"], "sources": ["Easing.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * \n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _bezier2 = _interopRequireDefault(require(\"./bezier\"));\nvar ease;\n\n/**\n * The `Easing` module implements common easing functions. This module is used\n * by [Animate.timing()](docs/animate.html#timing) to convey physically\n * believable motion in animations.\n *\n * You can find a visualization of some common easing functions at\n * http://easings.net/\n *\n * ### Predefined animations\n *\n * The `Easing` module provides several predefined animations through the\n * following methods:\n *\n * - [`back`](docs/easing.html#back) provides a simple animation where the\n *   object goes slightly back before moving forward\n * - [`bounce`](docs/easing.html#bounce) provides a bouncing animation\n * - [`ease`](docs/easing.html#ease) provides a simple inertial animation\n * - [`elastic`](docs/easing.html#elastic) provides a simple spring interaction\n *\n * ### Standard functions\n *\n * Three standard easing functions are provided:\n *\n * - [`linear`](docs/easing.html#linear)\n * - [`quad`](docs/easing.html#quad)\n * - [`cubic`](docs/easing.html#cubic)\n *\n * The [`poly`](docs/easing.html#poly) function can be used to implement\n * quartic, quintic, and other higher power functions.\n *\n * ### Additional functions\n *\n * Additional mathematical functions are provided by the following methods:\n *\n * - [`bezier`](docs/easing.html#bezier) provides a cubic bezier curve\n * - [`circle`](docs/easing.html#circle) provides a circular function\n * - [`sin`](docs/easing.html#sin) provides a sinusoidal function\n * - [`exp`](docs/easing.html#exp) provides an exponential function\n *\n * The following helpers are used to modify other easing functions.\n *\n * - [`in`](docs/easing.html#in) runs an easing function forwards\n * - [`inOut`](docs/easing.html#inout) makes any easing function symmetrical\n * - [`out`](docs/easing.html#out) runs an easing function backwards\n */\nclass Easing {\n  /**\n   * A stepping function, returns 1 for any positive value of `n`.\n   */\n  static step0(n) {\n    return n > 0 ? 1 : 0;\n  }\n\n  /**\n   * A stepping function, returns 1 if `n` is greater than or equal to 1.\n   */\n  static step1(n) {\n    return n >= 1 ? 1 : 0;\n  }\n\n  /**\n   * A linear function, `f(t) = t`. Position correlates to elapsed time one to\n   * one.\n   *\n   * http://cubic-bezier.com/#0,0,1,1\n   */\n  static linear(t) {\n    return t;\n  }\n\n  /**\n   * A simple inertial interaction, similar to an object slowly accelerating to\n   * speed.\n   *\n   * http://cubic-bezier.com/#.42,0,1,1\n   */\n  static ease(t) {\n    if (!ease) {\n      ease = Easing.bezier(0.42, 0, 1, 1);\n    }\n    return ease(t);\n  }\n\n  /**\n   * A quadratic function, `f(t) = t * t`. Position equals the square of elapsed\n   * time.\n   *\n   * http://easings.net/#easeInQuad\n   */\n  static quad(t) {\n    return t * t;\n  }\n\n  /**\n   * A cubic function, `f(t) = t * t * t`. Position equals the cube of elapsed\n   * time.\n   *\n   * http://easings.net/#easeInCubic\n   */\n  static cubic(t) {\n    return t * t * t;\n  }\n\n  /**\n   * A power function. Position is equal to the Nth power of elapsed time.\n   *\n   * n = 4: http://easings.net/#easeInQuart\n   * n = 5: http://easings.net/#easeInQuint\n   */\n  static poly(n) {\n    return t => Math.pow(t, n);\n  }\n\n  /**\n   * A sinusoidal function.\n   *\n   * http://easings.net/#easeInSine\n   */\n  static sin(t) {\n    return 1 - Math.cos(t * Math.PI / 2);\n  }\n\n  /**\n   * A circular function.\n   *\n   * http://easings.net/#easeInCirc\n   */\n  static circle(t) {\n    return 1 - Math.sqrt(1 - t * t);\n  }\n\n  /**\n   * An exponential function.\n   *\n   * http://easings.net/#easeInExpo\n   */\n  static exp(t) {\n    return Math.pow(2, 10 * (t - 1));\n  }\n\n  /**\n   * A simple elastic interaction, similar to a spring oscillating back and\n   * forth.\n   *\n   * Default bounciness is 1, which overshoots a little bit once. 0 bounciness\n   * doesn't overshoot at all, and bounciness of N > 1 will overshoot about N\n   * times.\n   *\n   * http://easings.net/#easeInElastic\n   */\n  static elastic(bounciness) {\n    if (bounciness === void 0) {\n      bounciness = 1;\n    }\n    var p = bounciness * Math.PI;\n    return t => 1 - Math.pow(Math.cos(t * Math.PI / 2), 3) * Math.cos(t * p);\n  }\n\n  /**\n   * Use with `Animated.parallel()` to create a simple effect where the object\n   * animates back slightly as the animation starts.\n   *\n   * Wolfram Plot:\n   *\n   * - http://tiny.cc/back_default (s = 1.70158, default)\n   */\n  static back(s) {\n    if (s === void 0) {\n      s = 1.70158;\n    }\n    return t => t * t * ((s + 1) * t - s);\n  }\n\n  /**\n   * Provides a simple bouncing effect.\n   *\n   * http://easings.net/#easeInBounce\n   */\n  static bounce(t) {\n    if (t < 1 / 2.75) {\n      return 7.5625 * t * t;\n    }\n    if (t < 2 / 2.75) {\n      var _t = t - 1.5 / 2.75;\n      return 7.5625 * _t * _t + 0.75;\n    }\n    if (t < 2.5 / 2.75) {\n      var _t2 = t - 2.25 / 2.75;\n      return 7.5625 * _t2 * _t2 + 0.9375;\n    }\n    var t2 = t - 2.625 / 2.75;\n    return 7.5625 * t2 * t2 + 0.984375;\n  }\n\n  /**\n   * Provides a cubic bezier curve, equivalent to CSS Transitions'\n   * `transition-timing-function`.\n   *\n   * A useful tool to visualize cubic bezier curves can be found at\n   * http://cubic-bezier.com/\n   */\n  static bezier(x1, y1, x2, y2) {\n    return (0, _bezier2.default)(x1, y1, x2, y2);\n  }\n\n  /**\n   * Runs an easing function forwards.\n   */\n  static in(easing) {\n    return easing;\n  }\n\n  /**\n   * Runs an easing function backwards.\n   */\n  static out(easing) {\n    return t => 1 - easing(1 - t);\n  }\n\n  /**\n   * Makes any easing function symmetrical. The easing function will run\n   * forwards for half of the duration, then backwards for the rest of the\n   * duration.\n   */\n  static inOut(easing) {\n    return t => {\n      if (t < 0.5) {\n        return easing(t * 2) / 2;\n      }\n      return 1 - easing((1 - t) * 2) / 2;\n    };\n  }\n}\nvar _default = exports.default = Easing;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAEb,IAAIG,sBAAsB,GAAGH,OAAO,CAAC,8CAA8C,CAAC,CAACI,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,QAAQ,GAAGJ,sBAAsB,CAACH,OAAO,WAAW,CAAC,CAAC;AAC1D,IAAIQ,KAAI;AAAC,IA+CHC,MAAM;EAAA,SAAAA,OAAA;IAAA,IAAAR,gBAAA,CAAAG,OAAA,QAAAK,MAAA;EAAA;EAAA,WAAAP,aAAA,CAAAE,OAAA,EAAAK,MAAA;IAAAC,GAAA;IAAAC,KAAA,EAIV,SAAOC,KAAKA,CAACC,CAAC,EAAE;MACd,OAAOA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACtB;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAKD,SAAOG,KAAKA,CAACD,CAAC,EAAE;MACd,OAAOA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACvB;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAQD,SAAOI,MAAMA,CAACC,CAAC,EAAE;MACf,OAAOA,CAAC;IACV;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAQD,SAAOH,IAAIA,CAACQ,CAAC,EAAE;MACb,IAAI,CAACR,KAAI,EAAE;QACTA,KAAI,GAAGC,MAAM,CAACQ,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrC;MACA,OAAOT,KAAI,CAACQ,CAAC,CAAC;IAChB;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAQD,SAAOO,IAAIA,CAACF,CAAC,EAAE;MACb,OAAOA,CAAC,GAAGA,CAAC;IACd;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAQD,SAAOQ,KAAKA,CAACH,CAAC,EAAE;MACd,OAAOA,CAAC,GAAGA,CAAC,GAAGA,CAAC;IAClB;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAQD,SAAOS,IAAIA,CAACP,CAAC,EAAE;MACb,OAAO,UAAAG,CAAC;QAAA,OAAIK,IAAI,CAACC,GAAG,CAACN,CAAC,EAAEH,CAAC,CAAC;MAAA;IAC5B;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAOD,SAAOY,GAAGA,CAACP,CAAC,EAAE;MACZ,OAAO,CAAC,GAAGK,IAAI,CAACG,GAAG,CAACR,CAAC,GAAGK,IAAI,CAACI,EAAE,GAAG,CAAC,CAAC;IACtC;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAOD,SAAOe,MAAMA,CAACV,CAAC,EAAE;MACf,OAAO,CAAC,GAAGK,IAAI,CAACM,IAAI,CAAC,CAAC,GAAGX,CAAC,GAAGA,CAAC,CAAC;IACjC;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAOD,SAAOiB,GAAGA,CAACZ,CAAC,EAAE;MACZ,OAAOK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIN,CAAC,GAAG,CAAC,CAAC,CAAC;IAClC;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAYD,SAAOkB,OAAOA,CAACC,UAAU,EAAE;MACzB,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;QACzBA,UAAU,GAAG,CAAC;MAChB;MACA,IAAIC,CAAC,GAAGD,UAAU,GAAGT,IAAI,CAACI,EAAE;MAC5B,OAAO,UAAAT,CAAC;QAAA,OAAI,CAAC,GAAGK,IAAI,CAACC,GAAG,CAACD,IAAI,CAACG,GAAG,CAACR,CAAC,GAAGK,IAAI,CAACI,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGJ,IAAI,CAACG,GAAG,CAACR,CAAC,GAAGe,CAAC,CAAC;MAAA;IAC1E;EAAC;IAAArB,GAAA;IAAAC,KAAA,EAUD,SAAOqB,IAAIA,CAACC,CAAC,EAAE;MACb,IAAIA,CAAC,KAAK,KAAK,CAAC,EAAE;QAChBA,CAAC,GAAG,OAAO;MACb;MACA,OAAO,UAAAjB,CAAC;QAAA,OAAIA,CAAC,GAAGA,CAAC,IAAI,CAACiB,CAAC,GAAG,CAAC,IAAIjB,CAAC,GAAGiB,CAAC,CAAC;MAAA;IACvC;EAAC;IAAAvB,GAAA;IAAAC,KAAA,EAOD,SAAOuB,MAAMA,CAAClB,CAAC,EAAE;MACf,IAAIA,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;QAChB,OAAO,MAAM,GAAGA,CAAC,GAAGA,CAAC;MACvB;MACA,IAAIA,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;QAChB,IAAImB,EAAE,GAAGnB,CAAC,GAAG,GAAG,GAAG,IAAI;QACvB,OAAO,MAAM,GAAGmB,EAAE,GAAGA,EAAE,GAAG,IAAI;MAChC;MACA,IAAInB,CAAC,GAAG,GAAG,GAAG,IAAI,EAAE;QAClB,IAAIoB,GAAG,GAAGpB,CAAC,GAAG,IAAI,GAAG,IAAI;QACzB,OAAO,MAAM,GAAGoB,GAAG,GAAGA,GAAG,GAAG,MAAM;MACpC;MACA,IAAIC,EAAE,GAAGrB,CAAC,GAAG,KAAK,GAAG,IAAI;MACzB,OAAO,MAAM,GAAGqB,EAAE,GAAGA,EAAE,GAAG,QAAQ;IACpC;EAAC;IAAA3B,GAAA;IAAAC,KAAA,EASD,SAAOM,MAAMA,CAACqB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;MAC5B,OAAO,CAAC,CAAC,EAAElC,QAAQ,CAACH,OAAO,EAAEkC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC9C;EAAC;IAAA/B,GAAA;IAAAC,KAAA,EAKD,SAAO+B,GAAEA,CAACC,MAAM,EAAE;MAChB,OAAOA,MAAM;IACf;EAAC;IAAAjC,GAAA;IAAAC,KAAA,EAKD,SAAOiC,GAAGA,CAACD,MAAM,EAAE;MACjB,OAAO,UAAA3B,CAAC;QAAA,OAAI,CAAC,GAAG2B,MAAM,CAAC,CAAC,GAAG3B,CAAC,CAAC;MAAA;IAC/B;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAOD,SAAOkC,KAAKA,CAACF,MAAM,EAAE;MACnB,OAAO,UAAA3B,CAAC,EAAI;QACV,IAAIA,CAAC,GAAG,GAAG,EAAE;UACX,OAAO2B,MAAM,CAAC3B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;QAC1B;QACA,OAAO,CAAC,GAAG2B,MAAM,CAAC,CAAC,CAAC,GAAG3B,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;MACpC,CAAC;IACH;EAAC;AAAA;AAEH,IAAI8B,QAAQ,GAAGzC,OAAO,CAACD,OAAO,GAAGK,MAAM;AACvCsC,MAAM,CAAC1C,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}