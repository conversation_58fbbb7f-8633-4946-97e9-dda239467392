{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_RCTDeviceEventEmitter", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _RCTDeviceEventEmitter = _interopRequireDefault(require(\"../../vendor/react-native/EventEmitter/RCTDeviceEventEmitter\"));\nvar _default = exports.default = _RCTDeviceEventEmitter.default;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,sBAAsB,GAAGL,sBAAsB,CAACC,OAAO,+DAA+D,CAAC,CAAC;AAC5H,IAAIK,QAAQ,GAAGH,OAAO,CAACD,OAAO,GAAGG,sBAAsB,CAACH,OAAO;AAC/DK,MAAM,CAACJ,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}