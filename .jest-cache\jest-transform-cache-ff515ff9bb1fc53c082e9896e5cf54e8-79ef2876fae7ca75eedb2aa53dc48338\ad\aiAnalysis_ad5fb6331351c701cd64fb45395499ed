8a107b7f160a596e7fdae682c87389cd
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_bi4w3tskw() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\aiAnalysis.ts";
  var hash = "8486e8a1ab404c7368e05163ca42a192668589a6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\aiAnalysis.ts",
    statementMap: {
      "0": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 103,
          column: 5
        }
      },
      "1": {
        start: {
          line: 56,
          column: 6
        },
        end: {
          line: 56,
          column: 59
        }
      },
      "2": {
        start: {
          line: 59,
          column: 29
        },
        end: {
          line: 59,
          column: 85
        }
      },
      "3": {
        start: {
          line: 62,
          column: 53
        },
        end: {
          line: 67,
          column: 7
        }
      },
      "4": {
        start: {
          line: 69,
          column: 25
        },
        end: {
          line: 69,
          column: 84
        }
      },
      "5": {
        start: {
          line: 72,
          column: 29
        },
        end: {
          line: 72,
          column: 77
        }
      },
      "6": {
        start: {
          line: 73,
          column: 34
        },
        end: {
          line: 73,
          column: 101
        }
      },
      "7": {
        start: {
          line: 76,
          column: 30
        },
        end: {
          line: 80,
          column: 7
        }
      },
      "8": {
        start: {
          line: 82,
          column: 27
        },
        end: {
          line: 86,
          column: 7
        }
      },
      "9": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 99,
          column: 8
        }
      },
      "10": {
        start: {
          line: 101,
          column: 6
        },
        end: {
          line: 101,
          column: 49
        }
      },
      "11": {
        start: {
          line: 102,
          column: 6
        },
        end: {
          line: 102,
          column: 51
        }
      },
      "12": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 145,
          column: 5
        }
      },
      "13": {
        start: {
          line: 116,
          column: 31
        },
        end: {
          line: 116,
          column: 71
        }
      },
      "14": {
        start: {
          line: 119,
          column: 26
        },
        end: {
          line: 123,
          column: 7
        }
      },
      "15": {
        start: {
          line: 126,
          column: 28
        },
        end: {
          line: 126,
          column: 73
        }
      },
      "16": {
        start: {
          line: 129,
          column: 31
        },
        end: {
          line: 132,
          column: 7
        }
      },
      "17": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 141,
          column: 8
        }
      },
      "18": {
        start: {
          line: 143,
          column: 6
        },
        end: {
          line: 143,
          column: 56
        }
      },
      "19": {
        start: {
          line: 144,
          column: 6
        },
        end: {
          line: 144,
          column: 48
        }
      },
      "20": {
        start: {
          line: 161,
          column: 4
        },
        end: {
          line: 189,
          column: 5
        }
      },
      "21": {
        start: {
          line: 163,
          column: 34
        },
        end: {
          line: 163,
          column: 97
        }
      },
      "22": {
        start: {
          line: 166,
          column: 31
        },
        end: {
          line: 170,
          column: 7
        }
      },
      "23": {
        start: {
          line: 173,
          column: 25
        },
        end: {
          line: 178,
          column: 7
        }
      },
      "24": {
        start: {
          line: 180,
          column: 6
        },
        end: {
          line: 185,
          column: 8
        }
      },
      "25": {
        start: {
          line: 187,
          column: 6
        },
        end: {
          line: 187,
          column: 52
        }
      },
      "26": {
        start: {
          line: 188,
          column: 6
        },
        end: {
          line: 188,
          column: 45
        }
      },
      "27": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 228,
          column: 5
        }
      },
      "28": {
        start: {
          line: 206,
          column: 53
        },
        end: {
          line: 211,
          column: 7
        }
      },
      "29": {
        start: {
          line: 208,
          column: 48
        },
        end: {
          line: 208,
          column: 55
        }
      },
      "30": {
        start: {
          line: 213,
          column: 25
        },
        end: {
          line: 213,
          column: 84
        }
      },
      "31": {
        start: {
          line: 215,
          column: 25
        },
        end: {
          line: 215,
          column: 81
        }
      },
      "32": {
        start: {
          line: 216,
          column: 25
        },
        end: {
          line: 216,
          column: 74
        }
      },
      "33": {
        start: {
          line: 217,
          column: 30
        },
        end: {
          line: 217,
          column: 78
        }
      },
      "34": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 224,
          column: 8
        }
      },
      "35": {
        start: {
          line: 226,
          column: 6
        },
        end: {
          line: 226,
          column: 62
        }
      },
      "36": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 55
        }
      },
      "37": {
        start: {
          line: 234,
          column: 21
        },
        end: {
          line: 234,
          column: 134
        }
      },
      "38": {
        start: {
          line: 234,
          column: 77
        },
        end: {
          line: 234,
          column: 97
        }
      },
      "39": {
        start: {
          line: 235,
          column: 29
        },
        end: {
          line: 235,
          column: 79
        }
      },
      "40": {
        start: {
          line: 236,
          column: 28
        },
        end: {
          line: 236,
          column: 138
        }
      },
      "41": {
        start: {
          line: 236,
          column: 85
        },
        end: {
          line: 236,
          column: 100
        }
      },
      "42": {
        start: {
          line: 238,
          column: 4
        },
        end: {
          line: 238,
          column: 166
        }
      },
      "43": {
        start: {
          line: 242,
          column: 50
        },
        end: {
          line: 242,
          column: 52
        }
      },
      "44": {
        start: {
          line: 243,
          column: 4
        },
        end: {
          line: 245,
          column: 7
        }
      },
      "45": {
        start: {
          line: 244,
          column: 6
        },
        end: {
          line: 244,
          column: 65
        }
      },
      "46": {
        start: {
          line: 247,
          column: 4
        },
        end: {
          line: 247,
          column: 88
        }
      },
      "47": {
        start: {
          line: 247,
          column: 59
        },
        end: {
          line: 247,
          column: 64
        }
      },
      "48": {
        start: {
          line: 252,
          column: 4
        },
        end: {
          line: 269,
          column: 6
        }
      },
      "49": {
        start: {
          line: 273,
          column: 4
        },
        end: {
          line: 273,
          column: 94
        }
      },
      "50": {
        start: {
          line: 273,
          column: 50
        },
        end: {
          line: 273,
          column: 70
        }
      },
      "51": {
        start: {
          line: 281,
          column: 4
        },
        end: {
          line: 295,
          column: 6
        }
      },
      "52": {
        start: {
          line: 303,
          column: 25
        },
        end: {
          line: 303,
          column: 76
        }
      },
      "53": {
        start: {
          line: 305,
          column: 4
        },
        end: {
          line: 313,
          column: 6
        }
      },
      "54": {
        start: {
          line: 317,
          column: 4
        },
        end: {
          line: 320,
          column: 49
        }
      },
      "55": {
        start: {
          line: 318,
          column: 28
        },
        end: {
          line: 318,
          column: 57
        }
      },
      "56": {
        start: {
          line: 320,
          column: 24
        },
        end: {
          line: 320,
          column: 47
        }
      },
      "57": {
        start: {
          line: 325,
          column: 4
        },
        end: {
          line: 325,
          column: 53
        }
      },
      "58": {
        start: {
          line: 334,
          column: 4
        },
        end: {
          line: 338,
          column: 6
        }
      },
      "59": {
        start: {
          line: 342,
          column: 29
        },
        end: {
          line: 347,
          column: 5
        }
      },
      "60": {
        start: {
          line: 349,
          column: 4
        },
        end: {
          line: 349,
          column: 81
        }
      },
      "61": {
        start: {
          line: 353,
          column: 4
        },
        end: {
          line: 359,
          column: 6
        }
      },
      "62": {
        start: {
          line: 363,
          column: 4
        },
        end: {
          line: 368,
          column: 6
        }
      },
      "63": {
        start: {
          line: 372,
          column: 4
        },
        end: {
          line: 372,
          column: 51
        }
      },
      "64": {
        start: {
          line: 376,
          column: 4
        },
        end: {
          line: 381,
          column: 8
        }
      },
      "65": {
        start: {
          line: 376,
          column: 30
        },
        end: {
          line: 381,
          column: 5
        }
      },
      "66": {
        start: {
          line: 387,
          column: 4
        },
        end: {
          line: 420,
          column: 6
        }
      },
      "67": {
        start: {
          line: 424,
          column: 4
        },
        end: {
          line: 434,
          column: 6
        }
      },
      "68": {
        start: {
          line: 438,
          column: 4
        },
        end: {
          line: 443,
          column: 6
        }
      },
      "69": {
        start: {
          line: 447,
          column: 4
        },
        end: {
          line: 452,
          column: 6
        }
      },
      "70": {
        start: {
          line: 456,
          column: 33
        },
        end: {
          line: 456,
          column: 56
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 46,
            column: 2
          },
          end: {
            line: 46,
            column: 3
          }
        },
        loc: {
          start: {
            line: 54,
            column: 36
          },
          end: {
            line: 104,
            column: 3
          }
        },
        line: 54
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 109,
            column: 2
          },
          end: {
            line: 109,
            column: 3
          }
        },
        loc: {
          start: {
            line: 113,
            column: 31
          },
          end: {
            line: 146,
            column: 3
          }
        },
        line: 113
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 151,
            column: 2
          },
          end: {
            line: 151,
            column: 3
          }
        },
        loc: {
          start: {
            line: 160,
            column: 5
          },
          end: {
            line: 190,
            column: 3
          }
        },
        line: 160
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 195,
            column: 2
          },
          end: {
            line: 195,
            column: 3
          }
        },
        loc: {
          start: {
            line: 204,
            column: 5
          },
          end: {
            line: 229,
            column: 3
          }
        },
        line: 204
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 208,
            column: 43
          },
          end: {
            line: 208,
            column: 44
          }
        },
        loc: {
          start: {
            line: 208,
            column: 48
          },
          end: {
            line: 208,
            column: 55
          }
        },
        line: 208
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 233,
            column: 2
          },
          end: {
            line: 233,
            column: 3
          }
        },
        loc: {
          start: {
            line: 233,
            column: 60
          },
          end: {
            line: 239,
            column: 3
          }
        },
        line: 233
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 234,
            column: 52
          },
          end: {
            line: 234,
            column: 53
          }
        },
        loc: {
          start: {
            line: 234,
            column: 77
          },
          end: {
            line: 234,
            column: 97
          }
        },
        line: 234
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 236,
            column: 60
          },
          end: {
            line: 236,
            column: 61
          }
        },
        loc: {
          start: {
            line: 236,
            column: 85
          },
          end: {
            line: 236,
            column: 100
          }
        },
        line: 236
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 241,
            column: 2
          },
          end: {
            line: 241,
            column: 3
          }
        },
        loc: {
          start: {
            line: 241,
            column: 70
          },
          end: {
            line: 248,
            column: 3
          }
        },
        line: 241
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 243,
            column: 21
          },
          end: {
            line: 243,
            column: 22
          }
        },
        loc: {
          start: {
            line: 243,
            column: 26
          },
          end: {
            line: 245,
            column: 5
          }
        },
        line: 243
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 247,
            column: 43
          },
          end: {
            line: 247,
            column: 44
          }
        },
        loc: {
          start: {
            line: 247,
            column: 59
          },
          end: {
            line: 247,
            column: 64
          }
        },
        line: 247
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 250,
            column: 2
          },
          end: {
            line: 250,
            column: 3
          }
        },
        loc: {
          start: {
            line: 250,
            column: 76
          },
          end: {
            line: 270,
            column: 3
          }
        },
        line: 250
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 272,
            column: 2
          },
          end: {
            line: 272,
            column: 3
          }
        },
        loc: {
          start: {
            line: 272,
            column: 76
          },
          end: {
            line: 274,
            column: 3
          }
        },
        line: 272
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 273,
            column: 38
          },
          end: {
            line: 273,
            column: 39
          }
        },
        loc: {
          start: {
            line: 273,
            column: 50
          },
          end: {
            line: 273,
            column: 70
          }
        },
        line: 273
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 276,
            column: 2
          },
          end: {
            line: 276,
            column: 3
          }
        },
        loc: {
          start: {
            line: 280,
            column: 4
          },
          end: {
            line: 296,
            column: 3
          }
        },
        line: 280
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 298,
            column: 2
          },
          end: {
            line: 298,
            column: 3
          }
        },
        loc: {
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 314,
            column: 3
          }
        },
        line: 302
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 316,
            column: 2
          },
          end: {
            line: 316,
            column: 3
          }
        },
        loc: {
          start: {
            line: 316,
            column: 60
          },
          end: {
            line: 321,
            column: 3
          }
        },
        line: 316
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 318,
            column: 12
          },
          end: {
            line: 318,
            column: 13
          }
        },
        loc: {
          start: {
            line: 318,
            column: 28
          },
          end: {
            line: 318,
            column: 57
          }
        },
        line: 318
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 320,
            column: 11
          },
          end: {
            line: 320,
            column: 12
          }
        },
        loc: {
          start: {
            line: 320,
            column: 24
          },
          end: {
            line: 320,
            column: 47
          }
        },
        line: 320
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 323,
            column: 2
          },
          end: {
            line: 323,
            column: 3
          }
        },
        loc: {
          start: {
            line: 323,
            column: 62
          },
          end: {
            line: 326,
            column: 3
          }
        },
        line: 323
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 328,
            column: 2
          },
          end: {
            line: 328,
            column: 3
          }
        },
        loc: {
          start: {
            line: 332,
            column: 23
          },
          end: {
            line: 339,
            column: 3
          }
        },
        line: 332
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 341,
            column: 2
          },
          end: {
            line: 341,
            column: 3
          }
        },
        loc: {
          start: {
            line: 341,
            column: 66
          },
          end: {
            line: 350,
            column: 3
          }
        },
        line: 341
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 352,
            column: 2
          },
          end: {
            line: 352,
            column: 3
          }
        },
        loc: {
          start: {
            line: 352,
            column: 79
          },
          end: {
            line: 360,
            column: 3
          }
        },
        line: 352
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 362,
            column: 2
          },
          end: {
            line: 362,
            column: 3
          }
        },
        loc: {
          start: {
            line: 362,
            column: 87
          },
          end: {
            line: 369,
            column: 3
          }
        },
        line: 362
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 371,
            column: 2
          },
          end: {
            line: 371,
            column: 3
          }
        },
        loc: {
          start: {
            line: 371,
            column: 58
          },
          end: {
            line: 373,
            column: 3
          }
        },
        line: 371
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 375,
            column: 2
          },
          end: {
            line: 375,
            column: 3
          }
        },
        loc: {
          start: {
            line: 375,
            column: 69
          },
          end: {
            line: 382,
            column: 3
          }
        },
        line: 375
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 376,
            column: 21
          },
          end: {
            line: 376,
            column: 22
          }
        },
        loc: {
          start: {
            line: 376,
            column: 30
          },
          end: {
            line: 381,
            column: 5
          }
        },
        line: 376
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 386,
            column: 2
          },
          end: {
            line: 386,
            column: 3
          }
        },
        loc: {
          start: {
            line: 386,
            column: 71
          },
          end: {
            line: 421,
            column: 3
          }
        },
        line: 386
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 423,
            column: 2
          },
          end: {
            line: 423,
            column: 3
          }
        },
        loc: {
          start: {
            line: 423,
            column: 58
          },
          end: {
            line: 435,
            column: 3
          }
        },
        line: 423
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 437,
            column: 2
          },
          end: {
            line: 437,
            column: 3
          }
        },
        loc: {
          start: {
            line: 437,
            column: 37
          },
          end: {
            line: 444,
            column: 3
          }
        },
        line: 437
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 446,
            column: 2
          },
          end: {
            line: 446,
            column: 3
          }
        },
        loc: {
          start: {
            line: 446,
            column: 47
          },
          end: {
            line: 453,
            column: 3
          }
        },
        line: 446
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 244,
            column: 32
          },
          end: {
            line: 244,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 244,
            column: 32
          },
          end: {
            line: 244,
            column: 54
          }
        }, {
          start: {
            line: 244,
            column: 58
          },
          end: {
            line: 244,
            column: 59
          }
        }],
        line: 244
      },
      "1": {
        loc: {
          start: {
            line: 247,
            column: 11
          },
          end: {
            line: 247,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 247,
            column: 11
          },
          end: {
            line: 247,
            column: 73
          }
        }, {
          start: {
            line: 247,
            column: 77
          },
          end: {
            line: 247,
            column: 87
          }
        }],
        line: 247
      },
      "2": {
        loc: {
          start: {
            line: 311,
            column: 16
          },
          end: {
            line: 311,
            column: 89
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 311,
            column: 56
          },
          end: {
            line: 311,
            column: 71
          }
        }, {
          start: {
            line: 311,
            column: 74
          },
          end: {
            line: 311,
            column: 89
          }
        }],
        line: 311
      },
      "3": {
        loc: {
          start: {
            line: 312,
            column: 17
          },
          end: {
            line: 312,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 312,
            column: 57
          },
          end: {
            line: 312,
            column: 77
          }
        }, {
          start: {
            line: 312,
            column: 80
          },
          end: {
            line: 312,
            column: 100
          }
        }],
        line: 312
      },
      "4": {
        loc: {
          start: {
            line: 378,
            column: 20
          },
          end: {
            line: 378,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 378,
            column: 20
          },
          end: {
            line: 378,
            column: 50
          }
        }, {
          start: {
            line: 378,
            column: 54
          },
          end: {
            line: 378,
            column: 56
          }
        }],
        line: 378
      },
      "5": {
        loc: {
          start: {
            line: 379,
            column: 34
          },
          end: {
            line: 379,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 379,
            column: 34
          },
          end: {
            line: 379,
            column: 64
          }
        }, {
          start: {
            line: 379,
            column: 68
          },
          end: {
            line: 379,
            column: 70
          }
        }],
        line: 379
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8486e8a1ab404c7368e05163ca42a192668589a6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_bi4w3tskw = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_bi4w3tskw();
import { openAIService } from "./openai";
import { computerVisionService } from "./computerVision";
import { performanceAnalyticsService } from "./performanceAnalytics";
var AIAnalysisService = function () {
  function AIAnalysisService() {
    _classCallCheck(this, AIAnalysisService);
  }
  return _createClass(AIAnalysisService, [{
    key: "analyzeTrainingVideo",
    value: (function () {
      var _analyzeTrainingVideo = _asyncToGenerator(function* (videoUrl, userProfile) {
        cov_bi4w3tskw().f[0]++;
        cov_bi4w3tskw().s[0]++;
        try {
          cov_bi4w3tskw().s[1]++;
          console.log('Starting comprehensive AI analysis...');
          var visionAnalysis = (cov_bi4w3tskw().s[2]++, yield computerVisionService.analyzeVideoFrames(videoUrl));
          var coachingRequest = (cov_bi4w3tskw().s[3]++, {
            skillLevel: userProfile.skillLevel,
            recentSessions: userProfile.recentSessions,
            currentStats: userProfile.currentStats,
            context: this.buildAnalysisContext(visionAnalysis)
          });
          var aiCoaching = (cov_bi4w3tskw().s[4]++, yield openAIService.generateCoachingAdvice(coachingRequest));
          var mockMatchStats = (cov_bi4w3tskw().s[5]++, this.generateMatchStatsFromVideo(visionAnalysis));
          var performanceAnalysis = (cov_bi4w3tskw().s[6]++, performanceAnalyticsService.analyzeMatchPerformance(mockMatchStats));
          var recommendations = (cov_bi4w3tskw().s[7]++, this.generateIntegratedRecommendations(visionAnalysis, aiCoaching, performanceAnalysis.insights));
          var trainingPlan = (cov_bi4w3tskw().s[8]++, this.generatePersonalizedTrainingPlan(userProfile, visionAnalysis, aiCoaching));
          cov_bi4w3tskw().s[9]++;
          return {
            videoAnalysis: {
              overallScore: this.calculateOverallVideoScore(visionAnalysis.analysis),
              techniqueBreakdown: visionAnalysis.analysis,
              movementAnalysis: visionAnalysis.movements,
              highlights: visionAnalysis.highlights
            },
            aiCoaching: aiCoaching,
            performanceInsights: performanceAnalysis.insights,
            recommendations: recommendations,
            trainingPlan: trainingPlan
          };
        } catch (error) {
          cov_bi4w3tskw().s[10]++;
          console.error('AI Analysis error:', error);
          cov_bi4w3tskw().s[11]++;
          return this.getFallbackAnalysis(userProfile);
        }
      });
      function analyzeTrainingVideo(_x, _x2) {
        return _analyzeTrainingVideo.apply(this, arguments);
      }
      return analyzeTrainingVideo;
    }())
  }, {
    key: "generateRealTimeCoaching",
    value: (function () {
      var _generateRealTimeCoaching = _asyncToGenerator(function* (currentAction, recentPerformance, userProfile) {
        cov_bi4w3tskw().f[1]++;
        cov_bi4w3tskw().s[12]++;
        try {
          var currentTechnique = (cov_bi4w3tskw().s[13]++, this.analyzeLiveTechnique(currentAction));
          var suggestions = (cov_bi4w3tskw().s[14]++, yield this.generateLiveSuggestions(currentAction, recentPerformance, userProfile));
          var encouragement = (cov_bi4w3tskw().s[15]++, this.generateEncouragement(recentPerformance));
          var adaptiveCoaching = (cov_bi4w3tskw().s[16]++, this.generateAdaptiveCoaching(recentPerformance, userProfile));
          cov_bi4w3tskw().s[17]++;
          return {
            liveAnalysis: {
              currentTechnique: currentTechnique,
              suggestions: suggestions,
              encouragement: encouragement
            },
            adaptiveCoaching: adaptiveCoaching
          };
        } catch (error) {
          cov_bi4w3tskw().s[18]++;
          console.error('Real-time coaching error:', error);
          cov_bi4w3tskw().s[19]++;
          return this.getFallbackRealTimeCoaching();
        }
      });
      function generateRealTimeCoaching(_x3, _x4, _x5) {
        return _generateRealTimeCoaching.apply(this, arguments);
      }
      return generateRealTimeCoaching;
    }())
  }, {
    key: "analyzeMatchPerformance",
    value: (function () {
      var _analyzeMatchPerformance = _asyncToGenerator(function* (matchStats, opponentProfile, userProfile) {
        cov_bi4w3tskw().f[2]++;
        cov_bi4w3tskw().s[20]++;
        try {
          var performanceAnalysis = (cov_bi4w3tskw().s[21]++, performanceAnalyticsService.analyzeMatchPerformance(matchStats));
          var tacticalAnalysis = (cov_bi4w3tskw().s[22]++, performanceAnalyticsService.analyzeTacticalPerformance(matchStats, opponentProfile.style, opponentProfile.surface));
          var aiInsights = (cov_bi4w3tskw().s[23]++, yield openAIService.generateMatchStrategy(opponentProfile.style, userProfile.strengths, userProfile.weaknesses, opponentProfile.surface));
          cov_bi4w3tskw().s[24]++;
          return {
            matchAnalysis: performanceAnalysis,
            tacticalInsights: tacticalAnalysis.successfulTactics,
            improvementAreas: performanceAnalysis.insights.weaknesses,
            nextMatchStrategy: aiInsights
          };
        } catch (error) {
          cov_bi4w3tskw().s[25]++;
          console.error('Match analysis error:', error);
          cov_bi4w3tskw().s[26]++;
          return this.getFallbackMatchAnalysis();
        }
      });
      function analyzeMatchPerformance(_x6, _x7, _x8) {
        return _analyzeMatchPerformance.apply(this, arguments);
      }
      return analyzeMatchPerformance;
    }())
  }, {
    key: "generateTrainingRecommendations",
    value: (function () {
      var _generateTrainingRecommendations = _asyncToGenerator(function* (userProfile, recentSessions, goals) {
        cov_bi4w3tskw().f[3]++;
        cov_bi4w3tskw().s[27]++;
        try {
          var coachingRequest = (cov_bi4w3tskw().s[28]++, {
            skillLevel: userProfile.skillLevel,
            recentSessions: recentSessions.map(function (s) {
              cov_bi4w3tskw().f[4]++;
              cov_bi4w3tskw().s[29]++;
              return s.title;
            }),
            currentStats: userProfile.currentStats,
            context: `Goals: ${goals.join(', ')}`
          });
          var aiCoaching = (cov_bi4w3tskw().s[30]++, yield openAIService.generateCoachingAdvice(coachingRequest));
          var weeklyPlan = (cov_bi4w3tskw().s[31]++, this.generateWeeklyTrainingPlan(aiCoaching, userProfile));
          var skillFocus = (cov_bi4w3tskw().s[32]++, this.identifySkillFocus(userProfile.currentStats));
          var progressTargets = (cov_bi4w3tskw().s[33]++, this.generateProgressTargets(userProfile, goals));
          cov_bi4w3tskw().s[34]++;
          return {
            weeklyPlan: weeklyPlan,
            skillFocus: skillFocus,
            drillRecommendations: aiCoaching.recommendedDrills,
            progressTargets: progressTargets
          };
        } catch (error) {
          cov_bi4w3tskw().s[35]++;
          console.error('Training recommendations error:', error);
          cov_bi4w3tskw().s[36]++;
          return this.getFallbackTrainingRecommendations();
        }
      });
      function generateTrainingRecommendations(_x9, _x0, _x1) {
        return _generateTrainingRecommendations.apply(this, arguments);
      }
      return generateTrainingRecommendations;
    }())
  }, {
    key: "buildAnalysisContext",
    value: function buildAnalysisContext(visionAnalysis) {
      cov_bi4w3tskw().f[5]++;
      var avgScore = (cov_bi4w3tskw().s[37]++, visionAnalysis.analysis.reduce(function (sum, a) {
        cov_bi4w3tskw().f[6]++;
        cov_bi4w3tskw().s[38]++;
        return sum + a.overallScore;
      }, 0) / visionAnalysis.analysis.length);
      var dominantShotType = (cov_bi4w3tskw().s[39]++, this.findDominantShotType(visionAnalysis.analysis));
      var movementQuality = (cov_bi4w3tskw().s[40]++, visionAnalysis.movements.reduce(function (sum, m) {
        cov_bi4w3tskw().f[7]++;
        cov_bi4w3tskw().s[41]++;
        return sum + m.quality;
      }, 0) / visionAnalysis.movements.length);
      cov_bi4w3tskw().s[42]++;
      return `Video analysis shows ${avgScore.toFixed(0)}% technique score, primarily ${dominantShotType} shots, with ${movementQuality.toFixed(0)}% movement quality.`;
    }
  }, {
    key: "findDominantShotType",
    value: function findDominantShotType(analysis) {
      var _Object$entries$sort$;
      cov_bi4w3tskw().f[8]++;
      var shotCounts = (cov_bi4w3tskw().s[43]++, {});
      cov_bi4w3tskw().s[44]++;
      analysis.forEach(function (a) {
        cov_bi4w3tskw().f[9]++;
        cov_bi4w3tskw().s[45]++;
        shotCounts[a.shotType] = ((cov_bi4w3tskw().b[0][0]++, shotCounts[a.shotType]) || (cov_bi4w3tskw().b[0][1]++, 0)) + 1;
      });
      cov_bi4w3tskw().s[46]++;
      return (cov_bi4w3tskw().b[1][0]++, (_Object$entries$sort$ = Object.entries(shotCounts).sort(function (_ref, _ref2) {
        var _ref3 = _slicedToArray(_ref, 2),
          a = _ref3[1];
        var _ref4 = _slicedToArray(_ref2, 2),
          b = _ref4[1];
        cov_bi4w3tskw().f[10]++;
        cov_bi4w3tskw().s[47]++;
        return b - a;
      })[0]) == null ? void 0 : _Object$entries$sort$[0]) || (cov_bi4w3tskw().b[1][1]++, 'forehand');
    }
  }, {
    key: "generateMatchStatsFromVideo",
    value: function generateMatchStatsFromVideo(visionAnalysis) {
      cov_bi4w3tskw().f[11]++;
      cov_bi4w3tskw().s[48]++;
      return {
        totalPoints: 50,
        pointsWon: 32,
        winners: 8,
        unforcedErrors: 12,
        forcedErrors: 6,
        aces: 3,
        doubleFaults: 2,
        firstServePercentage: 68,
        firstServePointsWon: 22,
        secondServePointsWon: 8,
        breakPointsConverted: 3,
        breakPointsTotal: 5,
        netApproaches: 6,
        netPointsWon: 4,
        returnPointsWon: 15,
        totalGameTime: 45
      };
    }
  }, {
    key: "calculateOverallVideoScore",
    value: function calculateOverallVideoScore(analysis) {
      cov_bi4w3tskw().f[12]++;
      cov_bi4w3tskw().s[49]++;
      return Math.round(analysis.reduce(function (sum, a) {
        cov_bi4w3tskw().f[13]++;
        cov_bi4w3tskw().s[50]++;
        return sum + a.overallScore;
      }, 0) / analysis.length);
    }
  }, {
    key: "generateIntegratedRecommendations",
    value: function generateIntegratedRecommendations(visionAnalysis, aiCoaching, performanceInsights) {
      cov_bi4w3tskw().f[14]++;
      cov_bi4w3tskw().s[51]++;
      return {
        immediate: [aiCoaching.personalizedTip].concat(_toConsumableArray(performanceInsights.recommendations.slice(0, 2))),
        shortTerm: [].concat(_toConsumableArray(aiCoaching.technicalFeedback.slice(0, 2)), _toConsumableArray(performanceInsights.nextTrainingFocus.slice(0, 2))),
        longTerm: [aiCoaching.improvementPlan, 'Develop consistent match-play experience', 'Work on mental game and pressure situations']
      };
    }
  }, {
    key: "generatePersonalizedTrainingPlan",
    value: function generatePersonalizedTrainingPlan(userProfile, visionAnalysis, aiCoaching) {
      cov_bi4w3tskw().f[15]++;
      var weakestAreas = (cov_bi4w3tskw().s[52]++, this.identifyWeakestAreas(userProfile.currentStats));
      cov_bi4w3tskw().s[53]++;
      return {
        focus: [].concat(_toConsumableArray(weakestAreas.slice(0, 2)), ['Overall consistency']),
        drills: aiCoaching.recommendedDrills,
        duration: userProfile.skillLevel === 'beginner' ? (cov_bi4w3tskw().b[2][0]++, '30-45 minutes') : (cov_bi4w3tskw().b[2][1]++, '60-90 minutes'),
        frequency: userProfile.skillLevel === 'advanced' ? (cov_bi4w3tskw().b[3][0]++, '5-6 times per week') : (cov_bi4w3tskw().b[3][1]++, '3-4 times per week')
      };
    }
  }, {
    key: "identifyWeakestAreas",
    value: function identifyWeakestAreas(currentStats) {
      cov_bi4w3tskw().f[16]++;
      cov_bi4w3tskw().s[54]++;
      return Object.entries(currentStats).sort(function (_ref5, _ref6) {
        var _ref7 = _slicedToArray(_ref5, 2),
          a = _ref7[1];
        var _ref8 = _slicedToArray(_ref6, 2),
          b = _ref8[1];
        cov_bi4w3tskw().f[17]++;
        cov_bi4w3tskw().s[55]++;
        return a - b;
      }).slice(0, 3).map(function (_ref9) {
        var _ref0 = _slicedToArray(_ref9, 1),
          skill = _ref0[0];
        cov_bi4w3tskw().f[18]++;
        cov_bi4w3tskw().s[56]++;
        return skill.replace('_', ' ');
      });
    }
  }, {
    key: "analyzeLiveTechnique",
    value: function analyzeLiveTechnique(currentAction) {
      cov_bi4w3tskw().f[19]++;
      cov_bi4w3tskw().s[57]++;
      return `Analyzing ${currentAction} technique...`;
    }
  }, {
    key: "generateLiveSuggestions",
    value: function () {
      var _generateLiveSuggestions = _asyncToGenerator(function* (currentAction, recentPerformance, userProfile) {
        cov_bi4w3tskw().f[20]++;
        cov_bi4w3tskw().s[58]++;
        return ['Keep your eye on the ball', 'Follow through completely', 'Stay balanced on your feet'];
      });
      function generateLiveSuggestions(_x10, _x11, _x12) {
        return _generateLiveSuggestions.apply(this, arguments);
      }
      return generateLiveSuggestions;
    }()
  }, {
    key: "generateEncouragement",
    value: function generateEncouragement(recentPerformance) {
      cov_bi4w3tskw().f[21]++;
      var positiveMessages = (cov_bi4w3tskw().s[59]++, ['Great improvement in your technique!', 'Your consistency is getting better!', 'Excellent focus and determination!', 'You\'re making solid progress!']);
      cov_bi4w3tskw().s[60]++;
      return positiveMessages[Math.floor(Math.random() * positiveMessages.length)];
    }
  }, {
    key: "generateAdaptiveCoaching",
    value: function generateAdaptiveCoaching(recentPerformance, userProfile) {
      cov_bi4w3tskw().f[22]++;
      cov_bi4w3tskw().s[61]++;
      return {
        adjustments: ['Increase practice intensity', 'Focus more on weak areas'],
        nextFocus: 'Consistency and placement'
      };
    }
  }, {
    key: "generateWeeklyTrainingPlan",
    value: function generateWeeklyTrainingPlan(aiCoaching, userProfile) {
      var _aiCoaching$recommend;
      cov_bi4w3tskw().f[23]++;
      cov_bi4w3tskw().s[62]++;
      return {
        monday: 'Technique focus - ' + aiCoaching.technicalFeedback[0],
        wednesday: 'Drill practice - ' + ((_aiCoaching$recommend = aiCoaching.recommendedDrills[0]) == null ? void 0 : _aiCoaching$recommend.name),
        friday: 'Match simulation',
        weekend: 'Physical conditioning'
      };
    }
  }, {
    key: "identifySkillFocus",
    value: function identifySkillFocus(currentStats) {
      cov_bi4w3tskw().f[24]++;
      cov_bi4w3tskw().s[63]++;
      return this.identifyWeakestAreas(currentStats);
    }
  }, {
    key: "generateProgressTargets",
    value: function generateProgressTargets(userProfile, goals) {
      cov_bi4w3tskw().f[25]++;
      cov_bi4w3tskw().s[64]++;
      return goals.map(function (goal) {
        cov_bi4w3tskw().f[26]++;
        cov_bi4w3tskw().s[65]++;
        return {
          goal: goal,
          currentLevel: (cov_bi4w3tskw().b[4][0]++, userProfile.currentStats[goal]) || (cov_bi4w3tskw().b[4][1]++, 50),
          targetLevel: Math.min(100, ((cov_bi4w3tskw().b[5][0]++, userProfile.currentStats[goal]) || (cov_bi4w3tskw().b[5][1]++, 50)) + 15),
          timeframe: '4 weeks'
        };
      });
    }
  }, {
    key: "getFallbackAnalysis",
    value: function getFallbackAnalysis(userProfile) {
      cov_bi4w3tskw().f[27]++;
      cov_bi4w3tskw().s[66]++;
      return {
        videoAnalysis: {
          overallScore: 75,
          techniqueBreakdown: [],
          movementAnalysis: [],
          highlights: []
        },
        aiCoaching: {
          personalizedTip: 'Focus on consistent practice',
          technicalFeedback: ['Work on fundamentals'],
          strategicAdvice: 'Play to your strengths',
          mentalGameTips: 'Stay focused',
          recommendedDrills: [],
          improvementPlan: 'Practice regularly'
        },
        performanceInsights: {
          strengths: ['Determination'],
          weaknesses: ['Consistency'],
          recommendations: ['Practice more'],
          nextTrainingFocus: ['Technique'],
          competitiveReadiness: 70
        },
        recommendations: {
          immediate: ['Focus on basics'],
          shortTerm: ['Build consistency'],
          longTerm: ['Develop match experience']
        },
        trainingPlan: {
          focus: ['Fundamentals'],
          drills: [],
          duration: '45 minutes',
          frequency: '3 times per week'
        }
      };
    }
  }, {
    key: "getFallbackRealTimeCoaching",
    value: function getFallbackRealTimeCoaching() {
      cov_bi4w3tskw().f[28]++;
      cov_bi4w3tskw().s[67]++;
      return {
        liveAnalysis: {
          currentTechnique: 'Good form',
          suggestions: ['Keep practicing'],
          encouragement: 'You\'re doing great!'
        },
        adaptiveCoaching: {
          adjustments: ['Continue current approach'],
          nextFocus: 'Consistency'
        }
      };
    }
  }, {
    key: "getFallbackMatchAnalysis",
    value: function getFallbackMatchAnalysis() {
      cov_bi4w3tskw().f[29]++;
      cov_bi4w3tskw().s[68]++;
      return {
        matchAnalysis: {
          overallRating: 70
        },
        tacticalInsights: ['Good baseline play'],
        improvementAreas: ['Net game'],
        nextMatchStrategy: 'Play consistent tennis'
      };
    }
  }, {
    key: "getFallbackTrainingRecommendations",
    value: function getFallbackTrainingRecommendations() {
      cov_bi4w3tskw().f[30]++;
      cov_bi4w3tskw().s[69]++;
      return {
        weeklyPlan: {
          monday: 'Practice',
          wednesday: 'Drills',
          friday: 'Match play'
        },
        skillFocus: ['Consistency'],
        drillRecommendations: [],
        progressTargets: []
      };
    }
  }]);
}();
export var aiAnalysisService = (cov_bi4w3tskw().s[70]++, new AIAnalysisService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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