{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "React", "_StyleSheet", "_View", "RootTagContext", "createContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "props", "forwardedRef", "children", "WrapperComponent", "innerView", "createElement", "key", "style", "styles", "appContainer", "Provider", "value", "rootTag", "ref", "displayName", "_default", "create", "flex", "pointerEvents", "module"], "sources": ["AppContainer.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _View = _interopRequireDefault(require(\"../View\"));\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar RootTagContext = /*#__PURE__*/React.createContext(null);\nvar AppContainer = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var children = props.children,\n    WrapperComponent = props.WrapperComponent;\n  var innerView = /*#__PURE__*/React.createElement(_View.default, {\n    children: children,\n    key: 1,\n    style: styles.appContainer\n  });\n  if (WrapperComponent) {\n    innerView = /*#__PURE__*/React.createElement(WrapperComponent, null, innerView);\n  }\n  return /*#__PURE__*/React.createElement(RootTagContext.Provider, {\n    value: props.rootTag\n  }, /*#__PURE__*/React.createElement(_View.default, {\n    ref: forwardedRef,\n    style: styles.appContainer\n  }, innerView));\n});\nAppContainer.displayName = 'AppContainer';\nvar _default = exports.default = AppContainer;\nvar styles = _StyleSheet.default.create({\n  appContainer: {\n    flex: 1,\n    pointerEvents: 'box-none'\n  }\n});\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,KAAK,GAAGH,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIM,WAAW,GAAGP,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIO,KAAK,GAAGR,sBAAsB,CAACC,OAAO,UAAU,CAAC,CAAC;AAWtD,IAAIQ,cAAc,GAAgBH,KAAK,CAACI,aAAa,CAAC,IAAI,CAAC;AAC3D,IAAIC,YAAY,GAAgBL,KAAK,CAACM,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EACxE,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,gBAAgB,GAAGH,KAAK,CAACG,gBAAgB;EAC3C,IAAIC,SAAS,GAAgBX,KAAK,CAACY,aAAa,CAACV,KAAK,CAACN,OAAO,EAAE;IAC9Da,QAAQ,EAAEA,QAAQ;IAClBI,GAAG,EAAE,CAAC;IACNC,KAAK,EAAEC,MAAM,CAACC;EAChB,CAAC,CAAC;EACF,IAAIN,gBAAgB,EAAE;IACpBC,SAAS,GAAgBX,KAAK,CAACY,aAAa,CAACF,gBAAgB,EAAE,IAAI,EAAEC,SAAS,CAAC;EACjF;EACA,OAAoBX,KAAK,CAACY,aAAa,CAACT,cAAc,CAACc,QAAQ,EAAE;IAC/DC,KAAK,EAAEX,KAAK,CAACY;EACf,CAAC,EAAenB,KAAK,CAACY,aAAa,CAACV,KAAK,CAACN,OAAO,EAAE;IACjDwB,GAAG,EAAEZ,YAAY;IACjBM,KAAK,EAAEC,MAAM,CAACC;EAChB,CAAC,EAAEL,SAAS,CAAC,CAAC;AAChB,CAAC,CAAC;AACFN,YAAY,CAACgB,WAAW,GAAG,cAAc;AACzC,IAAIC,QAAQ,GAAGxB,OAAO,CAACF,OAAO,GAAGS,YAAY;AAC7C,IAAIU,MAAM,GAAGd,WAAW,CAACL,OAAO,CAAC2B,MAAM,CAAC;EACtCP,YAAY,EAAE;IACZQ,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AACFC,MAAM,CAAC5B,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}