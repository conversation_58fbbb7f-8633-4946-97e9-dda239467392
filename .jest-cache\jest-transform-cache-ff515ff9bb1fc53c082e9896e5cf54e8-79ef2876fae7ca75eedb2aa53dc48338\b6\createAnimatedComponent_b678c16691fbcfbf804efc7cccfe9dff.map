{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "createAnimatedComponent", "_extends2", "_objectWithoutPropertiesLoose2", "_useAnimatedProps2", "_useMergeRefs", "_StyleSheet", "_View", "React", "_excluded", "Component", "forwardRef", "props", "forwardedRef", "_useAnimatedProps", "reducedProps", "callback<PERSON><PERSON>", "ref", "passthroughAnimatedPropExplicitValues", "style", "_ref", "passthroughStyle", "passthroughProps", "mergedStyle", "createElement", "module"], "sources": ["createAnimatedComponent.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = createAnimatedComponent;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _useAnimatedProps2 = _interopRequireDefault(require(\"./useAnimatedProps\"));\nvar _useMergeRefs = _interopRequireDefault(require(\"../Utilities/useMergeRefs\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../../../exports/StyleSheet\"));\nvar _View = _interopRequireDefault(require(\"../../../exports/View\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _excluded = [\"style\"];\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n/**\n * Experimental implementation of `createAnimatedComponent` that is intended to\n * be compatible with concurrent rendering.\n */\nfunction createAnimatedComponent(Component) {\n  return /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n    var _useAnimatedProps = (0, _useAnimatedProps2.default)(props),\n      reducedProps = _useAnimatedProps[0],\n      callbackRef = _useAnimatedProps[1];\n    var ref = (0, _useMergeRefs.default)(callbackRef, forwardedRef);\n\n    // Some components require explicit passthrough values for animation\n    // to work properly. For example, if an animated component is\n    // transformed and Pressable, onPress will not work after transform\n    // without these passthrough values.\n    // $FlowFixMe[prop-missing]\n    var passthroughAnimatedPropExplicitValues = reducedProps.passthroughAnimatedPropExplicitValues,\n      style = reducedProps.style;\n    var _ref = passthroughAnimatedPropExplicitValues !== null && passthroughAnimatedPropExplicitValues !== void 0 ? passthroughAnimatedPropExplicitValues : {},\n      passthroughStyle = _ref.style,\n      passthroughProps = (0, _objectWithoutPropertiesLoose2.default)(_ref, _excluded);\n    var mergedStyle = [style, passthroughStyle];\n    return /*#__PURE__*/React.createElement(Component, (0, _extends2.default)({}, reducedProps, passthroughProps, {\n      style: mergedStyle,\n      ref: ref\n    }));\n  });\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAGI,uBAAuB;AACzC,IAAIC,SAAS,GAAGP,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIO,8BAA8B,GAAGR,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIQ,kBAAkB,GAAGT,sBAAsB,CAACC,OAAO,qBAAqB,CAAC,CAAC;AAC9E,IAAIS,aAAa,GAAGV,sBAAsB,CAACC,OAAO,4BAA4B,CAAC,CAAC;AAChF,IAAIU,WAAW,GAAGX,sBAAsB,CAACC,OAAO,8BAA8B,CAAC,CAAC;AAChF,IAAIW,KAAK,GAAGZ,sBAAsB,CAACC,OAAO,wBAAwB,CAAC,CAAC;AACpE,IAAIY,KAAK,GAAGV,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIa,SAAS,GAAG,CAAC,OAAO,CAAC;AAczB,SAASR,uBAAuBA,CAACS,SAAS,EAAE;EAC1C,OAAoBF,KAAK,CAACG,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;IAC5D,IAAIC,iBAAiB,GAAG,CAAC,CAAC,EAAEV,kBAAkB,CAACP,OAAO,EAAEe,KAAK,CAAC;MAC5DG,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MACnCE,WAAW,GAAGF,iBAAiB,CAAC,CAAC,CAAC;IACpC,IAAIG,GAAG,GAAG,CAAC,CAAC,EAAEZ,aAAa,CAACR,OAAO,EAAEmB,WAAW,EAAEH,YAAY,CAAC;IAO/D,IAAIK,qCAAqC,GAAGH,YAAY,CAACG,qCAAqC;MAC5FC,KAAK,GAAGJ,YAAY,CAACI,KAAK;IAC5B,IAAIC,IAAI,GAAGF,qCAAqC,KAAK,IAAI,IAAIA,qCAAqC,KAAK,KAAK,CAAC,GAAGA,qCAAqC,GAAG,CAAC,CAAC;MACxJG,gBAAgB,GAAGD,IAAI,CAACD,KAAK;MAC7BG,gBAAgB,GAAG,CAAC,CAAC,EAAEnB,8BAA8B,CAACN,OAAO,EAAEuB,IAAI,EAAEX,SAAS,CAAC;IACjF,IAAIc,WAAW,GAAG,CAACJ,KAAK,EAAEE,gBAAgB,CAAC;IAC3C,OAAoBb,KAAK,CAACgB,aAAa,CAACd,SAAS,EAAE,CAAC,CAAC,EAAER,SAAS,CAACL,OAAO,EAAE,CAAC,CAAC,EAAEkB,YAAY,EAAEO,gBAAgB,EAAE;MAC5GH,KAAK,EAAEI,WAAW;MAClBN,GAAG,EAAEA;IACP,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AACAQ,MAAM,CAAC1B,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}