/**
 * Migration Demo Component
 * Demonstrates the migration from Supabase to Firebase and OpenAI to DeepSeek
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  TextInput,
} from 'react-native';
import { firebaseDatabaseService } from '@/services/database/FirebaseDatabaseService';
import { deepSeekService } from '@/services/deepseek';
import { authService } from '@/services/auth/AuthService';

export default function MigrationDemo() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [userProfile, setUserProfile] = useState<any>(null);
  const [aiResponse, setAiResponse] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Subscribe to auth state changes
    const unsubscribe = authService.onAuthStateChange((state) => {
      setUserProfile(state.profile);
    });

    return unsubscribe;
  }, []);

  // Firebase Authentication Examples
  const handleFirebaseSignUp = async () => {
    setIsLoading(true);
    try {
      const result = await authService.signUp({
        email,
        password,
        fullName: 'Test User',
        tennisLevel: 'intermediate',
      });

      if (result.success) {
        Alert.alert('Success', 'User created successfully with Firebase Auth!');
      } else {
        Alert.alert('Error', result.error || 'Sign up failed');
      }
    } catch (error) {
      Alert.alert('Error', 'Firebase sign up failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFirebaseSignIn = async () => {
    setIsLoading(true);
    try {
      const result = await authService.signIn({ email, password });

      if (result.success) {
        Alert.alert('Success', 'Signed in successfully with Firebase Auth!');
      } else {
        Alert.alert('Error', result.error || 'Sign in failed');
      }
    } catch (error) {
      Alert.alert('Error', 'Firebase sign in failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFirebaseSignOut = async () => {
    setIsLoading(true);
    try {
      const result = await authService.signOut();

      if (result.success) {
        Alert.alert('Success', 'Signed out successfully!');
      } else {
        Alert.alert('Error', result.error || 'Sign out failed');
      }
    } catch (error) {
      Alert.alert('Error', 'Firebase sign out failed');
    } finally {
      setIsLoading(false);
    }
  };

  // Firebase Firestore Database Examples
  const handleFirestoreCreate = async () => {
    setIsLoading(true);
    try {
      const result = await firebaseDatabaseService.query('training_sessions', 'insert', {
        data: {
          title: 'Firebase Demo Session',
          session_type: 'technique',
          overall_score: 85,
          duration_minutes: 60,
          improvement_areas: ['forehand', 'footwork'],
          user_id: userProfile?.id || 'demo-user',
        },
      });

      if (result.data) {
        Alert.alert('Success', 'Training session created in Firestore!');
      } else {
        Alert.alert('Error', result.error?.message || 'Failed to create session');
      }
    } catch (error) {
      Alert.alert('Error', 'Firestore create failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFirestoreRead = async () => {
    setIsLoading(true);
    try {
      const result = await firebaseDatabaseService.query('training_sessions', 'select', {
        filter: { user_id: userProfile?.id || 'demo-user' },
        orderBy: [{ field: 'created_at', direction: 'desc' }],
        limit: 5,
      });

      if (result.data) {
        const sessions = Array.isArray(result.data) ? result.data : [result.data];
        Alert.alert(
          'Firestore Data',
          `Found ${sessions.length} training sessions:\n${sessions
            .map((s: any) => `• ${s.title}`)
            .join('\n')}`
        );
      } else {
        Alert.alert('Info', 'No training sessions found');
      }
    } catch (error) {
      Alert.alert('Error', 'Firestore read failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFirestoreUpdate = async () => {
    setIsLoading(true);
    try {
      // First, get a session to update
      const readResult = await firebaseDatabaseService.query('training_sessions', 'select', {
        filter: { user_id: userProfile?.id || 'demo-user' },
        limit: 1,
      });

      if (readResult.data && Array.isArray(readResult.data) && readResult.data.length > 0) {
        const session = readResult.data[0];
        
        const updateResult = await firebaseDatabaseService.query('training_sessions', 'update', {
          id: session.id,
          data: {
            overall_score: 90,
            notes: 'Updated via Firebase migration demo',
          },
        });

        if (updateResult.data) {
          Alert.alert('Success', 'Training session updated in Firestore!');
        } else {
          Alert.alert('Error', updateResult.error?.message || 'Failed to update session');
        }
      } else {
        Alert.alert('Info', 'No sessions found to update. Create one first.');
      }
    } catch (error) {
      Alert.alert('Error', 'Firestore update failed');
    } finally {
      setIsLoading(false);
    }
  };

  // DeepSeek AI Examples
  const handleDeepSeekCoaching = async () => {
    setIsLoading(true);
    try {
      const coachingAdvice = await deepSeekService.generateCoachingAdvice({
        skillLevel: 'intermediate',
        recentSessions: ['Forehand practice', 'Serve training'],
        currentStats: {
          forehand: 75,
          backhand: 65,
          serve: 80,
          volley: 60,
          footwork: 70,
          strategy: 68,
          mental_game: 72,
        },
        context: 'Preparing for tournament',
      });

      setAiResponse(coachingAdvice.personalizedTip);
      Alert.alert('DeepSeek Coaching', coachingAdvice.personalizedTip);
    } catch (error) {
      Alert.alert('Error', 'DeepSeek coaching request failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeepSeekTips = async () => {
    setIsLoading(true);
    try {
      const tips = await deepSeekService.generateAITips(
        'technique',
        'intermediate',
        'forehand consistency'
      );

      const tipsText = tips.join('\n• ');
      setAiResponse(`Tennis Tips:\n• ${tipsText}`);
      Alert.alert('DeepSeek Tips', `Tennis Tips:\n• ${tipsText}`);
    } catch (error) {
      Alert.alert('Error', 'DeepSeek tips request failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeepSeekVideoAnalysis = async () => {
    setIsLoading(true);
    try {
      const analysis = await deepSeekService.analyzeVideoTechnique({
        videoDescription: 'Player performing forehand strokes',
        detectedMovements: ['forehand_preparation', 'contact_point', 'follow_through'],
        skillLevel: 'intermediate',
      });

      const analysisText = `Score: ${analysis.overallScore}/100\n\nStrengths:\n• ${analysis.strengths.join('\n• ')}\n\nImprovements:\n• ${analysis.improvements.join('\n• ')}`;
      setAiResponse(analysisText);
      Alert.alert('DeepSeek Analysis', analysisText);
    } catch (error) {
      Alert.alert('Error', 'DeepSeek video analysis failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🔄 Firebase & DeepSeek Migration Demo</Text>
      
      {/* Authentication Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🔐 Firebase Authentication</Text>
        <Text style={styles.sectionDescription}>
          Replaces Supabase Auth with Firebase Auth
        </Text>
        
        <TextInput
          style={styles.input}
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
        />
        
        <TextInput
          style={styles.input}
          placeholder="Password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
        />
        
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={[styles.button, styles.primaryButton]} 
            onPress={handleFirebaseSignUp}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>Sign Up</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.primaryButton]} 
            onPress={handleFirebaseSignIn}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>Sign In</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.secondaryButton]} 
            onPress={handleFirebaseSignOut}
            disabled={isLoading}
          >
            <Text style={styles.secondaryButtonText}>Sign Out</Text>
          </TouchableOpacity>
        </View>
        
        {userProfile && (
          <View style={styles.userInfo}>
            <Text style={styles.userInfoText}>
              Logged in as: {userProfile.email}
            </Text>
            <Text style={styles.userInfoText}>
              Level: {userProfile.skill_level}
            </Text>
          </View>
        )}
      </View>

      {/* Database Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🔥 Firebase Firestore</Text>
        <Text style={styles.sectionDescription}>
          Replaces Supabase Database with Firestore
        </Text>
        
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={[styles.button, styles.primaryButton]} 
            onPress={handleFirestoreCreate}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>Create</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.primaryButton]} 
            onPress={handleFirestoreRead}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>Read</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.primaryButton]} 
            onPress={handleFirestoreUpdate}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>Update</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* AI Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🤖 DeepSeek AI</Text>
        <Text style={styles.sectionDescription}>
          Replaces OpenAI with DeepSeek API
        </Text>
        
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={[styles.button, styles.aiButton]} 
            onPress={handleDeepSeekCoaching}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>Coaching</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.aiButton]} 
            onPress={handleDeepSeekTips}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>Tips</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.aiButton]} 
            onPress={handleDeepSeekVideoAnalysis}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>Analysis</Text>
          </TouchableOpacity>
        </View>
        
        {aiResponse && (
          <View style={styles.responseContainer}>
            <Text style={styles.responseTitle}>AI Response:</Text>
            <Text style={styles.responseText}>{aiResponse}</Text>
          </View>
        )}
      </View>

      {/* Migration Status */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>✅ Migration Status</Text>
        
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Authentication:</Text>
          <Text style={styles.statusValue}>Supabase → Firebase ✅</Text>
        </View>
        
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Database:</Text>
          <Text style={styles.statusValue}>Supabase → Firestore ✅</Text>
        </View>
        
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>AI/LLM:</Text>
          <Text style={styles.statusValue}>OpenAI → DeepSeek ✅</Text>
        </View>
        
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Video Analysis:</Text>
          <Text style={styles.statusValue}>Replicate → MediaPipe ✅</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f8f9fa',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  section: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 10,
    flexWrap: 'wrap',
  },
  button: {
    flex: 1,
    minWidth: 80,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  secondaryButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  aiButton: {
    backgroundColor: '#FF6B35',
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#007AFF',
    fontSize: 14,
    fontWeight: '600',
  },
  userInfo: {
    marginTop: 15,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 8,
  },
  userInfoText: {
    color: '#2d5a2d',
    fontSize: 14,
    marginBottom: 4,
  },
  responseContainer: {
    marginTop: 15,
    padding: 15,
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
  },
  responseTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  responseText: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  statusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  statusLabel: {
    fontSize: 14,
    color: '#666',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#28a745',
  },
});
