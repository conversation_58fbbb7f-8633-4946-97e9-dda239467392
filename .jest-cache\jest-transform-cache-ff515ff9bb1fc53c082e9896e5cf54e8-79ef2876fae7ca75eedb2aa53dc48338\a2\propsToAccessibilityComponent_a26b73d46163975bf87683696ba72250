2a494ce47f2810e3de8abd350f59db37
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _propsToAriaRole = _interopRequireDefault(require("./propsToAriaRole"));
var roleComponents = {
  article: 'article',
  banner: 'header',
  blockquote: 'blockquote',
  button: 'button',
  code: 'code',
  complementary: 'aside',
  contentinfo: 'footer',
  deletion: 'del',
  emphasis: 'em',
  figure: 'figure',
  insertion: 'ins',
  form: 'form',
  list: 'ul',
  listitem: 'li',
  main: 'main',
  navigation: 'nav',
  paragraph: 'p',
  region: 'section',
  strong: 'strong'
};
var emptyObject = {};
var propsToAccessibilityComponent = function propsToAccessibilityComponent(props) {
  if (props === void 0) {
    props = emptyObject;
  }
  var roleProp = props.role || props.accessibilityRole;
  if (roleProp === 'label') {
    return 'label';
  }
  var role = (0, _propsToAriaRole.default)(props);
  if (role) {
    if (role === 'heading') {
      var level = props.accessibilityLevel || props['aria-level'];
      if (level != null) {
        return "h" + level;
      }
      return 'h1';
    }
    return roleComponents[role];
  }
};
var _default = exports.default = propsToAccessibilityComponent;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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