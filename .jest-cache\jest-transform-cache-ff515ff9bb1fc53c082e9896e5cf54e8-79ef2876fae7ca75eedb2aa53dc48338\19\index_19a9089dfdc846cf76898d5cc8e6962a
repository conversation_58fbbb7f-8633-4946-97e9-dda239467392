09cab125190f3e98792ecbe1af81fa51
"use strict";
'use client';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _Dimensions = _interopRequireDefault(require("../Dimensions"));
var _dismissKeyboard = _interopRequireDefault(require("../../modules/dismissKeyboard"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var _mergeRefs = _interopRequireDefault(require("../../modules/mergeRefs"));
var _Platform = _interopRequireDefault(require("../Platform"));
var _ScrollViewBase = _interopRequireDefault(require("./ScrollViewBase"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _TextInputState = _interopRequireDefault(require("../../modules/TextInputState"));
var _UIManager = _interopRequireDefault(require("../UIManager"));
var _View = _interopRequireDefault(require("../View"));
var _react = _interopRequireDefault(require("react"));
var _warning = _interopRequireDefault(require("fbjs/lib/warning"));
var _excluded = ["contentContainerStyle", "horizontal", "onContentSizeChange", "refreshControl", "stickyHeaderIndices", "pagingEnabled", "forwardedRef", "keyboardDismissMode", "onScroll", "centerContent"];
var emptyObject = {};
var IS_ANIMATING_TOUCH_START_THRESHOLD_MS = 16;
var ScrollView = function (_react$default$Compon) {
  function ScrollView() {
    var _this;
    (0, _classCallCheck2.default)(this, ScrollView);
    _this = _callSuper(this, ScrollView, arguments);
    _this._scrollNodeRef = null;
    _this._innerViewRef = null;
    _this.isTouching = false;
    _this.lastMomentumScrollBeginTime = 0;
    _this.lastMomentumScrollEndTime = 0;
    _this.observedScrollSinceBecomingResponder = false;
    _this.becameResponderWhileAnimating = false;
    _this.scrollResponderHandleScrollShouldSetResponder = function () {
      return _this.isTouching;
    };
    _this.scrollResponderHandleStartShouldSetResponderCapture = function (e) {
      return _this.scrollResponderIsAnimating();
    };
    _this.scrollResponderHandleTerminationRequest = function () {
      return !_this.observedScrollSinceBecomingResponder;
    };
    _this.scrollResponderHandleTouchEnd = function (e) {
      var nativeEvent = e.nativeEvent;
      _this.isTouching = nativeEvent.touches.length !== 0;
      _this.props.onTouchEnd && _this.props.onTouchEnd(e);
    };
    _this.scrollResponderHandleResponderRelease = function (e) {
      _this.props.onResponderRelease && _this.props.onResponderRelease(e);
      var currentlyFocusedTextInput = _TextInputState.default.currentlyFocusedField();
      if (!_this.props.keyboardShouldPersistTaps && currentlyFocusedTextInput != null && e.target !== currentlyFocusedTextInput && !_this.observedScrollSinceBecomingResponder && !_this.becameResponderWhileAnimating) {
        _this.props.onScrollResponderKeyboardDismissed && _this.props.onScrollResponderKeyboardDismissed(e);
        _TextInputState.default.blurTextInput(currentlyFocusedTextInput);
      }
    };
    _this.scrollResponderHandleScroll = function (e) {
      _this.observedScrollSinceBecomingResponder = true;
      _this.props.onScroll && _this.props.onScroll(e);
    };
    _this.scrollResponderHandleResponderGrant = function (e) {
      _this.observedScrollSinceBecomingResponder = false;
      _this.props.onResponderGrant && _this.props.onResponderGrant(e);
      _this.becameResponderWhileAnimating = _this.scrollResponderIsAnimating();
    };
    _this.scrollResponderHandleScrollBeginDrag = function (e) {
      _this.props.onScrollBeginDrag && _this.props.onScrollBeginDrag(e);
    };
    _this.scrollResponderHandleScrollEndDrag = function (e) {
      _this.props.onScrollEndDrag && _this.props.onScrollEndDrag(e);
    };
    _this.scrollResponderHandleMomentumScrollBegin = function (e) {
      _this.lastMomentumScrollBeginTime = Date.now();
      _this.props.onMomentumScrollBegin && _this.props.onMomentumScrollBegin(e);
    };
    _this.scrollResponderHandleMomentumScrollEnd = function (e) {
      _this.lastMomentumScrollEndTime = Date.now();
      _this.props.onMomentumScrollEnd && _this.props.onMomentumScrollEnd(e);
    };
    _this.scrollResponderHandleTouchStart = function (e) {
      _this.isTouching = true;
      _this.props.onTouchStart && _this.props.onTouchStart(e);
    };
    _this.scrollResponderHandleTouchMove = function (e) {
      _this.props.onTouchMove && _this.props.onTouchMove(e);
    };
    _this.scrollResponderIsAnimating = function () {
      var now = Date.now();
      var timeSinceLastMomentumScrollEnd = now - _this.lastMomentumScrollEndTime;
      var isAnimating = timeSinceLastMomentumScrollEnd < IS_ANIMATING_TOUCH_START_THRESHOLD_MS || _this.lastMomentumScrollEndTime < _this.lastMomentumScrollBeginTime;
      return isAnimating;
    };
    _this.scrollResponderScrollTo = function (x, y, animated) {
      if (typeof x === 'number') {
        console.warn('`scrollResponderScrollTo(x, y, animated)` is deprecated. Use `scrollResponderScrollTo({x: 5, y: 5, animated: true})` instead.');
      } else {
        var _ref = x || emptyObject;
        x = _ref.x;
        y = _ref.y;
        animated = _ref.animated;
      }
      var node = _this.getScrollableNode();
      var left = x || 0;
      var top = y || 0;
      if (node != null) {
        if (typeof node.scroll === 'function') {
          node.scroll({
            top: top,
            left: left,
            behavior: !animated ? 'auto' : 'smooth'
          });
        } else {
          node.scrollLeft = left;
          node.scrollTop = top;
        }
      }
    };
    _this.scrollResponderZoomTo = function (rect, animated) {
      if (_Platform.default.OS !== 'ios') {
        (0, _invariant.default)('zoomToRect is not implemented');
      }
    };
    _this.scrollResponderScrollNativeHandleToKeyboard = function (nodeHandle, additionalOffset, preventNegativeScrollOffset) {
      _this.additionalScrollOffset = additionalOffset || 0;
      _this.preventNegativeScrollOffset = !!preventNegativeScrollOffset;
      _UIManager.default.measureLayout(nodeHandle, _this.getInnerViewNode(), _this.scrollResponderTextInputFocusError, _this.scrollResponderInputMeasureAndScrollToKeyboard);
    };
    _this.scrollResponderInputMeasureAndScrollToKeyboard = function (left, top, width, height) {
      var keyboardScreenY = _Dimensions.default.get('window').height;
      if (_this.keyboardWillOpenTo) {
        keyboardScreenY = _this.keyboardWillOpenTo.endCoordinates.screenY;
      }
      var scrollOffsetY = top - keyboardScreenY + height + _this.additionalScrollOffset;
      if (_this.preventNegativeScrollOffset) {
        scrollOffsetY = Math.max(0, scrollOffsetY);
      }
      _this.scrollResponderScrollTo({
        x: 0,
        y: scrollOffsetY,
        animated: true
      });
      _this.additionalOffset = 0;
      _this.preventNegativeScrollOffset = false;
    };
    _this.scrollResponderKeyboardWillShow = function (e) {
      _this.keyboardWillOpenTo = e;
      _this.props.onKeyboardWillShow && _this.props.onKeyboardWillShow(e);
    };
    _this.scrollResponderKeyboardWillHide = function (e) {
      _this.keyboardWillOpenTo = null;
      _this.props.onKeyboardWillHide && _this.props.onKeyboardWillHide(e);
    };
    _this.scrollResponderKeyboardDidShow = function (e) {
      if (e) {
        _this.keyboardWillOpenTo = e;
      }
      _this.props.onKeyboardDidShow && _this.props.onKeyboardDidShow(e);
    };
    _this.scrollResponderKeyboardDidHide = function (e) {
      _this.keyboardWillOpenTo = null;
      _this.props.onKeyboardDidHide && _this.props.onKeyboardDidHide(e);
    };
    _this.flashScrollIndicators = function () {
      _this.scrollResponderFlashScrollIndicators();
    };
    _this.getScrollResponder = function () {
      return _this;
    };
    _this.getScrollableNode = function () {
      return _this._scrollNodeRef;
    };
    _this.getInnerViewRef = function () {
      return _this._innerViewRef;
    };
    _this.getInnerViewNode = function () {
      return _this._innerViewRef;
    };
    _this.getNativeScrollRef = function () {
      return _this._scrollNodeRef;
    };
    _this.scrollTo = function (y, x, animated) {
      if (typeof y === 'number') {
        console.warn('`scrollTo(y, x, animated)` is deprecated. Use `scrollTo({x: 5, y: 5, animated: true})` instead.');
      } else {
        var _ref2 = y || emptyObject;
        x = _ref2.x;
        y = _ref2.y;
        animated = _ref2.animated;
      }
      _this.scrollResponderScrollTo({
        x: x || 0,
        y: y || 0,
        animated: animated !== false
      });
    };
    _this.scrollToEnd = function (options) {
      var animated = (options && options.animated) !== false;
      var horizontal = _this.props.horizontal;
      var scrollResponderNode = _this.getScrollableNode();
      var x = horizontal ? scrollResponderNode.scrollWidth : 0;
      var y = horizontal ? 0 : scrollResponderNode.scrollHeight;
      _this.scrollResponderScrollTo({
        x: x,
        y: y,
        animated: animated
      });
    };
    _this._handleContentOnLayout = function (e) {
      var _e$nativeEvent$layout = e.nativeEvent.layout,
        width = _e$nativeEvent$layout.width,
        height = _e$nativeEvent$layout.height;
      _this.props.onContentSizeChange(width, height);
    };
    _this._handleScroll = function (e) {
      if (process.env.NODE_ENV !== 'production') {
        if (_this.props.onScroll && _this.props.scrollEventThrottle == null) {
          console.log('You specified `onScroll` on a <ScrollView> but not ' + '`scrollEventThrottle`. You will only receive one event. ' + 'Using `16` you get all the events but be aware that it may ' + "cause frame drops, use a bigger number if you don't need as " + 'much precision.');
        }
      }
      if (_this.props.keyboardDismissMode === 'on-drag') {
        (0, _dismissKeyboard.default)();
      }
      _this.scrollResponderHandleScroll(e);
    };
    _this._setInnerViewRef = function (node) {
      _this._innerViewRef = node;
    };
    _this._setScrollNodeRef = function (node) {
      _this._scrollNodeRef = node;
      if (node != null) {
        node.getScrollResponder = _this.getScrollResponder;
        node.getInnerViewNode = _this.getInnerViewNode;
        node.getInnerViewRef = _this.getInnerViewRef;
        node.getNativeScrollRef = _this.getNativeScrollRef;
        node.getScrollableNode = _this.getScrollableNode;
        node.scrollTo = _this.scrollTo;
        node.scrollToEnd = _this.scrollToEnd;
        node.flashScrollIndicators = _this.flashScrollIndicators;
        node.scrollResponderZoomTo = _this.scrollResponderZoomTo;
        node.scrollResponderScrollNativeHandleToKeyboard = _this.scrollResponderScrollNativeHandleToKeyboard;
      }
      var ref = (0, _mergeRefs.default)(_this.props.forwardedRef);
      ref(node);
    };
    return _this;
  }
  (0, _inherits2.default)(ScrollView, _react$default$Compon);
  return (0, _createClass2.default)(ScrollView, [{
    key: "scrollResponderHandleStartShouldSetResponder",
    value: function scrollResponderHandleStartShouldSetResponder() {
      return false;
    }
  }, {
    key: "scrollResponderHandleResponderReject",
    value: function scrollResponderHandleResponderReject() {
      (0, _warning.default)(false, "ScrollView doesn't take rejection well - scrolls anyway");
    }
  }, {
    key: "scrollResponderFlashScrollIndicators",
    value: function scrollResponderFlashScrollIndicators() {}
  }, {
    key: "scrollResponderTextInputFocusError",
    value: function scrollResponderTextInputFocusError(e) {
      console.error('Error measuring text field: ', e);
    }
  }, {
    key: "render",
    value: function render() {
      var _this$props = this.props,
        contentContainerStyle = _this$props.contentContainerStyle,
        horizontal = _this$props.horizontal,
        onContentSizeChange = _this$props.onContentSizeChange,
        refreshControl = _this$props.refreshControl,
        stickyHeaderIndices = _this$props.stickyHeaderIndices,
        pagingEnabled = _this$props.pagingEnabled,
        forwardedRef = _this$props.forwardedRef,
        keyboardDismissMode = _this$props.keyboardDismissMode,
        onScroll = _this$props.onScroll,
        centerContent = _this$props.centerContent,
        other = (0, _objectWithoutPropertiesLoose2.default)(_this$props, _excluded);
      if (process.env.NODE_ENV !== 'production' && this.props.style) {
        var style = _StyleSheet.default.flatten(this.props.style);
        var childLayoutProps = ['alignItems', 'justifyContent'].filter(function (prop) {
          return style && style[prop] !== undefined;
        });
        (0, _invariant.default)(childLayoutProps.length === 0, "ScrollView child layout (" + JSON.stringify(childLayoutProps) + ") " + 'must be applied through the contentContainerStyle prop.');
      }
      var contentSizeChangeProps = {};
      if (onContentSizeChange) {
        contentSizeChangeProps = {
          onLayout: this._handleContentOnLayout
        };
      }
      var hasStickyHeaderIndices = !horizontal && Array.isArray(stickyHeaderIndices);
      var children = hasStickyHeaderIndices || pagingEnabled ? _react.default.Children.map(this.props.children, function (child, i) {
        var isSticky = hasStickyHeaderIndices && stickyHeaderIndices.indexOf(i) > -1;
        if (child != null && (isSticky || pagingEnabled)) {
          return _react.default.createElement(_View.default, {
            style: [isSticky && styles.stickyHeader, pagingEnabled && styles.pagingEnabledChild]
          }, child);
        } else {
          return child;
        }
      }) : this.props.children;
      var contentContainer = _react.default.createElement(_View.default, (0, _extends2.default)({}, contentSizeChangeProps, {
        children: children,
        collapsable: false,
        ref: this._setInnerViewRef,
        style: [horizontal && styles.contentContainerHorizontal, centerContent && styles.contentContainerCenterContent, contentContainerStyle]
      }));
      var baseStyle = horizontal ? styles.baseHorizontal : styles.baseVertical;
      var pagingEnabledStyle = horizontal ? styles.pagingEnabledHorizontal : styles.pagingEnabledVertical;
      var props = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, other), {}, {
        style: [baseStyle, pagingEnabled && pagingEnabledStyle, this.props.style],
        onTouchStart: this.scrollResponderHandleTouchStart,
        onTouchMove: this.scrollResponderHandleTouchMove,
        onTouchEnd: this.scrollResponderHandleTouchEnd,
        onScrollBeginDrag: this.scrollResponderHandleScrollBeginDrag,
        onScrollEndDrag: this.scrollResponderHandleScrollEndDrag,
        onMomentumScrollBegin: this.scrollResponderHandleMomentumScrollBegin,
        onMomentumScrollEnd: this.scrollResponderHandleMomentumScrollEnd,
        onStartShouldSetResponder: this.scrollResponderHandleStartShouldSetResponder,
        onStartShouldSetResponderCapture: this.scrollResponderHandleStartShouldSetResponderCapture,
        onScrollShouldSetResponder: this.scrollResponderHandleScrollShouldSetResponder,
        onScroll: this._handleScroll,
        onResponderGrant: this.scrollResponderHandleResponderGrant,
        onResponderTerminationRequest: this.scrollResponderHandleTerminationRequest,
        onResponderTerminate: this.scrollResponderHandleTerminate,
        onResponderRelease: this.scrollResponderHandleResponderRelease,
        onResponderReject: this.scrollResponderHandleResponderReject
      });
      var ScrollViewClass = _ScrollViewBase.default;
      (0, _invariant.default)(ScrollViewClass !== undefined, 'ScrollViewClass must not be undefined');
      var scrollView = _react.default.createElement(ScrollViewClass, (0, _extends2.default)({}, props, {
        ref: this._setScrollNodeRef
      }), contentContainer);
      if (refreshControl) {
        return _react.default.cloneElement(refreshControl, {
          style: props.style
        }, scrollView);
      }
      return scrollView;
    }
  }]);
}(_react.default.Component);
var commonStyle = {
  flexGrow: 1,
  flexShrink: 1,
  transform: 'translateZ(0)',
  WebkitOverflowScrolling: 'touch'
};
var styles = _StyleSheet.default.create({
  baseVertical: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, commonStyle), {}, {
    flexDirection: 'column',
    overflowX: 'hidden',
    overflowY: 'auto'
  }),
  baseHorizontal: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, commonStyle), {}, {
    flexDirection: 'row',
    overflowX: 'auto',
    overflowY: 'hidden'
  }),
  contentContainerHorizontal: {
    flexDirection: 'row'
  },
  contentContainerCenterContent: {
    justifyContent: 'center',
    flexGrow: 1
  },
  stickyHeader: {
    position: 'sticky',
    top: 0,
    zIndex: 10
  },
  pagingEnabledHorizontal: {
    scrollSnapType: 'x mandatory'
  },
  pagingEnabledVertical: {
    scrollSnapType: 'y mandatory'
  },
  pagingEnabledChild: {
    scrollSnapAlign: 'start'
  }
});
var ForwardedScrollView = _react.default.forwardRef(function (props, forwardedRef) {
  return _react.default.createElement(ScrollView, (0, _extends2.default)({}, props, {
    forwardedRef: forwardedRef
  }));
});
ForwardedScrollView.displayName = 'ScrollView';
var _default = exports.default = ForwardedScrollView;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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