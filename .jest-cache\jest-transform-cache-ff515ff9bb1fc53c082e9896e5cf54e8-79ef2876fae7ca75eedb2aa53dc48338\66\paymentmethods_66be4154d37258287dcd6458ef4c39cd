c3fc02b4eb2d25d996576ee16a239480
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1lxoualpe8() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\payment\\payment-methods.tsx";
  var hash = "c937774e21fc5ff24fd0e1dadd765b17ef08ea62";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\payment\\payment-methods.tsx",
    statementMap: {
      "0": {
        start: {
          line: 10,
          column: 15
        },
        end: {
          line: 17,
          column: 1
        }
      },
      "1": {
        start: {
          line: 28,
          column: 46
        },
        end: {
          line: 43,
          column: 4
        }
      },
      "2": {
        start: {
          line: 45,
          column: 29
        },
        end: {
          line: 60,
          column: 3
        }
      },
      "3": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 59,
          column: 6
        }
      },
      "4": {
        start: {
          line: 55,
          column: 12
        },
        end: {
          line: 55,
          column: 79
        }
      },
      "5": {
        start: {
          line: 55,
          column: 38
        },
        end: {
          line: 55,
          column: 77
        }
      },
      "6": {
        start: {
          line: 55,
          column: 60
        },
        end: {
          line: 55,
          column: 76
        }
      },
      "7": {
        start: {
          line: 62,
          column: 27
        },
        end: {
          line: 69,
          column: 3
        }
      },
      "8": {
        start: {
          line: 63,
          column: 4
        },
        end: {
          line: 68,
          column: 6
        }
      },
      "9": {
        start: {
          line: 64,
          column: 6
        },
        end: {
          line: 67,
          column: 9
        }
      },
      "10": {
        start: {
          line: 64,
          column: 26
        },
        end: {
          line: 67,
          column: 7
        }
      },
      "11": {
        start: {
          line: 71,
          column: 26
        },
        end: {
          line: 73,
          column: 3
        }
      },
      "12": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 72,
          column: 78
        }
      },
      "13": {
        start: {
          line: 75,
          column: 2
        },
        end: {
          line: 150,
          column: 4
        }
      },
      "14": {
        start: {
          line: 80,
          column: 25
        },
        end: {
          line: 80,
          column: 38
        }
      },
      "15": {
        start: {
          line: 101,
          column: 10
        },
        end: {
          line: 136,
          column: 17
        }
      },
      "16": {
        start: {
          line: 114,
          column: 31
        },
        end: {
          line: 114,
          column: 60
        }
      },
      "17": {
        start: {
          line: 131,
          column: 31
        },
        end: {
          line: 131,
          column: 58
        }
      },
      "18": {
        start: {
          line: 153,
          column: 15
        },
        end: {
          line: 257,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "PaymentMethodsScreen",
        decl: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 44
          }
        },
        loc: {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 151,
            column: 1
          }
        },
        line: 27
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 45,
            column: 29
          },
          end: {
            line: 45,
            column: 30
          }
        },
        loc: {
          start: {
            line: 45,
            column: 45
          },
          end: {
            line: 60,
            column: 3
          }
        },
        line: 45
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 54,
            column: 19
          },
          end: {
            line: 54,
            column: 20
          }
        },
        loc: {
          start: {
            line: 54,
            column: 25
          },
          end: {
            line: 56,
            column: 11
          }
        },
        line: 54
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 55,
            column: 30
          },
          end: {
            line: 55,
            column: 31
          }
        },
        loc: {
          start: {
            line: 55,
            column: 38
          },
          end: {
            line: 55,
            column: 77
          }
        },
        line: 55
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 55,
            column: 50
          },
          end: {
            line: 55,
            column: 51
          }
        },
        loc: {
          start: {
            line: 55,
            column: 60
          },
          end: {
            line: 55,
            column: 76
          }
        },
        line: 55
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 62,
            column: 27
          },
          end: {
            line: 62,
            column: 28
          }
        },
        loc: {
          start: {
            line: 62,
            column: 43
          },
          end: {
            line: 69,
            column: 3
          }
        },
        line: 62
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 63,
            column: 22
          },
          end: {
            line: 63,
            column: 23
          }
        },
        loc: {
          start: {
            line: 64,
            column: 6
          },
          end: {
            line: 67,
            column: 9
          }
        },
        line: 64
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 64,
            column: 15
          },
          end: {
            line: 64,
            column: 16
          }
        },
        loc: {
          start: {
            line: 64,
            column: 26
          },
          end: {
            line: 67,
            column: 7
          }
        },
        line: 64
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 71,
            column: 26
          },
          end: {
            line: 71,
            column: 27
          }
        },
        loc: {
          start: {
            line: 71,
            column: 32
          },
          end: {
            line: 73,
            column: 3
          }
        },
        line: 71
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 80,
            column: 19
          },
          end: {
            line: 80,
            column: 20
          }
        },
        loc: {
          start: {
            line: 80,
            column: 25
          },
          end: {
            line: 80,
            column: 38
          }
        },
        line: 80
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 100,
            column: 28
          },
          end: {
            line: 100,
            column: 29
          }
        },
        loc: {
          start: {
            line: 101,
            column: 10
          },
          end: {
            line: 136,
            column: 17
          }
        },
        line: 101
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 114,
            column: 25
          },
          end: {
            line: 114,
            column: 26
          }
        },
        loc: {
          start: {
            line: 114,
            column: 31
          },
          end: {
            line: 114,
            column: 60
          }
        },
        line: 114
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 131,
            column: 25
          },
          end: {
            line: 131,
            column: 26
          }
        },
        loc: {
          start: {
            line: 131,
            column: 31
          },
          end: {
            line: 131,
            column: 58
          }
        },
        line: 131
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 122,
            column: 13
          },
          end: {
            line: 126,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 122,
            column: 13
          },
          end: {
            line: 122,
            column: 29
          }
        }, {
          start: {
            line: 123,
            column: 14
          },
          end: {
            line: 125,
            column: 21
          }
        }],
        line: 122
      },
      "1": {
        loc: {
          start: {
            line: 128,
            column: 13
          },
          end: {
            line: 135,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 128,
            column: 13
          },
          end: {
            line: 128,
            column: 30
          }
        }, {
          start: {
            line: 129,
            column: 14
          },
          end: {
            line: 134,
            column: 16
          }
        }],
        line: 128
      },
      "2": {
        loc: {
          start: {
            line: 139,
            column: 9
          },
          end: {
            line: 147,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 139,
            column: 9
          },
          end: {
            line: 139,
            column: 36
          }
        }, {
          start: {
            line: 140,
            column: 10
          },
          end: {
            line: 146,
            column: 17
          }
        }],
        line: 139
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c937774e21fc5ff24fd0e1dadd765b17ef08ea62"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1lxoualpe8 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1lxoualpe8();
import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { ArrowLeft, CreditCard, Plus, Trash2 } from 'lucide-react-native';
import Button from "../../components/ui/Button";
import Card from "../../components/ui/Card";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_1lxoualpe8().s[0]++, {
  primary: '#23ba16',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  red: '#ef4444'
});
export default function PaymentMethodsScreen() {
  cov_1lxoualpe8().f[0]++;
  var _ref = (cov_1lxoualpe8().s[1]++, useState([{
      id: '1',
      type: 'visa',
      last4: '1234',
      expiry: '12/25',
      isDefault: true
    }, {
      id: '2',
      type: 'mastercard',
      last4: '5678',
      expiry: '08/26',
      isDefault: false
    }])),
    _ref2 = _slicedToArray(_ref, 2),
    paymentMethods = _ref2[0],
    setPaymentMethods = _ref2[1];
  cov_1lxoualpe8().s[2]++;
  var handleDeleteMethod = function handleDeleteMethod(id) {
    cov_1lxoualpe8().f[1]++;
    cov_1lxoualpe8().s[3]++;
    Alert.alert('Delete Payment Method', 'Are you sure you want to delete this payment method?', [{
      text: 'Cancel',
      style: 'cancel'
    }, {
      text: 'Delete',
      style: 'destructive',
      onPress: function onPress() {
        cov_1lxoualpe8().f[2]++;
        cov_1lxoualpe8().s[4]++;
        setPaymentMethods(function (prev) {
          cov_1lxoualpe8().f[3]++;
          cov_1lxoualpe8().s[5]++;
          return prev.filter(function (method) {
            cov_1lxoualpe8().f[4]++;
            cov_1lxoualpe8().s[6]++;
            return method.id !== id;
          });
        });
      }
    }]);
  };
  cov_1lxoualpe8().s[7]++;
  var handleSetDefault = function handleSetDefault(id) {
    cov_1lxoualpe8().f[5]++;
    cov_1lxoualpe8().s[8]++;
    setPaymentMethods(function (prev) {
      cov_1lxoualpe8().f[6]++;
      cov_1lxoualpe8().s[9]++;
      return prev.map(function (method) {
        cov_1lxoualpe8().f[7]++;
        cov_1lxoualpe8().s[10]++;
        return Object.assign({}, method, {
          isDefault: method.id === id
        });
      });
    });
  };
  cov_1lxoualpe8().s[11]++;
  var handleAddMethod = function handleAddMethod() {
    cov_1lxoualpe8().f[8]++;
    cov_1lxoualpe8().s[12]++;
    Alert.alert('Add Payment Method', 'This feature will be available soon!');
  };
  cov_1lxoualpe8().s[13]++;
  return _jsxs(SafeAreaView, {
    style: styles.container,
    children: [_jsxs(View, {
      style: styles.header,
      children: [_jsx(Button, {
        title: "",
        onPress: function onPress() {
          cov_1lxoualpe8().f[9]++;
          cov_1lxoualpe8().s[14]++;
          return router.back();
        },
        variant: "ghost",
        style: styles.backButton,
        children: _jsx(ArrowLeft, {
          size: 24,
          color: colors.dark
        })
      }), _jsx(Text, {
        style: styles.headerTitle,
        children: "Payment Methods"
      }), _jsx(View, {
        style: styles.placeholder
      })]
    }), _jsxs(ScrollView, {
      style: styles.content,
      children: [_jsx(Button, {
        title: "Add Payment Method",
        onPress: handleAddMethod,
        variant: "outline",
        style: styles.addButton,
        children: _jsx(Plus, {
          size: 20,
          color: colors.primary
        })
      }), paymentMethods.map(function (method) {
        cov_1lxoualpe8().f[10]++;
        cov_1lxoualpe8().s[15]++;
        return _jsxs(Card, {
          style: styles.methodCard,
          children: [_jsxs(View, {
            style: styles.methodHeader,
            children: [_jsxs(View, {
              style: styles.methodInfo,
              children: [_jsx(CreditCard, {
                size: 24,
                color: colors.primary
              }), _jsxs(View, {
                style: styles.methodDetails,
                children: [_jsxs(Text, {
                  style: styles.methodType,
                  children: [method.type.toUpperCase(), " \u2022\u2022\u2022\u2022 ", method.last4]
                }), _jsxs(Text, {
                  style: styles.methodExpiry,
                  children: ["Expires ", method.expiry]
                })]
              })]
            }), _jsx(Button, {
              title: "",
              onPress: function onPress() {
                cov_1lxoualpe8().f[11]++;
                cov_1lxoualpe8().s[16]++;
                return handleDeleteMethod(method.id);
              },
              variant: "ghost",
              style: styles.deleteButton,
              children: _jsx(Trash2, {
                size: 20,
                color: colors.red
              })
            })]
          }), (cov_1lxoualpe8().b[0][0]++, method.isDefault) && (cov_1lxoualpe8().b[0][1]++, _jsx(View, {
            style: styles.defaultBadge,
            children: _jsx(Text, {
              style: styles.defaultText,
              children: "Default"
            })
          })), (cov_1lxoualpe8().b[1][0]++, !method.isDefault) && (cov_1lxoualpe8().b[1][1]++, _jsx(Button, {
            title: "Set as Default",
            onPress: function onPress() {
              cov_1lxoualpe8().f[12]++;
              cov_1lxoualpe8().s[17]++;
              return handleSetDefault(method.id);
            },
            variant: "outline",
            style: styles.defaultButton
          }))]
        }, method.id);
      }), (cov_1lxoualpe8().b[2][0]++, paymentMethods.length === 0) && (cov_1lxoualpe8().b[2][1]++, _jsxs(View, {
        style: styles.emptyState,
        children: [_jsx(CreditCard, {
          size: 48,
          color: colors.gray
        }), _jsx(Text, {
          style: styles.emptyTitle,
          children: "No Payment Methods"
        }), _jsx(Text, {
          style: styles.emptyDescription,
          children: "Add a payment method to manage your subscription"
        })]
      }))]
    })]
  });
}
var styles = (cov_1lxoualpe8().s[18]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGray
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray
  },
  backButton: {
    width: 40,
    height: 40
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark
  },
  placeholder: {
    width: 40
  },
  content: {
    flex: 1,
    padding: 20
  },
  addButton: {
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  methodCard: {
    marginBottom: 16,
    padding: 20
  },
  methodHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16
  },
  methodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1
  },
  methodDetails: {
    marginLeft: 12,
    flex: 1
  },
  methodType: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 4
  },
  methodExpiry: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray
  },
  deleteButton: {
    width: 40,
    height: 40
  },
  defaultBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start'
  },
  defaultText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: colors.white
  },
  defaultButton: {
    alignSelf: 'flex-start'
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60
  },
  emptyTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginTop: 16,
    marginBottom: 8
  },
  emptyDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    textAlign: 'center'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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