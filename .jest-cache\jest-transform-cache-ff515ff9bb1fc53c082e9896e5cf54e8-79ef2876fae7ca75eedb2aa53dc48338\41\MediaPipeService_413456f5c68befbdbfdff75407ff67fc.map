{"version": 3, "names": ["performanceMonitor", "TENNIS_POSE_LANDMARKS", "cov_1als4ma6pd", "s", "LEFT_SHOULDER", "RIGHT_SHOULDER", "LEFT_ELBOW", "RIGHT_ELBOW", "LEFT_WRIST", "RIGHT_WRIST", "LEFT_HIP", "RIGHT_HIP", "LEFT_KNEE", "RIGHT_KNEE", "LEFT_ANKLE", "RIGHT_ANKLE", "NOSE", "LEFT_EYE", "RIGHT_EYE", "LEFT_EAR", "RIGHT_EAR", "MOUTH_LEFT", "MOUTH_RIGHT", "MediaPipeService", "_classCallCheck", "isInitialized", "config", "minDetectionConfidence", "minTrackingConfidence", "modelComplexity", "smoothLandmarks", "enableSegmentation", "smoothSegmentation", "_createClass", "key", "value", "_initialize", "_asyncToGenerator", "f", "start", "b", "Object", "assign", "loadMediaPipeModel", "end", "error", "console", "Error", "initialize", "_x", "apply", "arguments", "_detectPose", "imageData", "frameIndex", "length", "undefined", "mockResult", "simulatePoseDetection", "detectPose", "_x2", "analyzeTennisMovement", "poseResult", "landmarks", "keyAngles", "calculateKeyAngles", "movementType", "classifyTennisMovement", "bodyPosition", "analyzeBodyPosition", "technicalMetrics", "evaluateTechnicalMetrics", "analysis", "type", "confidence", "_processVideoFrames", "frames", "onProgress", "results", "i", "push", "processVideoFrames", "_x3", "_x4", "shoulderAngle", "calculateAngle", "elbowAngle", "hipAngle", "kneeAngle", "wristAngle", "point1", "point2", "point3", "vector1", "x", "y", "vector2", "dotProduct", "magnitude1", "Math", "sqrt", "magnitude2", "cosAngle", "angle", "acos", "max", "min", "PI", "angles", "rightWrist", "leftWrist", "rightShoulder", "leftShoulder", "isRightHanded", "leftFoot", "rightFoot", "leftHip", "rightHip", "footDistance", "abs", "stance", "hipCenter", "footCenter", "weightShift", "weight", "balance", "racketPosition", "wristHeight", "follow<PERSON><PERSON><PERSON>", "footwork", "footStability", "_loadMediaPipeModel", "Promise", "resolve", "setTimeout", "log", "_simulatePoseDetection", "random", "z", "visibility", "timestamp", "Date", "now", "_x5", "updateConfig", "getConfig", "isReady", "cleanup", "mediaPipeService"], "sources": ["MediaPipeService.ts"], "sourcesContent": ["/**\n * MediaPipe Pose Detection Service\n * Integrates Google MediaPipe for tennis technique analysis\n */\n\nimport { performanceMonitor } from '@/utils/performance';\n\n// MediaPipe pose landmark indices for tennis-specific analysis\nexport const TENNIS_POSE_LANDMARKS = {\n  // Upper body landmarks critical for tennis\n  LEFT_SHOULDER: 11,\n  RIGHT_SHOULDER: 12,\n  LEFT_ELBOW: 13,\n  RIGHT_ELBOW: 14,\n  LEFT_WRIST: 15,\n  RIGHT_WRIST: 16,\n  LEFT_HIP: 23,\n  RIGHT_HIP: 24,\n  LEFT_KNEE: 25,\n  RIGHT_KNEE: 26,\n  LEFT_ANKLE: 27,\n  RIGHT_ANKLE: 28,\n  // Additional landmarks for tennis analysis\n  NOSE: 0,\n  LEFT_EYE: 1,\n  RIGHT_EYE: 2,\n  LEFT_EAR: 3,\n  RIGHT_EAR: 4,\n  MOUTH_LEFT: 9,\n  MOUTH_RIGHT: 10,\n} as const;\n\nexport interface PoseLandmark {\n  x: number; // Normalized x coordinate (0-1)\n  y: number; // Normalized y coordinate (0-1)\n  z: number; // Depth coordinate\n  visibility: number; // Visibility confidence (0-1)\n}\n\nexport interface PoseDetectionResult {\n  landmarks: PoseLandmark[];\n  confidence: number;\n  timestamp: number;\n  frameIndex: number;\n  worldLandmarks?: PoseLandmark[]; // 3D world coordinates\n}\n\nexport interface TennisMovementAnalysis {\n  movementType: 'serve' | 'forehand' | 'backhand' | 'volley' | 'overhead' | 'unknown';\n  confidence: number;\n  keyAngles: {\n    shoulderAngle: number;\n    elbowAngle: number;\n    wristAngle: number;\n    hipAngle: number;\n    kneeAngle: number;\n  };\n  bodyPosition: {\n    stance: 'open' | 'closed' | 'neutral';\n    weight: 'forward' | 'backward' | 'centered';\n    balance: number; // 0-1 score\n  };\n  technicalMetrics: {\n    racketPosition: 'high' | 'medium' | 'low';\n    followThrough: 'complete' | 'partial' | 'none';\n    footwork: 'excellent' | 'good' | 'needs_improvement';\n  };\n}\n\nexport interface PoseAnalysisConfig {\n  minDetectionConfidence: number;\n  minTrackingConfidence: number;\n  modelComplexity: 0 | 1 | 2; // 0=lite, 1=full, 2=heavy\n  smoothLandmarks: boolean;\n  enableSegmentation: boolean;\n  smoothSegmentation: boolean;\n}\n\nclass MediaPipeService {\n  private isInitialized = false;\n  private config: PoseAnalysisConfig = {\n    minDetectionConfidence: 0.7,\n    minTrackingConfidence: 0.5,\n    modelComplexity: 1,\n    smoothLandmarks: true,\n    enableSegmentation: false,\n    smoothSegmentation: true,\n  };\n\n  /**\n   * Initialize MediaPipe pose detection\n   */\n  async initialize(config?: Partial<PoseAnalysisConfig>): Promise<void> {\n    try {\n      performanceMonitor.start('mediapipe_init');\n\n      if (config) {\n        this.config = { ...this.config, ...config };\n      }\n\n      // Note: In a real implementation, this would initialize the MediaPipe library\n      // For now, we'll simulate the initialization\n      await this.loadMediaPipeModel();\n      \n      this.isInitialized = true;\n      performanceMonitor.end('mediapipe_init');\n    } catch (error) {\n      console.error('Failed to initialize MediaPipe:', error);\n      throw new Error('MediaPipe initialization failed');\n    }\n  }\n\n  /**\n   * Detect pose landmarks in a video frame\n   */\n  async detectPose(\n    imageData: ImageData | HTMLVideoElement | HTMLCanvasElement,\n    frameIndex: number = 0\n  ): Promise<PoseDetectionResult | null> {\n    if (!this.isInitialized) {\n      throw new Error('MediaPipe service not initialized');\n    }\n\n    try {\n      performanceMonitor.start('pose_detection');\n\n      // Note: In a real implementation, this would call MediaPipe's pose detection\n      // For now, we'll simulate pose detection with mock data\n      const mockResult = await this.simulatePoseDetection(frameIndex);\n\n      performanceMonitor.end('pose_detection');\n      return mockResult;\n    } catch (error) {\n      console.error('Pose detection failed:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Analyze tennis-specific movements from pose data\n   */\n  analyzeTennisMovement(poseResult: PoseDetectionResult): TennisMovementAnalysis {\n    try {\n      performanceMonitor.start('tennis_movement_analysis');\n\n      const landmarks = poseResult.landmarks;\n      \n      // Calculate key angles for tennis analysis\n      const keyAngles = this.calculateKeyAngles(landmarks);\n      \n      // Determine movement type based on pose patterns\n      const movementType = this.classifyTennisMovement(landmarks, keyAngles);\n      \n      // Analyze body position and stance\n      const bodyPosition = this.analyzeBodyPosition(landmarks);\n      \n      // Evaluate technical metrics\n      const technicalMetrics = this.evaluateTechnicalMetrics(landmarks, keyAngles);\n\n      const analysis: TennisMovementAnalysis = {\n        movementType: movementType.type,\n        confidence: movementType.confidence,\n        keyAngles,\n        bodyPosition,\n        technicalMetrics,\n      };\n\n      performanceMonitor.end('tennis_movement_analysis');\n      return analysis;\n    } catch (error) {\n      console.error('Tennis movement analysis failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Process multiple frames for continuous analysis\n   */\n  async processVideoFrames(\n    frames: (ImageData | HTMLVideoElement | HTMLCanvasElement)[],\n    onProgress?: (progress: number) => void\n  ): Promise<TennisMovementAnalysis[]> {\n    const results: TennisMovementAnalysis[] = [];\n    \n    for (let i = 0; i < frames.length; i++) {\n      const poseResult = await this.detectPose(frames[i], i);\n      \n      if (poseResult) {\n        const analysis = this.analyzeTennisMovement(poseResult);\n        results.push(analysis);\n      }\n      \n      if (onProgress) {\n        onProgress((i + 1) / frames.length);\n      }\n    }\n    \n    return results;\n  }\n\n  /**\n   * Calculate angles between key body joints\n   */\n  private calculateKeyAngles(landmarks: PoseLandmark[]): TennisMovementAnalysis['keyAngles'] {\n    // Calculate shoulder angle (shoulder-elbow-wrist)\n    const shoulderAngle = this.calculateAngle(\n      landmarks[TENNIS_POSE_LANDMARKS.LEFT_SHOULDER],\n      landmarks[TENNIS_POSE_LANDMARKS.LEFT_ELBOW],\n      landmarks[TENNIS_POSE_LANDMARKS.LEFT_WRIST]\n    );\n\n    // Calculate elbow angle\n    const elbowAngle = this.calculateAngle(\n      landmarks[TENNIS_POSE_LANDMARKS.LEFT_SHOULDER],\n      landmarks[TENNIS_POSE_LANDMARKS.LEFT_ELBOW],\n      landmarks[TENNIS_POSE_LANDMARKS.LEFT_WRIST]\n    );\n\n    // Calculate hip angle\n    const hipAngle = this.calculateAngle(\n      landmarks[TENNIS_POSE_LANDMARKS.LEFT_SHOULDER],\n      landmarks[TENNIS_POSE_LANDMARKS.LEFT_HIP],\n      landmarks[TENNIS_POSE_LANDMARKS.LEFT_KNEE]\n    );\n\n    // Calculate knee angle\n    const kneeAngle = this.calculateAngle(\n      landmarks[TENNIS_POSE_LANDMARKS.LEFT_HIP],\n      landmarks[TENNIS_POSE_LANDMARKS.LEFT_KNEE],\n      landmarks[TENNIS_POSE_LANDMARKS.LEFT_ANKLE]\n    );\n\n    return {\n      shoulderAngle,\n      elbowAngle,\n      wristAngle: 0, // Simplified for now\n      hipAngle,\n      kneeAngle,\n    };\n  }\n\n  /**\n   * Calculate angle between three points\n   */\n  private calculateAngle(point1: PoseLandmark, point2: PoseLandmark, point3: PoseLandmark): number {\n    const vector1 = {\n      x: point1.x - point2.x,\n      y: point1.y - point2.y,\n    };\n    \n    const vector2 = {\n      x: point3.x - point2.x,\n      y: point3.y - point2.y,\n    };\n    \n    const dotProduct = vector1.x * vector2.x + vector1.y * vector2.y;\n    const magnitude1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y);\n    const magnitude2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y);\n    \n    const cosAngle = dotProduct / (magnitude1 * magnitude2);\n    const angle = Math.acos(Math.max(-1, Math.min(1, cosAngle)));\n    \n    return (angle * 180) / Math.PI; // Convert to degrees\n  }\n\n  /**\n   * Classify tennis movement type based on pose patterns\n   */\n  private classifyTennisMovement(\n    landmarks: PoseLandmark[], \n    angles: TennisMovementAnalysis['keyAngles']\n  ): { type: TennisMovementAnalysis['movementType']; confidence: number } {\n    // Simplified classification logic\n    // In a real implementation, this would use machine learning models\n    \n    const rightWrist = landmarks[TENNIS_POSE_LANDMARKS.RIGHT_WRIST];\n    const leftWrist = landmarks[TENNIS_POSE_LANDMARKS.LEFT_WRIST];\n    const rightShoulder = landmarks[TENNIS_POSE_LANDMARKS.RIGHT_SHOULDER];\n    const leftShoulder = landmarks[TENNIS_POSE_LANDMARKS.LEFT_SHOULDER];\n\n    // Determine dominant hand based on wrist position\n    const isRightHanded = rightWrist.y < leftWrist.y;\n    \n    // Classify based on arm position and angles\n    if (angles.shoulderAngle > 160 && rightWrist.y < rightShoulder.y) {\n      return { type: 'serve', confidence: 0.85 };\n    } else if (isRightHanded && rightWrist.x > rightShoulder.x) {\n      return { type: 'forehand', confidence: 0.80 };\n    } else if (isRightHanded && rightWrist.x < rightShoulder.x) {\n      return { type: 'backhand', confidence: 0.75 };\n    } else if (rightWrist.y < rightShoulder.y - 0.1) {\n      return { type: 'volley', confidence: 0.70 };\n    } else {\n      return { type: 'unknown', confidence: 0.50 };\n    }\n  }\n\n  /**\n   * Analyze body position and stance\n   */\n  private analyzeBodyPosition(landmarks: PoseLandmark[]): TennisMovementAnalysis['bodyPosition'] {\n    const leftFoot = landmarks[TENNIS_POSE_LANDMARKS.LEFT_ANKLE];\n    const rightFoot = landmarks[TENNIS_POSE_LANDMARKS.RIGHT_ANKLE];\n    const leftHip = landmarks[TENNIS_POSE_LANDMARKS.LEFT_HIP];\n    const rightHip = landmarks[TENNIS_POSE_LANDMARKS.RIGHT_HIP];\n\n    // Calculate stance based on foot positioning\n    const footDistance = Math.abs(leftFoot.x - rightFoot.x);\n    let stance: 'open' | 'closed' | 'neutral';\n    \n    if (footDistance > 0.3) {\n      stance = 'open';\n    } else if (footDistance < 0.1) {\n      stance = 'closed';\n    } else {\n      stance = 'neutral';\n    }\n\n    // Calculate weight distribution\n    const hipCenter = (leftHip.x + rightHip.x) / 2;\n    const footCenter = (leftFoot.x + rightFoot.x) / 2;\n    const weightShift = hipCenter - footCenter;\n    \n    let weight: 'forward' | 'backward' | 'centered';\n    if (weightShift > 0.05) {\n      weight = 'forward';\n    } else if (weightShift < -0.05) {\n      weight = 'backward';\n    } else {\n      weight = 'centered';\n    }\n\n    // Calculate balance score\n    const balance = Math.max(0, 1 - Math.abs(weightShift) * 10);\n\n    return {\n      stance,\n      weight,\n      balance,\n    };\n  }\n\n  /**\n   * Evaluate technical metrics\n   */\n  private evaluateTechnicalMetrics(\n    landmarks: PoseLandmark[], \n    angles: TennisMovementAnalysis['keyAngles']\n  ): TennisMovementAnalysis['technicalMetrics'] {\n    const rightWrist = landmarks[TENNIS_POSE_LANDMARKS.RIGHT_WRIST];\n    const rightShoulder = landmarks[TENNIS_POSE_LANDMARKS.RIGHT_SHOULDER];\n\n    // Evaluate racket position\n    let racketPosition: 'high' | 'medium' | 'low';\n    const wristHeight = rightShoulder.y - rightWrist.y;\n    \n    if (wristHeight > 0.2) {\n      racketPosition = 'high';\n    } else if (wristHeight > -0.1) {\n      racketPosition = 'medium';\n    } else {\n      racketPosition = 'low';\n    }\n\n    // Evaluate follow-through based on arm extension\n    let followThrough: 'complete' | 'partial' | 'none';\n    if (angles.elbowAngle > 150) {\n      followThrough = 'complete';\n    } else if (angles.elbowAngle > 120) {\n      followThrough = 'partial';\n    } else {\n      followThrough = 'none';\n    }\n\n    // Evaluate footwork based on stance and balance\n    let footwork: 'excellent' | 'good' | 'needs_improvement';\n    const leftFoot = landmarks[TENNIS_POSE_LANDMARKS.LEFT_ANKLE];\n    const rightFoot = landmarks[TENNIS_POSE_LANDMARKS.RIGHT_ANKLE];\n    const footStability = 1 - Math.abs(leftFoot.y - rightFoot.y);\n    \n    if (footStability > 0.9) {\n      footwork = 'excellent';\n    } else if (footStability > 0.7) {\n      footwork = 'good';\n    } else {\n      footwork = 'needs_improvement';\n    }\n\n    return {\n      racketPosition,\n      followThrough,\n      footwork,\n    };\n  }\n\n  /**\n   * Load MediaPipe model (simulated)\n   */\n  private async loadMediaPipeModel(): Promise<void> {\n    // Simulate model loading time\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    console.log('MediaPipe model loaded successfully');\n  }\n\n  /**\n   * Simulate pose detection for development/testing\n   */\n  private async simulatePoseDetection(frameIndex: number): Promise<PoseDetectionResult> {\n    // Generate mock pose landmarks\n    const landmarks: PoseLandmark[] = [];\n    \n    // Generate 33 pose landmarks (MediaPipe standard)\n    for (let i = 0; i < 33; i++) {\n      landmarks.push({\n        x: Math.random() * 0.8 + 0.1, // Keep within reasonable bounds\n        y: Math.random() * 0.8 + 0.1,\n        z: Math.random() * 0.2 - 0.1,\n        visibility: Math.random() * 0.3 + 0.7, // High visibility\n      });\n    }\n\n    return {\n      landmarks,\n      confidence: Math.random() * 0.2 + 0.8, // High confidence\n      timestamp: Date.now(),\n      frameIndex,\n    };\n  }\n\n  /**\n   * Update configuration\n   */\n  updateConfig(config: Partial<PoseAnalysisConfig>): void {\n    this.config = { ...this.config, ...config };\n  }\n\n  /**\n   * Get current configuration\n   */\n  getConfig(): PoseAnalysisConfig {\n    return { ...this.config };\n  }\n\n  /**\n   * Check if service is initialized\n   */\n  isReady(): boolean {\n    return this.isInitialized;\n  }\n\n  /**\n   * Cleanup resources\n   */\n  cleanup(): void {\n    this.isInitialized = false;\n    // Cleanup MediaPipe resources\n  }\n}\n\n// Export singleton instance\nexport const mediaPipeService = new MediaPipeService();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,SAASA,kBAAkB;AAG3B,OAAO,IAAMC,qBAAqB,IAAAC,cAAA,GAAAC,CAAA,OAAG;EAEnCC,aAAa,EAAE,EAAE;EACjBC,cAAc,EAAE,EAAE;EAClBC,UAAU,EAAE,EAAE;EACdC,WAAW,EAAE,EAAE;EACfC,UAAU,EAAE,EAAE;EACdC,WAAW,EAAE,EAAE;EACfC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,EAAE;EACbC,UAAU,EAAE,EAAE;EACdC,UAAU,EAAE,EAAE;EACdC,WAAW,EAAE,EAAE;EAEfC,IAAI,EAAE,CAAC;EACPC,QAAQ,EAAE,CAAC;EACXC,SAAS,EAAE,CAAC;EACZC,QAAQ,EAAE,CAAC;EACXC,SAAS,EAAE,CAAC;EACZC,UAAU,EAAE,CAAC;EACbC,WAAW,EAAE;AACf,CAAC,CAAS;AAAC,IAgDLC,gBAAgB;EAAA,SAAAA,iBAAA;IAAAC,eAAA,OAAAD,gBAAA;IAAA,KACZE,aAAa,IAAAvB,cAAA,GAAAC,CAAA,OAAG,KAAK;IAAA,KACrBuB,MAAM,IAAAxB,cAAA,GAAAC,CAAA,OAAuB;MACnCwB,sBAAsB,EAAE,GAAG;MAC3BC,qBAAqB,EAAE,GAAG;MAC1BC,eAAe,EAAE,CAAC;MAClBC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,KAAK;MACzBC,kBAAkB,EAAE;IACtB,CAAC;EAAA;EAAA,OAAAC,YAAA,CAAAV,gBAAA;IAAAW,GAAA;IAAAC,KAAA;MAAA,IAAAC,WAAA,GAAAC,iBAAA,CAKD,WAAiBX,MAAoC,EAAiB;QAAAxB,cAAA,GAAAoC,CAAA;QAAApC,cAAA,GAAAC,CAAA;QACpE,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACFH,kBAAkB,CAACuC,KAAK,CAAC,gBAAgB,CAAC;UAACrC,cAAA,GAAAC,CAAA;UAE3C,IAAIuB,MAAM,EAAE;YAAAxB,cAAA,GAAAsC,CAAA;YAAAtC,cAAA,GAAAC,CAAA;YACV,IAAI,CAACuB,MAAM,GAAAe,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAAChB,MAAM,EAAKA,MAAM,CAAE;UAC7C,CAAC;YAAAxB,cAAA,GAAAsC,CAAA;UAAA;UAAAtC,cAAA,GAAAC,CAAA;UAID,MAAM,IAAI,CAACwC,kBAAkB,CAAC,CAAC;UAACzC,cAAA,GAAAC,CAAA;UAEhC,IAAI,CAACsB,aAAa,GAAG,IAAI;UAACvB,cAAA,GAAAC,CAAA;UAC1BH,kBAAkB,CAAC4C,GAAG,CAAC,gBAAgB,CAAC;QAC1C,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAA3C,cAAA,GAAAC,CAAA;UACd2C,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UAAC3C,cAAA,GAAAC,CAAA;UACxD,MAAM,IAAI4C,KAAK,CAAC,iCAAiC,CAAC;QACpD;MACF,CAAC;MAAA,SAlBKC,UAAUA,CAAAC,EAAA;QAAA,OAAAb,WAAA,CAAAc,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVH,UAAU;IAAA;EAAA;IAAAd,GAAA;IAAAC,KAAA;MAAA,IAAAiB,WAAA,GAAAf,iBAAA,CAuBhB,WACEgB,SAA2D,EAEtB;QAAA,IADrCC,UAAkB,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,OAAAjD,cAAA,GAAAsC,CAAA,UAAG,CAAC;QAAAtC,cAAA,GAAAoC,CAAA;QAAApC,cAAA,GAAAC,CAAA;QAEtB,IAAI,CAAC,IAAI,CAACsB,aAAa,EAAE;UAAAvB,cAAA,GAAAsC,CAAA;UAAAtC,cAAA,GAAAC,CAAA;UACvB,MAAM,IAAI4C,KAAK,CAAC,mCAAmC,CAAC;QACtD,CAAC;UAAA7C,cAAA,GAAAsC,CAAA;QAAA;QAAAtC,cAAA,GAAAC,CAAA;QAED,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACFH,kBAAkB,CAACuC,KAAK,CAAC,gBAAgB,CAAC;UAI1C,IAAMkB,UAAU,IAAAvD,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACuD,qBAAqB,CAACJ,UAAU,CAAC;UAACpD,cAAA,GAAAC,CAAA;UAEhEH,kBAAkB,CAAC4C,GAAG,CAAC,gBAAgB,CAAC;UAAC1C,cAAA,GAAAC,CAAA;UACzC,OAAOsD,UAAU;QACnB,CAAC,CAAC,OAAOZ,KAAK,EAAE;UAAA3C,cAAA,GAAAC,CAAA;UACd2C,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAAC3C,cAAA,GAAAC,CAAA;UAC/C,OAAO,IAAI;QACb;MACF,CAAC;MAAA,SArBKwD,UAAUA,CAAAC,GAAA;QAAA,OAAAR,WAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVQ,UAAU;IAAA;EAAA;IAAAzB,GAAA;IAAAC,KAAA,EA0BhB,SAAA0B,qBAAqBA,CAACC,UAA+B,EAA0B;MAAA5D,cAAA,GAAAoC,CAAA;MAAApC,cAAA,GAAAC,CAAA;MAC7E,IAAI;QAAAD,cAAA,GAAAC,CAAA;QACFH,kBAAkB,CAACuC,KAAK,CAAC,0BAA0B,CAAC;QAEpD,IAAMwB,SAAS,IAAA7D,cAAA,GAAAC,CAAA,QAAG2D,UAAU,CAACC,SAAS;QAGtC,IAAMC,SAAS,IAAA9D,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC8D,kBAAkB,CAACF,SAAS,CAAC;QAGpD,IAAMG,YAAY,IAAAhE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACgE,sBAAsB,CAACJ,SAAS,EAAEC,SAAS,CAAC;QAGtE,IAAMI,YAAY,IAAAlE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACkE,mBAAmB,CAACN,SAAS,CAAC;QAGxD,IAAMO,gBAAgB,IAAApE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACoE,wBAAwB,CAACR,SAAS,EAAEC,SAAS,CAAC;QAE5E,IAAMQ,QAAgC,IAAAtE,cAAA,GAAAC,CAAA,QAAG;UACvC+D,YAAY,EAAEA,YAAY,CAACO,IAAI;UAC/BC,UAAU,EAAER,YAAY,CAACQ,UAAU;UACnCV,SAAS,EAATA,SAAS;UACTI,YAAY,EAAZA,YAAY;UACZE,gBAAgB,EAAhBA;QACF,CAAC;QAACpE,cAAA,GAAAC,CAAA;QAEFH,kBAAkB,CAAC4C,GAAG,CAAC,0BAA0B,CAAC;QAAC1C,cAAA,GAAAC,CAAA;QACnD,OAAOqE,QAAQ;MACjB,CAAC,CAAC,OAAO3B,KAAK,EAAE;QAAA3C,cAAA,GAAAC,CAAA;QACd2C,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAAC3C,cAAA,GAAAC,CAAA;QACzD,MAAM0C,KAAK;MACb;IACF;EAAC;IAAAX,GAAA;IAAAC,KAAA;MAAA,IAAAwC,mBAAA,GAAAtC,iBAAA,CAKD,WACEuC,MAA4D,EAC5DC,UAAuC,EACJ;QAAA3E,cAAA,GAAAoC,CAAA;QACnC,IAAMwC,OAAiC,IAAA5E,cAAA,GAAAC,CAAA,QAAG,EAAE;QAACD,cAAA,GAAAC,CAAA;QAE7C,KAAK,IAAI4E,CAAC,IAAA7E,cAAA,GAAAC,CAAA,QAAG,CAAC,GAAE4E,CAAC,GAAGH,MAAM,CAACrB,MAAM,EAAEwB,CAAC,EAAE,EAAE;UACtC,IAAMjB,UAAU,IAAA5D,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACwD,UAAU,CAACiB,MAAM,CAACG,CAAC,CAAC,EAAEA,CAAC,CAAC;UAAC7E,cAAA,GAAAC,CAAA;UAEvD,IAAI2D,UAAU,EAAE;YAAA5D,cAAA,GAAAsC,CAAA;YACd,IAAMgC,QAAQ,IAAAtE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC0D,qBAAqB,CAACC,UAAU,CAAC;YAAC5D,cAAA,GAAAC,CAAA;YACxD2E,OAAO,CAACE,IAAI,CAACR,QAAQ,CAAC;UACxB,CAAC;YAAAtE,cAAA,GAAAsC,CAAA;UAAA;UAAAtC,cAAA,GAAAC,CAAA;UAED,IAAI0E,UAAU,EAAE;YAAA3E,cAAA,GAAAsC,CAAA;YAAAtC,cAAA,GAAAC,CAAA;YACd0E,UAAU,CAAC,CAACE,CAAC,GAAG,CAAC,IAAIH,MAAM,CAACrB,MAAM,CAAC;UACrC,CAAC;YAAArD,cAAA,GAAAsC,CAAA;UAAA;QACH;QAACtC,cAAA,GAAAC,CAAA;QAED,OAAO2E,OAAO;MAChB,CAAC;MAAA,SApBKG,kBAAkBA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAR,mBAAA,CAAAzB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlB8B,kBAAkB;IAAA;EAAA;IAAA/C,GAAA;IAAAC,KAAA,EAyBxB,SAAQ8B,kBAAkBA,CAACF,SAAyB,EAAuC;MAAA7D,cAAA,GAAAoC,CAAA;MAEzF,IAAM8C,aAAa,IAAAlF,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACkF,cAAc,CACvCtB,SAAS,CAAC9D,qBAAqB,CAACG,aAAa,CAAC,EAC9C2D,SAAS,CAAC9D,qBAAqB,CAACK,UAAU,CAAC,EAC3CyD,SAAS,CAAC9D,qBAAqB,CAACO,UAAU,CAC5C,CAAC;MAGD,IAAM8E,UAAU,IAAApF,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACkF,cAAc,CACpCtB,SAAS,CAAC9D,qBAAqB,CAACG,aAAa,CAAC,EAC9C2D,SAAS,CAAC9D,qBAAqB,CAACK,UAAU,CAAC,EAC3CyD,SAAS,CAAC9D,qBAAqB,CAACO,UAAU,CAC5C,CAAC;MAGD,IAAM+E,QAAQ,IAAArF,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACkF,cAAc,CAClCtB,SAAS,CAAC9D,qBAAqB,CAACG,aAAa,CAAC,EAC9C2D,SAAS,CAAC9D,qBAAqB,CAACS,QAAQ,CAAC,EACzCqD,SAAS,CAAC9D,qBAAqB,CAACW,SAAS,CAC3C,CAAC;MAGD,IAAM4E,SAAS,IAAAtF,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACkF,cAAc,CACnCtB,SAAS,CAAC9D,qBAAqB,CAACS,QAAQ,CAAC,EACzCqD,SAAS,CAAC9D,qBAAqB,CAACW,SAAS,CAAC,EAC1CmD,SAAS,CAAC9D,qBAAqB,CAACa,UAAU,CAC5C,CAAC;MAACZ,cAAA,GAAAC,CAAA;MAEF,OAAO;QACLiF,aAAa,EAAbA,aAAa;QACbE,UAAU,EAAVA,UAAU;QACVG,UAAU,EAAE,CAAC;QACbF,QAAQ,EAARA,QAAQ;QACRC,SAAS,EAATA;MACF,CAAC;IACH;EAAC;IAAAtD,GAAA;IAAAC,KAAA,EAKD,SAAQkD,cAAcA,CAACK,MAAoB,EAAEC,MAAoB,EAAEC,MAAoB,EAAU;MAAA1F,cAAA,GAAAoC,CAAA;MAC/F,IAAMuD,OAAO,IAAA3F,cAAA,GAAAC,CAAA,QAAG;QACd2F,CAAC,EAAEJ,MAAM,CAACI,CAAC,GAAGH,MAAM,CAACG,CAAC;QACtBC,CAAC,EAAEL,MAAM,CAACK,CAAC,GAAGJ,MAAM,CAACI;MACvB,CAAC;MAED,IAAMC,OAAO,IAAA9F,cAAA,GAAAC,CAAA,QAAG;QACd2F,CAAC,EAAEF,MAAM,CAACE,CAAC,GAAGH,MAAM,CAACG,CAAC;QACtBC,CAAC,EAAEH,MAAM,CAACG,CAAC,GAAGJ,MAAM,CAACI;MACvB,CAAC;MAED,IAAME,UAAU,IAAA/F,cAAA,GAAAC,CAAA,QAAG0F,OAAO,CAACC,CAAC,GAAGE,OAAO,CAACF,CAAC,GAAGD,OAAO,CAACE,CAAC,GAAGC,OAAO,CAACD,CAAC;MAChE,IAAMG,UAAU,IAAAhG,cAAA,GAAAC,CAAA,QAAGgG,IAAI,CAACC,IAAI,CAACP,OAAO,CAACC,CAAC,GAAGD,OAAO,CAACC,CAAC,GAAGD,OAAO,CAACE,CAAC,GAAGF,OAAO,CAACE,CAAC,CAAC;MAC3E,IAAMM,UAAU,IAAAnG,cAAA,GAAAC,CAAA,QAAGgG,IAAI,CAACC,IAAI,CAACJ,OAAO,CAACF,CAAC,GAAGE,OAAO,CAACF,CAAC,GAAGE,OAAO,CAACD,CAAC,GAAGC,OAAO,CAACD,CAAC,CAAC;MAE3E,IAAMO,QAAQ,IAAApG,cAAA,GAAAC,CAAA,QAAG8F,UAAU,IAAIC,UAAU,GAAGG,UAAU,CAAC;MACvD,IAAME,KAAK,IAAArG,cAAA,GAAAC,CAAA,QAAGgG,IAAI,CAACK,IAAI,CAACL,IAAI,CAACM,GAAG,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACO,GAAG,CAAC,CAAC,EAAEJ,QAAQ,CAAC,CAAC,CAAC;MAACpG,cAAA,GAAAC,CAAA;MAE7D,OAAQoG,KAAK,GAAG,GAAG,GAAIJ,IAAI,CAACQ,EAAE;IAChC;EAAC;IAAAzE,GAAA;IAAAC,KAAA,EAKD,SAAQgC,sBAAsBA,CAC5BJ,SAAyB,EACzB6C,MAA2C,EAC2B;MAAA1G,cAAA,GAAAoC,CAAA;MAItE,IAAMuE,UAAU,IAAA3G,cAAA,GAAAC,CAAA,QAAG4D,SAAS,CAAC9D,qBAAqB,CAACQ,WAAW,CAAC;MAC/D,IAAMqG,SAAS,IAAA5G,cAAA,GAAAC,CAAA,QAAG4D,SAAS,CAAC9D,qBAAqB,CAACO,UAAU,CAAC;MAC7D,IAAMuG,aAAa,IAAA7G,cAAA,GAAAC,CAAA,QAAG4D,SAAS,CAAC9D,qBAAqB,CAACI,cAAc,CAAC;MACrE,IAAM2G,YAAY,IAAA9G,cAAA,GAAAC,CAAA,QAAG4D,SAAS,CAAC9D,qBAAqB,CAACG,aAAa,CAAC;MAGnE,IAAM6G,aAAa,IAAA/G,cAAA,GAAAC,CAAA,QAAG0G,UAAU,CAACd,CAAC,GAAGe,SAAS,CAACf,CAAC;MAAC7F,cAAA,GAAAC,CAAA;MAGjD,IAAI,CAAAD,cAAA,GAAAsC,CAAA,UAAAoE,MAAM,CAACxB,aAAa,GAAG,GAAG,MAAAlF,cAAA,GAAAsC,CAAA,UAAIqE,UAAU,CAACd,CAAC,GAAGgB,aAAa,CAAChB,CAAC,GAAE;QAAA7F,cAAA,GAAAsC,CAAA;QAAAtC,cAAA,GAAAC,CAAA;QAChE,OAAO;UAAEsE,IAAI,EAAE,OAAO;UAAEC,UAAU,EAAE;QAAK,CAAC;MAC5C,CAAC,MAAM;QAAAxE,cAAA,GAAAsC,CAAA;QAAAtC,cAAA,GAAAC,CAAA;QAAA,IAAI,CAAAD,cAAA,GAAAsC,CAAA,UAAAyE,aAAa,MAAA/G,cAAA,GAAAsC,CAAA,UAAIqE,UAAU,CAACf,CAAC,GAAGiB,aAAa,CAACjB,CAAC,GAAE;UAAA5F,cAAA,GAAAsC,CAAA;UAAAtC,cAAA,GAAAC,CAAA;UAC1D,OAAO;YAAEsE,IAAI,EAAE,UAAU;YAAEC,UAAU,EAAE;UAAK,CAAC;QAC/C,CAAC,MAAM;UAAAxE,cAAA,GAAAsC,CAAA;UAAAtC,cAAA,GAAAC,CAAA;UAAA,IAAI,CAAAD,cAAA,GAAAsC,CAAA,WAAAyE,aAAa,MAAA/G,cAAA,GAAAsC,CAAA,WAAIqE,UAAU,CAACf,CAAC,GAAGiB,aAAa,CAACjB,CAAC,GAAE;YAAA5F,cAAA,GAAAsC,CAAA;YAAAtC,cAAA,GAAAC,CAAA;YAC1D,OAAO;cAAEsE,IAAI,EAAE,UAAU;cAAEC,UAAU,EAAE;YAAK,CAAC;UAC/C,CAAC,MAAM;YAAAxE,cAAA,GAAAsC,CAAA;YAAAtC,cAAA,GAAAC,CAAA;YAAA,IAAI0G,UAAU,CAACd,CAAC,GAAGgB,aAAa,CAAChB,CAAC,GAAG,GAAG,EAAE;cAAA7F,cAAA,GAAAsC,CAAA;cAAAtC,cAAA,GAAAC,CAAA;cAC/C,OAAO;gBAAEsE,IAAI,EAAE,QAAQ;gBAAEC,UAAU,EAAE;cAAK,CAAC;YAC7C,CAAC,MAAM;cAAAxE,cAAA,GAAAsC,CAAA;cAAAtC,cAAA,GAAAC,CAAA;cACL,OAAO;gBAAEsE,IAAI,EAAE,SAAS;gBAAEC,UAAU,EAAE;cAAK,CAAC;YAC9C;UAAA;QAAA;MAAA;IACF;EAAC;IAAAxC,GAAA;IAAAC,KAAA,EAKD,SAAQkC,mBAAmBA,CAACN,SAAyB,EAA0C;MAAA7D,cAAA,GAAAoC,CAAA;MAC7F,IAAM4E,QAAQ,IAAAhH,cAAA,GAAAC,CAAA,QAAG4D,SAAS,CAAC9D,qBAAqB,CAACa,UAAU,CAAC;MAC5D,IAAMqG,SAAS,IAAAjH,cAAA,GAAAC,CAAA,QAAG4D,SAAS,CAAC9D,qBAAqB,CAACc,WAAW,CAAC;MAC9D,IAAMqG,OAAO,IAAAlH,cAAA,GAAAC,CAAA,QAAG4D,SAAS,CAAC9D,qBAAqB,CAACS,QAAQ,CAAC;MACzD,IAAM2G,QAAQ,IAAAnH,cAAA,GAAAC,CAAA,QAAG4D,SAAS,CAAC9D,qBAAqB,CAACU,SAAS,CAAC;MAG3D,IAAM2G,YAAY,IAAApH,cAAA,GAAAC,CAAA,QAAGgG,IAAI,CAACoB,GAAG,CAACL,QAAQ,CAACpB,CAAC,GAAGqB,SAAS,CAACrB,CAAC,CAAC;MACvD,IAAI0B,MAAqC;MAACtH,cAAA,GAAAC,CAAA;MAE1C,IAAImH,YAAY,GAAG,GAAG,EAAE;QAAApH,cAAA,GAAAsC,CAAA;QAAAtC,cAAA,GAAAC,CAAA;QACtBqH,MAAM,GAAG,MAAM;MACjB,CAAC,MAAM;QAAAtH,cAAA,GAAAsC,CAAA;QAAAtC,cAAA,GAAAC,CAAA;QAAA,IAAImH,YAAY,GAAG,GAAG,EAAE;UAAApH,cAAA,GAAAsC,CAAA;UAAAtC,cAAA,GAAAC,CAAA;UAC7BqH,MAAM,GAAG,QAAQ;QACnB,CAAC,MAAM;UAAAtH,cAAA,GAAAsC,CAAA;UAAAtC,cAAA,GAAAC,CAAA;UACLqH,MAAM,GAAG,SAAS;QACpB;MAAA;MAGA,IAAMC,SAAS,IAAAvH,cAAA,GAAAC,CAAA,QAAG,CAACiH,OAAO,CAACtB,CAAC,GAAGuB,QAAQ,CAACvB,CAAC,IAAI,CAAC;MAC9C,IAAM4B,UAAU,IAAAxH,cAAA,GAAAC,CAAA,QAAG,CAAC+G,QAAQ,CAACpB,CAAC,GAAGqB,SAAS,CAACrB,CAAC,IAAI,CAAC;MACjD,IAAM6B,WAAW,IAAAzH,cAAA,GAAAC,CAAA,QAAGsH,SAAS,GAAGC,UAAU;MAE1C,IAAIE,MAA2C;MAAC1H,cAAA,GAAAC,CAAA;MAChD,IAAIwH,WAAW,GAAG,IAAI,EAAE;QAAAzH,cAAA,GAAAsC,CAAA;QAAAtC,cAAA,GAAAC,CAAA;QACtByH,MAAM,GAAG,SAAS;MACpB,CAAC,MAAM;QAAA1H,cAAA,GAAAsC,CAAA;QAAAtC,cAAA,GAAAC,CAAA;QAAA,IAAIwH,WAAW,GAAG,CAAC,IAAI,EAAE;UAAAzH,cAAA,GAAAsC,CAAA;UAAAtC,cAAA,GAAAC,CAAA;UAC9ByH,MAAM,GAAG,UAAU;QACrB,CAAC,MAAM;UAAA1H,cAAA,GAAAsC,CAAA;UAAAtC,cAAA,GAAAC,CAAA;UACLyH,MAAM,GAAG,UAAU;QACrB;MAAA;MAGA,IAAMC,OAAO,IAAA3H,cAAA,GAAAC,CAAA,QAAGgG,IAAI,CAACM,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGN,IAAI,CAACoB,GAAG,CAACI,WAAW,CAAC,GAAG,EAAE,CAAC;MAACzH,cAAA,GAAAC,CAAA;MAE5D,OAAO;QACLqH,MAAM,EAANA,MAAM;QACNI,MAAM,EAANA,MAAM;QACNC,OAAO,EAAPA;MACF,CAAC;IACH;EAAC;IAAA3F,GAAA;IAAAC,KAAA,EAKD,SAAQoC,wBAAwBA,CAC9BR,SAAyB,EACzB6C,MAA2C,EACC;MAAA1G,cAAA,GAAAoC,CAAA;MAC5C,IAAMuE,UAAU,IAAA3G,cAAA,GAAAC,CAAA,QAAG4D,SAAS,CAAC9D,qBAAqB,CAACQ,WAAW,CAAC;MAC/D,IAAMsG,aAAa,IAAA7G,cAAA,GAAAC,CAAA,QAAG4D,SAAS,CAAC9D,qBAAqB,CAACI,cAAc,CAAC;MAGrE,IAAIyH,cAAyC;MAC7C,IAAMC,WAAW,IAAA7H,cAAA,GAAAC,CAAA,QAAG4G,aAAa,CAAChB,CAAC,GAAGc,UAAU,CAACd,CAAC;MAAC7F,cAAA,GAAAC,CAAA;MAEnD,IAAI4H,WAAW,GAAG,GAAG,EAAE;QAAA7H,cAAA,GAAAsC,CAAA;QAAAtC,cAAA,GAAAC,CAAA;QACrB2H,cAAc,GAAG,MAAM;MACzB,CAAC,MAAM;QAAA5H,cAAA,GAAAsC,CAAA;QAAAtC,cAAA,GAAAC,CAAA;QAAA,IAAI4H,WAAW,GAAG,CAAC,GAAG,EAAE;UAAA7H,cAAA,GAAAsC,CAAA;UAAAtC,cAAA,GAAAC,CAAA;UAC7B2H,cAAc,GAAG,QAAQ;QAC3B,CAAC,MAAM;UAAA5H,cAAA,GAAAsC,CAAA;UAAAtC,cAAA,GAAAC,CAAA;UACL2H,cAAc,GAAG,KAAK;QACxB;MAAA;MAGA,IAAIE,aAA8C;MAAC9H,cAAA,GAAAC,CAAA;MACnD,IAAIyG,MAAM,CAACtB,UAAU,GAAG,GAAG,EAAE;QAAApF,cAAA,GAAAsC,CAAA;QAAAtC,cAAA,GAAAC,CAAA;QAC3B6H,aAAa,GAAG,UAAU;MAC5B,CAAC,MAAM;QAAA9H,cAAA,GAAAsC,CAAA;QAAAtC,cAAA,GAAAC,CAAA;QAAA,IAAIyG,MAAM,CAACtB,UAAU,GAAG,GAAG,EAAE;UAAApF,cAAA,GAAAsC,CAAA;UAAAtC,cAAA,GAAAC,CAAA;UAClC6H,aAAa,GAAG,SAAS;QAC3B,CAAC,MAAM;UAAA9H,cAAA,GAAAsC,CAAA;UAAAtC,cAAA,GAAAC,CAAA;UACL6H,aAAa,GAAG,MAAM;QACxB;MAAA;MAGA,IAAIC,QAAoD;MACxD,IAAMf,QAAQ,IAAAhH,cAAA,GAAAC,CAAA,SAAG4D,SAAS,CAAC9D,qBAAqB,CAACa,UAAU,CAAC;MAC5D,IAAMqG,SAAS,IAAAjH,cAAA,GAAAC,CAAA,SAAG4D,SAAS,CAAC9D,qBAAqB,CAACc,WAAW,CAAC;MAC9D,IAAMmH,aAAa,IAAAhI,cAAA,GAAAC,CAAA,SAAG,CAAC,GAAGgG,IAAI,CAACoB,GAAG,CAACL,QAAQ,CAACnB,CAAC,GAAGoB,SAAS,CAACpB,CAAC,CAAC;MAAC7F,cAAA,GAAAC,CAAA;MAE7D,IAAI+H,aAAa,GAAG,GAAG,EAAE;QAAAhI,cAAA,GAAAsC,CAAA;QAAAtC,cAAA,GAAAC,CAAA;QACvB8H,QAAQ,GAAG,WAAW;MACxB,CAAC,MAAM;QAAA/H,cAAA,GAAAsC,CAAA;QAAAtC,cAAA,GAAAC,CAAA;QAAA,IAAI+H,aAAa,GAAG,GAAG,EAAE;UAAAhI,cAAA,GAAAsC,CAAA;UAAAtC,cAAA,GAAAC,CAAA;UAC9B8H,QAAQ,GAAG,MAAM;QACnB,CAAC,MAAM;UAAA/H,cAAA,GAAAsC,CAAA;UAAAtC,cAAA,GAAAC,CAAA;UACL8H,QAAQ,GAAG,mBAAmB;QAChC;MAAA;MAAC/H,cAAA,GAAAC,CAAA;MAED,OAAO;QACL2H,cAAc,EAAdA,cAAc;QACdE,aAAa,EAAbA,aAAa;QACbC,QAAQ,EAARA;MACF,CAAC;IACH;EAAC;IAAA/F,GAAA;IAAAC,KAAA;MAAA,IAAAgG,mBAAA,GAAA9F,iBAAA,CAKD,aAAkD;QAAAnC,cAAA,GAAAoC,CAAA;QAAApC,cAAA,GAAAC,CAAA;QAEhD,MAAM,IAAIiI,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAAnI,cAAA,GAAAoC,CAAA;UAAApC,cAAA,GAAAC,CAAA;UAAA,OAAAmI,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;QAAD,CAAC,CAAC;QAACnI,cAAA,GAAAC,CAAA;QACxD2C,OAAO,CAACyF,GAAG,CAAC,qCAAqC,CAAC;MACpD,CAAC;MAAA,SAJa5F,kBAAkBA,CAAA;QAAA,OAAAwF,mBAAA,CAAAjF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBR,kBAAkB;IAAA;EAAA;IAAAT,GAAA;IAAAC,KAAA;MAAA,IAAAqG,sBAAA,GAAAnG,iBAAA,CAShC,WAAoCiB,UAAkB,EAAgC;QAAApD,cAAA,GAAAoC,CAAA;QAEpF,IAAMyB,SAAyB,IAAA7D,cAAA,GAAAC,CAAA,SAAG,EAAE;QAACD,cAAA,GAAAC,CAAA;QAGrC,KAAK,IAAI4E,CAAC,IAAA7E,cAAA,GAAAC,CAAA,SAAG,CAAC,GAAE4E,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;UAAA7E,cAAA,GAAAC,CAAA;UAC3B4D,SAAS,CAACiB,IAAI,CAAC;YACbc,CAAC,EAAEK,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;YAC5B1C,CAAC,EAAEI,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;YAC5BC,CAAC,EAAEvC,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;YAC5BE,UAAU,EAAExC,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UACpC,CAAC,CAAC;QACJ;QAACvI,cAAA,GAAAC,CAAA;QAED,OAAO;UACL4D,SAAS,EAATA,SAAS;UACTW,UAAU,EAAEyB,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UACrCG,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACrBxF,UAAU,EAAVA;QACF,CAAC;MACH,CAAC;MAAA,SApBaI,qBAAqBA,CAAAqF,GAAA;QAAA,OAAAP,sBAAA,CAAAtF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBO,qBAAqB;IAAA;EAAA;IAAAxB,GAAA;IAAAC,KAAA,EAyBnC,SAAA6G,YAAYA,CAACtH,MAAmC,EAAQ;MAAAxB,cAAA,GAAAoC,CAAA;MAAApC,cAAA,GAAAC,CAAA;MACtD,IAAI,CAACuB,MAAM,GAAAe,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAAChB,MAAM,EAAKA,MAAM,CAAE;IAC7C;EAAC;IAAAQ,GAAA;IAAAC,KAAA,EAKD,SAAA8G,SAASA,CAAA,EAAuB;MAAA/I,cAAA,GAAAoC,CAAA;MAAApC,cAAA,GAAAC,CAAA;MAC9B,OAAAsC,MAAA,CAAAC,MAAA,KAAY,IAAI,CAAChB,MAAM;IACzB;EAAC;IAAAQ,GAAA;IAAAC,KAAA,EAKD,SAAA+G,OAAOA,CAAA,EAAY;MAAAhJ,cAAA,GAAAoC,CAAA;MAAApC,cAAA,GAAAC,CAAA;MACjB,OAAO,IAAI,CAACsB,aAAa;IAC3B;EAAC;IAAAS,GAAA;IAAAC,KAAA,EAKD,SAAAgH,OAAOA,CAAA,EAAS;MAAAjJ,cAAA,GAAAoC,CAAA;MAAApC,cAAA,GAAAC,CAAA;MACd,IAAI,CAACsB,aAAa,GAAG,KAAK;IAE5B;EAAC;AAAA;AAIH,OAAO,IAAM2H,gBAAgB,IAAAlJ,cAAA,GAAAC,CAAA,SAAG,IAAIoB,gBAAgB,CAAC,CAAC", "ignoreList": []}