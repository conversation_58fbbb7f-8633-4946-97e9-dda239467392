ed8d6ba1952c2c7a8751595eecbfda25
"use strict";

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var TaskQueue = function () {
  function TaskQueue(_ref) {
    (0, _classCallCheck2.default)(this, TaskQueue);
    var onMoreTasks = _ref.onMoreTasks;
    this._onMoreTasks = onMoreTasks;
    this._queueStack = [{
      tasks: [],
      popable: true
    }];
  }
  return (0, _createClass2.default)(TaskQueue, [{
    key: "enqueue",
    value: function enqueue(task) {
      this._getCurrentQueue().push(task);
    }
  }, {
    key: "enqueueTasks",
    value: function enqueueTasks(tasks) {
      var _this = this;
      tasks.forEach(function (task) {
        return _this.enqueue(task);
      });
    }
  }, {
    key: "cancelTasks",
    value: function cancelTasks(tasksToCancel) {
      this._queueStack = this._queueStack.map(function (queue) {
        return (0, _objectSpread2.default)((0, _objectSpread2.default)({}, queue), {}, {
          tasks: queue.tasks.filter(function (task) {
            return tasksToCancel.indexOf(task) === -1;
          })
        });
      }).filter(function (queue, idx) {
        return queue.tasks.length > 0 || idx === 0;
      });
    }
  }, {
    key: "hasTasksToProcess",
    value: function hasTasksToProcess() {
      return this._getCurrentQueue().length > 0;
    }
  }, {
    key: "processNext",
    value: function processNext() {
      var queue = this._getCurrentQueue();
      if (queue.length) {
        var task = queue.shift();
        try {
          if (typeof task === 'object' && task.gen) {
            this._genPromise(task);
          } else if (typeof task === 'object' && task.run) {
            task.run();
          } else {
            (0, _invariant.default)(typeof task === 'function', 'Expected Function, SimpleTask, or PromiseTask, but got:\n' + JSON.stringify(task, null, 2));
            task();
          }
        } catch (e) {
          e.message = 'TaskQueue: Error with task ' + (task.name || '') + ': ' + e.message;
          throw e;
        }
      }
    }
  }, {
    key: "_getCurrentQueue",
    value: function _getCurrentQueue() {
      var stackIdx = this._queueStack.length - 1;
      var queue = this._queueStack[stackIdx];
      if (queue.popable && queue.tasks.length === 0 && stackIdx > 0) {
        this._queueStack.pop();
        return this._getCurrentQueue();
      } else {
        return queue.tasks;
      }
    }
  }, {
    key: "_genPromise",
    value: function _genPromise(task) {
      var _this2 = this;
      var length = this._queueStack.push({
        tasks: [],
        popable: false
      });
      var stackIdx = length - 1;
      var stackItem = this._queueStack[stackIdx];
      task.gen().then(function () {
        stackItem.popable = true;
        _this2.hasTasksToProcess() && _this2._onMoreTasks();
      }).catch(function (ex) {
        setTimeout(function () {
          ex.message = "TaskQueue: Error resolving Promise in task " + task.name + ": " + ex.message;
          throw ex;
        }, 0);
      });
    }
  }]);
}();
var _default = exports.default = TaskQueue;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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