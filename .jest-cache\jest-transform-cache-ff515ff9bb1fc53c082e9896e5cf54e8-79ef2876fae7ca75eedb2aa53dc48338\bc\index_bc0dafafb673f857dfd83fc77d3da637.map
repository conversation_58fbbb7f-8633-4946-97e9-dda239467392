{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_UIManager", "TextInputState", "_currentlyFocusedNode", "currentlyFocusedField", "document", "activeElement", "focusTextInput", "textFieldNode", "focus", "blurTextInput", "blur", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _UIManager = _interopRequireDefault(require(\"../../exports/UIManager\"));\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n/**\n * This class is responsible for coordinating the \"focused\"\n * state for TextInputs. All calls relating to the keyboard\n * should be funneled through here\n */\nvar TextInputState = {\n  /**\n   * Internal state\n   */\n  _currentlyFocusedNode: null,\n  /**\n   * Returns the ID of the currently focused text field, if one exists\n   * If no text field is focused it returns null\n   */\n  currentlyFocusedField() {\n    if (document.activeElement !== this._currentlyFocusedNode) {\n      this._currentlyFocusedNode = null;\n    }\n    return this._currentlyFocusedNode;\n  },\n  /**\n   * @param {Object} TextInputID id of the text field to focus\n   * Focuses the specified text field\n   * noop if the text field was already focused\n   */\n  focusTextInput(textFieldNode) {\n    if (textFieldNode !== null) {\n      this._currentlyFocusedNode = textFieldNode;\n      if (document.activeElement !== textFieldNode) {\n        _UIManager.default.focus(textFieldNode);\n      }\n    }\n  },\n  /**\n   * @param {Object} textFieldNode id of the text field to focus\n   * Unfocuses the specified text field\n   * noop if it wasn't focused\n   */\n  blurTextInput(textFieldNode) {\n    if (textFieldNode !== null) {\n      this._currentlyFocusedNode = null;\n      if (document.activeElement === textFieldNode) {\n        _UIManager.default.blur(textFieldNode);\n      }\n    }\n  }\n};\nvar _default = exports.default = TextInputState;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,UAAU,GAAGL,sBAAsB,CAACC,OAAO,0BAA0B,CAAC,CAAC;AAgB3E,IAAIK,cAAc,GAAG;EAInBC,qBAAqB,EAAE,IAAI;EAK3BC,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;IACtB,IAAIC,QAAQ,CAACC,aAAa,KAAK,IAAI,CAACH,qBAAqB,EAAE;MACzD,IAAI,CAACA,qBAAqB,GAAG,IAAI;IACnC;IACA,OAAO,IAAI,CAACA,qBAAqB;EACnC,CAAC;EAMDI,cAAc,WAAdA,cAAcA,CAACC,aAAa,EAAE;IAC5B,IAAIA,aAAa,KAAK,IAAI,EAAE;MAC1B,IAAI,CAACL,qBAAqB,GAAGK,aAAa;MAC1C,IAAIH,QAAQ,CAACC,aAAa,KAAKE,aAAa,EAAE;QAC5CP,UAAU,CAACH,OAAO,CAACW,KAAK,CAACD,aAAa,CAAC;MACzC;IACF;EACF,CAAC;EAMDE,aAAa,WAAbA,aAAaA,CAACF,aAAa,EAAE;IAC3B,IAAIA,aAAa,KAAK,IAAI,EAAE;MAC1B,IAAI,CAACL,qBAAqB,GAAG,IAAI;MACjC,IAAIE,QAAQ,CAACC,aAAa,KAAKE,aAAa,EAAE;QAC5CP,UAAU,CAACH,OAAO,CAACa,IAAI,CAACH,aAAa,CAAC;MACxC;IACF;EACF;AACF,CAAC;AACD,IAAII,QAAQ,GAAGb,OAAO,CAACD,OAAO,GAAGI,cAAc;AAC/CW,MAAM,CAACd,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}