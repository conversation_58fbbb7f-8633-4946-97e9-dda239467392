e1112ca51d7d5330b646f42e2bfe4aa2
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = exports.cancelIdleCallback = void 0;
var _canUseDom = _interopRequireDefault(require("../canUseDom"));
var _requestIdleCallback = function _requestIdleCallback(cb, options) {
  return setTimeout(function () {
    var start = Date.now();
    cb({
      didTimeout: false,
      timeRemaining: function timeRemaining() {
        return Math.max(0, 50 - (Date.now() - start));
      }
    });
  }, 1);
};
var _cancelIdleCallback = function _cancelIdleCallback(id) {
  clearTimeout(id);
};
var isSupported = _canUseDom.default && typeof window.requestIdleCallback !== 'undefined';
var requestIdleCallback = isSupported ? window.requestIdleCallback : _requestIdleCallback;
var cancelIdleCallback = exports.cancelIdleCallback = isSupported ? window.cancelIdleCallback : _cancelIdleCallback;
var _default = exports.default = requestIdleCallback;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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