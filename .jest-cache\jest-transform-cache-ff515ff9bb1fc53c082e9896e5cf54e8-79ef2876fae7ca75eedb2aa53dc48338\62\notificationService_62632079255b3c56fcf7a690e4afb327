df37f8f94e7d83f9be690b86195ca28f
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
function cov_7khvxhdkt() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\notificationService.ts";
  var hash = "a892827adb150a92f436f9959b64a93ba34665bd";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\notificationService.ts",
    statementMap: {
      "0": {
        start: {
          line: 30,
          column: 0
        },
        end: {
          line: 36,
          column: 3
        }
      },
      "1": {
        start: {
          line: 31,
          column: 35
        },
        end: {
          line: 35,
          column: 3
        }
      },
      "2": {
        start: {
          line: 39,
          column: 41
        },
        end: {
          line: 39,
          column: 45
        }
      },
      "3": {
        start: {
          line: 40,
          column: 55
        },
        end: {
          line: 47,
          column: 3
        }
      },
      "4": {
        start: {
          line: 53,
          column: 4
        },
        end: {
          line: 83,
          column: 5
        }
      },
      "5": {
        start: {
          line: 55,
          column: 41
        },
        end: {
          line: 55,
          column: 82
        }
      },
      "6": {
        start: {
          line: 56,
          column: 24
        },
        end: {
          line: 56,
          column: 38
        }
      },
      "7": {
        start: {
          line: 58,
          column: 6
        },
        end: {
          line: 61,
          column: 7
        }
      },
      "8": {
        start: {
          line: 59,
          column: 27
        },
        end: {
          line: 59,
          column: 72
        }
      },
      "9": {
        start: {
          line: 60,
          column: 8
        },
        end: {
          line: 60,
          column: 29
        }
      },
      "10": {
        start: {
          line: 63,
          column: 6
        },
        end: {
          line: 66,
          column: 7
        }
      },
      "11": {
        start: {
          line: 64,
          column: 8
        },
        end: {
          line: 64,
          column: 60
        }
      },
      "12": {
        start: {
          line: 65,
          column: 8
        },
        end: {
          line: 65,
          column: 21
        }
      },
      "13": {
        start: {
          line: 69,
          column: 6
        },
        end: {
          line: 71,
          column: 7
        }
      },
      "14": {
        start: {
          line: 70,
          column: 8
        },
        end: {
          line: 70,
          column: 59
        }
      },
      "15": {
        start: {
          line: 74,
          column: 6
        },
        end: {
          line: 76,
          column: 7
        }
      },
      "16": {
        start: {
          line: 75,
          column: 8
        },
        end: {
          line: 75,
          column: 42
        }
      },
      "17": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 78,
          column: 67
        }
      },
      "18": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 79,
          column: 18
        }
      },
      "19": {
        start: {
          line: 81,
          column: 6
        },
        end: {
          line: 81,
          column: 64
        }
      },
      "20": {
        start: {
          line: 82,
          column: 6
        },
        end: {
          line: 82,
          column: 19
        }
      },
      "21": {
        start: {
          line: 90,
          column: 4
        },
        end: {
          line: 106,
          column: 5
        }
      },
      "22": {
        start: {
          line: 91,
          column: 29
        },
        end: {
          line: 100,
          column: 8
        }
      },
      "23": {
        start: {
          line: 102,
          column: 6
        },
        end: {
          line: 102,
          column: 28
        }
      },
      "24": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 64
        }
      },
      "25": {
        start: {
          line: 105,
          column: 6
        },
        end: {
          line: 105,
          column: 18
        }
      },
      "26": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 132,
          column: 5
        }
      },
      "27": {
        start: {
          line: 117,
          column: 29
        },
        end: {
          line: 126,
          column: 8
        }
      },
      "28": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 28
        }
      },
      "29": {
        start: {
          line: 130,
          column: 6
        },
        end: {
          line: 130,
          column: 61
        }
      },
      "30": {
        start: {
          line: 131,
          column: 6
        },
        end: {
          line: 131,
          column: 18
        }
      },
      "31": {
        start: {
          line: 139,
          column: 4
        },
        end: {
          line: 139,
          column: 53
        }
      },
      "32": {
        start: {
          line: 139,
          column: 46
        },
        end: {
          line: 139,
          column: 53
        }
      },
      "33": {
        start: {
          line: 141,
          column: 4
        },
        end: {
          line: 175,
          column: 5
        }
      },
      "34": {
        start: {
          line: 143,
          column: 6
        },
        end: {
          line: 143,
          column: 55
        }
      },
      "35": {
        start: {
          line: 145,
          column: 19
        },
        end: {
          line: 153,
          column: 7
        }
      },
      "36": {
        start: {
          line: 156,
          column: 6
        },
        end: {
          line: 172,
          column: 7
        }
      },
      "37": {
        start: {
          line: 156,
          column: 19
        },
        end: {
          line: 156,
          column: 20
        }
      },
      "38": {
        start: {
          line: 157,
          column: 20
        },
        end: {
          line: 157,
          column: 41
        }
      },
      "39": {
        start: {
          line: 158,
          column: 57
        },
        end: {
          line: 162,
          column: 9
        }
      },
      "40": {
        start: {
          line: 164,
          column: 8
        },
        end: {
          line: 171,
          column: 10
        }
      },
      "41": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 59
        }
      },
      "42": {
        start: {
          line: 186,
          column: 4
        },
        end: {
          line: 188,
          column: 5
        }
      },
      "43": {
        start: {
          line: 187,
          column: 6
        },
        end: {
          line: 187,
          column: 57
        }
      },
      "44": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 206,
          column: 5
        }
      },
      "45": {
        start: {
          line: 191,
          column: 54
        },
        end: {
          line: 193,
          column: 7
        }
      },
      "46": {
        start: {
          line: 195,
          column: 6
        },
        end: {
          line: 202,
          column: 8
        }
      },
      "47": {
        start: {
          line: 204,
          column: 6
        },
        end: {
          line: 204,
          column: 66
        }
      },
      "48": {
        start: {
          line: 205,
          column: 6
        },
        end: {
          line: 205,
          column: 18
        }
      },
      "49": {
        start: {
          line: 216,
          column: 4
        },
        end: {
          line: 216,
          column: 61
        }
      },
      "50": {
        start: {
          line: 216,
          column: 54
        },
        end: {
          line: 216,
          column: 61
        }
      },
      "51": {
        start: {
          line: 218,
          column: 4
        },
        end: {
          line: 227,
          column: 5
        }
      },
      "52": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 224,
          column: 9
        }
      },
      "53": {
        start: {
          line: 226,
          column: 6
        },
        end: {
          line: 226,
          column: 70
        }
      },
      "54": {
        start: {
          line: 238,
          column: 4
        },
        end: {
          line: 238,
          column: 59
        }
      },
      "55": {
        start: {
          line: 238,
          column: 52
        },
        end: {
          line: 238,
          column: 59
        }
      },
      "56": {
        start: {
          line: 240,
          column: 4
        },
        end: {
          line: 253,
          column: 5
        }
      },
      "57": {
        start: {
          line: 241,
          column: 26
        },
        end: {
          line: 241,
          column: 47
        }
      },
      "58": {
        start: {
          line: 242,
          column: 22
        },
        end: {
          line: 244,
          column: 64
        }
      },
      "59": {
        start: {
          line: 246,
          column: 6
        },
        end: {
          line: 250,
          column: 9
        }
      },
      "60": {
        start: {
          line: 252,
          column: 6
        },
        end: {
          line: 252,
          column: 61
        }
      },
      "61": {
        start: {
          line: 264,
          column: 4
        },
        end: {
          line: 266,
          column: 5
        }
      },
      "62": {
        start: {
          line: 265,
          column: 6
        },
        end: {
          line: 265,
          column: 54
        }
      },
      "63": {
        start: {
          line: 268,
          column: 4
        },
        end: {
          line: 291,
          column: 5
        }
      },
      "64": {
        start: {
          line: 270,
          column: 27
        },
        end: {
          line: 270,
          column: 73
        }
      },
      "65": {
        start: {
          line: 272,
          column: 22
        },
        end: {
          line: 274,
          column: 51
        }
      },
      "66": {
        start: {
          line: 276,
          column: 54
        },
        end: {
          line: 278,
          column: 7
        }
      },
      "67": {
        start: {
          line: 280,
          column: 6
        },
        end: {
          line: 287,
          column: 8
        }
      },
      "68": {
        start: {
          line: 289,
          column: 6
        },
        end: {
          line: 289,
          column: 63
        }
      },
      "69": {
        start: {
          line: 290,
          column: 6
        },
        end: {
          line: 290,
          column: 18
        }
      },
      "70": {
        start: {
          line: 298,
          column: 4
        },
        end: {
          line: 302,
          column: 5
        }
      },
      "71": {
        start: {
          line: 299,
          column: 6
        },
        end: {
          line: 299,
          column: 75
        }
      },
      "72": {
        start: {
          line: 301,
          column: 6
        },
        end: {
          line: 301,
          column: 60
        }
      },
      "73": {
        start: {
          line: 309,
          column: 4
        },
        end: {
          line: 321,
          column: 5
        }
      },
      "74": {
        start: {
          line: 310,
          column: 37
        },
        end: {
          line: 310,
          column: 92
        }
      },
      "75": {
        start: {
          line: 312,
          column: 36
        },
        end: {
          line: 314,
          column: 7
        }
      },
      "76": {
        start: {
          line: 313,
          column: 24
        },
        end: {
          line: 313,
          column: 63
        }
      },
      "77": {
        start: {
          line: 316,
          column: 6
        },
        end: {
          line: 318,
          column: 7
        }
      },
      "78": {
        start: {
          line: 317,
          column: 8
        },
        end: {
          line: 317,
          column: 86
        }
      },
      "79": {
        start: {
          line: 320,
          column: 6
        },
        end: {
          line: 320,
          column: 68
        }
      },
      "80": {
        start: {
          line: 328,
          column: 4
        },
        end: {
          line: 333,
          column: 5
        }
      },
      "81": {
        start: {
          line: 329,
          column: 6
        },
        end: {
          line: 329,
          column: 69
        }
      },
      "82": {
        start: {
          line: 331,
          column: 6
        },
        end: {
          line: 331,
          column: 69
        }
      },
      "83": {
        start: {
          line: 332,
          column: 6
        },
        end: {
          line: 332,
          column: 16
        }
      },
      "84": {
        start: {
          line: 340,
          column: 4
        },
        end: {
          line: 343,
          column: 6
        }
      },
      "85": {
        start: {
          line: 346,
          column: 4
        },
        end: {
          line: 352,
          column: 5
        }
      },
      "86": {
        start: {
          line: 347,
          column: 6
        },
        end: {
          line: 351,
          column: 7
        }
      },
      "87": {
        start: {
          line: 348,
          column: 8
        },
        end: {
          line: 348,
          column: 33
        }
      },
      "88": {
        start: {
          line: 350,
          column: 8
        },
        end: {
          line: 350,
          column: 51
        }
      },
      "89": {
        start: {
          line: 359,
          column: 4
        },
        end: {
          line: 359,
          column: 44
        }
      },
      "90": {
        start: {
          line: 366,
          column: 4
        },
        end: {
          line: 366,
          column: 30
        }
      },
      "91": {
        start: {
          line: 372,
          column: 4
        },
        end: {
          line: 379,
          column: 5
        }
      },
      "92": {
        start: {
          line: 373,
          column: 20
        },
        end: {
          line: 373,
          column: 70
        }
      },
      "93": {
        start: {
          line: 374,
          column: 6
        },
        end: {
          line: 374,
          column: 45
        }
      },
      "94": {
        start: {
          line: 375,
          column: 6
        },
        end: {
          line: 375,
          column: 19
        }
      },
      "95": {
        start: {
          line: 377,
          column: 6
        },
        end: {
          line: 377,
          column: 61
        }
      },
      "96": {
        start: {
          line: 378,
          column: 6
        },
        end: {
          line: 378,
          column: 18
        }
      },
      "97": {
        start: {
          line: 383,
          column: 4
        },
        end: {
          line: 415,
          column: 5
        }
      },
      "98": {
        start: {
          line: 385,
          column: 6
        },
        end: {
          line: 390,
          column: 9
        }
      },
      "99": {
        start: {
          line: 392,
          column: 6
        },
        end: {
          line: 398,
          column: 9
        }
      },
      "100": {
        start: {
          line: 400,
          column: 6
        },
        end: {
          line: 405,
          column: 9
        }
      },
      "101": {
        start: {
          line: 407,
          column: 6
        },
        end: {
          line: 412,
          column: 9
        }
      },
      "102": {
        start: {
          line: 414,
          column: 6
        },
        end: {
          line: 414,
          column: 65
        }
      },
      "103": {
        start: {
          line: 419,
          column: 35
        },
        end: {
          line: 419,
          column: 60
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 31,
            column: 22
          },
          end: {
            line: 31,
            column: 23
          }
        },
        loc: {
          start: {
            line: 31,
            column: 35
          },
          end: {
            line: 35,
            column: 3
          }
        },
        line: 31
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 52,
            column: 2
          },
          end: {
            line: 52,
            column: 3
          }
        },
        loc: {
          start: {
            line: 52,
            column: 39
          },
          end: {
            line: 84,
            column: 3
          }
        },
        line: 52
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 89,
            column: 2
          },
          end: {
            line: 89,
            column: 3
          }
        },
        loc: {
          start: {
            line: 89,
            column: 77
          },
          end: {
            line: 107,
            column: 3
          }
        },
        line: 89
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 112,
            column: 2
          },
          end: {
            line: 112,
            column: 3
          }
        },
        loc: {
          start: {
            line: 115,
            column: 21
          },
          end: {
            line: 133,
            column: 3
          }
        },
        line: 115
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 138,
            column: 2
          },
          end: {
            line: 138,
            column: 3
          }
        },
        loc: {
          start: {
            line: 138,
            column: 43
          },
          end: {
            line: 176,
            column: 3
          }
        },
        line: 138
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 181,
            column: 2
          },
          end: {
            line: 181,
            column: 3
          }
        },
        loc: {
          start: {
            line: 185,
            column: 21
          },
          end: {
            line: 207,
            column: 3
          }
        },
        line: 185
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 212,
            column: 2
          },
          end: {
            line: 212,
            column: 3
          }
        },
        loc: {
          start: {
            line: 215,
            column: 19
          },
          end: {
            line: 228,
            column: 3
          }
        },
        line: 215
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 233,
            column: 2
          },
          end: {
            line: 233,
            column: 3
          }
        },
        loc: {
          start: {
            line: 237,
            column: 19
          },
          end: {
            line: 254,
            column: 3
          }
        },
        line: 237
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 259,
            column: 2
          },
          end: {
            line: 259,
            column: 3
          }
        },
        loc: {
          start: {
            line: 263,
            column: 21
          },
          end: {
            line: 292,
            column: 3
          }
        },
        line: 263
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 297,
            column: 2
          },
          end: {
            line: 297,
            column: 3
          }
        },
        loc: {
          start: {
            line: 297,
            column: 66
          },
          end: {
            line: 303,
            column: 3
          }
        },
        line: 297
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 308,
            column: 2
          },
          end: {
            line: 308,
            column: 3
          }
        },
        loc: {
          start: {
            line: 308,
            column: 61
          },
          end: {
            line: 322,
            column: 3
          }
        },
        line: 308
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 313,
            column: 8
          },
          end: {
            line: 313,
            column: 9
          }
        },
        loc: {
          start: {
            line: 313,
            column: 24
          },
          end: {
            line: 313,
            column: 63
          }
        },
        line: 313
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 327,
            column: 2
          },
          end: {
            line: 327,
            column: 3
          }
        },
        loc: {
          start: {
            line: 327,
            column: 82
          },
          end: {
            line: 334,
            column: 3
          }
        },
        line: 327
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 339,
            column: 2
          },
          end: {
            line: 339,
            column: 3
          }
        },
        loc: {
          start: {
            line: 339,
            column: 67
          },
          end: {
            line: 353,
            column: 3
          }
        },
        line: 339
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 358,
            column: 2
          },
          end: {
            line: 358,
            column: 3
          }
        },
        loc: {
          start: {
            line: 358,
            column: 38
          },
          end: {
            line: 360,
            column: 3
          }
        },
        line: 358
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 365,
            column: 2
          },
          end: {
            line: 365,
            column: 3
          }
        },
        loc: {
          start: {
            line: 365,
            column: 32
          },
          end: {
            line: 367,
            column: 3
          }
        },
        line: 365
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 371,
            column: 2
          },
          end: {
            line: 371,
            column: 3
          }
        },
        loc: {
          start: {
            line: 371,
            column: 52
          },
          end: {
            line: 380,
            column: 3
          }
        },
        line: 371
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 382,
            column: 2
          },
          end: {
            line: 382,
            column: 3
          }
        },
        loc: {
          start: {
            line: 382,
            column: 54
          },
          end: {
            line: 416,
            column: 3
          }
        },
        line: 382
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 58,
            column: 6
          },
          end: {
            line: 61,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 58,
            column: 6
          },
          end: {
            line: 61,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 58
      },
      "1": {
        loc: {
          start: {
            line: 63,
            column: 6
          },
          end: {
            line: 66,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 63,
            column: 6
          },
          end: {
            line: 66,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 63
      },
      "2": {
        loc: {
          start: {
            line: 69,
            column: 6
          },
          end: {
            line: 71,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 6
          },
          end: {
            line: 71,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 69
      },
      "3": {
        loc: {
          start: {
            line: 74,
            column: 6
          },
          end: {
            line: 76,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 6
          },
          end: {
            line: 76,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "4": {
        loc: {
          start: {
            line: 95,
            column: 16
          },
          end: {
            line: 95,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 95,
            column: 16
          },
          end: {
            line: 95,
            column: 28
          }
        }, {
          start: {
            line: 95,
            column: 32
          },
          end: {
            line: 95,
            column: 34
          }
        }],
        line: 95
      },
      "5": {
        loc: {
          start: {
            line: 121,
            column: 16
          },
          end: {
            line: 121,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 121,
            column: 16
          },
          end: {
            line: 121,
            column: 28
          }
        }, {
          start: {
            line: 121,
            column: 32
          },
          end: {
            line: 121,
            column: 34
          }
        }],
        line: 121
      },
      "6": {
        loc: {
          start: {
            line: 139,
            column: 4
          },
          end: {
            line: 139,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 4
          },
          end: {
            line: 139,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 139
      },
      "7": {
        loc: {
          start: {
            line: 186,
            column: 4
          },
          end: {
            line: 188,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 186,
            column: 4
          },
          end: {
            line: 188,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 186
      },
      "8": {
        loc: {
          start: {
            line: 216,
            column: 4
          },
          end: {
            line: 216,
            column: 61
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 216,
            column: 4
          },
          end: {
            line: 216,
            column: 61
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 216
      },
      "9": {
        loc: {
          start: {
            line: 238,
            column: 4
          },
          end: {
            line: 238,
            column: 59
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 238,
            column: 4
          },
          end: {
            line: 238,
            column: 59
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 238
      },
      "10": {
        loc: {
          start: {
            line: 242,
            column: 22
          },
          end: {
            line: 244,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 243,
            column: 10
          },
          end: {
            line: 243,
            column: 88
          }
        }, {
          start: {
            line: 244,
            column: 10
          },
          end: {
            line: 244,
            column: 64
          }
        }],
        line: 242
      },
      "11": {
        loc: {
          start: {
            line: 264,
            column: 4
          },
          end: {
            line: 266,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 264,
            column: 4
          },
          end: {
            line: 266,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 264
      },
      "12": {
        loc: {
          start: {
            line: 272,
            column: 22
          },
          end: {
            line: 274,
            column: 51
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 273,
            column: 10
          },
          end: {
            line: 273,
            column: 66
          }
        }, {
          start: {
            line: 274,
            column: 10
          },
          end: {
            line: 274,
            column: 51
          }
        }],
        line: 272
      },
      "13": {
        loc: {
          start: {
            line: 346,
            column: 4
          },
          end: {
            line: 352,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 346,
            column: 4
          },
          end: {
            line: 352,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 346
      },
      "14": {
        loc: {
          start: {
            line: 347,
            column: 6
          },
          end: {
            line: 351,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 347,
            column: 6
          },
          end: {
            line: 351,
            column: 7
          }
        }, {
          start: {
            line: 349,
            column: 13
          },
          end: {
            line: 351,
            column: 7
          }
        }],
        line: 347
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a892827adb150a92f436f9959b64a93ba34665bd"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_7khvxhdkt = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_7khvxhdkt();
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
cov_7khvxhdkt().s[0]++;
Notifications.setNotificationHandler({
  handleNotification: function () {
    var _handleNotification = _asyncToGenerator(function* () {
      cov_7khvxhdkt().f[0]++;
      cov_7khvxhdkt().s[1]++;
      return {
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true
      };
    });
    function handleNotification() {
      return _handleNotification.apply(this, arguments);
    }
    return handleNotification;
  }()
});
var NotificationService = function () {
  function NotificationService() {
    _classCallCheck(this, NotificationService);
    this.expoPushToken = (cov_7khvxhdkt().s[2]++, null);
    this.notificationSettings = (cov_7khvxhdkt().s[3]++, {
      dailyTips: true,
      trainingReminders: true,
      matchReminders: true,
      achievementAlerts: true,
      progressUpdates: true,
      socialNotifications: false
    });
  }
  return _createClass(NotificationService, [{
    key: "initialize",
    value: (function () {
      var _initialize = _asyncToGenerator(function* () {
        cov_7khvxhdkt().f[1]++;
        cov_7khvxhdkt().s[4]++;
        try {
          var _ref = (cov_7khvxhdkt().s[5]++, yield Notifications.getPermissionsAsync()),
            existingStatus = _ref.status;
          var finalStatus = (cov_7khvxhdkt().s[6]++, existingStatus);
          cov_7khvxhdkt().s[7]++;
          if (existingStatus !== 'granted') {
            cov_7khvxhdkt().b[0][0]++;
            var _ref2 = (cov_7khvxhdkt().s[8]++, yield Notifications.requestPermissionsAsync()),
              status = _ref2.status;
            cov_7khvxhdkt().s[9]++;
            finalStatus = status;
          } else {
            cov_7khvxhdkt().b[0][1]++;
          }
          cov_7khvxhdkt().s[10]++;
          if (finalStatus !== 'granted') {
            cov_7khvxhdkt().b[1][0]++;
            cov_7khvxhdkt().s[11]++;
            console.log('Notification permissions not granted');
            cov_7khvxhdkt().s[12]++;
            return false;
          } else {
            cov_7khvxhdkt().b[1][1]++;
          }
          cov_7khvxhdkt().s[13]++;
          if (Device.isDevice) {
            cov_7khvxhdkt().b[2][0]++;
            cov_7khvxhdkt().s[14]++;
            this.expoPushToken = yield this.getExpoPushToken();
          } else {
            cov_7khvxhdkt().b[2][1]++;
          }
          cov_7khvxhdkt().s[15]++;
          if (Platform.OS === 'android') {
            cov_7khvxhdkt().b[3][0]++;
            cov_7khvxhdkt().s[16]++;
            yield this.setupAndroidChannels();
          } else {
            cov_7khvxhdkt().b[3][1]++;
          }
          cov_7khvxhdkt().s[17]++;
          console.log('Notification service initialized successfully');
          cov_7khvxhdkt().s[18]++;
          return true;
        } catch (error) {
          cov_7khvxhdkt().s[19]++;
          console.error('Error initializing notifications:', error);
          cov_7khvxhdkt().s[20]++;
          return false;
        }
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }())
  }, {
    key: "sendLocalNotification",
    value: (function () {
      var _sendLocalNotification = _asyncToGenerator(function* (content) {
        cov_7khvxhdkt().f[2]++;
        cov_7khvxhdkt().s[21]++;
        try {
          var notificationId = (cov_7khvxhdkt().s[22]++, yield Notifications.scheduleNotificationAsync({
            content: {
              title: content.title,
              body: content.body,
              data: (cov_7khvxhdkt().b[4][0]++, content.data) || (cov_7khvxhdkt().b[4][1]++, {}),
              sound: content.sound !== false,
              badge: content.badge
            },
            trigger: null
          }));
          cov_7khvxhdkt().s[23]++;
          return notificationId;
        } catch (error) {
          cov_7khvxhdkt().s[24]++;
          console.error('Error sending local notification:', error);
          cov_7khvxhdkt().s[25]++;
          throw error;
        }
      });
      function sendLocalNotification(_x) {
        return _sendLocalNotification.apply(this, arguments);
      }
      return sendLocalNotification;
    }())
  }, {
    key: "scheduleNotification",
    value: (function () {
      var _scheduleNotification = _asyncToGenerator(function* (content, trigger) {
        cov_7khvxhdkt().f[3]++;
        cov_7khvxhdkt().s[26]++;
        try {
          var notificationId = (cov_7khvxhdkt().s[27]++, yield Notifications.scheduleNotificationAsync({
            content: {
              title: content.title,
              body: content.body,
              data: (cov_7khvxhdkt().b[5][0]++, content.data) || (cov_7khvxhdkt().b[5][1]++, {}),
              sound: content.sound !== false,
              badge: content.badge
            },
            trigger: trigger
          }));
          cov_7khvxhdkt().s[28]++;
          return notificationId;
        } catch (error) {
          cov_7khvxhdkt().s[29]++;
          console.error('Error scheduling notification:', error);
          cov_7khvxhdkt().s[30]++;
          throw error;
        }
      });
      function scheduleNotification(_x2, _x3) {
        return _scheduleNotification.apply(this, arguments);
      }
      return scheduleNotification;
    }())
  }, {
    key: "scheduleDailyTips",
    value: (function () {
      var _scheduleDailyTips = _asyncToGenerator(function* () {
        cov_7khvxhdkt().f[4]++;
        cov_7khvxhdkt().s[31]++;
        if (!this.notificationSettings.dailyTips) {
          cov_7khvxhdkt().b[6][0]++;
          cov_7khvxhdkt().s[32]++;
          return;
        } else {
          cov_7khvxhdkt().b[6][1]++;
        }
        cov_7khvxhdkt().s[33]++;
        try {
          cov_7khvxhdkt().s[34]++;
          yield this.cancelNotificationsByTag('daily_tip');
          var tips = (cov_7khvxhdkt().s[35]++, ["Remember to warm up before playing tennis!", "Focus on your footwork today - it's the foundation of good tennis.", "Practice your serve toss for more consistent serves.", "Keep your eye on the ball through contact.", "Work on your split step timing at the net.", "Remember to follow through on your groundstrokes.", "Stay relaxed and loose between points."]);
          cov_7khvxhdkt().s[36]++;
          for (var i = (cov_7khvxhdkt().s[37]++, 0); i < 7; i++) {
            var tip = (cov_7khvxhdkt().s[38]++, tips[i % tips.length]);
            var trigger = (cov_7khvxhdkt().s[39]++, {
              hour: 9,
              minute: 0,
              repeats: true
            });
            cov_7khvxhdkt().s[40]++;
            yield this.scheduleNotification({
              title: "🎾 Daily Tennis Tip",
              body: tip,
              data: {
                type: 'daily_tip',
                day: i
              }
            }, trigger);
          }
        } catch (error) {
          cov_7khvxhdkt().s[41]++;
          console.error('Error scheduling daily tips:', error);
        }
      });
      function scheduleDailyTips() {
        return _scheduleDailyTips.apply(this, arguments);
      }
      return scheduleDailyTips;
    }())
  }, {
    key: "scheduleTrainingReminder",
    value: (function () {
      var _scheduleTrainingReminder = _asyncToGenerator(function* (title, message, scheduledTime) {
        cov_7khvxhdkt().f[5]++;
        cov_7khvxhdkt().s[42]++;
        if (!this.notificationSettings.trainingReminders) {
          cov_7khvxhdkt().b[7][0]++;
          cov_7khvxhdkt().s[43]++;
          throw new Error('Training reminders are disabled');
        } else {
          cov_7khvxhdkt().b[7][1]++;
        }
        cov_7khvxhdkt().s[44]++;
        try {
          var trigger = (cov_7khvxhdkt().s[45]++, {
            date: scheduledTime
          });
          cov_7khvxhdkt().s[46]++;
          return yield this.scheduleNotification({
            title: `🏃‍♂️ ${title}`,
            body: message,
            data: {
              type: 'training_reminder'
            }
          }, trigger);
        } catch (error) {
          cov_7khvxhdkt().s[47]++;
          console.error('Error scheduling training reminder:', error);
          cov_7khvxhdkt().s[48]++;
          throw error;
        }
      });
      function scheduleTrainingReminder(_x4, _x5, _x6) {
        return _scheduleTrainingReminder.apply(this, arguments);
      }
      return scheduleTrainingReminder;
    }())
  }, {
    key: "sendAchievementNotification",
    value: (function () {
      var _sendAchievementNotification = _asyncToGenerator(function* (achievementTitle, description) {
        cov_7khvxhdkt().f[6]++;
        cov_7khvxhdkt().s[49]++;
        if (!this.notificationSettings.achievementAlerts) {
          cov_7khvxhdkt().b[8][0]++;
          cov_7khvxhdkt().s[50]++;
          return;
        } else {
          cov_7khvxhdkt().b[8][1]++;
        }
        cov_7khvxhdkt().s[51]++;
        try {
          cov_7khvxhdkt().s[52]++;
          yield this.sendLocalNotification({
            title: "🏆 Achievement Unlocked!",
            body: `${achievementTitle}: ${description}`,
            data: {
              type: 'achievement'
            },
            sound: true
          });
        } catch (error) {
          cov_7khvxhdkt().s[53]++;
          console.error('Error sending achievement notification:', error);
        }
      });
      function sendAchievementNotification(_x7, _x8) {
        return _sendAchievementNotification.apply(this, arguments);
      }
      return sendAchievementNotification;
    }())
  }, {
    key: "sendProgressUpdate",
    value: (function () {
      var _sendProgressUpdate = _asyncToGenerator(function* (skillName, oldRating, newRating) {
        cov_7khvxhdkt().f[7]++;
        cov_7khvxhdkt().s[54]++;
        if (!this.notificationSettings.progressUpdates) {
          cov_7khvxhdkt().b[9][0]++;
          cov_7khvxhdkt().s[55]++;
          return;
        } else {
          cov_7khvxhdkt().b[9][1]++;
        }
        cov_7khvxhdkt().s[56]++;
        try {
          var improvement = (cov_7khvxhdkt().s[57]++, newRating - oldRating);
          var message = (cov_7khvxhdkt().s[58]++, improvement > 0 ? (cov_7khvxhdkt().b[10][0]++, `Your ${skillName} improved by ${improvement} points! Now at ${newRating}/100`) : (cov_7khvxhdkt().b[10][1]++, `Your ${skillName} rating updated to ${newRating}/100`));
          cov_7khvxhdkt().s[59]++;
          yield this.sendLocalNotification({
            title: "📈 Progress Update",
            body: message,
            data: {
              type: 'progress_update',
              skill: skillName
            }
          });
        } catch (error) {
          cov_7khvxhdkt().s[60]++;
          console.error('Error sending progress update:', error);
        }
      });
      function sendProgressUpdate(_x9, _x0, _x1) {
        return _sendProgressUpdate.apply(this, arguments);
      }
      return sendProgressUpdate;
    }())
  }, {
    key: "scheduleMatchReminder",
    value: (function () {
      var _scheduleMatchReminder = _asyncToGenerator(function* (opponentName, matchTime, location) {
        cov_7khvxhdkt().f[8]++;
        cov_7khvxhdkt().s[61]++;
        if (!this.notificationSettings.matchReminders) {
          cov_7khvxhdkt().b[11][0]++;
          cov_7khvxhdkt().s[62]++;
          throw new Error('Match reminders are disabled');
        } else {
          cov_7khvxhdkt().b[11][1]++;
        }
        cov_7khvxhdkt().s[63]++;
        try {
          var reminderTime = (cov_7khvxhdkt().s[64]++, new Date(matchTime.getTime() - 60 * 60 * 1000));
          var message = (cov_7khvxhdkt().s[65]++, location ? (cov_7khvxhdkt().b[12][0]++, `Match against ${opponentName} at ${location} in 1 hour`) : (cov_7khvxhdkt().b[12][1]++, `Match against ${opponentName} in 1 hour`));
          var trigger = (cov_7khvxhdkt().s[66]++, {
            date: reminderTime
          });
          cov_7khvxhdkt().s[67]++;
          return yield this.scheduleNotification({
            title: "🎾 Match Reminder",
            body: message,
            data: {
              type: 'match_reminder',
              opponent: opponentName
            }
          }, trigger);
        } catch (error) {
          cov_7khvxhdkt().s[68]++;
          console.error('Error scheduling match reminder:', error);
          cov_7khvxhdkt().s[69]++;
          throw error;
        }
      });
      function scheduleMatchReminder(_x10, _x11, _x12) {
        return _scheduleMatchReminder.apply(this, arguments);
      }
      return scheduleMatchReminder;
    }())
  }, {
    key: "cancelNotification",
    value: (function () {
      var _cancelNotification = _asyncToGenerator(function* (notificationId) {
        cov_7khvxhdkt().f[9]++;
        cov_7khvxhdkt().s[70]++;
        try {
          cov_7khvxhdkt().s[71]++;
          yield Notifications.cancelScheduledNotificationAsync(notificationId);
        } catch (error) {
          cov_7khvxhdkt().s[72]++;
          console.error('Error canceling notification:', error);
        }
      });
      function cancelNotification(_x13) {
        return _cancelNotification.apply(this, arguments);
      }
      return cancelNotification;
    }())
  }, {
    key: "cancelNotificationsByTag",
    value: (function () {
      var _cancelNotificationsByTag = _asyncToGenerator(function* (tag) {
        cov_7khvxhdkt().f[10]++;
        cov_7khvxhdkt().s[73]++;
        try {
          var scheduledNotifications = (cov_7khvxhdkt().s[74]++, yield Notifications.getAllScheduledNotificationsAsync());
          var notificationsToCancel = (cov_7khvxhdkt().s[75]++, scheduledNotifications.filter(function (notification) {
            var _notification$content;
            cov_7khvxhdkt().f[11]++;
            cov_7khvxhdkt().s[76]++;
            return ((_notification$content = notification.content.data) == null ? void 0 : _notification$content.type) === tag;
          }));
          cov_7khvxhdkt().s[77]++;
          for (var notification of notificationsToCancel) {
            cov_7khvxhdkt().s[78]++;
            yield Notifications.cancelScheduledNotificationAsync(notification.identifier);
          }
        } catch (error) {
          cov_7khvxhdkt().s[79]++;
          console.error('Error canceling notifications by tag:', error);
        }
      });
      function cancelNotificationsByTag(_x14) {
        return _cancelNotificationsByTag.apply(this, arguments);
      }
      return cancelNotificationsByTag;
    }())
  }, {
    key: "getScheduledNotifications",
    value: (function () {
      var _getScheduledNotifications = _asyncToGenerator(function* () {
        cov_7khvxhdkt().f[12]++;
        cov_7khvxhdkt().s[80]++;
        try {
          cov_7khvxhdkt().s[81]++;
          return yield Notifications.getAllScheduledNotificationsAsync();
        } catch (error) {
          cov_7khvxhdkt().s[82]++;
          console.error('Error getting scheduled notifications:', error);
          cov_7khvxhdkt().s[83]++;
          return [];
        }
      });
      function getScheduledNotifications() {
        return _getScheduledNotifications.apply(this, arguments);
      }
      return getScheduledNotifications;
    }())
  }, {
    key: "updateSettings",
    value: function updateSettings(newSettings) {
      cov_7khvxhdkt().f[13]++;
      cov_7khvxhdkt().s[84]++;
      this.notificationSettings = Object.assign({}, this.notificationSettings, newSettings);
      cov_7khvxhdkt().s[85]++;
      if (newSettings.dailyTips !== undefined) {
        cov_7khvxhdkt().b[13][0]++;
        cov_7khvxhdkt().s[86]++;
        if (newSettings.dailyTips) {
          cov_7khvxhdkt().b[14][0]++;
          cov_7khvxhdkt().s[87]++;
          this.scheduleDailyTips();
        } else {
          cov_7khvxhdkt().b[14][1]++;
          cov_7khvxhdkt().s[88]++;
          this.cancelNotificationsByTag('daily_tip');
        }
      } else {
        cov_7khvxhdkt().b[13][1]++;
      }
    }
  }, {
    key: "getSettings",
    value: function getSettings() {
      cov_7khvxhdkt().f[14]++;
      cov_7khvxhdkt().s[89]++;
      return Object.assign({}, this.notificationSettings);
    }
  }, {
    key: "getPushToken",
    value: function getPushToken() {
      cov_7khvxhdkt().f[15]++;
      cov_7khvxhdkt().s[90]++;
      return this.expoPushToken;
    }
  }, {
    key: "getExpoPushToken",
    value: function () {
      var _getExpoPushToken = _asyncToGenerator(function* () {
        cov_7khvxhdkt().f[16]++;
        cov_7khvxhdkt().s[91]++;
        try {
          var token = (cov_7khvxhdkt().s[92]++, (yield Notifications.getExpoPushTokenAsync()).data);
          cov_7khvxhdkt().s[93]++;
          console.log('Expo push token:', token);
          cov_7khvxhdkt().s[94]++;
          return token;
        } catch (error) {
          cov_7khvxhdkt().s[95]++;
          console.error('Error getting Expo push token:', error);
          cov_7khvxhdkt().s[96]++;
          throw error;
        }
      });
      function getExpoPushToken() {
        return _getExpoPushToken.apply(this, arguments);
      }
      return getExpoPushToken;
    }()
  }, {
    key: "setupAndroidChannels",
    value: function () {
      var _setupAndroidChannels = _asyncToGenerator(function* () {
        cov_7khvxhdkt().f[17]++;
        cov_7khvxhdkt().s[97]++;
        try {
          cov_7khvxhdkt().s[98]++;
          yield Notifications.setNotificationChannelAsync('default', {
            name: 'Default',
            importance: Notifications.AndroidImportance.DEFAULT,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: '#3b82f6'
          });
          cov_7khvxhdkt().s[99]++;
          yield Notifications.setNotificationChannelAsync('achievements', {
            name: 'Achievements',
            importance: Notifications.AndroidImportance.HIGH,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: '#10b981',
            sound: 'default'
          });
          cov_7khvxhdkt().s[100]++;
          yield Notifications.setNotificationChannelAsync('reminders', {
            name: 'Reminders',
            importance: Notifications.AndroidImportance.DEFAULT,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: '#f59e0b'
          });
          cov_7khvxhdkt().s[101]++;
          yield Notifications.setNotificationChannelAsync('tips', {
            name: 'Daily Tips',
            importance: Notifications.AndroidImportance.LOW,
            vibrationPattern: [0, 250],
            lightColor: '#8b5cf6'
          });
        } catch (error) {
          cov_7khvxhdkt().s[102]++;
          console.error('Error setting up Android channels:', error);
        }
      });
      function setupAndroidChannels() {
        return _setupAndroidChannels.apply(this, arguments);
      }
      return setupAndroidChannels;
    }()
  }]);
}();
export var notificationService = (cov_7khvxhdkt().s[103]++, new NotificationService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJOb3RpZmljYXRpb25zIiwiRGV2aWNlIiwiUGxhdGZvcm0iLCJjb3ZfN2todnhoZGt0IiwicyIsInNldE5vdGlmaWNhdGlvbkhhbmRsZXIiLCJoYW5kbGVOb3RpZmljYXRpb24iLCJfaGFuZGxlTm90aWZpY2F0aW9uIiwiX2FzeW5jVG9HZW5lcmF0b3IiLCJmIiwic2hvdWxkU2hvd0FsZXJ0Iiwic2hvdWxkUGxheVNvdW5kIiwic2hvdWxkU2V0QmFkZ2UiLCJhcHBseSIsImFyZ3VtZW50cyIsIk5vdGlmaWNhdGlvblNlcnZpY2UiLCJfY2xhc3NDYWxsQ2hlY2siLCJleHBvUHVzaFRva2VuIiwibm90aWZpY2F0aW9uU2V0dGluZ3MiLCJkYWlseVRpcHMiLCJ0cmFpbmluZ1JlbWluZGVycyIsIm1hdGNoUmVtaW5kZXJzIiwiYWNoaWV2ZW1lbnRBbGVydHMiLCJwcm9ncmVzc1VwZGF0ZXMiLCJzb2NpYWxOb3RpZmljYXRpb25zIiwiX2NyZWF0ZUNsYXNzIiwia2V5IiwidmFsdWUiLCJfaW5pdGlhbGl6ZSIsIl9yZWYiLCJnZXRQZXJtaXNzaW9uc0FzeW5jIiwiZXhpc3RpbmdTdGF0dXMiLCJzdGF0dXMiLCJmaW5hbFN0YXR1cyIsImIiLCJfcmVmMiIsInJlcXVlc3RQZXJtaXNzaW9uc0FzeW5jIiwiY29uc29sZSIsImxvZyIsImlzRGV2aWNlIiwiZ2V0RXhwb1B1c2hUb2tlbiIsIk9TIiwic2V0dXBBbmRyb2lkQ2hhbm5lbHMiLCJlcnJvciIsImluaXRpYWxpemUiLCJfc2VuZExvY2FsTm90aWZpY2F0aW9uIiwiY29udGVudCIsIm5vdGlmaWNhdGlvbklkIiwic2NoZWR1bGVOb3RpZmljYXRpb25Bc3luYyIsInRpdGxlIiwiYm9keSIsImRhdGEiLCJzb3VuZCIsImJhZGdlIiwidHJpZ2dlciIsInNlbmRMb2NhbE5vdGlmaWNhdGlvbiIsIl94IiwiX3NjaGVkdWxlTm90aWZpY2F0aW9uIiwic2NoZWR1bGVOb3RpZmljYXRpb24iLCJfeDIiLCJfeDMiLCJfc2NoZWR1bGVEYWlseVRpcHMiLCJjYW5jZWxOb3RpZmljYXRpb25zQnlUYWciLCJ0aXBzIiwiaSIsInRpcCIsImxlbmd0aCIsImhvdXIiLCJtaW51dGUiLCJyZXBlYXRzIiwidHlwZSIsImRheSIsInNjaGVkdWxlRGFpbHlUaXBzIiwiX3NjaGVkdWxlVHJhaW5pbmdSZW1pbmRlciIsIm1lc3NhZ2UiLCJzY2hlZHVsZWRUaW1lIiwiRXJyb3IiLCJkYXRlIiwic2NoZWR1bGVUcmFpbmluZ1JlbWluZGVyIiwiX3g0IiwiX3g1IiwiX3g2IiwiX3NlbmRBY2hpZXZlbWVudE5vdGlmaWNhdGlvbiIsImFjaGlldmVtZW50VGl0bGUiLCJkZXNjcmlwdGlvbiIsInNlbmRBY2hpZXZlbWVudE5vdGlmaWNhdGlvbiIsIl94NyIsIl94OCIsIl9zZW5kUHJvZ3Jlc3NVcGRhdGUiLCJza2lsbE5hbWUiLCJvbGRSYXRpbmciLCJuZXdSYXRpbmciLCJpbXByb3ZlbWVudCIsInNraWxsIiwic2VuZFByb2dyZXNzVXBkYXRlIiwiX3g5IiwiX3gwIiwiX3gxIiwiX3NjaGVkdWxlTWF0Y2hSZW1pbmRlciIsIm9wcG9uZW50TmFtZSIsIm1hdGNoVGltZSIsImxvY2F0aW9uIiwicmVtaW5kZXJUaW1lIiwiRGF0ZSIsImdldFRpbWUiLCJvcHBvbmVudCIsInNjaGVkdWxlTWF0Y2hSZW1pbmRlciIsIl94MTAiLCJfeDExIiwiX3gxMiIsIl9jYW5jZWxOb3RpZmljYXRpb24iLCJjYW5jZWxTY2hlZHVsZWROb3RpZmljYXRpb25Bc3luYyIsImNhbmNlbE5vdGlmaWNhdGlvbiIsIl94MTMiLCJfY2FuY2VsTm90aWZpY2F0aW9uc0J5VGFnIiwidGFnIiwic2NoZWR1bGVkTm90aWZpY2F0aW9ucyIsImdldEFsbFNjaGVkdWxlZE5vdGlmaWNhdGlvbnNBc3luYyIsIm5vdGlmaWNhdGlvbnNUb0NhbmNlbCIsImZpbHRlciIsIm5vdGlmaWNhdGlvbiIsIl9ub3RpZmljYXRpb24kY29udGVudCIsImlkZW50aWZpZXIiLCJfeDE0IiwiX2dldFNjaGVkdWxlZE5vdGlmaWNhdGlvbnMiLCJnZXRTY2hlZHVsZWROb3RpZmljYXRpb25zIiwidXBkYXRlU2V0dGluZ3MiLCJuZXdTZXR0aW5ncyIsIk9iamVjdCIsImFzc2lnbiIsInVuZGVmaW5lZCIsImdldFNldHRpbmdzIiwiZ2V0UHVzaFRva2VuIiwiX2dldEV4cG9QdXNoVG9rZW4iLCJ0b2tlbiIsImdldEV4cG9QdXNoVG9rZW5Bc3luYyIsIl9zZXR1cEFuZHJvaWRDaGFubmVscyIsInNldE5vdGlmaWNhdGlvbkNoYW5uZWxBc3luYyIsIm5hbWUiLCJpbXBvcnRhbmNlIiwiQW5kcm9pZEltcG9ydGFuY2UiLCJERUZBVUxUIiwidmlicmF0aW9uUGF0dGVybiIsImxpZ2h0Q29sb3IiLCJISUdIIiwiTE9XIiwibm90aWZpY2F0aW9uU2VydmljZSJdLCJzb3VyY2VzIjpbIm5vdGlmaWNhdGlvblNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gUHVzaCBOb3RpZmljYXRpb24gU2VydmljZSBmb3IgQWNlTWluZCBUZW5uaXMgQXBwXG5pbXBvcnQgKiBhcyBOb3RpZmljYXRpb25zIGZyb20gJ2V4cG8tbm90aWZpY2F0aW9ucyc7XG5pbXBvcnQgKiBhcyBEZXZpY2UgZnJvbSAnZXhwby1kZXZpY2UnO1xuaW1wb3J0IHsgUGxhdGZvcm0gfSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuXG5leHBvcnQgaW50ZXJmYWNlIE5vdGlmaWNhdGlvbkNvbnRlbnQge1xuICB0aXRsZTogc3RyaW5nO1xuICBib2R5OiBzdHJpbmc7XG4gIGRhdGE/OiBhbnk7XG4gIHNvdW5kPzogYm9vbGVhbjtcbiAgYmFkZ2U/OiBudW1iZXI7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU2NoZWR1bGVkTm90aWZpY2F0aW9uIHtcbiAgaWQ6IHN0cmluZztcbiAgY29udGVudDogTm90aWZpY2F0aW9uQ29udGVudDtcbiAgdHJpZ2dlcjogTm90aWZpY2F0aW9ucy5Ob3RpZmljYXRpb25UcmlnZ2VySW5wdXQ7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTm90aWZpY2F0aW9uU2V0dGluZ3Mge1xuICBkYWlseVRpcHM6IGJvb2xlYW47XG4gIHRyYWluaW5nUmVtaW5kZXJzOiBib29sZWFuO1xuICBtYXRjaFJlbWluZGVyczogYm9vbGVhbjtcbiAgYWNoaWV2ZW1lbnRBbGVydHM6IGJvb2xlYW47XG4gIHByb2dyZXNzVXBkYXRlczogYm9vbGVhbjtcbiAgc29jaWFsTm90aWZpY2F0aW9uczogYm9vbGVhbjtcbn1cblxuLy8gQ29uZmlndXJlIG5vdGlmaWNhdGlvbiBiZWhhdmlvclxuTm90aWZpY2F0aW9ucy5zZXROb3RpZmljYXRpb25IYW5kbGVyKHtcbiAgaGFuZGxlTm90aWZpY2F0aW9uOiBhc3luYyAoKSA9PiAoe1xuICAgIHNob3VsZFNob3dBbGVydDogdHJ1ZSxcbiAgICBzaG91bGRQbGF5U291bmQ6IHRydWUsXG4gICAgc2hvdWxkU2V0QmFkZ2U6IHRydWUsXG4gIH0pLFxufSk7XG5cbmNsYXNzIE5vdGlmaWNhdGlvblNlcnZpY2Uge1xuICBwcml2YXRlIGV4cG9QdXNoVG9rZW46IHN0cmluZyB8IG51bGwgPSBudWxsO1xuICBwcml2YXRlIG5vdGlmaWNhdGlvblNldHRpbmdzOiBOb3RpZmljYXRpb25TZXR0aW5ncyA9IHtcbiAgICBkYWlseVRpcHM6IHRydWUsXG4gICAgdHJhaW5pbmdSZW1pbmRlcnM6IHRydWUsXG4gICAgbWF0Y2hSZW1pbmRlcnM6IHRydWUsXG4gICAgYWNoaWV2ZW1lbnRBbGVydHM6IHRydWUsXG4gICAgcHJvZ3Jlc3NVcGRhdGVzOiB0cnVlLFxuICAgIHNvY2lhbE5vdGlmaWNhdGlvbnM6IGZhbHNlLFxuICB9O1xuXG4gIC8qKlxuICAgKiBJbml0aWFsaXplIG5vdGlmaWNhdGlvbiBzZXJ2aWNlIGFuZCByZXF1ZXN0IHBlcm1pc3Npb25zXG4gICAqL1xuICBhc3luYyBpbml0aWFsaXplKCk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBSZXF1ZXN0IHBlcm1pc3Npb25zXG4gICAgICBjb25zdCB7IHN0YXR1czogZXhpc3RpbmdTdGF0dXMgfSA9IGF3YWl0IE5vdGlmaWNhdGlvbnMuZ2V0UGVybWlzc2lvbnNBc3luYygpO1xuICAgICAgbGV0IGZpbmFsU3RhdHVzID0gZXhpc3RpbmdTdGF0dXM7XG5cbiAgICAgIGlmIChleGlzdGluZ1N0YXR1cyAhPT0gJ2dyYW50ZWQnKSB7XG4gICAgICAgIGNvbnN0IHsgc3RhdHVzIH0gPSBhd2FpdCBOb3RpZmljYXRpb25zLnJlcXVlc3RQZXJtaXNzaW9uc0FzeW5jKCk7XG4gICAgICAgIGZpbmFsU3RhdHVzID0gc3RhdHVzO1xuICAgICAgfVxuXG4gICAgICBpZiAoZmluYWxTdGF0dXMgIT09ICdncmFudGVkJykge1xuICAgICAgICBjb25zb2xlLmxvZygnTm90aWZpY2F0aW9uIHBlcm1pc3Npb25zIG5vdCBncmFudGVkJyk7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgLy8gR2V0IHB1c2ggdG9rZW4gZm9yIHJlbW90ZSBub3RpZmljYXRpb25zXG4gICAgICBpZiAoRGV2aWNlLmlzRGV2aWNlKSB7XG4gICAgICAgIHRoaXMuZXhwb1B1c2hUb2tlbiA9IGF3YWl0IHRoaXMuZ2V0RXhwb1B1c2hUb2tlbigpO1xuICAgICAgfVxuXG4gICAgICAvLyBTZXQgdXAgbm90aWZpY2F0aW9uIGNoYW5uZWxzIGZvciBBbmRyb2lkXG4gICAgICBpZiAoUGxhdGZvcm0uT1MgPT09ICdhbmRyb2lkJykge1xuICAgICAgICBhd2FpdCB0aGlzLnNldHVwQW5kcm9pZENoYW5uZWxzKCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCdOb3RpZmljYXRpb24gc2VydmljZSBpbml0aWFsaXplZCBzdWNjZXNzZnVsbHknKTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbml0aWFsaXppbmcgbm90aWZpY2F0aW9uczonLCBlcnJvcik7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFNlbmQgbG9jYWwgbm90aWZpY2F0aW9uIGltbWVkaWF0ZWx5XG4gICAqL1xuICBhc3luYyBzZW5kTG9jYWxOb3RpZmljYXRpb24oY29udGVudDogTm90aWZpY2F0aW9uQ29udGVudCk6IFByb21pc2U8c3RyaW5nPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IG5vdGlmaWNhdGlvbklkID0gYXdhaXQgTm90aWZpY2F0aW9ucy5zY2hlZHVsZU5vdGlmaWNhdGlvbkFzeW5jKHtcbiAgICAgICAgY29udGVudDoge1xuICAgICAgICAgIHRpdGxlOiBjb250ZW50LnRpdGxlLFxuICAgICAgICAgIGJvZHk6IGNvbnRlbnQuYm9keSxcbiAgICAgICAgICBkYXRhOiBjb250ZW50LmRhdGEgfHwge30sXG4gICAgICAgICAgc291bmQ6IGNvbnRlbnQuc291bmQgIT09IGZhbHNlLFxuICAgICAgICAgIGJhZGdlOiBjb250ZW50LmJhZGdlLFxuICAgICAgICB9LFxuICAgICAgICB0cmlnZ2VyOiBudWxsLCAvLyBTZW5kIGltbWVkaWF0ZWx5XG4gICAgICB9KTtcblxuICAgICAgcmV0dXJuIG5vdGlmaWNhdGlvbklkO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZW5kaW5nIGxvY2FsIG5vdGlmaWNhdGlvbjonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogU2NoZWR1bGUgYSBub3RpZmljYXRpb24gZm9yIGxhdGVyXG4gICAqL1xuICBhc3luYyBzY2hlZHVsZU5vdGlmaWNhdGlvbihcbiAgICBjb250ZW50OiBOb3RpZmljYXRpb25Db250ZW50LFxuICAgIHRyaWdnZXI6IE5vdGlmaWNhdGlvbnMuTm90aWZpY2F0aW9uVHJpZ2dlcklucHV0XG4gICk6IFByb21pc2U8c3RyaW5nPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IG5vdGlmaWNhdGlvbklkID0gYXdhaXQgTm90aWZpY2F0aW9ucy5zY2hlZHVsZU5vdGlmaWNhdGlvbkFzeW5jKHtcbiAgICAgICAgY29udGVudDoge1xuICAgICAgICAgIHRpdGxlOiBjb250ZW50LnRpdGxlLFxuICAgICAgICAgIGJvZHk6IGNvbnRlbnQuYm9keSxcbiAgICAgICAgICBkYXRhOiBjb250ZW50LmRhdGEgfHwge30sXG4gICAgICAgICAgc291bmQ6IGNvbnRlbnQuc291bmQgIT09IGZhbHNlLFxuICAgICAgICAgIGJhZGdlOiBjb250ZW50LmJhZGdlLFxuICAgICAgICB9LFxuICAgICAgICB0cmlnZ2VyLFxuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiBub3RpZmljYXRpb25JZDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2NoZWR1bGluZyBub3RpZmljYXRpb246JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFNjaGVkdWxlIGRhaWx5IHRlbm5pcyB0aXBzXG4gICAqL1xuICBhc3luYyBzY2hlZHVsZURhaWx5VGlwcygpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBpZiAoIXRoaXMubm90aWZpY2F0aW9uU2V0dGluZ3MuZGFpbHlUaXBzKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgLy8gQ2FuY2VsIGV4aXN0aW5nIGRhaWx5IHRpcCBub3RpZmljYXRpb25zXG4gICAgICBhd2FpdCB0aGlzLmNhbmNlbE5vdGlmaWNhdGlvbnNCeVRhZygnZGFpbHlfdGlwJyk7XG5cbiAgICAgIGNvbnN0IHRpcHMgPSBbXG4gICAgICAgIFwiUmVtZW1iZXIgdG8gd2FybSB1cCBiZWZvcmUgcGxheWluZyB0ZW5uaXMhXCIsXG4gICAgICAgIFwiRm9jdXMgb24geW91ciBmb290d29yayB0b2RheSAtIGl0J3MgdGhlIGZvdW5kYXRpb24gb2YgZ29vZCB0ZW5uaXMuXCIsXG4gICAgICAgIFwiUHJhY3RpY2UgeW91ciBzZXJ2ZSB0b3NzIGZvciBtb3JlIGNvbnNpc3RlbnQgc2VydmVzLlwiLFxuICAgICAgICBcIktlZXAgeW91ciBleWUgb24gdGhlIGJhbGwgdGhyb3VnaCBjb250YWN0LlwiLFxuICAgICAgICBcIldvcmsgb24geW91ciBzcGxpdCBzdGVwIHRpbWluZyBhdCB0aGUgbmV0LlwiLFxuICAgICAgICBcIlJlbWVtYmVyIHRvIGZvbGxvdyB0aHJvdWdoIG9uIHlvdXIgZ3JvdW5kc3Ryb2tlcy5cIixcbiAgICAgICAgXCJTdGF5IHJlbGF4ZWQgYW5kIGxvb3NlIGJldHdlZW4gcG9pbnRzLlwiLFxuICAgICAgXTtcblxuICAgICAgLy8gU2NoZWR1bGUgdGlwcyBmb3IgdGhlIG5leHQgNyBkYXlzIGF0IDkgQU1cbiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgNzsgaSsrKSB7XG4gICAgICAgIGNvbnN0IHRpcCA9IHRpcHNbaSAlIHRpcHMubGVuZ3RoXTtcbiAgICAgICAgY29uc3QgdHJpZ2dlcjogTm90aWZpY2F0aW9ucy5EYWlseVRyaWdnZXJJbnB1dCA9IHtcbiAgICAgICAgICBob3VyOiA5LFxuICAgICAgICAgIG1pbnV0ZTogMCxcbiAgICAgICAgICByZXBlYXRzOiB0cnVlLFxuICAgICAgICB9O1xuXG4gICAgICAgIGF3YWl0IHRoaXMuc2NoZWR1bGVOb3RpZmljYXRpb24oXG4gICAgICAgICAge1xuICAgICAgICAgICAgdGl0bGU6IFwi8J+OviBEYWlseSBUZW5uaXMgVGlwXCIsXG4gICAgICAgICAgICBib2R5OiB0aXAsXG4gICAgICAgICAgICBkYXRhOiB7IHR5cGU6ICdkYWlseV90aXAnLCBkYXk6IGkgfSxcbiAgICAgICAgICB9LFxuICAgICAgICAgIHRyaWdnZXJcbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2NoZWR1bGluZyBkYWlseSB0aXBzOicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogU2NoZWR1bGUgdHJhaW5pbmcgcmVtaW5kZXJzXG4gICAqL1xuICBhc3luYyBzY2hlZHVsZVRyYWluaW5nUmVtaW5kZXIoXG4gICAgdGl0bGU6IHN0cmluZyxcbiAgICBtZXNzYWdlOiBzdHJpbmcsXG4gICAgc2NoZWR1bGVkVGltZTogRGF0ZVxuICApOiBQcm9taXNlPHN0cmluZz4ge1xuICAgIGlmICghdGhpcy5ub3RpZmljYXRpb25TZXR0aW5ncy50cmFpbmluZ1JlbWluZGVycykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdUcmFpbmluZyByZW1pbmRlcnMgYXJlIGRpc2FibGVkJyk7XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHRyaWdnZXI6IE5vdGlmaWNhdGlvbnMuRGF0ZVRyaWdnZXJJbnB1dCA9IHtcbiAgICAgICAgZGF0ZTogc2NoZWR1bGVkVGltZSxcbiAgICAgIH07XG5cbiAgICAgIHJldHVybiBhd2FpdCB0aGlzLnNjaGVkdWxlTm90aWZpY2F0aW9uKFxuICAgICAgICB7XG4gICAgICAgICAgdGl0bGU6IGDwn4+D4oCN4pmC77iPICR7dGl0bGV9YCxcbiAgICAgICAgICBib2R5OiBtZXNzYWdlLFxuICAgICAgICAgIGRhdGE6IHsgdHlwZTogJ3RyYWluaW5nX3JlbWluZGVyJyB9LFxuICAgICAgICB9LFxuICAgICAgICB0cmlnZ2VyXG4gICAgICApO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzY2hlZHVsaW5nIHRyYWluaW5nIHJlbWluZGVyOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBTZW5kIGFjaGlldmVtZW50IG5vdGlmaWNhdGlvblxuICAgKi9cbiAgYXN5bmMgc2VuZEFjaGlldmVtZW50Tm90aWZpY2F0aW9uKFxuICAgIGFjaGlldmVtZW50VGl0bGU6IHN0cmluZyxcbiAgICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gICk6IFByb21pc2U8dm9pZD4ge1xuICAgIGlmICghdGhpcy5ub3RpZmljYXRpb25TZXR0aW5ncy5hY2hpZXZlbWVudEFsZXJ0cykgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHRoaXMuc2VuZExvY2FsTm90aWZpY2F0aW9uKHtcbiAgICAgICAgdGl0bGU6IFwi8J+PhiBBY2hpZXZlbWVudCBVbmxvY2tlZCFcIixcbiAgICAgICAgYm9keTogYCR7YWNoaWV2ZW1lbnRUaXRsZX06ICR7ZGVzY3JpcHRpb259YCxcbiAgICAgICAgZGF0YTogeyB0eXBlOiAnYWNoaWV2ZW1lbnQnIH0sXG4gICAgICAgIHNvdW5kOiB0cnVlLFxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNlbmRpbmcgYWNoaWV2ZW1lbnQgbm90aWZpY2F0aW9uOicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogU2VuZCBwcm9ncmVzcyB1cGRhdGUgbm90aWZpY2F0aW9uXG4gICAqL1xuICBhc3luYyBzZW5kUHJvZ3Jlc3NVcGRhdGUoXG4gICAgc2tpbGxOYW1lOiBzdHJpbmcsXG4gICAgb2xkUmF0aW5nOiBudW1iZXIsXG4gICAgbmV3UmF0aW5nOiBudW1iZXJcbiAgKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgaWYgKCF0aGlzLm5vdGlmaWNhdGlvblNldHRpbmdzLnByb2dyZXNzVXBkYXRlcykgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGltcHJvdmVtZW50ID0gbmV3UmF0aW5nIC0gb2xkUmF0aW5nO1xuICAgICAgY29uc3QgbWVzc2FnZSA9IGltcHJvdmVtZW50ID4gMCBcbiAgICAgICAgPyBgWW91ciAke3NraWxsTmFtZX0gaW1wcm92ZWQgYnkgJHtpbXByb3ZlbWVudH0gcG9pbnRzISBOb3cgYXQgJHtuZXdSYXRpbmd9LzEwMGBcbiAgICAgICAgOiBgWW91ciAke3NraWxsTmFtZX0gcmF0aW5nIHVwZGF0ZWQgdG8gJHtuZXdSYXRpbmd9LzEwMGA7XG5cbiAgICAgIGF3YWl0IHRoaXMuc2VuZExvY2FsTm90aWZpY2F0aW9uKHtcbiAgICAgICAgdGl0bGU6IFwi8J+TiCBQcm9ncmVzcyBVcGRhdGVcIixcbiAgICAgICAgYm9keTogbWVzc2FnZSxcbiAgICAgICAgZGF0YTogeyB0eXBlOiAncHJvZ3Jlc3NfdXBkYXRlJywgc2tpbGw6IHNraWxsTmFtZSB9LFxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNlbmRpbmcgcHJvZ3Jlc3MgdXBkYXRlOicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogU2VuZCBtYXRjaCByZW1pbmRlclxuICAgKi9cbiAgYXN5bmMgc2NoZWR1bGVNYXRjaFJlbWluZGVyKFxuICAgIG9wcG9uZW50TmFtZTogc3RyaW5nLFxuICAgIG1hdGNoVGltZTogRGF0ZSxcbiAgICBsb2NhdGlvbj86IHN0cmluZ1xuICApOiBQcm9taXNlPHN0cmluZz4ge1xuICAgIGlmICghdGhpcy5ub3RpZmljYXRpb25TZXR0aW5ncy5tYXRjaFJlbWluZGVycykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdNYXRjaCByZW1pbmRlcnMgYXJlIGRpc2FibGVkJyk7XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIFNjaGVkdWxlIHJlbWluZGVyIDEgaG91ciBiZWZvcmUgbWF0Y2hcbiAgICAgIGNvbnN0IHJlbWluZGVyVGltZSA9IG5ldyBEYXRlKG1hdGNoVGltZS5nZXRUaW1lKCkgLSA2MCAqIDYwICogMTAwMCk7XG4gICAgICBcbiAgICAgIGNvbnN0IG1lc3NhZ2UgPSBsb2NhdGlvbiBcbiAgICAgICAgPyBgTWF0Y2ggYWdhaW5zdCAke29wcG9uZW50TmFtZX0gYXQgJHtsb2NhdGlvbn0gaW4gMSBob3VyYFxuICAgICAgICA6IGBNYXRjaCBhZ2FpbnN0ICR7b3Bwb25lbnROYW1lfSBpbiAxIGhvdXJgO1xuXG4gICAgICBjb25zdCB0cmlnZ2VyOiBOb3RpZmljYXRpb25zLkRhdGVUcmlnZ2VySW5wdXQgPSB7XG4gICAgICAgIGRhdGU6IHJlbWluZGVyVGltZSxcbiAgICAgIH07XG5cbiAgICAgIHJldHVybiBhd2FpdCB0aGlzLnNjaGVkdWxlTm90aWZpY2F0aW9uKFxuICAgICAgICB7XG4gICAgICAgICAgdGl0bGU6IFwi8J+OviBNYXRjaCBSZW1pbmRlclwiLFxuICAgICAgICAgIGJvZHk6IG1lc3NhZ2UsXG4gICAgICAgICAgZGF0YTogeyB0eXBlOiAnbWF0Y2hfcmVtaW5kZXInLCBvcHBvbmVudDogb3Bwb25lbnROYW1lIH0sXG4gICAgICAgIH0sXG4gICAgICAgIHRyaWdnZXJcbiAgICAgICk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNjaGVkdWxpbmcgbWF0Y2ggcmVtaW5kZXI6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIENhbmNlbCBzcGVjaWZpYyBub3RpZmljYXRpb25cbiAgICovXG4gIGFzeW5jIGNhbmNlbE5vdGlmaWNhdGlvbihub3RpZmljYXRpb25JZDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IE5vdGlmaWNhdGlvbnMuY2FuY2VsU2NoZWR1bGVkTm90aWZpY2F0aW9uQXN5bmMobm90aWZpY2F0aW9uSWQpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjYW5jZWxpbmcgbm90aWZpY2F0aW9uOicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ2FuY2VsIG5vdGlmaWNhdGlvbnMgYnkgdGFnL3R5cGVcbiAgICovXG4gIGFzeW5jIGNhbmNlbE5vdGlmaWNhdGlvbnNCeVRhZyh0YWc6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBzY2hlZHVsZWROb3RpZmljYXRpb25zID0gYXdhaXQgTm90aWZpY2F0aW9ucy5nZXRBbGxTY2hlZHVsZWROb3RpZmljYXRpb25zQXN5bmMoKTtcbiAgICAgIFxuICAgICAgY29uc3Qgbm90aWZpY2F0aW9uc1RvQ2FuY2VsID0gc2NoZWR1bGVkTm90aWZpY2F0aW9ucy5maWx0ZXIoXG4gICAgICAgIG5vdGlmaWNhdGlvbiA9PiBub3RpZmljYXRpb24uY29udGVudC5kYXRhPy50eXBlID09PSB0YWdcbiAgICAgICk7XG5cbiAgICAgIGZvciAoY29uc3Qgbm90aWZpY2F0aW9uIG9mIG5vdGlmaWNhdGlvbnNUb0NhbmNlbCkge1xuICAgICAgICBhd2FpdCBOb3RpZmljYXRpb25zLmNhbmNlbFNjaGVkdWxlZE5vdGlmaWNhdGlvbkFzeW5jKG5vdGlmaWNhdGlvbi5pZGVudGlmaWVyKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2FuY2VsaW5nIG5vdGlmaWNhdGlvbnMgYnkgdGFnOicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IGFsbCBzY2hlZHVsZWQgbm90aWZpY2F0aW9uc1xuICAgKi9cbiAgYXN5bmMgZ2V0U2NoZWR1bGVkTm90aWZpY2F0aW9ucygpOiBQcm9taXNlPE5vdGlmaWNhdGlvbnMuTm90aWZpY2F0aW9uUmVxdWVzdFtdPiB7XG4gICAgdHJ5IHtcbiAgICAgIHJldHVybiBhd2FpdCBOb3RpZmljYXRpb25zLmdldEFsbFNjaGVkdWxlZE5vdGlmaWNhdGlvbnNBc3luYygpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHNjaGVkdWxlZCBub3RpZmljYXRpb25zOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogVXBkYXRlIG5vdGlmaWNhdGlvbiBzZXR0aW5nc1xuICAgKi9cbiAgdXBkYXRlU2V0dGluZ3MobmV3U2V0dGluZ3M6IFBhcnRpYWw8Tm90aWZpY2F0aW9uU2V0dGluZ3M+KTogdm9pZCB7XG4gICAgdGhpcy5ub3RpZmljYXRpb25TZXR0aW5ncyA9IHtcbiAgICAgIC4uLnRoaXMubm90aWZpY2F0aW9uU2V0dGluZ3MsXG4gICAgICAuLi5uZXdTZXR0aW5ncyxcbiAgICB9O1xuXG4gICAgLy8gUmUtc2NoZWR1bGUgbm90aWZpY2F0aW9ucyBiYXNlZCBvbiBuZXcgc2V0dGluZ3NcbiAgICBpZiAobmV3U2V0dGluZ3MuZGFpbHlUaXBzICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIGlmIChuZXdTZXR0aW5ncy5kYWlseVRpcHMpIHtcbiAgICAgICAgdGhpcy5zY2hlZHVsZURhaWx5VGlwcygpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhpcy5jYW5jZWxOb3RpZmljYXRpb25zQnlUYWcoJ2RhaWx5X3RpcCcpO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgY3VycmVudCBub3RpZmljYXRpb24gc2V0dGluZ3NcbiAgICovXG4gIGdldFNldHRpbmdzKCk6IE5vdGlmaWNhdGlvblNldHRpbmdzIHtcbiAgICByZXR1cm4geyAuLi50aGlzLm5vdGlmaWNhdGlvblNldHRpbmdzIH07XG4gIH1cblxuICAvKipcbiAgICogR2V0IHB1c2ggdG9rZW4gZm9yIHJlbW90ZSBub3RpZmljYXRpb25zXG4gICAqL1xuICBnZXRQdXNoVG9rZW4oKTogc3RyaW5nIHwgbnVsbCB7XG4gICAgcmV0dXJuIHRoaXMuZXhwb1B1c2hUb2tlbjtcbiAgfVxuXG4gIC8vIFByaXZhdGUgaGVscGVyIG1ldGhvZHNcblxuICBwcml2YXRlIGFzeW5jIGdldEV4cG9QdXNoVG9rZW4oKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdG9rZW4gPSAoYXdhaXQgTm90aWZpY2F0aW9ucy5nZXRFeHBvUHVzaFRva2VuQXN5bmMoKSkuZGF0YTtcbiAgICAgIGNvbnNvbGUubG9nKCdFeHBvIHB1c2ggdG9rZW46JywgdG9rZW4pO1xuICAgICAgcmV0dXJuIHRva2VuO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIEV4cG8gcHVzaCB0b2tlbjonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGFzeW5jIHNldHVwQW5kcm9pZENoYW5uZWxzKCk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBDcmVhdGUgbm90aWZpY2F0aW9uIGNoYW5uZWxzIGZvciBBbmRyb2lkXG4gICAgICBhd2FpdCBOb3RpZmljYXRpb25zLnNldE5vdGlmaWNhdGlvbkNoYW5uZWxBc3luYygnZGVmYXVsdCcsIHtcbiAgICAgICAgbmFtZTogJ0RlZmF1bHQnLFxuICAgICAgICBpbXBvcnRhbmNlOiBOb3RpZmljYXRpb25zLkFuZHJvaWRJbXBvcnRhbmNlLkRFRkFVTFQsXG4gICAgICAgIHZpYnJhdGlvblBhdHRlcm46IFswLCAyNTAsIDI1MCwgMjUwXSxcbiAgICAgICAgbGlnaHRDb2xvcjogJyMzYjgyZjYnLFxuICAgICAgfSk7XG5cbiAgICAgIGF3YWl0IE5vdGlmaWNhdGlvbnMuc2V0Tm90aWZpY2F0aW9uQ2hhbm5lbEFzeW5jKCdhY2hpZXZlbWVudHMnLCB7XG4gICAgICAgIG5hbWU6ICdBY2hpZXZlbWVudHMnLFxuICAgICAgICBpbXBvcnRhbmNlOiBOb3RpZmljYXRpb25zLkFuZHJvaWRJbXBvcnRhbmNlLkhJR0gsXG4gICAgICAgIHZpYnJhdGlvblBhdHRlcm46IFswLCAyNTAsIDI1MCwgMjUwXSxcbiAgICAgICAgbGlnaHRDb2xvcjogJyMxMGI5ODEnLFxuICAgICAgICBzb3VuZDogJ2RlZmF1bHQnLFxuICAgICAgfSk7XG5cbiAgICAgIGF3YWl0IE5vdGlmaWNhdGlvbnMuc2V0Tm90aWZpY2F0aW9uQ2hhbm5lbEFzeW5jKCdyZW1pbmRlcnMnLCB7XG4gICAgICAgIG5hbWU6ICdSZW1pbmRlcnMnLFxuICAgICAgICBpbXBvcnRhbmNlOiBOb3RpZmljYXRpb25zLkFuZHJvaWRJbXBvcnRhbmNlLkRFRkFVTFQsXG4gICAgICAgIHZpYnJhdGlvblBhdHRlcm46IFswLCAyNTAsIDI1MCwgMjUwXSxcbiAgICAgICAgbGlnaHRDb2xvcjogJyNmNTllMGInLFxuICAgICAgfSk7XG5cbiAgICAgIGF3YWl0IE5vdGlmaWNhdGlvbnMuc2V0Tm90aWZpY2F0aW9uQ2hhbm5lbEFzeW5jKCd0aXBzJywge1xuICAgICAgICBuYW1lOiAnRGFpbHkgVGlwcycsXG4gICAgICAgIGltcG9ydGFuY2U6IE5vdGlmaWNhdGlvbnMuQW5kcm9pZEltcG9ydGFuY2UuTE9XLFxuICAgICAgICB2aWJyYXRpb25QYXR0ZXJuOiBbMCwgMjUwXSxcbiAgICAgICAgbGlnaHRDb2xvcjogJyM4YjVjZjYnLFxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNldHRpbmcgdXAgQW5kcm9pZCBjaGFubmVsczonLCBlcnJvcik7XG4gICAgfVxuICB9XG59XG5cbmV4cG9ydCBjb25zdCBub3RpZmljYXRpb25TZXJ2aWNlID0gbmV3IE5vdGlmaWNhdGlvblNlcnZpY2UoKTtcbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ0EsT0FBTyxLQUFLQSxhQUFhLE1BQU0sb0JBQW9CO0FBQ25ELE9BQU8sS0FBS0MsTUFBTSxNQUFNLGFBQWE7QUFDckMsU0FBU0MsUUFBUSxRQUFRLGNBQWM7QUFBQ0MsYUFBQSxHQUFBQyxDQUFBO0FBMEJ4Q0osYUFBYSxDQUFDSyxzQkFBc0IsQ0FBQztFQUNuQ0Msa0JBQWtCO0lBQUEsSUFBQUMsbUJBQUEsR0FBQUMsaUJBQUEsQ0FBRSxhQUFhO01BQUFMLGFBQUEsR0FBQU0sQ0FBQTtNQUFBTixhQUFBLEdBQUFDLENBQUE7TUFBQTtRQUMvQk0sZUFBZSxFQUFFLElBQUk7UUFDckJDLGVBQWUsRUFBRSxJQUFJO1FBQ3JCQyxjQUFjLEVBQUU7TUFDbEIsQ0FBQztJQUFELENBQUU7SUFBQSxTQUpGTixrQkFBa0JBLENBQUE7TUFBQSxPQUFBQyxtQkFBQSxDQUFBTSxLQUFBLE9BQUFDLFNBQUE7SUFBQTtJQUFBLE9BQWxCUixrQkFBa0I7RUFBQTtBQUtwQixDQUFDLENBQUM7QUFBQyxJQUVHUyxtQkFBbUI7RUFBQSxTQUFBQSxvQkFBQTtJQUFBQyxlQUFBLE9BQUFELG1CQUFBO0lBQUEsS0FDZkUsYUFBYSxJQUFBZCxhQUFBLEdBQUFDLENBQUEsT0FBa0IsSUFBSTtJQUFBLEtBQ25DYyxvQkFBb0IsSUFBQWYsYUFBQSxHQUFBQyxDQUFBLE9BQXlCO01BQ25EZSxTQUFTLEVBQUUsSUFBSTtNQUNmQyxpQkFBaUIsRUFBRSxJQUFJO01BQ3ZCQyxjQUFjLEVBQUUsSUFBSTtNQUNwQkMsaUJBQWlCLEVBQUUsSUFBSTtNQUN2QkMsZUFBZSxFQUFFLElBQUk7TUFDckJDLG1CQUFtQixFQUFFO0lBQ3ZCLENBQUM7RUFBQTtFQUFBLE9BQUFDLFlBQUEsQ0FBQVYsbUJBQUE7SUFBQVcsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQUMsV0FBQSxHQUFBcEIsaUJBQUEsQ0FLRCxhQUFxQztRQUFBTCxhQUFBLEdBQUFNLENBQUE7UUFBQU4sYUFBQSxHQUFBQyxDQUFBO1FBQ25DLElBQUk7VUFFRixJQUFBeUIsSUFBQSxJQUFBMUIsYUFBQSxHQUFBQyxDQUFBLGFBQXlDSixhQUFhLENBQUM4QixtQkFBbUIsQ0FBQyxDQUFDO1lBQTVEQyxjQUFjLEdBQUFGLElBQUEsQ0FBdEJHLE1BQU07VUFDZCxJQUFJQyxXQUFXLElBQUE5QixhQUFBLEdBQUFDLENBQUEsT0FBRzJCLGNBQWM7VUFBQzVCLGFBQUEsR0FBQUMsQ0FBQTtVQUVqQyxJQUFJMkIsY0FBYyxLQUFLLFNBQVMsRUFBRTtZQUFBNUIsYUFBQSxHQUFBK0IsQ0FBQTtZQUNoQyxJQUFBQyxLQUFBLElBQUFoQyxhQUFBLEdBQUFDLENBQUEsYUFBeUJKLGFBQWEsQ0FBQ29DLHVCQUF1QixDQUFDLENBQUM7Y0FBeERKLE1BQU0sR0FBQUcsS0FBQSxDQUFOSCxNQUFNO1lBQW1EN0IsYUFBQSxHQUFBQyxDQUFBO1lBQ2pFNkIsV0FBVyxHQUFHRCxNQUFNO1VBQ3RCLENBQUM7WUFBQTdCLGFBQUEsR0FBQStCLENBQUE7VUFBQTtVQUFBL0IsYUFBQSxHQUFBQyxDQUFBO1VBRUQsSUFBSTZCLFdBQVcsS0FBSyxTQUFTLEVBQUU7WUFBQTlCLGFBQUEsR0FBQStCLENBQUE7WUFBQS9CLGFBQUEsR0FBQUMsQ0FBQTtZQUM3QmlDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNDQUFzQyxDQUFDO1lBQUNuQyxhQUFBLEdBQUFDLENBQUE7WUFDcEQsT0FBTyxLQUFLO1VBQ2QsQ0FBQztZQUFBRCxhQUFBLEdBQUErQixDQUFBO1VBQUE7VUFBQS9CLGFBQUEsR0FBQUMsQ0FBQTtVQUdELElBQUlILE1BQU0sQ0FBQ3NDLFFBQVEsRUFBRTtZQUFBcEMsYUFBQSxHQUFBK0IsQ0FBQTtZQUFBL0IsYUFBQSxHQUFBQyxDQUFBO1lBQ25CLElBQUksQ0FBQ2EsYUFBYSxTQUFTLElBQUksQ0FBQ3VCLGdCQUFnQixDQUFDLENBQUM7VUFDcEQsQ0FBQztZQUFBckMsYUFBQSxHQUFBK0IsQ0FBQTtVQUFBO1VBQUEvQixhQUFBLEdBQUFDLENBQUE7VUFHRCxJQUFJRixRQUFRLENBQUN1QyxFQUFFLEtBQUssU0FBUyxFQUFFO1lBQUF0QyxhQUFBLEdBQUErQixDQUFBO1lBQUEvQixhQUFBLEdBQUFDLENBQUE7WUFDN0IsTUFBTSxJQUFJLENBQUNzQyxvQkFBb0IsQ0FBQyxDQUFDO1VBQ25DLENBQUM7WUFBQXZDLGFBQUEsR0FBQStCLENBQUE7VUFBQTtVQUFBL0IsYUFBQSxHQUFBQyxDQUFBO1VBRURpQyxPQUFPLENBQUNDLEdBQUcsQ0FBQywrQ0FBK0MsQ0FBQztVQUFDbkMsYUFBQSxHQUFBQyxDQUFBO1VBQzdELE9BQU8sSUFBSTtRQUNiLENBQUMsQ0FBQyxPQUFPdUMsS0FBSyxFQUFFO1VBQUF4QyxhQUFBLEdBQUFDLENBQUE7VUFDZGlDLE9BQU8sQ0FBQ00sS0FBSyxDQUFDLG1DQUFtQyxFQUFFQSxLQUFLLENBQUM7VUFBQ3hDLGFBQUEsR0FBQUMsQ0FBQTtVQUMxRCxPQUFPLEtBQUs7UUFDZDtNQUNGLENBQUM7TUFBQSxTQWhDS3dDLFVBQVVBLENBQUE7UUFBQSxPQUFBaEIsV0FBQSxDQUFBZixLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQVY4QixVQUFVO0lBQUE7RUFBQTtJQUFBbEIsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQWtCLHNCQUFBLEdBQUFyQyxpQkFBQSxDQXFDaEIsV0FBNEJzQyxPQUE0QixFQUFtQjtRQUFBM0MsYUFBQSxHQUFBTSxDQUFBO1FBQUFOLGFBQUEsR0FBQUMsQ0FBQTtRQUN6RSxJQUFJO1VBQ0YsSUFBTTJDLGNBQWMsSUFBQTVDLGFBQUEsR0FBQUMsQ0FBQSxjQUFTSixhQUFhLENBQUNnRCx5QkFBeUIsQ0FBQztZQUNuRUYsT0FBTyxFQUFFO2NBQ1BHLEtBQUssRUFBRUgsT0FBTyxDQUFDRyxLQUFLO2NBQ3BCQyxJQUFJLEVBQUVKLE9BQU8sQ0FBQ0ksSUFBSTtjQUNsQkMsSUFBSSxFQUFFLENBQUFoRCxhQUFBLEdBQUErQixDQUFBLFVBQUFZLE9BQU8sQ0FBQ0ssSUFBSSxNQUFBaEQsYUFBQSxHQUFBK0IsQ0FBQSxVQUFJLENBQUMsQ0FBQztjQUN4QmtCLEtBQUssRUFBRU4sT0FBTyxDQUFDTSxLQUFLLEtBQUssS0FBSztjQUM5QkMsS0FBSyxFQUFFUCxPQUFPLENBQUNPO1lBQ2pCLENBQUM7WUFDREMsT0FBTyxFQUFFO1VBQ1gsQ0FBQyxDQUFDO1VBQUNuRCxhQUFBLEdBQUFDLENBQUE7VUFFSCxPQUFPMkMsY0FBYztRQUN2QixDQUFDLENBQUMsT0FBT0osS0FBSyxFQUFFO1VBQUF4QyxhQUFBLEdBQUFDLENBQUE7VUFDZGlDLE9BQU8sQ0FBQ00sS0FBSyxDQUFDLG1DQUFtQyxFQUFFQSxLQUFLLENBQUM7VUFBQ3hDLGFBQUEsR0FBQUMsQ0FBQTtVQUMxRCxNQUFNdUMsS0FBSztRQUNiO01BQ0YsQ0FBQztNQUFBLFNBbEJLWSxxQkFBcUJBLENBQUFDLEVBQUE7UUFBQSxPQUFBWCxzQkFBQSxDQUFBaEMsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFyQnlDLHFCQUFxQjtJQUFBO0VBQUE7SUFBQTdCLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUE4QixxQkFBQSxHQUFBakQsaUJBQUEsQ0F1QjNCLFdBQ0VzQyxPQUE0QixFQUM1QlEsT0FBK0MsRUFDOUI7UUFBQW5ELGFBQUEsR0FBQU0sQ0FBQTtRQUFBTixhQUFBLEdBQUFDLENBQUE7UUFDakIsSUFBSTtVQUNGLElBQU0yQyxjQUFjLElBQUE1QyxhQUFBLEdBQUFDLENBQUEsY0FBU0osYUFBYSxDQUFDZ0QseUJBQXlCLENBQUM7WUFDbkVGLE9BQU8sRUFBRTtjQUNQRyxLQUFLLEVBQUVILE9BQU8sQ0FBQ0csS0FBSztjQUNwQkMsSUFBSSxFQUFFSixPQUFPLENBQUNJLElBQUk7Y0FDbEJDLElBQUksRUFBRSxDQUFBaEQsYUFBQSxHQUFBK0IsQ0FBQSxVQUFBWSxPQUFPLENBQUNLLElBQUksTUFBQWhELGFBQUEsR0FBQStCLENBQUEsVUFBSSxDQUFDLENBQUM7Y0FDeEJrQixLQUFLLEVBQUVOLE9BQU8sQ0FBQ00sS0FBSyxLQUFLLEtBQUs7Y0FDOUJDLEtBQUssRUFBRVAsT0FBTyxDQUFDTztZQUNqQixDQUFDO1lBQ0RDLE9BQU8sRUFBUEE7VUFDRixDQUFDLENBQUM7VUFBQ25ELGFBQUEsR0FBQUMsQ0FBQTtVQUVILE9BQU8yQyxjQUFjO1FBQ3ZCLENBQUMsQ0FBQyxPQUFPSixLQUFLLEVBQUU7VUFBQXhDLGFBQUEsR0FBQUMsQ0FBQTtVQUNkaUMsT0FBTyxDQUFDTSxLQUFLLENBQUMsZ0NBQWdDLEVBQUVBLEtBQUssQ0FBQztVQUFDeEMsYUFBQSxHQUFBQyxDQUFBO1VBQ3ZELE1BQU11QyxLQUFLO1FBQ2I7TUFDRixDQUFDO01BQUEsU0FyQktlLG9CQUFvQkEsQ0FBQUMsR0FBQSxFQUFBQyxHQUFBO1FBQUEsT0FBQUgscUJBQUEsQ0FBQTVDLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBcEI0QyxvQkFBb0I7SUFBQTtFQUFBO0lBQUFoQyxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBa0Msa0JBQUEsR0FBQXJELGlCQUFBLENBMEIxQixhQUF5QztRQUFBTCxhQUFBLEdBQUFNLENBQUE7UUFBQU4sYUFBQSxHQUFBQyxDQUFBO1FBQ3ZDLElBQUksQ0FBQyxJQUFJLENBQUNjLG9CQUFvQixDQUFDQyxTQUFTLEVBQUU7VUFBQWhCLGFBQUEsR0FBQStCLENBQUE7VUFBQS9CLGFBQUEsR0FBQUMsQ0FBQTtVQUFBO1FBQU0sQ0FBQztVQUFBRCxhQUFBLEdBQUErQixDQUFBO1FBQUE7UUFBQS9CLGFBQUEsR0FBQUMsQ0FBQTtRQUVqRCxJQUFJO1VBQUFELGFBQUEsR0FBQUMsQ0FBQTtVQUVGLE1BQU0sSUFBSSxDQUFDMEQsd0JBQXdCLENBQUMsV0FBVyxDQUFDO1VBRWhELElBQU1DLElBQUksSUFBQTVELGFBQUEsR0FBQUMsQ0FBQSxRQUFHLENBQ1gsNENBQTRDLEVBQzVDLG9FQUFvRSxFQUNwRSxzREFBc0QsRUFDdEQsNENBQTRDLEVBQzVDLDRDQUE0QyxFQUM1QyxtREFBbUQsRUFDbkQsd0NBQXdDLENBQ3pDO1VBQUNELGFBQUEsR0FBQUMsQ0FBQTtVQUdGLEtBQUssSUFBSTRELENBQUMsSUFBQTdELGFBQUEsR0FBQUMsQ0FBQSxRQUFHLENBQUMsR0FBRTRELENBQUMsR0FBRyxDQUFDLEVBQUVBLENBQUMsRUFBRSxFQUFFO1lBQzFCLElBQU1DLEdBQUcsSUFBQTlELGFBQUEsR0FBQUMsQ0FBQSxRQUFHMkQsSUFBSSxDQUFDQyxDQUFDLEdBQUdELElBQUksQ0FBQ0csTUFBTSxDQUFDO1lBQ2pDLElBQU1aLE9BQXdDLElBQUFuRCxhQUFBLEdBQUFDLENBQUEsUUFBRztjQUMvQytELElBQUksRUFBRSxDQUFDO2NBQ1BDLE1BQU0sRUFBRSxDQUFDO2NBQ1RDLE9BQU8sRUFBRTtZQUNYLENBQUM7WUFBQ2xFLGFBQUEsR0FBQUMsQ0FBQTtZQUVGLE1BQU0sSUFBSSxDQUFDc0Qsb0JBQW9CLENBQzdCO2NBQ0VULEtBQUssRUFBRSxxQkFBcUI7Y0FDNUJDLElBQUksRUFBRWUsR0FBRztjQUNUZCxJQUFJLEVBQUU7Z0JBQUVtQixJQUFJLEVBQUUsV0FBVztnQkFBRUMsR0FBRyxFQUFFUDtjQUFFO1lBQ3BDLENBQUMsRUFDRFYsT0FDRixDQUFDO1VBQ0g7UUFDRixDQUFDLENBQUMsT0FBT1gsS0FBSyxFQUFFO1VBQUF4QyxhQUFBLEdBQUFDLENBQUE7VUFDZGlDLE9BQU8sQ0FBQ00sS0FBSyxDQUFDLDhCQUE4QixFQUFFQSxLQUFLLENBQUM7UUFDdEQ7TUFDRixDQUFDO01BQUEsU0F0Q0s2QixpQkFBaUJBLENBQUE7UUFBQSxPQUFBWCxrQkFBQSxDQUFBaEQsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFqQjBELGlCQUFpQjtJQUFBO0VBQUE7SUFBQTlDLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUE4Qyx5QkFBQSxHQUFBakUsaUJBQUEsQ0EyQ3ZCLFdBQ0V5QyxLQUFhLEVBQ2J5QixPQUFlLEVBQ2ZDLGFBQW1CLEVBQ0Y7UUFBQXhFLGFBQUEsR0FBQU0sQ0FBQTtRQUFBTixhQUFBLEdBQUFDLENBQUE7UUFDakIsSUFBSSxDQUFDLElBQUksQ0FBQ2Msb0JBQW9CLENBQUNFLGlCQUFpQixFQUFFO1VBQUFqQixhQUFBLEdBQUErQixDQUFBO1VBQUEvQixhQUFBLEdBQUFDLENBQUE7VUFDaEQsTUFBTSxJQUFJd0UsS0FBSyxDQUFDLGlDQUFpQyxDQUFDO1FBQ3BELENBQUM7VUFBQXpFLGFBQUEsR0FBQStCLENBQUE7UUFBQTtRQUFBL0IsYUFBQSxHQUFBQyxDQUFBO1FBRUQsSUFBSTtVQUNGLElBQU1rRCxPQUF1QyxJQUFBbkQsYUFBQSxHQUFBQyxDQUFBLFFBQUc7WUFDOUN5RSxJQUFJLEVBQUVGO1VBQ1IsQ0FBQztVQUFDeEUsYUFBQSxHQUFBQyxDQUFBO1VBRUYsYUFBYSxJQUFJLENBQUNzRCxvQkFBb0IsQ0FDcEM7WUFDRVQsS0FBSyxFQUFFLFNBQVNBLEtBQUssRUFBRTtZQUN2QkMsSUFBSSxFQUFFd0IsT0FBTztZQUNidkIsSUFBSSxFQUFFO2NBQUVtQixJQUFJLEVBQUU7WUFBb0I7VUFDcEMsQ0FBQyxFQUNEaEIsT0FDRixDQUFDO1FBQ0gsQ0FBQyxDQUFDLE9BQU9YLEtBQUssRUFBRTtVQUFBeEMsYUFBQSxHQUFBQyxDQUFBO1VBQ2RpQyxPQUFPLENBQUNNLEtBQUssQ0FBQyxxQ0FBcUMsRUFBRUEsS0FBSyxDQUFDO1VBQUN4QyxhQUFBLEdBQUFDLENBQUE7VUFDNUQsTUFBTXVDLEtBQUs7UUFDYjtNQUNGLENBQUM7TUFBQSxTQTFCS21DLHdCQUF3QkEsQ0FBQUMsR0FBQSxFQUFBQyxHQUFBLEVBQUFDLEdBQUE7UUFBQSxPQUFBUix5QkFBQSxDQUFBNUQsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUF4QmdFLHdCQUF3QjtJQUFBO0VBQUE7SUFBQXBELEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUF1RCw0QkFBQSxHQUFBMUUsaUJBQUEsQ0ErQjlCLFdBQ0UyRSxnQkFBd0IsRUFDeEJDLFdBQW1CLEVBQ0o7UUFBQWpGLGFBQUEsR0FBQU0sQ0FBQTtRQUFBTixhQUFBLEdBQUFDLENBQUE7UUFDZixJQUFJLENBQUMsSUFBSSxDQUFDYyxvQkFBb0IsQ0FBQ0ksaUJBQWlCLEVBQUU7VUFBQW5CLGFBQUEsR0FBQStCLENBQUE7VUFBQS9CLGFBQUEsR0FBQUMsQ0FBQTtVQUFBO1FBQU0sQ0FBQztVQUFBRCxhQUFBLEdBQUErQixDQUFBO1FBQUE7UUFBQS9CLGFBQUEsR0FBQUMsQ0FBQTtRQUV6RCxJQUFJO1VBQUFELGFBQUEsR0FBQUMsQ0FBQTtVQUNGLE1BQU0sSUFBSSxDQUFDbUQscUJBQXFCLENBQUM7WUFDL0JOLEtBQUssRUFBRSwwQkFBMEI7WUFDakNDLElBQUksRUFBRSxHQUFHaUMsZ0JBQWdCLEtBQUtDLFdBQVcsRUFBRTtZQUMzQ2pDLElBQUksRUFBRTtjQUFFbUIsSUFBSSxFQUFFO1lBQWMsQ0FBQztZQUM3QmxCLEtBQUssRUFBRTtVQUNULENBQUMsQ0FBQztRQUNKLENBQUMsQ0FBQyxPQUFPVCxLQUFLLEVBQUU7VUFBQXhDLGFBQUEsR0FBQUMsQ0FBQTtVQUNkaUMsT0FBTyxDQUFDTSxLQUFLLENBQUMseUNBQXlDLEVBQUVBLEtBQUssQ0FBQztRQUNqRTtNQUNGLENBQUM7TUFBQSxTQWhCSzBDLDJCQUEyQkEsQ0FBQUMsR0FBQSxFQUFBQyxHQUFBO1FBQUEsT0FBQUwsNEJBQUEsQ0FBQXJFLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBM0J1RSwyQkFBMkI7SUFBQTtFQUFBO0lBQUEzRCxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBNkQsbUJBQUEsR0FBQWhGLGlCQUFBLENBcUJqQyxXQUNFaUYsU0FBaUIsRUFDakJDLFNBQWlCLEVBQ2pCQyxTQUFpQixFQUNGO1FBQUF4RixhQUFBLEdBQUFNLENBQUE7UUFBQU4sYUFBQSxHQUFBQyxDQUFBO1FBQ2YsSUFBSSxDQUFDLElBQUksQ0FBQ2Msb0JBQW9CLENBQUNLLGVBQWUsRUFBRTtVQUFBcEIsYUFBQSxHQUFBK0IsQ0FBQTtVQUFBL0IsYUFBQSxHQUFBQyxDQUFBO1VBQUE7UUFBTSxDQUFDO1VBQUFELGFBQUEsR0FBQStCLENBQUE7UUFBQTtRQUFBL0IsYUFBQSxHQUFBQyxDQUFBO1FBRXZELElBQUk7VUFDRixJQUFNd0YsV0FBVyxJQUFBekYsYUFBQSxHQUFBQyxDQUFBLFFBQUd1RixTQUFTLEdBQUdELFNBQVM7VUFDekMsSUFBTWhCLE9BQU8sSUFBQXZFLGFBQUEsR0FBQUMsQ0FBQSxRQUFHd0YsV0FBVyxHQUFHLENBQUMsSUFBQXpGLGFBQUEsR0FBQStCLENBQUEsV0FDM0IsUUFBUXVELFNBQVMsZ0JBQWdCRyxXQUFXLG1CQUFtQkQsU0FBUyxNQUFNLEtBQUF4RixhQUFBLEdBQUErQixDQUFBLFdBQzlFLFFBQVF1RCxTQUFTLHNCQUFzQkUsU0FBUyxNQUFNO1VBQUN4RixhQUFBLEdBQUFDLENBQUE7VUFFM0QsTUFBTSxJQUFJLENBQUNtRCxxQkFBcUIsQ0FBQztZQUMvQk4sS0FBSyxFQUFFLG9CQUFvQjtZQUMzQkMsSUFBSSxFQUFFd0IsT0FBTztZQUNidkIsSUFBSSxFQUFFO2NBQUVtQixJQUFJLEVBQUUsaUJBQWlCO2NBQUV1QixLQUFLLEVBQUVKO1lBQVU7VUFDcEQsQ0FBQyxDQUFDO1FBQ0osQ0FBQyxDQUFDLE9BQU85QyxLQUFLLEVBQUU7VUFBQXhDLGFBQUEsR0FBQUMsQ0FBQTtVQUNkaUMsT0FBTyxDQUFDTSxLQUFLLENBQUMsZ0NBQWdDLEVBQUVBLEtBQUssQ0FBQztRQUN4RDtNQUNGLENBQUM7TUFBQSxTQXJCS21ELGtCQUFrQkEsQ0FBQUMsR0FBQSxFQUFBQyxHQUFBLEVBQUFDLEdBQUE7UUFBQSxPQUFBVCxtQkFBQSxDQUFBM0UsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFsQmdGLGtCQUFrQjtJQUFBO0VBQUE7SUFBQXBFLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUF1RSxzQkFBQSxHQUFBMUYsaUJBQUEsQ0EwQnhCLFdBQ0UyRixZQUFvQixFQUNwQkMsU0FBZSxFQUNmQyxRQUFpQixFQUNBO1FBQUFsRyxhQUFBLEdBQUFNLENBQUE7UUFBQU4sYUFBQSxHQUFBQyxDQUFBO1FBQ2pCLElBQUksQ0FBQyxJQUFJLENBQUNjLG9CQUFvQixDQUFDRyxjQUFjLEVBQUU7VUFBQWxCLGFBQUEsR0FBQStCLENBQUE7VUFBQS9CLGFBQUEsR0FBQUMsQ0FBQTtVQUM3QyxNQUFNLElBQUl3RSxLQUFLLENBQUMsOEJBQThCLENBQUM7UUFDakQsQ0FBQztVQUFBekUsYUFBQSxHQUFBK0IsQ0FBQTtRQUFBO1FBQUEvQixhQUFBLEdBQUFDLENBQUE7UUFFRCxJQUFJO1VBRUYsSUFBTWtHLFlBQVksSUFBQW5HLGFBQUEsR0FBQUMsQ0FBQSxRQUFHLElBQUltRyxJQUFJLENBQUNILFNBQVMsQ0FBQ0ksT0FBTyxDQUFDLENBQUMsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLElBQUksQ0FBQztVQUVuRSxJQUFNOUIsT0FBTyxJQUFBdkUsYUFBQSxHQUFBQyxDQUFBLFFBQUdpRyxRQUFRLElBQUFsRyxhQUFBLEdBQUErQixDQUFBLFdBQ3BCLGlCQUFpQmlFLFlBQVksT0FBT0UsUUFBUSxZQUFZLEtBQUFsRyxhQUFBLEdBQUErQixDQUFBLFdBQ3hELGlCQUFpQmlFLFlBQVksWUFBWTtVQUU3QyxJQUFNN0MsT0FBdUMsSUFBQW5ELGFBQUEsR0FBQUMsQ0FBQSxRQUFHO1lBQzlDeUUsSUFBSSxFQUFFeUI7VUFDUixDQUFDO1VBQUNuRyxhQUFBLEdBQUFDLENBQUE7VUFFRixhQUFhLElBQUksQ0FBQ3NELG9CQUFvQixDQUNwQztZQUNFVCxLQUFLLEVBQUUsbUJBQW1CO1lBQzFCQyxJQUFJLEVBQUV3QixPQUFPO1lBQ2J2QixJQUFJLEVBQUU7Y0FBRW1CLElBQUksRUFBRSxnQkFBZ0I7Y0FBRW1DLFFBQVEsRUFBRU47WUFBYTtVQUN6RCxDQUFDLEVBQ0Q3QyxPQUNGLENBQUM7UUFDSCxDQUFDLENBQUMsT0FBT1gsS0FBSyxFQUFFO1VBQUF4QyxhQUFBLEdBQUFDLENBQUE7VUFDZGlDLE9BQU8sQ0FBQ00sS0FBSyxDQUFDLGtDQUFrQyxFQUFFQSxLQUFLLENBQUM7VUFBQ3hDLGFBQUEsR0FBQUMsQ0FBQTtVQUN6RCxNQUFNdUMsS0FBSztRQUNiO01BQ0YsQ0FBQztNQUFBLFNBakNLK0QscUJBQXFCQSxDQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQTtRQUFBLE9BQUFYLHNCQUFBLENBQUFyRixLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQXJCNEYscUJBQXFCO0lBQUE7RUFBQTtJQUFBaEYsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQW1GLG1CQUFBLEdBQUF0RyxpQkFBQSxDQXNDM0IsV0FBeUJ1QyxjQUFzQixFQUFpQjtRQUFBNUMsYUFBQSxHQUFBTSxDQUFBO1FBQUFOLGFBQUEsR0FBQUMsQ0FBQTtRQUM5RCxJQUFJO1VBQUFELGFBQUEsR0FBQUMsQ0FBQTtVQUNGLE1BQU1KLGFBQWEsQ0FBQytHLGdDQUFnQyxDQUFDaEUsY0FBYyxDQUFDO1FBQ3RFLENBQUMsQ0FBQyxPQUFPSixLQUFLLEVBQUU7VUFBQXhDLGFBQUEsR0FBQUMsQ0FBQTtVQUNkaUMsT0FBTyxDQUFDTSxLQUFLLENBQUMsK0JBQStCLEVBQUVBLEtBQUssQ0FBQztRQUN2RDtNQUNGLENBQUM7TUFBQSxTQU5LcUUsa0JBQWtCQSxDQUFBQyxJQUFBO1FBQUEsT0FBQUgsbUJBQUEsQ0FBQWpHLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBbEJrRyxrQkFBa0I7SUFBQTtFQUFBO0lBQUF0RixHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBdUYseUJBQUEsR0FBQTFHLGlCQUFBLENBV3hCLFdBQStCMkcsR0FBVyxFQUFpQjtRQUFBaEgsYUFBQSxHQUFBTSxDQUFBO1FBQUFOLGFBQUEsR0FBQUMsQ0FBQTtRQUN6RCxJQUFJO1VBQ0YsSUFBTWdILHNCQUFzQixJQUFBakgsYUFBQSxHQUFBQyxDQUFBLGNBQVNKLGFBQWEsQ0FBQ3FILGlDQUFpQyxDQUFDLENBQUM7VUFFdEYsSUFBTUMscUJBQXFCLElBQUFuSCxhQUFBLEdBQUFDLENBQUEsUUFBR2dILHNCQUFzQixDQUFDRyxNQUFNLENBQ3pELFVBQUFDLFlBQVksRUFBSTtZQUFBLElBQUFDLHFCQUFBO1lBQUF0SCxhQUFBLEdBQUFNLENBQUE7WUFBQU4sYUFBQSxHQUFBQyxDQUFBO1lBQUEsU0FBQXFILHFCQUFBLEdBQUFELFlBQVksQ0FBQzFFLE9BQU8sQ0FBQ0ssSUFBSSxxQkFBekJzRSxxQkFBQSxDQUEyQm5ELElBQUksTUFBSzZDLEdBQUc7VUFBRCxDQUN4RCxDQUFDO1VBQUNoSCxhQUFBLEdBQUFDLENBQUE7VUFFRixLQUFLLElBQU1vSCxZQUFZLElBQUlGLHFCQUFxQixFQUFFO1lBQUFuSCxhQUFBLEdBQUFDLENBQUE7WUFDaEQsTUFBTUosYUFBYSxDQUFDK0csZ0NBQWdDLENBQUNTLFlBQVksQ0FBQ0UsVUFBVSxDQUFDO1VBQy9FO1FBQ0YsQ0FBQyxDQUFDLE9BQU8vRSxLQUFLLEVBQUU7VUFBQXhDLGFBQUEsR0FBQUMsQ0FBQTtVQUNkaUMsT0FBTyxDQUFDTSxLQUFLLENBQUMsdUNBQXVDLEVBQUVBLEtBQUssQ0FBQztRQUMvRDtNQUNGLENBQUM7TUFBQSxTQWRLbUIsd0JBQXdCQSxDQUFBNkQsSUFBQTtRQUFBLE9BQUFULHlCQUFBLENBQUFyRyxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQXhCZ0Qsd0JBQXdCO0lBQUE7RUFBQTtJQUFBcEMsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQWlHLDBCQUFBLEdBQUFwSCxpQkFBQSxDQW1COUIsYUFBZ0Y7UUFBQUwsYUFBQSxHQUFBTSxDQUFBO1FBQUFOLGFBQUEsR0FBQUMsQ0FBQTtRQUM5RSxJQUFJO1VBQUFELGFBQUEsR0FBQUMsQ0FBQTtVQUNGLGFBQWFKLGFBQWEsQ0FBQ3FILGlDQUFpQyxDQUFDLENBQUM7UUFDaEUsQ0FBQyxDQUFDLE9BQU8xRSxLQUFLLEVBQUU7VUFBQXhDLGFBQUEsR0FBQUMsQ0FBQTtVQUNkaUMsT0FBTyxDQUFDTSxLQUFLLENBQUMsd0NBQXdDLEVBQUVBLEtBQUssQ0FBQztVQUFDeEMsYUFBQSxHQUFBQyxDQUFBO1VBQy9ELE9BQU8sRUFBRTtRQUNYO01BQ0YsQ0FBQztNQUFBLFNBUEt5SCx5QkFBeUJBLENBQUE7UUFBQSxPQUFBRCwwQkFBQSxDQUFBL0csS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUF6QitHLHlCQUF5QjtJQUFBO0VBQUE7SUFBQW5HLEdBQUE7SUFBQUMsS0FBQSxFQVkvQixTQUFBbUcsY0FBY0EsQ0FBQ0MsV0FBMEMsRUFBUTtNQUFBNUgsYUFBQSxHQUFBTSxDQUFBO01BQUFOLGFBQUEsR0FBQUMsQ0FBQTtNQUMvRCxJQUFJLENBQUNjLG9CQUFvQixHQUFBOEcsTUFBQSxDQUFBQyxNQUFBLEtBQ3BCLElBQUksQ0FBQy9HLG9CQUFvQixFQUN6QjZHLFdBQVcsQ0FDZjtNQUFDNUgsYUFBQSxHQUFBQyxDQUFBO01BR0YsSUFBSTJILFdBQVcsQ0FBQzVHLFNBQVMsS0FBSytHLFNBQVMsRUFBRTtRQUFBL0gsYUFBQSxHQUFBK0IsQ0FBQTtRQUFBL0IsYUFBQSxHQUFBQyxDQUFBO1FBQ3ZDLElBQUkySCxXQUFXLENBQUM1RyxTQUFTLEVBQUU7VUFBQWhCLGFBQUEsR0FBQStCLENBQUE7VUFBQS9CLGFBQUEsR0FBQUMsQ0FBQTtVQUN6QixJQUFJLENBQUNvRSxpQkFBaUIsQ0FBQyxDQUFDO1FBQzFCLENBQUMsTUFBTTtVQUFBckUsYUFBQSxHQUFBK0IsQ0FBQTtVQUFBL0IsYUFBQSxHQUFBQyxDQUFBO1VBQ0wsSUFBSSxDQUFDMEQsd0JBQXdCLENBQUMsV0FBVyxDQUFDO1FBQzVDO01BQ0YsQ0FBQztRQUFBM0QsYUFBQSxHQUFBK0IsQ0FBQTtNQUFBO0lBQ0g7RUFBQztJQUFBUixHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFBd0csV0FBV0EsQ0FBQSxFQUF5QjtNQUFBaEksYUFBQSxHQUFBTSxDQUFBO01BQUFOLGFBQUEsR0FBQUMsQ0FBQTtNQUNsQyxPQUFBNEgsTUFBQSxDQUFBQyxNQUFBLEtBQVksSUFBSSxDQUFDL0csb0JBQW9CO0lBQ3ZDO0VBQUM7SUFBQVEsR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQXlHLFlBQVlBLENBQUEsRUFBa0I7TUFBQWpJLGFBQUEsR0FBQU0sQ0FBQTtNQUFBTixhQUFBLEdBQUFDLENBQUE7TUFDNUIsT0FBTyxJQUFJLENBQUNhLGFBQWE7SUFDM0I7RUFBQztJQUFBUyxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBMEcsaUJBQUEsR0FBQTdILGlCQUFBLENBSUQsYUFBa0Q7UUFBQUwsYUFBQSxHQUFBTSxDQUFBO1FBQUFOLGFBQUEsR0FBQUMsQ0FBQTtRQUNoRCxJQUFJO1VBQ0YsSUFBTWtJLEtBQUssSUFBQW5JLGFBQUEsR0FBQUMsQ0FBQSxRQUFHLE9BQU9KLGFBQWEsQ0FBQ3VJLHFCQUFxQixDQUFDLENBQUMsRUFBRXBGLElBQUk7VUFBQ2hELGFBQUEsR0FBQUMsQ0FBQTtVQUNqRWlDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGtCQUFrQixFQUFFZ0csS0FBSyxDQUFDO1VBQUNuSSxhQUFBLEdBQUFDLENBQUE7VUFDdkMsT0FBT2tJLEtBQUs7UUFDZCxDQUFDLENBQUMsT0FBTzNGLEtBQUssRUFBRTtVQUFBeEMsYUFBQSxHQUFBQyxDQUFBO1VBQ2RpQyxPQUFPLENBQUNNLEtBQUssQ0FBQyxnQ0FBZ0MsRUFBRUEsS0FBSyxDQUFDO1VBQUN4QyxhQUFBLEdBQUFDLENBQUE7VUFDdkQsTUFBTXVDLEtBQUs7UUFDYjtNQUNGLENBQUM7TUFBQSxTQVRhSCxnQkFBZ0JBLENBQUE7UUFBQSxPQUFBNkYsaUJBQUEsQ0FBQXhILEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBaEIwQixnQkFBZ0I7SUFBQTtFQUFBO0lBQUFkLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUE2RyxxQkFBQSxHQUFBaEksaUJBQUEsQ0FXOUIsYUFBb0Q7UUFBQUwsYUFBQSxHQUFBTSxDQUFBO1FBQUFOLGFBQUEsR0FBQUMsQ0FBQTtRQUNsRCxJQUFJO1VBQUFELGFBQUEsR0FBQUMsQ0FBQTtVQUVGLE1BQU1KLGFBQWEsQ0FBQ3lJLDJCQUEyQixDQUFDLFNBQVMsRUFBRTtZQUN6REMsSUFBSSxFQUFFLFNBQVM7WUFDZkMsVUFBVSxFQUFFM0ksYUFBYSxDQUFDNEksaUJBQWlCLENBQUNDLE9BQU87WUFDbkRDLGdCQUFnQixFQUFFLENBQUMsQ0FBQyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxDQUFDO1lBQ3BDQyxVQUFVLEVBQUU7VUFDZCxDQUFDLENBQUM7VUFBQzVJLGFBQUEsR0FBQUMsQ0FBQTtVQUVILE1BQU1KLGFBQWEsQ0FBQ3lJLDJCQUEyQixDQUFDLGNBQWMsRUFBRTtZQUM5REMsSUFBSSxFQUFFLGNBQWM7WUFDcEJDLFVBQVUsRUFBRTNJLGFBQWEsQ0FBQzRJLGlCQUFpQixDQUFDSSxJQUFJO1lBQ2hERixnQkFBZ0IsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsQ0FBQztZQUNwQ0MsVUFBVSxFQUFFLFNBQVM7WUFDckIzRixLQUFLLEVBQUU7VUFDVCxDQUFDLENBQUM7VUFBQ2pELGFBQUEsR0FBQUMsQ0FBQTtVQUVILE1BQU1KLGFBQWEsQ0FBQ3lJLDJCQUEyQixDQUFDLFdBQVcsRUFBRTtZQUMzREMsSUFBSSxFQUFFLFdBQVc7WUFDakJDLFVBQVUsRUFBRTNJLGFBQWEsQ0FBQzRJLGlCQUFpQixDQUFDQyxPQUFPO1lBQ25EQyxnQkFBZ0IsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsQ0FBQztZQUNwQ0MsVUFBVSxFQUFFO1VBQ2QsQ0FBQyxDQUFDO1VBQUM1SSxhQUFBLEdBQUFDLENBQUE7VUFFSCxNQUFNSixhQUFhLENBQUN5SSwyQkFBMkIsQ0FBQyxNQUFNLEVBQUU7WUFDdERDLElBQUksRUFBRSxZQUFZO1lBQ2xCQyxVQUFVLEVBQUUzSSxhQUFhLENBQUM0SSxpQkFBaUIsQ0FBQ0ssR0FBRztZQUMvQ0gsZ0JBQWdCLEVBQUUsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDO1lBQzFCQyxVQUFVLEVBQUU7VUFDZCxDQUFDLENBQUM7UUFDSixDQUFDLENBQUMsT0FBT3BHLEtBQUssRUFBRTtVQUFBeEMsYUFBQSxHQUFBQyxDQUFBO1VBQ2RpQyxPQUFPLENBQUNNLEtBQUssQ0FBQyxvQ0FBb0MsRUFBRUEsS0FBSyxDQUFDO1FBQzVEO01BQ0YsQ0FBQztNQUFBLFNBbENhRCxvQkFBb0JBLENBQUE7UUFBQSxPQUFBOEYscUJBQUEsQ0FBQTNILEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBcEI0QixvQkFBb0I7SUFBQTtFQUFBO0FBQUE7QUFxQ3BDLE9BQU8sSUFBTXdHLG1CQUFtQixJQUFBL0ksYUFBQSxHQUFBQyxDQUFBLFNBQUcsSUFBSVcsbUJBQW1CLENBQUMsQ0FBQyIsImlnbm9yZUxpc3QiOltdfQ==