{"version": 3, "names": ["AsyncStorage", "env", "RateLimitingService", "_this", "_classCallCheck", "storage", "cov_2kt21rq1zn", "s", "Map", "storageKey", "endpointLimits", "login", "windowMs", "maxRequests", "onLimitReached", "identifier", "resetTime", "f", "handleAuthLimitReached", "signup", "resetPassword", "aiAnalysis", "getEnvironment", "b", "aiCoaching", "uploadVideo", "saveSession", "sendMessage", "createPost", "general", "retryAfter", "Math", "ceil", "Date", "now", "console", "warn", "_createClass", "key", "value", "_initialize", "_asyncToGenerator", "_this2", "loadStoredData", "setInterval", "cleanupExpiredEntries", "get", "log", "error", "initialize", "apply", "arguments", "_checkLimit", "endpoint", "config", "getEndpointConfig", "<PERSON><PERSON>ey", "entry", "count", "firstRequest", "allowed", "remaining", "set", "persistData", "checkLimit", "_x", "_x2", "_recordSuccess", "skipSuccessfulRequests", "recordSuccess", "_x3", "_x4", "_recordFailure", "skipFailedRequests", "recordFailure", "_x5", "_x6", "_getRemainingRequests", "max", "getRemainingRequests", "_x7", "_x8", "_resetLimit", "delete", "resetLimit", "_x9", "_x0", "_getLimitStatus", "status", "Object", "keys", "result", "getLimitStatus", "_x1", "updateEndpointConfig", "assign", "_clearAll", "clear", "removeItem", "clearAll", "keyGenerator", "_loadStoredData", "stored", "getItem", "data", "JSON", "parse", "entries", "_persistData", "fromEntries", "setItem", "stringify", "_this3", "keysToDelete", "_ref", "_ref2", "_slicedToArray", "push", "for<PERSON>ach", "length", "rateLimitingService", "checkRateLimit", "resetRateLimit"], "sources": ["rateLimiting.ts"], "sourcesContent": ["import AsyncStorage from '@react-native-async-storage/async-storage';\nimport env from '@/config/environment';\n\n/**\n * Comprehensive Rate Limiting Service\n * Implements multiple rate limiting strategies for API protection\n */\n\nexport interface RateLimitConfig {\n  windowMs: number; // Time window in milliseconds\n  maxRequests: number; // Maximum requests per window\n  skipSuccessfulRequests?: boolean;\n  skipFailedRequests?: boolean;\n  keyGenerator?: (identifier: string) => string;\n  onLimitReached?: (identifier: string, resetTime: number) => void;\n}\n\nexport interface RateLimitResult {\n  allowed: boolean;\n  remaining: number;\n  resetTime: number;\n  retryAfter?: number;\n}\n\nexport interface RateLimitEntry {\n  count: number;\n  resetTime: number;\n  firstRequest: number;\n}\n\nexport interface EndpointLimits {\n  // Authentication endpoints\n  login: RateLimitConfig;\n  signup: RateLimitConfig;\n  resetPassword: RateLimitConfig;\n  \n  // AI endpoints\n  aiAnalysis: RateLimitConfig;\n  aiCoaching: RateLimitConfig;\n  \n  // Data endpoints\n  uploadVideo: RateLimitConfig;\n  saveSession: RateLimitConfig;\n  \n  // Social endpoints\n  sendMessage: RateLimitConfig;\n  createPost: RateLimitConfig;\n  \n  // General API\n  general: RateLimitConfig;\n}\n\nclass RateLimitingService {\n  private storage: Map<string, RateLimitEntry> = new Map();\n  private readonly storageKey = 'rate_limit_data';\n\n  private readonly endpointLimits: EndpointLimits = {\n    // Authentication - stricter limits\n    login: {\n      windowMs: 15 * 60 * 1000, // 15 minutes\n      maxRequests: 5, // 5 attempts per 15 minutes\n      onLimitReached: (identifier: string, resetTime: number) => this.handleAuthLimitReached(identifier, resetTime),\n    },\n    signup: {\n      windowMs: 60 * 60 * 1000, // 1 hour\n      maxRequests: 3, // 3 signups per hour\n    },\n    resetPassword: {\n      windowMs: 60 * 60 * 1000, // 1 hour\n      maxRequests: 3, // 3 reset attempts per hour\n    },\n\n    // AI endpoints - moderate limits due to cost\n    aiAnalysis: {\n      windowMs: 60 * 60 * 1000, // 1 hour\n      maxRequests: env.getEnvironment() === 'production' ? 10 : 50, // 10 per hour in prod\n    },\n    aiCoaching: {\n      windowMs: 60 * 60 * 1000, // 1 hour\n      maxRequests: env.getEnvironment() === 'production' ? 20 : 100, // 20 per hour in prod\n    },\n\n    // Data endpoints\n    uploadVideo: {\n      windowMs: 60 * 60 * 1000, // 1 hour\n      maxRequests: 20, // 20 uploads per hour\n    },\n    saveSession: {\n      windowMs: 60 * 60 * 1000, // 1 hour\n      maxRequests: 50, // 50 sessions per hour\n    },\n\n    // Social endpoints\n    sendMessage: {\n      windowMs: 60 * 1000, // 1 minute\n      maxRequests: 10, // 10 messages per minute\n    },\n    createPost: {\n      windowMs: 60 * 60 * 1000, // 1 hour\n      maxRequests: 20, // 20 posts per hour\n    },\n\n    // General API\n    general: {\n      windowMs: 60 * 1000, // 1 minute\n      maxRequests: 100, // 100 requests per minute\n    },\n  };\n\n  /**\n   * Initialize rate limiting service\n   */\n  async initialize(): Promise<void> {\n    try {\n      await this.loadStoredData();\n      \n      // Clean up expired entries every 5 minutes\n      setInterval(() => {\n        this.cleanupExpiredEntries();\n      }, 5 * 60 * 1000);\n\n      if (env.get('DEBUG_MODE')) {\n        console.log('🚦 Rate limiting service initialized');\n      }\n    } catch (error) {\n      console.error('Failed to initialize rate limiting service:', error);\n    }\n  }\n\n  /**\n   * Check if request is allowed\n   */\n  async checkLimit(\n    endpoint: keyof EndpointLimits | string,\n    identifier: string\n  ): Promise<RateLimitResult> {\n    try {\n      const config = this.getEndpointConfig(endpoint);\n      const key = this.generateKey(endpoint, identifier);\n      const now = Date.now();\n\n      let entry = this.storage.get(key);\n\n      // Create new entry if doesn't exist or window has expired\n      if (!entry || now >= entry.resetTime) {\n        entry = {\n          count: 0,\n          resetTime: now + config.windowMs,\n          firstRequest: now,\n        };\n      }\n\n      // Check if limit exceeded\n      if (entry.count >= config.maxRequests) {\n        const retryAfter = Math.ceil((entry.resetTime - now) / 1000);\n        \n        // Call limit reached handler\n        if (config.onLimitReached) {\n          config.onLimitReached(identifier, entry.resetTime);\n        }\n\n        return {\n          allowed: false,\n          remaining: 0,\n          resetTime: entry.resetTime,\n          retryAfter,\n        };\n      }\n\n      // Increment counter and store\n      entry.count++;\n      this.storage.set(key, entry);\n      await this.persistData();\n\n      return {\n        allowed: true,\n        remaining: config.maxRequests - entry.count,\n        resetTime: entry.resetTime,\n      };\n    } catch (error) {\n      console.error('Rate limit check failed:', error);\n      // Allow request on error to avoid blocking legitimate users\n      return {\n        allowed: true,\n        remaining: 999,\n        resetTime: Date.now() + 60000,\n      };\n    }\n  }\n\n  /**\n   * Record successful request (for endpoints that skip successful requests)\n   */\n  async recordSuccess(\n    endpoint: keyof EndpointLimits | string,\n    identifier: string\n  ): Promise<void> {\n    const config = this.getEndpointConfig(endpoint);\n    \n    if (config.skipSuccessfulRequests) {\n      const key = this.generateKey(endpoint, identifier);\n      const entry = this.storage.get(key);\n      \n      if (entry && entry.count > 0) {\n        entry.count--;\n        this.storage.set(key, entry);\n        await this.persistData();\n      }\n    }\n  }\n\n  /**\n   * Record failed request (for endpoints that skip failed requests)\n   */\n  async recordFailure(\n    endpoint: keyof EndpointLimits | string,\n    identifier: string\n  ): Promise<void> {\n    const config = this.getEndpointConfig(endpoint);\n    \n    if (config.skipFailedRequests) {\n      const key = this.generateKey(endpoint, identifier);\n      const entry = this.storage.get(key);\n      \n      if (entry && entry.count > 0) {\n        entry.count--;\n        this.storage.set(key, entry);\n        await this.persistData();\n      }\n    }\n  }\n\n  /**\n   * Get remaining requests for an endpoint\n   */\n  async getRemainingRequests(\n    endpoint: keyof EndpointLimits | string,\n    identifier: string\n  ): Promise<number> {\n    const config = this.getEndpointConfig(endpoint);\n    const key = this.generateKey(endpoint, identifier);\n    const entry = this.storage.get(key);\n    const now = Date.now();\n\n    if (!entry || now >= entry.resetTime) {\n      return config.maxRequests;\n    }\n\n    return Math.max(0, config.maxRequests - entry.count);\n  }\n\n  /**\n   * Reset rate limit for specific endpoint and identifier\n   */\n  async resetLimit(\n    endpoint: keyof EndpointLimits | string,\n    identifier: string\n  ): Promise<void> {\n    const key = this.generateKey(endpoint, identifier);\n    this.storage.delete(key);\n    await this.persistData();\n  }\n\n  /**\n   * Get rate limit status for all endpoints\n   */\n  async getLimitStatus(identifier: string): Promise<Record<string, RateLimitResult>> {\n    const status: Record<string, RateLimitResult> = {};\n\n    for (const endpoint of Object.keys(this.endpointLimits)) {\n      const result = await this.checkLimit(endpoint, identifier);\n      // Restore the count since checkLimit increments it\n      if (result.allowed) {\n        const key = this.generateKey(endpoint, identifier);\n        const entry = this.storage.get(key);\n        if (entry) {\n          entry.count--;\n          this.storage.set(key, entry);\n        }\n      }\n      status[endpoint] = result;\n    }\n\n    await this.persistData();\n    return status;\n  }\n\n  /**\n   * Update endpoint configuration\n   */\n  updateEndpointConfig(\n    endpoint: keyof EndpointLimits | string,\n    config: Partial<RateLimitConfig>\n  ): void {\n    if (endpoint in this.endpointLimits) {\n      this.endpointLimits[endpoint as keyof EndpointLimits] = {\n        ...this.endpointLimits[endpoint as keyof EndpointLimits],\n        ...config,\n      };\n    }\n  }\n\n  /**\n   * Clear all rate limit data\n   */\n  async clearAll(): Promise<void> {\n    this.storage.clear();\n    await AsyncStorage.removeItem(this.storageKey);\n  }\n\n  /**\n   * Get endpoint configuration\n   */\n  private getEndpointConfig(endpoint: keyof EndpointLimits | string): RateLimitConfig {\n    if (endpoint in this.endpointLimits) {\n      return this.endpointLimits[endpoint as keyof EndpointLimits];\n    }\n    return this.endpointLimits.general;\n  }\n\n  /**\n   * Generate storage key\n   */\n  private generateKey(endpoint: string, identifier: string): string {\n    const config = this.getEndpointConfig(endpoint);\n    \n    if (config.keyGenerator) {\n      return config.keyGenerator(identifier);\n    }\n    \n    return `${endpoint}:${identifier}`;\n  }\n\n  /**\n   * Load stored rate limit data\n   */\n  private async loadStoredData(): Promise<void> {\n    try {\n      const stored = await AsyncStorage.getItem(this.storageKey);\n      if (stored) {\n        const data = JSON.parse(stored);\n        this.storage = new Map(Object.entries(data));\n        \n        // Clean up expired entries\n        this.cleanupExpiredEntries();\n      }\n    } catch (error) {\n      console.error('Failed to load rate limit data:', error);\n      this.storage.clear();\n    }\n  }\n\n  /**\n   * Persist rate limit data\n   */\n  private async persistData(): Promise<void> {\n    try {\n      const data = Object.fromEntries(this.storage);\n      await AsyncStorage.setItem(this.storageKey, JSON.stringify(data));\n    } catch (error) {\n      console.error('Failed to persist rate limit data:', error);\n    }\n  }\n\n  /**\n   * Clean up expired entries\n   */\n  private cleanupExpiredEntries(): void {\n    const now = Date.now();\n    const keysToDelete: string[] = [];\n\n    for (const [key, entry] of this.storage) {\n      if (now >= entry.resetTime) {\n        keysToDelete.push(key);\n      }\n    }\n\n    keysToDelete.forEach(key => this.storage.delete(key));\n\n    if (keysToDelete.length > 0) {\n      this.persistData();\n    }\n  }\n\n  /**\n   * Handle authentication limit reached\n   */\n  private handleAuthLimitReached = (identifier: string, resetTime: number): void => {\n    const retryAfter = Math.ceil((resetTime - Date.now()) / 1000);\n    \n    console.warn(`Authentication rate limit exceeded for ${identifier}. Retry after ${retryAfter} seconds.`);\n    \n    // In a real app, you might want to:\n    // 1. Show a user-friendly message\n    // 2. Implement progressive delays\n    // 3. Send security alerts\n    // 4. Log suspicious activity\n  };\n}\n\n// Create singleton instance\nexport const rateLimitingService = new RateLimitingService();\n\n// Convenience functions\nexport const checkRateLimit = (endpoint: keyof EndpointLimits | string, identifier: string) =>\n  rateLimitingService.checkLimit(endpoint, identifier);\n\nexport const recordSuccess = (endpoint: keyof EndpointLimits | string, identifier: string) =>\n  rateLimitingService.recordSuccess(endpoint, identifier);\n\nexport const recordFailure = (endpoint: keyof EndpointLimits | string, identifier: string) =>\n  rateLimitingService.recordFailure(endpoint, identifier);\n\nexport const getRemainingRequests = (endpoint: keyof EndpointLimits | string, identifier: string) =>\n  rateLimitingService.getRemainingRequests(endpoint, identifier);\n\nexport const resetRateLimit = (endpoint: keyof EndpointLimits | string, identifier: string) =>\n  rateLimitingService.resetLimit(endpoint, identifier);\n\nexport default rateLimitingService;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,YAAY,MAAM,2CAA2C;AACpE,OAAOC,GAAG;AAA6B,IAmDjCC,mBAAmB;EAAA,SAAAA,oBAAA;IAAA,IAAAC,KAAA;IAAAC,eAAA,OAAAF,mBAAA;IAAA,KACfG,OAAO,IAAAC,cAAA,GAAAC,CAAA,OAAgC,IAAIC,GAAG,CAAC,CAAC;IAAA,KACvCC,UAAU,IAAAH,cAAA,GAAAC,CAAA,OAAG,iBAAiB;IAAA,KAE9BG,cAAc,IAAAJ,cAAA,GAAAC,CAAA,OAAmB;MAEhDI,KAAK,EAAE;QACLC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACxBC,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,SAAhBA,cAAcA,CAAGC,UAAkB,EAAEC,SAAiB,EAAK;UAAAV,cAAA,GAAAW,CAAA;UAAAX,cAAA,GAAAC,CAAA;UAAA,OAAAJ,KAAI,CAACe,sBAAsB,CAACH,UAAU,EAAEC,SAAS,CAAC;QAAD;MAC9G,CAAC;MACDG,MAAM,EAAE;QACNP,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACxBC,WAAW,EAAE;MACf,CAAC;MACDO,aAAa,EAAE;QACbR,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACxBC,WAAW,EAAE;MACf,CAAC;MAGDQ,UAAU,EAAE;QACVT,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACxBC,WAAW,EAAEZ,GAAG,CAACqB,cAAc,CAAC,CAAC,KAAK,YAAY,IAAAhB,cAAA,GAAAiB,CAAA,UAAG,EAAE,KAAAjB,cAAA,GAAAiB,CAAA,UAAG,EAAE;MAC9D,CAAC;MACDC,UAAU,EAAE;QACVZ,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACxBC,WAAW,EAAEZ,GAAG,CAACqB,cAAc,CAAC,CAAC,KAAK,YAAY,IAAAhB,cAAA,GAAAiB,CAAA,UAAG,EAAE,KAAAjB,cAAA,GAAAiB,CAAA,UAAG,GAAG;MAC/D,CAAC;MAGDE,WAAW,EAAE;QACXb,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACxBC,WAAW,EAAE;MACf,CAAC;MACDa,WAAW,EAAE;QACXd,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACxBC,WAAW,EAAE;MACf,CAAC;MAGDc,WAAW,EAAE;QACXf,QAAQ,EAAE,EAAE,GAAG,IAAI;QACnBC,WAAW,EAAE;MACf,CAAC;MACDe,UAAU,EAAE;QACVhB,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACxBC,WAAW,EAAE;MACf,CAAC;MAGDgB,OAAO,EAAE;QACPjB,QAAQ,EAAE,EAAE,GAAG,IAAI;QACnBC,WAAW,EAAE;MACf;IACF,CAAC;IAAA,KAwROK,sBAAsB,IAAAZ,cAAA,GAAAC,CAAA,QAAG,UAACQ,UAAkB,EAAEC,SAAiB,EAAW;MAAAV,cAAA,GAAAW,CAAA;MAChF,IAAMa,UAAU,IAAAxB,cAAA,GAAAC,CAAA,SAAGwB,IAAI,CAACC,IAAI,CAAC,CAAChB,SAAS,GAAGiB,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC;MAAC5B,cAAA,GAAAC,CAAA;MAE9D4B,OAAO,CAACC,IAAI,CAAC,0CAA0CrB,UAAU,iBAAiBe,UAAU,WAAW,CAAC;IAO1G,CAAC;EAAA;EAAA,OAAAO,YAAA,CAAAnC,mBAAA;IAAAoC,GAAA;IAAAC,KAAA;MAAA,IAAAC,WAAA,GAAAC,iBAAA,CA7RD,aAAkC;QAAA,IAAAC,MAAA;QAAApC,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAChC,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACF,MAAM,IAAI,CAACoC,cAAc,CAAC,CAAC;UAACrC,cAAA,GAAAC,CAAA;UAG5BqC,WAAW,CAAC,YAAM;YAAAtC,cAAA,GAAAW,CAAA;YAAAX,cAAA,GAAAC,CAAA;YAChBmC,MAAI,CAACG,qBAAqB,CAAC,CAAC;UAC9B,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;UAACvC,cAAA,GAAAC,CAAA;UAElB,IAAIN,GAAG,CAAC6C,GAAG,CAAC,YAAY,CAAC,EAAE;YAAAxC,cAAA,GAAAiB,CAAA;YAAAjB,cAAA,GAAAC,CAAA;YACzB4B,OAAO,CAACY,GAAG,CAAC,sCAAsC,CAAC;UACrD,CAAC;YAAAzC,cAAA,GAAAiB,CAAA;UAAA;QACH,CAAC,CAAC,OAAOyB,KAAK,EAAE;UAAA1C,cAAA,GAAAC,CAAA;UACd4B,OAAO,CAACa,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;QACrE;MACF,CAAC;MAAA,SAfKC,UAAUA,CAAA;QAAA,OAAAT,WAAA,CAAAU,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVF,UAAU;IAAA;EAAA;IAAAX,GAAA;IAAAC,KAAA;MAAA,IAAAa,WAAA,GAAAX,iBAAA,CAoBhB,WACEY,QAAuC,EACvCtC,UAAkB,EACQ;QAAAT,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAC1B,IAAI;UACF,IAAM+C,MAAM,IAAAhD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACgD,iBAAiB,CAACF,QAAQ,CAAC;UAC/C,IAAMf,GAAG,IAAAhC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACiD,WAAW,CAACH,QAAQ,EAAEtC,UAAU,CAAC;UAClD,IAAMmB,GAAG,IAAA5B,cAAA,GAAAC,CAAA,QAAG0B,IAAI,CAACC,GAAG,CAAC,CAAC;UAEtB,IAAIuB,KAAK,IAAAnD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,OAAO,CAACyC,GAAG,CAACR,GAAG,CAAC;UAAChC,cAAA,GAAAC,CAAA;UAGlC,IAAI,CAAAD,cAAA,GAAAiB,CAAA,WAACkC,KAAK,MAAAnD,cAAA,GAAAiB,CAAA,UAAIW,GAAG,IAAIuB,KAAK,CAACzC,SAAS,GAAE;YAAAV,cAAA,GAAAiB,CAAA;YAAAjB,cAAA,GAAAC,CAAA;YACpCkD,KAAK,GAAG;cACNC,KAAK,EAAE,CAAC;cACR1C,SAAS,EAAEkB,GAAG,GAAGoB,MAAM,CAAC1C,QAAQ;cAChC+C,YAAY,EAAEzB;YAChB,CAAC;UACH,CAAC;YAAA5B,cAAA,GAAAiB,CAAA;UAAA;UAAAjB,cAAA,GAAAC,CAAA;UAGD,IAAIkD,KAAK,CAACC,KAAK,IAAIJ,MAAM,CAACzC,WAAW,EAAE;YAAAP,cAAA,GAAAiB,CAAA;YACrC,IAAMO,UAAU,IAAAxB,cAAA,GAAAC,CAAA,QAAGwB,IAAI,CAACC,IAAI,CAAC,CAACyB,KAAK,CAACzC,SAAS,GAAGkB,GAAG,IAAI,IAAI,CAAC;YAAC5B,cAAA,GAAAC,CAAA;YAG7D,IAAI+C,MAAM,CAACxC,cAAc,EAAE;cAAAR,cAAA,GAAAiB,CAAA;cAAAjB,cAAA,GAAAC,CAAA;cACzB+C,MAAM,CAACxC,cAAc,CAACC,UAAU,EAAE0C,KAAK,CAACzC,SAAS,CAAC;YACpD,CAAC;cAAAV,cAAA,GAAAiB,CAAA;YAAA;YAAAjB,cAAA,GAAAC,CAAA;YAED,OAAO;cACLqD,OAAO,EAAE,KAAK;cACdC,SAAS,EAAE,CAAC;cACZ7C,SAAS,EAAEyC,KAAK,CAACzC,SAAS;cAC1Bc,UAAU,EAAVA;YACF,CAAC;UACH,CAAC;YAAAxB,cAAA,GAAAiB,CAAA;UAAA;UAAAjB,cAAA,GAAAC,CAAA;UAGDkD,KAAK,CAACC,KAAK,EAAE;UAACpD,cAAA,GAAAC,CAAA;UACd,IAAI,CAACF,OAAO,CAACyD,GAAG,CAACxB,GAAG,EAAEmB,KAAK,CAAC;UAACnD,cAAA,GAAAC,CAAA;UAC7B,MAAM,IAAI,CAACwD,WAAW,CAAC,CAAC;UAACzD,cAAA,GAAAC,CAAA;UAEzB,OAAO;YACLqD,OAAO,EAAE,IAAI;YACbC,SAAS,EAAEP,MAAM,CAACzC,WAAW,GAAG4C,KAAK,CAACC,KAAK;YAC3C1C,SAAS,EAAEyC,KAAK,CAACzC;UACnB,CAAC;QACH,CAAC,CAAC,OAAOgC,KAAK,EAAE;UAAA1C,cAAA,GAAAC,CAAA;UACd4B,OAAO,CAACa,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAAC1C,cAAA,GAAAC,CAAA;UAEjD,OAAO;YACLqD,OAAO,EAAE,IAAI;YACbC,SAAS,EAAE,GAAG;YACd7C,SAAS,EAAEiB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG;UAC1B,CAAC;QACH;MACF,CAAC;MAAA,SAxDK8B,UAAUA,CAAAC,EAAA,EAAAC,GAAA;QAAA,OAAAd,WAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVa,UAAU;IAAA;EAAA;IAAA1B,GAAA;IAAAC,KAAA;MAAA,IAAA4B,cAAA,GAAA1B,iBAAA,CA6DhB,WACEY,QAAuC,EACvCtC,UAAkB,EACH;QAAAT,cAAA,GAAAW,CAAA;QACf,IAAMqC,MAAM,IAAAhD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACgD,iBAAiB,CAACF,QAAQ,CAAC;QAAC/C,cAAA,GAAAC,CAAA;QAEhD,IAAI+C,MAAM,CAACc,sBAAsB,EAAE;UAAA9D,cAAA,GAAAiB,CAAA;UACjC,IAAMe,GAAG,IAAAhC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACiD,WAAW,CAACH,QAAQ,EAAEtC,UAAU,CAAC;UAClD,IAAM0C,KAAK,IAAAnD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,OAAO,CAACyC,GAAG,CAACR,GAAG,CAAC;UAAChC,cAAA,GAAAC,CAAA;UAEpC,IAAI,CAAAD,cAAA,GAAAiB,CAAA,UAAAkC,KAAK,MAAAnD,cAAA,GAAAiB,CAAA,UAAIkC,KAAK,CAACC,KAAK,GAAG,CAAC,GAAE;YAAApD,cAAA,GAAAiB,CAAA;YAAAjB,cAAA,GAAAC,CAAA;YAC5BkD,KAAK,CAACC,KAAK,EAAE;YAACpD,cAAA,GAAAC,CAAA;YACd,IAAI,CAACF,OAAO,CAACyD,GAAG,CAACxB,GAAG,EAAEmB,KAAK,CAAC;YAACnD,cAAA,GAAAC,CAAA;YAC7B,MAAM,IAAI,CAACwD,WAAW,CAAC,CAAC;UAC1B,CAAC;YAAAzD,cAAA,GAAAiB,CAAA;UAAA;QACH,CAAC;UAAAjB,cAAA,GAAAiB,CAAA;QAAA;MACH,CAAC;MAAA,SAhBK8C,aAAaA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAJ,cAAA,CAAAjB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAbkB,aAAa;IAAA;EAAA;IAAA/B,GAAA;IAAAC,KAAA;MAAA,IAAAiC,cAAA,GAAA/B,iBAAA,CAqBnB,WACEY,QAAuC,EACvCtC,UAAkB,EACH;QAAAT,cAAA,GAAAW,CAAA;QACf,IAAMqC,MAAM,IAAAhD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACgD,iBAAiB,CAACF,QAAQ,CAAC;QAAC/C,cAAA,GAAAC,CAAA;QAEhD,IAAI+C,MAAM,CAACmB,kBAAkB,EAAE;UAAAnE,cAAA,GAAAiB,CAAA;UAC7B,IAAMe,GAAG,IAAAhC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACiD,WAAW,CAACH,QAAQ,EAAEtC,UAAU,CAAC;UAClD,IAAM0C,KAAK,IAAAnD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,OAAO,CAACyC,GAAG,CAACR,GAAG,CAAC;UAAChC,cAAA,GAAAC,CAAA;UAEpC,IAAI,CAAAD,cAAA,GAAAiB,CAAA,WAAAkC,KAAK,MAAAnD,cAAA,GAAAiB,CAAA,WAAIkC,KAAK,CAACC,KAAK,GAAG,CAAC,GAAE;YAAApD,cAAA,GAAAiB,CAAA;YAAAjB,cAAA,GAAAC,CAAA;YAC5BkD,KAAK,CAACC,KAAK,EAAE;YAACpD,cAAA,GAAAC,CAAA;YACd,IAAI,CAACF,OAAO,CAACyD,GAAG,CAACxB,GAAG,EAAEmB,KAAK,CAAC;YAACnD,cAAA,GAAAC,CAAA;YAC7B,MAAM,IAAI,CAACwD,WAAW,CAAC,CAAC;UAC1B,CAAC;YAAAzD,cAAA,GAAAiB,CAAA;UAAA;QACH,CAAC;UAAAjB,cAAA,GAAAiB,CAAA;QAAA;MACH,CAAC;MAAA,SAhBKmD,aAAaA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAJ,cAAA,CAAAtB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAbuB,aAAa;IAAA;EAAA;IAAApC,GAAA;IAAAC,KAAA;MAAA,IAAAsC,qBAAA,GAAApC,iBAAA,CAqBnB,WACEY,QAAuC,EACvCtC,UAAkB,EACD;QAAAT,cAAA,GAAAW,CAAA;QACjB,IAAMqC,MAAM,IAAAhD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACgD,iBAAiB,CAACF,QAAQ,CAAC;QAC/C,IAAMf,GAAG,IAAAhC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACiD,WAAW,CAACH,QAAQ,EAAEtC,UAAU,CAAC;QAClD,IAAM0C,KAAK,IAAAnD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,OAAO,CAACyC,GAAG,CAACR,GAAG,CAAC;QACnC,IAAMJ,GAAG,IAAA5B,cAAA,GAAAC,CAAA,QAAG0B,IAAI,CAACC,GAAG,CAAC,CAAC;QAAC5B,cAAA,GAAAC,CAAA;QAEvB,IAAI,CAAAD,cAAA,GAAAiB,CAAA,YAACkC,KAAK,MAAAnD,cAAA,GAAAiB,CAAA,WAAIW,GAAG,IAAIuB,KAAK,CAACzC,SAAS,GAAE;UAAAV,cAAA,GAAAiB,CAAA;UAAAjB,cAAA,GAAAC,CAAA;UACpC,OAAO+C,MAAM,CAACzC,WAAW;QAC3B,CAAC;UAAAP,cAAA,GAAAiB,CAAA;QAAA;QAAAjB,cAAA,GAAAC,CAAA;QAED,OAAOwB,IAAI,CAAC+C,GAAG,CAAC,CAAC,EAAExB,MAAM,CAACzC,WAAW,GAAG4C,KAAK,CAACC,KAAK,CAAC;MACtD,CAAC;MAAA,SAdKqB,oBAAoBA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAJ,qBAAA,CAAA3B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApB4B,oBAAoB;IAAA;EAAA;IAAAzC,GAAA;IAAAC,KAAA;MAAA,IAAA2C,WAAA,GAAAzC,iBAAA,CAmB1B,WACEY,QAAuC,EACvCtC,UAAkB,EACH;QAAAT,cAAA,GAAAW,CAAA;QACf,IAAMqB,GAAG,IAAAhC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACiD,WAAW,CAACH,QAAQ,EAAEtC,UAAU,CAAC;QAACT,cAAA,GAAAC,CAAA;QACnD,IAAI,CAACF,OAAO,CAAC8E,MAAM,CAAC7C,GAAG,CAAC;QAAChC,cAAA,GAAAC,CAAA;QACzB,MAAM,IAAI,CAACwD,WAAW,CAAC,CAAC;MAC1B,CAAC;MAAA,SAPKqB,UAAUA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAJ,WAAA,CAAAhC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAViC,UAAU;IAAA;EAAA;IAAA9C,GAAA;IAAAC,KAAA;MAAA,IAAAgD,eAAA,GAAA9C,iBAAA,CAYhB,WAAqB1B,UAAkB,EAA4C;QAAAT,cAAA,GAAAW,CAAA;QACjF,IAAMuE,MAAuC,IAAAlF,cAAA,GAAAC,CAAA,QAAG,CAAC,CAAC;QAACD,cAAA,GAAAC,CAAA;QAEnD,KAAK,IAAM8C,QAAQ,IAAIoC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChF,cAAc,CAAC,EAAE;UACvD,IAAMiF,MAAM,IAAArF,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACyD,UAAU,CAACX,QAAQ,EAAEtC,UAAU,CAAC;UAACT,cAAA,GAAAC,CAAA;UAE3D,IAAIoF,MAAM,CAAC/B,OAAO,EAAE;YAAAtD,cAAA,GAAAiB,CAAA;YAClB,IAAMe,GAAG,IAAAhC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACiD,WAAW,CAACH,QAAQ,EAAEtC,UAAU,CAAC;YAClD,IAAM0C,KAAK,IAAAnD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,OAAO,CAACyC,GAAG,CAACR,GAAG,CAAC;YAAChC,cAAA,GAAAC,CAAA;YACpC,IAAIkD,KAAK,EAAE;cAAAnD,cAAA,GAAAiB,CAAA;cAAAjB,cAAA,GAAAC,CAAA;cACTkD,KAAK,CAACC,KAAK,EAAE;cAACpD,cAAA,GAAAC,CAAA;cACd,IAAI,CAACF,OAAO,CAACyD,GAAG,CAACxB,GAAG,EAAEmB,KAAK,CAAC;YAC9B,CAAC;cAAAnD,cAAA,GAAAiB,CAAA;YAAA;UACH,CAAC;YAAAjB,cAAA,GAAAiB,CAAA;UAAA;UAAAjB,cAAA,GAAAC,CAAA;UACDiF,MAAM,CAACnC,QAAQ,CAAC,GAAGsC,MAAM;QAC3B;QAACrF,cAAA,GAAAC,CAAA;QAED,MAAM,IAAI,CAACwD,WAAW,CAAC,CAAC;QAACzD,cAAA,GAAAC,CAAA;QACzB,OAAOiF,MAAM;MACf,CAAC;MAAA,SAnBKI,cAAcA,CAAAC,GAAA;QAAA,OAAAN,eAAA,CAAArC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdyC,cAAc;IAAA;EAAA;IAAAtD,GAAA;IAAAC,KAAA,EAwBpB,SAAAuD,oBAAoBA,CAClBzC,QAAuC,EACvCC,MAAgC,EAC1B;MAAAhD,cAAA,GAAAW,CAAA;MAAAX,cAAA,GAAAC,CAAA;MACN,IAAI8C,QAAQ,IAAI,IAAI,CAAC3C,cAAc,EAAE;QAAAJ,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QACnC,IAAI,CAACG,cAAc,CAAC2C,QAAQ,CAAyB,GAAAoC,MAAA,CAAAM,MAAA,KAChD,IAAI,CAACrF,cAAc,CAAC2C,QAAQ,CAAyB,EACrDC,MAAM,CACV;MACH,CAAC;QAAAhD,cAAA,GAAAiB,CAAA;MAAA;IACH;EAAC;IAAAe,GAAA;IAAAC,KAAA;MAAA,IAAAyD,SAAA,GAAAvD,iBAAA,CAKD,aAAgC;QAAAnC,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAC9B,IAAI,CAACF,OAAO,CAAC4F,KAAK,CAAC,CAAC;QAAC3F,cAAA,GAAAC,CAAA;QACrB,MAAMP,YAAY,CAACkG,UAAU,CAAC,IAAI,CAACzF,UAAU,CAAC;MAChD,CAAC;MAAA,SAHK0F,QAAQA,CAAA;QAAA,OAAAH,SAAA,CAAA9C,KAAA,OAAAC,SAAA;MAAA;MAAA,OAARgD,QAAQ;IAAA;EAAA;IAAA7D,GAAA;IAAAC,KAAA,EAQd,SAAQgB,iBAAiBA,CAACF,QAAuC,EAAmB;MAAA/C,cAAA,GAAAW,CAAA;MAAAX,cAAA,GAAAC,CAAA;MAClF,IAAI8C,QAAQ,IAAI,IAAI,CAAC3C,cAAc,EAAE;QAAAJ,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QACnC,OAAO,IAAI,CAACG,cAAc,CAAC2C,QAAQ,CAAyB;MAC9D,CAAC;QAAA/C,cAAA,GAAAiB,CAAA;MAAA;MAAAjB,cAAA,GAAAC,CAAA;MACD,OAAO,IAAI,CAACG,cAAc,CAACmB,OAAO;IACpC;EAAC;IAAAS,GAAA;IAAAC,KAAA,EAKD,SAAQiB,WAAWA,CAACH,QAAgB,EAAEtC,UAAkB,EAAU;MAAAT,cAAA,GAAAW,CAAA;MAChE,IAAMqC,MAAM,IAAAhD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACgD,iBAAiB,CAACF,QAAQ,CAAC;MAAC/C,cAAA,GAAAC,CAAA;MAEhD,IAAI+C,MAAM,CAAC8C,YAAY,EAAE;QAAA9F,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QACvB,OAAO+C,MAAM,CAAC8C,YAAY,CAACrF,UAAU,CAAC;MACxC,CAAC;QAAAT,cAAA,GAAAiB,CAAA;MAAA;MAAAjB,cAAA,GAAAC,CAAA;MAED,OAAO,GAAG8C,QAAQ,IAAItC,UAAU,EAAE;IACpC;EAAC;IAAAuB,GAAA;IAAAC,KAAA;MAAA,IAAA8D,eAAA,GAAA5D,iBAAA,CAKD,aAA8C;QAAAnC,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAC5C,IAAI;UACF,IAAM+F,MAAM,IAAAhG,cAAA,GAAAC,CAAA,cAASP,YAAY,CAACuG,OAAO,CAAC,IAAI,CAAC9F,UAAU,CAAC;UAACH,cAAA,GAAAC,CAAA;UAC3D,IAAI+F,MAAM,EAAE;YAAAhG,cAAA,GAAAiB,CAAA;YACV,IAAMiF,IAAI,IAAAlG,cAAA,GAAAC,CAAA,QAAGkG,IAAI,CAACC,KAAK,CAACJ,MAAM,CAAC;YAAChG,cAAA,GAAAC,CAAA;YAChC,IAAI,CAACF,OAAO,GAAG,IAAIG,GAAG,CAACiF,MAAM,CAACkB,OAAO,CAACH,IAAI,CAAC,CAAC;YAAClG,cAAA,GAAAC,CAAA;YAG7C,IAAI,CAACsC,qBAAqB,CAAC,CAAC;UAC9B,CAAC;YAAAvC,cAAA,GAAAiB,CAAA;UAAA;QACH,CAAC,CAAC,OAAOyB,KAAK,EAAE;UAAA1C,cAAA,GAAAC,CAAA;UACd4B,OAAO,CAACa,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UAAC1C,cAAA,GAAAC,CAAA;UACxD,IAAI,CAACF,OAAO,CAAC4F,KAAK,CAAC,CAAC;QACtB;MACF,CAAC;MAAA,SAdatD,cAAcA,CAAA;QAAA,OAAA0D,eAAA,CAAAnD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdR,cAAc;IAAA;EAAA;IAAAL,GAAA;IAAAC,KAAA;MAAA,IAAAqE,YAAA,GAAAnE,iBAAA,CAmB5B,aAA2C;QAAAnC,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QACzC,IAAI;UACF,IAAMiG,IAAI,IAAAlG,cAAA,GAAAC,CAAA,QAAGkF,MAAM,CAACoB,WAAW,CAAC,IAAI,CAACxG,OAAO,CAAC;UAACC,cAAA,GAAAC,CAAA;UAC9C,MAAMP,YAAY,CAAC8G,OAAO,CAAC,IAAI,CAACrG,UAAU,EAAEgG,IAAI,CAACM,SAAS,CAACP,IAAI,CAAC,CAAC;QACnE,CAAC,CAAC,OAAOxD,KAAK,EAAE;UAAA1C,cAAA,GAAAC,CAAA;UACd4B,OAAO,CAACa,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC5D;MACF,CAAC;MAAA,SAPae,WAAWA,CAAA;QAAA,OAAA6C,YAAA,CAAA1D,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAXY,WAAW;IAAA;EAAA;IAAAzB,GAAA;IAAAC,KAAA,EAYzB,SAAQM,qBAAqBA,CAAA,EAAS;MAAA,IAAAmE,MAAA;MAAA1G,cAAA,GAAAW,CAAA;MACpC,IAAMiB,GAAG,IAAA5B,cAAA,GAAAC,CAAA,QAAG0B,IAAI,CAACC,GAAG,CAAC,CAAC;MACtB,IAAM+E,YAAsB,IAAA3G,cAAA,GAAAC,CAAA,QAAG,EAAE;MAACD,cAAA,GAAAC,CAAA;MAElC,SAAA2G,IAAA,IAA2B,IAAI,CAAC7G,OAAO,EAAE;QAAA,IAAA8G,KAAA,GAAAC,cAAA,CAAAF,IAAA;QAAA,IAA7B5E,GAAG,GAAA6E,KAAA;QAAA,IAAE1D,KAAK,GAAA0D,KAAA;QAAA7G,cAAA,GAAAC,CAAA;QACpB,IAAI2B,GAAG,IAAIuB,KAAK,CAACzC,SAAS,EAAE;UAAAV,cAAA,GAAAiB,CAAA;UAAAjB,cAAA,GAAAC,CAAA;UAC1B0G,YAAY,CAACI,IAAI,CAAC/E,GAAG,CAAC;QACxB,CAAC;UAAAhC,cAAA,GAAAiB,CAAA;QAAA;MACH;MAACjB,cAAA,GAAAC,CAAA;MAED0G,YAAY,CAACK,OAAO,CAAC,UAAAhF,GAAG,EAAI;QAAAhC,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAAA,OAAAyG,MAAI,CAAC3G,OAAO,CAAC8E,MAAM,CAAC7C,GAAG,CAAC;MAAD,CAAC,CAAC;MAAChC,cAAA,GAAAC,CAAA;MAEtD,IAAI0G,YAAY,CAACM,MAAM,GAAG,CAAC,EAAE;QAAAjH,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QAC3B,IAAI,CAACwD,WAAW,CAAC,CAAC;MACpB,CAAC;QAAAzD,cAAA,GAAAiB,CAAA;MAAA;IACH;EAAC;AAAA;AAmBH,OAAO,IAAMiG,mBAAmB,IAAAlH,cAAA,GAAAC,CAAA,SAAG,IAAIL,mBAAmB,CAAC,CAAC;AAACI,cAAA,GAAAC,CAAA;AAG7D,OAAO,IAAMkH,cAAc,GAAG,SAAjBA,cAAcA,CAAIpE,QAAuC,EAAEtC,UAAkB,EACxF;EAAAT,cAAA,GAAAW,CAAA;EAAAX,cAAA,GAAAC,CAAA;EAAA,OAAAiH,mBAAmB,CAACxD,UAAU,CAACX,QAAQ,EAAEtC,UAAU,CAAC;AAAD,CAAC;AAACT,cAAA,GAAAC,CAAA;AAEvD,OAAO,IAAM8D,aAAa,GAAG,SAAhBA,aAAaA,CAAIhB,QAAuC,EAAEtC,UAAkB,EACvF;EAAAT,cAAA,GAAAW,CAAA;EAAAX,cAAA,GAAAC,CAAA;EAAA,OAAAiH,mBAAmB,CAACnD,aAAa,CAAChB,QAAQ,EAAEtC,UAAU,CAAC;AAAD,CAAC;AAACT,cAAA,GAAAC,CAAA;AAE1D,OAAO,IAAMmE,aAAa,GAAG,SAAhBA,aAAaA,CAAIrB,QAAuC,EAAEtC,UAAkB,EACvF;EAAAT,cAAA,GAAAW,CAAA;EAAAX,cAAA,GAAAC,CAAA;EAAA,OAAAiH,mBAAmB,CAAC9C,aAAa,CAACrB,QAAQ,EAAEtC,UAAU,CAAC;AAAD,CAAC;AAACT,cAAA,GAAAC,CAAA;AAE1D,OAAO,IAAMwE,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAI1B,QAAuC,EAAEtC,UAAkB,EAC9F;EAAAT,cAAA,GAAAW,CAAA;EAAAX,cAAA,GAAAC,CAAA;EAAA,OAAAiH,mBAAmB,CAACzC,oBAAoB,CAAC1B,QAAQ,EAAEtC,UAAU,CAAC;AAAD,CAAC;AAACT,cAAA,GAAAC,CAAA;AAEjE,OAAO,IAAMmH,cAAc,GAAG,SAAjBA,cAAcA,CAAIrE,QAAuC,EAAEtC,UAAkB,EACxF;EAAAT,cAAA,GAAAW,CAAA;EAAAX,cAAA,GAAAC,CAAA;EAAA,OAAAiH,mBAAmB,CAACpC,UAAU,CAAC/B,QAAQ,EAAEtC,UAAU,CAAC;AAAD,CAAC;AAEtD,eAAeyG,mBAAmB", "ignoreList": []}