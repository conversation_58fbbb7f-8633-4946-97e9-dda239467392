7f1e81b5d6b1ecd5f2e2c84f7d69acd1
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _AnimatedNode = _interopRequireDefault(require("./AnimatedNode"));
var _AnimatedWithChildren = _interopRequireDefault(require("./AnimatedWithChildren"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../NativeAnimatedHelper"));
var AnimatedTransform = function (_AnimatedWithChildren2) {
  function AnimatedTransform(transforms) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedTransform);
    _this = _callSuper(this, AnimatedTransform);
    _this._transforms = transforms;
    return _this;
  }
  (0, _inherits2.default)(AnimatedTransform, _AnimatedWithChildren2);
  return (0, _createClass2.default)(AnimatedTransform, [{
    key: "__makeNative",
    value: function __makeNative() {
      this._transforms.forEach(function (transform) {
        for (var key in transform) {
          var value = transform[key];
          if (value instanceof _AnimatedNode.default) {
            value.__makeNative();
          }
        }
      });
      _superPropGet(AnimatedTransform, "__makeNative", this, 3)([]);
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      return this._transforms.map(function (transform) {
        var result = {};
        for (var key in transform) {
          var value = transform[key];
          if (value instanceof _AnimatedNode.default) {
            result[key] = value.__getValue();
          } else {
            result[key] = value;
          }
        }
        return result;
      });
    }
  }, {
    key: "__getAnimatedValue",
    value: function __getAnimatedValue() {
      return this._transforms.map(function (transform) {
        var result = {};
        for (var key in transform) {
          var value = transform[key];
          if (value instanceof _AnimatedNode.default) {
            result[key] = value.__getAnimatedValue();
          } else {
            result[key] = value;
          }
        }
        return result;
      });
    }
  }, {
    key: "__attach",
    value: function __attach() {
      var _this2 = this;
      this._transforms.forEach(function (transform) {
        for (var key in transform) {
          var value = transform[key];
          if (value instanceof _AnimatedNode.default) {
            value.__addChild(_this2);
          }
        }
      });
    }
  }, {
    key: "__detach",
    value: function __detach() {
      var _this3 = this;
      this._transforms.forEach(function (transform) {
        for (var key in transform) {
          var value = transform[key];
          if (value instanceof _AnimatedNode.default) {
            value.__removeChild(_this3);
          }
        }
      });
      _superPropGet(AnimatedTransform, "__detach", this, 3)([]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      var transConfigs = [];
      this._transforms.forEach(function (transform) {
        for (var key in transform) {
          var value = transform[key];
          if (value instanceof _AnimatedNode.default) {
            transConfigs.push({
              type: 'animated',
              property: key,
              nodeTag: value.__getNativeTag()
            });
          } else {
            transConfigs.push({
              type: 'static',
              property: key,
              value: _NativeAnimatedHelper.default.transformDataType(value)
            });
          }
        }
      });
      _NativeAnimatedHelper.default.validateTransform(transConfigs);
      return {
        type: 'transform',
        transforms: transConfigs
      };
    }
  }]);
}(_AnimatedWithChildren.default);
var _default = exports.default = AnimatedTransform;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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