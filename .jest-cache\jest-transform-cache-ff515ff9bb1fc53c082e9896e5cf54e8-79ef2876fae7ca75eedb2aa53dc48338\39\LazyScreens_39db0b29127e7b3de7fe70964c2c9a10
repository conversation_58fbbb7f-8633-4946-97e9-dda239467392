975c7914db83d7f62700420dd55bfe79
function cov_1xzjlns84d() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\LazyScreens.tsx";
  var hash = "ac7c3cf7cfcd0c79158b7d5bee5a82c14e87e960";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\LazyScreens.tsx",
    statementMap: {
      "0": {
        start: {
          line: 14,
          column: 29
        },
        end: {
          line: 18,
          column: 1
        }
      },
      "1": {
        start: {
          line: 15,
          column: 8
        },
        end: {
          line: 15,
          column: 36
        }
      },
      "2": {
        start: {
          line: 20,
          column: 28
        },
        end: {
          line: 24,
          column: 1
        }
      },
      "3": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "4": {
        start: {
          line: 26,
          column: 28
        },
        end: {
          line: 30,
          column: 1
        }
      },
      "5": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 39
        }
      },
      "6": {
        start: {
          line: 32,
          column: 27
        },
        end: {
          line: 36,
          column: 1
        }
      },
      "7": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 33,
          column: 38
        }
      },
      "8": {
        start: {
          line: 42,
          column: 33
        },
        end: {
          line: 46,
          column: 1
        }
      },
      "9": {
        start: {
          line: 43,
          column: 8
        },
        end: {
          line: 43,
          column: 38
        }
      },
      "10": {
        start: {
          line: 48,
          column: 30
        },
        end: {
          line: 52,
          column: 1
        }
      },
      "11": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 49,
          column: 35
        }
      },
      "12": {
        start: {
          line: 54,
          column: 33
        },
        end: {
          line: 58,
          column: 1
        }
      },
      "13": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 55,
          column: 38
        }
      },
      "14": {
        start: {
          line: 60,
          column: 35
        },
        end: {
          line: 64,
          column: 1
        }
      },
      "15": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 40
        }
      },
      "16": {
        start: {
          line: 70,
          column: 26
        },
        end: {
          line: 74,
          column: 1
        }
      },
      "17": {
        start: {
          line: 71,
          column: 8
        },
        end: {
          line: 71,
          column: 30
        }
      },
      "18": {
        start: {
          line: 76,
          column: 31
        },
        end: {
          line: 80,
          column: 1
        }
      },
      "19": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 77,
          column: 35
        }
      },
      "20": {
        start: {
          line: 82,
          column: 30
        },
        end: {
          line: 86,
          column: 1
        }
      },
      "21": {
        start: {
          line: 83,
          column: 8
        },
        end: {
          line: 83,
          column: 34
        }
      },
      "22": {
        start: {
          line: 92,
          column: 27
        },
        end: {
          line: 96,
          column: 1
        }
      },
      "23": {
        start: {
          line: 93,
          column: 8
        },
        end: {
          line: 93,
          column: 31
        }
      },
      "24": {
        start: {
          line: 98,
          column: 32
        },
        end: {
          line: 102,
          column: 1
        }
      },
      "25": {
        start: {
          line: 99,
          column: 8
        },
        end: {
          line: 99,
          column: 36
        }
      },
      "26": {
        start: {
          line: 104,
          column: 37
        },
        end: {
          line: 108,
          column: 1
        }
      },
      "27": {
        start: {
          line: 105,
          column: 8
        },
        end: {
          line: 105,
          column: 42
        }
      },
      "28": {
        start: {
          line: 114,
          column: 28
        },
        end: {
          line: 118,
          column: 1
        }
      },
      "29": {
        start: {
          line: 115,
          column: 8
        },
        end: {
          line: 115,
          column: 32
        }
      },
      "30": {
        start: {
          line: 120,
          column: 33
        },
        end: {
          line: 124,
          column: 1
        }
      },
      "31": {
        start: {
          line: 121,
          column: 8
        },
        end: {
          line: 121,
          column: 37
        }
      },
      "32": {
        start: {
          line: 126,
          column: 24
        },
        end: {
          line: 130,
          column: 1
        }
      },
      "33": {
        start: {
          line: 127,
          column: 8
        },
        end: {
          line: 127,
          column: 28
        }
      },
      "34": {
        start: {
          line: 132,
          column: 27
        },
        end: {
          line: 136,
          column: 1
        }
      },
      "35": {
        start: {
          line: 133,
          column: 8
        },
        end: {
          line: 133,
          column: 31
        }
      },
      "36": {
        start: {
          line: 142,
          column: 25
        },
        end: {
          line: 146,
          column: 1
        }
      },
      "37": {
        start: {
          line: 143,
          column: 8
        },
        end: {
          line: 143,
          column: 34
        }
      },
      "38": {
        start: {
          line: 148,
          column: 28
        },
        end: {
          line: 152,
          column: 1
        }
      },
      "39": {
        start: {
          line: 149,
          column: 8
        },
        end: {
          line: 149,
          column: 37
        }
      },
      "40": {
        start: {
          line: 154,
          column: 30
        },
        end: {
          line: 158,
          column: 1
        }
      },
      "41": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 155,
          column: 41
        }
      },
      "42": {
        start: {
          line: 172,
          column: 25
        },
        end: {
          line: 176,
          column: 3
        }
      },
      "43": {
        start: {
          line: 173,
          column: 43
        },
        end: {
          line: 173,
          column: 71
        }
      },
      "44": {
        start: {
          line: 174,
          column: 42
        },
        end: {
          line: 174,
          column: 73
        }
      },
      "45": {
        start: {
          line: 175,
          column: 42
        },
        end: {
          line: 175,
          column: 73
        }
      },
      "46": {
        start: {
          line: 178,
          column: 25
        },
        end: {
          line: 181,
          column: 3
        }
      },
      "47": {
        start: {
          line: 179,
          column: 39
        },
        end: {
          line: 179,
          column: 65
        }
      },
      "48": {
        start: {
          line: 180,
          column: 42
        },
        end: {
          line: 180,
          column: 71
        }
      },
      "49": {
        start: {
          line: 183,
          column: 28
        },
        end: {
          line: 187,
          column: 3
        }
      },
      "50": {
        start: {
          line: 184,
          column: 47
        },
        end: {
          line: 184,
          column: 77
        }
      },
      "51": {
        start: {
          line: 185,
          column: 44
        },
        end: {
          line: 185,
          column: 71
        }
      },
      "52": {
        start: {
          line: 186,
          column: 47
        },
        end: {
          line: 186,
          column: 77
        }
      },
      "53": {
        start: {
          line: 189,
          column: 28
        },
        end: {
          line: 192,
          column: 3
        }
      },
      "54": {
        start: {
          line: 190,
          column: 51
        },
        end: {
          line: 190,
          column: 85
        }
      },
      "55": {
        start: {
          line: 191,
          column: 41
        },
        end: {
          line: 191,
          column: 64
        }
      },
      "56": {
        start: {
          line: 195,
          column: 28
        },
        end: {
          line: 195,
          column: 30
        }
      },
      "57": {
        start: {
          line: 197,
          column: 2
        },
        end: {
          line: 209,
          column: 3
        }
      },
      "58": {
        start: {
          line: 198,
          column: 4
        },
        end: {
          line: 198,
          column: 48
        }
      },
      "59": {
        start: {
          line: 200,
          column: 4
        },
        end: {
          line: 202,
          column: 5
        }
      },
      "60": {
        start: {
          line: 201,
          column: 6
        },
        end: {
          line: 201,
          column: 53
        }
      },
      "61": {
        start: {
          line: 204,
          column: 4
        },
        end: {
          line: 206,
          column: 5
        }
      },
      "62": {
        start: {
          line: 205,
          column: 6
        },
        end: {
          line: 205,
          column: 53
        }
      },
      "63": {
        start: {
          line: 208,
          column: 4
        },
        end: {
          line: 208,
          column: 48
        }
      },
      "64": {
        start: {
          line: 211,
          column: 2
        },
        end: {
          line: 211,
          column: 41
        }
      },
      "65": {
        start: {
          line: 222,
          column: 2
        },
        end: {
          line: 229,
          column: 4
        }
      },
      "66": {
        start: {
          line: 236,
          column: 21
        },
        end: {
          line: 236,
          column: 36
        }
      },
      "67": {
        start: {
          line: 238,
          column: 2
        },
        end: {
          line: 259,
          column: 4
        }
      },
      "68": {
        start: {
          line: 240,
          column: 6
        },
        end: {
          line: 240,
          column: 53
        }
      },
      "69": {
        start: {
          line: 245,
          column: 6
        },
        end: {
          line: 245,
          column: 16
        }
      },
      "70": {
        start: {
          line: 250,
          column: 6
        },
        end: {
          line: 257,
          column: 8
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 15,
            column: 2
          },
          end: {
            line: 15,
            column: 3
          }
        },
        loc: {
          start: {
            line: 15,
            column: 8
          },
          end: {
            line: 15,
            column: 36
          }
        },
        line: 15
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 21,
            column: 2
          },
          end: {
            line: 21,
            column: 3
          }
        },
        loc: {
          start: {
            line: 21,
            column: 8
          },
          end: {
            line: 21,
            column: 39
          }
        },
        line: 21
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 27,
            column: 2
          },
          end: {
            line: 27,
            column: 3
          }
        },
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 39
          }
        },
        line: 27
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 33,
            column: 2
          },
          end: {
            line: 33,
            column: 3
          }
        },
        loc: {
          start: {
            line: 33,
            column: 8
          },
          end: {
            line: 33,
            column: 38
          }
        },
        line: 33
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 43,
            column: 2
          },
          end: {
            line: 43,
            column: 3
          }
        },
        loc: {
          start: {
            line: 43,
            column: 8
          },
          end: {
            line: 43,
            column: 38
          }
        },
        line: 43
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 49,
            column: 2
          },
          end: {
            line: 49,
            column: 3
          }
        },
        loc: {
          start: {
            line: 49,
            column: 8
          },
          end: {
            line: 49,
            column: 35
          }
        },
        line: 49
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 55,
            column: 2
          },
          end: {
            line: 55,
            column: 3
          }
        },
        loc: {
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 55,
            column: 38
          }
        },
        line: 55
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 61,
            column: 2
          },
          end: {
            line: 61,
            column: 3
          }
        },
        loc: {
          start: {
            line: 61,
            column: 8
          },
          end: {
            line: 61,
            column: 40
          }
        },
        line: 61
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 71,
            column: 2
          },
          end: {
            line: 71,
            column: 3
          }
        },
        loc: {
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 71,
            column: 30
          }
        },
        line: 71
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 77,
            column: 2
          },
          end: {
            line: 77,
            column: 3
          }
        },
        loc: {
          start: {
            line: 77,
            column: 8
          },
          end: {
            line: 77,
            column: 35
          }
        },
        line: 77
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 83,
            column: 2
          },
          end: {
            line: 83,
            column: 3
          }
        },
        loc: {
          start: {
            line: 83,
            column: 8
          },
          end: {
            line: 83,
            column: 34
          }
        },
        line: 83
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 93,
            column: 2
          },
          end: {
            line: 93,
            column: 3
          }
        },
        loc: {
          start: {
            line: 93,
            column: 8
          },
          end: {
            line: 93,
            column: 31
          }
        },
        line: 93
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 99,
            column: 2
          },
          end: {
            line: 99,
            column: 3
          }
        },
        loc: {
          start: {
            line: 99,
            column: 8
          },
          end: {
            line: 99,
            column: 36
          }
        },
        line: 99
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 105,
            column: 2
          },
          end: {
            line: 105,
            column: 3
          }
        },
        loc: {
          start: {
            line: 105,
            column: 8
          },
          end: {
            line: 105,
            column: 42
          }
        },
        line: 105
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 115,
            column: 2
          },
          end: {
            line: 115,
            column: 3
          }
        },
        loc: {
          start: {
            line: 115,
            column: 8
          },
          end: {
            line: 115,
            column: 32
          }
        },
        line: 115
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 121,
            column: 2
          },
          end: {
            line: 121,
            column: 3
          }
        },
        loc: {
          start: {
            line: 121,
            column: 8
          },
          end: {
            line: 121,
            column: 37
          }
        },
        line: 121
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 127,
            column: 2
          },
          end: {
            line: 127,
            column: 3
          }
        },
        loc: {
          start: {
            line: 127,
            column: 8
          },
          end: {
            line: 127,
            column: 28
          }
        },
        line: 127
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 133,
            column: 2
          },
          end: {
            line: 133,
            column: 3
          }
        },
        loc: {
          start: {
            line: 133,
            column: 8
          },
          end: {
            line: 133,
            column: 31
          }
        },
        line: 133
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 143,
            column: 2
          },
          end: {
            line: 143,
            column: 3
          }
        },
        loc: {
          start: {
            line: 143,
            column: 8
          },
          end: {
            line: 143,
            column: 34
          }
        },
        line: 143
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 149,
            column: 2
          },
          end: {
            line: 149,
            column: 3
          }
        },
        loc: {
          start: {
            line: 149,
            column: 8
          },
          end: {
            line: 149,
            column: 37
          }
        },
        line: 149
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 155,
            column: 2
          },
          end: {
            line: 155,
            column: 3
          }
        },
        loc: {
          start: {
            line: 155,
            column: 8
          },
          end: {
            line: 155,
            column: 41
          }
        },
        line: 155
      },
      "21": {
        name: "initializePreloading",
        decl: {
          start: {
            line: 167,
            column: 16
          },
          end: {
            line: 167,
            column: 36
          }
        },
        loc: {
          start: {
            line: 171,
            column: 3
          },
          end: {
            line: 212,
            column: 1
          }
        },
        line: 171
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 173,
            column: 37
          },
          end: {
            line: 173,
            column: 38
          }
        },
        loc: {
          start: {
            line: 173,
            column: 43
          },
          end: {
            line: 173,
            column: 71
          }
        },
        line: 173
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 174,
            column: 36
          },
          end: {
            line: 174,
            column: 37
          }
        },
        loc: {
          start: {
            line: 174,
            column: 42
          },
          end: {
            line: 174,
            column: 73
          }
        },
        line: 174
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 175,
            column: 36
          },
          end: {
            line: 175,
            column: 37
          }
        },
        loc: {
          start: {
            line: 175,
            column: 42
          },
          end: {
            line: 175,
            column: 73
          }
        },
        line: 175
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 179,
            column: 33
          },
          end: {
            line: 179,
            column: 34
          }
        },
        loc: {
          start: {
            line: 179,
            column: 39
          },
          end: {
            line: 179,
            column: 65
          }
        },
        line: 179
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 180,
            column: 36
          },
          end: {
            line: 180,
            column: 37
          }
        },
        loc: {
          start: {
            line: 180,
            column: 42
          },
          end: {
            line: 180,
            column: 71
          }
        },
        line: 180
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 184,
            column: 41
          },
          end: {
            line: 184,
            column: 42
          }
        },
        loc: {
          start: {
            line: 184,
            column: 47
          },
          end: {
            line: 184,
            column: 77
          }
        },
        line: 184
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 185,
            column: 38
          },
          end: {
            line: 185,
            column: 39
          }
        },
        loc: {
          start: {
            line: 185,
            column: 44
          },
          end: {
            line: 185,
            column: 71
          }
        },
        line: 185
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 186,
            column: 41
          },
          end: {
            line: 186,
            column: 42
          }
        },
        loc: {
          start: {
            line: 186,
            column: 47
          },
          end: {
            line: 186,
            column: 77
          }
        },
        line: 186
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 190,
            column: 45
          },
          end: {
            line: 190,
            column: 46
          }
        },
        loc: {
          start: {
            line: 190,
            column: 51
          },
          end: {
            line: 190,
            column: 85
          }
        },
        line: 190
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 191,
            column: 35
          },
          end: {
            line: 191,
            column: 36
          }
        },
        loc: {
          start: {
            line: 191,
            column: 41
          },
          end: {
            line: 191,
            column: 64
          }
        },
        line: 191
      },
      "32": {
        name: "getBundleInfo",
        decl: {
          start: {
            line: 221,
            column: 16
          },
          end: {
            line: 221,
            column: 29
          }
        },
        loc: {
          start: {
            line: 221,
            column: 32
          },
          end: {
            line: 230,
            column: 1
          }
        },
        line: 221
      },
      "33": {
        name: "trackLazyLoadingPerformance",
        decl: {
          start: {
            line: 235,
            column: 16
          },
          end: {
            line: 235,
            column: 43
          }
        },
        loc: {
          start: {
            line: 235,
            column: 46
          },
          end: {
            line: 260,
            column: 1
          }
        },
        line: 235
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 239,
            column: 21
          },
          end: {
            line: 239,
            column: 22
          }
        },
        loc: {
          start: {
            line: 239,
            column: 27
          },
          end: {
            line: 241,
            column: 5
          }
        },
        line: 239
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 243,
            column: 22
          },
          end: {
            line: 243,
            column: 23
          }
        },
        loc: {
          start: {
            line: 243,
            column: 28
          },
          end: {
            line: 246,
            column: 5
          }
        },
        line: 243
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 248,
            column: 27
          },
          end: {
            line: 248,
            column: 28
          }
        },
        loc: {
          start: {
            line: 248,
            column: 33
          },
          end: {
            line: 258,
            column: 5
          }
        },
        line: 248
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 197,
            column: 2
          },
          end: {
            line: 209,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 197,
            column: 2
          },
          end: {
            line: 209,
            column: 3
          }
        }, {
          start: {
            line: 207,
            column: 9
          },
          end: {
            line: 209,
            column: 3
          }
        }],
        line: 197
      },
      "1": {
        loc: {
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 202,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 202,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 200
      },
      "2": {
        loc: {
          start: {
            line: 204,
            column: 4
          },
          end: {
            line: 206,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 4
          },
          end: {
            line: 206,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 204
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ac7c3cf7cfcd0c79158b7d5bee5a82c14e87e960"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1xzjlns84d = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1xzjlns84d();
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
import { createLazyComponent, preloadComponents } from "../utils/lazyLoading";
export var LazyDashboard = (cov_1xzjlns84d().s[0]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[0]++;
  cov_1xzjlns84d().s[1]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/(tabs)"));
  });
}, 'Dashboard', {
  preload: true,
  timeout: 5000
}));
export var LazyTraining = (cov_1xzjlns84d().s[2]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[1]++;
  cov_1xzjlns84d().s[3]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/(tabs)/training"));
  });
}, 'Training', {
  preload: true,
  timeout: 5000
}));
export var LazyProgress = (cov_1xzjlns84d().s[4]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[2]++;
  cov_1xzjlns84d().s[5]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/(tabs)/progress"));
  });
}, 'Progress', {
  preload: true,
  timeout: 5000
}));
export var LazyProfile = (cov_1xzjlns84d().s[6]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[3]++;
  cov_1xzjlns84d().s[7]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/(tabs)/profile"));
  });
}, 'Profile', {
  preload: true,
  timeout: 5000
}));
export var LazyVideoAnalysis = (cov_1xzjlns84d().s[8]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[4]++;
  cov_1xzjlns84d().s[9]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/video-analysis"));
  });
}, 'VideoAnalysis', {
  preload: false,
  timeout: 8000
}));
export var LazyAICoaching = (cov_1xzjlns84d().s[10]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[5]++;
  cov_1xzjlns84d().s[11]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/ai-coaching"));
  });
}, 'AICoaching', {
  preload: false,
  timeout: 8000
}));
export var LazyMatchAnalysis = (cov_1xzjlns84d().s[12]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[6]++;
  cov_1xzjlns84d().s[13]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/match-analysis"));
  });
}, 'MatchAnalysis', {
  preload: false,
  timeout: 8000
}));
export var LazySkillAssessment = (cov_1xzjlns84d().s[14]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[7]++;
  cov_1xzjlns84d().s[15]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/skill-assessment"));
  });
}, 'SkillAssessment', {
  preload: false,
  timeout: 8000
}));
export var LazySocial = (cov_1xzjlns84d().s[16]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[8]++;
  cov_1xzjlns84d().s[17]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/social"));
  });
}, 'Social', {
  preload: false,
  timeout: 10000
}));
export var LazyLeaderboard = (cov_1xzjlns84d().s[18]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[9]++;
  cov_1xzjlns84d().s[19]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/leaderboard"));
  });
}, 'Leaderboard', {
  preload: false,
  timeout: 10000
}));
export var LazyChallenges = (cov_1xzjlns84d().s[20]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[10]++;
  cov_1xzjlns84d().s[21]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/challenges"));
  });
}, 'Challenges', {
  preload: false,
  timeout: 10000
}));
export var LazyPremium = (cov_1xzjlns84d().s[22]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[11]++;
  cov_1xzjlns84d().s[23]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/premium"));
  });
}, 'Premium', {
  preload: false,
  timeout: 10000
}));
export var LazySubscription = (cov_1xzjlns84d().s[24]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[12]++;
  cov_1xzjlns84d().s[25]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/subscription"));
  });
}, 'Subscription', {
  preload: false,
  timeout: 10000
}));
export var LazyAdvancedAnalytics = (cov_1xzjlns84d().s[26]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[13]++;
  cov_1xzjlns84d().s[27]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/advanced-analytics"));
  });
}, 'AdvancedAnalytics', {
  preload: false,
  timeout: 10000
}));
export var LazySettings = (cov_1xzjlns84d().s[28]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[14]++;
  cov_1xzjlns84d().s[29]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/settings"));
  });
}, 'Settings', {
  preload: false,
  timeout: 5000
}));
export var LazyNotifications = (cov_1xzjlns84d().s[30]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[15]++;
  cov_1xzjlns84d().s[31]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/notifications"));
  });
}, 'Notifications', {
  preload: false,
  timeout: 5000
}));
export var LazyHelp = (cov_1xzjlns84d().s[32]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[16]++;
  cov_1xzjlns84d().s[33]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/help"));
  });
}, 'Help', {
  preload: false,
  timeout: 5000
}));
export var LazyPrivacy = (cov_1xzjlns84d().s[34]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[17]++;
  cov_1xzjlns84d().s[35]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/privacy"));
  });
}, 'Privacy', {
  preload: false,
  timeout: 5000
}));
export var LazyLogin = (cov_1xzjlns84d().s[36]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[18]++;
  cov_1xzjlns84d().s[37]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/auth/login"));
  });
}, 'Login', {
  preload: true,
  timeout: 3000
}));
export var LazyRegister = (cov_1xzjlns84d().s[38]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[19]++;
  cov_1xzjlns84d().s[39]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/auth/register"));
  });
}, 'Register', {
  preload: true,
  timeout: 3000
}));
export var LazyOnboarding = (cov_1xzjlns84d().s[40]++, createLazyComponent(function () {
  cov_1xzjlns84d().f[20]++;
  cov_1xzjlns84d().s[41]++;
  return Promise.resolve().then(function () {
    return _interopRequireWildcard(require("../app/(tabs)/onboarding"));
  });
}, 'Onboarding', {
  preload: true,
  timeout: 5000
}));
export function initializePreloading(userContext) {
  cov_1xzjlns84d().f[21]++;
  var coreComponents = (cov_1xzjlns84d().s[42]++, [{
    name: 'Dashboard',
    importFunc: function importFunc() {
      cov_1xzjlns84d().f[22]++;
      cov_1xzjlns84d().s[43]++;
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require("../app/(tabs)"));
      });
    },
    priority: 'high'
  }, {
    name: 'Training',
    importFunc: function importFunc() {
      cov_1xzjlns84d().f[23]++;
      cov_1xzjlns84d().s[44]++;
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require("../app/(tabs)/training"));
      });
    },
    priority: 'high'
  }, {
    name: 'Progress',
    importFunc: function importFunc() {
      cov_1xzjlns84d().f[24]++;
      cov_1xzjlns84d().s[45]++;
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require("../app/(tabs)/progress"));
      });
    },
    priority: 'high'
  }]);
  var authComponents = (cov_1xzjlns84d().s[46]++, [{
    name: 'Login',
    importFunc: function importFunc() {
      cov_1xzjlns84d().f[25]++;
      cov_1xzjlns84d().s[47]++;
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require("../app/auth/login"));
      });
    },
    priority: 'high'
  }, {
    name: 'Register',
    importFunc: function importFunc() {
      cov_1xzjlns84d().f[26]++;
      cov_1xzjlns84d().s[48]++;
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require("../app/auth/register"));
      });
    },
    priority: 'high'
  }]);
  var featureComponents = (cov_1xzjlns84d().s[49]++, [{
    name: 'VideoAnalysis',
    importFunc: function importFunc() {
      cov_1xzjlns84d().f[27]++;
      cov_1xzjlns84d().s[50]++;
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require("../app/video-analysis"));
      });
    },
    priority: 'medium'
  }, {
    name: 'AICoaching',
    importFunc: function importFunc() {
      cov_1xzjlns84d().f[28]++;
      cov_1xzjlns84d().s[51]++;
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require("../app/ai-coaching"));
      });
    },
    priority: 'medium'
  }, {
    name: 'MatchAnalysis',
    importFunc: function importFunc() {
      cov_1xzjlns84d().f[29]++;
      cov_1xzjlns84d().s[52]++;
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require("../app/match-analysis"));
      });
    },
    priority: 'medium'
  }]);
  var premiumComponents = (cov_1xzjlns84d().s[53]++, [{
    name: 'AdvancedAnalytics',
    importFunc: function importFunc() {
      cov_1xzjlns84d().f[30]++;
      cov_1xzjlns84d().s[54]++;
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require("../app/advanced-analytics"));
      });
    },
    priority: 'low'
  }, {
    name: 'Premium',
    importFunc: function importFunc() {
      cov_1xzjlns84d().f[31]++;
      cov_1xzjlns84d().s[55]++;
      return Promise.resolve().then(function () {
        return _interopRequireWildcard(require("../app/premium"));
      });
    },
    priority: 'low'
  }]);
  var componentsToPreload = (cov_1xzjlns84d().s[56]++, []);
  cov_1xzjlns84d().s[57]++;
  if (userContext != null && userContext.isAuthenticated) {
    cov_1xzjlns84d().b[0][0]++;
    cov_1xzjlns84d().s[58]++;
    componentsToPreload.push.apply(componentsToPreload, coreComponents);
    cov_1xzjlns84d().s[59]++;
    if (userContext.hasCompletedOnboarding) {
      cov_1xzjlns84d().b[1][0]++;
      cov_1xzjlns84d().s[60]++;
      componentsToPreload.push.apply(componentsToPreload, featureComponents);
    } else {
      cov_1xzjlns84d().b[1][1]++;
    }
    cov_1xzjlns84d().s[61]++;
    if (userContext.isPremium) {
      cov_1xzjlns84d().b[2][0]++;
      cov_1xzjlns84d().s[62]++;
      componentsToPreload.push.apply(componentsToPreload, premiumComponents);
    } else {
      cov_1xzjlns84d().b[2][1]++;
    }
  } else {
    cov_1xzjlns84d().b[0][1]++;
    cov_1xzjlns84d().s[63]++;
    componentsToPreload.push.apply(componentsToPreload, authComponents);
  }
  cov_1xzjlns84d().s[64]++;
  preloadComponents(componentsToPreload);
}
export function getBundleInfo() {
  cov_1xzjlns84d().f[32]++;
  cov_1xzjlns84d().s[65]++;
  return {
    coreScreens: ['Dashboard', 'Training', 'Progress', 'Profile'],
    featureScreens: ['VideoAnalysis', 'AICoaching', 'MatchAnalysis', 'SkillAssessment'],
    socialScreens: ['Social', 'Leaderboard', 'Challenges'],
    premiumScreens: ['Premium', 'Subscription', 'AdvancedAnalytics'],
    utilityScreens: ['Settings', 'Notifications', 'Help', 'Privacy'],
    authScreens: ['Login', 'Register', 'Onboarding']
  };
}
export function trackLazyLoadingPerformance() {
  cov_1xzjlns84d().f[33]++;
  var bundleInfo = (cov_1xzjlns84d().s[66]++, getBundleInfo());
  cov_1xzjlns84d().s[67]++;
  return {
    getTotalScreens: function getTotalScreens() {
      cov_1xzjlns84d().f[34]++;
      cov_1xzjlns84d().s[68]++;
      return Object.values(bundleInfo).flat().length;
    },
    getLoadedScreens: function getLoadedScreens() {
      cov_1xzjlns84d().f[35]++;
      cov_1xzjlns84d().s[69]++;
      return [];
    },
    getBundleSizeEstimate: function getBundleSizeEstimate() {
      cov_1xzjlns84d().f[36]++;
      cov_1xzjlns84d().s[70]++;
      return {
        core: '~150KB',
        features: '~200KB',
        social: '~100KB',
        premium: '~150KB',
        utility: '~50KB',
        auth: '~75KB'
      };
    }
  };
}
export default {
  LazyDashboard: LazyDashboard,
  LazyTraining: LazyTraining,
  LazyProgress: LazyProgress,
  LazyProfile: LazyProfile,
  LazyVideoAnalysis: LazyVideoAnalysis,
  LazyAICoaching: LazyAICoaching,
  LazyMatchAnalysis: LazyMatchAnalysis,
  LazySkillAssessment: LazySkillAssessment,
  LazySocial: LazySocial,
  LazyLeaderboard: LazyLeaderboard,
  LazyChallenges: LazyChallenges,
  LazyPremium: LazyPremium,
  LazySubscription: LazySubscription,
  LazyAdvancedAnalytics: LazyAdvancedAnalytics,
  LazySettings: LazySettings,
  LazyNotifications: LazyNotifications,
  LazyHelp: LazyHelp,
  LazyPrivacy: LazyPrivacy,
  LazyLogin: LazyLogin,
  LazyRegister: LazyRegister,
  LazyOnboarding: LazyOnboarding,
  initializePreloading: initializePreloading,
  getBundleInfo: getBundleInfo,
  trackLazyLoadingPerformance: trackLazyLoadingPerformance
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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