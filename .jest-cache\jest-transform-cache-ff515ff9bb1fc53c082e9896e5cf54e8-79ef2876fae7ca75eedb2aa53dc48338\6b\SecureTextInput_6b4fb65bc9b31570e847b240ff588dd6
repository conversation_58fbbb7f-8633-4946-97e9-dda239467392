636060da00d0a800179a08ecdfafdbaf
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _objectWithoutProperties from "@babel/runtime/helpers/objectWithoutProperties";
var _excluded = ["label", "error", "helperText", "validationType", "showPasswordToggle", "enableValidation", "enableSanitization", "maxLength", "onChangeText", "onValidationChange", "securityLevel", "style"];
function cov_24eydchrob() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\security\\SecureTextInput.tsx";
  var hash = "65dce0a58f81952b7270e0f4953b3afc09d76381";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\security\\SecureTextInput.tsx",
    statementMap: {
      "0": {
        start: {
          line: 33,
          column: 15
        },
        end: {
          line: 41,
          column: 1
        }
      },
      "1": {
        start: {
          line: 58,
          column: 28
        },
        end: {
          line: 58,
          column: 41
        }
      },
      "2": {
        start: {
          line: 59,
          column: 28
        },
        end: {
          line: 59,
          column: 40
        }
      },
      "3": {
        start: {
          line: 60,
          column: 52
        },
        end: {
          line: 60,
          column: 67
        }
      },
      "4": {
        start: {
          line: 61,
          column: 50
        },
        end: {
          line: 61,
          column: 72
        }
      },
      "5": {
        start: {
          line: 62,
          column: 32
        },
        end: {
          line: 62,
          column: 46
        }
      },
      "6": {
        start: {
          line: 63,
          column: 36
        },
        end: {
          line: 63,
          column: 51
        }
      },
      "7": {
        start: {
          line: 64,
          column: 19
        },
        end: {
          line: 64,
          column: 42
        }
      },
      "8": {
        start: {
          line: 69,
          column: 24
        },
        end: {
          line: 123,
          column: 66
        }
      },
      "9": {
        start: {
          line: 70,
          column: 29
        },
        end: {
          line: 70,
          column: 31
        }
      },
      "10": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 74,
          column: 5
        }
      },
      "11": {
        start: {
          line: 73,
          column: 6
        },
        end: {
          line: 73,
          column: 43
        }
      },
      "12": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 79,
          column: 5
        }
      },
      "13": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 78,
          column: 63
        }
      },
      "14": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 112,
          column: 5
        }
      },
      "15": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 86,
          column: 9
        }
      },
      "16": {
        start: {
          line: 85,
          column: 10
        },
        end: {
          line: 85,
          column: 46
        }
      },
      "17": {
        start: {
          line: 87,
          column: 8
        },
        end: {
          line: 89,
          column: 9
        }
      },
      "18": {
        start: {
          line: 88,
          column: 10
        },
        end: {
          line: 88,
          column: 50
        }
      },
      "19": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 90,
          column: 14
        }
      },
      "20": {
        start: {
          line: 93,
          column: 8
        },
        end: {
          line: 98,
          column: 9
        }
      },
      "21": {
        start: {
          line: 94,
          column: 27
        },
        end: {
          line: 94,
          column: 68
        }
      },
      "22": {
        start: {
          line: 95,
          column: 10
        },
        end: {
          line: 97,
          column: 11
        }
      },
      "23": {
        start: {
          line: 96,
          column: 12
        },
        end: {
          line: 96,
          column: 46
        }
      },
      "24": {
        start: {
          line: 99,
          column: 8
        },
        end: {
          line: 99,
          column: 14
        }
      },
      "25": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 104,
          column: 9
        }
      },
      "26": {
        start: {
          line: 103,
          column: 10
        },
        end: {
          line: 103,
          column: 41
        }
      },
      "27": {
        start: {
          line: 105,
          column: 8
        },
        end: {
          line: 105,
          column: 14
        }
      },
      "28": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 110,
          column: 9
        }
      },
      "29": {
        start: {
          line: 109,
          column: 10
        },
        end: {
          line: 109,
          column: 67
        }
      },
      "30": {
        start: {
          line: 111,
          column: 8
        },
        end: {
          line: 111,
          column: 14
        }
      },
      "31": {
        start: {
          line: 115,
          column: 4
        },
        end: {
          line: 117,
          column: 5
        }
      },
      "32": {
        start: {
          line: 116,
          column: 6
        },
        end: {
          line: 116,
          column: 68
        }
      },
      "33": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 122,
          column: 6
        }
      },
      "34": {
        start: {
          line: 128,
          column: 27
        },
        end: {
          line: 155,
          column: 102
        }
      },
      "35": {
        start: {
          line: 129,
          column: 24
        },
        end: {
          line: 129,
          column: 28
        }
      },
      "36": {
        start: {
          line: 132,
          column: 4
        },
        end: {
          line: 143,
          column: 5
        }
      },
      "37": {
        start: {
          line: 133,
          column: 6
        },
        end: {
          line: 142,
          column: 7
        }
      },
      "38": {
        start: {
          line: 135,
          column: 10
        },
        end: {
          line: 135,
          column: 52
        }
      },
      "39": {
        start: {
          line: 136,
          column: 10
        },
        end: {
          line: 136,
          column: 16
        }
      },
      "40": {
        start: {
          line: 138,
          column: 10
        },
        end: {
          line: 138,
          column: 72
        }
      },
      "41": {
        start: {
          line: 139,
          column: 10
        },
        end: {
          line: 139,
          column: 16
        }
      },
      "42": {
        start: {
          line: 141,
          column: 10
        },
        end: {
          line: 141,
          column: 51
        }
      },
      "43": {
        start: {
          line: 145,
          column: 4
        },
        end: {
          line: 145,
          column: 28
        }
      },
      "44": {
        start: {
          line: 148,
          column: 23
        },
        end: {
          line: 148,
          column: 51
        }
      },
      "45": {
        start: {
          line: 149,
          column: 4
        },
        end: {
          line: 149,
          column: 43
        }
      },
      "46": {
        start: {
          line: 150,
          column: 4
        },
        end: {
          line: 150,
          column: 35
        }
      },
      "47": {
        start: {
          line: 153,
          column: 4
        },
        end: {
          line: 153,
          column: 34
        }
      },
      "48": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 154,
          column: 64
        }
      },
      "49": {
        start: {
          line: 160,
          column: 35
        },
        end: {
          line: 162,
          column: 8
        }
      },
      "50": {
        start: {
          line: 161,
          column: 4
        },
        end: {
          line: 161,
          column: 40
        }
      },
      "51": {
        start: {
          line: 161,
          column: 33
        },
        end: {
          line: 161,
          column: 38
        }
      },
      "52": {
        start: {
          line: 167,
          column: 31
        },
        end: {
          line: 197,
          column: 3
        }
      },
      "53": {
        start: {
          line: 168,
          column: 4
        },
        end: {
          line: 183,
          column: 5
        }
      },
      "54": {
        start: {
          line: 169,
          column: 23
        },
        end: {
          line: 169,
          column: 65
        }
      },
      "55": {
        start: {
          line: 170,
          column: 29
        },
        end: {
          line: 170,
          column: 62
        }
      },
      "56": {
        start: {
          line: 171,
          column: 29
        },
        end: {
          line: 171,
          column: 57
        }
      },
      "57": {
        start: {
          line: 173,
          column: 25
        },
        end: {
          line: 173,
          column: 68
        }
      },
      "58": {
        start: {
          line: 175,
          column: 6
        },
        end: {
          line: 182,
          column: 8
        }
      },
      "59": {
        start: {
          line: 185,
          column: 4
        },
        end: {
          line: 194,
          column: 5
        }
      },
      "60": {
        start: {
          line: 186,
          column: 6
        },
        end: {
          line: 193,
          column: 8
        }
      },
      "61": {
        start: {
          line: 196,
          column: 4
        },
        end: {
          line: 196,
          column: 16
        }
      },
      "62": {
        start: {
          line: 202,
          column: 24
        },
        end: {
          line: 218,
          column: 3
        }
      },
      "63": {
        start: {
          line: 203,
          column: 22
        },
        end: {
          line: 203,
          column: 36
        }
      },
      "64": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 207,
          column: 5
        }
      },
      "65": {
        start: {
          line: 206,
          column: 6
        },
        end: {
          line: 206,
          column: 49
        }
      },
      "66": {
        start: {
          line: 209,
          column: 4
        },
        end: {
          line: 211,
          column: 5
        }
      },
      "67": {
        start: {
          line: 210,
          column: 6
        },
        end: {
          line: 210,
          column: 47
        }
      },
      "68": {
        start: {
          line: 213,
          column: 4
        },
        end: {
          line: 215,
          column: 5
        }
      },
      "69": {
        start: {
          line: 214,
          column: 6
        },
        end: {
          line: 214,
          column: 35
        }
      },
      "70": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 217,
          column: 21
        }
      },
      "71": {
        start: {
          line: 220,
          column: 2
        },
        end: {
          line: 281,
          column: 4
        }
      },
      "72": {
        start: {
          line: 232,
          column: 25
        },
        end: {
          line: 232,
          column: 43
        }
      },
      "73": {
        start: {
          line: 233,
          column: 24
        },
        end: {
          line: 233,
          column: 43
        }
      },
      "74": {
        start: {
          line: 284,
          column: 15
        },
        end: {
          line: 356,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "SecureTextInput",
        decl: {
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 39
          }
        },
        loc: {
          start: {
            line: 57,
            column: 25
          },
          end: {
            line: 282,
            column: 1
          }
        },
        line: 57
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 69,
            column: 36
          },
          end: {
            line: 69,
            column: 37
          }
        },
        loc: {
          start: {
            line: 69,
            column: 94
          },
          end: {
            line: 123,
            column: 3
          }
        },
        line: 69
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 128,
            column: 39
          },
          end: {
            line: 128,
            column: 40
          }
        },
        loc: {
          start: {
            line: 128,
            column: 57
          },
          end: {
            line: 155,
            column: 3
          }
        },
        line: 128
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 160,
            column: 47
          },
          end: {
            line: 160,
            column: 48
          }
        },
        loc: {
          start: {
            line: 160,
            column: 53
          },
          end: {
            line: 162,
            column: 3
          }
        },
        line: 160
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 161,
            column: 25
          },
          end: {
            line: 161,
            column: 26
          }
        },
        loc: {
          start: {
            line: 161,
            column: 33
          },
          end: {
            line: 161,
            column: 38
          }
        },
        line: 161
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 167,
            column: 31
          },
          end: {
            line: 167,
            column: 32
          }
        },
        loc: {
          start: {
            line: 167,
            column: 37
          },
          end: {
            line: 197,
            column: 3
          }
        },
        line: 167
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 202,
            column: 24
          },
          end: {
            line: 202,
            column: 25
          }
        },
        loc: {
          start: {
            line: 202,
            column: 30
          },
          end: {
            line: 218,
            column: 3
          }
        },
        line: 202
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 232,
            column: 19
          },
          end: {
            line: 232,
            column: 20
          }
        },
        loc: {
          start: {
            line: 232,
            column: 25
          },
          end: {
            line: 232,
            column: 43
          }
        },
        line: 232
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 233,
            column: 18
          },
          end: {
            line: 233,
            column: 19
          }
        },
        loc: {
          start: {
            line: 233,
            column: 24
          },
          end: {
            line: 233,
            column: 43
          }
        },
        line: 233
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 47,
            column: 2
          },
          end: {
            line: 47,
            column: 25
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 47,
            column: 19
          },
          end: {
            line: 47,
            column: 25
          }
        }],
        line: 47
      },
      "1": {
        loc: {
          start: {
            line: 48,
            column: 2
          },
          end: {
            line: 48,
            column: 28
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 48,
            column: 23
          },
          end: {
            line: 48,
            column: 28
          }
        }],
        line: 48
      },
      "2": {
        loc: {
          start: {
            line: 49,
            column: 2
          },
          end: {
            line: 49,
            column: 25
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 49,
            column: 21
          },
          end: {
            line: 49,
            column: 25
          }
        }],
        line: 49
      },
      "3": {
        loc: {
          start: {
            line: 50,
            column: 2
          },
          end: {
            line: 50,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 50,
            column: 23
          },
          end: {
            line: 50,
            column: 27
          }
        }],
        line: 50
      },
      "4": {
        loc: {
          start: {
            line: 51,
            column: 2
          },
          end: {
            line: 51,
            column: 18
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 51,
            column: 14
          },
          end: {
            line: 51,
            column: 18
          }
        }],
        line: 51
      },
      "5": {
        loc: {
          start: {
            line: 54,
            column: 2
          },
          end: {
            line: 54,
            column: 26
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 54,
            column: 18
          },
          end: {
            line: 54,
            column: 26
          }
        }],
        line: 54
      },
      "6": {
        loc: {
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 74,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 74,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "7": {
        loc: {
          start: {
            line: 77,
            column: 4
          },
          end: {
            line: 79,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 4
          },
          end: {
            line: 79,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "8": {
        loc: {
          start: {
            line: 77,
            column: 29
          },
          end: {
            line: 77,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 77,
            column: 58
          },
          end: {
            line: 77,
            column: 65
          }
        }, {
          start: {
            line: 77,
            column: 68
          },
          end: {
            line: 77,
            column: 109
          }
        }],
        line: 77
      },
      "9": {
        loc: {
          start: {
            line: 77,
            column: 68
          },
          end: {
            line: 77,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 77,
            column: 95
          },
          end: {
            line: 77,
            column: 100
          }
        }, {
          start: {
            line: 77,
            column: 103
          },
          end: {
            line: 77,
            column: 109
          }
        }],
        line: 77
      },
      "10": {
        loc: {
          start: {
            line: 82,
            column: 4
          },
          end: {
            line: 112,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 83,
            column: 6
          },
          end: {
            line: 90,
            column: 14
          }
        }, {
          start: {
            line: 92,
            column: 6
          },
          end: {
            line: 99,
            column: 14
          }
        }, {
          start: {
            line: 101,
            column: 6
          },
          end: {
            line: 105,
            column: 14
          }
        }, {
          start: {
            line: 107,
            column: 6
          },
          end: {
            line: 111,
            column: 14
          }
        }],
        line: 82
      },
      "11": {
        loc: {
          start: {
            line: 84,
            column: 8
          },
          end: {
            line: 86,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 84,
            column: 8
          },
          end: {
            line: 86,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 84
      },
      "12": {
        loc: {
          start: {
            line: 84,
            column: 12
          },
          end: {
            line: 84,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 12
          },
          end: {
            line: 84,
            column: 16
          }
        }, {
          start: {
            line: 84,
            column: 20
          },
          end: {
            line: 84,
            column: 60
          }
        }],
        line: 84
      },
      "13": {
        loc: {
          start: {
            line: 87,
            column: 8
          },
          end: {
            line: 89,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 87,
            column: 8
          },
          end: {
            line: 89,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 87
      },
      "14": {
        loc: {
          start: {
            line: 87,
            column: 12
          },
          end: {
            line: 87,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 87,
            column: 12
          },
          end: {
            line: 87,
            column: 16
          }
        }, {
          start: {
            line: 87,
            column: 20
          },
          end: {
            line: 87,
            column: 60
          }
        }],
        line: 87
      },
      "15": {
        loc: {
          start: {
            line: 93,
            column: 8
          },
          end: {
            line: 98,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 8
          },
          end: {
            line: 98,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 93
      },
      "16": {
        loc: {
          start: {
            line: 95,
            column: 10
          },
          end: {
            line: 97,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 95,
            column: 10
          },
          end: {
            line: 97,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 95
      },
      "17": {
        loc: {
          start: {
            line: 102,
            column: 8
          },
          end: {
            line: 104,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 102,
            column: 8
          },
          end: {
            line: 104,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 102
      },
      "18": {
        loc: {
          start: {
            line: 102,
            column: 12
          },
          end: {
            line: 102,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 102,
            column: 12
          },
          end: {
            line: 102,
            column: 16
          }
        }, {
          start: {
            line: 102,
            column: 20
          },
          end: {
            line: 102,
            column: 52
          }
        }],
        line: 102
      },
      "19": {
        loc: {
          start: {
            line: 108,
            column: 8
          },
          end: {
            line: 110,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 8
          },
          end: {
            line: 110,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 108
      },
      "20": {
        loc: {
          start: {
            line: 115,
            column: 4
          },
          end: {
            line: 117,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 115,
            column: 4
          },
          end: {
            line: 117,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 115
      },
      "21": {
        loc: {
          start: {
            line: 132,
            column: 4
          },
          end: {
            line: 143,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 132,
            column: 4
          },
          end: {
            line: 143,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 132
      },
      "22": {
        loc: {
          start: {
            line: 133,
            column: 6
          },
          end: {
            line: 142,
            column: 7
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 134,
            column: 8
          },
          end: {
            line: 136,
            column: 16
          }
        }, {
          start: {
            line: 137,
            column: 8
          },
          end: {
            line: 139,
            column: 16
          }
        }, {
          start: {
            line: 140,
            column: 8
          },
          end: {
            line: 141,
            column: 51
          }
        }],
        line: 133
      },
      "23": {
        loc: {
          start: {
            line: 168,
            column: 4
          },
          end: {
            line: 183,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 168,
            column: 4
          },
          end: {
            line: 183,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 168
      },
      "24": {
        loc: {
          start: {
            line: 168,
            column: 8
          },
          end: {
            line: 168,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 168,
            column: 8
          },
          end: {
            line: 168,
            column: 37
          }
        }, {
          start: {
            line: 168,
            column: 41
          },
          end: {
            line: 168,
            column: 46
          }
        }],
        line: 168
      },
      "25": {
        loc: {
          start: {
            line: 185,
            column: 4
          },
          end: {
            line: 194,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 185,
            column: 4
          },
          end: {
            line: 194,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 185
      },
      "26": {
        loc: {
          start: {
            line: 185,
            column: 8
          },
          end: {
            line: 185,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 185,
            column: 8
          },
          end: {
            line: 185,
            column: 32
          }
        }, {
          start: {
            line: 185,
            column: 36
          },
          end: {
            line: 185,
            column: 43
          }
        }],
        line: 185
      },
      "27": {
        loc: {
          start: {
            line: 205,
            column: 4
          },
          end: {
            line: 207,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 205,
            column: 4
          },
          end: {
            line: 207,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 205
      },
      "28": {
        loc: {
          start: {
            line: 209,
            column: 4
          },
          end: {
            line: 211,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 209,
            column: 4
          },
          end: {
            line: 211,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 209
      },
      "29": {
        loc: {
          start: {
            line: 209,
            column: 8
          },
          end: {
            line: 209,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 209,
            column: 8
          },
          end: {
            line: 209,
            column: 13
          }
        }, {
          start: {
            line: 209,
            column: 17
          },
          end: {
            line: 209,
            column: 25
          }
        }],
        line: 209
      },
      "30": {
        loc: {
          start: {
            line: 213,
            column: 4
          },
          end: {
            line: 215,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 4
          },
          end: {
            line: 215,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 213
      },
      "31": {
        loc: {
          start: {
            line: 222,
            column: 7
          },
          end: {
            line: 224,
            column: 7
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 222,
            column: 7
          },
          end: {
            line: 222,
            column: 12
          }
        }, {
          start: {
            line: 223,
            column: 8
          },
          end: {
            line: 223,
            column: 49
          }
        }],
        line: 222
      },
      "32": {
        loc: {
          start: {
            line: 234,
            column: 27
          },
          end: {
            line: 234,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 234,
            column: 27
          },
          end: {
            line: 234,
            column: 56
          }
        }, {
          start: {
            line: 234,
            column: 60
          },
          end: {
            line: 234,
            column: 78
          }
        }],
        line: 234
      },
      "33": {
        loc: {
          start: {
            line: 235,
            column: 26
          },
          end: {
            line: 235,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 235,
            column: 55
          },
          end: {
            line: 235,
            column: 61
          }
        }, {
          start: {
            line: 235,
            column: 64
          },
          end: {
            line: 235,
            column: 75
          }
        }],
        line: 235
      },
      "34": {
        loc: {
          start: {
            line: 236,
            column: 23
          },
          end: {
            line: 236,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 236,
            column: 23
          },
          end: {
            line: 236,
            column: 49
          }
        }, {
          start: {
            line: 236,
            column: 53
          },
          end: {
            line: 236,
            column: 82
          }
        }],
        line: 236
      },
      "35": {
        loc: {
          start: {
            line: 237,
            column: 24
          },
          end: {
            line: 237,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 237,
            column: 53
          },
          end: {
            line: 237,
            column: 68
          }
        }, {
          start: {
            line: 237,
            column: 71
          },
          end: {
            line: 237,
            column: 80
          }
        }],
        line: 237
      },
      "36": {
        loc: {
          start: {
            line: 242,
            column: 9
          },
          end: {
            line: 254,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 242,
            column: 9
          },
          end: {
            line: 242,
            column: 27
          }
        }, {
          start: {
            line: 242,
            column: 31
          },
          end: {
            line: 242,
            column: 60
          }
        }, {
          start: {
            line: 243,
            column: 10
          },
          end: {
            line: 253,
            column: 29
          }
        }],
        line: 242
      },
      "37": {
        loc: {
          start: {
            line: 246,
            column: 32
          },
          end: {
            line: 246,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 246,
            column: 52
          },
          end: {
            line: 246,
            column: 67
          }
        }, {
          start: {
            line: 246,
            column: 70
          },
          end: {
            line: 246,
            column: 85
          }
        }],
        line: 246
      },
      "38": {
        loc: {
          start: {
            line: 248,
            column: 13
          },
          end: {
            line: 252,
            column: 13
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 249,
            column: 14
          },
          end: {
            line: 249,
            column: 54
          }
        }, {
          start: {
            line: 251,
            column: 14
          },
          end: {
            line: 251,
            column: 51
          }
        }],
        line: 248
      },
      "39": {
        loc: {
          start: {
            line: 260,
            column: 7
          },
          end: {
            line: 267,
            column: 7
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 260,
            column: 8
          },
          end: {
            line: 260,
            column: 13
          }
        }, {
          start: {
            line: 260,
            column: 18
          },
          end: {
            line: 260,
            column: 26
          }
        }, {
          start: {
            line: 260,
            column: 30
          },
          end: {
            line: 260,
            column: 57
          }
        }, {
          start: {
            line: 261,
            column: 8
          },
          end: {
            line: 266,
            column: 15
          }
        }],
        line: 260
      },
      "40": {
        loc: {
          start: {
            line: 264,
            column: 13
          },
          end: {
            line: 264,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 264,
            column: 13
          },
          end: {
            line: 264,
            column: 18
          }
        }, {
          start: {
            line: 264,
            column: 22
          },
          end: {
            line: 264,
            column: 41
          }
        }],
        line: 264
      },
      "41": {
        loc: {
          start: {
            line: 270,
            column: 7
          },
          end: {
            line: 272,
            column: 7
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 270,
            column: 7
          },
          end: {
            line: 270,
            column: 17
          }
        }, {
          start: {
            line: 270,
            column: 21
          },
          end: {
            line: 270,
            column: 27
          }
        }, {
          start: {
            line: 270,
            column: 31
          },
          end: {
            line: 270,
            column: 38
          }
        }, {
          start: {
            line: 271,
            column: 8
          },
          end: {
            line: 271,
            column: 59
          }
        }],
        line: 270
      },
      "42": {
        loc: {
          start: {
            line: 275,
            column: 7
          },
          end: {
            line: 279,
            column: 7
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 275,
            column: 7
          },
          end: {
            line: 275,
            column: 16
          }
        }, {
          start: {
            line: 275,
            column: 20
          },
          end: {
            line: 275,
            column: 50
          }
        }, {
          start: {
            line: 276,
            column: 8
          },
          end: {
            line: 278,
            column: 15
          }
        }],
        line: 275
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0],
      "4": [0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0, 0, 0],
      "40": [0, 0],
      "41": [0, 0, 0, 0],
      "42": [0, 0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "65dce0a58f81952b7270e0f4953b3afc09d76381"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_24eydchrob = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_24eydchrob();
import React, { useState, useCallback, useRef } from 'react';
import { TextInput, View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Eye, EyeOff, Shield, AlertTriangle } from 'lucide-react-native';
import { ValidationUtils } from "../../utils/validation";
import { useSecurity } from "../../hooks/useSecurity";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_24eydchrob().s[0]++, {
  primary: '#23ba16',
  error: '#ef4444',
  warning: '#f59e0b',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  dark: '#111827',
  white: '#ffffff'
});
export default function SecureTextInput(_ref) {
  var label = _ref.label,
    error = _ref.error,
    helperText = _ref.helperText,
    _ref$validationType = _ref.validationType,
    validationType = _ref$validationType === void 0 ? (cov_24eydchrob().b[0][0]++, 'text') : _ref$validationType,
    _ref$showPasswordTogg = _ref.showPasswordToggle,
    showPasswordToggle = _ref$showPasswordTogg === void 0 ? (cov_24eydchrob().b[1][0]++, false) : _ref$showPasswordTogg,
    _ref$enableValidation = _ref.enableValidation,
    enableValidation = _ref$enableValidation === void 0 ? (cov_24eydchrob().b[2][0]++, true) : _ref$enableValidation,
    _ref$enableSanitizati = _ref.enableSanitization,
    enableSanitization = _ref$enableSanitizati === void 0 ? (cov_24eydchrob().b[3][0]++, true) : _ref$enableSanitizati,
    _ref$maxLength = _ref.maxLength,
    maxLength = _ref$maxLength === void 0 ? (cov_24eydchrob().b[4][0]++, 1000) : _ref$maxLength,
    onChangeText = _ref.onChangeText,
    onValidationChange = _ref.onValidationChange,
    _ref$securityLevel = _ref.securityLevel,
    securityLevel = _ref$securityLevel === void 0 ? (cov_24eydchrob().b[5][0]++, 'medium') : _ref$securityLevel,
    style = _ref.style,
    props = _objectWithoutProperties(_ref, _excluded);
  cov_24eydchrob().f[0]++;
  var _ref2 = (cov_24eydchrob().s[1]++, useSecurity()),
    validateInput = _ref2.validateInput;
  var _ref3 = (cov_24eydchrob().s[2]++, useState('')),
    _ref4 = _slicedToArray(_ref3, 2),
    value = _ref4[0],
    setValue = _ref4[1];
  var _ref5 = (cov_24eydchrob().s[3]++, useState(false)),
    _ref6 = _slicedToArray(_ref5, 2),
    isPasswordVisible = _ref6[0],
    setIsPasswordVisible = _ref6[1];
  var _ref7 = (cov_24eydchrob().s[4]++, useState([])),
    _ref8 = _slicedToArray(_ref7, 2),
    validationErrors = _ref8[0],
    setValidationErrors = _ref8[1];
  var _ref9 = (cov_24eydchrob().s[5]++, useState(true)),
    _ref0 = _slicedToArray(_ref9, 2),
    isValid = _ref0[0],
    setIsValid = _ref0[1];
  var _ref1 = (cov_24eydchrob().s[6]++, useState(false)),
    _ref10 = _slicedToArray(_ref1, 2),
    isFocused = _ref10[0],
    setIsFocused = _ref10[1];
  var inputRef = (cov_24eydchrob().s[7]++, useRef(null));
  var validateValue = (cov_24eydchrob().s[8]++, useCallback(function (text) {
    cov_24eydchrob().f[1]++;
    var errors = (cov_24eydchrob().s[9]++, []);
    cov_24eydchrob().s[10]++;
    if (!enableValidation) {
      cov_24eydchrob().b[6][0]++;
      cov_24eydchrob().s[11]++;
      return {
        isValid: true,
        errors: []
      };
    } else {
      cov_24eydchrob().b[6][1]++;
    }
    cov_24eydchrob().s[12]++;
    if (!validateInput(text, validationType === 'email' ? (cov_24eydchrob().b[8][0]++, 'email') : (cov_24eydchrob().b[8][1]++, validationType === 'url' ? (cov_24eydchrob().b[9][0]++, 'url') : (cov_24eydchrob().b[9][1]++, 'text')))) {
      cov_24eydchrob().b[7][0]++;
      cov_24eydchrob().s[13]++;
      errors.push('Input contains potentially unsafe content');
    } else {
      cov_24eydchrob().b[7][1]++;
    }
    cov_24eydchrob().s[14]++;
    switch (validationType) {
      case 'email':
        cov_24eydchrob().b[10][0]++;
        cov_24eydchrob().s[15]++;
        if ((cov_24eydchrob().b[12][0]++, text) && (cov_24eydchrob().b[12][1]++, !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(text))) {
          cov_24eydchrob().b[11][0]++;
          cov_24eydchrob().s[16]++;
          errors.push('Invalid email format');
        } else {
          cov_24eydchrob().b[11][1]++;
        }
        cov_24eydchrob().s[17]++;
        if ((cov_24eydchrob().b[14][0]++, text) && (cov_24eydchrob().b[14][1]++, !ValidationUtils.isEmailDomainSafe(text))) {
          cov_24eydchrob().b[13][0]++;
          cov_24eydchrob().s[18]++;
          errors.push('Email domain not allowed');
        } else {
          cov_24eydchrob().b[13][1]++;
        }
        cov_24eydchrob().s[19]++;
        break;
      case 'password':
        cov_24eydchrob().b[10][1]++;
        cov_24eydchrob().s[20]++;
        if (text) {
          cov_24eydchrob().b[15][0]++;
          var strength = (cov_24eydchrob().s[21]++, ValidationUtils.getPasswordStrength(text));
          cov_24eydchrob().s[22]++;
          if (strength.score < 4) {
            cov_24eydchrob().b[16][0]++;
            cov_24eydchrob().s[23]++;
            errors.push.apply(errors, _toConsumableArray(strength.feedback));
          } else {
            cov_24eydchrob().b[16][1]++;
          }
        } else {
          cov_24eydchrob().b[15][1]++;
        }
        cov_24eydchrob().s[24]++;
        break;
      case 'url':
        cov_24eydchrob().b[10][2]++;
        cov_24eydchrob().s[25]++;
        if ((cov_24eydchrob().b[18][0]++, text) && (cov_24eydchrob().b[18][1]++, !ValidationUtils.isUrlSafe(text))) {
          cov_24eydchrob().b[17][0]++;
          cov_24eydchrob().s[26]++;
          errors.push('URL is not safe');
        } else {
          cov_24eydchrob().b[17][1]++;
        }
        cov_24eydchrob().s[27]++;
        break;
      case 'text':
        cov_24eydchrob().b[10][3]++;
        cov_24eydchrob().s[28]++;
        if (ValidationUtils.hasInjectionPatterns(text)) {
          cov_24eydchrob().b[19][0]++;
          cov_24eydchrob().s[29]++;
          errors.push('Text contains potentially harmful content');
        } else {
          cov_24eydchrob().b[19][1]++;
        }
        cov_24eydchrob().s[30]++;
        break;
    }
    cov_24eydchrob().s[31]++;
    if (text.length > maxLength) {
      cov_24eydchrob().b[20][0]++;
      cov_24eydchrob().s[32]++;
      errors.push(`Text must be less than ${maxLength} characters`);
    } else {
      cov_24eydchrob().b[20][1]++;
    }
    cov_24eydchrob().s[33]++;
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }, [enableValidation, validateInput, validationType, maxLength]));
  var handleTextChange = (cov_24eydchrob().s[34]++, useCallback(function (text) {
    cov_24eydchrob().f[2]++;
    var processedText = (cov_24eydchrob().s[35]++, text);
    cov_24eydchrob().s[36]++;
    if (enableSanitization) {
      cov_24eydchrob().b[21][0]++;
      cov_24eydchrob().s[37]++;
      switch (validationType) {
        case 'email':
          cov_24eydchrob().b[22][0]++;
          cov_24eydchrob().s[38]++;
          processedText = text.toLowerCase().trim();
          cov_24eydchrob().s[39]++;
          break;
        case 'text':
          cov_24eydchrob().b[22][1]++;
          cov_24eydchrob().s[40]++;
          processedText = ValidationUtils.sanitizeText(text, maxLength);
          cov_24eydchrob().s[41]++;
          break;
        default:
          cov_24eydchrob().b[22][2]++;
          cov_24eydchrob().s[42]++;
          processedText = text.slice(0, maxLength);
      }
    } else {
      cov_24eydchrob().b[21][1]++;
    }
    cov_24eydchrob().s[43]++;
    setValue(processedText);
    var validation = (cov_24eydchrob().s[44]++, validateValue(processedText));
    cov_24eydchrob().s[45]++;
    setValidationErrors(validation.errors);
    cov_24eydchrob().s[46]++;
    setIsValid(validation.isValid);
    cov_24eydchrob().s[47]++;
    onChangeText == null || onChangeText(processedText);
    cov_24eydchrob().s[48]++;
    onValidationChange == null || onValidationChange(validation.isValid, validation.errors);
  }, [enableSanitization, validationType, maxLength, validateValue, onChangeText, onValidationChange]));
  var togglePasswordVisibility = (cov_24eydchrob().s[49]++, useCallback(function () {
    cov_24eydchrob().f[3]++;
    cov_24eydchrob().s[50]++;
    setIsPasswordVisible(function (prev) {
      cov_24eydchrob().f[4]++;
      cov_24eydchrob().s[51]++;
      return !prev;
    });
  }, []));
  cov_24eydchrob().s[52]++;
  var getSecurityIndicator = function getSecurityIndicator() {
    cov_24eydchrob().f[5]++;
    cov_24eydchrob().s[53]++;
    if ((cov_24eydchrob().b[24][0]++, validationType === 'password') && (cov_24eydchrob().b[24][1]++, value)) {
      cov_24eydchrob().b[23][0]++;
      var strength = (cov_24eydchrob().s[54]++, ValidationUtils.getPasswordStrength(value));
      var strengthColors = (cov_24eydchrob().s[55]++, ['#ef4444', '#f59e0b', '#10b981']);
      var strengthLabels = (cov_24eydchrob().s[56]++, ['Weak', 'Medium', 'Strong']);
      var colorIndex = (cov_24eydchrob().s[57]++, Math.min(Math.floor(strength.score / 3), 2));
      cov_24eydchrob().s[58]++;
      return _jsxs(View, {
        style: styles.securityIndicator,
        children: [_jsx(Shield, {
          size: 16,
          color: strengthColors[colorIndex]
        }), _jsx(Text, {
          style: [styles.securityText, {
            color: strengthColors[colorIndex]
          }],
          children: strengthLabels[colorIndex]
        })]
      });
    } else {
      cov_24eydchrob().b[23][1]++;
    }
    cov_24eydchrob().s[59]++;
    if ((cov_24eydchrob().b[26][0]++, securityLevel === 'high') && (cov_24eydchrob().b[26][1]++, isValid)) {
      cov_24eydchrob().b[25][0]++;
      cov_24eydchrob().s[60]++;
      return _jsxs(View, {
        style: styles.securityIndicator,
        children: [_jsx(Shield, {
          size: 16,
          color: colors.primary
        }), _jsx(Text, {
          style: [styles.securityText, {
            color: colors.primary
          }],
          children: "Secure"
        })]
      });
    } else {
      cov_24eydchrob().b[25][1]++;
    }
    cov_24eydchrob().s[61]++;
    return null;
  };
  cov_24eydchrob().s[62]++;
  var getInputStyle = function getInputStyle() {
    cov_24eydchrob().f[6]++;
    var baseStyle = (cov_24eydchrob().s[63]++, [styles.input]);
    cov_24eydchrob().s[64]++;
    if (isFocused) {
      cov_24eydchrob().b[27][0]++;
      cov_24eydchrob().s[65]++;
      baseStyle.push(styles.inputFocused);
    } else {
      cov_24eydchrob().b[27][1]++;
    }
    cov_24eydchrob().s[66]++;
    if ((cov_24eydchrob().b[29][0]++, error) || (cov_24eydchrob().b[29][1]++, !isValid)) {
      cov_24eydchrob().b[28][0]++;
      cov_24eydchrob().s[67]++;
      baseStyle.push(styles.inputError);
    } else {
      cov_24eydchrob().b[28][1]++;
    }
    cov_24eydchrob().s[68]++;
    if (style) {
      cov_24eydchrob().b[30][0]++;
      cov_24eydchrob().s[69]++;
      baseStyle.push(style);
    } else {
      cov_24eydchrob().b[30][1]++;
    }
    cov_24eydchrob().s[70]++;
    return baseStyle;
  };
  cov_24eydchrob().s[71]++;
  return _jsxs(View, {
    style: styles.container,
    children: [(cov_24eydchrob().b[31][0]++, label) && (cov_24eydchrob().b[31][1]++, _jsx(Text, {
      style: styles.label,
      children: label
    })), _jsxs(View, {
      style: styles.inputContainer,
      children: [_jsx(TextInput, Object.assign({
        ref: inputRef,
        style: getInputStyle(),
        value: value,
        onChangeText: handleTextChange,
        onFocus: function onFocus() {
          cov_24eydchrob().f[7]++;
          cov_24eydchrob().s[72]++;
          return setIsFocused(true);
        },
        onBlur: function onBlur() {
          cov_24eydchrob().f[8]++;
          cov_24eydchrob().s[73]++;
          return setIsFocused(false);
        },
        secureTextEntry: (cov_24eydchrob().b[32][0]++, validationType === 'password') && (cov_24eydchrob().b[32][1]++, !isPasswordVisible),
        autoCapitalize: validationType === 'email' ? (cov_24eydchrob().b[33][0]++, 'none') : (cov_24eydchrob().b[33][1]++, 'sentences'),
        autoCorrect: (cov_24eydchrob().b[34][0]++, validationType !== 'email') && (cov_24eydchrob().b[34][1]++, validationType !== 'password'),
        keyboardType: validationType === 'email' ? (cov_24eydchrob().b[35][0]++, 'email-address') : (cov_24eydchrob().b[35][1]++, 'default'),
        maxLength: maxLength
      }, props)), (cov_24eydchrob().b[36][0]++, showPasswordToggle) && (cov_24eydchrob().b[36][1]++, validationType === 'password') && (cov_24eydchrob().b[36][2]++, _jsx(TouchableOpacity, {
        style: styles.passwordToggle,
        onPress: togglePasswordVisibility,
        accessibilityLabel: isPasswordVisible ? (cov_24eydchrob().b[37][0]++, 'Hide password') : (cov_24eydchrob().b[37][1]++, 'Show password'),
        children: isPasswordVisible ? (cov_24eydchrob().b[38][0]++, _jsx(EyeOff, {
          size: 20,
          color: colors.gray
        })) : (cov_24eydchrob().b[38][1]++, _jsx(Eye, {
          size: 20,
          color: colors.gray
        }))
      })), getSecurityIndicator()]
    }), ((cov_24eydchrob().b[39][0]++, error) || (cov_24eydchrob().b[39][1]++, !isValid) && (cov_24eydchrob().b[39][2]++, validationErrors.length > 0)) && (cov_24eydchrob().b[39][3]++, _jsxs(View, {
      style: styles.errorContainer,
      children: [_jsx(AlertTriangle, {
        size: 16,
        color: colors.error
      }), _jsx(Text, {
        style: styles.errorText,
        children: (cov_24eydchrob().b[40][0]++, error) || (cov_24eydchrob().b[40][1]++, validationErrors[0])
      })]
    })), (cov_24eydchrob().b[41][0]++, helperText) && (cov_24eydchrob().b[41][1]++, !error) && (cov_24eydchrob().b[41][2]++, isValid) && (cov_24eydchrob().b[41][3]++, _jsx(Text, {
      style: styles.helperText,
      children: helperText
    })), (cov_24eydchrob().b[42][0]++, maxLength) && (cov_24eydchrob().b[42][1]++, value.length > maxLength * 0.8) && (cov_24eydchrob().b[42][2]++, _jsxs(Text, {
      style: styles.characterCount,
      children: [value.length, "/", maxLength]
    }))]
  });
}
var styles = (cov_24eydchrob().s[74]++, StyleSheet.create({
  container: {
    marginBottom: 16
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.dark,
    marginBottom: 8
  },
  inputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center'
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.dark,
    backgroundColor: colors.white
  },
  inputFocused: {
    borderColor: colors.primary,
    borderWidth: 2
  },
  inputError: {
    borderColor: colors.error,
    borderWidth: 2
  },
  passwordToggle: {
    position: 'absolute',
    right: 12,
    padding: 4
  },
  securityIndicator: {
    position: 'absolute',
    right: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4
  },
  securityText: {
    fontSize: 12,
    fontWeight: '500'
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 6
  },
  errorText: {
    fontSize: 14,
    color: colors.error,
    flex: 1
  },
  helperText: {
    fontSize: 14,
    color: colors.gray,
    marginTop: 4
  },
  characterCount: {
    fontSize: 12,
    color: colors.gray,
    textAlign: 'right',
    marginTop: 4
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VSZWYiLCJUZXh0SW5wdXQiLCJWaWV3IiwiVGV4dCIsIlN0eWxlU2hlZXQiLCJUb3VjaGFibGVPcGFjaXR5IiwiRXllIiwiRXllT2ZmIiwiU2hpZWxkIiwiQWxlcnRUcmlhbmdsZSIsIlZhbGlkYXRpb25VdGlscyIsInVzZVNlY3VyaXR5IiwianN4IiwiX2pzeCIsImpzeHMiLCJfanN4cyIsImNvbG9ycyIsImNvdl8yNGV5ZGNocm9iIiwicyIsInByaW1hcnkiLCJlcnJvciIsIndhcm5pbmciLCJncmF5IiwibGlnaHRHcmF5IiwiZGFyayIsIndoaXRlIiwiU2VjdXJlVGV4dElucHV0IiwiX3JlZiIsImxhYmVsIiwiaGVscGVyVGV4dCIsIl9yZWYkdmFsaWRhdGlvblR5cGUiLCJ2YWxpZGF0aW9uVHlwZSIsImIiLCJfcmVmJHNob3dQYXNzd29yZFRvZ2ciLCJzaG93UGFzc3dvcmRUb2dnbGUiLCJfcmVmJGVuYWJsZVZhbGlkYXRpb24iLCJlbmFibGVWYWxpZGF0aW9uIiwiX3JlZiRlbmFibGVTYW5pdGl6YXRpIiwiZW5hYmxlU2FuaXRpemF0aW9uIiwiX3JlZiRtYXhMZW5ndGgiLCJtYXhMZW5ndGgiLCJvbkNoYW5nZVRleHQiLCJvblZhbGlkYXRpb25DaGFuZ2UiLCJfcmVmJHNlY3VyaXR5TGV2ZWwiLCJzZWN1cml0eUxldmVsIiwic3R5bGUiLCJwcm9wcyIsIl9vYmplY3RXaXRob3V0UHJvcGVydGllcyIsIl9leGNsdWRlZCIsImYiLCJfcmVmMiIsInZhbGlkYXRlSW5wdXQiLCJfcmVmMyIsIl9yZWY0IiwiX3NsaWNlZFRvQXJyYXkiLCJ2YWx1ZSIsInNldFZhbHVlIiwiX3JlZjUiLCJfcmVmNiIsImlzUGFzc3dvcmRWaXNpYmxlIiwic2V0SXNQYXNzd29yZFZpc2libGUiLCJfcmVmNyIsIl9yZWY4IiwidmFsaWRhdGlvbkVycm9ycyIsInNldFZhbGlkYXRpb25FcnJvcnMiLCJfcmVmOSIsIl9yZWYwIiwiaXNWYWxpZCIsInNldElzVmFsaWQiLCJfcmVmMSIsIl9yZWYxMCIsImlzRm9jdXNlZCIsInNldElzRm9jdXNlZCIsImlucHV0UmVmIiwidmFsaWRhdGVWYWx1ZSIsInRleHQiLCJlcnJvcnMiLCJwdXNoIiwidGVzdCIsImlzRW1haWxEb21haW5TYWZlIiwic3RyZW5ndGgiLCJnZXRQYXNzd29yZFN0cmVuZ3RoIiwic2NvcmUiLCJhcHBseSIsIl90b0NvbnN1bWFibGVBcnJheSIsImZlZWRiYWNrIiwiaXNVcmxTYWZlIiwiaGFzSW5qZWN0aW9uUGF0dGVybnMiLCJsZW5ndGgiLCJoYW5kbGVUZXh0Q2hhbmdlIiwicHJvY2Vzc2VkVGV4dCIsInRvTG93ZXJDYXNlIiwidHJpbSIsInNhbml0aXplVGV4dCIsInNsaWNlIiwidmFsaWRhdGlvbiIsInRvZ2dsZVBhc3N3b3JkVmlzaWJpbGl0eSIsInByZXYiLCJnZXRTZWN1cml0eUluZGljYXRvciIsInN0cmVuZ3RoQ29sb3JzIiwic3RyZW5ndGhMYWJlbHMiLCJjb2xvckluZGV4IiwiTWF0aCIsIm1pbiIsImZsb29yIiwic3R5bGVzIiwic2VjdXJpdHlJbmRpY2F0b3IiLCJjaGlsZHJlbiIsInNpemUiLCJjb2xvciIsInNlY3VyaXR5VGV4dCIsImdldElucHV0U3R5bGUiLCJiYXNlU3R5bGUiLCJpbnB1dCIsImlucHV0Rm9jdXNlZCIsImlucHV0RXJyb3IiLCJjb250YWluZXIiLCJpbnB1dENvbnRhaW5lciIsIk9iamVjdCIsImFzc2lnbiIsInJlZiIsIm9uRm9jdXMiLCJvbkJsdXIiLCJzZWN1cmVUZXh0RW50cnkiLCJhdXRvQ2FwaXRhbGl6ZSIsImF1dG9Db3JyZWN0Iiwia2V5Ym9hcmRUeXBlIiwicGFzc3dvcmRUb2dnbGUiLCJvblByZXNzIiwiYWNjZXNzaWJpbGl0eUxhYmVsIiwiZXJyb3JDb250YWluZXIiLCJlcnJvclRleHQiLCJjaGFyYWN0ZXJDb3VudCIsImNyZWF0ZSIsIm1hcmdpbkJvdHRvbSIsImZvbnRTaXplIiwiZm9udFdlaWdodCIsInBvc2l0aW9uIiwiZmxleERpcmVjdGlvbiIsImFsaWduSXRlbXMiLCJmbGV4IiwiYm9yZGVyV2lkdGgiLCJib3JkZXJDb2xvciIsImJvcmRlclJhZGl1cyIsInBhZGRpbmdIb3Jpem9udGFsIiwicGFkZGluZ1ZlcnRpY2FsIiwiYmFja2dyb3VuZENvbG9yIiwicmlnaHQiLCJwYWRkaW5nIiwiZ2FwIiwibWFyZ2luVG9wIiwidGV4dEFsaWduIl0sInNvdXJjZXMiOlsiU2VjdXJlVGV4dElucHV0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUNhbGxiYWNrLCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQge1xuICBUZXh0SW5wdXQsXG4gIFZpZXcsXG4gIFRleHQsXG4gIFN0eWxlU2hlZXQsXG4gIFRvdWNoYWJsZU9wYWNpdHksXG4gIFRleHRJbnB1dFByb3BzLFxufSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuaW1wb3J0IHsgRXllLCBFeWVPZmYsIFNoaWVsZCwgQWxlcnRUcmlhbmdsZSB9IGZyb20gJ2x1Y2lkZS1yZWFjdC1uYXRpdmUnO1xuaW1wb3J0IHsgVmFsaWRhdGlvblV0aWxzIH0gZnJvbSAnQC91dGlscy92YWxpZGF0aW9uJztcbmltcG9ydCB7IHVzZVNlY3VyaXR5IH0gZnJvbSAnQC9ob29rcy91c2VTZWN1cml0eSc7XG5cbi8qKlxuICogU2VjdXJlIFRleHQgSW5wdXQgQ29tcG9uZW50XG4gKiBQcm92aWRlcyBlbmhhbmNlZCBzZWN1cml0eSBmZWF0dXJlcyBmb3IgdGV4dCBpbnB1dFxuICovXG5cbmludGVyZmFjZSBTZWN1cmVUZXh0SW5wdXRQcm9wcyBleHRlbmRzIE9taXQ8VGV4dElucHV0UHJvcHMsICdvbkNoYW5nZVRleHQnPiB7XG4gIGxhYmVsPzogc3RyaW5nO1xuICBlcnJvcj86IHN0cmluZztcbiAgaGVscGVyVGV4dD86IHN0cmluZztcbiAgdmFsaWRhdGlvblR5cGU/OiAndGV4dCcgfCAnZW1haWwnIHwgJ3Bhc3N3b3JkJyB8ICd1cmwnO1xuICBzaG93UGFzc3dvcmRUb2dnbGU/OiBib29sZWFuO1xuICBlbmFibGVWYWxpZGF0aW9uPzogYm9vbGVhbjtcbiAgZW5hYmxlU2FuaXRpemF0aW9uPzogYm9vbGVhbjtcbiAgbWF4TGVuZ3RoPzogbnVtYmVyO1xuICBvbkNoYW5nZVRleHQ/OiAodGV4dDogc3RyaW5nKSA9PiB2b2lkO1xuICBvblZhbGlkYXRpb25DaGFuZ2U/OiAoaXNWYWxpZDogYm9vbGVhbiwgZXJyb3JzOiBzdHJpbmdbXSkgPT4gdm9pZDtcbiAgc2VjdXJpdHlMZXZlbD86ICdsb3cnIHwgJ21lZGl1bScgfCAnaGlnaCc7XG59XG5cbmNvbnN0IGNvbG9ycyA9IHtcbiAgcHJpbWFyeTogJyMyM2JhMTYnLFxuICBlcnJvcjogJyNlZjQ0NDQnLFxuICB3YXJuaW5nOiAnI2Y1OWUwYicsXG4gIGdyYXk6ICcjNmI3MjgwJyxcbiAgbGlnaHRHcmF5OiAnI2YzZjRmNicsXG4gIGRhcms6ICcjMTExODI3JyxcbiAgd2hpdGU6ICcjZmZmZmZmJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNlY3VyZVRleHRJbnB1dCh7XG4gIGxhYmVsLFxuICBlcnJvcixcbiAgaGVscGVyVGV4dCxcbiAgdmFsaWRhdGlvblR5cGUgPSAndGV4dCcsXG4gIHNob3dQYXNzd29yZFRvZ2dsZSA9IGZhbHNlLFxuICBlbmFibGVWYWxpZGF0aW9uID0gdHJ1ZSxcbiAgZW5hYmxlU2FuaXRpemF0aW9uID0gdHJ1ZSxcbiAgbWF4TGVuZ3RoID0gMTAwMCxcbiAgb25DaGFuZ2VUZXh0LFxuICBvblZhbGlkYXRpb25DaGFuZ2UsXG4gIHNlY3VyaXR5TGV2ZWwgPSAnbWVkaXVtJyxcbiAgc3R5bGUsXG4gIC4uLnByb3BzXG59OiBTZWN1cmVUZXh0SW5wdXRQcm9wcykge1xuICBjb25zdCB7IHZhbGlkYXRlSW5wdXQgfSA9IHVzZVNlY3VyaXR5KCk7XG4gIGNvbnN0IFt2YWx1ZSwgc2V0VmFsdWVdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbaXNQYXNzd29yZFZpc2libGUsIHNldElzUGFzc3dvcmRWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3ZhbGlkYXRpb25FcnJvcnMsIHNldFZhbGlkYXRpb25FcnJvcnNdID0gdXNlU3RhdGU8c3RyaW5nW10+KFtdKTtcbiAgY29uc3QgW2lzVmFsaWQsIHNldElzVmFsaWRdID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtpc0ZvY3VzZWQsIHNldElzRm9jdXNlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IGlucHV0UmVmID0gdXNlUmVmPFRleHRJbnB1dD4obnVsbCk7XG5cbiAgLyoqXG4gICAqIFZhbGlkYXRlIGlucHV0IGJhc2VkIG9uIHR5cGVcbiAgICovXG4gIGNvbnN0IHZhbGlkYXRlVmFsdWUgPSB1c2VDYWxsYmFjaygodGV4dDogc3RyaW5nKTogeyBpc1ZhbGlkOiBib29sZWFuOyBlcnJvcnM6IHN0cmluZ1tdIH0gPT4ge1xuICAgIGNvbnN0IGVycm9yczogc3RyaW5nW10gPSBbXTtcblxuICAgIGlmICghZW5hYmxlVmFsaWRhdGlvbikge1xuICAgICAgcmV0dXJuIHsgaXNWYWxpZDogdHJ1ZSwgZXJyb3JzOiBbXSB9O1xuICAgIH1cblxuICAgIC8vIEJhc2ljIHNlY3VyaXR5IHZhbGlkYXRpb25cbiAgICBpZiAoIXZhbGlkYXRlSW5wdXQodGV4dCwgdmFsaWRhdGlvblR5cGUgPT09ICdlbWFpbCcgPyAnZW1haWwnIDogdmFsaWRhdGlvblR5cGUgPT09ICd1cmwnID8gJ3VybCcgOiAndGV4dCcpKSB7XG4gICAgICBlcnJvcnMucHVzaCgnSW5wdXQgY29udGFpbnMgcG90ZW50aWFsbHkgdW5zYWZlIGNvbnRlbnQnKTtcbiAgICB9XG5cbiAgICAvLyBUeXBlLXNwZWNpZmljIHZhbGlkYXRpb25cbiAgICBzd2l0Y2ggKHZhbGlkYXRpb25UeXBlKSB7XG4gICAgICBjYXNlICdlbWFpbCc6XG4gICAgICAgIGlmICh0ZXh0ICYmICEvXlteXFxzQF0rQFteXFxzQF0rXFwuW15cXHNAXSskLy50ZXN0KHRleHQpKSB7XG4gICAgICAgICAgZXJyb3JzLnB1c2goJ0ludmFsaWQgZW1haWwgZm9ybWF0Jyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRleHQgJiYgIVZhbGlkYXRpb25VdGlscy5pc0VtYWlsRG9tYWluU2FmZSh0ZXh0KSkge1xuICAgICAgICAgIGVycm9ycy5wdXNoKCdFbWFpbCBkb21haW4gbm90IGFsbG93ZWQnKTtcbiAgICAgICAgfVxuICAgICAgICBicmVhaztcblxuICAgICAgY2FzZSAncGFzc3dvcmQnOlxuICAgICAgICBpZiAodGV4dCkge1xuICAgICAgICAgIGNvbnN0IHN0cmVuZ3RoID0gVmFsaWRhdGlvblV0aWxzLmdldFBhc3N3b3JkU3RyZW5ndGgodGV4dCk7XG4gICAgICAgICAgaWYgKHN0cmVuZ3RoLnNjb3JlIDwgNCkge1xuICAgICAgICAgICAgZXJyb3JzLnB1c2goLi4uc3RyZW5ndGguZmVlZGJhY2spO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBicmVhaztcblxuICAgICAgY2FzZSAndXJsJzpcbiAgICAgICAgaWYgKHRleHQgJiYgIVZhbGlkYXRpb25VdGlscy5pc1VybFNhZmUodGV4dCkpIHtcbiAgICAgICAgICBlcnJvcnMucHVzaCgnVVJMIGlzIG5vdCBzYWZlJyk7XG4gICAgICAgIH1cbiAgICAgICAgYnJlYWs7XG5cbiAgICAgIGNhc2UgJ3RleHQnOlxuICAgICAgICBpZiAoVmFsaWRhdGlvblV0aWxzLmhhc0luamVjdGlvblBhdHRlcm5zKHRleHQpKSB7XG4gICAgICAgICAgZXJyb3JzLnB1c2goJ1RleHQgY29udGFpbnMgcG90ZW50aWFsbHkgaGFybWZ1bCBjb250ZW50Jyk7XG4gICAgICAgIH1cbiAgICAgICAgYnJlYWs7XG4gICAgfVxuXG4gICAgLy8gTGVuZ3RoIHZhbGlkYXRpb25cbiAgICBpZiAodGV4dC5sZW5ndGggPiBtYXhMZW5ndGgpIHtcbiAgICAgIGVycm9ycy5wdXNoKGBUZXh0IG11c3QgYmUgbGVzcyB0aGFuICR7bWF4TGVuZ3RofSBjaGFyYWN0ZXJzYCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIGlzVmFsaWQ6IGVycm9ycy5sZW5ndGggPT09IDAsXG4gICAgICBlcnJvcnMsXG4gICAgfTtcbiAgfSwgW2VuYWJsZVZhbGlkYXRpb24sIHZhbGlkYXRlSW5wdXQsIHZhbGlkYXRpb25UeXBlLCBtYXhMZW5ndGhdKTtcblxuICAvKipcbiAgICogSGFuZGxlIHRleHQgY2hhbmdlIHdpdGggdmFsaWRhdGlvbiBhbmQgc2FuaXRpemF0aW9uXG4gICAqL1xuICBjb25zdCBoYW5kbGVUZXh0Q2hhbmdlID0gdXNlQ2FsbGJhY2soKHRleHQ6IHN0cmluZykgPT4ge1xuICAgIGxldCBwcm9jZXNzZWRUZXh0ID0gdGV4dDtcblxuICAgIC8vIFNhbml0aXplIGlucHV0IGlmIGVuYWJsZWRcbiAgICBpZiAoZW5hYmxlU2FuaXRpemF0aW9uKSB7XG4gICAgICBzd2l0Y2ggKHZhbGlkYXRpb25UeXBlKSB7XG4gICAgICAgIGNhc2UgJ2VtYWlsJzpcbiAgICAgICAgICBwcm9jZXNzZWRUZXh0ID0gdGV4dC50b0xvd2VyQ2FzZSgpLnRyaW0oKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAndGV4dCc6XG4gICAgICAgICAgcHJvY2Vzc2VkVGV4dCA9IFZhbGlkYXRpb25VdGlscy5zYW5pdGl6ZVRleHQodGV4dCwgbWF4TGVuZ3RoKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICBwcm9jZXNzZWRUZXh0ID0gdGV4dC5zbGljZSgwLCBtYXhMZW5ndGgpO1xuICAgICAgfVxuICAgIH1cblxuICAgIHNldFZhbHVlKHByb2Nlc3NlZFRleHQpO1xuXG4gICAgLy8gVmFsaWRhdGUgaW5wdXRcbiAgICBjb25zdCB2YWxpZGF0aW9uID0gdmFsaWRhdGVWYWx1ZShwcm9jZXNzZWRUZXh0KTtcbiAgICBzZXRWYWxpZGF0aW9uRXJyb3JzKHZhbGlkYXRpb24uZXJyb3JzKTtcbiAgICBzZXRJc1ZhbGlkKHZhbGlkYXRpb24uaXNWYWxpZCk7XG5cbiAgICAvLyBDYWxsIGNhbGxiYWNrc1xuICAgIG9uQ2hhbmdlVGV4dD8uKHByb2Nlc3NlZFRleHQpO1xuICAgIG9uVmFsaWRhdGlvbkNoYW5nZT8uKHZhbGlkYXRpb24uaXNWYWxpZCwgdmFsaWRhdGlvbi5lcnJvcnMpO1xuICB9LCBbZW5hYmxlU2FuaXRpemF0aW9uLCB2YWxpZGF0aW9uVHlwZSwgbWF4TGVuZ3RoLCB2YWxpZGF0ZVZhbHVlLCBvbkNoYW5nZVRleHQsIG9uVmFsaWRhdGlvbkNoYW5nZV0pO1xuXG4gIC8qKlxuICAgKiBUb2dnbGUgcGFzc3dvcmQgdmlzaWJpbGl0eVxuICAgKi9cbiAgY29uc3QgdG9nZ2xlUGFzc3dvcmRWaXNpYmlsaXR5ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHNldElzUGFzc3dvcmRWaXNpYmxlKHByZXYgPT4gIXByZXYpO1xuICB9LCBbXSk7XG5cbiAgLyoqXG4gICAqIEdldCBzZWN1cml0eSBpbmRpY2F0b3JcbiAgICovXG4gIGNvbnN0IGdldFNlY3VyaXR5SW5kaWNhdG9yID0gKCkgPT4ge1xuICAgIGlmICh2YWxpZGF0aW9uVHlwZSA9PT0gJ3Bhc3N3b3JkJyAmJiB2YWx1ZSkge1xuICAgICAgY29uc3Qgc3RyZW5ndGggPSBWYWxpZGF0aW9uVXRpbHMuZ2V0UGFzc3dvcmRTdHJlbmd0aCh2YWx1ZSk7XG4gICAgICBjb25zdCBzdHJlbmd0aENvbG9ycyA9IFsnI2VmNDQ0NCcsICcjZjU5ZTBiJywgJyMxMGI5ODEnXTtcbiAgICAgIGNvbnN0IHN0cmVuZ3RoTGFiZWxzID0gWydXZWFrJywgJ01lZGl1bScsICdTdHJvbmcnXTtcbiAgICAgIFxuICAgICAgY29uc3QgY29sb3JJbmRleCA9IE1hdGgubWluKE1hdGguZmxvb3Ioc3RyZW5ndGguc2NvcmUgLyAzKSwgMik7XG4gICAgICBcbiAgICAgIHJldHVybiAoXG4gICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuc2VjdXJpdHlJbmRpY2F0b3J9PlxuICAgICAgICAgIDxTaGllbGQgc2l6ZT17MTZ9IGNvbG9yPXtzdHJlbmd0aENvbG9yc1tjb2xvckluZGV4XX0gLz5cbiAgICAgICAgICA8VGV4dCBzdHlsZT17W3N0eWxlcy5zZWN1cml0eVRleHQsIHsgY29sb3I6IHN0cmVuZ3RoQ29sb3JzW2NvbG9ySW5kZXhdIH1dfT5cbiAgICAgICAgICAgIHtzdHJlbmd0aExhYmVsc1tjb2xvckluZGV4XX1cbiAgICAgICAgICA8L1RleHQ+XG4gICAgICAgIDwvVmlldz5cbiAgICAgICk7XG4gICAgfVxuXG4gICAgaWYgKHNlY3VyaXR5TGV2ZWwgPT09ICdoaWdoJyAmJiBpc1ZhbGlkKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8VmlldyBzdHlsZT17c3R5bGVzLnNlY3VyaXR5SW5kaWNhdG9yfT5cbiAgICAgICAgICA8U2hpZWxkIHNpemU9ezE2fSBjb2xvcj17Y29sb3JzLnByaW1hcnl9IC8+XG4gICAgICAgICAgPFRleHQgc3R5bGU9e1tzdHlsZXMuc2VjdXJpdHlUZXh0LCB7IGNvbG9yOiBjb2xvcnMucHJpbWFyeSB9XX0+XG4gICAgICAgICAgICBTZWN1cmVcbiAgICAgICAgICA8L1RleHQ+XG4gICAgICAgIDwvVmlldz5cbiAgICAgICk7XG4gICAgfVxuXG4gICAgcmV0dXJuIG51bGw7XG4gIH07XG5cbiAgLyoqXG4gICAqIEdldCBpbnB1dCBzdHlsZSBiYXNlZCBvbiBzdGF0ZVxuICAgKi9cbiAgY29uc3QgZ2V0SW5wdXRTdHlsZSA9ICgpID0+IHtcbiAgICBjb25zdCBiYXNlU3R5bGUgPSBbc3R5bGVzLmlucHV0XTtcbiAgICBcbiAgICBpZiAoaXNGb2N1c2VkKSB7XG4gICAgICBiYXNlU3R5bGUucHVzaChzdHlsZXMuaW5wdXRGb2N1c2VkIGFzIGFueSk7XG4gICAgfVxuICAgIFxuICAgIGlmIChlcnJvciB8fCAhaXNWYWxpZCkge1xuICAgICAgYmFzZVN0eWxlLnB1c2goc3R5bGVzLmlucHV0RXJyb3IgYXMgYW55KTtcbiAgICB9XG4gICAgXG4gICAgaWYgKHN0eWxlKSB7XG4gICAgICBiYXNlU3R5bGUucHVzaChzdHlsZSBhcyBhbnkpO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gYmFzZVN0eWxlO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPFZpZXcgc3R5bGU9e3N0eWxlcy5jb250YWluZXJ9PlxuICAgICAge2xhYmVsICYmIChcbiAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5sYWJlbH0+e2xhYmVsfTwvVGV4dD5cbiAgICAgICl9XG4gICAgICBcbiAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuaW5wdXRDb250YWluZXJ9PlxuICAgICAgICA8VGV4dElucHV0XG4gICAgICAgICAgcmVmPXtpbnB1dFJlZn1cbiAgICAgICAgICBzdHlsZT17Z2V0SW5wdXRTdHlsZSgpfVxuICAgICAgICAgIHZhbHVlPXt2YWx1ZX1cbiAgICAgICAgICBvbkNoYW5nZVRleHQ9e2hhbmRsZVRleHRDaGFuZ2V9XG4gICAgICAgICAgb25Gb2N1cz17KCkgPT4gc2V0SXNGb2N1c2VkKHRydWUpfVxuICAgICAgICAgIG9uQmx1cj17KCkgPT4gc2V0SXNGb2N1c2VkKGZhbHNlKX1cbiAgICAgICAgICBzZWN1cmVUZXh0RW50cnk9e3ZhbGlkYXRpb25UeXBlID09PSAncGFzc3dvcmQnICYmICFpc1Bhc3N3b3JkVmlzaWJsZX1cbiAgICAgICAgICBhdXRvQ2FwaXRhbGl6ZT17dmFsaWRhdGlvblR5cGUgPT09ICdlbWFpbCcgPyAnbm9uZScgOiAnc2VudGVuY2VzJ31cbiAgICAgICAgICBhdXRvQ29ycmVjdD17dmFsaWRhdGlvblR5cGUgIT09ICdlbWFpbCcgJiYgdmFsaWRhdGlvblR5cGUgIT09ICdwYXNzd29yZCd9XG4gICAgICAgICAga2V5Ym9hcmRUeXBlPXt2YWxpZGF0aW9uVHlwZSA9PT0gJ2VtYWlsJyA/ICdlbWFpbC1hZGRyZXNzJyA6ICdkZWZhdWx0J31cbiAgICAgICAgICBtYXhMZW5ndGg9e21heExlbmd0aH1cbiAgICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAgIC8+XG4gICAgICAgIFxuICAgICAgICB7c2hvd1Bhc3N3b3JkVG9nZ2xlICYmIHZhbGlkYXRpb25UeXBlID09PSAncGFzc3dvcmQnICYmIChcbiAgICAgICAgICA8VG91Y2hhYmxlT3BhY2l0eVxuICAgICAgICAgICAgc3R5bGU9e3N0eWxlcy5wYXNzd29yZFRvZ2dsZX1cbiAgICAgICAgICAgIG9uUHJlc3M9e3RvZ2dsZVBhc3N3b3JkVmlzaWJpbGl0eX1cbiAgICAgICAgICAgIGFjY2Vzc2liaWxpdHlMYWJlbD17aXNQYXNzd29yZFZpc2libGUgPyAnSGlkZSBwYXNzd29yZCcgOiAnU2hvdyBwYXNzd29yZCd9XG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzUGFzc3dvcmRWaXNpYmxlID8gKFxuICAgICAgICAgICAgICA8RXllT2ZmIHNpemU9ezIwfSBjb2xvcj17Y29sb3JzLmdyYXl9IC8+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8RXllIHNpemU9ezIwfSBjb2xvcj17Y29sb3JzLmdyYXl9IC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvVG91Y2hhYmxlT3BhY2l0eT5cbiAgICAgICAgKX1cbiAgICAgICAgXG4gICAgICAgIHtnZXRTZWN1cml0eUluZGljYXRvcigpfVxuICAgICAgPC9WaWV3PlxuICAgICAgXG4gICAgICB7LyogRXJyb3IgbWVzc2FnZXMgKi99XG4gICAgICB7KGVycm9yIHx8ICghaXNWYWxpZCAmJiB2YWxpZGF0aW9uRXJyb3JzLmxlbmd0aCA+IDApKSAmJiAoXG4gICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuZXJyb3JDb250YWluZXJ9PlxuICAgICAgICAgIDxBbGVydFRyaWFuZ2xlIHNpemU9ezE2fSBjb2xvcj17Y29sb3JzLmVycm9yfSAvPlxuICAgICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMuZXJyb3JUZXh0fT5cbiAgICAgICAgICAgIHtlcnJvciB8fCB2YWxpZGF0aW9uRXJyb3JzWzBdfVxuICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgPC9WaWV3PlxuICAgICAgKX1cbiAgICAgIFxuICAgICAgey8qIEhlbHBlciB0ZXh0ICovfVxuICAgICAge2hlbHBlclRleHQgJiYgIWVycm9yICYmIGlzVmFsaWQgJiYgKFxuICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLmhlbHBlclRleHR9PntoZWxwZXJUZXh0fTwvVGV4dD5cbiAgICAgICl9XG4gICAgICBcbiAgICAgIHsvKiBDaGFyYWN0ZXIgY291bnQgKi99XG4gICAgICB7bWF4TGVuZ3RoICYmIHZhbHVlLmxlbmd0aCA+IG1heExlbmd0aCAqIDAuOCAmJiAoXG4gICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMuY2hhcmFjdGVyQ291bnR9PlxuICAgICAgICAgIHt2YWx1ZS5sZW5ndGh9L3ttYXhMZW5ndGh9XG4gICAgICAgIDwvVGV4dD5cbiAgICAgICl9XG4gICAgPC9WaWV3PlxuICApO1xufVxuXG5jb25zdCBzdHlsZXMgPSBTdHlsZVNoZWV0LmNyZWF0ZSh7XG4gIGNvbnRhaW5lcjoge1xuICAgIG1hcmdpbkJvdHRvbTogMTYsXG4gIH0sXG4gIGxhYmVsOiB7XG4gICAgZm9udFNpemU6IDE2LFxuICAgIGZvbnRXZWlnaHQ6ICc2MDAnLFxuICAgIGNvbG9yOiBjb2xvcnMuZGFyayxcbiAgICBtYXJnaW5Cb3R0b206IDgsXG4gIH0sXG4gIGlucHV0Q29udGFpbmVyOiB7XG4gICAgcG9zaXRpb246ICdyZWxhdGl2ZScsXG4gICAgZmxleERpcmVjdGlvbjogJ3JvdycsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gIH0sXG4gIGlucHV0OiB7XG4gICAgZmxleDogMSxcbiAgICBib3JkZXJXaWR0aDogMSxcbiAgICBib3JkZXJDb2xvcjogY29sb3JzLmxpZ2h0R3JheSxcbiAgICBib3JkZXJSYWRpdXM6IDgsXG4gICAgcGFkZGluZ0hvcml6b250YWw6IDE2LFxuICAgIHBhZGRpbmdWZXJ0aWNhbDogMTIsXG4gICAgZm9udFNpemU6IDE2LFxuICAgIGNvbG9yOiBjb2xvcnMuZGFyayxcbiAgICBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9ycy53aGl0ZSxcbiAgfSxcbiAgaW5wdXRGb2N1c2VkOiB7XG4gICAgYm9yZGVyQ29sb3I6IGNvbG9ycy5wcmltYXJ5LFxuICAgIGJvcmRlcldpZHRoOiAyLFxuICB9LFxuICBpbnB1dEVycm9yOiB7XG4gICAgYm9yZGVyQ29sb3I6IGNvbG9ycy5lcnJvcixcbiAgICBib3JkZXJXaWR0aDogMixcbiAgfSxcbiAgcGFzc3dvcmRUb2dnbGU6IHtcbiAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICByaWdodDogMTIsXG4gICAgcGFkZGluZzogNCxcbiAgfSxcbiAgc2VjdXJpdHlJbmRpY2F0b3I6IHtcbiAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICByaWdodDogMTIsXG4gICAgZmxleERpcmVjdGlvbjogJ3JvdycsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgZ2FwOiA0LFxuICB9LFxuICBzZWN1cml0eVRleHQ6IHtcbiAgICBmb250U2l6ZTogMTIsXG4gICAgZm9udFdlaWdodDogJzUwMCcsXG4gIH0sXG4gIGVycm9yQ29udGFpbmVyOiB7XG4gICAgZmxleERpcmVjdGlvbjogJ3JvdycsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgbWFyZ2luVG9wOiA4LFxuICAgIGdhcDogNixcbiAgfSxcbiAgZXJyb3JUZXh0OiB7XG4gICAgZm9udFNpemU6IDE0LFxuICAgIGNvbG9yOiBjb2xvcnMuZXJyb3IsXG4gICAgZmxleDogMSxcbiAgfSxcbiAgaGVscGVyVGV4dDoge1xuICAgIGZvbnRTaXplOiAxNCxcbiAgICBjb2xvcjogY29sb3JzLmdyYXksXG4gICAgbWFyZ2luVG9wOiA0LFxuICB9LFxuICBjaGFyYWN0ZXJDb3VudDoge1xuICAgIGZvbnRTaXplOiAxMixcbiAgICBjb2xvcjogY29sb3JzLmdyYXksXG4gICAgdGV4dEFsaWduOiAncmlnaHQnLFxuICAgIG1hcmdpblRvcDogNCxcbiAgfSxcbn0pO1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsT0FBT0EsS0FBSyxJQUFJQyxRQUFRLEVBQUVDLFdBQVcsRUFBRUMsTUFBTSxRQUFRLE9BQU87QUFDNUQsU0FDRUMsU0FBUyxFQUNUQyxJQUFJLEVBQ0pDLElBQUksRUFDSkMsVUFBVSxFQUNWQyxnQkFBZ0IsUUFFWCxjQUFjO0FBQ3JCLFNBQVNDLEdBQUcsRUFBRUMsTUFBTSxFQUFFQyxNQUFNLEVBQUVDLGFBQWEsUUFBUSxxQkFBcUI7QUFDeEUsU0FBU0MsZUFBZTtBQUN4QixTQUFTQyxXQUFXO0FBQThCLFNBQUFDLEdBQUEsSUFBQUMsSUFBQSxFQUFBQyxJQUFBLElBQUFDLEtBQUE7QUFxQmxELElBQU1DLE1BQU0sSUFBQUMsY0FBQSxHQUFBQyxDQUFBLE9BQUc7RUFDYkMsT0FBTyxFQUFFLFNBQVM7RUFDbEJDLEtBQUssRUFBRSxTQUFTO0VBQ2hCQyxPQUFPLEVBQUUsU0FBUztFQUNsQkMsSUFBSSxFQUFFLFNBQVM7RUFDZkMsU0FBUyxFQUFFLFNBQVM7RUFDcEJDLElBQUksRUFBRSxTQUFTO0VBQ2ZDLEtBQUssRUFBRTtBQUNULENBQUM7QUFFRCxlQUFlLFNBQVNDLGVBQWVBLENBQUFDLElBQUEsRUFjZDtFQUFBLElBYnZCQyxLQUFLLEdBQUFELElBQUEsQ0FBTEMsS0FBSztJQUNMUixLQUFLLEdBQUFPLElBQUEsQ0FBTFAsS0FBSztJQUNMUyxVQUFVLEdBQUFGLElBQUEsQ0FBVkUsVUFBVTtJQUFBQyxtQkFBQSxHQUFBSCxJQUFBLENBQ1ZJLGNBQWM7SUFBZEEsY0FBYyxHQUFBRCxtQkFBQSxlQUFBYixjQUFBLEdBQUFlLENBQUEsVUFBRyxNQUFNLElBQUFGLG1CQUFBO0lBQUFHLHFCQUFBLEdBQUFOLElBQUEsQ0FDdkJPLGtCQUFrQjtJQUFsQkEsa0JBQWtCLEdBQUFELHFCQUFBLGVBQUFoQixjQUFBLEdBQUFlLENBQUEsVUFBRyxLQUFLLElBQUFDLHFCQUFBO0lBQUFFLHFCQUFBLEdBQUFSLElBQUEsQ0FDMUJTLGdCQUFnQjtJQUFoQkEsZ0JBQWdCLEdBQUFELHFCQUFBLGVBQUFsQixjQUFBLEdBQUFlLENBQUEsVUFBRyxJQUFJLElBQUFHLHFCQUFBO0lBQUFFLHFCQUFBLEdBQUFWLElBQUEsQ0FDdkJXLGtCQUFrQjtJQUFsQkEsa0JBQWtCLEdBQUFELHFCQUFBLGVBQUFwQixjQUFBLEdBQUFlLENBQUEsVUFBRyxJQUFJLElBQUFLLHFCQUFBO0lBQUFFLGNBQUEsR0FBQVosSUFBQSxDQUN6QmEsU0FBUztJQUFUQSxTQUFTLEdBQUFELGNBQUEsZUFBQXRCLGNBQUEsR0FBQWUsQ0FBQSxVQUFHLElBQUksSUFBQU8sY0FBQTtJQUNoQkUsWUFBWSxHQUFBZCxJQUFBLENBQVpjLFlBQVk7SUFDWkMsa0JBQWtCLEdBQUFmLElBQUEsQ0FBbEJlLGtCQUFrQjtJQUFBQyxrQkFBQSxHQUFBaEIsSUFBQSxDQUNsQmlCLGFBQWE7SUFBYkEsYUFBYSxHQUFBRCxrQkFBQSxlQUFBMUIsY0FBQSxHQUFBZSxDQUFBLFVBQUcsUUFBUSxJQUFBVyxrQkFBQTtJQUN4QkUsS0FBSyxHQUFBbEIsSUFBQSxDQUFMa0IsS0FBSztJQUNGQyxLQUFLLEdBQUFDLHdCQUFBLENBQUFwQixJQUFBLEVBQUFxQixTQUFBO0VBQUEvQixjQUFBLEdBQUFnQyxDQUFBO0VBRVIsSUFBQUMsS0FBQSxJQUFBakMsY0FBQSxHQUFBQyxDQUFBLE9BQTBCUCxXQUFXLENBQUMsQ0FBQztJQUEvQndDLGFBQWEsR0FBQUQsS0FBQSxDQUFiQyxhQUFhO0VBQ3JCLElBQUFDLEtBQUEsSUFBQW5DLGNBQUEsR0FBQUMsQ0FBQSxPQUEwQnBCLFFBQVEsQ0FBQyxFQUFFLENBQUM7SUFBQXVELEtBQUEsR0FBQUMsY0FBQSxDQUFBRixLQUFBO0lBQS9CRyxLQUFLLEdBQUFGLEtBQUE7SUFBRUcsUUFBUSxHQUFBSCxLQUFBO0VBQ3RCLElBQUFJLEtBQUEsSUFBQXhDLGNBQUEsR0FBQUMsQ0FBQSxPQUFrRHBCLFFBQVEsQ0FBQyxLQUFLLENBQUM7SUFBQTRELEtBQUEsR0FBQUosY0FBQSxDQUFBRyxLQUFBO0lBQTFERSxpQkFBaUIsR0FBQUQsS0FBQTtJQUFFRSxvQkFBb0IsR0FBQUYsS0FBQTtFQUM5QyxJQUFBRyxLQUFBLElBQUE1QyxjQUFBLEdBQUFDLENBQUEsT0FBZ0RwQixRQUFRLENBQVcsRUFBRSxDQUFDO0lBQUFnRSxLQUFBLEdBQUFSLGNBQUEsQ0FBQU8sS0FBQTtJQUEvREUsZ0JBQWdCLEdBQUFELEtBQUE7SUFBRUUsbUJBQW1CLEdBQUFGLEtBQUE7RUFDNUMsSUFBQUcsS0FBQSxJQUFBaEQsY0FBQSxHQUFBQyxDQUFBLE9BQThCcEIsUUFBUSxDQUFDLElBQUksQ0FBQztJQUFBb0UsS0FBQSxHQUFBWixjQUFBLENBQUFXLEtBQUE7SUFBckNFLE9BQU8sR0FBQUQsS0FBQTtJQUFFRSxVQUFVLEdBQUFGLEtBQUE7RUFDMUIsSUFBQUcsS0FBQSxJQUFBcEQsY0FBQSxHQUFBQyxDQUFBLE9BQWtDcEIsUUFBUSxDQUFDLEtBQUssQ0FBQztJQUFBd0UsTUFBQSxHQUFBaEIsY0FBQSxDQUFBZSxLQUFBO0lBQTFDRSxTQUFTLEdBQUFELE1BQUE7SUFBRUUsWUFBWSxHQUFBRixNQUFBO0VBQzlCLElBQU1HLFFBQVEsSUFBQXhELGNBQUEsR0FBQUMsQ0FBQSxPQUFHbEIsTUFBTSxDQUFZLElBQUksQ0FBQztFQUt4QyxJQUFNMEUsYUFBYSxJQUFBekQsY0FBQSxHQUFBQyxDQUFBLE9BQUduQixXQUFXLENBQUMsVUFBQzRFLElBQVksRUFBNkM7SUFBQTFELGNBQUEsR0FBQWdDLENBQUE7SUFDMUYsSUFBTTJCLE1BQWdCLElBQUEzRCxjQUFBLEdBQUFDLENBQUEsT0FBRyxFQUFFO0lBQUNELGNBQUEsR0FBQUMsQ0FBQTtJQUU1QixJQUFJLENBQUNrQixnQkFBZ0IsRUFBRTtNQUFBbkIsY0FBQSxHQUFBZSxDQUFBO01BQUFmLGNBQUEsR0FBQUMsQ0FBQTtNQUNyQixPQUFPO1FBQUVpRCxPQUFPLEVBQUUsSUFBSTtRQUFFUyxNQUFNLEVBQUU7TUFBRyxDQUFDO0lBQ3RDLENBQUM7TUFBQTNELGNBQUEsR0FBQWUsQ0FBQTtJQUFBO0lBQUFmLGNBQUEsR0FBQUMsQ0FBQTtJQUdELElBQUksQ0FBQ2lDLGFBQWEsQ0FBQ3dCLElBQUksRUFBRTVDLGNBQWMsS0FBSyxPQUFPLElBQUFkLGNBQUEsR0FBQWUsQ0FBQSxVQUFHLE9BQU8sS0FBQWYsY0FBQSxHQUFBZSxDQUFBLFVBQUdELGNBQWMsS0FBSyxLQUFLLElBQUFkLGNBQUEsR0FBQWUsQ0FBQSxVQUFHLEtBQUssS0FBQWYsY0FBQSxHQUFBZSxDQUFBLFVBQUcsTUFBTSxHQUFDLEVBQUU7TUFBQWYsY0FBQSxHQUFBZSxDQUFBO01BQUFmLGNBQUEsR0FBQUMsQ0FBQTtNQUMxRzBELE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLDJDQUEyQyxDQUFDO0lBQzFELENBQUM7TUFBQTVELGNBQUEsR0FBQWUsQ0FBQTtJQUFBO0lBQUFmLGNBQUEsR0FBQUMsQ0FBQTtJQUdELFFBQVFhLGNBQWM7TUFDcEIsS0FBSyxPQUFPO1FBQUFkLGNBQUEsR0FBQWUsQ0FBQTtRQUFBZixjQUFBLEdBQUFDLENBQUE7UUFDVixJQUFJLENBQUFELGNBQUEsR0FBQWUsQ0FBQSxXQUFBMkMsSUFBSSxNQUFBMUQsY0FBQSxHQUFBZSxDQUFBLFdBQUksQ0FBQyw0QkFBNEIsQ0FBQzhDLElBQUksQ0FBQ0gsSUFBSSxDQUFDLEdBQUU7VUFBQTFELGNBQUEsR0FBQWUsQ0FBQTtVQUFBZixjQUFBLEdBQUFDLENBQUE7VUFDcEQwRCxNQUFNLENBQUNDLElBQUksQ0FBQyxzQkFBc0IsQ0FBQztRQUNyQyxDQUFDO1VBQUE1RCxjQUFBLEdBQUFlLENBQUE7UUFBQTtRQUFBZixjQUFBLEdBQUFDLENBQUE7UUFDRCxJQUFJLENBQUFELGNBQUEsR0FBQWUsQ0FBQSxXQUFBMkMsSUFBSSxNQUFBMUQsY0FBQSxHQUFBZSxDQUFBLFdBQUksQ0FBQ3RCLGVBQWUsQ0FBQ3FFLGlCQUFpQixDQUFDSixJQUFJLENBQUMsR0FBRTtVQUFBMUQsY0FBQSxHQUFBZSxDQUFBO1VBQUFmLGNBQUEsR0FBQUMsQ0FBQTtVQUNwRDBELE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLDBCQUEwQixDQUFDO1FBQ3pDLENBQUM7VUFBQTVELGNBQUEsR0FBQWUsQ0FBQTtRQUFBO1FBQUFmLGNBQUEsR0FBQUMsQ0FBQTtRQUNEO01BRUYsS0FBSyxVQUFVO1FBQUFELGNBQUEsR0FBQWUsQ0FBQTtRQUFBZixjQUFBLEdBQUFDLENBQUE7UUFDYixJQUFJeUQsSUFBSSxFQUFFO1VBQUExRCxjQUFBLEdBQUFlLENBQUE7VUFDUixJQUFNZ0QsUUFBUSxJQUFBL0QsY0FBQSxHQUFBQyxDQUFBLFFBQUdSLGVBQWUsQ0FBQ3VFLG1CQUFtQixDQUFDTixJQUFJLENBQUM7VUFBQzFELGNBQUEsR0FBQUMsQ0FBQTtVQUMzRCxJQUFJOEQsUUFBUSxDQUFDRSxLQUFLLEdBQUcsQ0FBQyxFQUFFO1lBQUFqRSxjQUFBLEdBQUFlLENBQUE7WUFBQWYsY0FBQSxHQUFBQyxDQUFBO1lBQ3RCMEQsTUFBTSxDQUFDQyxJQUFJLENBQUFNLEtBQUEsQ0FBWFAsTUFBTSxFQUFBUSxrQkFBQSxDQUFTSixRQUFRLENBQUNLLFFBQVEsRUFBQztVQUNuQyxDQUFDO1lBQUFwRSxjQUFBLEdBQUFlLENBQUE7VUFBQTtRQUNILENBQUM7VUFBQWYsY0FBQSxHQUFBZSxDQUFBO1FBQUE7UUFBQWYsY0FBQSxHQUFBQyxDQUFBO1FBQ0Q7TUFFRixLQUFLLEtBQUs7UUFBQUQsY0FBQSxHQUFBZSxDQUFBO1FBQUFmLGNBQUEsR0FBQUMsQ0FBQTtRQUNSLElBQUksQ0FBQUQsY0FBQSxHQUFBZSxDQUFBLFdBQUEyQyxJQUFJLE1BQUExRCxjQUFBLEdBQUFlLENBQUEsV0FBSSxDQUFDdEIsZUFBZSxDQUFDNEUsU0FBUyxDQUFDWCxJQUFJLENBQUMsR0FBRTtVQUFBMUQsY0FBQSxHQUFBZSxDQUFBO1VBQUFmLGNBQUEsR0FBQUMsQ0FBQTtVQUM1QzBELE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLGlCQUFpQixDQUFDO1FBQ2hDLENBQUM7VUFBQTVELGNBQUEsR0FBQWUsQ0FBQTtRQUFBO1FBQUFmLGNBQUEsR0FBQUMsQ0FBQTtRQUNEO01BRUYsS0FBSyxNQUFNO1FBQUFELGNBQUEsR0FBQWUsQ0FBQTtRQUFBZixjQUFBLEdBQUFDLENBQUE7UUFDVCxJQUFJUixlQUFlLENBQUM2RSxvQkFBb0IsQ0FBQ1osSUFBSSxDQUFDLEVBQUU7VUFBQTFELGNBQUEsR0FBQWUsQ0FBQTtVQUFBZixjQUFBLEdBQUFDLENBQUE7VUFDOUMwRCxNQUFNLENBQUNDLElBQUksQ0FBQywyQ0FBMkMsQ0FBQztRQUMxRCxDQUFDO1VBQUE1RCxjQUFBLEdBQUFlLENBQUE7UUFBQTtRQUFBZixjQUFBLEdBQUFDLENBQUE7UUFDRDtJQUNKO0lBQUNELGNBQUEsR0FBQUMsQ0FBQTtJQUdELElBQUl5RCxJQUFJLENBQUNhLE1BQU0sR0FBR2hELFNBQVMsRUFBRTtNQUFBdkIsY0FBQSxHQUFBZSxDQUFBO01BQUFmLGNBQUEsR0FBQUMsQ0FBQTtNQUMzQjBELE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLDBCQUEwQnJDLFNBQVMsYUFBYSxDQUFDO0lBQy9ELENBQUM7TUFBQXZCLGNBQUEsR0FBQWUsQ0FBQTtJQUFBO0lBQUFmLGNBQUEsR0FBQUMsQ0FBQTtJQUVELE9BQU87TUFDTGlELE9BQU8sRUFBRVMsTUFBTSxDQUFDWSxNQUFNLEtBQUssQ0FBQztNQUM1QlosTUFBTSxFQUFOQTtJQUNGLENBQUM7RUFDSCxDQUFDLEVBQUUsQ0FBQ3hDLGdCQUFnQixFQUFFZSxhQUFhLEVBQUVwQixjQUFjLEVBQUVTLFNBQVMsQ0FBQyxDQUFDO0VBS2hFLElBQU1pRCxnQkFBZ0IsSUFBQXhFLGNBQUEsR0FBQUMsQ0FBQSxRQUFHbkIsV0FBVyxDQUFDLFVBQUM0RSxJQUFZLEVBQUs7SUFBQTFELGNBQUEsR0FBQWdDLENBQUE7SUFDckQsSUFBSXlDLGFBQWEsSUFBQXpFLGNBQUEsR0FBQUMsQ0FBQSxRQUFHeUQsSUFBSTtJQUFDMUQsY0FBQSxHQUFBQyxDQUFBO0lBR3pCLElBQUlvQixrQkFBa0IsRUFBRTtNQUFBckIsY0FBQSxHQUFBZSxDQUFBO01BQUFmLGNBQUEsR0FBQUMsQ0FBQTtNQUN0QixRQUFRYSxjQUFjO1FBQ3BCLEtBQUssT0FBTztVQUFBZCxjQUFBLEdBQUFlLENBQUE7VUFBQWYsY0FBQSxHQUFBQyxDQUFBO1VBQ1Z3RSxhQUFhLEdBQUdmLElBQUksQ0FBQ2dCLFdBQVcsQ0FBQyxDQUFDLENBQUNDLElBQUksQ0FBQyxDQUFDO1VBQUMzRSxjQUFBLEdBQUFDLENBQUE7VUFDMUM7UUFDRixLQUFLLE1BQU07VUFBQUQsY0FBQSxHQUFBZSxDQUFBO1VBQUFmLGNBQUEsR0FBQUMsQ0FBQTtVQUNUd0UsYUFBYSxHQUFHaEYsZUFBZSxDQUFDbUYsWUFBWSxDQUFDbEIsSUFBSSxFQUFFbkMsU0FBUyxDQUFDO1VBQUN2QixjQUFBLEdBQUFDLENBQUE7VUFDOUQ7UUFDRjtVQUFBRCxjQUFBLEdBQUFlLENBQUE7VUFBQWYsY0FBQSxHQUFBQyxDQUFBO1VBQ0V3RSxhQUFhLEdBQUdmLElBQUksQ0FBQ21CLEtBQUssQ0FBQyxDQUFDLEVBQUV0RCxTQUFTLENBQUM7TUFDNUM7SUFDRixDQUFDO01BQUF2QixjQUFBLEdBQUFlLENBQUE7SUFBQTtJQUFBZixjQUFBLEdBQUFDLENBQUE7SUFFRHNDLFFBQVEsQ0FBQ2tDLGFBQWEsQ0FBQztJQUd2QixJQUFNSyxVQUFVLElBQUE5RSxjQUFBLEdBQUFDLENBQUEsUUFBR3dELGFBQWEsQ0FBQ2dCLGFBQWEsQ0FBQztJQUFDekUsY0FBQSxHQUFBQyxDQUFBO0lBQ2hEOEMsbUJBQW1CLENBQUMrQixVQUFVLENBQUNuQixNQUFNLENBQUM7SUFBQzNELGNBQUEsR0FBQUMsQ0FBQTtJQUN2Q2tELFVBQVUsQ0FBQzJCLFVBQVUsQ0FBQzVCLE9BQU8sQ0FBQztJQUFDbEQsY0FBQSxHQUFBQyxDQUFBO0lBRy9CdUIsWUFBWSxZQUFaQSxZQUFZLENBQUdpRCxhQUFhLENBQUM7SUFBQ3pFLGNBQUEsR0FBQUMsQ0FBQTtJQUM5QndCLGtCQUFrQixZQUFsQkEsa0JBQWtCLENBQUdxRCxVQUFVLENBQUM1QixPQUFPLEVBQUU0QixVQUFVLENBQUNuQixNQUFNLENBQUM7RUFDN0QsQ0FBQyxFQUFFLENBQUN0QyxrQkFBa0IsRUFBRVAsY0FBYyxFQUFFUyxTQUFTLEVBQUVrQyxhQUFhLEVBQUVqQyxZQUFZLEVBQUVDLGtCQUFrQixDQUFDLENBQUM7RUFLcEcsSUFBTXNELHdCQUF3QixJQUFBL0UsY0FBQSxHQUFBQyxDQUFBLFFBQUduQixXQUFXLENBQUMsWUFBTTtJQUFBa0IsY0FBQSxHQUFBZ0MsQ0FBQTtJQUFBaEMsY0FBQSxHQUFBQyxDQUFBO0lBQ2pEMEMsb0JBQW9CLENBQUMsVUFBQXFDLElBQUksRUFBSTtNQUFBaEYsY0FBQSxHQUFBZ0MsQ0FBQTtNQUFBaEMsY0FBQSxHQUFBQyxDQUFBO01BQUEsUUFBQytFLElBQUk7SUFBRCxDQUFDLENBQUM7RUFDckMsQ0FBQyxFQUFFLEVBQUUsQ0FBQztFQUFDaEYsY0FBQSxHQUFBQyxDQUFBO0VBS1AsSUFBTWdGLG9CQUFvQixHQUFHLFNBQXZCQSxvQkFBb0JBLENBQUEsRUFBUztJQUFBakYsY0FBQSxHQUFBZ0MsQ0FBQTtJQUFBaEMsY0FBQSxHQUFBQyxDQUFBO0lBQ2pDLElBQUksQ0FBQUQsY0FBQSxHQUFBZSxDQUFBLFdBQUFELGNBQWMsS0FBSyxVQUFVLE1BQUFkLGNBQUEsR0FBQWUsQ0FBQSxXQUFJdUIsS0FBSyxHQUFFO01BQUF0QyxjQUFBLEdBQUFlLENBQUE7TUFDMUMsSUFBTWdELFFBQVEsSUFBQS9ELGNBQUEsR0FBQUMsQ0FBQSxRQUFHUixlQUFlLENBQUN1RSxtQkFBbUIsQ0FBQzFCLEtBQUssQ0FBQztNQUMzRCxJQUFNNEMsY0FBYyxJQUFBbEYsY0FBQSxHQUFBQyxDQUFBLFFBQUcsQ0FBQyxTQUFTLEVBQUUsU0FBUyxFQUFFLFNBQVMsQ0FBQztNQUN4RCxJQUFNa0YsY0FBYyxJQUFBbkYsY0FBQSxHQUFBQyxDQUFBLFFBQUcsQ0FBQyxNQUFNLEVBQUUsUUFBUSxFQUFFLFFBQVEsQ0FBQztNQUVuRCxJQUFNbUYsVUFBVSxJQUFBcEYsY0FBQSxHQUFBQyxDQUFBLFFBQUdvRixJQUFJLENBQUNDLEdBQUcsQ0FBQ0QsSUFBSSxDQUFDRSxLQUFLLENBQUN4QixRQUFRLENBQUNFLEtBQUssR0FBRyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUM7TUFBQ2pFLGNBQUEsR0FBQUMsQ0FBQTtNQUUvRCxPQUNFSCxLQUFBLENBQUNiLElBQUk7UUFBQzJDLEtBQUssRUFBRTRELE1BQU0sQ0FBQ0MsaUJBQWtCO1FBQUFDLFFBQUEsR0FDcEM5RixJQUFBLENBQUNMLE1BQU07VUFBQ29HLElBQUksRUFBRSxFQUFHO1VBQUNDLEtBQUssRUFBRVYsY0FBYyxDQUFDRSxVQUFVO1FBQUUsQ0FBRSxDQUFDLEVBQ3ZEeEYsSUFBQSxDQUFDVixJQUFJO1VBQUMwQyxLQUFLLEVBQUUsQ0FBQzRELE1BQU0sQ0FBQ0ssWUFBWSxFQUFFO1lBQUVELEtBQUssRUFBRVYsY0FBYyxDQUFDRSxVQUFVO1VBQUUsQ0FBQyxDQUFFO1VBQUFNLFFBQUEsRUFDdkVQLGNBQWMsQ0FBQ0MsVUFBVTtRQUFDLENBQ3ZCLENBQUM7TUFBQSxDQUNILENBQUM7SUFFWCxDQUFDO01BQUFwRixjQUFBLEdBQUFlLENBQUE7SUFBQTtJQUFBZixjQUFBLEdBQUFDLENBQUE7SUFFRCxJQUFJLENBQUFELGNBQUEsR0FBQWUsQ0FBQSxXQUFBWSxhQUFhLEtBQUssTUFBTSxNQUFBM0IsY0FBQSxHQUFBZSxDQUFBLFdBQUltQyxPQUFPLEdBQUU7TUFBQWxELGNBQUEsR0FBQWUsQ0FBQTtNQUFBZixjQUFBLEdBQUFDLENBQUE7TUFDdkMsT0FDRUgsS0FBQSxDQUFDYixJQUFJO1FBQUMyQyxLQUFLLEVBQUU0RCxNQUFNLENBQUNDLGlCQUFrQjtRQUFBQyxRQUFBLEdBQ3BDOUYsSUFBQSxDQUFDTCxNQUFNO1VBQUNvRyxJQUFJLEVBQUUsRUFBRztVQUFDQyxLQUFLLEVBQUU3RixNQUFNLENBQUNHO1FBQVEsQ0FBRSxDQUFDLEVBQzNDTixJQUFBLENBQUNWLElBQUk7VUFBQzBDLEtBQUssRUFBRSxDQUFDNEQsTUFBTSxDQUFDSyxZQUFZLEVBQUU7WUFBRUQsS0FBSyxFQUFFN0YsTUFBTSxDQUFDRztVQUFRLENBQUMsQ0FBRTtVQUFBd0YsUUFBQSxFQUFDO1FBRS9ELENBQU0sQ0FBQztNQUFBLENBQ0gsQ0FBQztJQUVYLENBQUM7TUFBQTFGLGNBQUEsR0FBQWUsQ0FBQTtJQUFBO0lBQUFmLGNBQUEsR0FBQUMsQ0FBQTtJQUVELE9BQU8sSUFBSTtFQUNiLENBQUM7RUFBQ0QsY0FBQSxHQUFBQyxDQUFBO0VBS0YsSUFBTTZGLGFBQWEsR0FBRyxTQUFoQkEsYUFBYUEsQ0FBQSxFQUFTO0lBQUE5RixjQUFBLEdBQUFnQyxDQUFBO0lBQzFCLElBQU0rRCxTQUFTLElBQUEvRixjQUFBLEdBQUFDLENBQUEsUUFBRyxDQUFDdUYsTUFBTSxDQUFDUSxLQUFLLENBQUM7SUFBQ2hHLGNBQUEsR0FBQUMsQ0FBQTtJQUVqQyxJQUFJcUQsU0FBUyxFQUFFO01BQUF0RCxjQUFBLEdBQUFlLENBQUE7TUFBQWYsY0FBQSxHQUFBQyxDQUFBO01BQ2I4RixTQUFTLENBQUNuQyxJQUFJLENBQUM0QixNQUFNLENBQUNTLFlBQW1CLENBQUM7SUFDNUMsQ0FBQztNQUFBakcsY0FBQSxHQUFBZSxDQUFBO0lBQUE7SUFBQWYsY0FBQSxHQUFBQyxDQUFBO0lBRUQsSUFBSSxDQUFBRCxjQUFBLEdBQUFlLENBQUEsV0FBQVosS0FBSyxNQUFBSCxjQUFBLEdBQUFlLENBQUEsV0FBSSxDQUFDbUMsT0FBTyxHQUFFO01BQUFsRCxjQUFBLEdBQUFlLENBQUE7TUFBQWYsY0FBQSxHQUFBQyxDQUFBO01BQ3JCOEYsU0FBUyxDQUFDbkMsSUFBSSxDQUFDNEIsTUFBTSxDQUFDVSxVQUFpQixDQUFDO0lBQzFDLENBQUM7TUFBQWxHLGNBQUEsR0FBQWUsQ0FBQTtJQUFBO0lBQUFmLGNBQUEsR0FBQUMsQ0FBQTtJQUVELElBQUkyQixLQUFLLEVBQUU7TUFBQTVCLGNBQUEsR0FBQWUsQ0FBQTtNQUFBZixjQUFBLEdBQUFDLENBQUE7TUFDVDhGLFNBQVMsQ0FBQ25DLElBQUksQ0FBQ2hDLEtBQVksQ0FBQztJQUM5QixDQUFDO01BQUE1QixjQUFBLEdBQUFlLENBQUE7SUFBQTtJQUFBZixjQUFBLEdBQUFDLENBQUE7SUFFRCxPQUFPOEYsU0FBUztFQUNsQixDQUFDO0VBQUMvRixjQUFBLEdBQUFDLENBQUE7RUFFRixPQUNFSCxLQUFBLENBQUNiLElBQUk7SUFBQzJDLEtBQUssRUFBRTRELE1BQU0sQ0FBQ1csU0FBVTtJQUFBVCxRQUFBLEdBQzNCLENBQUExRixjQUFBLEdBQUFlLENBQUEsV0FBQUosS0FBSyxNQUFBWCxjQUFBLEdBQUFlLENBQUEsV0FDSm5CLElBQUEsQ0FBQ1YsSUFBSTtNQUFDMEMsS0FBSyxFQUFFNEQsTUFBTSxDQUFDN0UsS0FBTTtNQUFBK0UsUUFBQSxFQUFFL0U7SUFBSyxDQUFPLENBQUMsQ0FDMUMsRUFFRGIsS0FBQSxDQUFDYixJQUFJO01BQUMyQyxLQUFLLEVBQUU0RCxNQUFNLENBQUNZLGNBQWU7TUFBQVYsUUFBQSxHQUNqQzlGLElBQUEsQ0FBQ1osU0FBUyxFQUFBcUgsTUFBQSxDQUFBQyxNQUFBO1FBQ1JDLEdBQUcsRUFBRS9DLFFBQVM7UUFDZDVCLEtBQUssRUFBRWtFLGFBQWEsQ0FBQyxDQUFFO1FBQ3ZCeEQsS0FBSyxFQUFFQSxLQUFNO1FBQ2JkLFlBQVksRUFBRWdELGdCQUFpQjtRQUMvQmdDLE9BQU8sRUFBRSxTQUFUQSxPQUFPQSxDQUFBLEVBQVE7VUFBQXhHLGNBQUEsR0FBQWdDLENBQUE7VUFBQWhDLGNBQUEsR0FBQUMsQ0FBQTtVQUFBLE9BQUFzRCxZQUFZLENBQUMsSUFBSSxDQUFDO1FBQUQsQ0FBRTtRQUNsQ2tELE1BQU0sRUFBRSxTQUFSQSxNQUFNQSxDQUFBLEVBQVE7VUFBQXpHLGNBQUEsR0FBQWdDLENBQUE7VUFBQWhDLGNBQUEsR0FBQUMsQ0FBQTtVQUFBLE9BQUFzRCxZQUFZLENBQUMsS0FBSyxDQUFDO1FBQUQsQ0FBRTtRQUNsQ21ELGVBQWUsRUFBRSxDQUFBMUcsY0FBQSxHQUFBZSxDQUFBLFdBQUFELGNBQWMsS0FBSyxVQUFVLE1BQUFkLGNBQUEsR0FBQWUsQ0FBQSxXQUFJLENBQUMyQixpQkFBaUIsQ0FBQztRQUNyRWlFLGNBQWMsRUFBRTdGLGNBQWMsS0FBSyxPQUFPLElBQUFkLGNBQUEsR0FBQWUsQ0FBQSxXQUFHLE1BQU0sS0FBQWYsY0FBQSxHQUFBZSxDQUFBLFdBQUcsV0FBVyxDQUFDO1FBQ2xFNkYsV0FBVyxFQUFFLENBQUE1RyxjQUFBLEdBQUFlLENBQUEsV0FBQUQsY0FBYyxLQUFLLE9BQU8sTUFBQWQsY0FBQSxHQUFBZSxDQUFBLFdBQUlELGNBQWMsS0FBSyxVQUFVLENBQUM7UUFDekUrRixZQUFZLEVBQUUvRixjQUFjLEtBQUssT0FBTyxJQUFBZCxjQUFBLEdBQUFlLENBQUEsV0FBRyxlQUFlLEtBQUFmLGNBQUEsR0FBQWUsQ0FBQSxXQUFHLFNBQVMsQ0FBQztRQUN2RVEsU0FBUyxFQUFFQTtNQUFVLEdBQ2pCTSxLQUFLLENBQ1YsQ0FBQyxFQUVELENBQUE3QixjQUFBLEdBQUFlLENBQUEsV0FBQUUsa0JBQWtCLE1BQUFqQixjQUFBLEdBQUFlLENBQUEsV0FBSUQsY0FBYyxLQUFLLFVBQVUsTUFBQWQsY0FBQSxHQUFBZSxDQUFBLFdBQ2xEbkIsSUFBQSxDQUFDUixnQkFBZ0I7UUFDZndDLEtBQUssRUFBRTRELE1BQU0sQ0FBQ3NCLGNBQWU7UUFDN0JDLE9BQU8sRUFBRWhDLHdCQUF5QjtRQUNsQ2lDLGtCQUFrQixFQUFFdEUsaUJBQWlCLElBQUExQyxjQUFBLEdBQUFlLENBQUEsV0FBRyxlQUFlLEtBQUFmLGNBQUEsR0FBQWUsQ0FBQSxXQUFHLGVBQWUsQ0FBQztRQUFBMkUsUUFBQSxFQUV6RWhELGlCQUFpQixJQUFBMUMsY0FBQSxHQUFBZSxDQUFBLFdBQ2hCbkIsSUFBQSxDQUFDTixNQUFNO1VBQUNxRyxJQUFJLEVBQUUsRUFBRztVQUFDQyxLQUFLLEVBQUU3RixNQUFNLENBQUNNO1FBQUssQ0FBRSxDQUFDLEtBQUFMLGNBQUEsR0FBQWUsQ0FBQSxXQUV4Q25CLElBQUEsQ0FBQ1AsR0FBRztVQUFDc0csSUFBSSxFQUFFLEVBQUc7VUFBQ0MsS0FBSyxFQUFFN0YsTUFBTSxDQUFDTTtRQUFLLENBQUUsQ0FBQztNQUN0QyxDQUNlLENBQUMsQ0FDcEIsRUFFQTRFLG9CQUFvQixDQUFDLENBQUM7SUFBQSxDQUNuQixDQUFDLEVBR04sQ0FBQyxDQUFBakYsY0FBQSxHQUFBZSxDQUFBLFdBQUFaLEtBQUssS0FBSyxDQUFBSCxjQUFBLEdBQUFlLENBQUEsWUFBQ21DLE9BQU8sTUFBQWxELGNBQUEsR0FBQWUsQ0FBQSxXQUFJK0IsZ0JBQWdCLENBQUN5QixNQUFNLEdBQUcsQ0FBQyxDQUFDLE1BQUF2RSxjQUFBLEdBQUFlLENBQUEsV0FDbERqQixLQUFBLENBQUNiLElBQUk7TUFBQzJDLEtBQUssRUFBRTRELE1BQU0sQ0FBQ3lCLGNBQWU7TUFBQXZCLFFBQUEsR0FDakM5RixJQUFBLENBQUNKLGFBQWE7UUFBQ21HLElBQUksRUFBRSxFQUFHO1FBQUNDLEtBQUssRUFBRTdGLE1BQU0sQ0FBQ0k7TUFBTSxDQUFFLENBQUMsRUFDaERQLElBQUEsQ0FBQ1YsSUFBSTtRQUFDMEMsS0FBSyxFQUFFNEQsTUFBTSxDQUFDMEIsU0FBVTtRQUFBeEIsUUFBQSxFQUMzQixDQUFBMUYsY0FBQSxHQUFBZSxDQUFBLFdBQUFaLEtBQUssTUFBQUgsY0FBQSxHQUFBZSxDQUFBLFdBQUkrQixnQkFBZ0IsQ0FBQyxDQUFDLENBQUM7TUFBQSxDQUN6QixDQUFDO0lBQUEsQ0FDSCxDQUFDLENBQ1IsRUFHQSxDQUFBOUMsY0FBQSxHQUFBZSxDQUFBLFdBQUFILFVBQVUsTUFBQVosY0FBQSxHQUFBZSxDQUFBLFdBQUksQ0FBQ1osS0FBSyxNQUFBSCxjQUFBLEdBQUFlLENBQUEsV0FBSW1DLE9BQU8sTUFBQWxELGNBQUEsR0FBQWUsQ0FBQSxXQUM5Qm5CLElBQUEsQ0FBQ1YsSUFBSTtNQUFDMEMsS0FBSyxFQUFFNEQsTUFBTSxDQUFDNUUsVUFBVztNQUFBOEUsUUFBQSxFQUFFOUU7SUFBVSxDQUFPLENBQUMsQ0FDcEQsRUFHQSxDQUFBWixjQUFBLEdBQUFlLENBQUEsV0FBQVEsU0FBUyxNQUFBdkIsY0FBQSxHQUFBZSxDQUFBLFdBQUl1QixLQUFLLENBQUNpQyxNQUFNLEdBQUdoRCxTQUFTLEdBQUcsR0FBRyxNQUFBdkIsY0FBQSxHQUFBZSxDQUFBLFdBQzFDakIsS0FBQSxDQUFDWixJQUFJO01BQUMwQyxLQUFLLEVBQUU0RCxNQUFNLENBQUMyQixjQUFlO01BQUF6QixRQUFBLEdBQ2hDcEQsS0FBSyxDQUFDaUMsTUFBTSxFQUFDLEdBQUMsRUFBQ2hELFNBQVM7SUFBQSxDQUNyQixDQUFDLENBQ1I7RUFBQSxDQUNHLENBQUM7QUFFWDtBQUVBLElBQU1pRSxNQUFNLElBQUF4RixjQUFBLEdBQUFDLENBQUEsUUFBR2QsVUFBVSxDQUFDaUksTUFBTSxDQUFDO0VBQy9CakIsU0FBUyxFQUFFO0lBQ1RrQixZQUFZLEVBQUU7RUFDaEIsQ0FBQztFQUNEMUcsS0FBSyxFQUFFO0lBQ0wyRyxRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUUsS0FBSztJQUNqQjNCLEtBQUssRUFBRTdGLE1BQU0sQ0FBQ1EsSUFBSTtJQUNsQjhHLFlBQVksRUFBRTtFQUNoQixDQUFDO0VBQ0RqQixjQUFjLEVBQUU7SUFDZG9CLFFBQVEsRUFBRSxVQUFVO0lBQ3BCQyxhQUFhLEVBQUUsS0FBSztJQUNwQkMsVUFBVSxFQUFFO0VBQ2QsQ0FBQztFQUNEMUIsS0FBSyxFQUFFO0lBQ0wyQixJQUFJLEVBQUUsQ0FBQztJQUNQQyxXQUFXLEVBQUUsQ0FBQztJQUNkQyxXQUFXLEVBQUU5SCxNQUFNLENBQUNPLFNBQVM7SUFDN0J3SCxZQUFZLEVBQUUsQ0FBQztJQUNmQyxpQkFBaUIsRUFBRSxFQUFFO0lBQ3JCQyxlQUFlLEVBQUUsRUFBRTtJQUNuQlYsUUFBUSxFQUFFLEVBQUU7SUFDWjFCLEtBQUssRUFBRTdGLE1BQU0sQ0FBQ1EsSUFBSTtJQUNsQjBILGVBQWUsRUFBRWxJLE1BQU0sQ0FBQ1M7RUFDMUIsQ0FBQztFQUNEeUYsWUFBWSxFQUFFO0lBQ1o0QixXQUFXLEVBQUU5SCxNQUFNLENBQUNHLE9BQU87SUFDM0IwSCxXQUFXLEVBQUU7RUFDZixDQUFDO0VBQ0QxQixVQUFVLEVBQUU7SUFDVjJCLFdBQVcsRUFBRTlILE1BQU0sQ0FBQ0ksS0FBSztJQUN6QnlILFdBQVcsRUFBRTtFQUNmLENBQUM7RUFDRGQsY0FBYyxFQUFFO0lBQ2RVLFFBQVEsRUFBRSxVQUFVO0lBQ3BCVSxLQUFLLEVBQUUsRUFBRTtJQUNUQyxPQUFPLEVBQUU7RUFDWCxDQUFDO0VBQ0QxQyxpQkFBaUIsRUFBRTtJQUNqQitCLFFBQVEsRUFBRSxVQUFVO0lBQ3BCVSxLQUFLLEVBQUUsRUFBRTtJQUNUVCxhQUFhLEVBQUUsS0FBSztJQUNwQkMsVUFBVSxFQUFFLFFBQVE7SUFDcEJVLEdBQUcsRUFBRTtFQUNQLENBQUM7RUFDRHZDLFlBQVksRUFBRTtJQUNaeUIsUUFBUSxFQUFFLEVBQUU7SUFDWkMsVUFBVSxFQUFFO0VBQ2QsQ0FBQztFQUNETixjQUFjLEVBQUU7SUFDZFEsYUFBYSxFQUFFLEtBQUs7SUFDcEJDLFVBQVUsRUFBRSxRQUFRO0lBQ3BCVyxTQUFTLEVBQUUsQ0FBQztJQUNaRCxHQUFHLEVBQUU7RUFDUCxDQUFDO0VBQ0RsQixTQUFTLEVBQUU7SUFDVEksUUFBUSxFQUFFLEVBQUU7SUFDWjFCLEtBQUssRUFBRTdGLE1BQU0sQ0FBQ0ksS0FBSztJQUNuQndILElBQUksRUFBRTtFQUNSLENBQUM7RUFDRC9HLFVBQVUsRUFBRTtJQUNWMEcsUUFBUSxFQUFFLEVBQUU7SUFDWjFCLEtBQUssRUFBRTdGLE1BQU0sQ0FBQ00sSUFBSTtJQUNsQmdJLFNBQVMsRUFBRTtFQUNiLENBQUM7RUFDRGxCLGNBQWMsRUFBRTtJQUNkRyxRQUFRLEVBQUUsRUFBRTtJQUNaMUIsS0FBSyxFQUFFN0YsTUFBTSxDQUFDTSxJQUFJO0lBQ2xCaUksU0FBUyxFQUFFLE9BQU87SUFDbEJELFNBQVMsRUFBRTtFQUNiO0FBQ0YsQ0FBQyxDQUFDIiwiaWdub3JlTGlzdCI6W119