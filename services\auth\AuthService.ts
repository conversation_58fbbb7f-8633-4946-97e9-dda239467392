/**
 * Real Authentication Service
 * 
 * Replaces mock authentication with real Supabase auth
 * Handles user registration, login, logout, and session management
 */

import { createClient, SupabaseClient, User, Session } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';
import { handleAuthError, handleDatabaseError, logError } from '@/utils/errorHandling';
import { magicLinkService } from './MagicLinkService';
import { postHogService } from '../analytics/PostHogService';
import { sentryService } from '../monitoring/SentryService';

// Types
export interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  username?: string;
  avatar_url?: string;
  phone?: string;
  date_of_birth?: string;
  tennis_level: 'beginner' | 'intermediate' | 'advanced' | 'professional';
  playing_style: 'aggressive' | 'defensive' | 'all_court' | 'serve_volley';
  dominant_hand: 'right' | 'left' | 'ambidextrous';
  favorite_surface: 'hard' | 'clay' | 'grass' | 'indoor';
  years_playing: number;
  primary_goal?: string;
  training_frequency: number;
  preferred_session_duration: number;
  country?: string;
  city?: string;
  timezone: string;
  subscription_tier: 'free' | 'premium' | 'pro';
  subscription_expires_at?: string;
  profile_visibility: 'public' | 'friends' | 'private';
  allow_friend_requests: boolean;
  show_online_status: boolean;
  created_at: string;
  updated_at: string;
  last_active_at: string;
  is_active: boolean;
}

export interface AuthState {
  user: User | null;
  profile: UserProfile | null;
  session: Session | null;
  loading: boolean;
  error: string | null;
}

export interface SignUpData {
  email: string;
  password: string;
  fullName: string;
  tennisLevel?: 'beginner' | 'intermediate' | 'advanced' | 'professional';
  playingStyle?: 'aggressive' | 'defensive' | 'all_court' | 'serve_volley';
  dominantHand?: 'right' | 'left' | 'ambidextrous';
  favoriteSurface?: 'hard' | 'clay' | 'grass' | 'indoor';
  yearsPlaying?: number;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface UpdateProfileData {
  full_name?: string;
  username?: string;
  phone?: string;
  date_of_birth?: string;
  tennis_level?: 'beginner' | 'intermediate' | 'advanced' | 'professional';
  playing_style?: 'aggressive' | 'defensive' | 'all_court' | 'serve_volley';
  dominant_hand?: 'right' | 'left' | 'ambidextrous';
  favorite_surface?: 'hard' | 'clay' | 'grass' | 'indoor';
  years_playing?: number;
  primary_goal?: string;
  training_frequency?: number;
  preferred_session_duration?: number;
  country?: string;
  city?: string;
  profile_visibility?: 'public' | 'friends' | 'private';
  allow_friend_requests?: boolean;
  show_online_status?: boolean;
}

class AuthService {
  private supabase: SupabaseClient;
  private authStateListeners: ((state: AuthState) => void)[] = [];
  private currentState: AuthState = {
    user: null,
    profile: null,
    session: null,
    loading: true,
    error: null,
  };

  constructor() {
    // Initialize Supabase client
    const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Missing Supabase configuration');
      this.currentState.error = 'Authentication service not configured';
      return;
    }

    this.supabase = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        storage: AsyncStorage,
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false,
      },
    });

    this.initializeAuth();
    this.initializeServices();
  }

  /**
   * Initialize additional services
   */
  private async initializeServices(): Promise<void> {
    try {
      await magicLinkService.initialize();
      await postHogService.initialize();
      await sentryService.initialize();
    } catch (error) {
      console.error('Failed to initialize auth services:', error);
    }
  }

  /**
   * Initialize authentication state
   */
  private async initializeAuth() {
    try {
      // Get initial session
      const { data: { session }, error } = await this.supabase.auth.getSession();
      
      if (error) {
        console.error('Error getting session:', error);
        this.updateState({ error: error.message, loading: false });
        return;
      }

      if (session?.user) {
        await this.loadUserProfile(session.user.id);
        this.updateState({
          user: session.user,
          session,
          loading: false,
          error: null,
        });
      } else {
        this.updateState({ loading: false });
      }

      // Listen for auth changes
      this.supabase.auth.onAuthStateChange(async (event, session) => {
        console.log('Auth state changed:', event);
        
        if (event === 'SIGNED_IN' && session?.user) {
          await this.loadUserProfile(session.user.id);

          // Set user context for analytics and monitoring
          const userProfile = this.currentState.profile;
          if (userProfile) {
            postHogService.identify(session.user.id, {
              userId: session.user.id,
              email: userProfile.email,
              name: userProfile.full_name,
              tennisLevel: userProfile.skill_level,
              subscriptionTier: userProfile.subscription_tier,
            });

            sentryService.setUser({
              id: session.user.id,
              email: userProfile.email,
              username: userProfile.full_name,
              tennisLevel: userProfile.skill_level,
              subscriptionTier: userProfile.subscription_tier,
            });
          }

          this.updateState({
            user: session.user,
            session,
            error: null,
          });
        } else if (event === 'SIGNED_OUT') {
          // Clear analytics and monitoring user context
          postHogService.reset();
          sentryService.clearUser();

          this.updateState({
            user: null,
            profile: null,
            session: null,
            error: null,
          });
        } else if (event === 'TOKEN_REFRESHED' && session) {
          this.updateState({ session });
        }
      });

    } catch (error) {
      console.error('Error initializing auth:', error);
      this.updateState({
        error: 'Failed to initialize authentication',
        loading: false,
      });
    }
  }

  /**
   * Load user profile from database
   */
  private async loadUserProfile(userId: string): Promise<void> {
    try {
      const { data: profile, error } = await this.supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error loading profile:', error);
        return;
      }

      this.updateState({ profile });
    } catch (error) {
      console.error('Error loading user profile:', error);
    }
  }

  /**
   * Update authentication state and notify listeners
   */
  private updateState(updates: Partial<AuthState>) {
    this.currentState = { ...this.currentState, ...updates };
    this.authStateListeners.forEach(listener => listener(this.currentState));
  }

  /**
   * Subscribe to authentication state changes
   */
  onAuthStateChange(callback: (state: AuthState) => void): () => void {
    this.authStateListeners.push(callback);
    
    // Immediately call with current state
    callback(this.currentState);
    
    // Return unsubscribe function
    return () => {
      const index = this.authStateListeners.indexOf(callback);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  /**
   * Get current authentication state
   */
  getCurrentState(): AuthState {
    return this.currentState;
  }

  /**
   * Sign up new user
   */
  async signUp(data: SignUpData): Promise<{ success: boolean; error?: string }> {
    try {
      this.updateState({ loading: true, error: null });

      const { data: authData, error: authError } = await this.supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            full_name: data.fullName,
            tennis_level: data.tennisLevel || 'beginner',
            playing_style: data.playingStyle || 'all_court',
            dominant_hand: data.dominantHand || 'right',
            favorite_surface: data.favoriteSurface || 'hard',
            years_playing: data.yearsPlaying || 0,
          },
        },
      });

      if (authError) {
        const appError = handleAuthError(authError);
        logError(appError, { context: 'signUp', email: data.email });
        this.updateState({ error: appError.userMessage, loading: false });
        return { success: false, error: appError.userMessage };
      }

      // If user is created but needs email confirmation
      if (authData.user && !authData.session) {
        this.updateState({ loading: false });
        return {
          success: true,
          error: 'Please check your email to confirm your account'
        };
      }

      this.updateState({ loading: false });
      return { success: true };

    } catch (error) {
      const appError = handleAuthError(error);
      logError(appError, { context: 'signUp', email: data.email });
      this.updateState({ error: appError.userMessage, loading: false });
      return { success: false, error: appError.userMessage };
    }
  }

  /**
   * Sign in user
   */
  async signIn(data: SignInData): Promise<{ success: boolean; error?: string }> {
    try {
      this.updateState({ loading: true, error: null });

      const { data: authData, error: authError } = await this.supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      });

      if (authError) {
        this.updateState({ error: authError.message, loading: false });
        return { success: false, error: authError.message };
      }

      this.updateState({ loading: false });

      // Track login event
      postHogService.trackLogin('email');

      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sign in failed';
      this.updateState({ error: errorMessage, loading: false });

      // Track auth error
      sentryService.trackAuthError(error as Error, 'email');

      return { success: false, error: errorMessage };
    }
  }

  /**
   * Sign in with Magic.link (passwordless)
   */
  async signInWithMagicLink(email: string): Promise<{ success: boolean; error?: string }> {
    try {
      this.updateState({ loading: true, error: null });

      const result = await magicLinkService.loginWithEmail({ email });

      if (!result.success) {
        this.updateState({ error: result.error, loading: false });
        return { success: false, error: result.error };
      }

      // Create or update user profile in Supabase
      if (result.user) {
        await this.createOrUpdateMagicLinkUser(result.user);
      }

      this.updateState({ loading: false });

      // Track login event
      postHogService.trackLogin('magic_link');

      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Magic.link sign in failed';
      this.updateState({ error: errorMessage, loading: false });

      // Track auth error
      sentryService.trackAuthError(error as Error, 'magic_link');

      return { success: false, error: errorMessage };
    }
  }

  /**
   * Sign in with Google
   */
  async signInWithGoogle(idToken: string): Promise<{ success: boolean; error?: string }> {
    try {
      this.updateState({ loading: true, error: null });

      const { data, error } = await this.supabase.auth.signInWithIdToken({
        provider: 'google',
        token: idToken,
      });

      if (error) {
        this.updateState({ error: error.message, loading: false });
        return { success: false, error: error.message };
      }

      this.updateState({ loading: false });

      // Track login event
      postHogService.trackLogin('google');

      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Google sign in failed';
      this.updateState({ error: errorMessage, loading: false });

      // Track auth error
      sentryService.trackAuthError(error as Error, 'google');

      return { success: false, error: errorMessage };
    }
  }

  /**
   * Sign in with Facebook
   */
  async signInWithFacebook(accessToken: string): Promise<{ success: boolean; error?: string }> {
    try {
      this.updateState({ loading: true, error: null });

      const { data, error } = await this.supabase.auth.signInWithIdToken({
        provider: 'facebook',
        token: accessToken,
      });

      if (error) {
        this.updateState({ error: error.message, loading: false });
        return { success: false, error: error.message };
      }

      this.updateState({ loading: false });

      // Track login event
      postHogService.trackLogin('facebook');

      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Facebook sign in failed';
      this.updateState({ error: errorMessage, loading: false });

      // Track auth error
      sentryService.trackAuthError(error as Error, 'facebook');

      return { success: false, error: errorMessage };
    }
  }

  /**
   * Sign out user
   */
  async signOut(): Promise<{ success: boolean; error?: string }> {
    try {
      this.updateState({ loading: true, error: null });

      // Sign out from Supabase
      const { error } = await this.supabase.auth.signOut();

      if (error) {
        this.updateState({ error: error.message, loading: false });
        return { success: false, error: error.message };
      }

      // Sign out from Magic.link if logged in
      if (await magicLinkService.isLoggedIn()) {
        await magicLinkService.logout();
      }

      // Clear analytics and monitoring user context
      postHogService.reset();
      sentryService.clearUser();

      this.updateState({ loading: false });
      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sign out failed';
      this.updateState({ error: errorMessage, loading: false });
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Create or update Magic.link user in Supabase
   */
  private async createOrUpdateMagicLinkUser(magicUser: any): Promise<void> {
    try {
      // Check if user exists
      const { data: existingUser } = await this.supabase
        .from('user_profiles')
        .select('*')
        .eq('email', magicUser.email)
        .single();

      if (!existingUser) {
        // Create new user profile
        const { error } = await this.supabase
          .from('user_profiles')
          .insert({
            email: magicUser.email,
            full_name: magicUser.email.split('@')[0], // Use email prefix as name
            magic_issuer: magicUser.issuer,
            magic_public_address: magicUser.publicAddress,
            skill_level: 'beginner',
            subscription_tier: 'free',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });

        if (error) {
          console.error('Failed to create Magic.link user profile:', error);
        }
      } else {
        // Update existing user with Magic.link data
        const { error } = await this.supabase
          .from('user_profiles')
          .update({
            magic_issuer: magicUser.issuer,
            magic_public_address: magicUser.publicAddress,
            updated_at: new Date().toISOString(),
          })
          .eq('email', magicUser.email);

        if (error) {
          console.error('Failed to update Magic.link user profile:', error);
        }
      }
    } catch (error) {
      console.error('Error handling Magic.link user:', error);
    }
  }

  /**
   * Reset password
   */
  async resetPassword(email: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'acemind://reset-password',
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Password reset failed';
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: UpdateProfileData): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.currentState.user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { data, error } = await this.supabase
        .from('user_profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', this.currentState.user.id)
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      this.updateState({ profile: data });
      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Profile update failed';
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Update user's last active timestamp
   */
  async updateLastActive(): Promise<void> {
    if (!this.currentState.user) return;

    try {
      await this.supabase
        .from('user_profiles')
        .update({ last_active_at: new Date().toISOString() })
        .eq('id', this.currentState.user.id);
    } catch (error) {
      console.error('Error updating last active:', error);
    }
  }

  /**
   * Upload avatar image
   */
  async uploadAvatar(imageUri: string): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      if (!this.currentState.user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Convert image URI to blob (implementation depends on platform)
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      const fileExt = imageUri.split('.').pop();
      const fileName = `${this.currentState.user.id}/avatar.${fileExt}`;

      const { data, error } = await this.supabase.storage
        .from('avatars')
        .upload(fileName, blob, {
          cacheControl: '3600',
          upsert: true,
        });

      if (error) {
        return { success: false, error: error.message };
      }

      // Get public URL
      const { data: { publicUrl } } = this.supabase.storage
        .from('avatars')
        .getPublicUrl(fileName);

      // Update profile with new avatar URL
      await this.updateProfile({ avatar_url: publicUrl });

      return { success: true, url: publicUrl };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Avatar upload failed';
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Delete user account
   */
  async deleteAccount(): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.currentState.user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Note: Supabase doesn't have a direct delete user method in the client
      // This would typically be handled by a server function
      Alert.alert(
        'Delete Account',
        'Please contact support to delete your account.',
        [{ text: 'OK' }]
      );

      return { success: false, error: 'Please contact support to delete your account' };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Account deletion failed';
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Get Supabase client for direct database operations
   */
  getSupabaseClient(): SupabaseClient {
    return this.supabase;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.currentState.user && !!this.currentState.session;
  }

  /**
   * Check if user has premium subscription
   */
  hasPremiumSubscription(): boolean {
    return this.currentState.profile?.subscription_tier === 'premium' || 
           this.currentState.profile?.subscription_tier === 'pro';
  }

  /**
   * Check if user has pro subscription
   */
  hasProSubscription(): boolean {
    return this.currentState.profile?.subscription_tier === 'pro';
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
