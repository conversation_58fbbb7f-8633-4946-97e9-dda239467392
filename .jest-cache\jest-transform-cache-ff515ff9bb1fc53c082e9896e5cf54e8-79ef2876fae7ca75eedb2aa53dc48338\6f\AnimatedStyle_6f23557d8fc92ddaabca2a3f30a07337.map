{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_interopRequireDefault", "exports", "__esModule", "_AnimatedNode", "_AnimatedTransform", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "_NativeAnimatedHelper", "_StyleSheet", "flattenStyle", "flatten", "createAnimatedStyle", "inputStyle", "style", "animatedStyles", "key", "value", "Array", "isArray", "AnimatedStyle", "_AnimatedWithChildren2", "_this", "_inputStyle", "_style", "_walkStyleAndGetValues", "updatedStyle", "__isNative", "__getValue", "_walkStyleAndGetAnimatedValues", "__getAnimatedValue", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__makeNative", "__getNativeConfig", "styleConfig", "styleKey", "__getNativeTag", "validateStyles", "type", "_default", "module"], "sources": ["AnimatedStyle.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _AnimatedNode = _interopRequireDefault(require(\"./AnimatedNode\"));\nvar _AnimatedTransform = _interopRequireDefault(require(\"./AnimatedTransform\"));\nvar _AnimatedWithChildren = _interopRequireDefault(require(\"./AnimatedWithChildren\"));\nvar _NativeAnimatedHelper = _interopRequireDefault(require(\"../NativeAnimatedHelper\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../../../../exports/StyleSheet\"));\nvar flattenStyle = _StyleSheet.default.flatten;\nfunction createAnimatedStyle(inputStyle) {\n  var style = flattenStyle(inputStyle);\n  var animatedStyles = {};\n  for (var key in style) {\n    var value = style[key];\n    if (key === 'transform' && Array.isArray(value)) {\n      animatedStyles[key] = new _AnimatedTransform.default(value);\n    } else if (value instanceof _AnimatedNode.default) {\n      animatedStyles[key] = value;\n    } else if (value && !Array.isArray(value) && typeof value === 'object') {\n      animatedStyles[key] = createAnimatedStyle(value);\n    }\n  }\n  return animatedStyles;\n}\nclass AnimatedStyle extends _AnimatedWithChildren.default {\n  constructor(style) {\n    super();\n    this._inputStyle = style;\n    this._style = createAnimatedStyle(style);\n  }\n\n  // Recursively get values for nested styles (like iOS's shadowOffset)\n  _walkStyleAndGetValues(style) {\n    var updatedStyle = {};\n    for (var key in style) {\n      var value = style[key];\n      if (value instanceof _AnimatedNode.default) {\n        if (!value.__isNative) {\n          // We cannot use value of natively driven nodes this way as the value we have access from\n          // JS may not be up to date.\n          updatedStyle[key] = value.__getValue();\n        }\n      } else if (value && !Array.isArray(value) && typeof value === 'object') {\n        // Support animating nested values (for example: shadowOffset.height)\n        updatedStyle[key] = this._walkStyleAndGetValues(value);\n      } else {\n        updatedStyle[key] = value;\n      }\n    }\n    return updatedStyle;\n  }\n  __getValue() {\n    return [this._inputStyle, this._walkStyleAndGetValues(this._style)];\n  }\n\n  // Recursively get animated values for nested styles (like iOS's shadowOffset)\n  _walkStyleAndGetAnimatedValues(style) {\n    var updatedStyle = {};\n    for (var key in style) {\n      var value = style[key];\n      if (value instanceof _AnimatedNode.default) {\n        updatedStyle[key] = value.__getAnimatedValue();\n      } else if (value && !Array.isArray(value) && typeof value === 'object') {\n        // Support animating nested values (for example: shadowOffset.height)\n        updatedStyle[key] = this._walkStyleAndGetAnimatedValues(value);\n      }\n    }\n    return updatedStyle;\n  }\n  __getAnimatedValue() {\n    return this._walkStyleAndGetAnimatedValues(this._style);\n  }\n  __attach() {\n    for (var key in this._style) {\n      var value = this._style[key];\n      if (value instanceof _AnimatedNode.default) {\n        value.__addChild(this);\n      }\n    }\n  }\n  __detach() {\n    for (var key in this._style) {\n      var value = this._style[key];\n      if (value instanceof _AnimatedNode.default) {\n        value.__removeChild(this);\n      }\n    }\n    super.__detach();\n  }\n  __makeNative() {\n    for (var key in this._style) {\n      var value = this._style[key];\n      if (value instanceof _AnimatedNode.default) {\n        value.__makeNative();\n      }\n    }\n    super.__makeNative();\n  }\n  __getNativeConfig() {\n    var styleConfig = {};\n    for (var styleKey in this._style) {\n      if (this._style[styleKey] instanceof _AnimatedNode.default) {\n        var style = this._style[styleKey];\n        style.__makeNative();\n        styleConfig[styleKey] = style.__getNativeTag();\n      }\n    }\n    _NativeAnimatedHelper.default.validateStyles(styleConfig);\n    return {\n      type: 'style',\n      style: styleConfig\n    };\n  }\n}\nvar _default = exports.default = AnimatedStyle;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,KAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,IAAAM,UAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAA,SAAAO,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,SAAAa,cAAAb,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAY,CAAA,QAAAC,CAAA,OAAAlB,KAAA,CAAAM,OAAA,MAAAP,gBAAA,CAAAO,OAAA,MAAAW,CAAA,GAAAd,CAAA,CAAAU,SAAA,GAAAV,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAY,CAAA,yBAAAC,CAAA,aAAAf,CAAA,WAAAe,CAAA,CAAAP,KAAA,CAAAN,CAAA,EAAAF,CAAA,OAAAe,CAAA;AAEb,IAAIC,sBAAsB,GAAGxB,OAAO,CAAC,8CAA8C,CAAC,CAACW,OAAO;AAC5Fc,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACd,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIgB,aAAa,GAAGH,sBAAsB,CAACxB,OAAO,iBAAiB,CAAC,CAAC;AACrE,IAAI4B,kBAAkB,GAAGJ,sBAAsB,CAACxB,OAAO,sBAAsB,CAAC,CAAC;AAC/E,IAAI6B,qBAAqB,GAAGL,sBAAsB,CAACxB,OAAO,yBAAyB,CAAC,CAAC;AACrF,IAAI8B,qBAAqB,GAAGN,sBAAsB,CAACxB,OAAO,0BAA0B,CAAC,CAAC;AACtF,IAAI+B,WAAW,GAAGP,sBAAsB,CAACxB,OAAO,iCAAiC,CAAC,CAAC;AACnF,IAAIgC,YAAY,GAAGD,WAAW,CAACpB,OAAO,CAACsB,OAAO;AAC9C,SAASC,mBAAmBA,CAACC,UAAU,EAAE;EACvC,IAAIC,KAAK,GAAGJ,YAAY,CAACG,UAAU,CAAC;EACpC,IAAIE,cAAc,GAAG,CAAC,CAAC;EACvB,KAAK,IAAIC,GAAG,IAAIF,KAAK,EAAE;IACrB,IAAIG,KAAK,GAAGH,KAAK,CAACE,GAAG,CAAC;IACtB,IAAIA,GAAG,KAAK,WAAW,IAAIE,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;MAC/CF,cAAc,CAACC,GAAG,CAAC,GAAG,IAAIV,kBAAkB,CAACjB,OAAO,CAAC4B,KAAK,CAAC;IAC7D,CAAC,MAAM,IAAIA,KAAK,YAAYZ,aAAa,CAAChB,OAAO,EAAE;MACjD0B,cAAc,CAACC,GAAG,CAAC,GAAGC,KAAK;IAC7B,CAAC,MAAM,IAAIA,KAAK,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACtEF,cAAc,CAACC,GAAG,CAAC,GAAGJ,mBAAmB,CAACK,KAAK,CAAC;IAClD;EACF;EACA,OAAOF,cAAc;AACvB;AAAC,IACKK,aAAa,aAAAC,sBAAA;EACjB,SAAAD,cAAYN,KAAK,EAAE;IAAA,IAAAQ,KAAA;IAAA,IAAA3C,gBAAA,CAAAU,OAAA,QAAA+B,aAAA;IACjBE,KAAA,GAAArC,UAAA,OAAAmC,aAAA;IACAE,KAAA,CAAKC,WAAW,GAAGT,KAAK;IACxBQ,KAAA,CAAKE,MAAM,GAAGZ,mBAAmB,CAACE,KAAK,CAAC;IAAC,OAAAQ,KAAA;EAC3C;EAAC,IAAAtC,UAAA,CAAAK,OAAA,EAAA+B,aAAA,EAAAC,sBAAA;EAAA,WAAAzC,aAAA,CAAAS,OAAA,EAAA+B,aAAA;IAAAJ,GAAA;IAAAC,KAAA,EAGD,SAAAQ,sBAAsBA,CAACX,KAAK,EAAE;MAC5B,IAAIY,YAAY,GAAG,CAAC,CAAC;MACrB,KAAK,IAAIV,GAAG,IAAIF,KAAK,EAAE;QACrB,IAAIG,KAAK,GAAGH,KAAK,CAACE,GAAG,CAAC;QACtB,IAAIC,KAAK,YAAYZ,aAAa,CAAChB,OAAO,EAAE;UAC1C,IAAI,CAAC4B,KAAK,CAACU,UAAU,EAAE;YAGrBD,YAAY,CAACV,GAAG,CAAC,GAAGC,KAAK,CAACW,UAAU,CAAC,CAAC;UACxC;QACF,CAAC,MAAM,IAAIX,KAAK,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAEtES,YAAY,CAACV,GAAG,CAAC,GAAG,IAAI,CAACS,sBAAsB,CAACR,KAAK,CAAC;QACxD,CAAC,MAAM;UACLS,YAAY,CAACV,GAAG,CAAC,GAAGC,KAAK;QAC3B;MACF;MACA,OAAOS,YAAY;IACrB;EAAC;IAAAV,GAAA;IAAAC,KAAA,EACD,SAAAW,UAAUA,CAAA,EAAG;MACX,OAAO,CAAC,IAAI,CAACL,WAAW,EAAE,IAAI,CAACE,sBAAsB,CAAC,IAAI,CAACD,MAAM,CAAC,CAAC;IACrE;EAAC;IAAAR,GAAA;IAAAC,KAAA,EAGD,SAAAY,8BAA8BA,CAACf,KAAK,EAAE;MACpC,IAAIY,YAAY,GAAG,CAAC,CAAC;MACrB,KAAK,IAAIV,GAAG,IAAIF,KAAK,EAAE;QACrB,IAAIG,KAAK,GAAGH,KAAK,CAACE,GAAG,CAAC;QACtB,IAAIC,KAAK,YAAYZ,aAAa,CAAChB,OAAO,EAAE;UAC1CqC,YAAY,CAACV,GAAG,CAAC,GAAGC,KAAK,CAACa,kBAAkB,CAAC,CAAC;QAChD,CAAC,MAAM,IAAIb,KAAK,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAEtES,YAAY,CAACV,GAAG,CAAC,GAAG,IAAI,CAACa,8BAA8B,CAACZ,KAAK,CAAC;QAChE;MACF;MACA,OAAOS,YAAY;IACrB;EAAC;IAAAV,GAAA;IAAAC,KAAA,EACD,SAAAa,kBAAkBA,CAAA,EAAG;MACnB,OAAO,IAAI,CAACD,8BAA8B,CAAC,IAAI,CAACL,MAAM,CAAC;IACzD;EAAC;IAAAR,GAAA;IAAAC,KAAA,EACD,SAAAc,QAAQA,CAAA,EAAG;MACT,KAAK,IAAIf,GAAG,IAAI,IAAI,CAACQ,MAAM,EAAE;QAC3B,IAAIP,KAAK,GAAG,IAAI,CAACO,MAAM,CAACR,GAAG,CAAC;QAC5B,IAAIC,KAAK,YAAYZ,aAAa,CAAChB,OAAO,EAAE;UAC1C4B,KAAK,CAACe,UAAU,CAAC,IAAI,CAAC;QACxB;MACF;IACF;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EACD,SAAAgB,QAAQA,CAAA,EAAG;MACT,KAAK,IAAIjB,GAAG,IAAI,IAAI,CAACQ,MAAM,EAAE;QAC3B,IAAIP,KAAK,GAAG,IAAI,CAACO,MAAM,CAACR,GAAG,CAAC;QAC5B,IAAIC,KAAK,YAAYZ,aAAa,CAAChB,OAAO,EAAE;UAC1C4B,KAAK,CAACiB,aAAa,CAAC,IAAI,CAAC;QAC3B;MACF;MACAnC,aAAA,CAAAqB,aAAA;IACF;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAkB,YAAYA,CAAA,EAAG;MACb,KAAK,IAAInB,GAAG,IAAI,IAAI,CAACQ,MAAM,EAAE;QAC3B,IAAIP,KAAK,GAAG,IAAI,CAACO,MAAM,CAACR,GAAG,CAAC;QAC5B,IAAIC,KAAK,YAAYZ,aAAa,CAAChB,OAAO,EAAE;UAC1C4B,KAAK,CAACkB,YAAY,CAAC,CAAC;QACtB;MACF;MACApC,aAAA,CAAAqB,aAAA;IACF;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAmB,iBAAiBA,CAAA,EAAG;MAClB,IAAIC,WAAW,GAAG,CAAC,CAAC;MACpB,KAAK,IAAIC,QAAQ,IAAI,IAAI,CAACd,MAAM,EAAE;QAChC,IAAI,IAAI,CAACA,MAAM,CAACc,QAAQ,CAAC,YAAYjC,aAAa,CAAChB,OAAO,EAAE;UAC1D,IAAIyB,KAAK,GAAG,IAAI,CAACU,MAAM,CAACc,QAAQ,CAAC;UACjCxB,KAAK,CAACqB,YAAY,CAAC,CAAC;UACpBE,WAAW,CAACC,QAAQ,CAAC,GAAGxB,KAAK,CAACyB,cAAc,CAAC,CAAC;QAChD;MACF;MACA/B,qBAAqB,CAACnB,OAAO,CAACmD,cAAc,CAACH,WAAW,CAAC;MACzD,OAAO;QACLI,IAAI,EAAE,OAAO;QACb3B,KAAK,EAAEuB;MACT,CAAC;IACH;EAAC;AAAA,EAxFyB9B,qBAAqB,CAAClB,OAAO;AA0FzD,IAAIqD,QAAQ,GAAGvC,OAAO,CAACd,OAAO,GAAG+B,aAAa;AAC9CuB,MAAM,CAACxC,OAAO,GAAGA,OAAO,CAACd,OAAO", "ignoreList": []}