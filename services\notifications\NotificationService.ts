/**
 * Unified Notification Service
 * Uses Firebase Cloud Messaging for Android/web and Expo Push Notifications for Expo apps
 */

import { Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage, MessagePayload } from 'firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.FIREBASE_API_KEY,
  authDomain: process.env.FIREBASE_AUTH_DOMAIN,
  projectId: process.env.FIREBASE_PROJECT_ID,
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.FIREBASE_APP_ID,
  measurementId: process.env.FIREBASE_MEASUREMENT_ID,
};

export interface NotificationData {
  title: string;
  body: string;
  data?: Record<string, any>;
  sound?: string;
  badge?: number;
  categoryId?: string;
  priority?: 'default' | 'high' | 'max';
  channelId?: string;
}

export interface PushToken {
  token: string;
  type: 'expo' | 'fcm';
  platform: 'ios' | 'android' | 'web';
}

export interface NotificationPermissions {
  status: 'granted' | 'denied' | 'undetermined';
  canAskAgain: boolean;
  allowsAlert: boolean;
  allowsBadge: boolean;
  allowsSound: boolean;
}

class NotificationService {
  private isInitialized = false;
  private messaging: any = null;
  private expoPushToken: string | null = null;
  private fcmToken: string | null = null;
  private notificationListeners: ((notification: any) => void)[] = [];

  /**
   * Initialize notification service
   */
  async initialize(): Promise<void> {
    try {
      console.log('Initializing notification service...');

      // Configure notification behavior
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: true,
        }),
      });

      // Initialize Firebase for web/Android FCM
      if (Platform.OS === 'web' || Platform.OS === 'android') {
        await this.initializeFirebaseMessaging();
      }

      // Initialize Expo push notifications
      await this.initializeExpoPushNotifications();

      this.isInitialized = true;
      console.log('Notification service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize notification service:', error);
      throw error;
    }
  }

  /**
   * Initialize Firebase Cloud Messaging
   */
  private async initializeFirebaseMessaging(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Initialize Firebase app
        const app = initializeApp(firebaseConfig);
        this.messaging = getMessaging(app);

        // Get FCM token
        const vapidKey = process.env.EXPO_PUBLIC_FCM_VAPID_KEY;
        if (vapidKey) {
          this.fcmToken = await getToken(this.messaging, { vapidKey });
          console.log('FCM token obtained:', this.fcmToken);
          
          // Store token
          await AsyncStorage.setItem('fcm_token', this.fcmToken);
        }

        // Listen for foreground messages
        onMessage(this.messaging, (payload: MessagePayload) => {
          console.log('Foreground FCM message received:', payload);
          this.handleForegroundMessage(payload);
        });
      }
    } catch (error) {
      console.error('Failed to initialize Firebase messaging:', error);
    }
  }

  /**
   * Initialize Expo Push Notifications
   */
  private async initializeExpoPushNotifications(): Promise<void> {
    try {
      // Request permissions
      const { status } = await Notifications.requestPermissionsAsync();
      if (status !== 'granted') {
        console.warn('Notification permissions not granted');
        return;
      }

      // Get Expo push token
      const tokenData = await Notifications.getExpoPushTokenAsync({
        projectId: process.env.EXPO_PUBLIC_PROJECT_ID,
      });
      
      this.expoPushToken = tokenData.data;
      console.log('Expo push token obtained:', this.expoPushToken);
      
      // Store token
      await AsyncStorage.setItem('expo_push_token', this.expoPushToken);

      // Set up notification listeners
      this.setupNotificationListeners();
    } catch (error) {
      console.error('Failed to initialize Expo push notifications:', error);
    }
  }

  /**
   * Set up notification event listeners
   */
  private setupNotificationListeners(): void {
    // Notification received while app is in foreground
    Notifications.addNotificationReceivedListener((notification) => {
      console.log('Notification received in foreground:', notification);
      this.notifyListeners(notification);
    });

    // Notification tapped/opened
    Notifications.addNotificationResponseReceivedListener((response) => {
      console.log('Notification response received:', response);
      this.handleNotificationResponse(response);
    });
  }

  /**
   * Handle foreground FCM message
   */
  private handleForegroundMessage(payload: MessagePayload): void {
    const notification = {
      request: {
        content: {
          title: payload.notification?.title || '',
          body: payload.notification?.body || '',
          data: payload.data || {},
        },
      },
    };
    this.notifyListeners(notification);
  }

  /**
   * Handle notification response (when user taps notification)
   */
  private handleNotificationResponse(response: any): void {
    const { notification } = response;
    const data = notification.request.content.data;
    
    // Handle different notification types
    if (data?.type === 'training_reminder') {
      // Navigate to training screen
      console.log('Navigate to training screen');
    } else if (data?.type === 'match_result') {
      // Navigate to match details
      console.log('Navigate to match details');
    } else if (data?.type === 'coaching_feedback') {
      // Navigate to coaching feedback
      console.log('Navigate to coaching feedback');
    }
  }

  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<NotificationPermissions> {
    try {
      const { status, canAskAgain, granted } = await Notifications.requestPermissionsAsync();
      
      const permissions: NotificationPermissions = {
        status: status as 'granted' | 'denied' | 'undetermined',
        canAskAgain,
        allowsAlert: granted,
        allowsBadge: granted,
        allowsSound: granted,
      };

      return permissions;
    } catch (error) {
      console.error('Failed to request permissions:', error);
      return {
        status: 'denied',
        canAskAgain: false,
        allowsAlert: false,
        allowsBadge: false,
        allowsSound: false,
      };
    }
  }

  /**
   * Get current push tokens
   */
  async getPushTokens(): Promise<PushToken[]> {
    const tokens: PushToken[] = [];

    if (this.expoPushToken) {
      tokens.push({
        token: this.expoPushToken,
        type: 'expo',
        platform: Platform.OS as 'ios' | 'android' | 'web',
      });
    }

    if (this.fcmToken) {
      tokens.push({
        token: this.fcmToken,
        type: 'fcm',
        platform: Platform.OS as 'ios' | 'android' | 'web',
      });
    }

    return tokens;
  }

  /**
   * Send local notification
   */
  async sendLocalNotification(notification: NotificationData): Promise<string> {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
          sound: notification.sound || 'default',
          badge: notification.badge,
          categoryIdentifier: notification.categoryId,
        },
        trigger: null, // Send immediately
      });

      console.log('Local notification sent:', notificationId);
      return notificationId;
    } catch (error) {
      console.error('Failed to send local notification:', error);
      throw error;
    }
  }

  /**
   * Schedule notification for later
   */
  async scheduleNotification(
    notification: NotificationData,
    trigger: Date | number
  ): Promise<string> {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
          sound: notification.sound || 'default',
          badge: notification.badge,
          categoryIdentifier: notification.categoryId,
        },
        trigger: typeof trigger === 'number' ? { seconds: trigger } : trigger,
      });

      console.log('Notification scheduled:', notificationId);
      return notificationId;
    } catch (error) {
      console.error('Failed to schedule notification:', error);
      throw error;
    }
  }

  /**
   * Cancel scheduled notification
   */
  async cancelNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
      console.log('Notification cancelled:', notificationId);
    } catch (error) {
      console.error('Failed to cancel notification:', error);
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('All notifications cancelled');
    } catch (error) {
      console.error('Failed to cancel all notifications:', error);
    }
  }

  /**
   * Add notification listener
   */
  addNotificationListener(listener: (notification: any) => void): () => void {
    this.notificationListeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.notificationListeners.indexOf(listener);
      if (index > -1) {
        this.notificationListeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify all listeners
   */
  private notifyListeners(notification: any): void {
    this.notificationListeners.forEach(listener => {
      try {
        listener(notification);
      } catch (error) {
        console.error('Notification listener error:', error);
      }
    });
  }

  /**
   * Check if service is ready
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Get notification settings
   */
  async getNotificationSettings(): Promise<any> {
    try {
      return await Notifications.getPermissionsAsync();
    } catch (error) {
      console.error('Failed to get notification settings:', error);
      return null;
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
