{"version": 3, "names": ["Link", "<PERSON><PERSON>", "StyleSheet", "Text", "View", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "NotFoundScreen", "cov_gza4pauxk", "f", "s", "children", "Screen", "options", "title", "style", "styles", "container", "text", "href", "link", "create", "flex", "alignItems", "justifyContent", "padding", "fontSize", "fontWeight", "marginTop", "paddingVertical"], "sources": ["+not-found.tsx"], "sourcesContent": ["import { Link, Stack } from 'expo-router';\nimport { StyleSheet, Text, View } from 'react-native';\n\nexport default function NotFoundScreen() {\n  return (\n    <>\n      <Stack.Screen options={{ title: 'Oops!' }} />\n      <View style={styles.container}>\n        <Text style={styles.text}>This screen doesn't exist.</Text>\n        <Link href=\"/\" style={styles.link}>\n          <Text>Go to home screen!</Text>\n        </Link>\n      </View>\n    </>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    alignItems: 'center',\n    justifyContent: 'center',\n    padding: 20,\n  },\n  text: {\n    fontSize: 20,\n    fontWeight: 600,\n  },\n  link: {\n    marginTop: 15,\n    paddingVertical: 15,\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,IAAI,EAAEC,KAAK,QAAQ,aAAa;AACzC,SAASC,UAAU,EAAEC,IAAI,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,aAAA,GAAAC,CAAA;EAAAD,aAAA,GAAAE,CAAA;EACvC,OACEN,KAAA,CAAAE,SAAA;IAAAK,QAAA,GACET,IAAA,CAACL,KAAK,CAACe,MAAM;MAACC,OAAO,EAAE;QAAEC,KAAK,EAAE;MAAQ;IAAE,CAAE,CAAC,EAC7CV,KAAA,CAACJ,IAAI;MAACe,KAAK,EAAEC,MAAM,CAACC,SAAU;MAAAN,QAAA,GAC5BT,IAAA,CAACH,IAAI;QAACgB,KAAK,EAAEC,MAAM,CAACE,IAAK;QAAAP,QAAA,EAAC;MAA0B,CAAM,CAAC,EAC3DT,IAAA,CAACN,IAAI;QAACuB,IAAI,EAAC,GAAG;QAACJ,KAAK,EAAEC,MAAM,CAACI,IAAK;QAAAT,QAAA,EAChCT,IAAA,CAACH,IAAI;UAAAY,QAAA,EAAC;QAAkB,CAAM;MAAC,CAC3B,CAAC;IAAA,CACH,CAAC;EAAA,CACP,CAAC;AAEP;AAEA,IAAMK,MAAM,IAAAR,aAAA,GAAAE,CAAA,OAAGZ,UAAU,CAACuB,MAAM,CAAC;EAC/BJ,SAAS,EAAE;IACTK,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,OAAO,EAAE;EACX,CAAC;EACDP,IAAI,EAAE;IACJQ,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDP,IAAI,EAAE;IACJQ,SAAS,EAAE,EAAE;IACbC,eAAe,EAAE;EACnB;AACF,CAAC,CAAC", "ignoreList": []}