{"version": 3, "names": ["useState", "useCallback", "useMemo", "useAuth", "supabase", "usePerformanceData", "usePerformanceMetrics", "usePerformanceAnalysis", "useMatchAnalysis", "optimizedDatabaseService", "usePerformanceAnalytics", "cov_6ubufw8oh", "f", "_ref", "s", "_ref2", "_slicedToArray", "matchAnalysis", "setMatchAnalysis", "_ref3", "_ref4", "loading", "setLoading", "_ref5", "_ref6", "error", "setError", "_ref7", "user", "_ref8", "enableCache", "cacheTimeout", "autoRefresh", "refreshInterval", "rawPerformanceData", "data", "dataLoading", "dataError", "fetchData", "refreshData", "performanceMetrics", "analysisResult", "enableTrends", "enableProjections", "enableRecommendations", "<PERSON><PERSON><PERSON>h", "analyzeMatchOptimized", "performanceData", "b", "recentMatches", "trends", "insights", "strengths", "weaknesses", "recommendations", "projections", "analyzeMatch", "_ref9", "_asyncToGenerator", "matchStats", "opponentInfo", "overallRating", "push", "detailedMetrics", "errorRate", "winnerRate", "result", "tacticalInsights", "fitnessAnalysis", "batchInsert", "user_id", "id", "opponent_name", "name", "opponent_type", "match_score", "pointsWon", "totalPoints", "sets", "opponent_sets", "surface", "duration_minutes", "totalGameTime", "match_stats", "Object", "assign", "analysis", "err", "Error", "message", "console", "_x", "_x2", "apply", "arguments", "generatePerformanceReport", "getSkillProgression", "_ref1", "timeframe", "getPerformanceData", "skillStats", "length", "processSkillProgression", "_x3", "compareWithPeers", "_ref10", "skillLevel", "_ref11", "from", "select", "eq", "single", "userStats", "_ref12", "limit", "peerStats", "peerAverages", "calculatePeerAverages", "generatePeerComparison", "_x4", "refreshAnalytics", "fetchRecentPerformanceData", "_ref14", "userId", "_ref15", "order", "ascending", "matches", "_ref16", "sessions", "_ref17", "_x5", "analyzeTrends", "skillProgression", "calculateSkillTrends", "matchPerformance", "map", "match", "_match$match_stats", "_match$match_stats2", "_match$match_stats3", "date", "created_at", "opponent", "score", "keyMetrics", "winners", "errors", "firstServePercentage", "weeklyProgress", "calculateWeeklyProgress", "generateInsights", "recentWins", "filter", "m", "winRate", "improvingSkills", "trend", "decliningSkills", "skill", "join", "generateProjections", "currentRating", "projectedRating", "Math", "min", "changeRate", "nextMilestones", "generateSetPerformance", "Array", "_", "i", "floor", "unforcedErrors", "generateMatchRecommendations", "basicAnalysis", "tacticalAnalysis", "tacticalEffectiveness", "enduranceRating", "saveMatchAnalysis", "_ref18", "insert", "_x6", "_x7", "skills", "values", "v", "previousRating", "Number", "weeklyData", "for<PERSON>ach", "session", "week", "Date", "toISOString", "slice", "sessionsCompleted", "totalScore", "improvementAreas", "Set", "overall_score", "improvement_areas", "area", "add", "averageScore", "round", "skillHistory", "now", "cutoffDate", "setDate", "getDate", "setMonth", "getMonth", "setFullYear", "getFullYear", "stat", "updated_at", "forehand", "backhand", "serve", "volley", "footwork", "strategy", "mental_game", "totals", "count", "peer", "skill_stats", "keys", "comparison", "userValue", "<PERSON><PERSON><PERSON><PERSON>", "difference", "percentile", "calculatePercentile"], "sources": ["usePerformanceAnalytics.ts"], "sourcesContent": ["import { useState, useCallback, useEffect, useMemo } from 'react';\nimport { performanceAnalyticsService, MatchStatistics, PerformanceTrends } from '@/services/performanceAnalytics';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { supabase } from '@/lib/supabase';\nimport { usePerformanceData, usePerformanceMetrics } from '@/hooks/optimized/usePerformanceData';\nimport { usePerformanceAnalysis, useMatchAnalysis } from '@/hooks/optimized/usePerformanceAnalysis';\nimport { optimizedDatabaseService } from '@/services/optimized/databaseService';\n\nexport interface PerformanceData {\n  matchAnalysis: any;\n  trends: PerformanceTrends;\n  insights: {\n    strengths: string[];\n    weaknesses: string[];\n    recommendations: string[];\n  };\n  projections: {\n    skillProgression: any[];\n    nextMilestones: string[];\n  };\n}\n\nexport interface MatchAnalysisResult {\n  overallRating: number;\n  detailedMetrics: any;\n  tacticalInsights: string[];\n  fitnessAnalysis: any;\n  recommendations: string[];\n}\n\ninterface UsePerformanceAnalyticsReturn {\n  performanceData: PerformanceData | null;\n  matchAnalysis: MatchAnalysisResult | null;\n  loading: boolean;\n  error: string | null;\n  analyzeMatch: (matchStats: MatchStatistics, opponentInfo?: any) => Promise<void>;\n  generatePerformanceReport: () => Promise<void>;\n  getSkillProgression: (timeframe: 'week' | 'month' | 'year') => Promise<any>;\n  compareWithPeers: (skillLevel: string) => Promise<any>;\n  refreshAnalytics: () => Promise<void>;\n}\n\nexport function usePerformanceAnalytics(): UsePerformanceAnalyticsReturn {\n  const [matchAnalysis, setMatchAnalysis] = useState<MatchAnalysisResult | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const { user } = useAuth();\n\n  // Use optimized data fetching hook\n  const {\n    data: rawPerformanceData,\n    loading: dataLoading,\n    error: dataError,\n    fetchData,\n    refreshData,\n  } = usePerformanceData({\n    enableCache: true,\n    cacheTimeout: 300000, // 5 minutes\n    autoRefresh: true,\n    refreshInterval: 600000, // 10 minutes\n  });\n\n  // Use optimized metrics calculation\n  const performanceMetrics = usePerformanceMetrics(rawPerformanceData);\n\n  // Use optimized analysis hook\n  const analysisResult = usePerformanceAnalysis(rawPerformanceData, {\n    enableTrends: true,\n    enableProjections: true,\n    enableRecommendations: true,\n    analysisDepth: 'detailed',\n  });\n\n  // Use optimized match analysis hook\n  const analyzeMatchOptimized = useMatchAnalysis();\n\n  // Memoized performance data transformation\n  const performanceData = useMemo((): PerformanceData | null => {\n    if (!analysisResult || !performanceMetrics) return null;\n\n    return {\n      matchAnalysis: performanceMetrics.recentMatches[0] || null,\n      trends: analysisResult.trends,\n      insights: {\n        strengths: analysisResult.strengths,\n        weaknesses: analysisResult.weaknesses,\n        recommendations: analysisResult.recommendations,\n      },\n      projections: analysisResult.projections,\n    };\n  }, [analysisResult, performanceMetrics]);\n\n  /**\n   * Optimized match analysis with performance tracking\n   */\n  const analyzeMatch = useCallback(async (matchStats: MatchStatistics, opponentInfo?: any) => {\n    if (!user) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Use optimized match analysis\n      const analysisResult = await analyzeMatchOptimized(matchStats, opponentInfo);\n\n      // Generate recommendations based on analysis\n      const recommendations: string[] = [];\n\n      if (analysisResult.overallRating < 60) {\n        recommendations.push('Focus on fundamental technique improvement');\n      }\n\n      if (analysisResult.detailedMetrics.errorRate > 30) {\n        recommendations.push('Work on reducing unforced errors');\n      }\n\n      if (analysisResult.detailedMetrics.winnerRate < 15) {\n        recommendations.push('Practice aggressive shot-making');\n      }\n\n      const result: MatchAnalysisResult = {\n        overallRating: analysisResult.overallRating,\n        detailedMetrics: analysisResult.detailedMetrics,\n        tacticalInsights: analysisResult.tacticalInsights,\n        fitnessAnalysis: analysisResult.fitnessAnalysis,\n        recommendations,\n      };\n\n      setMatchAnalysis(result);\n\n      // Save match analysis using optimized database service\n      await optimizedDatabaseService.batchInsert('match_results', [{\n        user_id: user.id,\n        opponent_name: opponentInfo?.name || 'Analysis Session',\n        opponent_type: 'ai',\n        match_score: `${matchStats.pointsWon}-${matchStats.totalPoints - matchStats.pointsWon}`,\n        sets: [matchStats.pointsWon],\n        opponent_sets: [matchStats.totalPoints - matchStats.pointsWon],\n        surface: opponentInfo?.surface || 'hard',\n        duration_minutes: matchStats.totalGameTime,\n        result: matchStats.pointsWon > (matchStats.totalPoints / 2) ? 'win' : 'loss',\n        match_stats: {\n          ...matchStats,\n          analysis: result,\n        },\n      }]);\n\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to analyze match');\n      console.error('Match analysis error:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [user, analyzeMatchOptimized]);\n\n  /**\n   * Optimized performance report generation\n   */\n  const generatePerformanceReport = useCallback(async () => {\n    if (!user) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Use optimized data fetching\n      await fetchData(true); // Force refresh\n\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to generate performance report');\n      console.error('Performance report error:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [user, fetchData]);\n\n  /**\n   * Optimized skill progression fetching\n   */\n  const getSkillProgression = useCallback(async (timeframe: 'week' | 'month' | 'year') => {\n    if (!user) return null;\n\n    try {\n      // Use optimized database service\n      const performanceData = await optimizedDatabaseService.getPerformanceData(user.id, timeframe);\n\n      if (!performanceData.skillStats || performanceData.skillStats.length === 0) {\n        return null;\n      }\n\n      // Process skill progression with memoized calculations\n      return processSkillProgression(performanceData.skillStats, timeframe);\n\n    } catch (error) {\n      console.error('Error fetching skill progression:', error);\n      return null;\n    }\n  }, [user]);\n\n  /**\n   * Compare performance with peers\n   */\n  const compareWithPeers = useCallback(async (skillLevel: string) => {\n    if (!user) return null;\n\n    try {\n      // Get user's current stats\n      const { data: userStats } = await supabase\n        .from('skill_stats')\n        .select('*')\n        .eq('user_id', user.id)\n        .single();\n\n      if (!userStats) return null;\n\n      // Get anonymous peer data (same skill level)\n      const { data: peerStats } = await supabase\n        .from('users')\n        .select(`\n          skill_stats (\n            forehand, backhand, serve, volley, footwork, strategy, mental_game\n          )\n        `)\n        .eq('skill_level', skillLevel)\n        .limit(50);\n\n      if (!peerStats || peerStats.length === 0) {\n        return null;\n      }\n\n      // Calculate peer averages\n      const peerAverages = calculatePeerAverages(peerStats);\n      \n      // Compare user stats with peer averages\n      return generatePeerComparison(userStats, peerAverages);\n\n    } catch (error) {\n      console.error('Error comparing with peers:', error);\n      return null;\n    }\n  }, [user]);\n\n  /**\n   * Optimized analytics refresh\n   */\n  const refreshAnalytics = useCallback(async () => {\n    await refreshData();\n  }, [refreshData]);\n\n  // Memoized return value to prevent unnecessary re-renders\n  return useMemo(() => ({\n    performanceData,\n    matchAnalysis,\n    loading: loading || dataLoading,\n    error: error || dataError,\n    analyzeMatch,\n    generatePerformanceReport,\n    getSkillProgression,\n    compareWithPeers,\n    refreshAnalytics,\n  }), [\n    performanceData,\n    matchAnalysis,\n    loading,\n    dataLoading,\n    error,\n    dataError,\n    analyzeMatch,\n    generatePerformanceReport,\n    getSkillProgression,\n    compareWithPeers,\n    refreshAnalytics,\n  ]);\n\n  // Private helper methods\n\n  const fetchRecentPerformanceData = useCallback(async (userId: string) => {\n    const { data: matches } = await supabase\n      .from('match_results')\n      .select('*')\n      .eq('user_id', userId)\n      .order('created_at', { ascending: false })\n      .limit(10);\n\n    const { data: sessions } = await supabase\n      .from('training_sessions')\n      .select('*')\n      .eq('user_id', userId)\n      .order('created_at', { ascending: false })\n      .limit(20);\n\n    const { data: skillStats } = await supabase\n      .from('skill_stats')\n      .select('*')\n      .eq('user_id', userId)\n      .order('updated_at', { ascending: false })\n      .limit(5);\n\n    return {\n      matches: matches || [],\n      sessions: sessions || [],\n      skillStats: skillStats || [],\n    };\n  }, []);\n\n  const analyzeTrends = useCallback((data: any): PerformanceTrends => {\n    // Analyze skill progression\n    const skillProgression = data.skillStats.length > 1 \n      ? calculateSkillTrends(data.skillStats)\n      : [];\n\n    // Analyze match performance\n    const matchPerformance = data.matches.map((match: any) => ({\n      date: match.created_at,\n      opponent: match.opponent_name,\n      result: match.result,\n      score: match.match_score,\n      keyMetrics: {\n        winners: match.match_stats?.winners || 0,\n        errors: match.match_stats?.errors || 0,\n        firstServePercentage: match.match_stats?.firstServePercentage || 0,\n      },\n    }));\n\n    // Analyze weekly progress\n    const weeklyProgress = calculateWeeklyProgress(data.sessions);\n\n    return {\n      skillProgression,\n      matchPerformance,\n      weeklyProgress,\n    };\n  }, []);\n\n  const generateInsights = useCallback((data: any, trends: PerformanceTrends) => {\n    const strengths: string[] = [];\n    const weaknesses: string[] = [];\n    const recommendations: string[] = [];\n\n    // Analyze recent match results\n    const recentWins = data.matches.filter((m: any) => m.result === 'win').length;\n    const winRate = data.matches.length > 0 ? (recentWins / data.matches.length) * 100 : 0;\n\n    if (winRate > 60) {\n      strengths.push('Strong recent match performance');\n    } else if (winRate < 40) {\n      weaknesses.push('Struggling in recent matches');\n      recommendations.push('Focus on match-specific training');\n    }\n\n    // Analyze skill trends\n    const improvingSkills = trends.skillProgression.filter(s => s.trend === 'improving');\n    const decliningSkills = trends.skillProgression.filter(s => s.trend === 'declining');\n\n    if (improvingSkills.length > 0) {\n      strengths.push(`Improving in ${improvingSkills.map(s => s.skill).join(', ')}`);\n    }\n\n    if (decliningSkills.length > 0) {\n      weaknesses.push(`Declining in ${decliningSkills.map(s => s.skill).join(', ')}`);\n      recommendations.push(`Focus practice on ${decliningSkills[0].skill}`);\n    }\n\n    return { strengths, weaknesses, recommendations };\n  }, []);\n\n  const generateProjections = useCallback((trends: PerformanceTrends) => {\n    const skillProgression = trends.skillProgression\n      .filter(s => s.trend === 'improving')\n      .map(s => ({\n        skill: s.skill,\n        currentRating: s.currentRating,\n        projectedRating: Math.min(100, s.currentRating + (s.changeRate * 4)), // 4 weeks\n        timeframe: '4 weeks',\n      }));\n\n    const nextMilestones = [\n      'Reach 80% consistency in forehand',\n      'Improve serve percentage to 70%',\n      'Win next tournament match',\n    ];\n\n    return { skillProgression, nextMilestones };\n  }, []);\n\n  const generateSetPerformance = useCallback((matchStats: MatchStatistics): MatchStatistics[] => {\n    // Mock set-by-set performance for fitness analysis\n    const sets = 3;\n    return Array.from({ length: sets }, (_, i) => ({\n      ...matchStats,\n      totalPoints: Math.floor(matchStats.totalPoints / sets),\n      pointsWon: Math.floor(matchStats.pointsWon / sets) - (i * 2), // Simulate fatigue\n      unforcedErrors: Math.floor(matchStats.unforcedErrors / sets) + i, // More errors as match progresses\n    }));\n  }, []);\n\n  const generateMatchRecommendations = useCallback((\n    basicAnalysis: any,\n    tacticalAnalysis: any,\n    fitnessAnalysis: any\n  ): string[] => {\n    const recommendations: string[] = [];\n\n    if (basicAnalysis.overallRating < 60) {\n      recommendations.push('Focus on fundamental technique improvement');\n    }\n\n    if (tacticalAnalysis?.tacticalEffectiveness < 70) {\n      recommendations.push('Work on match strategy and tactical awareness');\n    }\n\n    if (fitnessAnalysis.enduranceRating < 60) {\n      recommendations.push('Improve cardiovascular fitness for longer matches');\n    }\n\n    return recommendations;\n  }, []);\n\n  const saveMatchAnalysis = useCallback(async (matchStats: MatchStatistics, analysis: MatchAnalysisResult) => {\n    try {\n      await supabase\n        .from('match_results')\n        .insert({\n          user_id: user?.id,\n          opponent_name: 'Analysis Session',\n          opponent_type: 'ai',\n          match_score: `${matchStats.pointsWon}-${matchStats.totalPoints - matchStats.pointsWon}`,\n          sets: [matchStats.pointsWon],\n          opponent_sets: [matchStats.totalPoints - matchStats.pointsWon],\n          surface: 'hard',\n          duration_minutes: matchStats.totalGameTime,\n          result: matchStats.pointsWon > (matchStats.totalPoints / 2) ? 'win' : 'loss',\n          match_stats: {\n            ...matchStats,\n            analysis: analysis,\n          },\n        });\n    } catch (error) {\n      console.error('Error saving match analysis:', error);\n    }\n  }, [user]);\n\n  const calculateSkillTrends = useCallback((skillStats: any[]): Array<{\n    skill: string;\n    previousRating: number;\n    currentRating: number;\n    trend: 'improving' | 'stable' | 'declining';\n    changeRate: number;\n  }> => {\n    // Calculate trends for each skill\n    const skills = ['forehand', 'backhand', 'serve', 'volley', 'footwork', 'strategy', 'mental_game'];\n\n    return skills.map(skill => {\n      const values = skillStats.map(s => s[skill]).filter(v => v != null);\n      if (values.length < 2) {\n        return {\n          skill,\n          previousRating: Number(values[0]) || 50,\n          currentRating: Number(values[0]) || 50,\n          trend: 'stable' as const,\n          changeRate: 0,\n        };\n      }\n\n      const previousRating = Number(values[values.length - 2]) || 50;\n      const currentRating = Number(values[values.length - 1]) || 50;\n      const changeRate = currentRating - previousRating;\n\n      return {\n        skill,\n        previousRating,\n        currentRating,\n        trend: changeRate > 2 ? ('improving' as const) : changeRate < -2 ? ('declining' as const) : ('stable' as const),\n        changeRate,\n      };\n    });\n  }, []);\n\n  const calculateWeeklyProgress = useCallback((sessions: any[]) => {\n    // Group sessions by week and calculate progress\n    const weeklyData: { [key: string]: any } = {};\n    \n    sessions.forEach(session => {\n      const week = new Date(session.created_at).toISOString().slice(0, 10);\n      if (!weeklyData[week]) {\n        weeklyData[week] = {\n          week,\n          sessionsCompleted: 0,\n          totalScore: 0,\n          improvementAreas: new Set(),\n        };\n      }\n      \n      weeklyData[week].sessionsCompleted++;\n      weeklyData[week].totalScore += session.overall_score || 75;\n      if (session.improvement_areas) {\n        session.improvement_areas.forEach((area: string) => \n          weeklyData[week].improvementAreas.add(area)\n        );\n      }\n    });\n\n    return Object.values(weeklyData).map((week: any) => ({\n      ...week,\n      averageScore: Math.round(week.totalScore / week.sessionsCompleted),\n      improvementAreas: Array.from(week.improvementAreas),\n    }));\n  }, []);\n\n  const processSkillProgression = useCallback((skillHistory: any[], timeframe: string) => {\n    // Process skill progression based on timeframe\n    const now = new Date();\n    const cutoffDate = new Date();\n    \n    switch (timeframe) {\n      case 'week':\n        cutoffDate.setDate(now.getDate() - 7);\n        break;\n      case 'month':\n        cutoffDate.setMonth(now.getMonth() - 1);\n        break;\n      case 'year':\n        cutoffDate.setFullYear(now.getFullYear() - 1);\n        break;\n    }\n\n    return skillHistory\n      .filter(stat => new Date(stat.updated_at) >= cutoffDate)\n      .map(stat => ({\n        date: stat.updated_at,\n        forehand: stat.forehand,\n        backhand: stat.backhand,\n        serve: stat.serve,\n        volley: stat.volley,\n        footwork: stat.footwork,\n        strategy: stat.strategy,\n        mental_game: stat.mental_game,\n      }));\n  }, []);\n\n  const calculatePeerAverages = useCallback((peerStats: any[]) => {\n    const totals = {\n      forehand: 0, backhand: 0, serve: 0, volley: 0,\n      footwork: 0, strategy: 0, mental_game: 0\n    };\n    \n    let count = 0;\n    peerStats.forEach(peer => {\n      if (peer.skill_stats) {\n        Object.keys(totals).forEach(skill => {\n          totals[skill as keyof typeof totals] += peer.skill_stats[skill] || 0;\n        });\n        count++;\n      }\n    });\n\n    if (count === 0) return totals;\n\n    Object.keys(totals).forEach(skill => {\n      totals[skill as keyof typeof totals] = Math.round(totals[skill as keyof typeof totals] / count);\n    });\n\n    return totals;\n  }, []);\n\n  const generatePeerComparison = useCallback((userStats: any, peerAverages: any) => {\n    const comparison: { [key: string]: any } = {};\n    \n    Object.keys(peerAverages).forEach(skill => {\n      const userValue = userStats[skill] || 0;\n      const peerValue = peerAverages[skill] || 0;\n      const difference = userValue - peerValue;\n      \n      comparison[skill] = {\n        user: userValue,\n        peer: peerValue,\n        difference,\n        percentile: calculatePercentile(difference),\n      };\n    });\n\n    return comparison;\n  }, []);\n\n  const calculatePercentile = useCallback((difference: number): number => {\n    // Simple percentile calculation based on difference\n    if (difference > 15) return 90;\n    if (difference > 10) return 80;\n    if (difference > 5) return 70;\n    if (difference > 0) return 60;\n    if (difference > -5) return 50;\n    if (difference > -10) return 40;\n    if (difference > -15) return 30;\n    return 20;\n  }, []);\n\n  return {\n    performanceData,\n    matchAnalysis,\n    loading,\n    error,\n    analyzeMatch,\n    generatePerformanceReport,\n    getSkillProgression,\n    compareWithPeers,\n    refreshAnalytics,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ,EAAEC,WAAW,EAAaC,OAAO,QAAQ,OAAO;AAEjE,SAASC,OAAO;AAChB,SAASC,QAAQ;AACjB,SAASC,kBAAkB,EAAEC,qBAAqB;AAClD,SAASC,sBAAsB,EAAEC,gBAAgB;AACjD,SAASC,wBAAwB;AAoCjC,OAAO,SAASC,uBAAuBA,CAAA,EAAkC;EAAAC,aAAA,GAAAC,CAAA;EACvE,IAAAC,IAAA,IAAAF,aAAA,GAAAG,CAAA,OAA0Cd,QAAQ,CAA6B,IAAI,CAAC;IAAAe,KAAA,GAAAC,cAAA,CAAAH,IAAA;IAA7EI,aAAa,GAAAF,KAAA;IAAEG,gBAAgB,GAAAH,KAAA;EACtC,IAAAI,KAAA,IAAAR,aAAA,GAAAG,CAAA,OAA8Bd,QAAQ,CAAC,KAAK,CAAC;IAAAoB,KAAA,GAAAJ,cAAA,CAAAG,KAAA;IAAtCE,OAAO,GAAAD,KAAA;IAAEE,UAAU,GAAAF,KAAA;EAC1B,IAAAG,KAAA,IAAAZ,aAAA,GAAAG,CAAA,OAA0Bd,QAAQ,CAAgB,IAAI,CAAC;IAAAwB,KAAA,GAAAR,cAAA,CAAAO,KAAA;IAAhDE,KAAK,GAAAD,KAAA;IAAEE,QAAQ,GAAAF,KAAA;EACtB,IAAAG,KAAA,IAAAhB,aAAA,GAAAG,CAAA,OAAiBX,OAAO,CAAC,CAAC;IAAlByB,IAAI,GAAAD,KAAA,CAAJC,IAAI;EAGZ,IAAAC,KAAA,IAAAlB,aAAA,GAAAG,CAAA,OAMIT,kBAAkB,CAAC;MACrByB,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,IAAI;MACjBC,eAAe,EAAE;IACnB,CAAC,CAAC;IAVMC,kBAAkB,GAAAL,KAAA,CAAxBM,IAAI;IACKC,WAAW,GAAAP,KAAA,CAApBR,OAAO;IACAgB,SAAS,GAAAR,KAAA,CAAhBJ,KAAK;IACLa,SAAS,GAAAT,KAAA,CAATS,SAAS;IACTC,WAAW,GAAAV,KAAA,CAAXU,WAAW;EASb,IAAMC,kBAAkB,IAAA7B,aAAA,GAAAG,CAAA,OAAGR,qBAAqB,CAAC4B,kBAAkB,CAAC;EAGpE,IAAMO,cAAc,IAAA9B,aAAA,GAAAG,CAAA,OAAGP,sBAAsB,CAAC2B,kBAAkB,EAAE;IAChEQ,YAAY,EAAE,IAAI;IAClBC,iBAAiB,EAAE,IAAI;IACvBC,qBAAqB,EAAE,IAAI;IAC3BC,aAAa,EAAE;EACjB,CAAC,CAAC;EAGF,IAAMC,qBAAqB,IAAAnC,aAAA,GAAAG,CAAA,OAAGN,gBAAgB,CAAC,CAAC;EAGhD,IAAMuC,eAAe,IAAApC,aAAA,GAAAG,CAAA,OAAGZ,OAAO,CAAC,YAA8B;IAAAS,aAAA,GAAAC,CAAA;IAAAD,aAAA,GAAAG,CAAA;IAC5D,IAAI,CAAAH,aAAA,GAAAqC,CAAA,WAACP,cAAc,MAAA9B,aAAA,GAAAqC,CAAA,UAAI,CAACR,kBAAkB,GAAE;MAAA7B,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;MAAAH,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAExD,OAAO;MACLG,aAAa,EAAE,CAAAN,aAAA,GAAAqC,CAAA,UAAAR,kBAAkB,CAACS,aAAa,CAAC,CAAC,CAAC,MAAAtC,aAAA,GAAAqC,CAAA,UAAI,IAAI;MAC1DE,MAAM,EAAET,cAAc,CAACS,MAAM;MAC7BC,QAAQ,EAAE;QACRC,SAAS,EAAEX,cAAc,CAACW,SAAS;QACnCC,UAAU,EAAEZ,cAAc,CAACY,UAAU;QACrCC,eAAe,EAAEb,cAAc,CAACa;MAClC,CAAC;MACDC,WAAW,EAAEd,cAAc,CAACc;IAC9B,CAAC;EACH,CAAC,EAAE,CAACd,cAAc,EAAED,kBAAkB,CAAC,CAAC;EAKxC,IAAMgB,YAAY,IAAA7C,aAAA,GAAAG,CAAA,QAAGb,WAAW;IAAA,IAAAwD,KAAA,GAAAC,iBAAA,CAAC,WAAOC,UAA2B,EAAEC,YAAkB,EAAK;MAAAjD,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MAC1F,IAAI,CAACc,IAAI,EAAE;QAAAjB,aAAA,GAAAqC,CAAA;QAAArC,aAAA,GAAAG,CAAA;QAAA;MAAM,CAAC;QAAAH,aAAA,GAAAqC,CAAA;MAAA;MAAArC,aAAA,GAAAG,CAAA;MAElB,IAAI;QAAAH,aAAA,GAAAG,CAAA;QACFQ,UAAU,CAAC,IAAI,CAAC;QAACX,aAAA,GAAAG,CAAA;QACjBY,QAAQ,CAAC,IAAI,CAAC;QAGd,IAAMe,eAAc,IAAA9B,aAAA,GAAAG,CAAA,cAASgC,qBAAqB,CAACa,UAAU,EAAEC,YAAY,CAAC;QAG5E,IAAMN,eAAyB,IAAA3C,aAAA,GAAAG,CAAA,QAAG,EAAE;QAACH,aAAA,GAAAG,CAAA;QAErC,IAAI2B,eAAc,CAACoB,aAAa,GAAG,EAAE,EAAE;UAAAlD,aAAA,GAAAqC,CAAA;UAAArC,aAAA,GAAAG,CAAA;UACrCwC,eAAe,CAACQ,IAAI,CAAC,4CAA4C,CAAC;QACpE,CAAC;UAAAnD,aAAA,GAAAqC,CAAA;QAAA;QAAArC,aAAA,GAAAG,CAAA;QAED,IAAI2B,eAAc,CAACsB,eAAe,CAACC,SAAS,GAAG,EAAE,EAAE;UAAArD,aAAA,GAAAqC,CAAA;UAAArC,aAAA,GAAAG,CAAA;UACjDwC,eAAe,CAACQ,IAAI,CAAC,kCAAkC,CAAC;QAC1D,CAAC;UAAAnD,aAAA,GAAAqC,CAAA;QAAA;QAAArC,aAAA,GAAAG,CAAA;QAED,IAAI2B,eAAc,CAACsB,eAAe,CAACE,UAAU,GAAG,EAAE,EAAE;UAAAtD,aAAA,GAAAqC,CAAA;UAAArC,aAAA,GAAAG,CAAA;UAClDwC,eAAe,CAACQ,IAAI,CAAC,iCAAiC,CAAC;QACzD,CAAC;UAAAnD,aAAA,GAAAqC,CAAA;QAAA;QAED,IAAMkB,MAA2B,IAAAvD,aAAA,GAAAG,CAAA,QAAG;UAClC+C,aAAa,EAAEpB,eAAc,CAACoB,aAAa;UAC3CE,eAAe,EAAEtB,eAAc,CAACsB,eAAe;UAC/CI,gBAAgB,EAAE1B,eAAc,CAAC0B,gBAAgB;UACjDC,eAAe,EAAE3B,eAAc,CAAC2B,eAAe;UAC/Cd,eAAe,EAAfA;QACF,CAAC;QAAC3C,aAAA,GAAAG,CAAA;QAEFI,gBAAgB,CAACgD,MAAM,CAAC;QAACvD,aAAA,GAAAG,CAAA;QAGzB,MAAML,wBAAwB,CAAC4D,WAAW,CAAC,eAAe,EAAE,CAAC;UAC3DC,OAAO,EAAE1C,IAAI,CAAC2C,EAAE;UAChBC,aAAa,EAAE,CAAA7D,aAAA,GAAAqC,CAAA,UAAAY,YAAY,oBAAZA,YAAY,CAAEa,IAAI,MAAA9D,aAAA,GAAAqC,CAAA,UAAI,kBAAkB;UACvD0B,aAAa,EAAE,IAAI;UACnBC,WAAW,EAAE,GAAGhB,UAAU,CAACiB,SAAS,IAAIjB,UAAU,CAACkB,WAAW,GAAGlB,UAAU,CAACiB,SAAS,EAAE;UACvFE,IAAI,EAAE,CAACnB,UAAU,CAACiB,SAAS,CAAC;UAC5BG,aAAa,EAAE,CAACpB,UAAU,CAACkB,WAAW,GAAGlB,UAAU,CAACiB,SAAS,CAAC;UAC9DI,OAAO,EAAE,CAAArE,aAAA,GAAAqC,CAAA,UAAAY,YAAY,oBAAZA,YAAY,CAAEoB,OAAO,MAAArE,aAAA,GAAAqC,CAAA,UAAI,MAAM;UACxCiC,gBAAgB,EAAEtB,UAAU,CAACuB,aAAa;UAC1ChB,MAAM,EAAEP,UAAU,CAACiB,SAAS,GAAIjB,UAAU,CAACkB,WAAW,GAAG,CAAE,IAAAlE,aAAA,GAAAqC,CAAA,UAAG,KAAK,KAAArC,aAAA,GAAAqC,CAAA,UAAG,MAAM;UAC5EmC,WAAW,EAAAC,MAAA,CAAAC,MAAA,KACN1B,UAAU;YACb2B,QAAQ,EAAEpB;UAAM;QAEpB,CAAC,CAAC,CAAC;MAEL,CAAC,CAAC,OAAOqB,GAAG,EAAE;QAAA5E,aAAA,GAAAG,CAAA;QACZY,QAAQ,CAAC6D,GAAG,YAAYC,KAAK,IAAA7E,aAAA,GAAAqC,CAAA,WAAGuC,GAAG,CAACE,OAAO,KAAA9E,aAAA,GAAAqC,CAAA,WAAG,yBAAyB,EAAC;QAACrC,aAAA,GAAAG,CAAA;QACzE4E,OAAO,CAACjE,KAAK,CAAC,uBAAuB,EAAE8D,GAAG,CAAC;MAC7C,CAAC,SAAS;QAAA5E,aAAA,GAAAG,CAAA;QACRQ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAAA,iBAAAqE,EAAA,EAAAC,GAAA;MAAA,OAAAnC,KAAA,CAAAoC,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,CAAClE,IAAI,EAAEkB,qBAAqB,CAAC,CAAC;EAKjC,IAAMiD,yBAAyB,IAAApF,aAAA,GAAAG,CAAA,QAAGb,WAAW,CAAAyD,iBAAA,CAAC,aAAY;IAAA/C,aAAA,GAAAC,CAAA;IAAAD,aAAA,GAAAG,CAAA;IACxD,IAAI,CAACc,IAAI,EAAE;MAAAjB,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAAA;IAAM,CAAC;MAAAH,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAElB,IAAI;MAAAH,aAAA,GAAAG,CAAA;MACFQ,UAAU,CAAC,IAAI,CAAC;MAACX,aAAA,GAAAG,CAAA;MACjBY,QAAQ,CAAC,IAAI,CAAC;MAACf,aAAA,GAAAG,CAAA;MAGf,MAAMwB,SAAS,CAAC,IAAI,CAAC;IAEvB,CAAC,CAAC,OAAOiD,GAAG,EAAE;MAAA5E,aAAA,GAAAG,CAAA;MACZY,QAAQ,CAAC6D,GAAG,YAAYC,KAAK,IAAA7E,aAAA,GAAAqC,CAAA,WAAGuC,GAAG,CAACE,OAAO,KAAA9E,aAAA,GAAAqC,CAAA,WAAG,uCAAuC,EAAC;MAACrC,aAAA,GAAAG,CAAA;MACvF4E,OAAO,CAACjE,KAAK,CAAC,2BAA2B,EAAE8D,GAAG,CAAC;IACjD,CAAC,SAAS;MAAA5E,aAAA,GAAAG,CAAA;MACRQ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,GAAE,CAACM,IAAI,EAAEU,SAAS,CAAC,CAAC;EAKrB,IAAM0D,mBAAmB,IAAArF,aAAA,GAAAG,CAAA,QAAGb,WAAW;IAAA,IAAAgG,KAAA,GAAAvC,iBAAA,CAAC,WAAOwC,SAAoC,EAAK;MAAAvF,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MACtF,IAAI,CAACc,IAAI,EAAE;QAAAjB,aAAA,GAAAqC,CAAA;QAAArC,aAAA,GAAAG,CAAA;QAAA,OAAO,IAAI;MAAA,CAAC;QAAAH,aAAA,GAAAqC,CAAA;MAAA;MAAArC,aAAA,GAAAG,CAAA;MAEvB,IAAI;QAEF,IAAMiC,gBAAe,IAAApC,aAAA,GAAAG,CAAA,cAASL,wBAAwB,CAAC0F,kBAAkB,CAACvE,IAAI,CAAC2C,EAAE,EAAE2B,SAAS,CAAC;QAACvF,aAAA,GAAAG,CAAA;QAE9F,IAAI,CAAAH,aAAA,GAAAqC,CAAA,YAACD,gBAAe,CAACqD,UAAU,MAAAzF,aAAA,GAAAqC,CAAA,WAAID,gBAAe,CAACqD,UAAU,CAACC,MAAM,KAAK,CAAC,GAAE;UAAA1F,aAAA,GAAAqC,CAAA;UAAArC,aAAA,GAAAG,CAAA;UAC1E,OAAO,IAAI;QACb,CAAC;UAAAH,aAAA,GAAAqC,CAAA;QAAA;QAAArC,aAAA,GAAAG,CAAA;QAGD,OAAOwF,uBAAuB,CAACvD,gBAAe,CAACqD,UAAU,EAAEF,SAAS,CAAC;MAEvE,CAAC,CAAC,OAAOzE,KAAK,EAAE;QAAAd,aAAA,GAAAG,CAAA;QACd4E,OAAO,CAACjE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QAACd,aAAA,GAAAG,CAAA;QAC1D,OAAO,IAAI;MACb;IACF,CAAC;IAAA,iBAAAyF,GAAA;MAAA,OAAAN,KAAA,CAAAJ,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,CAAClE,IAAI,CAAC,CAAC;EAKV,IAAM4E,gBAAgB,IAAA7F,aAAA,GAAAG,CAAA,QAAGb,WAAW;IAAA,IAAAwG,MAAA,GAAA/C,iBAAA,CAAC,WAAOgD,UAAkB,EAAK;MAAA/F,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MACjE,IAAI,CAACc,IAAI,EAAE;QAAAjB,aAAA,GAAAqC,CAAA;QAAArC,aAAA,GAAAG,CAAA;QAAA,OAAO,IAAI;MAAA,CAAC;QAAAH,aAAA,GAAAqC,CAAA;MAAA;MAAArC,aAAA,GAAAG,CAAA;MAEvB,IAAI;QAEF,IAAA6F,MAAA,IAAAhG,aAAA,GAAAG,CAAA,cAAkCV,QAAQ,CACvCwG,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAElF,IAAI,CAAC2C,EAAE,CAAC,CACtBwC,MAAM,CAAC,CAAC;UAJGC,SAAS,GAAAL,MAAA,CAAfxE,IAAI;QAIAxB,aAAA,GAAAG,CAAA;QAEZ,IAAI,CAACkG,SAAS,EAAE;UAAArG,aAAA,GAAAqC,CAAA;UAAArC,aAAA,GAAAG,CAAA;UAAA,OAAO,IAAI;QAAA,CAAC;UAAAH,aAAA,GAAAqC,CAAA;QAAA;QAG5B,IAAAiE,MAAA,IAAAtG,aAAA,GAAAG,CAAA,cAAkCV,QAAQ,CACvCwG,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC;AAChB;AACA;AACA;AACA,SAAS,CAAC,CACDC,EAAE,CAAC,aAAa,EAAEJ,UAAU,CAAC,CAC7BQ,KAAK,CAAC,EAAE,CAAC;UAREC,SAAS,GAAAF,MAAA,CAAf9E,IAAI;QAQCxB,aAAA,GAAAG,CAAA;QAEb,IAAI,CAAAH,aAAA,GAAAqC,CAAA,YAACmE,SAAS,MAAAxG,aAAA,GAAAqC,CAAA,WAAImE,SAAS,CAACd,MAAM,KAAK,CAAC,GAAE;UAAA1F,aAAA,GAAAqC,CAAA;UAAArC,aAAA,GAAAG,CAAA;UACxC,OAAO,IAAI;QACb,CAAC;UAAAH,aAAA,GAAAqC,CAAA;QAAA;QAGD,IAAMoE,YAAY,IAAAzG,aAAA,GAAAG,CAAA,QAAGuG,qBAAqB,CAACF,SAAS,CAAC;QAACxG,aAAA,GAAAG,CAAA;QAGtD,OAAOwG,sBAAsB,CAACN,SAAS,EAAEI,YAAY,CAAC;MAExD,CAAC,CAAC,OAAO3F,KAAK,EAAE;QAAAd,aAAA,GAAAG,CAAA;QACd4E,OAAO,CAACjE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QAACd,aAAA,GAAAG,CAAA;QACpD,OAAO,IAAI;MACb;IACF,CAAC;IAAA,iBAAAyG,GAAA;MAAA,OAAAd,MAAA,CAAAZ,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,CAAClE,IAAI,CAAC,CAAC;EAKV,IAAM4F,gBAAgB,IAAA7G,aAAA,GAAAG,CAAA,QAAGb,WAAW,CAAAyD,iBAAA,CAAC,aAAY;IAAA/C,aAAA,GAAAC,CAAA;IAAAD,aAAA,GAAAG,CAAA;IAC/C,MAAMyB,WAAW,CAAC,CAAC;EACrB,CAAC,GAAE,CAACA,WAAW,CAAC,CAAC;EAAC5B,aAAA,GAAAG,CAAA;EAGlB,OAAOZ,OAAO,CAAC,YAAO;IAAAS,aAAA,GAAAC,CAAA;IAAAD,aAAA,GAAAG,CAAA;IAAA;MACpBiC,eAAe,EAAfA,eAAe;MACf9B,aAAa,EAAbA,aAAa;MACbI,OAAO,EAAE,CAAAV,aAAA,GAAAqC,CAAA,WAAA3B,OAAO,MAAAV,aAAA,GAAAqC,CAAA,WAAIZ,WAAW;MAC/BX,KAAK,EAAE,CAAAd,aAAA,GAAAqC,CAAA,WAAAvB,KAAK,MAAAd,aAAA,GAAAqC,CAAA,WAAIX,SAAS;MACzBmB,YAAY,EAAZA,YAAY;MACZuC,yBAAyB,EAAzBA,yBAAyB;MACzBC,mBAAmB,EAAnBA,mBAAmB;MACnBQ,gBAAgB,EAAhBA,gBAAgB;MAChBgB,gBAAgB,EAAhBA;IACF,CAAC;EAAD,CAAE,EAAE,CACFzE,eAAe,EACf9B,aAAa,EACbI,OAAO,EACPe,WAAW,EACXX,KAAK,EACLY,SAAS,EACTmB,YAAY,EACZuC,yBAAyB,EACzBC,mBAAmB,EACnBQ,gBAAgB,EAChBgB,gBAAgB,CACjB,CAAC;EAIF,IAAMC,0BAA0B,IAAA9G,aAAA,GAAAG,CAAA,QAAGb,WAAW;IAAA,IAAAyH,MAAA,GAAAhE,iBAAA,CAAC,WAAOiE,MAAc,EAAK;MAAAhH,aAAA,GAAAC,CAAA;MACvE,IAAAgH,MAAA,IAAAjH,aAAA,GAAAG,CAAA,cAAgCV,QAAQ,CACrCwG,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEa,MAAM,CAAC,CACrBE,KAAK,CAAC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAM,CAAC,CAAC,CACzCZ,KAAK,CAAC,EAAE,CAAC;QALEa,OAAO,GAAAH,MAAA,CAAbzF,IAAI;MAOZ,IAAA6F,MAAA,IAAArH,aAAA,GAAAG,CAAA,cAAiCV,QAAQ,CACtCwG,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEa,MAAM,CAAC,CACrBE,KAAK,CAAC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAM,CAAC,CAAC,CACzCZ,KAAK,CAAC,EAAE,CAAC;QALEe,QAAQ,GAAAD,MAAA,CAAd7F,IAAI;MAOZ,IAAA+F,MAAA,IAAAvH,aAAA,GAAAG,CAAA,cAAmCV,QAAQ,CACxCwG,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEa,MAAM,CAAC,CACrBE,KAAK,CAAC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAM,CAAC,CAAC,CACzCZ,KAAK,CAAC,CAAC,CAAC;QALGd,UAAU,GAAA8B,MAAA,CAAhB/F,IAAI;MAKAxB,aAAA,GAAAG,CAAA;MAEZ,OAAO;QACLiH,OAAO,EAAE,CAAApH,aAAA,GAAAqC,CAAA,WAAA+E,OAAO,MAAApH,aAAA,GAAAqC,CAAA,WAAI,EAAE;QACtBiF,QAAQ,EAAE,CAAAtH,aAAA,GAAAqC,CAAA,WAAAiF,QAAQ,MAAAtH,aAAA,GAAAqC,CAAA,WAAI,EAAE;QACxBoD,UAAU,EAAE,CAAAzF,aAAA,GAAAqC,CAAA,WAAAoD,UAAU,MAAAzF,aAAA,GAAAqC,CAAA,WAAI,EAAE;MAC9B,CAAC;IACH,CAAC;IAAA,iBAAAmF,GAAA;MAAA,OAAAT,MAAA,CAAA7B,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,EAAE,CAAC;EAEN,IAAMsC,aAAa,IAAAzH,aAAA,GAAAG,CAAA,QAAGb,WAAW,CAAC,UAACkC,IAAS,EAAwB;IAAAxB,aAAA,GAAAC,CAAA;IAElE,IAAMyH,gBAAgB,IAAA1H,aAAA,GAAAG,CAAA,QAAGqB,IAAI,CAACiE,UAAU,CAACC,MAAM,GAAG,CAAC,IAAA1F,aAAA,GAAAqC,CAAA,WAC/CsF,oBAAoB,CAACnG,IAAI,CAACiE,UAAU,CAAC,KAAAzF,aAAA,GAAAqC,CAAA,WACrC,EAAE;IAGN,IAAMuF,gBAAgB,IAAA5H,aAAA,GAAAG,CAAA,QAAGqB,IAAI,CAAC4F,OAAO,CAACS,GAAG,CAAC,UAACC,KAAU,EAAM;MAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA;MAAAjI,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MAAA;QACzD+H,IAAI,EAAEJ,KAAK,CAACK,UAAU;QACtBC,QAAQ,EAAEN,KAAK,CAACjE,aAAa;QAC7BN,MAAM,EAAEuE,KAAK,CAACvE,MAAM;QACpB8E,KAAK,EAAEP,KAAK,CAAC9D,WAAW;QACxBsE,UAAU,EAAE;UACVC,OAAO,EAAE,CAAAvI,aAAA,GAAAqC,CAAA,YAAA0F,kBAAA,GAAAD,KAAK,CAACtD,WAAW,qBAAjBuD,kBAAA,CAAmBQ,OAAO,MAAAvI,aAAA,GAAAqC,CAAA,WAAI,CAAC;UACxCmG,MAAM,EAAE,CAAAxI,aAAA,GAAAqC,CAAA,YAAA2F,mBAAA,GAAAF,KAAK,CAACtD,WAAW,qBAAjBwD,mBAAA,CAAmBQ,MAAM,MAAAxI,aAAA,GAAAqC,CAAA,WAAI,CAAC;UACtCoG,oBAAoB,EAAE,CAAAzI,aAAA,GAAAqC,CAAA,YAAA4F,mBAAA,GAAAH,KAAK,CAACtD,WAAW,qBAAjByD,mBAAA,CAAmBQ,oBAAoB,MAAAzI,aAAA,GAAAqC,CAAA,WAAI,CAAC;QACpE;MACF,CAAC;IAAD,CAAE,CAAC;IAGH,IAAMqG,cAAc,IAAA1I,aAAA,GAAAG,CAAA,QAAGwI,uBAAuB,CAACnH,IAAI,CAAC8F,QAAQ,CAAC;IAACtH,aAAA,GAAAG,CAAA;IAE9D,OAAO;MACLuH,gBAAgB,EAAhBA,gBAAgB;MAChBE,gBAAgB,EAAhBA,gBAAgB;MAChBc,cAAc,EAAdA;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAME,gBAAgB,IAAA5I,aAAA,GAAAG,CAAA,QAAGb,WAAW,CAAC,UAACkC,IAAS,EAAEe,MAAyB,EAAK;IAAAvC,aAAA,GAAAC,CAAA;IAC7E,IAAMwC,SAAmB,IAAAzC,aAAA,GAAAG,CAAA,QAAG,EAAE;IAC9B,IAAMuC,UAAoB,IAAA1C,aAAA,GAAAG,CAAA,QAAG,EAAE;IAC/B,IAAMwC,eAAyB,IAAA3C,aAAA,GAAAG,CAAA,QAAG,EAAE;IAGpC,IAAM0I,UAAU,IAAA7I,aAAA,GAAAG,CAAA,QAAGqB,IAAI,CAAC4F,OAAO,CAAC0B,MAAM,CAAC,UAACC,CAAM,EAAK;MAAA/I,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MAAA,OAAA4I,CAAC,CAACxF,MAAM,KAAK,KAAK;IAAD,CAAC,CAAC,CAACmC,MAAM;IAC7E,IAAMsD,OAAO,IAAAhJ,aAAA,GAAAG,CAAA,QAAGqB,IAAI,CAAC4F,OAAO,CAAC1B,MAAM,GAAG,CAAC,IAAA1F,aAAA,GAAAqC,CAAA,WAAIwG,UAAU,GAAGrH,IAAI,CAAC4F,OAAO,CAAC1B,MAAM,GAAI,GAAG,KAAA1F,aAAA,GAAAqC,CAAA,WAAG,CAAC;IAACrC,aAAA,GAAAG,CAAA;IAEvF,IAAI6I,OAAO,GAAG,EAAE,EAAE;MAAAhJ,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAChBsC,SAAS,CAACU,IAAI,CAAC,iCAAiC,CAAC;IACnD,CAAC,MAAM;MAAAnD,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAAA,IAAI6I,OAAO,GAAG,EAAE,EAAE;QAAAhJ,aAAA,GAAAqC,CAAA;QAAArC,aAAA,GAAAG,CAAA;QACvBuC,UAAU,CAACS,IAAI,CAAC,8BAA8B,CAAC;QAACnD,aAAA,GAAAG,CAAA;QAChDwC,eAAe,CAACQ,IAAI,CAAC,kCAAkC,CAAC;MAC1D,CAAC;QAAAnD,aAAA,GAAAqC,CAAA;MAAA;IAAD;IAGA,IAAM4G,eAAe,IAAAjJ,aAAA,GAAAG,CAAA,QAAGoC,MAAM,CAACmF,gBAAgB,CAACoB,MAAM,CAAC,UAAA3I,CAAC,EAAI;MAAAH,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MAAA,OAAAA,CAAC,CAAC+I,KAAK,KAAK,WAAW;IAAD,CAAC,CAAC;IACpF,IAAMC,eAAe,IAAAnJ,aAAA,GAAAG,CAAA,QAAGoC,MAAM,CAACmF,gBAAgB,CAACoB,MAAM,CAAC,UAAA3I,CAAC,EAAI;MAAAH,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MAAA,OAAAA,CAAC,CAAC+I,KAAK,KAAK,WAAW;IAAD,CAAC,CAAC;IAAClJ,aAAA,GAAAG,CAAA;IAErF,IAAI8I,eAAe,CAACvD,MAAM,GAAG,CAAC,EAAE;MAAA1F,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAC9BsC,SAAS,CAACU,IAAI,CAAC,gBAAgB8F,eAAe,CAACpB,GAAG,CAAC,UAAA1H,CAAC,EAAI;QAAAH,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAG,CAAA;QAAA,OAAAA,CAAC,CAACiJ,KAAK;MAAD,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAChF,CAAC;MAAArJ,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAED,IAAIgJ,eAAe,CAACzD,MAAM,GAAG,CAAC,EAAE;MAAA1F,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAC9BuC,UAAU,CAACS,IAAI,CAAC,gBAAgBgG,eAAe,CAACtB,GAAG,CAAC,UAAA1H,CAAC,EAAI;QAAAH,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAG,CAAA;QAAA,OAAAA,CAAC,CAACiJ,KAAK;MAAD,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAACrJ,aAAA,GAAAG,CAAA;MAChFwC,eAAe,CAACQ,IAAI,CAAC,qBAAqBgG,eAAe,CAAC,CAAC,CAAC,CAACC,KAAK,EAAE,CAAC;IACvE,CAAC;MAAApJ,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAED,OAAO;MAAEsC,SAAS,EAATA,SAAS;MAAEC,UAAU,EAAVA,UAAU;MAAEC,eAAe,EAAfA;IAAgB,CAAC;EACnD,CAAC,EAAE,EAAE,CAAC;EAEN,IAAM2G,mBAAmB,IAAAtJ,aAAA,GAAAG,CAAA,SAAGb,WAAW,CAAC,UAACiD,MAAyB,EAAK;IAAAvC,aAAA,GAAAC,CAAA;IACrE,IAAMyH,gBAAgB,IAAA1H,aAAA,GAAAG,CAAA,SAAGoC,MAAM,CAACmF,gBAAgB,CAC7CoB,MAAM,CAAC,UAAA3I,CAAC,EAAI;MAAAH,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MAAA,OAAAA,CAAC,CAAC+I,KAAK,KAAK,WAAW;IAAD,CAAC,CAAC,CACpCrB,GAAG,CAAC,UAAA1H,CAAC,EAAK;MAAAH,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MAAA;QACTiJ,KAAK,EAAEjJ,CAAC,CAACiJ,KAAK;QACdG,aAAa,EAAEpJ,CAAC,CAACoJ,aAAa;QAC9BC,eAAe,EAAEC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEvJ,CAAC,CAACoJ,aAAa,GAAIpJ,CAAC,CAACwJ,UAAU,GAAG,CAAE,CAAC;QACpEpE,SAAS,EAAE;MACb,CAAC;IAAD,CAAE,CAAC;IAEL,IAAMqE,cAAc,IAAA5J,aAAA,GAAAG,CAAA,SAAG,CACrB,mCAAmC,EACnC,iCAAiC,EACjC,2BAA2B,CAC5B;IAACH,aAAA,GAAAG,CAAA;IAEF,OAAO;MAAEuH,gBAAgB,EAAhBA,gBAAgB;MAAEkC,cAAc,EAAdA;IAAe,CAAC;EAC7C,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMC,sBAAsB,IAAA7J,aAAA,GAAAG,CAAA,SAAGb,WAAW,CAAC,UAAC0D,UAA2B,EAAwB;IAAAhD,aAAA,GAAAC,CAAA;IAE7F,IAAMkE,IAAI,IAAAnE,aAAA,GAAAG,CAAA,SAAG,CAAC;IAACH,aAAA,GAAAG,CAAA;IACf,OAAO2J,KAAK,CAAC7D,IAAI,CAAC;MAAEP,MAAM,EAAEvB;IAAK,CAAC,EAAE,UAAC4F,CAAC,EAAEC,CAAC,EAAM;MAAAhK,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MAAA,OAAAsE,MAAA,CAAAC,MAAA,KAC1C1B,UAAU;QACbkB,WAAW,EAAEuF,IAAI,CAACQ,KAAK,CAACjH,UAAU,CAACkB,WAAW,GAAGC,IAAI,CAAC;QACtDF,SAAS,EAAEwF,IAAI,CAACQ,KAAK,CAACjH,UAAU,CAACiB,SAAS,GAAGE,IAAI,CAAC,GAAI6F,CAAC,GAAG,CAAE;QAC5DE,cAAc,EAAET,IAAI,CAACQ,KAAK,CAACjH,UAAU,CAACkH,cAAc,GAAG/F,IAAI,CAAC,GAAG6F;MAAC;IAClE,CAAE,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMG,4BAA4B,IAAAnK,aAAA,GAAAG,CAAA,SAAGb,WAAW,CAAC,UAC/C8K,aAAkB,EAClBC,gBAAqB,EACrB5G,eAAoB,EACP;IAAAzD,aAAA,GAAAC,CAAA;IACb,IAAM0C,eAAyB,IAAA3C,aAAA,GAAAG,CAAA,SAAG,EAAE;IAACH,aAAA,GAAAG,CAAA;IAErC,IAAIiK,aAAa,CAAClH,aAAa,GAAG,EAAE,EAAE;MAAAlD,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MACpCwC,eAAe,CAACQ,IAAI,CAAC,4CAA4C,CAAC;IACpE,CAAC;MAAAnD,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAED,IAAI,CAAAkK,gBAAgB,oBAAhBA,gBAAgB,CAAEC,qBAAqB,IAAG,EAAE,EAAE;MAAAtK,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAChDwC,eAAe,CAACQ,IAAI,CAAC,+CAA+C,CAAC;IACvE,CAAC;MAAAnD,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAED,IAAIsD,eAAe,CAAC8G,eAAe,GAAG,EAAE,EAAE;MAAAvK,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MACxCwC,eAAe,CAACQ,IAAI,CAAC,mDAAmD,CAAC;IAC3E,CAAC;MAAAnD,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAED,OAAOwC,eAAe;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAM6H,iBAAiB,IAAAxK,aAAA,GAAAG,CAAA,SAAGb,WAAW;IAAA,IAAAmL,MAAA,GAAA1H,iBAAA,CAAC,WAAOC,UAA2B,EAAE2B,QAA6B,EAAK;MAAA3E,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MAC1G,IAAI;QAAAH,aAAA,GAAAG,CAAA;QACF,MAAMV,QAAQ,CACXwG,IAAI,CAAC,eAAe,CAAC,CACrByE,MAAM,CAAC;UACN/G,OAAO,EAAE1C,IAAI,oBAAJA,IAAI,CAAE2C,EAAE;UACjBC,aAAa,EAAE,kBAAkB;UACjCE,aAAa,EAAE,IAAI;UACnBC,WAAW,EAAE,GAAGhB,UAAU,CAACiB,SAAS,IAAIjB,UAAU,CAACkB,WAAW,GAAGlB,UAAU,CAACiB,SAAS,EAAE;UACvFE,IAAI,EAAE,CAACnB,UAAU,CAACiB,SAAS,CAAC;UAC5BG,aAAa,EAAE,CAACpB,UAAU,CAACkB,WAAW,GAAGlB,UAAU,CAACiB,SAAS,CAAC;UAC9DI,OAAO,EAAE,MAAM;UACfC,gBAAgB,EAAEtB,UAAU,CAACuB,aAAa;UAC1ChB,MAAM,EAAEP,UAAU,CAACiB,SAAS,GAAIjB,UAAU,CAACkB,WAAW,GAAG,CAAE,IAAAlE,aAAA,GAAAqC,CAAA,WAAG,KAAK,KAAArC,aAAA,GAAAqC,CAAA,WAAG,MAAM;UAC5EmC,WAAW,EAAAC,MAAA,CAAAC,MAAA,KACN1B,UAAU;YACb2B,QAAQ,EAAEA;UAAQ;QAEtB,CAAC,CAAC;MACN,CAAC,CAAC,OAAO7D,KAAK,EAAE;QAAAd,aAAA,GAAAG,CAAA;QACd4E,OAAO,CAACjE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAAA,iBAAA6J,GAAA,EAAAC,GAAA;MAAA,OAAAH,MAAA,CAAAvF,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,CAAClE,IAAI,CAAC,CAAC;EAEV,IAAM0G,oBAAoB,IAAA3H,aAAA,GAAAG,CAAA,SAAGb,WAAW,CAAC,UAACmG,UAAiB,EAMrD;IAAAzF,aAAA,GAAAC,CAAA;IAEJ,IAAM4K,MAAM,IAAA7K,aAAA,GAAAG,CAAA,SAAG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,CAAC;IAACH,aAAA,GAAAG,CAAA;IAElG,OAAO0K,MAAM,CAAChD,GAAG,CAAC,UAAAuB,KAAK,EAAI;MAAApJ,aAAA,GAAAC,CAAA;MACzB,IAAM6K,MAAM,IAAA9K,aAAA,GAAAG,CAAA,SAAGsF,UAAU,CAACoC,GAAG,CAAC,UAAA1H,CAAC,EAAI;QAAAH,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAG,CAAA;QAAA,OAAAA,CAAC,CAACiJ,KAAK,CAAC;MAAD,CAAC,CAAC,CAACN,MAAM,CAAC,UAAAiC,CAAC,EAAI;QAAA/K,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAG,CAAA;QAAA,OAAA4K,CAAC,IAAI,IAAI;MAAD,CAAC,CAAC;MAAC/K,aAAA,GAAAG,CAAA;MACpE,IAAI2K,MAAM,CAACpF,MAAM,GAAG,CAAC,EAAE;QAAA1F,aAAA,GAAAqC,CAAA;QAAArC,aAAA,GAAAG,CAAA;QACrB,OAAO;UACLiJ,KAAK,EAALA,KAAK;UACL4B,cAAc,EAAE,CAAAhL,aAAA,GAAAqC,CAAA,WAAA4I,MAAM,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,MAAA9K,aAAA,GAAAqC,CAAA,WAAI,EAAE;UACvCkH,aAAa,EAAE,CAAAvJ,aAAA,GAAAqC,CAAA,WAAA4I,MAAM,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,MAAA9K,aAAA,GAAAqC,CAAA,WAAI,EAAE;UACtC6G,KAAK,EAAE,QAAiB;UACxBS,UAAU,EAAE;QACd,CAAC;MACH,CAAC;QAAA3J,aAAA,GAAAqC,CAAA;MAAA;MAED,IAAM2I,cAAc,IAAAhL,aAAA,GAAAG,CAAA,SAAG,CAAAH,aAAA,GAAAqC,CAAA,WAAA4I,MAAM,CAACH,MAAM,CAACA,MAAM,CAACpF,MAAM,GAAG,CAAC,CAAC,CAAC,MAAA1F,aAAA,GAAAqC,CAAA,WAAI,EAAE;MAC9D,IAAMkH,aAAa,IAAAvJ,aAAA,GAAAG,CAAA,SAAG,CAAAH,aAAA,GAAAqC,CAAA,WAAA4I,MAAM,CAACH,MAAM,CAACA,MAAM,CAACpF,MAAM,GAAG,CAAC,CAAC,CAAC,MAAA1F,aAAA,GAAAqC,CAAA,WAAI,EAAE;MAC7D,IAAMsH,UAAU,IAAA3J,aAAA,GAAAG,CAAA,SAAGoJ,aAAa,GAAGyB,cAAc;MAAChL,aAAA,GAAAG,CAAA;MAElD,OAAO;QACLiJ,KAAK,EAALA,KAAK;QACL4B,cAAc,EAAdA,cAAc;QACdzB,aAAa,EAAbA,aAAa;QACbL,KAAK,EAAES,UAAU,GAAG,CAAC,IAAA3J,aAAA,GAAAqC,CAAA,WAAI,WAAW,KAAArC,aAAA,GAAAqC,CAAA,WAAasH,UAAU,GAAG,CAAC,CAAC,IAAA3J,aAAA,GAAAqC,CAAA,WAAI,WAAW,KAAArC,aAAA,GAAAqC,CAAA,WAAc,QAAQ,CAAU;QAC/GsH,UAAU,EAAVA;MACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMhB,uBAAuB,IAAA3I,aAAA,GAAAG,CAAA,SAAGb,WAAW,CAAC,UAACgI,QAAe,EAAK;IAAAtH,aAAA,GAAAC,CAAA;IAE/D,IAAMiL,UAAkC,IAAAlL,aAAA,GAAAG,CAAA,SAAG,CAAC,CAAC;IAACH,aAAA,GAAAG,CAAA;IAE9CmH,QAAQ,CAAC6D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAAApL,aAAA,GAAAC,CAAA;MAC1B,IAAMoL,IAAI,IAAArL,aAAA,GAAAG,CAAA,SAAG,IAAImL,IAAI,CAACF,OAAO,CAACjD,UAAU,CAAC,CAACoD,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MAACxL,aAAA,GAAAG,CAAA;MACrE,IAAI,CAAC+K,UAAU,CAACG,IAAI,CAAC,EAAE;QAAArL,aAAA,GAAAqC,CAAA;QAAArC,aAAA,GAAAG,CAAA;QACrB+K,UAAU,CAACG,IAAI,CAAC,GAAG;UACjBA,IAAI,EAAJA,IAAI;UACJI,iBAAiB,EAAE,CAAC;UACpBC,UAAU,EAAE,CAAC;UACbC,gBAAgB,EAAE,IAAIC,GAAG,CAAC;QAC5B,CAAC;MACH,CAAC;QAAA5L,aAAA,GAAAqC,CAAA;MAAA;MAAArC,aAAA,GAAAG,CAAA;MAED+K,UAAU,CAACG,IAAI,CAAC,CAACI,iBAAiB,EAAE;MAACzL,aAAA,GAAAG,CAAA;MACrC+K,UAAU,CAACG,IAAI,CAAC,CAACK,UAAU,IAAI,CAAA1L,aAAA,GAAAqC,CAAA,WAAA+I,OAAO,CAACS,aAAa,MAAA7L,aAAA,GAAAqC,CAAA,WAAI,EAAE;MAACrC,aAAA,GAAAG,CAAA;MAC3D,IAAIiL,OAAO,CAACU,iBAAiB,EAAE;QAAA9L,aAAA,GAAAqC,CAAA;QAAArC,aAAA,GAAAG,CAAA;QAC7BiL,OAAO,CAACU,iBAAiB,CAACX,OAAO,CAAC,UAACY,IAAY,EAC7C;UAAA/L,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAG,CAAA;UAAA,OAAA+K,UAAU,CAACG,IAAI,CAAC,CAACM,gBAAgB,CAACK,GAAG,CAACD,IAAI,CAAC;QAAD,CAC5C,CAAC;MACH,CAAC;QAAA/L,aAAA,GAAAqC,CAAA;MAAA;IACH,CAAC,CAAC;IAACrC,aAAA,GAAAG,CAAA;IAEH,OAAOsE,MAAM,CAACqG,MAAM,CAACI,UAAU,CAAC,CAACrD,GAAG,CAAC,UAACwD,IAAS,EAAM;MAAArL,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MAAA,OAAAsE,MAAA,CAAAC,MAAA,KAChD2G,IAAI;QACPY,YAAY,EAAExC,IAAI,CAACyC,KAAK,CAACb,IAAI,CAACK,UAAU,GAAGL,IAAI,CAACI,iBAAiB,CAAC;QAClEE,gBAAgB,EAAE7B,KAAK,CAAC7D,IAAI,CAACoF,IAAI,CAACM,gBAAgB;MAAC;IACrD,CAAE,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMhG,uBAAuB,IAAA3F,aAAA,GAAAG,CAAA,SAAGb,WAAW,CAAC,UAAC6M,YAAmB,EAAE5G,SAAiB,EAAK;IAAAvF,aAAA,GAAAC,CAAA;IAEtF,IAAMmM,GAAG,IAAApM,aAAA,GAAAG,CAAA,SAAG,IAAImL,IAAI,CAAC,CAAC;IACtB,IAAMe,UAAU,IAAArM,aAAA,GAAAG,CAAA,SAAG,IAAImL,IAAI,CAAC,CAAC;IAACtL,aAAA,GAAAG,CAAA;IAE9B,QAAQoF,SAAS;MACf,KAAK,MAAM;QAAAvF,aAAA,GAAAqC,CAAA;QAAArC,aAAA,GAAAG,CAAA;QACTkM,UAAU,CAACC,OAAO,CAACF,GAAG,CAACG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QAACvM,aAAA,GAAAG,CAAA;QACtC;MACF,KAAK,OAAO;QAAAH,aAAA,GAAAqC,CAAA;QAAArC,aAAA,GAAAG,CAAA;QACVkM,UAAU,CAACG,QAAQ,CAACJ,GAAG,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAACzM,aAAA,GAAAG,CAAA;QACxC;MACF,KAAK,MAAM;QAAAH,aAAA,GAAAqC,CAAA;QAAArC,aAAA,GAAAG,CAAA;QACTkM,UAAU,CAACK,WAAW,CAACN,GAAG,CAACO,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;QAAC3M,aAAA,GAAAG,CAAA;QAC9C;IACJ;IAACH,aAAA,GAAAG,CAAA;IAED,OAAOgM,YAAY,CAChBrD,MAAM,CAAC,UAAA8D,IAAI,EAAI;MAAA5M,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MAAA,WAAImL,IAAI,CAACsB,IAAI,CAACC,UAAU,CAAC,IAAIR,UAAU;IAAD,CAAC,CAAC,CACvDxE,GAAG,CAAC,UAAA+E,IAAI,EAAK;MAAA5M,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MAAA;QACZ+H,IAAI,EAAE0E,IAAI,CAACC,UAAU;QACrBC,QAAQ,EAAEF,IAAI,CAACE,QAAQ;QACvBC,QAAQ,EAAEH,IAAI,CAACG,QAAQ;QACvBC,KAAK,EAAEJ,IAAI,CAACI,KAAK;QACjBC,MAAM,EAAEL,IAAI,CAACK,MAAM;QACnBC,QAAQ,EAAEN,IAAI,CAACM,QAAQ;QACvBC,QAAQ,EAAEP,IAAI,CAACO,QAAQ;QACvBC,WAAW,EAAER,IAAI,CAACQ;MACpB,CAAC;IAAD,CAAE,CAAC;EACP,CAAC,EAAE,EAAE,CAAC;EAEN,IAAM1G,qBAAqB,IAAA1G,aAAA,GAAAG,CAAA,SAAGb,WAAW,CAAC,UAACkH,SAAgB,EAAK;IAAAxG,aAAA,GAAAC,CAAA;IAC9D,IAAMoN,MAAM,IAAArN,aAAA,GAAAG,CAAA,SAAG;MACb2M,QAAQ,EAAE,CAAC;MAAEC,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAC7CC,QAAQ,EAAE,CAAC;MAAEC,QAAQ,EAAE,CAAC;MAAEC,WAAW,EAAE;IACzC,CAAC;IAED,IAAIE,KAAK,IAAAtN,aAAA,GAAAG,CAAA,SAAG,CAAC;IAACH,aAAA,GAAAG,CAAA;IACdqG,SAAS,CAAC2E,OAAO,CAAC,UAAAoC,IAAI,EAAI;MAAAvN,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MACxB,IAAIoN,IAAI,CAACC,WAAW,EAAE;QAAAxN,aAAA,GAAAqC,CAAA;QAAArC,aAAA,GAAAG,CAAA;QACpBsE,MAAM,CAACgJ,IAAI,CAACJ,MAAM,CAAC,CAAClC,OAAO,CAAC,UAAA/B,KAAK,EAAI;UAAApJ,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAG,CAAA;UACnCkN,MAAM,CAACjE,KAAK,CAAwB,IAAI,CAAApJ,aAAA,GAAAqC,CAAA,WAAAkL,IAAI,CAACC,WAAW,CAACpE,KAAK,CAAC,MAAApJ,aAAA,GAAAqC,CAAA,WAAI,CAAC;QACtE,CAAC,CAAC;QAACrC,aAAA,GAAAG,CAAA;QACHmN,KAAK,EAAE;MACT,CAAC;QAAAtN,aAAA,GAAAqC,CAAA;MAAA;IACH,CAAC,CAAC;IAACrC,aAAA,GAAAG,CAAA;IAEH,IAAImN,KAAK,KAAK,CAAC,EAAE;MAAAtN,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAAA,OAAOkN,MAAM;IAAA,CAAC;MAAArN,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAE/BsE,MAAM,CAACgJ,IAAI,CAACJ,MAAM,CAAC,CAAClC,OAAO,CAAC,UAAA/B,KAAK,EAAI;MAAApJ,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MACnCkN,MAAM,CAACjE,KAAK,CAAwB,GAAGK,IAAI,CAACyC,KAAK,CAACmB,MAAM,CAACjE,KAAK,CAAwB,GAAGkE,KAAK,CAAC;IACjG,CAAC,CAAC;IAACtN,aAAA,GAAAG,CAAA;IAEH,OAAOkN,MAAM;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,IAAM1G,sBAAsB,IAAA3G,aAAA,GAAAG,CAAA,SAAGb,WAAW,CAAC,UAAC+G,SAAc,EAAEI,YAAiB,EAAK;IAAAzG,aAAA,GAAAC,CAAA;IAChF,IAAMyN,UAAkC,IAAA1N,aAAA,GAAAG,CAAA,SAAG,CAAC,CAAC;IAACH,aAAA,GAAAG,CAAA;IAE9CsE,MAAM,CAACgJ,IAAI,CAAChH,YAAY,CAAC,CAAC0E,OAAO,CAAC,UAAA/B,KAAK,EAAI;MAAApJ,aAAA,GAAAC,CAAA;MACzC,IAAM0N,SAAS,IAAA3N,aAAA,GAAAG,CAAA,SAAG,CAAAH,aAAA,GAAAqC,CAAA,WAAAgE,SAAS,CAAC+C,KAAK,CAAC,MAAApJ,aAAA,GAAAqC,CAAA,WAAI,CAAC;MACvC,IAAMuL,SAAS,IAAA5N,aAAA,GAAAG,CAAA,SAAG,CAAAH,aAAA,GAAAqC,CAAA,WAAAoE,YAAY,CAAC2C,KAAK,CAAC,MAAApJ,aAAA,GAAAqC,CAAA,WAAI,CAAC;MAC1C,IAAMwL,UAAU,IAAA7N,aAAA,GAAAG,CAAA,SAAGwN,SAAS,GAAGC,SAAS;MAAC5N,aAAA,GAAAG,CAAA;MAEzCuN,UAAU,CAACtE,KAAK,CAAC,GAAG;QAClBnI,IAAI,EAAE0M,SAAS;QACfJ,IAAI,EAAEK,SAAS;QACfC,UAAU,EAAVA,UAAU;QACVC,UAAU,EAAEC,mBAAmB,CAACF,UAAU;MAC5C,CAAC;IACH,CAAC,CAAC;IAAC7N,aAAA,GAAAG,CAAA;IAEH,OAAOuN,UAAU;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMK,mBAAmB,IAAA/N,aAAA,GAAAG,CAAA,SAAGb,WAAW,CAAC,UAACuO,UAAkB,EAAa;IAAA7N,aAAA,GAAAC,CAAA;IAAAD,aAAA,GAAAG,CAAA;IAEtE,IAAI0N,UAAU,GAAG,EAAE,EAAE;MAAA7N,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;MAAAH,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAC/B,IAAI0N,UAAU,GAAG,EAAE,EAAE;MAAA7N,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;MAAAH,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAC/B,IAAI0N,UAAU,GAAG,CAAC,EAAE;MAAA7N,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;MAAAH,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAC9B,IAAI0N,UAAU,GAAG,CAAC,EAAE;MAAA7N,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;MAAAH,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAC9B,IAAI0N,UAAU,GAAG,CAAC,CAAC,EAAE;MAAA7N,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;MAAAH,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAC/B,IAAI0N,UAAU,GAAG,CAAC,EAAE,EAAE;MAAA7N,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;MAAAH,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAChC,IAAI0N,UAAU,GAAG,CAAC,EAAE,EAAE;MAAA7N,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAG,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;MAAAH,aAAA,GAAAqC,CAAA;IAAA;IAAArC,aAAA,GAAAG,CAAA;IAChC,OAAO,EAAE;EACX,CAAC,EAAE,EAAE,CAAC;EAACH,aAAA,GAAAG,CAAA;EAEP,OAAO;IACLiC,eAAe,EAAfA,eAAe;IACf9B,aAAa,EAAbA,aAAa;IACbI,OAAO,EAAPA,OAAO;IACPI,KAAK,EAALA,KAAK;IACL+B,YAAY,EAAZA,YAAY;IACZuC,yBAAyB,EAAzBA,yBAAyB;IACzBC,mBAAmB,EAAnBA,mBAAmB;IACnBQ,gBAAgB,EAAhBA,gBAAgB;IAChBgB,gBAAgB,EAAhBA;EACF,CAAC;AACH", "ignoreList": []}