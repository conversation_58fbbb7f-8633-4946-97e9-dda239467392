6b893e7f2433a3e02b1cf5e19856b30a
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _AnimatedInterpolation = _interopRequireDefault(require("./AnimatedInterpolation"));
var _AnimatedValue = _interopRequireDefault(require("./AnimatedValue"));
var _AnimatedWithChildren = _interopRequireDefault(require("./AnimatedWithChildren"));
var AnimatedMultiplication = function (_AnimatedWithChildren2) {
  function AnimatedMultiplication(a, b) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedMultiplication);
    _this = _callSuper(this, AnimatedMultiplication);
    _this._a = typeof a === 'number' ? new _AnimatedValue.default(a) : a;
    _this._b = typeof b === 'number' ? new _AnimatedValue.default(b) : b;
    return _this;
  }
  (0, _inherits2.default)(AnimatedMultiplication, _AnimatedWithChildren2);
  return (0, _createClass2.default)(AnimatedMultiplication, [{
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      this._a.__makeNative(platformConfig);
      this._b.__makeNative(platformConfig);
      _superPropGet(AnimatedMultiplication, "__makeNative", this, 3)([platformConfig]);
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      return this._a.__getValue() * this._b.__getValue();
    }
  }, {
    key: "interpolate",
    value: function interpolate(config) {
      return new _AnimatedInterpolation.default(this, config);
    }
  }, {
    key: "__attach",
    value: function __attach() {
      this._a.__addChild(this);
      this._b.__addChild(this);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      this._a.__removeChild(this);
      this._b.__removeChild(this);
      _superPropGet(AnimatedMultiplication, "__detach", this, 3)([]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      return {
        type: 'multiplication',
        input: [this._a.__getNativeTag(), this._b.__getNativeTag()]
      };
    }
  }]);
}(_AnimatedWithChildren.default);
var _default = exports.default = AnimatedMultiplication;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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