/**
 * MediaPipe Demo Page
 * Demonstrates video input → MediaPipe pose detection → data output
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { TouchableOpacity } from 'react-native';

import MediaPipeDemo from '@/components/MediaPipeDemo';

export default function MediaPipeDemoPage() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>MediaPipe Integration</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>🚀 Local Pose Detection</Text>
          <Text style={styles.infoText}>
            This demo showcases the new MediaPipe integration that replaces cloud-based Replicate API calls with local pose detection processing.
          </Text>
          
          <View style={styles.featureList}>
            <Text style={styles.featureItem}>✅ Real-time pose landmark detection</Text>
            <Text style={styles.featureItem}>✅ Tennis-specific movement analysis</Text>
            <Text style={styles.featureItem}>✅ Local processing (no cloud API calls)</Text>
            <Text style={styles.featureItem}>✅ AI coaching feedback integration</Text>
            <Text style={styles.featureItem}>✅ Frame-by-frame analysis</Text>
          </View>

          <View style={styles.techStack}>
            <Text style={styles.techTitle}>Technology Stack:</Text>
            <Text style={styles.techItem}>• MediaPipe Pose Detection</Text>
            <Text style={styles.techItem}>• JavaScript/TypeScript SDK</Text>
            <Text style={styles.techItem}>• Canvas-based frame processing</Text>
            <Text style={styles.techItem}>• DeepSeek AI for coaching feedback</Text>
          </View>
        </View>

        <MediaPipeDemo />

        <View style={styles.implementationDetails}>
          <Text style={styles.detailsTitle}>📋 Implementation Details</Text>
          
          <View style={styles.detailSection}>
            <Text style={styles.detailSubtitle}>1. Video Processing Pipeline</Text>
            <Text style={styles.detailText}>
              • Video file is loaded into HTML5 video element{'\n'}
              • Frames are extracted at configurable frame rate{'\n'}
              • Each frame is drawn to canvas for processing{'\n'}
              • MediaPipe processes canvas ImageData
            </Text>
          </View>

          <View style={styles.detailSection}>
            <Text style={styles.detailSubtitle}>2. Pose Detection</Text>
            <Text style={styles.detailText}>
              • 33 pose landmarks detected per frame{'\n'}
              • Tennis-specific joint angle calculations{'\n'}
              • Movement classification (serve, forehand, etc.){'\n'}
              • Confidence scoring for each detection
            </Text>
          </View>

          <View style={styles.detailSection}>
            <Text style={styles.detailSubtitle}>3. Analysis Output</Text>
            <Text style={styles.detailText}>
              • Frame-by-frame pose data{'\n'}
              • Overall technique scoring{'\n'}
              • Movement pattern identification{'\n'}
              • AI-generated coaching feedback
            </Text>
          </View>

          <View style={styles.detailSection}>
            <Text style={styles.detailSubtitle}>4. Performance Optimizations</Text>
            <Text style={styles.detailText}>
              • Configurable frame rate processing{'\n'}
              • Maximum duration limits{'\n'}
              • Efficient canvas operations{'\n'}
              • Memory cleanup after processing
            </Text>
          </View>
        </View>

        <View style={styles.codeExample}>
          <Text style={styles.codeTitle}>💻 Usage Example</Text>
          <View style={styles.codeBlock}>
            <Text style={styles.codeText}>
{`// Process video with MediaPipe
const results = await videoProcessingService.processVideo(videoFile, {
  frameRate: 5,        // Process 5 fps
  maxDuration: 30,     // Limit to 30 seconds
  includeVisualization: false
});

// Results include:
// - videoMetadata: duration, resolution, frameCount
// - poseAnalysis: frame-by-frame pose data
// - overallAnalysis: technique scores and insights
// - aiCoachingFeedback: AI-generated recommendations`}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  placeholder: {
    width: 34,
  },
  content: {
    flex: 1,
  },
  infoSection: {
    backgroundColor: '#fff',
    margin: 20,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    marginBottom: 20,
  },
  featureList: {
    marginBottom: 20,
  },
  featureItem: {
    fontSize: 14,
    color: '#28a745',
    marginBottom: 5,
    lineHeight: 20,
  },
  techStack: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 8,
  },
  techTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
  },
  techItem: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  implementationDetails: {
    backgroundColor: '#fff',
    margin: 20,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  detailSection: {
    marginBottom: 20,
  },
  detailSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  codeExample: {
    backgroundColor: '#fff',
    margin: 20,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  codeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  codeBlock: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  codeText: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: '#333',
    lineHeight: 18,
  },
});
