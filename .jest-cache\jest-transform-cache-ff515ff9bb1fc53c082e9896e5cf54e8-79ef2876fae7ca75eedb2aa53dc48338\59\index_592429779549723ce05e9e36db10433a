5491f2b3cf89da654946b25cf57dba83
"use strict";
'use client';

exports.__esModule = true;
exports.default = render;
exports.hydrate = hydrate;
var _client = require("react-dom/client");
var _dom = require("../StyleSheet/dom");
function hydrate(element, root) {
  (0, _dom.createSheet)(root);
  return (0, _client.hydrateRoot)(root, element);
}
function render(element, root) {
  (0, _dom.createSheet)(root);
  var reactRoot = (0, _client.createRoot)(root);
  reactRoot.render(element);
  return reactRoot;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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