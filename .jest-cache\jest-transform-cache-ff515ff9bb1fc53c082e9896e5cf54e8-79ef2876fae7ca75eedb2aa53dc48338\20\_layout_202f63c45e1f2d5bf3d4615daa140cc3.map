{"version": 3, "names": ["useEffect", "<PERSON><PERSON>", "StatusBar", "useFrameworkReady", "useFonts", "Inter_400Regular", "Inter_500Medium", "Inter_600SemiBold", "Inter_700Bold", "SplashScreen", "<PERSON>th<PERSON><PERSON><PERSON>", "Error<PERSON>ou<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "cov_a7pt6sn8h", "s", "preventAutoHideAsync", "RootLayout", "f", "_ref", "_ref2", "_slicedToArray", "fontsLoaded", "fontError", "b", "<PERSON><PERSON><PERSON>", "context", "children", "screenOptions", "headerShown", "Screen", "name", "options", "style"], "sources": ["_layout.tsx"], "sourcesContent": ["import { useEffect } from 'react';\nimport { Stack } from 'expo-router';\nimport { StatusBar } from 'expo-status-bar';\nimport { useFrameworkReady } from '@/hooks/useFrameworkReady';\nimport { useFonts, Inter_400Regular, Inter_500Medium, Inter_600SemiBold, Inter_700Bold } from '@expo-google-fonts/inter';\nimport * as SplashScreen from 'expo-splash-screen';\nimport { AuthProvider } from '@/contexts/AuthContext';\nimport ErrorBoundary from '@/components/ui/ErrorBoundary';\n\nSplashScreen.preventAutoHideAsync();\n\nexport default function RootLayout() {\n  useFrameworkReady();\n  \n  const [fontsLoaded, fontError] = useFonts({\n    'Inter-Regular': Inter_400Regular,\n    'Inter-Medium': Inter_500Medium,\n    'Inter-SemiBold': Inter_600SemiBold,\n    'Inter-Bold': Inter_700Bold,\n  });\n\n  useEffect(() => {\n    if (fontsLoaded || fontError) {\n      SplashScreen.hideAsync();\n    }\n  }, [fontsLoaded, fontError]);\n\n  if (!fontsLoaded && !fontError) {\n    return null;\n  }\n\n  return (\n    <ErrorBoundary context=\"RootLayout\">\n      <AuthProvider>\n        <Stack screenOptions={{ headerShown: false }}>\n          <Stack.Screen name=\"onboarding\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"(tabs)\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"auth\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"match-recording\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"ai-demo\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"core-features-demo\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"drills/index\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"drills/[id]\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"matches/index\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"matches/[id]\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"social/index\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"social/create-post\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"settings/edit-profile\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"settings/subscription\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"settings/notifications\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"settings/privacy\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"help/index\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"help/contact\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"social/challenges\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"social/messaging\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"analytics/dashboard\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"payment/checkout\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"payment/payment-methods\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"payment/billing-history\" options={{ headerShown: false }} />\n          <Stack.Screen name=\"+not-found\" />\n        </Stack>\n        <StatusBar style=\"auto\" />\n      </AuthProvider>\n    </ErrorBoundary>\n  );\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,iBAAiB;AAC1B,SAASC,QAAQ,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,aAAa,QAAQ,0BAA0B;AACxH,OAAO,KAAKC,YAAY,MAAM,oBAAoB;AAClD,SAASC,YAAY;AACrB,OAAOC,aAAa;AAAsC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAAAC,aAAA,GAAAC,CAAA;AAE1DR,YAAY,CAACS,oBAAoB,CAAC,CAAC;AAEnC,eAAe,SAASC,UAAUA,CAAA,EAAG;EAAAH,aAAA,GAAAI,CAAA;EAAAJ,aAAA,GAAAC,CAAA;EACnCd,iBAAiB,CAAC,CAAC;EAEnB,IAAAkB,IAAA,IAAAL,aAAA,GAAAC,CAAA,OAAiCb,QAAQ,CAAC;MACxC,eAAe,EAAEC,gBAAgB;MACjC,cAAc,EAAEC,eAAe;MAC/B,gBAAgB,EAAEC,iBAAiB;MACnC,YAAY,EAAEC;IAChB,CAAC,CAAC;IAAAc,KAAA,GAAAC,cAAA,CAAAF,IAAA;IALKG,WAAW,GAAAF,KAAA;IAAEG,SAAS,GAAAH,KAAA;EAK1BN,aAAA,GAAAC,CAAA;EAEHjB,SAAS,CAAC,YAAM;IAAAgB,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IACd,IAAI,CAAAD,aAAA,GAAAU,CAAA,UAAAF,WAAW,MAAAR,aAAA,GAAAU,CAAA,UAAID,SAAS,GAAE;MAAAT,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MAC5BR,YAAY,CAACkB,SAAS,CAAC,CAAC;IAC1B,CAAC;MAAAX,aAAA,GAAAU,CAAA;IAAA;EACH,CAAC,EAAE,CAACF,WAAW,EAAEC,SAAS,CAAC,CAAC;EAACT,aAAA,GAAAC,CAAA;EAE7B,IAAI,CAAAD,aAAA,GAAAU,CAAA,WAACF,WAAW,MAAAR,aAAA,GAAAU,CAAA,UAAI,CAACD,SAAS,GAAE;IAAAT,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAC9B,OAAO,IAAI;EACb,CAAC;IAAAD,aAAA,GAAAU,CAAA;EAAA;EAAAV,aAAA,GAAAC,CAAA;EAED,OACEJ,IAAA,CAACF,aAAa;IAACiB,OAAO,EAAC,YAAY;IAAAC,QAAA,EACjCd,KAAA,CAACL,YAAY;MAAAmB,QAAA,GACXd,KAAA,CAACd,KAAK;QAAC6B,aAAa,EAAE;UAAEC,WAAW,EAAE;QAAM,CAAE;QAAAF,QAAA,GAC3ChB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,YAAY;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EACnElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,QAAQ;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EAC/DlB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,MAAM;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EAC7DlB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,iBAAiB;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EACxElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,SAAS;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EAChElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,oBAAoB;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EAC3ElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,cAAc;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EACrElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,aAAa;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EACpElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,eAAe;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EACtElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,cAAc;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EACrElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,cAAc;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EACrElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,oBAAoB;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EAC3ElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,uBAAuB;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EAC9ElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,uBAAuB;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EAC9ElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,wBAAwB;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EAC/ElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,kBAAkB;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EACzElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,YAAY;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EACnElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,cAAc;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EACrElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,mBAAmB;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EAC1ElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,kBAAkB;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EACzElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,qBAAqB;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EAC5ElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,kBAAkB;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EACzElB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,yBAAyB;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EAChFlB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC,yBAAyB;UAACC,OAAO,EAAE;YAAEH,WAAW,EAAE;UAAM;QAAE,CAAE,CAAC,EAChFlB,IAAA,CAACZ,KAAK,CAAC+B,MAAM;UAACC,IAAI,EAAC;QAAY,CAAE,CAAC;MAAA,CAC7B,CAAC,EACRpB,IAAA,CAACX,SAAS;QAACiC,KAAK,EAAC;MAAM,CAAE,CAAC;IAAA,CACd;EAAC,CACF,CAAC;AAEpB", "ignoreList": []}