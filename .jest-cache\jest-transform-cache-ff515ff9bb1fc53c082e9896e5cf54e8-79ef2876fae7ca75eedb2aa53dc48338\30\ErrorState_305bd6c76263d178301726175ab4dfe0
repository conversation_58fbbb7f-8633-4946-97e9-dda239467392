89cd6d795a269f34f62a3b088444d33f
function cov_14vhv5qoaj() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\ui\\ErrorState.tsx";
  var hash = "13778ad4cf1dfbc0829da5d3b82fd479304b3786";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\ui\\ErrorState.tsx",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 15
        },
        end: {
          line: 13,
          column: 1
        }
      },
      "1": {
        start: {
          line: 26,
          column: 2
        },
        end: {
          line: 43,
          column: 4
        }
      },
      "2": {
        start: {
          line: 46,
          column: 15
        },
        end: {
          line: 93,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "<PERSON><PERSON><PERSON>State",
        decl: {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 34
          }
        },
        loc: {
          start: {
            line: 25,
            column: 20
          },
          end: {
            line: 44,
            column: 1
          }
        },
        line: 25
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 24,
            column: 2
          },
          end: {
            line: 24,
            column: 25
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 24,
            column: 14
          },
          end: {
            line: 24,
            column: 25
          }
        }],
        line: 24
      },
      "1": {
        loc: {
          start: {
            line: 34,
            column: 9
          },
          end: {
            line: 40,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 9
          },
          end: {
            line: 34,
            column: 16
          }
        }, {
          start: {
            line: 35,
            column: 10
          },
          end: {
            line: 39,
            column: 12
          }
        }],
        line: 34
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0],
      "1": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "13778ad4cf1dfbc0829da5d3b82fd479304b3786"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_14vhv5qoaj = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_14vhv5qoaj();
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { TriangleAlert as AlertTriangle } from 'lucide-react-native';
import Button from "./Button";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_14vhv5qoaj().s[0]++, {
  primary: '#23ba16',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  red: '#ef4444'
});
export default function ErrorState(_ref) {
  var message = _ref.message,
    onRetry = _ref.onRetry,
    _ref$retryText = _ref.retryText,
    retryText = _ref$retryText === void 0 ? (cov_14vhv5qoaj().b[0][0]++, 'Try Again') : _ref$retryText;
  cov_14vhv5qoaj().f[0]++;
  cov_14vhv5qoaj().s[1]++;
  return _jsx(View, {
    style: styles.container,
    children: _jsxs(View, {
      style: styles.content,
      children: [_jsx(View, {
        style: styles.iconContainer,
        children: _jsx(AlertTriangle, {
          size: 48,
          color: colors.red
        })
      }), _jsx(Text, {
        style: styles.title,
        children: "Something went wrong"
      }), _jsx(Text, {
        style: styles.message,
        children: message
      }), (cov_14vhv5qoaj().b[1][0]++, onRetry) && (cov_14vhv5qoaj().b[1][1]++, _jsx(Button, {
        title: retryText,
        onPress: onRetry,
        style: styles.retryButton
      }))]
    })
  });
}
var styles = (cov_14vhv5qoaj().s[2]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24
  },
  content: {
    alignItems: 'center',
    maxWidth: 280
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3
  },
  title: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: colors.dark,
    marginBottom: 8,
    textAlign: 'center'
  },
  message: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24
  },
  retryButton: {
    minWidth: 120
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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