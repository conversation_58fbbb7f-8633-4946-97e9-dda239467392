ff67d7d68e768d33946295d17cd9058e
"use strict";

exports.__esModule = true;
exports.TOUCH_START = exports.TOUCH_MOVE = exports.TOUCH_END = exports.TOUCH_CANCEL = exports.SELECTION_CHANGE = exports.SELECT = exports.SCROLL = exports.MOUSE_UP = exports.MOUSE_MOVE = exports.MOUSE_DOWN = exports.MOUSE_CANCEL = exports.FOCUS_OUT = exports.CONTEXT_MENU = exports.BLUR = void 0;
exports.isCancelish = isCancelish;
exports.isEndish = isEndish;
exports.isMoveish = isMoveish;
exports.isScroll = isScroll;
exports.isSelectionChange = isSelectionChange;
exports.isStartish = isStartish;
var BLUR = exports.BLUR = 'blur';
var CONTEXT_MENU = exports.CONTEXT_MENU = 'contextmenu';
var FOCUS_OUT = exports.FOCUS_OUT = 'focusout';
var MOUSE_DOWN = exports.MOUSE_DOWN = 'mousedown';
var MOUSE_MOVE = exports.MOUSE_MOVE = 'mousemove';
var MOUSE_UP = exports.MOUSE_UP = 'mouseup';
var MOUSE_CANCEL = exports.MOUSE_CANCEL = 'dragstart';
var TOUCH_START = exports.TOUCH_START = 'touchstart';
var TOUCH_MOVE = exports.TOUCH_MOVE = 'touchmove';
var TOUCH_END = exports.TOUCH_END = 'touchend';
var TOUCH_CANCEL = exports.TOUCH_CANCEL = 'touchcancel';
var SCROLL = exports.SCROLL = 'scroll';
var SELECT = exports.SELECT = 'select';
var SELECTION_CHANGE = exports.SELECTION_CHANGE = 'selectionchange';
function isStartish(eventType) {
  return eventType === TOUCH_START || eventType === MOUSE_DOWN;
}
function isMoveish(eventType) {
  return eventType === TOUCH_MOVE || eventType === MOUSE_MOVE;
}
function isEndish(eventType) {
  return eventType === TOUCH_END || eventType === MOUSE_UP || isCancelish(eventType);
}
function isCancelish(eventType) {
  return eventType === TOUCH_CANCEL || eventType === MOUSE_CANCEL;
}
function isScroll(eventType) {
  return eventType === SCROLL;
}
function isSelectionChange(eventType) {
  return eventType === SELECT || eventType === SELECTION_CHANGE;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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