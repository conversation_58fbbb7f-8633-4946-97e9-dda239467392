50f2dfb9addf457ad846aeaa4b185df8
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_2fwkcqgk93() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\(tabs)\\social.tsx";
  var hash = "e50d1c38bb6bdc08f3736061b8e0d6c1ad48ade6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\(tabs)\\social.tsx",
    statementMap: {
      "0": {
        start: {
          line: 27,
          column: 30
        },
        end: {
          line: 27,
          column: 39
        }
      },
      "1": {
        start: {
          line: 28,
          column: 36
        },
        end: {
          line: 28,
          column: 63
        }
      },
      "2": {
        start: {
          line: 29,
          column: 44
        },
        end: {
          line: 29,
          column: 72
        }
      },
      "3": {
        start: {
          line: 30,
          column: 40
        },
        end: {
          line: 30,
          column: 51
        }
      },
      "4": {
        start: {
          line: 32,
          column: 2
        },
        end: {
          line: 36,
          column: 9
        }
      },
      "5": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 35,
          column: 5
        }
      },
      "6": {
        start: {
          line: 34,
          column: 6
        },
        end: {
          line: 34,
          column: 26
        }
      },
      "7": {
        start: {
          line: 38,
          column: 28
        },
        end: {
          line: 48,
          column: 3
        }
      },
      "8": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 47,
          column: 5
        }
      },
      "9": {
        start: {
          line: 40,
          column: 45
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "10": {
        start: {
          line: 41,
          column: 6
        },
        end: {
          line: 44,
          column: 7
        }
      },
      "11": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 31
        }
      },
      "12": {
        start: {
          line: 43,
          column: 8
        },
        end: {
          line: 43,
          column: 60
        }
      },
      "13": {
        start: {
          line: 43,
          column: 40
        },
        end: {
          line: 43,
          column: 50
        }
      },
      "14": {
        start: {
          line: 46,
          column: 6
        },
        end: {
          line: 46,
          column: 59
        }
      },
      "15": {
        start: {
          line: 50,
          column: 34
        },
        end: {
          line: 53,
          column: 3
        }
      },
      "16": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 52,
          column: 84
        }
      },
      "17": {
        start: {
          line: 55,
          column: 31
        },
        end: {
          line: 58,
          column: 3
        }
      },
      "18": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 57,
          column: 73
        }
      },
      "19": {
        start: {
          line: 60,
          column: 30
        },
        end: {
          line: 69,
          column: 3
        }
      },
      "20": {
        start: {
          line: 61,
          column: 4
        },
        end: {
          line: 68,
          column: 6
        }
      },
      "21": {
        start: {
          line: 66,
          column: 43
        },
        end: {
          line: 66,
          column: 83
        }
      },
      "22": {
        start: {
          line: 71,
          column: 27
        },
        end: {
          line: 108,
          column: 3
        }
      },
      "23": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 107,
          column: 5
        }
      },
      "24": {
        start: {
          line: 74,
          column: 8
        },
        end: {
          line: 79,
          column: 10
        }
      },
      "25": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 85,
          column: 10
        }
      },
      "26": {
        start: {
          line: 87,
          column: 8
        },
        end: {
          line: 91,
          column: 10
        }
      },
      "27": {
        start: {
          line: 93,
          column: 8
        },
        end: {
          line: 104,
          column: 10
        }
      },
      "28": {
        start: {
          line: 106,
          column: 8
        },
        end: {
          line: 106,
          column: 20
        }
      },
      "29": {
        start: {
          line: 110,
          column: 21
        },
        end: {
          line: 118,
          column: 3
        }
      },
      "30": {
        start: {
          line: 111,
          column: 20
        },
        end: {
          line: 116,
          column: 5
        }
      },
      "31": {
        start: {
          line: 117,
          column: 4
        },
        end: {
          line: 117,
          column: 24
        }
      },
      "32": {
        start: {
          line: 120,
          column: 22
        },
        end: {
          line: 128,
          column: 3
        }
      },
      "33": {
        start: {
          line: 121,
          column: 21
        },
        end: {
          line: 126,
          column: 5
        }
      },
      "34": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 127,
          column: 25
        }
      },
      "35": {
        start: {
          line: 130,
          column: 2
        },
        end: {
          line: 219,
          column: 4
        }
      },
      "36": {
        start: {
          line: 158,
          column: 31
        },
        end: {
          line: 158,
          column: 89
        }
      },
      "37": {
        start: {
          line: 170,
          column: 27
        },
        end: {
          line: 170,
          column: 44
        }
      },
      "38": {
        start: {
          line: 171,
          column: 10
        },
        end: {
          line: 191,
          column: 12
        }
      },
      "39": {
        start: {
          line: 175,
          column: 29
        },
        end: {
          line: 175,
          column: 46
        }
      },
      "40": {
        start: {
          line: 211,
          column: 29
        },
        end: {
          line: 211,
          column: 55
        }
      },
      "41": {
        start: {
          line: 222,
          column: 15
        },
        end: {
          line: 401,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "SocialScreen",
        decl: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 36
          }
        },
        loc: {
          start: {
            line: 26,
            column: 39
          },
          end: {
            line: 220,
            column: 1
          }
        },
        line: 26
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 32,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        loc: {
          start: {
            line: 32,
            column: 18
          },
          end: {
            line: 36,
            column: 3
          }
        },
        line: 32
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 38,
            column: 28
          },
          end: {
            line: 38,
            column: 29
          }
        },
        loc: {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 48,
            column: 3
          }
        },
        line: 38
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 43,
            column: 35
          },
          end: {
            line: 43,
            column: 36
          }
        },
        loc: {
          start: {
            line: 43,
            column: 40
          },
          end: {
            line: 43,
            column: 50
          }
        },
        line: 43
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 50,
            column: 34
          },
          end: {
            line: 50,
            column: 35
          }
        },
        loc: {
          start: {
            line: 50,
            column: 54
          },
          end: {
            line: 53,
            column: 3
          }
        },
        line: 50
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 55,
            column: 31
          },
          end: {
            line: 55,
            column: 32
          }
        },
        loc: {
          start: {
            line: 55,
            column: 51
          },
          end: {
            line: 58,
            column: 3
          }
        },
        line: 55
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 60,
            column: 30
          },
          end: {
            line: 60,
            column: 31
          }
        },
        loc: {
          start: {
            line: 60,
            column: 36
          },
          end: {
            line: 69,
            column: 3
          }
        },
        line: 60
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 66,
            column: 37
          },
          end: {
            line: 66,
            column: 38
          }
        },
        loc: {
          start: {
            line: 66,
            column: 43
          },
          end: {
            line: 66,
            column: 83
          }
        },
        line: 66
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 71,
            column: 27
          },
          end: {
            line: 71,
            column: 28
          }
        },
        loc: {
          start: {
            line: 71,
            column: 33
          },
          end: {
            line: 108,
            column: 3
          }
        },
        line: 71
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 110,
            column: 21
          },
          end: {
            line: 110,
            column: 22
          }
        },
        loc: {
          start: {
            line: 110,
            column: 60
          },
          end: {
            line: 118,
            column: 3
          }
        },
        line: 110
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 120,
            column: 22
          },
          end: {
            line: 120,
            column: 23
          }
        },
        loc: {
          start: {
            line: 120,
            column: 42
          },
          end: {
            line: 128,
            column: 3
          }
        },
        line: 120
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 158,
            column: 25
          },
          end: {
            line: 158,
            column: 26
          }
        },
        loc: {
          start: {
            line: 158,
            column: 31
          },
          end: {
            line: 158,
            column: 89
          }
        },
        line: 158
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 169,
            column: 79
          },
          end: {
            line: 169,
            column: 80
          }
        },
        loc: {
          start: {
            line: 169,
            column: 88
          },
          end: {
            line: 192,
            column: 9
          }
        },
        line: 169
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 175,
            column: 23
          },
          end: {
            line: 175,
            column: 24
          }
        },
        loc: {
          start: {
            line: 175,
            column: 29
          },
          end: {
            line: 175,
            column: 46
          }
        },
        line: 175
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 211,
            column: 23
          },
          end: {
            line: 211,
            column: 24
          }
        },
        loc: {
          start: {
            line: 211,
            column: 29
          },
          end: {
            line: 211,
            column: 55
          }
        },
        line: 211
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 33,
            column: 4
          },
          end: {
            line: 35,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 4
          },
          end: {
            line: 35,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "1": {
        loc: {
          start: {
            line: 41,
            column: 6
          },
          end: {
            line: 44,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 6
          },
          end: {
            line: 44,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "2": {
        loc: {
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 107,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 73,
            column: 6
          },
          end: {
            line: 79,
            column: 10
          }
        }, {
          start: {
            line: 80,
            column: 6
          },
          end: {
            line: 85,
            column: 10
          }
        }, {
          start: {
            line: 86,
            column: 6
          },
          end: {
            line: 91,
            column: 10
          }
        }, {
          start: {
            line: 92,
            column: 6
          },
          end: {
            line: 104,
            column: 10
          }
        }, {
          start: {
            line: 105,
            column: 6
          },
          end: {
            line: 106,
            column: 20
          }
        }],
        line: 72
      },
      "3": {
        loc: {
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 112,
            column: 46
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 112,
            column: 23
          },
          end: {
            line: 112,
            column: 29
          }
        }, {
          start: {
            line: 112,
            column: 32
          },
          end: {
            line: 112,
            column: 46
          }
        }],
        line: 112
      },
      "4": {
        loc: {
          start: {
            line: 113,
            column: 15
          },
          end: {
            line: 113,
            column: 53
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 113,
            column: 26
          },
          end: {
            line: 113,
            column: 34
          }
        }, {
          start: {
            line: 113,
            column: 37
          },
          end: {
            line: 113,
            column: 53
          }
        }],
        line: 113
      },
      "5": {
        loc: {
          start: {
            line: 114,
            column: 20
          },
          end: {
            line: 114,
            column: 58
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 114,
            column: 31
          },
          end: {
            line: 114,
            column: 39
          }
        }, {
          start: {
            line: 114,
            column: 42
          },
          end: {
            line: 114,
            column: 58
          }
        }],
        line: 114
      },
      "6": {
        loc: {
          start: {
            line: 115,
            column: 17
          },
          end: {
            line: 115,
            column: 53
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 115,
            column: 28
          },
          end: {
            line: 115,
            column: 35
          }
        }, {
          start: {
            line: 115,
            column: 38
          },
          end: {
            line: 115,
            column: 53
          }
        }],
        line: 115
      },
      "7": {
        loc: {
          start: {
            line: 140,
            column: 11
          },
          end: {
            line: 163,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 140,
            column: 11
          },
          end: {
            line: 140,
            column: 28
          }
        }, {
          start: {
            line: 141,
            column: 12
          },
          end: {
            line: 162,
            column: 15
          }
        }],
        line: 140
      },
      "8": {
        loc: {
          start: {
            line: 147,
            column: 17
          },
          end: {
            line: 153,
            column: 17
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 147,
            column: 17
          },
          end: {
            line: 147,
            column: 32
          }
        }, {
          start: {
            line: 148,
            column: 18
          },
          end: {
            line: 152,
            column: 25
          }
        }],
        line: 147
      },
      "9": {
        loc: {
          start: {
            line: 150,
            column: 23
          },
          end: {
            line: 150,
            column: 59
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 150,
            column: 41
          },
          end: {
            line: 150,
            column: 45
          }
        }, {
          start: {
            line: 150,
            column: 48
          },
          end: {
            line: 150,
            column: 59
          }
        }],
        line: 150
      },
      "10": {
        loc: {
          start: {
            line: 174,
            column: 34
          },
          end: {
            line: 174,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 174,
            column: 34
          },
          end: {
            line: 174,
            column: 42
          }
        }, {
          start: {
            line: 174,
            column: 46
          },
          end: {
            line: 174,
            column: 62
          }
        }],
        line: 174
      },
      "11": {
        loc: {
          start: {
            line: 180,
            column: 23
          },
          end: {
            line: 180,
            column: 55
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 180,
            column: 34
          },
          end: {
            line: 180,
            column: 43
          }
        }, {
          start: {
            line: 180,
            column: 46
          },
          end: {
            line: 180,
            column: 55
          }
        }],
        line: 180
      },
      "12": {
        loc: {
          start: {
            line: 182,
            column: 44
          },
          end: {
            line: 182,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 182,
            column: 44
          },
          end: {
            line: 182,
            column: 52
          }
        }, {
          start: {
            line: 182,
            column: 56
          },
          end: {
            line: 182,
            column: 76
          }
        }],
        line: 182
      },
      "13": {
        loc: {
          start: {
            line: 185,
            column: 15
          },
          end: {
            line: 189,
            column: 15
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 185,
            column: 15
          },
          end: {
            line: 185,
            column: 32
          }
        }, {
          start: {
            line: 185,
            column: 36
          },
          end: {
            line: 185,
            column: 51
          }
        }, {
          start: {
            line: 186,
            column: 16
          },
          end: {
            line: 188,
            column: 23
          }
        }],
        line: 185
      },
      "14": {
        loc: {
          start: {
            line: 201,
            column: 7
          },
          end: {
            line: 217,
            column: 7
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 201,
            column: 7
          },
          end: {
            line: 201,
            column: 25
          }
        }, {
          start: {
            line: 201,
            column: 29
          },
          end: {
            line: 201,
            column: 57
          }
        }, {
          start: {
            line: 202,
            column: 8
          },
          end: {
            line: 216,
            column: 15
          }
        }],
        line: 201
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0, 0, 0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e50d1c38bb6bdc08f3736061b8e0d6c1ad48ade6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2fwkcqgk93 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2fwkcqgk93();
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, SafeAreaView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from "../../contexts/AuthContext";
import { socialService } from "../../services/social/SocialService";
import SocialFeedScreen from "../../components/social/SocialFeedScreen";
import FriendsScreen from "../../components/social/FriendsScreen";
import LeaderboardsScreen from "../../components/social/LeaderboardsScreen";
import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
export default function SocialScreen() {
  cov_2fwkcqgk93().f[0]++;
  var _ref = (cov_2fwkcqgk93().s[0]++, useAuth()),
    isAuthenticated = _ref.isAuthenticated;
  var _ref2 = (cov_2fwkcqgk93().s[1]++, useState('feed')),
    _ref3 = _slicedToArray(_ref2, 2),
    activeTab = _ref3[0],
    setActiveTab = _ref3[1];
  var _ref4 = (cov_2fwkcqgk93().s[2]++, useState([])),
    _ref5 = _slicedToArray(_ref4, 2),
    notifications = _ref5[0],
    setNotifications = _ref5[1];
  var _ref6 = (cov_2fwkcqgk93().s[3]++, useState(0)),
    _ref7 = _slicedToArray(_ref6, 2),
    unreadCount = _ref7[0],
    setUnreadCount = _ref7[1];
  cov_2fwkcqgk93().s[4]++;
  useEffect(function () {
    cov_2fwkcqgk93().f[1]++;
    cov_2fwkcqgk93().s[5]++;
    if (isAuthenticated()) {
      cov_2fwkcqgk93().b[0][0]++;
      cov_2fwkcqgk93().s[6]++;
      loadNotifications();
    } else {
      cov_2fwkcqgk93().b[0][1]++;
    }
  }, []);
  cov_2fwkcqgk93().s[7]++;
  var loadNotifications = function () {
    var _ref8 = _asyncToGenerator(function* () {
      cov_2fwkcqgk93().f[2]++;
      cov_2fwkcqgk93().s[8]++;
      try {
        var _ref9 = (cov_2fwkcqgk93().s[9]++, yield socialService.getNotifications(10)),
          data = _ref9.notifications,
          error = _ref9.error;
        cov_2fwkcqgk93().s[10]++;
        if (data) {
          cov_2fwkcqgk93().b[1][0]++;
          cov_2fwkcqgk93().s[11]++;
          setNotifications(data);
          cov_2fwkcqgk93().s[12]++;
          setUnreadCount(data.filter(function (n) {
            cov_2fwkcqgk93().f[3]++;
            cov_2fwkcqgk93().s[13]++;
            return !n.is_read;
          }).length);
        } else {
          cov_2fwkcqgk93().b[1][1]++;
        }
      } catch (error) {
        cov_2fwkcqgk93().s[14]++;
        console.error('Error loading notifications:', error);
      }
    });
    return function loadNotifications() {
      return _ref8.apply(this, arguments);
    };
  }();
  cov_2fwkcqgk93().s[15]++;
  var handleNavigateToProfile = function handleNavigateToProfile(userId) {
    cov_2fwkcqgk93().f[4]++;
    cov_2fwkcqgk93().s[16]++;
    Alert.alert('Navigate to Profile', `Would navigate to user profile: ${userId}`);
  };
  cov_2fwkcqgk93().s[17]++;
  var handleNavigateToPost = function handleNavigateToPost(postId) {
    cov_2fwkcqgk93().f[5]++;
    cov_2fwkcqgk93().s[18]++;
    Alert.alert('Navigate to Post', `Would navigate to post: ${postId}`);
  };
  cov_2fwkcqgk93().s[19]++;
  var handleNotifications = function handleNotifications() {
    cov_2fwkcqgk93().f[6]++;
    cov_2fwkcqgk93().s[20]++;
    Alert.alert('Notifications', `You have ${unreadCount} unread notifications`, [{
      text: 'OK',
      style: 'default'
    }, {
      text: 'View All',
      onPress: function onPress() {
        cov_2fwkcqgk93().f[7]++;
        cov_2fwkcqgk93().s[21]++;
        return console.log('Navigate to notifications');
      }
    }]);
  };
  cov_2fwkcqgk93().s[22]++;
  var renderTabContent = function renderTabContent() {
    cov_2fwkcqgk93().f[8]++;
    cov_2fwkcqgk93().s[23]++;
    switch (activeTab) {
      case 'feed':
        cov_2fwkcqgk93().b[2][0]++;
        cov_2fwkcqgk93().s[24]++;
        return _jsx(SocialFeedScreen, {
          onNavigateToProfile: handleNavigateToProfile,
          onNavigateToPost: handleNavigateToPost
        });
      case 'friends':
        cov_2fwkcqgk93().b[2][1]++;
        cov_2fwkcqgk93().s[25]++;
        return _jsx(FriendsScreen, {
          onNavigateToProfile: handleNavigateToProfile
        });
      case 'leaderboards':
        cov_2fwkcqgk93().b[2][2]++;
        cov_2fwkcqgk93().s[26]++;
        return _jsx(LeaderboardsScreen, {
          onNavigateToProfile: handleNavigateToProfile
        });
      case 'community':
        cov_2fwkcqgk93().b[2][3]++;
        cov_2fwkcqgk93().s[27]++;
        return _jsxs(View, {
          style: styles.comingSoonContainer,
          children: [_jsx(Ionicons, {
            name: "people-outline",
            size: 80,
            color: "#9CA3AF"
          }), _jsx(Text, {
            style: styles.comingSoonTitle,
            children: "Community Features"
          }), _jsx(Text, {
            style: styles.comingSoonText,
            children: "Tennis clubs, tournaments, and local events coming soon!"
          }), _jsx(TouchableOpacity, {
            style: styles.comingSoonButton,
            children: _jsx(Text, {
              style: styles.comingSoonButtonText,
              children: "Get Notified"
            })
          })]
        });
      default:
        cov_2fwkcqgk93().b[2][4]++;
        cov_2fwkcqgk93().s[28]++;
        return null;
    }
  };
  cov_2fwkcqgk93().s[29]++;
  var getTabIcon = function getTabIcon(tab, isActive) {
    cov_2fwkcqgk93().f[9]++;
    var iconMap = (cov_2fwkcqgk93().s[30]++, {
      feed: isActive ? (cov_2fwkcqgk93().b[3][0]++, 'home') : (cov_2fwkcqgk93().b[3][1]++, 'home-outline'),
      friends: isActive ? (cov_2fwkcqgk93().b[4][0]++, 'people') : (cov_2fwkcqgk93().b[4][1]++, 'people-outline'),
      leaderboards: isActive ? (cov_2fwkcqgk93().b[5][0]++, 'trophy') : (cov_2fwkcqgk93().b[5][1]++, 'trophy-outline'),
      community: isActive ? (cov_2fwkcqgk93().b[6][0]++, 'globe') : (cov_2fwkcqgk93().b[6][1]++, 'globe-outline')
    });
    cov_2fwkcqgk93().s[31]++;
    return iconMap[tab];
  };
  cov_2fwkcqgk93().s[32]++;
  var getTabLabel = function getTabLabel(tab) {
    cov_2fwkcqgk93().f[10]++;
    var labelMap = (cov_2fwkcqgk93().s[33]++, {
      feed: 'Feed',
      friends: 'Friends',
      leaderboards: 'Rankings',
      community: 'Community'
    });
    cov_2fwkcqgk93().s[34]++;
    return labelMap[tab];
  };
  cov_2fwkcqgk93().s[35]++;
  return _jsxs(SafeAreaView, {
    style: styles.container,
    children: [_jsxs(View, {
      style: styles.header,
      children: [_jsxs(View, {
        style: styles.headerLeft,
        children: [_jsx(Text, {
          style: styles.headerTitle,
          children: "Tennis Social"
        }), _jsx(Text, {
          style: styles.headerSubtitle,
          children: "Connect with players worldwide"
        })]
      }), _jsx(View, {
        style: styles.headerRight,
        children: (cov_2fwkcqgk93().b[7][0]++, isAuthenticated()) && (cov_2fwkcqgk93().b[7][1]++, _jsxs(_Fragment, {
          children: [_jsxs(TouchableOpacity, {
            style: styles.headerButton,
            onPress: handleNotifications,
            children: [_jsx(Ionicons, {
              name: "notifications-outline",
              size: 24,
              color: "#374151"
            }), (cov_2fwkcqgk93().b[8][0]++, unreadCount > 0) && (cov_2fwkcqgk93().b[8][1]++, _jsx(View, {
              style: styles.notificationBadge,
              children: _jsx(Text, {
                style: styles.notificationBadgeText,
                children: unreadCount > 9 ? (cov_2fwkcqgk93().b[9][0]++, '9+') : (cov_2fwkcqgk93().b[9][1]++, unreadCount)
              })
            }))]
          }), _jsx(TouchableOpacity, {
            style: styles.headerButton,
            onPress: function onPress() {
              cov_2fwkcqgk93().f[11]++;
              cov_2fwkcqgk93().s[36]++;
              return Alert.alert('Search', 'Search functionality coming soon!');
            },
            children: _jsx(Ionicons, {
              name: "search-outline",
              size: 24,
              color: "#374151"
            })
          })]
        }))
      })]
    }), _jsx(View, {
      style: styles.tabContainer,
      children: ['feed', 'friends', 'leaderboards', 'community'].map(function (tab) {
        cov_2fwkcqgk93().f[12]++;
        var isActive = (cov_2fwkcqgk93().s[37]++, activeTab === tab);
        cov_2fwkcqgk93().s[38]++;
        return _jsxs(TouchableOpacity, {
          style: [styles.tab, (cov_2fwkcqgk93().b[10][0]++, isActive) && (cov_2fwkcqgk93().b[10][1]++, styles.activeTab)],
          onPress: function onPress() {
            cov_2fwkcqgk93().f[13]++;
            cov_2fwkcqgk93().s[39]++;
            return setActiveTab(tab);
          },
          children: [_jsx(Ionicons, {
            name: getTabIcon(tab, isActive),
            size: 20,
            color: isActive ? (cov_2fwkcqgk93().b[11][0]++, '#3B82F6') : (cov_2fwkcqgk93().b[11][1]++, '#6B7280')
          }), _jsx(Text, {
            style: [styles.tabText, (cov_2fwkcqgk93().b[12][0]++, isActive) && (cov_2fwkcqgk93().b[12][1]++, styles.activeTabText)],
            children: getTabLabel(tab)
          }), (cov_2fwkcqgk93().b[13][0]++, tab === 'friends') && (cov_2fwkcqgk93().b[13][1]++, unreadCount > 0) && (cov_2fwkcqgk93().b[13][2]++, _jsx(View, {
            style: styles.tabBadge,
            children: _jsx(Text, {
              style: styles.tabBadgeText,
              children: unreadCount
            })
          }))]
        }, tab);
      })
    }), _jsx(View, {
      style: styles.content,
      children: renderTabContent()
    }), (cov_2fwkcqgk93().b[14][0]++, !isAuthenticated()) && (cov_2fwkcqgk93().b[14][1]++, activeTab !== 'leaderboards') && (cov_2fwkcqgk93().b[14][2]++, _jsx(View, {
      style: styles.signInPrompt,
      children: _jsxs(View, {
        style: styles.signInPromptContent,
        children: [_jsx(Ionicons, {
          name: "person-add-outline",
          size: 32,
          color: "#3B82F6"
        }), _jsx(Text, {
          style: styles.signInPromptTitle,
          children: "Join the Tennis Community"
        }), _jsx(Text, {
          style: styles.signInPromptText,
          children: "Sign in to connect with players, share your progress, and compete on leaderboards"
        }), _jsx(TouchableOpacity, {
          style: styles.signInButton,
          onPress: function onPress() {
            cov_2fwkcqgk93().f[14]++;
            cov_2fwkcqgk93().s[40]++;
            return router.push('/auth/login');
          },
          children: _jsx(Text, {
            style: styles.signInButtonText,
            children: "Sign In"
          })
        })]
      })
    }))]
  });
}
var styles = (cov_2fwkcqgk93().s[41]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB'
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  headerLeft: {
    flex: 1
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827'
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  headerButton: {
    padding: 8,
    position: 'relative'
  },
  notificationBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: '#EF4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center'
  },
  notificationBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    gap: 6,
    position: 'relative'
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#3B82F6'
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280'
  },
  activeTabText: {
    color: '#3B82F6',
    fontWeight: '600'
  },
  tabBadge: {
    position: 'absolute',
    top: 6,
    right: 8,
    backgroundColor: '#EF4444',
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center'
  },
  tabBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  content: {
    flex: 1
  },
  comingSoonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32
  },
  comingSoonTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8
  },
  comingSoonText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24
  },
  comingSoonButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8
  },
  comingSoonButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  signInPrompt: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24
  },
  signInPromptContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 32,
    alignItems: 'center',
    maxWidth: 320,
    width: '100%'
  },
  signInPromptTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center'
  },
  signInPromptText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24
  },
  signInButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center'
  },
  signInButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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