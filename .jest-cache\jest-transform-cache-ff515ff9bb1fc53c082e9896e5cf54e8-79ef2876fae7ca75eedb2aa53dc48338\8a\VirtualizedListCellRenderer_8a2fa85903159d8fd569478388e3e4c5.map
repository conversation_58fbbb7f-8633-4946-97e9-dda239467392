{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_interopRequireWildcard", "_interopRequireDefault", "exports", "__esModule", "_extends2", "_objectSpread2", "_View", "_StyleSheet", "_VirtualizedListContext", "_invariant", "React", "<PERSON><PERSON><PERSON><PERSON>", "_React$Component", "_this", "arguments", "state", "separatorProps", "highlighted", "leadingItem", "props", "item", "_separators", "highlight", "_this$props", "cellKey", "prevCell<PERSON>ey", "onUpdateSeparators", "unhighlight", "_this$props2", "updateProps", "select", "newProps", "_this$props3", "_onLayout", "nativeEvent", "onCellLayout", "index", "key", "value", "updateSeparatorProps", "setState", "componentWillUnmount", "onUnmount", "_renderElement", "renderItem", "ListItemComponent", "console", "warn", "createElement", "separators", "render", "_this$props4", "CellRendererComponent", "ItemSeparatorComponent", "horizontal", "inversionStyle", "onCellFocusCapture", "element", "itemSeparator", "isValidElement", "cellStyle", "styles", "rowReverse", "columnReverse", "row", "result", "style", "onFocusCapture", "onLayout", "VirtualizedListCellContextProvider", "getDerivedStateFromProps", "prevState", "Component", "create", "flexDirection", "module"], "sources": ["VirtualizedListCellRenderer.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _View = _interopRequireDefault(require(\"../../../exports/View\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../../../exports/StyleSheet\"));\nvar _VirtualizedListContext = require(\"./VirtualizedListContext.js\");\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nvar React = _interopRequireWildcard(require(\"react\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nclass CellRenderer extends React.Component {\n  constructor() {\n    super(...arguments);\n    this.state = {\n      separatorProps: {\n        highlighted: false,\n        leadingItem: this.props.item\n      }\n    };\n    this._separators = {\n      highlight: () => {\n        var _this$props = this.props,\n          cellKey = _this$props.cellKey,\n          prevCellKey = _this$props.prevCellKey;\n        this.props.onUpdateSeparators([cellKey, prevCellKey], {\n          highlighted: true\n        });\n      },\n      unhighlight: () => {\n        var _this$props2 = this.props,\n          cellKey = _this$props2.cellKey,\n          prevCellKey = _this$props2.prevCellKey;\n        this.props.onUpdateSeparators([cellKey, prevCellKey], {\n          highlighted: false\n        });\n      },\n      updateProps: (select, newProps) => {\n        var _this$props3 = this.props,\n          cellKey = _this$props3.cellKey,\n          prevCellKey = _this$props3.prevCellKey;\n        this.props.onUpdateSeparators([select === 'leading' ? prevCellKey : cellKey], newProps);\n      }\n    };\n    this._onLayout = nativeEvent => {\n      this.props.onCellLayout && this.props.onCellLayout(nativeEvent, this.props.cellKey, this.props.index);\n    };\n  }\n  static getDerivedStateFromProps(props, prevState) {\n    return {\n      separatorProps: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, prevState.separatorProps), {}, {\n        leadingItem: props.item\n      })\n    };\n  }\n\n  // TODO: consider factoring separator stuff out of VirtualizedList into FlatList since it's not\n  // reused by SectionList and we can keep VirtualizedList simpler.\n  // $FlowFixMe[missing-local-annot]\n\n  updateSeparatorProps(newProps) {\n    this.setState(state => ({\n      separatorProps: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, state.separatorProps), newProps)\n    }));\n  }\n  componentWillUnmount() {\n    this.props.onUnmount(this.props.cellKey);\n  }\n  _renderElement(renderItem, ListItemComponent, item, index) {\n    if (renderItem && ListItemComponent) {\n      console.warn('VirtualizedList: Both ListItemComponent and renderItem props are present. ListItemComponent will take' + ' precedence over renderItem.');\n    }\n    if (ListItemComponent) {\n      /* $FlowFixMe[not-a-component] (>=0.108.0 site=react_native_fb) This\n       * comment suppresses an error found when Flow v0.108 was deployed. To\n       * see the error, delete this comment and run Flow. */\n      /* $FlowFixMe[incompatible-type-arg] (>=0.108.0 site=react_native_fb)\n       * This comment suppresses an error found when Flow v0.108 was deployed.\n       * To see the error, delete this comment and run Flow. */\n      return /*#__PURE__*/React.createElement(ListItemComponent, {\n        item,\n        index,\n        separators: this._separators\n      });\n    }\n    if (renderItem) {\n      return renderItem({\n        item,\n        index,\n        separators: this._separators\n      });\n    }\n    (0, _invariant.default)(false, 'VirtualizedList: Either ListItemComponent or renderItem props are required but none were found.');\n  }\n  render() {\n    var _this$props4 = this.props,\n      CellRendererComponent = _this$props4.CellRendererComponent,\n      ItemSeparatorComponent = _this$props4.ItemSeparatorComponent,\n      ListItemComponent = _this$props4.ListItemComponent,\n      cellKey = _this$props4.cellKey,\n      horizontal = _this$props4.horizontal,\n      item = _this$props4.item,\n      index = _this$props4.index,\n      inversionStyle = _this$props4.inversionStyle,\n      onCellFocusCapture = _this$props4.onCellFocusCapture,\n      onCellLayout = _this$props4.onCellLayout,\n      renderItem = _this$props4.renderItem;\n    var element = this._renderElement(renderItem, ListItemComponent, item, index);\n\n    // NOTE: that when this is a sticky header, `onLayout` will get automatically extracted and\n    // called explicitly by `ScrollViewStickyHeader`.\n    var itemSeparator = /*#__PURE__*/React.isValidElement(ItemSeparatorComponent) ?\n    // $FlowFixMe[incompatible-type]\n    ItemSeparatorComponent :\n    // $FlowFixMe[incompatible-type]\n    ItemSeparatorComponent && /*#__PURE__*/React.createElement(ItemSeparatorComponent, this.state.separatorProps);\n    var cellStyle = inversionStyle ? horizontal ? [styles.rowReverse, inversionStyle] : [styles.columnReverse, inversionStyle] : horizontal ? [styles.row, inversionStyle] : inversionStyle;\n    var result = !CellRendererComponent ? /*#__PURE__*/React.createElement(_View.default, (0, _extends2.default)({\n      style: cellStyle,\n      onFocusCapture: onCellFocusCapture\n    }, onCellLayout && {\n      onLayout: this._onLayout\n    }), element, itemSeparator) : /*#__PURE__*/React.createElement(CellRendererComponent, (0, _extends2.default)({\n      cellKey: cellKey,\n      index: index,\n      item: item,\n      style: cellStyle,\n      onFocusCapture: onCellFocusCapture\n    }, onCellLayout && {\n      onLayout: this._onLayout\n    }), element, itemSeparator);\n    return /*#__PURE__*/React.createElement(_VirtualizedListContext.VirtualizedListCellContextProvider, {\n      cellKey: this.props.cellKey\n    }, result);\n  }\n}\nexports.default = CellRenderer;\nvar styles = _StyleSheet.default.create({\n  row: {\n    flexDirection: 'row'\n  },\n  rowReverse: {\n    flexDirection: 'row-reverse'\n  },\n  columnReverse: {\n    flexDirection: 'column-reverse'\n  }\n});\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,UAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,SAAAM,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAJ,gBAAA,CAAAM,OAAA,EAAAF,CAAA,OAAAL,2BAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAL,gBAAA,CAAAM,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAEb,IAAIa,uBAAuB,GAAGpB,OAAO,CAAC,+CAA+C,CAAC,CAACU,OAAO;AAC9F,IAAIW,sBAAsB,GAAGrB,OAAO,CAAC,8CAA8C,CAAC,CAACU,OAAO;AAC5FY,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACZ,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIc,SAAS,GAAGH,sBAAsB,CAACrB,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIyB,cAAc,GAAGJ,sBAAsB,CAACrB,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAI0B,KAAK,GAAGL,sBAAsB,CAACrB,OAAO,wBAAwB,CAAC,CAAC;AACpE,IAAI2B,WAAW,GAAGN,sBAAsB,CAACrB,OAAO,8BAA8B,CAAC,CAAC;AAChF,IAAI4B,uBAAuB,GAAG5B,OAAO,8BAA8B,CAAC;AACpE,IAAI6B,UAAU,GAAGR,sBAAsB,CAACrB,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAI8B,KAAK,GAAGV,uBAAuB,CAACpB,OAAO,CAAC,OAAO,CAAC,CAAC;AAAC,IAWhD+B,YAAY,aAAAC,gBAAA;EAChB,SAAAD,aAAA,EAAc;IAAA,IAAAE,KAAA;IAAA,IAAAhC,gBAAA,CAAAS,OAAA,QAAAqB,YAAA;IACZE,KAAA,GAAA3B,UAAA,OAAAyB,YAAA,EAASG,SAAS;IAClBD,KAAA,CAAKE,KAAK,GAAG;MACXC,cAAc,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAEL,KAAA,CAAKM,KAAK,CAACC;MAC1B;IACF,CAAC;IACDP,KAAA,CAAKQ,WAAW,GAAG;MACjBC,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAQ;QACf,IAAIC,WAAW,GAAGV,KAAA,CAAKM,KAAK;UAC1BK,OAAO,GAAGD,WAAW,CAACC,OAAO;UAC7BC,WAAW,GAAGF,WAAW,CAACE,WAAW;QACvCZ,KAAA,CAAKM,KAAK,CAACO,kBAAkB,CAAC,CAACF,OAAO,EAAEC,WAAW,CAAC,EAAE;UACpDR,WAAW,EAAE;QACf,CAAC,CAAC;MACJ,CAAC;MACDU,WAAW,EAAE,SAAbA,WAAWA,CAAA,EAAQ;QACjB,IAAIC,YAAY,GAAGf,KAAA,CAAKM,KAAK;UAC3BK,OAAO,GAAGI,YAAY,CAACJ,OAAO;UAC9BC,WAAW,GAAGG,YAAY,CAACH,WAAW;QACxCZ,KAAA,CAAKM,KAAK,CAACO,kBAAkB,CAAC,CAACF,OAAO,EAAEC,WAAW,CAAC,EAAE;UACpDR,WAAW,EAAE;QACf,CAAC,CAAC;MACJ,CAAC;MACDY,WAAW,EAAE,SAAbA,WAAWA,CAAGC,MAAM,EAAEC,QAAQ,EAAK;QACjC,IAAIC,YAAY,GAAGnB,KAAA,CAAKM,KAAK;UAC3BK,OAAO,GAAGQ,YAAY,CAACR,OAAO;UAC9BC,WAAW,GAAGO,YAAY,CAACP,WAAW;QACxCZ,KAAA,CAAKM,KAAK,CAACO,kBAAkB,CAAC,CAACI,MAAM,KAAK,SAAS,GAAGL,WAAW,GAAGD,OAAO,CAAC,EAAEO,QAAQ,CAAC;MACzF;IACF,CAAC;IACDlB,KAAA,CAAKoB,SAAS,GAAG,UAAAC,WAAW,EAAI;MAC9BrB,KAAA,CAAKM,KAAK,CAACgB,YAAY,IAAItB,KAAA,CAAKM,KAAK,CAACgB,YAAY,CAACD,WAAW,EAAErB,KAAA,CAAKM,KAAK,CAACK,OAAO,EAAEX,KAAA,CAAKM,KAAK,CAACiB,KAAK,CAAC;IACvG,CAAC;IAAC,OAAAvB,KAAA;EACJ;EAAC,IAAA5B,UAAA,CAAAK,OAAA,EAAAqB,YAAA,EAAAC,gBAAA;EAAA,WAAA9B,aAAA,CAAAQ,OAAA,EAAAqB,YAAA;IAAA0B,GAAA;IAAAC,KAAA,EAaD,SAAAC,oBAAoBA,CAACR,QAAQ,EAAE;MAC7B,IAAI,CAACS,QAAQ,CAAC,UAAAzB,KAAK;QAAA,OAAK;UACtBC,cAAc,EAAE,CAAC,CAAC,EAAEX,cAAc,CAACf,OAAO,EAAE,CAAC,CAAC,EAAEe,cAAc,CAACf,OAAO,EAAE,CAAC,CAAC,EAAEyB,KAAK,CAACC,cAAc,CAAC,EAAEe,QAAQ;QAC7G,CAAC;MAAA,CAAC,CAAC;IACL;EAAC;IAAAM,GAAA;IAAAC,KAAA,EACD,SAAAG,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAACtB,KAAK,CAACuB,SAAS,CAAC,IAAI,CAACvB,KAAK,CAACK,OAAO,CAAC;IAC1C;EAAC;IAAAa,GAAA;IAAAC,KAAA,EACD,SAAAK,cAAcA,CAACC,UAAU,EAAEC,iBAAiB,EAAEzB,IAAI,EAAEgB,KAAK,EAAE;MACzD,IAAIQ,UAAU,IAAIC,iBAAiB,EAAE;QACnCC,OAAO,CAACC,IAAI,CAAC,uGAAuG,GAAG,8BAA8B,CAAC;MACxJ;MACA,IAAIF,iBAAiB,EAAE;QAOrB,OAAoBnC,KAAK,CAACsC,aAAa,CAACH,iBAAiB,EAAE;UACzDzB,IAAI,EAAJA,IAAI;UACJgB,KAAK,EAALA,KAAK;UACLa,UAAU,EAAE,IAAI,CAAC5B;QACnB,CAAC,CAAC;MACJ;MACA,IAAIuB,UAAU,EAAE;QACd,OAAOA,UAAU,CAAC;UAChBxB,IAAI,EAAJA,IAAI;UACJgB,KAAK,EAALA,KAAK;UACLa,UAAU,EAAE,IAAI,CAAC5B;QACnB,CAAC,CAAC;MACJ;MACA,CAAC,CAAC,EAAEZ,UAAU,CAACnB,OAAO,EAAE,KAAK,EAAE,iGAAiG,CAAC;IACnI;EAAC;IAAA+C,GAAA;IAAAC,KAAA,EACD,SAAAY,MAAMA,CAAA,EAAG;MACP,IAAIC,YAAY,GAAG,IAAI,CAAChC,KAAK;QAC3BiC,qBAAqB,GAAGD,YAAY,CAACC,qBAAqB;QAC1DC,sBAAsB,GAAGF,YAAY,CAACE,sBAAsB;QAC5DR,iBAAiB,GAAGM,YAAY,CAACN,iBAAiB;QAClDrB,OAAO,GAAG2B,YAAY,CAAC3B,OAAO;QAC9B8B,UAAU,GAAGH,YAAY,CAACG,UAAU;QACpClC,IAAI,GAAG+B,YAAY,CAAC/B,IAAI;QACxBgB,KAAK,GAAGe,YAAY,CAACf,KAAK;QAC1BmB,cAAc,GAAGJ,YAAY,CAACI,cAAc;QAC5CC,kBAAkB,GAAGL,YAAY,CAACK,kBAAkB;QACpDrB,YAAY,GAAGgB,YAAY,CAAChB,YAAY;QACxCS,UAAU,GAAGO,YAAY,CAACP,UAAU;MACtC,IAAIa,OAAO,GAAG,IAAI,CAACd,cAAc,CAACC,UAAU,EAAEC,iBAAiB,EAAEzB,IAAI,EAAEgB,KAAK,CAAC;MAI7E,IAAIsB,aAAa,GAAgBhD,KAAK,CAACiD,cAAc,CAACN,sBAAsB,CAAC,GAE7EA,sBAAsB,GAEtBA,sBAAsB,IAAiB3C,KAAK,CAACsC,aAAa,CAACK,sBAAsB,EAAE,IAAI,CAACtC,KAAK,CAACC,cAAc,CAAC;MAC7G,IAAI4C,SAAS,GAAGL,cAAc,GAAGD,UAAU,GAAG,CAACO,MAAM,CAACC,UAAU,EAAEP,cAAc,CAAC,GAAG,CAACM,MAAM,CAACE,aAAa,EAAER,cAAc,CAAC,GAAGD,UAAU,GAAG,CAACO,MAAM,CAACG,GAAG,EAAET,cAAc,CAAC,GAAGA,cAAc;MACvL,IAAIU,MAAM,GAAG,CAACb,qBAAqB,GAAgB1C,KAAK,CAACsC,aAAa,CAAC1C,KAAK,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAEc,SAAS,CAACd,OAAO,EAAE;QAC3G4E,KAAK,EAAEN,SAAS;QAChBO,cAAc,EAAEX;MAClB,CAAC,EAAErB,YAAY,IAAI;QACjBiC,QAAQ,EAAE,IAAI,CAACnC;MACjB,CAAC,CAAC,EAAEwB,OAAO,EAAEC,aAAa,CAAC,GAAgBhD,KAAK,CAACsC,aAAa,CAACI,qBAAqB,EAAE,CAAC,CAAC,EAAEhD,SAAS,CAACd,OAAO,EAAE;QAC3GkC,OAAO,EAAEA,OAAO;QAChBY,KAAK,EAAEA,KAAK;QACZhB,IAAI,EAAEA,IAAI;QACV8C,KAAK,EAAEN,SAAS;QAChBO,cAAc,EAAEX;MAClB,CAAC,EAAErB,YAAY,IAAI;QACjBiC,QAAQ,EAAE,IAAI,CAACnC;MACjB,CAAC,CAAC,EAAEwB,OAAO,EAAEC,aAAa,CAAC;MAC3B,OAAoBhD,KAAK,CAACsC,aAAa,CAACxC,uBAAuB,CAAC6D,kCAAkC,EAAE;QAClG7C,OAAO,EAAE,IAAI,CAACL,KAAK,CAACK;MACtB,CAAC,EAAEyC,MAAM,CAAC;IACZ;EAAC;IAAA5B,GAAA;IAAAC,KAAA,EAtFD,SAAOgC,wBAAwBA,CAACnD,KAAK,EAAEoD,SAAS,EAAE;MAChD,OAAO;QACLvD,cAAc,EAAE,CAAC,CAAC,EAAEX,cAAc,CAACf,OAAO,EAAE,CAAC,CAAC,EAAEe,cAAc,CAACf,OAAO,EAAE,CAAC,CAAC,EAAEiF,SAAS,CAACvD,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;UACzGE,WAAW,EAAEC,KAAK,CAACC;QACrB,CAAC;MACH,CAAC;IACH;EAAC;AAAA,EA3CwBV,KAAK,CAAC8D,SAAS;AA6H1CtE,OAAO,CAACZ,OAAO,GAAGqB,YAAY;AAC9B,IAAIkD,MAAM,GAAGtD,WAAW,CAACjB,OAAO,CAACmF,MAAM,CAAC;EACtCT,GAAG,EAAE;IACHU,aAAa,EAAE;EACjB,CAAC;EACDZ,UAAU,EAAE;IACVY,aAAa,EAAE;EACjB,CAAC;EACDX,aAAa,EAAE;IACbW,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AACFC,MAAM,CAACzE,OAAO,GAAGA,OAAO,CAACZ,OAAO", "ignoreList": []}