{"version": 3, "names": ["useMemo", "useCallback", "performanceAnalyticsService", "usePerformanceAnalysis", "data", "options", "arguments", "length", "undefined", "cov_p8qgbk0si", "b", "f", "_ref", "s", "_ref$enableTrends", "enableTrends", "_ref$enableProjection", "enableProjections", "_ref$enableRecommenda", "enableRecommendations", "_ref$analysisDepth", "<PERSON><PERSON><PERSON>h", "basicAnalysis", "matches", "_ref2", "sessions", "skillStats", "wins", "filter", "m", "result", "winRate", "avgSessionScore", "reduce", "sum", "overall_score", "overallRating", "Math", "round", "totalMatches", "totalSessions", "lastFetched", "strengthsWeaknesses", "strengths", "weaknesses", "push", "latest", "previous", "skills", "for<PERSON>ach", "skill", "current", "prev", "improvement", "recommendations", "recs", "weakness", "includes", "slice", "trends", "skillProgression", "matchPerformance", "weeklyProgress", "map", "values", "v", "previousRating", "currentRating", "trend", "changeRate", "Number", "match", "date", "created_at", "opponent", "opponent_name", "score", "match_score", "duration", "duration_minutes", "weeks", "session", "week", "Date", "toISOString", "existing", "find", "w", "sessionsCompleted", "totalScore", "Object", "assign", "averageScore", "projections", "nextMilestones", "projectedRating", "min", "timeframe", "confidence", "useMatchAnalysis", "_ref3", "_asyncToGenerator", "matchStats", "opponentInfo", "startTime", "now", "_tacticalAnalysis", "analyzeMatchPerformance", "tacticalAnalysis", "analyzeTacticalPerformance", "style", "surface", "performanceBySet", "Array", "from", "_", "i", "totalPoints", "floor", "pointsWon", "unforcedErrors", "fitnessAnalysis", "analyzeFitnessMetrics", "totalGameTime", "analysisTime", "console", "warn", "detailedMetrics", "advancedMetrics", "winPercentage", "errorRate", "doubleFaults", "winnerRate", "winners", "tacticalInsights", "successfulTactics", "error", "_x", "_x2", "apply"], "sources": ["usePerformanceAnalysis.ts"], "sourcesContent": ["/**\n * Optimized Performance Analysis Hook\n * \n * Focused hook for performance analysis with memoized calculations\n * and efficient computation strategies.\n */\n\nimport { useMemo, useCallback } from 'react';\nimport { performanceAnalyticsService, MatchStatistics } from '@/services/performanceAnalytics';\nimport { PerformanceDataCache } from './usePerformanceData';\n\nexport interface AnalysisResult {\n  overallRating: number;\n  strengths: string[];\n  weaknesses: string[];\n  recommendations: string[];\n  trends: {\n    skillProgression: any[];\n    matchPerformance: any[];\n    weeklyProgress: any[];\n  };\n  projections: {\n    skillProgression: any[];\n    nextMilestones: string[];\n  };\n}\n\ninterface UsePerformanceAnalysisOptions {\n  enableTrends?: boolean;\n  enableProjections?: boolean;\n  enableRecommendations?: boolean;\n  analysisDepth?: 'basic' | 'detailed' | 'comprehensive';\n}\n\n/**\n * Memoized performance analysis hook with selective computation\n */\nexport function usePerformanceAnalysis(\n  data: PerformanceDataCache | null,\n  options: UsePerformanceAnalysisOptions = {}\n): AnalysisResult | null {\n  const {\n    enableTrends = true,\n    enableProjections = true,\n    enableRecommendations = true,\n    analysisDepth = 'detailed',\n  } = options;\n\n  // Memoized basic analysis\n  const basicAnalysis = useMemo(() => {\n    if (!data || data.matches.length === 0) return null;\n\n    const { matches, sessions, skillStats } = data;\n    \n    // Calculate win rate\n    const wins = matches.filter(m => m.result === 'win').length;\n    const winRate = (wins / matches.length) * 100;\n    \n    // Calculate average session performance\n    const avgSessionScore = sessions.length > 0\n      ? sessions.reduce((sum, s) => sum + (s.overall_score || 0), 0) / sessions.length\n      : 0;\n    \n    // Calculate overall rating\n    const overallRating = Math.round((winRate * 0.4) + (avgSessionScore * 0.6));\n    \n    return {\n      overallRating,\n      winRate,\n      avgSessionScore,\n      totalMatches: matches.length,\n      totalSessions: sessions.length,\n    };\n  }, [data?.matches, data?.sessions, data?.lastFetched]);\n\n  // Memoized strengths and weaknesses analysis\n  const strengthsWeaknesses = useMemo(() => {\n    if (!data || !basicAnalysis) return { strengths: [], weaknesses: [] };\n\n    const strengths: string[] = [];\n    const weaknesses: string[] = [];\n    \n    // Analyze win rate\n    if (basicAnalysis.winRate > 60) {\n      strengths.push('Strong match performance');\n    } else if (basicAnalysis.winRate < 40) {\n      weaknesses.push('Struggling in matches');\n    }\n    \n    // Analyze session performance\n    if (basicAnalysis.avgSessionScore > 75) {\n      strengths.push('Consistent training performance');\n    } else if (basicAnalysis.avgSessionScore < 60) {\n      weaknesses.push('Training consistency needs improvement');\n    }\n    \n    // Analyze skill trends if available\n    if (data.skillStats.length >= 2) {\n      const latest = data.skillStats[0];\n      const previous = data.skillStats[1];\n      const skills = ['forehand', 'backhand', 'serve', 'volley', 'footwork', 'strategy', 'mental_game'];\n      \n      skills.forEach(skill => {\n        const current = latest?.[skill] || 0;\n        const prev = previous?.[skill] || 0;\n        const improvement = current - prev;\n        \n        if (improvement > 5) {\n          strengths.push(`Improving ${skill}`);\n        } else if (improvement < -5) {\n          weaknesses.push(`Declining ${skill}`);\n        }\n      });\n    }\n    \n    return { strengths, weaknesses };\n  }, [data?.skillStats, basicAnalysis]);\n\n  // Memoized recommendations\n  const recommendations = useMemo(() => {\n    if (!enableRecommendations || !basicAnalysis || !strengthsWeaknesses) return [];\n\n    const recs: string[] = [];\n    \n    // Win rate recommendations\n    if (basicAnalysis.winRate < 50) {\n      recs.push('Focus on match-specific training and strategy');\n    }\n    \n    // Session performance recommendations\n    if (basicAnalysis.avgSessionScore < 70) {\n      recs.push('Increase training intensity and consistency');\n    }\n    \n    // Skill-specific recommendations\n    strengthsWeaknesses.weaknesses.forEach(weakness => {\n      if (weakness.includes('forehand')) {\n        recs.push('Practice forehand technique with video analysis');\n      } else if (weakness.includes('serve')) {\n        recs.push('Work on serve consistency and power');\n      } else if (weakness.includes('strategy')) {\n        recs.push('Study match tactics and court positioning');\n      }\n    });\n    \n    // Limit recommendations to top 5\n    return recs.slice(0, 5);\n  }, [enableRecommendations, basicAnalysis, strengthsWeaknesses]);\n\n  // Memoized trends analysis\n  const trends = useMemo(() => {\n    if (!enableTrends || !data) {\n      return {\n        skillProgression: [],\n        matchPerformance: [],\n        weeklyProgress: [],\n      };\n    }\n\n    // Skill progression analysis\n    const skillProgression = data.skillStats.length > 1 ? (() => {\n      const skills = ['forehand', 'backhand', 'serve', 'volley', 'footwork', 'strategy', 'mental_game'];\n      \n      return skills.map(skill => {\n        const values = data.skillStats.map(s => s[skill]).filter(v => v != null);\n        if (values.length < 2) {\n          return {\n            skill,\n            previousRating: values[0] || 50,\n            currentRating: values[0] || 50,\n            trend: 'stable' as const,\n            changeRate: 0,\n          };\n        }\n\n        const previousRating = Number(values[values.length - 2]) || 50;\n        const currentRating = Number(values[values.length - 1]) || 50;\n        const changeRate = currentRating - previousRating;\n\n        return {\n          skill,\n          previousRating,\n          currentRating,\n          trend: changeRate > 2 ? ('improving' as const) : changeRate < -2 ? ('declining' as const) : ('stable' as const),\n          changeRate,\n        };\n      });\n    })() : [];\n\n    // Match performance trends\n    const matchPerformance = data.matches.slice(0, 10).map(match => ({\n      date: match.created_at,\n      opponent: match.opponent_name,\n      result: match.result,\n      score: match.match_score,\n      duration: match.duration_minutes,\n    }));\n\n    // Weekly progress (simplified)\n    const weeklyProgress = data.sessions.reduce((weeks: any[], session) => {\n      const week = new Date(session.created_at).toISOString().slice(0, 10);\n      const existing = weeks.find(w => w.week === week);\n      \n      if (existing) {\n        existing.sessionsCompleted++;\n        existing.totalScore += session.overall_score || 0;\n      } else {\n        weeks.push({\n          week,\n          sessionsCompleted: 1,\n          totalScore: session.overall_score || 0,\n        });\n      }\n      \n      return weeks;\n    }, []).map(week => ({\n      ...week,\n      averageScore: Math.round(week.totalScore / week.sessionsCompleted),\n    }));\n\n    return {\n      skillProgression,\n      matchPerformance,\n      weeklyProgress,\n    };\n  }, [enableTrends, data?.skillStats, data?.matches, data?.sessions, data?.lastFetched]);\n\n  // Memoized projections\n  const projections = useMemo(() => {\n    if (!enableProjections || !trends.skillProgression.length) {\n      return {\n        skillProgression: [],\n        nextMilestones: [],\n      };\n    }\n\n    const skillProgression = trends.skillProgression\n      .filter(s => s.trend === 'improving')\n      .map(s => ({\n        skill: s.skill,\n        currentRating: s.currentRating,\n        projectedRating: Math.min(100, s.currentRating + (s.changeRate * 4)), // 4 weeks projection\n        timeframe: '4 weeks',\n        confidence: s.changeRate > 5 ? 'high' : s.changeRate > 2 ? 'medium' : 'low',\n      }));\n\n    const nextMilestones = [\n      'Reach 80% consistency in strongest skill',\n      'Improve weakest skill by 10 points',\n      'Win next 3 out of 5 matches',\n      'Complete 10 training sessions this month',\n    ];\n\n    return {\n      skillProgression,\n      nextMilestones,\n    };\n  }, [enableProjections, trends.skillProgression]);\n\n  // Memoized final result\n  return useMemo(() => {\n    if (!basicAnalysis) return null;\n\n    return {\n      overallRating: basicAnalysis.overallRating,\n      strengths: strengthsWeaknesses.strengths,\n      weaknesses: strengthsWeaknesses.weaknesses,\n      recommendations,\n      trends,\n      projections,\n    };\n  }, [basicAnalysis, strengthsWeaknesses, recommendations, trends, projections]);\n}\n\n/**\n * Optimized match analysis hook\n */\nexport function useMatchAnalysis() {\n  return useCallback(async (matchStats: MatchStatistics, opponentInfo?: any) => {\n    const startTime = Date.now();\n    \n    try {\n      // Basic performance analysis\n      const basicAnalysis = performanceAnalyticsService.analyzeMatchPerformance(matchStats);\n      \n      // Tactical analysis if opponent info provided\n      let tacticalAnalysis = null;\n      if (opponentInfo) {\n        tacticalAnalysis = performanceAnalyticsService.analyzeTacticalPerformance(\n          matchStats,\n          opponentInfo.style || 'baseline',\n          opponentInfo.surface || 'hard'\n        );\n      }\n      \n      // Mock fitness analysis\n      const performanceBySet = Array.from({ length: 3 }, (_, i) => ({\n        ...matchStats,\n        totalPoints: Math.floor(matchStats.totalPoints / 3),\n        pointsWon: Math.floor(matchStats.pointsWon / 3) - (i * 2),\n        unforcedErrors: Math.floor(matchStats.unforcedErrors / 3) + i,\n      }));\n      \n      const fitnessAnalysis = performanceAnalyticsService.analyzeFitnessMetrics(\n        performanceBySet,\n        matchStats.totalGameTime\n      );\n      \n      const analysisTime = Date.now() - startTime;\n      \n      if (analysisTime > 1000) {\n        console.warn(`Slow match analysis: ${analysisTime}ms`);\n      }\n      \n      return {\n        overallRating: basicAnalysis.overallRating,\n        detailedMetrics: {\n          ...basicAnalysis.advancedMetrics,\n          winPercentage: (matchStats.pointsWon / matchStats.totalPoints) * 100,\n          errorRate: ((matchStats.unforcedErrors + matchStats.doubleFaults) / matchStats.totalPoints) * 100,\n          winnerRate: (matchStats.winners / matchStats.totalPoints) * 100,\n        },\n        tacticalInsights: tacticalAnalysis?.successfulTactics || [],\n        fitnessAnalysis,\n        analysisTime,\n      };\n    } catch (error) {\n      console.error('Match analysis error:', error);\n      throw error;\n    }\n  }, []);\n}\n\nexport default {\n  usePerformanceAnalysis,\n  useMatchAnalysis,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,OAAO,EAAEC,WAAW,QAAQ,OAAO;AAC5C,SAASC,2BAA2B;AA6BpC,OAAO,SAASC,sBAAsBA,CACpCC,IAAiC,EAEV;EAAA,IADvBC,OAAsC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,aAAA,GAAAC,CAAA,UAAG,CAAC,CAAC;EAAAD,aAAA,GAAAE,CAAA;EAE3C,IAAAC,IAAA,IAAAH,aAAA,GAAAI,CAAA,OAKIR,OAAO;IAAAS,iBAAA,GAAAF,IAAA,CAJTG,YAAY;IAAZA,YAAY,GAAAD,iBAAA,eAAAL,aAAA,GAAAC,CAAA,UAAG,IAAI,IAAAI,iBAAA;IAAAE,qBAAA,GAAAJ,IAAA,CACnBK,iBAAiB;IAAjBA,iBAAiB,GAAAD,qBAAA,eAAAP,aAAA,GAAAC,CAAA,UAAG,IAAI,IAAAM,qBAAA;IAAAE,qBAAA,GAAAN,IAAA,CACxBO,qBAAqB;IAArBA,qBAAqB,GAAAD,qBAAA,eAAAT,aAAA,GAAAC,CAAA,UAAG,IAAI,IAAAQ,qBAAA;IAAAE,kBAAA,GAAAR,IAAA,CAC5BS,aAAa;IAAbA,aAAa,GAAAD,kBAAA,eAAAX,aAAA,GAAAC,CAAA,UAAG,UAAU,IAAAU,kBAAA;EAI5B,IAAME,aAAa,IAAAb,aAAA,GAAAI,CAAA,OAAGb,OAAO,CAAC,YAAM;IAAAS,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IAClC,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAACN,IAAI,MAAAK,aAAA,GAAAC,CAAA,UAAIN,IAAI,CAACmB,OAAO,CAAChB,MAAM,KAAK,CAAC,GAAE;MAAAE,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;MAAAJ,aAAA,GAAAC,CAAA;IAAA;IAEpD,IAAAc,KAAA,IAAAf,aAAA,GAAAI,CAAA,OAA0CT,IAAI;MAAtCmB,OAAO,GAAAC,KAAA,CAAPD,OAAO;MAAEE,QAAQ,GAAAD,KAAA,CAARC,QAAQ;MAAEC,UAAU,GAAAF,KAAA,CAAVE,UAAU;IAGrC,IAAMC,IAAI,IAAAlB,aAAA,GAAAI,CAAA,OAAGU,OAAO,CAACK,MAAM,CAAC,UAAAC,CAAC,EAAI;MAAApB,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MAAA,OAAAgB,CAAC,CAACC,MAAM,KAAK,KAAK;IAAD,CAAC,CAAC,CAACvB,MAAM;IAC3D,IAAMwB,OAAO,IAAAtB,aAAA,GAAAI,CAAA,OAAIc,IAAI,GAAGJ,OAAO,CAAChB,MAAM,GAAI,GAAG;IAG7C,IAAMyB,eAAe,IAAAvB,aAAA,GAAAI,CAAA,OAAGY,QAAQ,CAAClB,MAAM,GAAG,CAAC,IAAAE,aAAA,GAAAC,CAAA,UACvCe,QAAQ,CAACQ,MAAM,CAAC,UAACC,GAAG,EAAErB,CAAC,EAAK;MAAAJ,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MAAA,OAAAqB,GAAG,IAAI,CAAAzB,aAAA,GAAAC,CAAA,UAAAG,CAAC,CAACsB,aAAa,MAAA1B,aAAA,GAAAC,CAAA,UAAI,CAAC,EAAC;IAAD,CAAC,EAAE,CAAC,CAAC,GAAGe,QAAQ,CAAClB,MAAM,KAAAE,aAAA,GAAAC,CAAA,UAC9E,CAAC;IAGL,IAAM0B,aAAa,IAAA3B,aAAA,GAAAI,CAAA,QAAGwB,IAAI,CAACC,KAAK,CAAEP,OAAO,GAAG,GAAG,GAAKC,eAAe,GAAG,GAAI,CAAC;IAACvB,aAAA,GAAAI,CAAA;IAE5E,OAAO;MACLuB,aAAa,EAAbA,aAAa;MACbL,OAAO,EAAPA,OAAO;MACPC,eAAe,EAAfA,eAAe;MACfO,YAAY,EAAEhB,OAAO,CAAChB,MAAM;MAC5BiC,aAAa,EAAEf,QAAQ,CAAClB;IAC1B,CAAC;EACH,CAAC,EAAE,CAACH,IAAI,oBAAJA,IAAI,CAAEmB,OAAO,EAAEnB,IAAI,oBAAJA,IAAI,CAAEqB,QAAQ,EAAErB,IAAI,oBAAJA,IAAI,CAAEqC,WAAW,CAAC,CAAC;EAGtD,IAAMC,mBAAmB,IAAAjC,aAAA,GAAAI,CAAA,QAAGb,OAAO,CAAC,YAAM;IAAAS,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IACxC,IAAI,CAAAJ,aAAA,GAAAC,CAAA,YAACN,IAAI,MAAAK,aAAA,GAAAC,CAAA,WAAI,CAACY,aAAa,GAAE;MAAAb,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAAA,OAAO;QAAE8B,SAAS,EAAE,EAAE;QAAEC,UAAU,EAAE;MAAG,CAAC;IAAA,CAAC;MAAAnC,aAAA,GAAAC,CAAA;IAAA;IAEtE,IAAMiC,SAAmB,IAAAlC,aAAA,GAAAI,CAAA,QAAG,EAAE;IAC9B,IAAM+B,UAAoB,IAAAnC,aAAA,GAAAI,CAAA,QAAG,EAAE;IAACJ,aAAA,GAAAI,CAAA;IAGhC,IAAIS,aAAa,CAACS,OAAO,GAAG,EAAE,EAAE;MAAAtB,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAC9B8B,SAAS,CAACE,IAAI,CAAC,0BAA0B,CAAC;IAC5C,CAAC,MAAM;MAAApC,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAAA,IAAIS,aAAa,CAACS,OAAO,GAAG,EAAE,EAAE;QAAAtB,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACrC+B,UAAU,CAACC,IAAI,CAAC,uBAAuB,CAAC;MAC1C,CAAC;QAAApC,aAAA,GAAAC,CAAA;MAAA;IAAD;IAACD,aAAA,GAAAI,CAAA;IAGD,IAAIS,aAAa,CAACU,eAAe,GAAG,EAAE,EAAE;MAAAvB,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MACtC8B,SAAS,CAACE,IAAI,CAAC,iCAAiC,CAAC;IACnD,CAAC,MAAM;MAAApC,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAAA,IAAIS,aAAa,CAACU,eAAe,GAAG,EAAE,EAAE;QAAAvB,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QAC7C+B,UAAU,CAACC,IAAI,CAAC,wCAAwC,CAAC;MAC3D,CAAC;QAAApC,aAAA,GAAAC,CAAA;MAAA;IAAD;IAACD,aAAA,GAAAI,CAAA;IAGD,IAAIT,IAAI,CAACsB,UAAU,CAACnB,MAAM,IAAI,CAAC,EAAE;MAAAE,aAAA,GAAAC,CAAA;MAC/B,IAAMoC,MAAM,IAAArC,aAAA,GAAAI,CAAA,QAAGT,IAAI,CAACsB,UAAU,CAAC,CAAC,CAAC;MACjC,IAAMqB,QAAQ,IAAAtC,aAAA,GAAAI,CAAA,QAAGT,IAAI,CAACsB,UAAU,CAAC,CAAC,CAAC;MACnC,IAAMsB,MAAM,IAAAvC,aAAA,GAAAI,CAAA,QAAG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,CAAC;MAACJ,aAAA,GAAAI,CAAA;MAElGmC,MAAM,CAACC,OAAO,CAAC,UAAAC,KAAK,EAAI;QAAAzC,aAAA,GAAAE,CAAA;QACtB,IAAMwC,OAAO,IAAA1C,aAAA,GAAAI,CAAA,QAAG,CAAAJ,aAAA,GAAAC,CAAA,WAAAoC,MAAM,oBAANA,MAAM,CAAGI,KAAK,CAAC,MAAAzC,aAAA,GAAAC,CAAA,WAAI,CAAC;QACpC,IAAM0C,IAAI,IAAA3C,aAAA,GAAAI,CAAA,QAAG,CAAAJ,aAAA,GAAAC,CAAA,WAAAqC,QAAQ,oBAARA,QAAQ,CAAGG,KAAK,CAAC,MAAAzC,aAAA,GAAAC,CAAA,WAAI,CAAC;QACnC,IAAM2C,WAAW,IAAA5C,aAAA,GAAAI,CAAA,QAAGsC,OAAO,GAAGC,IAAI;QAAC3C,aAAA,GAAAI,CAAA;QAEnC,IAAIwC,WAAW,GAAG,CAAC,EAAE;UAAA5C,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACnB8B,SAAS,CAACE,IAAI,CAAC,aAAaK,KAAK,EAAE,CAAC;QACtC,CAAC,MAAM;UAAAzC,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UAAA,IAAIwC,WAAW,GAAG,CAAC,CAAC,EAAE;YAAA5C,aAAA,GAAAC,CAAA;YAAAD,aAAA,GAAAI,CAAA;YAC3B+B,UAAU,CAACC,IAAI,CAAC,aAAaK,KAAK,EAAE,CAAC;UACvC,CAAC;YAAAzC,aAAA,GAAAC,CAAA;UAAA;QAAD;MACF,CAAC,CAAC;IACJ,CAAC;MAAAD,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAED,OAAO;MAAE8B,SAAS,EAATA,SAAS;MAAEC,UAAU,EAAVA;IAAW,CAAC;EAClC,CAAC,EAAE,CAACxC,IAAI,oBAAJA,IAAI,CAAEsB,UAAU,EAAEJ,aAAa,CAAC,CAAC;EAGrC,IAAMgC,eAAe,IAAA7C,aAAA,GAAAI,CAAA,QAAGb,OAAO,CAAC,YAAM;IAAAS,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IACpC,IAAI,CAAAJ,aAAA,GAAAC,CAAA,YAACS,qBAAqB,MAAAV,aAAA,GAAAC,CAAA,WAAI,CAACY,aAAa,MAAAb,aAAA,GAAAC,CAAA,WAAI,CAACgC,mBAAmB,GAAE;MAAAjC,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;MAAAJ,aAAA,GAAAC,CAAA;IAAA;IAEhF,IAAM6C,IAAc,IAAA9C,aAAA,GAAAI,CAAA,QAAG,EAAE;IAACJ,aAAA,GAAAI,CAAA;IAG1B,IAAIS,aAAa,CAACS,OAAO,GAAG,EAAE,EAAE;MAAAtB,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAC9B0C,IAAI,CAACV,IAAI,CAAC,+CAA+C,CAAC;IAC5D,CAAC;MAAApC,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAGD,IAAIS,aAAa,CAACU,eAAe,GAAG,EAAE,EAAE;MAAAvB,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MACtC0C,IAAI,CAACV,IAAI,CAAC,6CAA6C,CAAC;IAC1D,CAAC;MAAApC,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAGD6B,mBAAmB,CAACE,UAAU,CAACK,OAAO,CAAC,UAAAO,QAAQ,EAAI;MAAA/C,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MACjD,IAAI2C,QAAQ,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;QAAAhD,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACjC0C,IAAI,CAACV,IAAI,CAAC,iDAAiD,CAAC;MAC9D,CAAC,MAAM;QAAApC,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QAAA,IAAI2C,QAAQ,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;UAAAhD,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACrC0C,IAAI,CAACV,IAAI,CAAC,qCAAqC,CAAC;QAClD,CAAC,MAAM;UAAApC,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UAAA,IAAI2C,QAAQ,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;YAAAhD,aAAA,GAAAC,CAAA;YAAAD,aAAA,GAAAI,CAAA;YACxC0C,IAAI,CAACV,IAAI,CAAC,2CAA2C,CAAC;UACxD,CAAC;YAAApC,aAAA,GAAAC,CAAA;UAAA;QAAD;MAAA;IACF,CAAC,CAAC;IAACD,aAAA,GAAAI,CAAA;IAGH,OAAO0C,IAAI,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACzB,CAAC,EAAE,CAACvC,qBAAqB,EAAEG,aAAa,EAAEoB,mBAAmB,CAAC,CAAC;EAG/D,IAAMiB,MAAM,IAAAlD,aAAA,GAAAI,CAAA,QAAGb,OAAO,CAAC,YAAM;IAAAS,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IAC3B,IAAI,CAAAJ,aAAA,GAAAC,CAAA,YAACK,YAAY,MAAAN,aAAA,GAAAC,CAAA,WAAI,CAACN,IAAI,GAAE;MAAAK,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAC1B,OAAO;QACL+C,gBAAgB,EAAE,EAAE;QACpBC,gBAAgB,EAAE,EAAE;QACpBC,cAAc,EAAE;MAClB,CAAC;IACH,CAAC;MAAArD,aAAA,GAAAC,CAAA;IAAA;IAGD,IAAMkD,gBAAgB,IAAAnD,aAAA,GAAAI,CAAA,QAAGT,IAAI,CAACsB,UAAU,CAACnB,MAAM,GAAG,CAAC,IAAAE,aAAA,GAAAC,CAAA,WAAI,YAAM;MAAAD,aAAA,GAAAE,CAAA;MAC3D,IAAMqC,MAAM,IAAAvC,aAAA,GAAAI,CAAA,QAAG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,CAAC;MAACJ,aAAA,GAAAI,CAAA;MAElG,OAAOmC,MAAM,CAACe,GAAG,CAAC,UAAAb,KAAK,EAAI;QAAAzC,aAAA,GAAAE,CAAA;QACzB,IAAMqD,MAAM,IAAAvD,aAAA,GAAAI,CAAA,QAAGT,IAAI,CAACsB,UAAU,CAACqC,GAAG,CAAC,UAAAlD,CAAC,EAAI;UAAAJ,aAAA,GAAAE,CAAA;UAAAF,aAAA,GAAAI,CAAA;UAAA,OAAAA,CAAC,CAACqC,KAAK,CAAC;QAAD,CAAC,CAAC,CAACtB,MAAM,CAAC,UAAAqC,CAAC,EAAI;UAAAxD,aAAA,GAAAE,CAAA;UAAAF,aAAA,GAAAI,CAAA;UAAA,OAAAoD,CAAC,IAAI,IAAI;QAAD,CAAC,CAAC;QAACxD,aAAA,GAAAI,CAAA;QACzE,IAAImD,MAAM,CAACzD,MAAM,GAAG,CAAC,EAAE;UAAAE,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACrB,OAAO;YACLqC,KAAK,EAALA,KAAK;YACLgB,cAAc,EAAE,CAAAzD,aAAA,GAAAC,CAAA,WAAAsD,MAAM,CAAC,CAAC,CAAC,MAAAvD,aAAA,GAAAC,CAAA,WAAI,EAAE;YAC/ByD,aAAa,EAAE,CAAA1D,aAAA,GAAAC,CAAA,WAAAsD,MAAM,CAAC,CAAC,CAAC,MAAAvD,aAAA,GAAAC,CAAA,WAAI,EAAE;YAC9B0D,KAAK,EAAE,QAAiB;YACxBC,UAAU,EAAE;UACd,CAAC;QACH,CAAC;UAAA5D,aAAA,GAAAC,CAAA;QAAA;QAED,IAAMwD,cAAc,IAAAzD,aAAA,GAAAI,CAAA,QAAG,CAAAJ,aAAA,GAAAC,CAAA,WAAA4D,MAAM,CAACN,MAAM,CAACA,MAAM,CAACzD,MAAM,GAAG,CAAC,CAAC,CAAC,MAAAE,aAAA,GAAAC,CAAA,WAAI,EAAE;QAC9D,IAAMyD,aAAa,IAAA1D,aAAA,GAAAI,CAAA,QAAG,CAAAJ,aAAA,GAAAC,CAAA,WAAA4D,MAAM,CAACN,MAAM,CAACA,MAAM,CAACzD,MAAM,GAAG,CAAC,CAAC,CAAC,MAAAE,aAAA,GAAAC,CAAA,WAAI,EAAE;QAC7D,IAAM2D,UAAU,IAAA5D,aAAA,GAAAI,CAAA,QAAGsD,aAAa,GAAGD,cAAc;QAACzD,aAAA,GAAAI,CAAA;QAElD,OAAO;UACLqC,KAAK,EAALA,KAAK;UACLgB,cAAc,EAAdA,cAAc;UACdC,aAAa,EAAbA,aAAa;UACbC,KAAK,EAAEC,UAAU,GAAG,CAAC,IAAA5D,aAAA,GAAAC,CAAA,WAAI,WAAW,KAAAD,aAAA,GAAAC,CAAA,WAAa2D,UAAU,GAAG,CAAC,CAAC,IAAA5D,aAAA,GAAAC,CAAA,WAAI,WAAW,KAAAD,aAAA,GAAAC,CAAA,WAAc,QAAQ,CAAU;UAC/G2D,UAAU,EAAVA;QACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAE,CAAC,KAAA5D,aAAA,GAAAC,CAAA,WAAG,EAAE;IAGT,IAAMmD,gBAAgB,IAAApD,aAAA,GAAAI,CAAA,QAAGT,IAAI,CAACmB,OAAO,CAACmC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACK,GAAG,CAAC,UAAAQ,KAAK,EAAK;MAAA9D,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MAAA;QAC/D2D,IAAI,EAAED,KAAK,CAACE,UAAU;QACtBC,QAAQ,EAAEH,KAAK,CAACI,aAAa;QAC7B7C,MAAM,EAAEyC,KAAK,CAACzC,MAAM;QACpB8C,KAAK,EAAEL,KAAK,CAACM,WAAW;QACxBC,QAAQ,EAAEP,KAAK,CAACQ;MAClB,CAAC;IAAD,CAAE,CAAC;IAGH,IAAMjB,cAAc,IAAArD,aAAA,GAAAI,CAAA,QAAGT,IAAI,CAACqB,QAAQ,CAACQ,MAAM,CAAC,UAAC+C,KAAY,EAAEC,OAAO,EAAK;MAAAxE,aAAA,GAAAE,CAAA;MACrE,IAAMuE,IAAI,IAAAzE,aAAA,GAAAI,CAAA,QAAG,IAAIsE,IAAI,CAACF,OAAO,CAACR,UAAU,CAAC,CAACW,WAAW,CAAC,CAAC,CAAC1B,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MACpE,IAAM2B,QAAQ,IAAA5E,aAAA,GAAAI,CAAA,QAAGmE,KAAK,CAACM,IAAI,CAAC,UAAAC,CAAC,EAAI;QAAA9E,aAAA,GAAAE,CAAA;QAAAF,aAAA,GAAAI,CAAA;QAAA,OAAA0E,CAAC,CAACL,IAAI,KAAKA,IAAI;MAAD,CAAC,CAAC;MAACzE,aAAA,GAAAI,CAAA;MAElD,IAAIwE,QAAQ,EAAE;QAAA5E,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACZwE,QAAQ,CAACG,iBAAiB,EAAE;QAAC/E,aAAA,GAAAI,CAAA;QAC7BwE,QAAQ,CAACI,UAAU,IAAI,CAAAhF,aAAA,GAAAC,CAAA,WAAAuE,OAAO,CAAC9C,aAAa,MAAA1B,aAAA,GAAAC,CAAA,WAAI,CAAC;MACnD,CAAC,MAAM;QAAAD,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACLmE,KAAK,CAACnC,IAAI,CAAC;UACTqC,IAAI,EAAJA,IAAI;UACJM,iBAAiB,EAAE,CAAC;UACpBC,UAAU,EAAE,CAAAhF,aAAA,GAAAC,CAAA,WAAAuE,OAAO,CAAC9C,aAAa,MAAA1B,aAAA,GAAAC,CAAA,WAAI,CAAC;QACxC,CAAC,CAAC;MACJ;MAACD,aAAA,GAAAI,CAAA;MAED,OAAOmE,KAAK;IACd,CAAC,EAAE,EAAE,CAAC,CAACjB,GAAG,CAAC,UAAAmB,IAAI,EAAK;MAAAzE,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MAAA,OAAA6E,MAAA,CAAAC,MAAA,KACfT,IAAI;QACPU,YAAY,EAAEvD,IAAI,CAACC,KAAK,CAAC4C,IAAI,CAACO,UAAU,GAAGP,IAAI,CAACM,iBAAiB;MAAC;IACpE,CAAE,CAAC;IAAC/E,aAAA,GAAAI,CAAA;IAEJ,OAAO;MACL+C,gBAAgB,EAAhBA,gBAAgB;MAChBC,gBAAgB,EAAhBA,gBAAgB;MAChBC,cAAc,EAAdA;IACF,CAAC;EACH,CAAC,EAAE,CAAC/C,YAAY,EAAEX,IAAI,oBAAJA,IAAI,CAAEsB,UAAU,EAAEtB,IAAI,oBAAJA,IAAI,CAAEmB,OAAO,EAAEnB,IAAI,oBAAJA,IAAI,CAAEqB,QAAQ,EAAErB,IAAI,oBAAJA,IAAI,CAAEqC,WAAW,CAAC,CAAC;EAGtF,IAAMoD,WAAW,IAAApF,aAAA,GAAAI,CAAA,QAAGb,OAAO,CAAC,YAAM;IAAAS,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IAChC,IAAI,CAAAJ,aAAA,GAAAC,CAAA,YAACO,iBAAiB,MAAAR,aAAA,GAAAC,CAAA,WAAI,CAACiD,MAAM,CAACC,gBAAgB,CAACrD,MAAM,GAAE;MAAAE,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MACzD,OAAO;QACL+C,gBAAgB,EAAE,EAAE;QACpBkC,cAAc,EAAE;MAClB,CAAC;IACH,CAAC;MAAArF,aAAA,GAAAC,CAAA;IAAA;IAED,IAAMkD,gBAAgB,IAAAnD,aAAA,GAAAI,CAAA,QAAG8C,MAAM,CAACC,gBAAgB,CAC7ChC,MAAM,CAAC,UAAAf,CAAC,EAAI;MAAAJ,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MAAA,OAAAA,CAAC,CAACuD,KAAK,KAAK,WAAW;IAAD,CAAC,CAAC,CACpCL,GAAG,CAAC,UAAAlD,CAAC,EAAK;MAAAJ,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MAAA;QACTqC,KAAK,EAAErC,CAAC,CAACqC,KAAK;QACdiB,aAAa,EAAEtD,CAAC,CAACsD,aAAa;QAC9B4B,eAAe,EAAE1D,IAAI,CAAC2D,GAAG,CAAC,GAAG,EAAEnF,CAAC,CAACsD,aAAa,GAAItD,CAAC,CAACwD,UAAU,GAAG,CAAE,CAAC;QACpE4B,SAAS,EAAE,SAAS;QACpBC,UAAU,EAAErF,CAAC,CAACwD,UAAU,GAAG,CAAC,IAAA5D,aAAA,GAAAC,CAAA,WAAG,MAAM,KAAAD,aAAA,GAAAC,CAAA,WAAGG,CAAC,CAACwD,UAAU,GAAG,CAAC,IAAA5D,aAAA,GAAAC,CAAA,WAAG,QAAQ,KAAAD,aAAA,GAAAC,CAAA,WAAG,KAAK;MAC7E,CAAC;IAAD,CAAE,CAAC;IAEL,IAAMoF,cAAc,IAAArF,aAAA,GAAAI,CAAA,QAAG,CACrB,0CAA0C,EAC1C,oCAAoC,EACpC,6BAA6B,EAC7B,0CAA0C,CAC3C;IAACJ,aAAA,GAAAI,CAAA;IAEF,OAAO;MACL+C,gBAAgB,EAAhBA,gBAAgB;MAChBkC,cAAc,EAAdA;IACF,CAAC;EACH,CAAC,EAAE,CAAC7E,iBAAiB,EAAE0C,MAAM,CAACC,gBAAgB,CAAC,CAAC;EAACnD,aAAA,GAAAI,CAAA;EAGjD,OAAOb,OAAO,CAAC,YAAM;IAAAS,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IACnB,IAAI,CAACS,aAAa,EAAE;MAAAb,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;MAAAJ,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAEhC,OAAO;MACLuB,aAAa,EAAEd,aAAa,CAACc,aAAa;MAC1CO,SAAS,EAAED,mBAAmB,CAACC,SAAS;MACxCC,UAAU,EAAEF,mBAAmB,CAACE,UAAU;MAC1CU,eAAe,EAAfA,eAAe;MACfK,MAAM,EAANA,MAAM;MACNkC,WAAW,EAAXA;IACF,CAAC;EACH,CAAC,EAAE,CAACvE,aAAa,EAAEoB,mBAAmB,EAAEY,eAAe,EAAEK,MAAM,EAAEkC,WAAW,CAAC,CAAC;AAChF;AAKA,OAAO,SAASM,gBAAgBA,CAAA,EAAG;EAAA1F,aAAA,GAAAE,CAAA;EAAAF,aAAA,GAAAI,CAAA;EACjC,OAAOZ,WAAW;IAAA,IAAAmG,KAAA,GAAAC,iBAAA,CAAC,WAAOC,UAA2B,EAAEC,YAAkB,EAAK;MAAA9F,aAAA,GAAAE,CAAA;MAC5E,IAAM6F,SAAS,IAAA/F,aAAA,GAAAI,CAAA,QAAGsE,IAAI,CAACsB,GAAG,CAAC,CAAC;MAAChG,aAAA,GAAAI,CAAA;MAE7B,IAAI;QAAA,IAAA6F,iBAAA;QAEF,IAAMpF,aAAa,IAAAb,aAAA,GAAAI,CAAA,QAAGX,2BAA2B,CAACyG,uBAAuB,CAACL,UAAU,CAAC;QAGrF,IAAIM,gBAAgB,IAAAnG,aAAA,GAAAI,CAAA,QAAG,IAAI;QAACJ,aAAA,GAAAI,CAAA;QAC5B,IAAI0F,YAAY,EAAE;UAAA9F,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UAChB+F,gBAAgB,GAAG1G,2BAA2B,CAAC2G,0BAA0B,CACvEP,UAAU,EACV,CAAA7F,aAAA,GAAAC,CAAA,WAAA6F,YAAY,CAACO,KAAK,MAAArG,aAAA,GAAAC,CAAA,WAAI,UAAU,GAChC,CAAAD,aAAA,GAAAC,CAAA,WAAA6F,YAAY,CAACQ,OAAO,MAAAtG,aAAA,GAAAC,CAAA,WAAI,MAAM,CAChC,CAAC;QACH,CAAC;UAAAD,aAAA,GAAAC,CAAA;QAAA;QAGD,IAAMsG,gBAAgB,IAAAvG,aAAA,GAAAI,CAAA,SAAGoG,KAAK,CAACC,IAAI,CAAC;UAAE3G,MAAM,EAAE;QAAE,CAAC,EAAE,UAAC4G,CAAC,EAAEC,CAAC,EAAM;UAAA3G,aAAA,GAAAE,CAAA;UAAAF,aAAA,GAAAI,CAAA;UAAA,OAAA6E,MAAA,CAAAC,MAAA,KACzDW,UAAU;YACbe,WAAW,EAAEhF,IAAI,CAACiF,KAAK,CAAChB,UAAU,CAACe,WAAW,GAAG,CAAC,CAAC;YACnDE,SAAS,EAAElF,IAAI,CAACiF,KAAK,CAAChB,UAAU,CAACiB,SAAS,GAAG,CAAC,CAAC,GAAIH,CAAC,GAAG,CAAE;YACzDI,cAAc,EAAEnF,IAAI,CAACiF,KAAK,CAAChB,UAAU,CAACkB,cAAc,GAAG,CAAC,CAAC,GAAGJ;UAAC;QAC/D,CAAE,CAAC;QAEH,IAAMK,eAAe,IAAAhH,aAAA,GAAAI,CAAA,SAAGX,2BAA2B,CAACwH,qBAAqB,CACvEV,gBAAgB,EAChBV,UAAU,CAACqB,aACb,CAAC;QAED,IAAMC,YAAY,IAAAnH,aAAA,GAAAI,CAAA,SAAGsE,IAAI,CAACsB,GAAG,CAAC,CAAC,GAAGD,SAAS;QAAC/F,aAAA,GAAAI,CAAA;QAE5C,IAAI+G,YAAY,GAAG,IAAI,EAAE;UAAAnH,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACvBgH,OAAO,CAACC,IAAI,CAAC,wBAAwBF,YAAY,IAAI,CAAC;QACxD,CAAC;UAAAnH,aAAA,GAAAC,CAAA;QAAA;QAAAD,aAAA,GAAAI,CAAA;QAED,OAAO;UACLuB,aAAa,EAAEd,aAAa,CAACc,aAAa;UAC1C2F,eAAe,EAAArC,MAAA,CAAAC,MAAA,KACVrE,aAAa,CAAC0G,eAAe;YAChCC,aAAa,EAAG3B,UAAU,CAACiB,SAAS,GAAGjB,UAAU,CAACe,WAAW,GAAI,GAAG;YACpEa,SAAS,EAAG,CAAC5B,UAAU,CAACkB,cAAc,GAAGlB,UAAU,CAAC6B,YAAY,IAAI7B,UAAU,CAACe,WAAW,GAAI,GAAG;YACjGe,UAAU,EAAG9B,UAAU,CAAC+B,OAAO,GAAG/B,UAAU,CAACe,WAAW,GAAI;UAAG,EAChE;UACDiB,gBAAgB,EAAE,CAAA7H,aAAA,GAAAC,CAAA,YAAAgG,iBAAA,GAAAE,gBAAgB,qBAAhBF,iBAAA,CAAkB6B,iBAAiB,MAAA9H,aAAA,GAAAC,CAAA,WAAI,EAAE;UAC3D+G,eAAe,EAAfA,eAAe;UACfG,YAAY,EAAZA;QACF,CAAC;MACH,CAAC,CAAC,OAAOY,KAAK,EAAE;QAAA/H,aAAA,GAAAI,CAAA;QACdgH,OAAO,CAACW,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAAC/H,aAAA,GAAAI,CAAA;QAC9C,MAAM2H,KAAK;MACb;IACF,CAAC;IAAA,iBAAAC,EAAA,EAAAC,GAAA;MAAA,OAAAtC,KAAA,CAAAuC,KAAA,OAAArI,SAAA;IAAA;EAAA,KAAE,EAAE,CAAC;AACR;AAEA,eAAe;EACbH,sBAAsB,EAAtBA,sBAAsB;EACtBgG,gBAAgB,EAAhBA;AACF,CAAC", "ignoreList": []}