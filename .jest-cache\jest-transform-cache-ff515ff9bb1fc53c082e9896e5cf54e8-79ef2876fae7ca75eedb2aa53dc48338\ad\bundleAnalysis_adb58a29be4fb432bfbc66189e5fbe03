c8dba4add9133105920c6002a4388090
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_11ic3jy76j() {
  var path = "C:\\_SaaS\\AceMind\\project\\utils\\bundleAnalysis.ts";
  var hash = "b0efc60740ffbcc47864717f57d8497f56c0a151";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\utils\\bundleAnalysis.ts",
    statementMap: {
      "0": {
        start: {
          line: 47,
          column: 42
        },
        end: {
          line: 47,
          column: 46
        }
      },
      "1": {
        start: {
          line: 48,
          column: 22
        },
        end: {
          line: 48,
          column: 49
        }
      },
      "2": {
        start: {
          line: 49,
          column: 23
        },
        end: {
          line: 49,
          column: 51
        }
      },
      "3": {
        start: {
          line: 55,
          column: 22
        },
        end: {
          line: 55,
          column: 32
        }
      },
      "4": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 80,
          column: 5
        }
      },
      "5": {
        start: {
          line: 59,
          column: 21
        },
        end: {
          line: 59,
          column: 47
        }
      },
      "6": {
        start: {
          line: 60,
          column: 27
        },
        end: {
          line: 60,
          column: 59
        }
      },
      "7": {
        start: {
          line: 62,
          column: 24
        },
        end: {
          line: 62,
          column: 74
        }
      },
      "8": {
        start: {
          line: 62,
          column: 54
        },
        end: {
          line: 62,
          column: 70
        }
      },
      "9": {
        start: {
          line: 63,
          column: 26
        },
        end: {
          line: 63,
          column: 53
        }
      },
      "10": {
        start: {
          line: 65,
          column: 37
        },
        end: {
          line: 72,
          column: 7
        }
      },
      "11": {
        start: {
          line: 74,
          column: 6
        },
        end: {
          line: 74,
          column: 29
        }
      },
      "12": {
        start: {
          line: 75,
          column: 6
        },
        end: {
          line: 75,
          column: 21
        }
      },
      "13": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 78,
          column: 54
        }
      },
      "14": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 79,
          column: 18
        }
      },
      "15": {
        start: {
          line: 88,
          column: 32
        },
        end: {
          line: 131,
          column: 5
        }
      },
      "16": {
        start: {
          line: 134,
          column: 4
        },
        end: {
          line: 136,
          column: 7
        }
      },
      "17": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 135,
          column: 45
        }
      },
      "18": {
        start: {
          line: 138,
          column: 4
        },
        end: {
          line: 138,
          column: 18
        }
      },
      "19": {
        start: {
          line: 146,
          column: 4
        },
        end: {
          line: 182,
          column: 6
        }
      },
      "20": {
        start: {
          line: 189,
          column: 4
        },
        end: {
          line: 191,
          column: 5
        }
      },
      "21": {
        start: {
          line: 190,
          column: 6
        },
        end: {
          line: 190,
          column: 59
        }
      },
      "22": {
        start: {
          line: 193,
          column: 50
        },
        end: {
          line: 193,
          column: 52
        }
      },
      "23": {
        start: {
          line: 196,
          column: 24
        },
        end: {
          line: 196,
          column: 80
        }
      },
      "24": {
        start: {
          line: 196,
          column: 60
        },
        end: {
          line: 196,
          column: 79
        }
      },
      "25": {
        start: {
          line: 197,
          column: 4
        },
        end: {
          line: 205,
          column: 5
        }
      },
      "26": {
        start: {
          line: 198,
          column: 6
        },
        end: {
          line: 204,
          column: 9
        }
      },
      "27": {
        start: {
          line: 201,
          column: 65
        },
        end: {
          line: 201,
          column: 71
        }
      },
      "28": {
        start: {
          line: 208,
          column: 27
        },
        end: {
          line: 208,
          column: 77
        }
      },
      "29": {
        start: {
          line: 208,
          column: 63
        },
        end: {
          line: 208,
          column: 76
        }
      },
      "30": {
        start: {
          line: 209,
          column: 4
        },
        end: {
          line: 217,
          column: 5
        }
      },
      "31": {
        start: {
          line: 210,
          column: 6
        },
        end: {
          line: 216,
          column: 9
        }
      },
      "32": {
        start: {
          line: 220,
          column: 30
        },
        end: {
          line: 220,
          column: 88
        }
      },
      "33": {
        start: {
          line: 220,
          column: 70
        },
        end: {
          line: 220,
          column: 87
        }
      },
      "34": {
        start: {
          line: 221,
          column: 4
        },
        end: {
          line: 229,
          column: 5
        }
      },
      "35": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 228,
          column: 9
        }
      },
      "36": {
        start: {
          line: 232,
          column: 4
        },
        end: {
          line: 240,
          column: 5
        }
      },
      "37": {
        start: {
          line: 233,
          column: 6
        },
        end: {
          line: 239,
          column: 9
        }
      },
      "38": {
        start: {
          line: 242,
          column: 4
        },
        end: {
          line: 245,
          column: 7
        }
      },
      "39": {
        start: {
          line: 243,
          column: 28
        },
        end: {
          line: 243,
          column: 58
        }
      },
      "40": {
        start: {
          line: 244,
          column: 6
        },
        end: {
          line: 244,
          column: 67
        }
      },
      "41": {
        start: {
          line: 252,
          column: 4
        },
        end: {
          line: 254,
          column: 5
        }
      },
      "42": {
        start: {
          line: 253,
          column: 6
        },
        end: {
          line: 253,
          column: 40
        }
      },
      "43": {
        start: {
          line: 256,
          column: 18
        },
        end: {
          line: 256,
          column: 48
        }
      },
      "44": {
        start: {
          line: 257,
          column: 4
        },
        end: {
          line: 257,
          column: 25
        }
      },
      "45": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 262,
          column: 5
        }
      },
      "46": {
        start: {
          line: 261,
          column: 6
        },
        end: {
          line: 261,
          column: 20
        }
      },
      "47": {
        start: {
          line: 265,
          column: 24
        },
        end: {
          line: 265,
          column: 54
        }
      },
      "48": {
        start: {
          line: 266,
          column: 4
        },
        end: {
          line: 268,
          column: 5
        }
      },
      "49": {
        start: {
          line: 267,
          column: 6
        },
        end: {
          line: 267,
          column: 87
        }
      },
      "50": {
        start: {
          line: 267,
          column: 57
        },
        end: {
          line: 267,
          column: 67
        }
      },
      "51": {
        start: {
          line: 275,
          column: 4
        },
        end: {
          line: 277,
          column: 5
        }
      },
      "52": {
        start: {
          line: 276,
          column: 6
        },
        end: {
          line: 276,
          column: 18
        }
      },
      "53": {
        start: {
          line: 279,
          column: 24
        },
        end: {
          line: 279,
          column: 62
        }
      },
      "54": {
        start: {
          line: 280,
          column: 29
        },
        end: {
          line: 280,
          column: 54
        }
      },
      "55": {
        start: {
          line: 282,
          column: 4
        },
        end: {
          line: 285,
          column: 5
        }
      },
      "56": {
        start: {
          line: 283,
          column: 22
        },
        end: {
          line: 283,
          column: 79
        }
      },
      "57": {
        start: {
          line: 283,
          column: 50
        },
        end: {
          line: 283,
          column: 60
        }
      },
      "58": {
        start: {
          line: 284,
          column: 6
        },
        end: {
          line: 284,
          column: 47
        }
      },
      "59": {
        start: {
          line: 287,
          column: 4
        },
        end: {
          line: 307,
          column: 6
        }
      },
      "60": {
        start: {
          line: 293,
          column: 48
        },
        end: {
          line: 299,
          column: 7
        }
      },
      "61": {
        start: {
          line: 303,
          column: 90
        },
        end: {
          line: 303,
          column: 100
        }
      },
      "62": {
        start: {
          line: 314,
          column: 4
        },
        end: {
          line: 314,
          column: 32
        }
      },
      "63": {
        start: {
          line: 314,
          column: 23
        },
        end: {
          line: 314,
          column: 32
        }
      },
      "64": {
        start: {
          line: 316,
          column: 16
        },
        end: {
          line: 316,
          column: 19
        }
      },
      "65": {
        start: {
          line: 319,
          column: 4
        },
        end: {
          line: 320,
          column: 58
        }
      },
      "66": {
        start: {
          line: 319,
          column: 42
        },
        end: {
          line: 319,
          column: 54
        }
      },
      "67": {
        start: {
          line: 320,
          column: 9
        },
        end: {
          line: 320,
          column: 58
        }
      },
      "68": {
        start: {
          line: 320,
          column: 46
        },
        end: {
          line: 320,
          column: 58
        }
      },
      "69": {
        start: {
          line: 323,
          column: 4
        },
        end: {
          line: 324,
          column: 55
        }
      },
      "70": {
        start: {
          line: 323,
          column: 38
        },
        end: {
          line: 323,
          column: 50
        }
      },
      "71": {
        start: {
          line: 324,
          column: 9
        },
        end: {
          line: 324,
          column: 55
        }
      },
      "72": {
        start: {
          line: 324,
          column: 43
        },
        end: {
          line: 324,
          column: 55
        }
      },
      "73": {
        start: {
          line: 327,
          column: 26
        },
        end: {
          line: 327,
          column: 104
        }
      },
      "74": {
        start: {
          line: 327,
          column: 58
        },
        end: {
          line: 327,
          column: 67
        }
      },
      "75": {
        start: {
          line: 328,
          column: 4
        },
        end: {
          line: 328,
          column: 32
        }
      },
      "76": {
        start: {
          line: 331,
          column: 29
        },
        end: {
          line: 331,
          column: 78
        }
      },
      "77": {
        start: {
          line: 332,
          column: 4
        },
        end: {
          line: 332,
          column: 44
        }
      },
      "78": {
        start: {
          line: 332,
          column: 32
        },
        end: {
          line: 332,
          column: 44
        }
      },
      "79": {
        start: {
          line: 334,
          column: 4
        },
        end: {
          line: 334,
          column: 42
        }
      },
      "80": {
        start: {
          line: 342,
          column: 4
        },
        end: {
          line: 342,
          column: 41
        }
      },
      "81": {
        start: {
          line: 349,
          column: 4
        },
        end: {
          line: 349,
          column: 34
        }
      },
      "82": {
        start: {
          line: 349,
          column: 21
        },
        end: {
          line: 349,
          column: 34
        }
      },
      "83": {
        start: {
          line: 351,
          column: 14
        },
        end: {
          line: 351,
          column: 18
        }
      },
      "84": {
        start: {
          line: 352,
          column: 18
        },
        end: {
          line: 352,
          column: 41
        }
      },
      "85": {
        start: {
          line: 353,
          column: 14
        },
        end: {
          line: 353,
          column: 55
        }
      },
      "86": {
        start: {
          line: 355,
          column: 4
        },
        end: {
          line: 355,
          column: 76
        }
      },
      "87": {
        start: {
          line: 362,
          column: 4
        },
        end: {
          line: 362,
          column: 24
        }
      },
      "88": {
        start: {
          line: 363,
          column: 4
        },
        end: {
          line: 363,
          column: 27
        }
      },
      "89": {
        start: {
          line: 364,
          column: 4
        },
        end: {
          line: 364,
          column: 28
        }
      },
      "90": {
        start: {
          line: 369,
          column: 37
        },
        end: {
          line: 369,
          column: 64
        }
      },
      "91": {
        start: {
          line: 372,
          column: 27
        },
        end: {
          line: 396,
          column: 1
        }
      },
      "92": {
        start: {
          line: 377,
          column: 4
        },
        end: {
          line: 377,
          column: 66
        }
      },
      "93": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 378,
          column: 67
        }
      },
      "94": {
        start: {
          line: 385,
          column: 4
        },
        end: {
          line: 385,
          column: 59
        }
      },
      "95": {
        start: {
          line: 386,
          column: 4
        },
        end: {
          line: 386,
          column: 67
        }
      },
      "96": {
        start: {
          line: 393,
          column: 4
        },
        end: {
          line: 393,
          column: 59
        }
      },
      "97": {
        start: {
          line: 394,
          column: 4
        },
        end: {
          line: 394,
          column: 56
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 54,
            column: 2
          },
          end: {
            line: 54,
            column: 3
          }
        },
        loc: {
          start: {
            line: 54,
            column: 59
          },
          end: {
            line: 81,
            column: 3
          }
        },
        line: 54
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 62,
            column: 38
          },
          end: {
            line: 62,
            column: 39
          }
        },
        loc: {
          start: {
            line: 62,
            column: 54
          },
          end: {
            line: 62,
            column: 70
          }
        },
        line: 62
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 86,
            column: 2
          },
          end: {
            line: 86,
            column: 3
          }
        },
        loc: {
          start: {
            line: 86,
            column: 54
          },
          end: {
            line: 139,
            column: 3
          }
        },
        line: 86
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 134,
            column: 19
          },
          end: {
            line: 134,
            column: 20
          }
        },
        loc: {
          start: {
            line: 134,
            column: 28
          },
          end: {
            line: 136,
            column: 5
          }
        },
        line: 134
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 144,
            column: 2
          },
          end: {
            line: 144,
            column: 3
          }
        },
        loc: {
          start: {
            line: 144,
            column: 65
          },
          end: {
            line: 183,
            column: 3
          }
        },
        line: 144
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 188,
            column: 2
          },
          end: {
            line: 188,
            column: 3
          }
        },
        loc: {
          start: {
            line: 188,
            column: 62
          },
          end: {
            line: 246,
            column: 3
          }
        },
        line: 188
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 196,
            column: 51
          },
          end: {
            line: 196,
            column: 52
          }
        },
        loc: {
          start: {
            line: 196,
            column: 60
          },
          end: {
            line: 196,
            column: 79
          }
        },
        line: 196
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 201,
            column: 60
          },
          end: {
            line: 201,
            column: 61
          }
        },
        loc: {
          start: {
            line: 201,
            column: 65
          },
          end: {
            line: 201,
            column: 71
          }
        },
        line: 201
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 208,
            column: 54
          },
          end: {
            line: 208,
            column: 55
          }
        },
        loc: {
          start: {
            line: 208,
            column: 63
          },
          end: {
            line: 208,
            column: 76
          }
        },
        line: 208
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 220,
            column: 63
          },
          end: {
            line: 220,
            column: 64
          }
        },
        loc: {
          start: {
            line: 220,
            column: 70
          },
          end: {
            line: 220,
            column: 87
          }
        },
        line: 220
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 242,
            column: 28
          },
          end: {
            line: 242,
            column: 29
          }
        },
        loc: {
          start: {
            line: 242,
            column: 38
          },
          end: {
            line: 245,
            column: 5
          }
        },
        line: 242
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 251,
            column: 2
          },
          end: {
            line: 251,
            column: 3
          }
        },
        loc: {
          start: {
            line: 251,
            column: 60
          },
          end: {
            line: 269,
            column: 3
          }
        },
        line: 251
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 267,
            column: 42
          },
          end: {
            line: 267,
            column: 43
          }
        },
        loc: {
          start: {
            line: 267,
            column: 57
          },
          end: {
            line: 267,
            column: 67
          }
        },
        line: 267
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 274,
            column: 2
          },
          end: {
            line: 274,
            column: 3
          }
        },
        loc: {
          start: {
            line: 274,
            column: 30
          },
          end: {
            line: 308,
            column: 3
          }
        },
        line: 274
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 283,
            column: 35
          },
          end: {
            line: 283,
            column: 36
          }
        },
        loc: {
          start: {
            line: 283,
            column: 50
          },
          end: {
            line: 283,
            column: 60
          }
        },
        line: 283
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 293,
            column: 38
          },
          end: {
            line: 293,
            column: 39
          }
        },
        loc: {
          start: {
            line: 293,
            column: 48
          },
          end: {
            line: 299,
            column: 7
          }
        },
        line: 293
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 303,
            column: 75
          },
          end: {
            line: 303,
            column: 76
          }
        },
        loc: {
          start: {
            line: 303,
            column: 90
          },
          end: {
            line: 303,
            column: 100
          }
        },
        line: 303
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 313,
            column: 2
          },
          end: {
            line: 313,
            column: 3
          }
        },
        loc: {
          start: {
            line: 313,
            column: 46
          },
          end: {
            line: 335,
            column: 3
          }
        },
        line: 313
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 327,
            column: 53
          },
          end: {
            line: 327,
            column: 54
          }
        },
        loc: {
          start: {
            line: 327,
            column: 58
          },
          end: {
            line: 327,
            column: 67
          }
        },
        line: 327
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 340,
            column: 2
          },
          end: {
            line: 340,
            column: 3
          }
        },
        loc: {
          start: {
            line: 340,
            column: 56
          },
          end: {
            line: 343,
            column: 3
          }
        },
        line: 340
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 348,
            column: 2
          },
          end: {
            line: 348,
            column: 3
          }
        },
        loc: {
          start: {
            line: 348,
            column: 45
          },
          end: {
            line: 356,
            column: 3
          }
        },
        line: 348
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 361,
            column: 2
          },
          end: {
            line: 361,
            column: 3
          }
        },
        loc: {
          start: {
            line: 361,
            column: 21
          },
          end: {
            line: 365,
            column: 3
          }
        },
        line: 361
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 376,
            column: 17
          },
          end: {
            line: 376,
            column: 18
          }
        },
        loc: {
          start: {
            line: 376,
            column: 62
          },
          end: {
            line: 379,
            column: 3
          }
        },
        line: 376
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 384,
            column: 34
          },
          end: {
            line: 384,
            column: 35
          }
        },
        loc: {
          start: {
            line: 384,
            column: 46
          },
          end: {
            line: 387,
            column: 3
          }
        },
        line: 384
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 392,
            column: 27
          },
          end: {
            line: 392,
            column: 28
          }
        },
        loc: {
          start: {
            line: 392,
            column: 39
          },
          end: {
            line: 395,
            column: 3
          }
        },
        line: 392
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 189,
            column: 4
          },
          end: {
            line: 191,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 189,
            column: 4
          },
          end: {
            line: 191,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 189
      },
      "1": {
        loc: {
          start: {
            line: 197,
            column: 4
          },
          end: {
            line: 205,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 197,
            column: 4
          },
          end: {
            line: 205,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 197
      },
      "2": {
        loc: {
          start: {
            line: 209,
            column: 4
          },
          end: {
            line: 217,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 209,
            column: 4
          },
          end: {
            line: 217,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 209
      },
      "3": {
        loc: {
          start: {
            line: 221,
            column: 4
          },
          end: {
            line: 229,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 221,
            column: 4
          },
          end: {
            line: 229,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 221
      },
      "4": {
        loc: {
          start: {
            line: 232,
            column: 4
          },
          end: {
            line: 240,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 232,
            column: 4
          },
          end: {
            line: 240,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 232
      },
      "5": {
        loc: {
          start: {
            line: 252,
            column: 4
          },
          end: {
            line: 254,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 252,
            column: 4
          },
          end: {
            line: 254,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 252
      },
      "6": {
        loc: {
          start: {
            line: 260,
            column: 4
          },
          end: {
            line: 262,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 260,
            column: 4
          },
          end: {
            line: 262,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 260
      },
      "7": {
        loc: {
          start: {
            line: 266,
            column: 4
          },
          end: {
            line: 268,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 266,
            column: 4
          },
          end: {
            line: 268,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 266
      },
      "8": {
        loc: {
          start: {
            line: 275,
            column: 4
          },
          end: {
            line: 277,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 275,
            column: 4
          },
          end: {
            line: 277,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 275
      },
      "9": {
        loc: {
          start: {
            line: 296,
            column: 18
          },
          end: {
            line: 296,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 296,
            column: 18
          },
          end: {
            line: 296,
            column: 32
          }
        }, {
          start: {
            line: 296,
            column: 36
          },
          end: {
            line: 296,
            column: 37
          }
        }],
        line: 296
      },
      "10": {
        loc: {
          start: {
            line: 303,
            column: 30
          },
          end: {
            line: 303,
            column: 133
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 303,
            column: 30
          },
          end: {
            line: 303,
            column: 128
          }
        }, {
          start: {
            line: 303,
            column: 132
          },
          end: {
            line: 303,
            column: 133
          }
        }],
        line: 303
      },
      "11": {
        loc: {
          start: {
            line: 314,
            column: 4
          },
          end: {
            line: 314,
            column: 32
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 314,
            column: 4
          },
          end: {
            line: 314,
            column: 32
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 314
      },
      "12": {
        loc: {
          start: {
            line: 319,
            column: 4
          },
          end: {
            line: 320,
            column: 58
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 319,
            column: 4
          },
          end: {
            line: 320,
            column: 58
          }
        }, {
          start: {
            line: 320,
            column: 9
          },
          end: {
            line: 320,
            column: 58
          }
        }],
        line: 319
      },
      "13": {
        loc: {
          start: {
            line: 320,
            column: 9
          },
          end: {
            line: 320,
            column: 58
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 320,
            column: 9
          },
          end: {
            line: 320,
            column: 58
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 320
      },
      "14": {
        loc: {
          start: {
            line: 323,
            column: 4
          },
          end: {
            line: 324,
            column: 55
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 323,
            column: 4
          },
          end: {
            line: 324,
            column: 55
          }
        }, {
          start: {
            line: 324,
            column: 9
          },
          end: {
            line: 324,
            column: 55
          }
        }],
        line: 323
      },
      "15": {
        loc: {
          start: {
            line: 324,
            column: 9
          },
          end: {
            line: 324,
            column: 55
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 324,
            column: 9
          },
          end: {
            line: 324,
            column: 55
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 324
      },
      "16": {
        loc: {
          start: {
            line: 332,
            column: 4
          },
          end: {
            line: 332,
            column: 44
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 332,
            column: 4
          },
          end: {
            line: 332,
            column: 44
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 332
      },
      "17": {
        loc: {
          start: {
            line: 349,
            column: 4
          },
          end: {
            line: 349,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 349,
            column: 4
          },
          end: {
            line: 349,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 349
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b0efc60740ffbcc47864717f57d8497f56c0a151"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_11ic3jy76j = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_11ic3jy76j();
import { performanceMonitor } from "./performance";
var BundleAnalysisService = function () {
  function BundleAnalysisService() {
    _classCallCheck(this, BundleAnalysisService);
    this.metrics = (cov_11ic3jy76j().s[0]++, null);
    this.loadTimes = (cov_11ic3jy76j().s[1]++, new Map());
    this.chunkCache = (cov_11ic3jy76j().s[2]++, new Map());
  }
  return _createClass(BundleAnalysisService, [{
    key: "analyzeBundlePerformance",
    value: (function () {
      var _analyzeBundlePerformance = _asyncToGenerator(function* () {
        cov_11ic3jy76j().f[0]++;
        var startTime = (cov_11ic3jy76j().s[3]++, Date.now());
        cov_11ic3jy76j().s[4]++;
        try {
          var chunks = (cov_11ic3jy76j().s[5]++, yield this.analyzeChunks());
          var dependencies = (cov_11ic3jy76j().s[6]++, yield this.analyzeDependencies());
          var totalSize = (cov_11ic3jy76j().s[7]++, chunks.reduce(function (sum, chunk) {
            cov_11ic3jy76j().f[1]++;
            cov_11ic3jy76j().s[8]++;
            return sum + chunk.size;
          }, 0));
          var gzippedSize = (cov_11ic3jy76j().s[9]++, Math.round(totalSize * 0.3));
          var metrics = (cov_11ic3jy76j().s[10]++, {
            totalSize: totalSize,
            gzippedSize: gzippedSize,
            chunks: chunks,
            dependencies: dependencies,
            loadTime: Date.now() - startTime,
            parseTime: this.estimateParseTime(totalSize)
          });
          cov_11ic3jy76j().s[11]++;
          this.metrics = metrics;
          cov_11ic3jy76j().s[12]++;
          return metrics;
        } catch (error) {
          cov_11ic3jy76j().s[13]++;
          console.error('Bundle analysis failed:', error);
          cov_11ic3jy76j().s[14]++;
          throw error;
        }
      });
      function analyzeBundlePerformance() {
        return _analyzeBundlePerformance.apply(this, arguments);
      }
      return analyzeBundlePerformance;
    }())
  }, {
    key: "analyzeChunks",
    value: (function () {
      var _analyzeChunks = _asyncToGenerator(function* () {
        var _this = this;
        cov_11ic3jy76j().f[2]++;
        var chunks = (cov_11ic3jy76j().s[15]++, [{
          name: 'main',
          size: 150000,
          modules: ['App', 'AuthContext', 'Navigation'],
          loadTime: 200,
          cached: false
        }, {
          name: 'core-screens',
          size: 120000,
          modules: ['Dashboard', 'Training', 'Progress', 'Profile'],
          loadTime: 150,
          cached: true
        }, {
          name: 'feature-screens',
          size: 200000,
          modules: ['VideoAnalysis', 'AICoaching', 'MatchAnalysis'],
          loadTime: 300,
          cached: false
        }, {
          name: 'social-features',
          size: 100000,
          modules: ['Social', 'Leaderboard', 'Challenges'],
          loadTime: 180,
          cached: false
        }, {
          name: 'premium-features',
          size: 150000,
          modules: ['Premium', 'AdvancedAnalytics', 'Subscription'],
          loadTime: 250,
          cached: false
        }, {
          name: 'vendor',
          size: 300000,
          modules: ['react', 'react-native', 'expo', 'supabase'],
          loadTime: 100,
          cached: true
        }]);
        cov_11ic3jy76j().s[16]++;
        chunks.forEach(function (chunk) {
          cov_11ic3jy76j().f[3]++;
          cov_11ic3jy76j().s[17]++;
          _this.chunkCache.set(chunk.name, chunk);
        });
        cov_11ic3jy76j().s[18]++;
        return chunks;
      });
      function analyzeChunks() {
        return _analyzeChunks.apply(this, arguments);
      }
      return analyzeChunks;
    }())
  }, {
    key: "analyzeDependencies",
    value: (function () {
      var _analyzeDependencies = _asyncToGenerator(function* () {
        cov_11ic3jy76j().f[4]++;
        cov_11ic3jy76j().s[19]++;
        return [{
          name: 'react',
          version: '19.0.0',
          size: 45000,
          type: 'production',
          treeshakeable: false
        }, {
          name: 'react-native',
          version: '0.79.1',
          size: 120000,
          type: 'production',
          treeshakeable: false
        }, {
          name: 'expo',
          version: '53.0.0',
          size: 80000,
          type: 'production',
          treeshakeable: true
        }, {
          name: '@supabase/supabase-js',
          version: '2.50.0',
          size: 35000,
          type: 'production',
          treeshakeable: true
        }, {
          name: 'openai',
          version: '4.104.0',
          size: 25000,
          type: 'production',
          treeshakeable: true
        }];
      });
      function analyzeDependencies() {
        return _analyzeDependencies.apply(this, arguments);
      }
      return analyzeDependencies;
    }())
  }, {
    key: "generateOptimizationSuggestions",
    value: function generateOptimizationSuggestions() {
      cov_11ic3jy76j().f[5]++;
      cov_11ic3jy76j().s[20]++;
      if (!this.metrics) {
        cov_11ic3jy76j().b[0][0]++;
        cov_11ic3jy76j().s[21]++;
        throw new Error('Bundle analysis must be run first');
      } else {
        cov_11ic3jy76j().b[0][1]++;
      }
      var suggestions = (cov_11ic3jy76j().s[22]++, []);
      var largeChunks = (cov_11ic3jy76j().s[23]++, this.metrics.chunks.filter(function (chunk) {
        cov_11ic3jy76j().f[6]++;
        cov_11ic3jy76j().s[24]++;
        return chunk.size > 150000;
      }));
      cov_11ic3jy76j().s[25]++;
      if (largeChunks.length > 0) {
        cov_11ic3jy76j().b[1][0]++;
        cov_11ic3jy76j().s[26]++;
        suggestions.push({
          type: 'bundle-split',
          priority: 'high',
          description: `Split large chunks: ${largeChunks.map(function (c) {
            cov_11ic3jy76j().f[7]++;
            cov_11ic3jy76j().s[27]++;
            return c.name;
          }).join(', ')}`,
          estimatedSavings: '20-30% initial load time',
          implementation: 'Implement more granular lazy loading and code splitting'
        });
      } else {
        cov_11ic3jy76j().b[1][1]++;
      }
      var uncachedChunks = (cov_11ic3jy76j().s[28]++, this.metrics.chunks.filter(function (chunk) {
        cov_11ic3jy76j().f[8]++;
        cov_11ic3jy76j().s[29]++;
        return !chunk.cached;
      }));
      cov_11ic3jy76j().s[30]++;
      if (uncachedChunks.length > 2) {
        cov_11ic3jy76j().b[2][0]++;
        cov_11ic3jy76j().s[31]++;
        suggestions.push({
          type: 'cache',
          priority: 'medium',
          description: 'Improve caching for frequently used chunks',
          estimatedSavings: '40-50% repeat load time',
          implementation: 'Implement service worker caching and chunk versioning'
        });
      } else {
        cov_11ic3jy76j().b[2][1]++;
      }
      var treeshakeableDeps = (cov_11ic3jy76j().s[32]++, this.metrics.dependencies.filter(function (dep) {
        cov_11ic3jy76j().f[9]++;
        cov_11ic3jy76j().s[33]++;
        return dep.treeshakeable;
      }));
      cov_11ic3jy76j().s[34]++;
      if (treeshakeableDeps.length > 0) {
        cov_11ic3jy76j().b[3][0]++;
        cov_11ic3jy76j().s[35]++;
        suggestions.push({
          type: 'tree-shake',
          priority: 'medium',
          description: 'Optimize imports for treeshakeable dependencies',
          estimatedSavings: '10-15% bundle size',
          implementation: 'Use named imports and configure webpack/metro for better tree shaking'
        });
      } else {
        cov_11ic3jy76j().b[3][1]++;
      }
      cov_11ic3jy76j().s[36]++;
      if (this.metrics.totalSize > 800000) {
        cov_11ic3jy76j().b[4][0]++;
        cov_11ic3jy76j().s[37]++;
        suggestions.push({
          type: 'compress',
          priority: 'high',
          description: 'Bundle size is large, implement compression',
          estimatedSavings: '60-70% transfer size',
          implementation: 'Enable gzip/brotli compression and optimize assets'
        });
      } else {
        cov_11ic3jy76j().b[4][1]++;
      }
      cov_11ic3jy76j().s[38]++;
      return suggestions.sort(function (a, b) {
        cov_11ic3jy76j().f[10]++;
        var priorityOrder = (cov_11ic3jy76j().s[39]++, {
          high: 0,
          medium: 1,
          low: 2
        });
        cov_11ic3jy76j().s[40]++;
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      });
    }
  }, {
    key: "trackChunkLoad",
    value: function trackChunkLoad(chunkName, loadTime) {
      cov_11ic3jy76j().f[11]++;
      cov_11ic3jy76j().s[41]++;
      if (!this.loadTimes.has(chunkName)) {
        cov_11ic3jy76j().b[5][0]++;
        cov_11ic3jy76j().s[42]++;
        this.loadTimes.set(chunkName, []);
      } else {
        cov_11ic3jy76j().b[5][1]++;
      }
      var times = (cov_11ic3jy76j().s[43]++, this.loadTimes.get(chunkName));
      cov_11ic3jy76j().s[44]++;
      times.push(loadTime);
      cov_11ic3jy76j().s[45]++;
      if (times.length > 10) {
        cov_11ic3jy76j().b[6][0]++;
        cov_11ic3jy76j().s[46]++;
        times.shift();
      } else {
        cov_11ic3jy76j().b[6][1]++;
      }
      var cachedChunk = (cov_11ic3jy76j().s[47]++, this.chunkCache.get(chunkName));
      cov_11ic3jy76j().s[48]++;
      if (cachedChunk) {
        cov_11ic3jy76j().b[7][0]++;
        cov_11ic3jy76j().s[49]++;
        cachedChunk.loadTime = times.reduce(function (sum, time) {
          cov_11ic3jy76j().f[12]++;
          cov_11ic3jy76j().s[50]++;
          return sum + time;
        }, 0) / times.length;
      } else {
        cov_11ic3jy76j().b[7][1]++;
      }
    }
  }, {
    key: "getPerformanceReport",
    value: function getPerformanceReport() {
      var _this2 = this;
      cov_11ic3jy76j().f[13]++;
      cov_11ic3jy76j().s[51]++;
      if (!this.metrics) {
        cov_11ic3jy76j().b[8][0]++;
        cov_11ic3jy76j().s[52]++;
        return null;
      } else {
        cov_11ic3jy76j().b[8][1]++;
      }
      var suggestions = (cov_11ic3jy76j().s[53]++, this.generateOptimizationSuggestions());
      var averageLoadTimes = (cov_11ic3jy76j().s[54]++, new Map());
      cov_11ic3jy76j().s[55]++;
      for (var _ref of this.loadTimes.entries()) {
        var _ref2 = _slicedToArray(_ref, 2);
        var chunkName = _ref2[0];
        var times = _ref2[1];
        var average = (cov_11ic3jy76j().s[56]++, times.reduce(function (sum, time) {
          cov_11ic3jy76j().f[14]++;
          cov_11ic3jy76j().s[57]++;
          return sum + time;
        }, 0) / times.length);
        cov_11ic3jy76j().s[58]++;
        averageLoadTimes.set(chunkName, average);
      }
      cov_11ic3jy76j().s[59]++;
      return {
        bundleSize: {
          total: this.formatBytes(this.metrics.totalSize),
          gzipped: this.formatBytes(this.metrics.gzippedSize),
          compressionRatio: Math.round(this.metrics.gzippedSize / this.metrics.totalSize * 100)
        },
        chunks: this.metrics.chunks.map(function (chunk) {
          cov_11ic3jy76j().f[15]++;
          cov_11ic3jy76j().s[60]++;
          return {
            name: chunk.name,
            size: _this2.formatBytes(chunk.size),
            loadTime: (cov_11ic3jy76j().b[9][0]++, chunk.loadTime) || (cov_11ic3jy76j().b[9][1]++, 0),
            cached: chunk.cached,
            modules: chunk.modules.length
          };
        }),
        performance: {
          totalLoadTime: this.metrics.loadTime,
          parseTime: this.metrics.parseTime,
          averageChunkLoadTime: (cov_11ic3jy76j().b[10][0]++, Array.from(averageLoadTimes.values()).reduce(function (sum, time) {
            cov_11ic3jy76j().f[16]++;
            cov_11ic3jy76j().s[61]++;
            return sum + time;
          }, 0) / averageLoadTimes.size) || (cov_11ic3jy76j().b[10][1]++, 0)
        },
        optimizations: suggestions,
        score: this.calculatePerformanceScore()
      };
    }
  }, {
    key: "calculatePerformanceScore",
    value: function calculatePerformanceScore() {
      cov_11ic3jy76j().f[17]++;
      cov_11ic3jy76j().s[62]++;
      if (!this.metrics) {
        cov_11ic3jy76j().b[11][0]++;
        cov_11ic3jy76j().s[63]++;
        return 0;
      } else {
        cov_11ic3jy76j().b[11][1]++;
      }
      var score = (cov_11ic3jy76j().s[64]++, 100);
      cov_11ic3jy76j().s[65]++;
      if (this.metrics.totalSize > 1000000) {
        cov_11ic3jy76j().b[12][0]++;
        cov_11ic3jy76j().s[66]++;
        score -= 30;
      } else {
        cov_11ic3jy76j().b[12][1]++;
        cov_11ic3jy76j().s[67]++;
        if (this.metrics.totalSize > 500000) {
          cov_11ic3jy76j().b[13][0]++;
          cov_11ic3jy76j().s[68]++;
          score -= 15;
        } else {
          cov_11ic3jy76j().b[13][1]++;
        }
      }
      cov_11ic3jy76j().s[69]++;
      if (this.metrics.loadTime > 3000) {
        cov_11ic3jy76j().b[14][0]++;
        cov_11ic3jy76j().s[70]++;
        score -= 25;
      } else {
        cov_11ic3jy76j().b[14][1]++;
        cov_11ic3jy76j().s[71]++;
        if (this.metrics.loadTime > 1000) {
          cov_11ic3jy76j().b[15][0]++;
          cov_11ic3jy76j().s[72]++;
          score -= 10;
        } else {
          cov_11ic3jy76j().b[15][1]++;
        }
      }
      var uncachedRatio = (cov_11ic3jy76j().s[73]++, this.metrics.chunks.filter(function (c) {
        cov_11ic3jy76j().f[18]++;
        cov_11ic3jy76j().s[74]++;
        return !c.cached;
      }).length / this.metrics.chunks.length);
      cov_11ic3jy76j().s[75]++;
      score -= uncachedRatio * 20;
      var compressionRatio = (cov_11ic3jy76j().s[76]++, this.metrics.gzippedSize / this.metrics.totalSize);
      cov_11ic3jy76j().s[77]++;
      if (compressionRatio < 0.3) {
        cov_11ic3jy76j().b[16][0]++;
        cov_11ic3jy76j().s[78]++;
        score += 10;
      } else {
        cov_11ic3jy76j().b[16][1]++;
      }
      cov_11ic3jy76j().s[79]++;
      return Math.max(0, Math.round(score));
    }
  }, {
    key: "estimateParseTime",
    value: function estimateParseTime(bundleSize) {
      cov_11ic3jy76j().f[19]++;
      cov_11ic3jy76j().s[80]++;
      return Math.round(bundleSize / 1000);
    }
  }, {
    key: "formatBytes",
    value: function formatBytes(bytes) {
      cov_11ic3jy76j().f[20]++;
      cov_11ic3jy76j().s[81]++;
      if (bytes === 0) {
        cov_11ic3jy76j().b[17][0]++;
        cov_11ic3jy76j().s[82]++;
        return '0 B';
      } else {
        cov_11ic3jy76j().b[17][1]++;
      }
      var k = (cov_11ic3jy76j().s[83]++, 1024);
      var sizes = (cov_11ic3jy76j().s[84]++, ['B', 'KB', 'MB', 'GB']);
      var i = (cov_11ic3jy76j().s[85]++, Math.floor(Math.log(bytes) / Math.log(k)));
      cov_11ic3jy76j().s[86]++;
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
  }, {
    key: "clearCache",
    value: function clearCache() {
      cov_11ic3jy76j().f[21]++;
      cov_11ic3jy76j().s[87]++;
      this.metrics = null;
      cov_11ic3jy76j().s[88]++;
      this.loadTimes.clear();
      cov_11ic3jy76j().s[89]++;
      this.chunkCache.clear();
    }
  }]);
}();
export var bundleAnalysisService = (cov_11ic3jy76j().s[90]++, new BundleAnalysisService());
export var bundleUtils = (cov_11ic3jy76j().s[91]++, {
  trackLazyLoad: function trackLazyLoad(componentName, loadTime) {
    cov_11ic3jy76j().f[22]++;
    cov_11ic3jy76j().s[92]++;
    bundleAnalysisService.trackChunkLoad(componentName, loadTime);
    cov_11ic3jy76j().s[93]++;
    performanceMonitor.trackComponentLoad(componentName, loadTime);
  },
  getOptimizationRecommendations: function () {
    var _getOptimizationRecommendations = _asyncToGenerator(function* () {
      cov_11ic3jy76j().f[23]++;
      cov_11ic3jy76j().s[94]++;
      yield bundleAnalysisService.analyzeBundlePerformance();
      cov_11ic3jy76j().s[95]++;
      return bundleAnalysisService.generateOptimizationSuggestions();
    });
    function getOptimizationRecommendations() {
      return _getOptimizationRecommendations.apply(this, arguments);
    }
    return getOptimizationRecommendations;
  }(),
  getPerformanceDashboard: function () {
    var _getPerformanceDashboard = _asyncToGenerator(function* () {
      cov_11ic3jy76j().f[24]++;
      cov_11ic3jy76j().s[96]++;
      yield bundleAnalysisService.analyzeBundlePerformance();
      cov_11ic3jy76j().s[97]++;
      return bundleAnalysisService.getPerformanceReport();
    });
    function getPerformanceDashboard() {
      return _getPerformanceDashboard.apply(this, arguments);
    }
    return getPerformanceDashboard;
  }()
});
export default bundleAnalysisService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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