{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_FlatList", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _FlatList = _interopRequireDefault(require(\"../../vendor/react-native/FlatList\"));\nvar _default = exports.default = _FlatList.default;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,SAAS,GAAGL,sBAAsB,CAACC,OAAO,qCAAqC,CAAC,CAAC;AACrF,IAAIK,QAAQ,GAAGH,OAAO,CAACD,OAAO,GAAGG,SAAS,CAACH,OAAO;AAClDK,MAAM,CAACJ,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}