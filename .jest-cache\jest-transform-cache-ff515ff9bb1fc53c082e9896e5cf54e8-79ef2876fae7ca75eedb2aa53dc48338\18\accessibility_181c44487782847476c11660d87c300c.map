{"version": 3, "names": ["AccessibilityInfo", "Platform", "AccessibilityManager", "_classCallCheck", "isScreenReaderEnabled", "cov_8iywu242h", "s", "isReduceMotionEnabled", "isReduceTransparencyEnabled", "listeners", "_createClass", "key", "value", "_initialize", "_asyncToGenerator", "f", "OS", "b", "setupListeners", "error", "console", "initialize", "apply", "arguments", "_this", "addEventListener", "enabled", "notifyListeners", "getScreenReaderEnabled", "getReduceMotionEnabled", "getReduceTransparencyEnabled", "addListener", "listener", "push", "removeListener", "index", "indexOf", "splice", "for<PERSON>ach", "announce", "message", "priority", "length", "undefined", "announceForAccessibility", "setFocus", "reactTag", "setAccessibilityFocus", "getInstance", "instance", "accessibilityHelpers", "button", "label", "hint", "disabled", "accessible", "accessibilityRole", "accessibilityLabel", "accessibilityHint", "accessibilityState", "link", "image", "description", "decorative", "importantForAccessibility", "header", "text", "level", "accessibilityValue", "textInput", "required", "checkbox", "checked", "switch", "progressBar", "min", "max", "percentage", "Math", "round", "now", "tab", "selected", "total", "alert", "type", "accessibilityLiveRegion", "listItem", "card", "title", "tennisAccessibilityHelpers", "score", "playerScore", "opponentScore", "set", "scoreNames", "playerScore<PERSON><PERSON>", "toString", "opponentScoreName", "setInfo", "drillInstruction", "step", "instruction", "analysisResult", "feedback", "sessionStatus", "status", "duration", "matchStats", "stats", "aces", "winners", "errors", "focusManager", "focusNext", "log", "focusPrevious", "trapFocus", "containerRef", "accessibilityManager"], "sources": ["accessibility.ts"], "sourcesContent": ["import { AccessibilityInfo, Platform } from 'react-native';\n\n/**\n * Accessibility utilities and helpers\n */\n\nexport interface AccessibilityProps {\n  accessible?: boolean;\n  accessibilityLabel?: string;\n  accessibilityHint?: string;\n  accessibilityRole?: \n    | 'none'\n    | 'button'\n    | 'link'\n    | 'search'\n    | 'image'\n    | 'keyboardkey'\n    | 'text'\n    | 'adjustable'\n    | 'imagebutton'\n    | 'header'\n    | 'summary'\n    | 'alert'\n    | 'checkbox'\n    | 'combobox'\n    | 'menu'\n    | 'menubar'\n    | 'menuitem'\n    | 'progressbar'\n    | 'radio'\n    | 'radiogroup'\n    | 'scrollbar'\n    | 'spinbutton'\n    | 'switch'\n    | 'tab'\n    | 'tablist'\n    | 'timer'\n    | 'toolbar';\n  accessibilityState?: {\n    disabled?: boolean;\n    selected?: boolean;\n    checked?: boolean | 'mixed';\n    busy?: boolean;\n    expanded?: boolean;\n  };\n  accessibilityValue?: {\n    min?: number;\n    max?: number;\n    now?: number;\n    text?: string;\n  };\n  accessibilityActions?: Array<{\n    name: string;\n    label?: string;\n  }>;\n  onAccessibilityAction?: (event: { nativeEvent: { actionName: string } }) => void;\n  importantForAccessibility?: 'auto' | 'yes' | 'no' | 'no-hide-descendants';\n  accessibilityLiveRegion?: 'none' | 'polite' | 'assertive';\n  accessibilityElementsHidden?: boolean;\n  accessibilityViewIsModal?: boolean;\n}\n\n/**\n * Screen reader detection and utilities\n */\nexport class AccessibilityManager {\n  private static instance: AccessibilityManager;\n  private isScreenReaderEnabled = false;\n  private isReduceMotionEnabled = false;\n  private isReduceTransparencyEnabled = false;\n  private listeners: (() => void)[] = [];\n\n  static getInstance(): AccessibilityManager {\n    if (!AccessibilityManager.instance) {\n      AccessibilityManager.instance = new AccessibilityManager();\n    }\n    return AccessibilityManager.instance;\n  }\n\n  async initialize(): Promise<void> {\n    try {\n      // Check screen reader status\n      this.isScreenReaderEnabled = await AccessibilityInfo.isScreenReaderEnabled();\n      \n      // Check reduce motion preference\n      if (Platform.OS === 'ios') {\n        this.isReduceMotionEnabled = await AccessibilityInfo.isReduceMotionEnabled();\n        this.isReduceTransparencyEnabled = await AccessibilityInfo.isReduceTransparencyEnabled();\n      }\n\n      // Set up listeners\n      this.setupListeners();\n    } catch (error) {\n      console.error('Failed to initialize accessibility manager:', error);\n    }\n  }\n\n  private setupListeners(): void {\n    AccessibilityInfo.addEventListener('screenReaderChanged', (enabled) => {\n      this.isScreenReaderEnabled = enabled;\n      this.notifyListeners();\n    });\n\n    if (Platform.OS === 'ios') {\n      AccessibilityInfo.addEventListener('reduceMotionChanged', (enabled) => {\n        this.isReduceMotionEnabled = enabled;\n        this.notifyListeners();\n      });\n\n      AccessibilityInfo.addEventListener('reduceTransparencyChanged', (enabled) => {\n        this.isReduceTransparencyEnabled = enabled;\n        this.notifyListeners();\n      });\n    }\n  }\n\n  getScreenReaderEnabled(): boolean {\n    return this.isScreenReaderEnabled;\n  }\n\n  getReduceMotionEnabled(): boolean {\n    return this.isReduceMotionEnabled;\n  }\n\n  getReduceTransparencyEnabled(): boolean {\n    return this.isReduceTransparencyEnabled;\n  }\n\n  addListener(listener: () => void): void {\n    this.listeners.push(listener);\n  }\n\n  removeListener(listener: () => void): void {\n    const index = this.listeners.indexOf(listener);\n    if (index > -1) {\n      this.listeners.splice(index, 1);\n    }\n  }\n\n  private notifyListeners(): void {\n    this.listeners.forEach(listener => {\n      try {\n        listener();\n      } catch (error) {\n        console.error('Error in accessibility listener:', error);\n      }\n    });\n  }\n\n  /**\n   * Announce message to screen reader\n   */\n  announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {\n    if (this.isScreenReaderEnabled) {\n      AccessibilityInfo.announceForAccessibility(message);\n    }\n  }\n\n  /**\n   * Set focus to element\n   */\n  setFocus(reactTag: number): void {\n    AccessibilityInfo.setAccessibilityFocus(reactTag);\n  }\n}\n\n/**\n * Generate accessibility props for common UI patterns\n */\nexport const accessibilityHelpers = {\n  /**\n   * Button accessibility props\n   */\n  button(label: string, hint?: string, disabled = false): AccessibilityProps {\n    return {\n      accessible: true,\n      accessibilityRole: 'button',\n      accessibilityLabel: label,\n      accessibilityHint: hint,\n      accessibilityState: { disabled },\n    };\n  },\n\n  /**\n   * Link accessibility props\n   */\n  link(label: string, hint?: string): AccessibilityProps {\n    return {\n      accessible: true,\n      accessibilityRole: 'link',\n      accessibilityLabel: label,\n      accessibilityHint: hint,\n    };\n  },\n\n  /**\n   * Image accessibility props\n   */\n  image(description: string, decorative = false): AccessibilityProps {\n    if (decorative) {\n      return {\n        accessible: false,\n        importantForAccessibility: 'no',\n      };\n    }\n\n    return {\n      accessible: true,\n      accessibilityRole: 'image',\n      accessibilityLabel: description,\n    };\n  },\n\n  /**\n   * Header accessibility props\n   */\n  header(text: string, level = 1): AccessibilityProps {\n    return {\n      accessible: true,\n      accessibilityRole: 'header',\n      accessibilityLabel: text,\n      accessibilityValue: { text: `Heading level ${level}` },\n    };\n  },\n\n  /**\n   * Text input accessibility props\n   */\n  textInput(label: string, hint?: string, required = false): AccessibilityProps {\n    return {\n      accessible: true,\n      accessibilityLabel: label + (required ? ' (required)' : ''),\n      accessibilityHint: hint,\n    };\n  },\n\n  /**\n   * Checkbox accessibility props\n   */\n  checkbox(label: string, checked: boolean, hint?: string): AccessibilityProps {\n    return {\n      accessible: true,\n      accessibilityRole: 'checkbox',\n      accessibilityLabel: label,\n      accessibilityHint: hint,\n      accessibilityState: { checked },\n    };\n  },\n\n  /**\n   * Switch accessibility props\n   */\n  switch(label: string, value: boolean, hint?: string): AccessibilityProps {\n    return {\n      accessible: true,\n      accessibilityRole: 'switch',\n      accessibilityLabel: label,\n      accessibilityHint: hint,\n      accessibilityState: { checked: value },\n    };\n  },\n\n  /**\n   * Progress bar accessibility props\n   */\n  progressBar(label: string, value: number, min = 0, max = 100): AccessibilityProps {\n    const percentage = Math.round(((value - min) / (max - min)) * 100);\n    \n    return {\n      accessible: true,\n      accessibilityRole: 'progressbar',\n      accessibilityLabel: label,\n      accessibilityValue: {\n        min,\n        max,\n        now: value,\n        text: `${percentage} percent`,\n      },\n    };\n  },\n\n  /**\n   * Tab accessibility props\n   */\n  tab(label: string, selected: boolean, index: number, total: number): AccessibilityProps {\n    return {\n      accessible: true,\n      accessibilityRole: 'tab',\n      accessibilityLabel: label,\n      accessibilityState: { selected },\n      accessibilityValue: { text: `Tab ${index + 1} of ${total}` },\n    };\n  },\n\n  /**\n   * Alert accessibility props\n   */\n  alert(message: string, type: 'info' | 'warning' | 'error' | 'success' = 'info'): AccessibilityProps {\n    return {\n      accessible: true,\n      accessibilityRole: 'alert',\n      accessibilityLabel: `${type} alert: ${message}`,\n      accessibilityLiveRegion: type === 'error' ? 'assertive' : 'polite',\n    };\n  },\n\n  /**\n   * List item accessibility props\n   */\n  listItem(label: string, index: number, total: number, hint?: string): AccessibilityProps {\n    return {\n      accessible: true,\n      accessibilityLabel: label,\n      accessibilityHint: hint,\n      accessibilityValue: { text: `Item ${index + 1} of ${total}` },\n    };\n  },\n\n  /**\n   * Card accessibility props\n   */\n  card(title: string, description?: string): AccessibilityProps {\n    const label = description ? `${title}. ${description}` : title;\n    \n    return {\n      accessible: true,\n      accessibilityRole: 'button',\n      accessibilityLabel: label,\n      accessibilityHint: 'Double tap to open',\n    };\n  },\n};\n\n/**\n * Tennis-specific accessibility helpers\n */\nexport const tennisAccessibilityHelpers = {\n  /**\n   * Score announcement\n   */\n  score(playerScore: number, opponentScore: number, set?: number): string {\n    const scoreNames = ['love', 'fifteen', 'thirty', 'forty'];\n    const playerScoreName = scoreNames[playerScore] || playerScore.toString();\n    const opponentScoreName = scoreNames[opponentScore] || opponentScore.toString();\n    \n    const setInfo = set ? ` in set ${set}` : '';\n    return `Score: ${playerScoreName} to ${opponentScoreName}${setInfo}`;\n  },\n\n  /**\n   * Drill instruction accessibility\n   */\n  drillInstruction(step: number, total: number, instruction: string): AccessibilityProps {\n    return {\n      accessible: true,\n      accessibilityLabel: `Step ${step} of ${total}: ${instruction}`,\n      accessibilityRole: 'text',\n    };\n  },\n\n  /**\n   * Video analysis result accessibility\n   */\n  analysisResult(score: number, feedback: string): AccessibilityProps {\n    return {\n      accessible: true,\n      accessibilityLabel: `Analysis complete. Score: ${score} out of 100. ${feedback}`,\n      accessibilityRole: 'text',\n      accessibilityLiveRegion: 'polite',\n    };\n  },\n\n  /**\n   * Training session status\n   */\n  sessionStatus(status: 'recording' | 'analyzing' | 'complete', duration?: number): AccessibilityProps {\n    let label = '';\n    \n    switch (status) {\n      case 'recording':\n        label = duration ? `Recording in progress. Duration: ${duration} seconds` : 'Recording in progress';\n        break;\n      case 'analyzing':\n        label = 'Analyzing video. Please wait.';\n        break;\n      case 'complete':\n        label = 'Training session complete';\n        break;\n    }\n\n    return {\n      accessible: true,\n      accessibilityLabel: label,\n      accessibilityRole: 'text',\n      accessibilityLiveRegion: 'polite',\n    };\n  },\n\n  /**\n   * Match statistics accessibility\n   */\n  matchStats(stats: { aces: number; winners: number; errors: number }): string {\n    return `Match statistics: ${stats.aces} aces, ${stats.winners} winners, ${stats.errors} unforced errors`;\n  },\n};\n\n/**\n * Focus management utilities\n */\nexport const focusManager = {\n  /**\n   * Move focus to next focusable element\n   */\n  focusNext(): void {\n    // Implementation would depend on your navigation library\n    console.log('Moving focus to next element');\n  },\n\n  /**\n   * Move focus to previous focusable element\n   */\n  focusPrevious(): void {\n    // Implementation would depend on your navigation library\n    console.log('Moving focus to previous element');\n  },\n\n  /**\n   * Trap focus within a container\n   */\n  trapFocus(containerRef: any): () => void {\n    // Implementation for focus trapping\n    console.log('Trapping focus in container');\n    \n    return () => {\n      console.log('Releasing focus trap');\n    };\n  },\n};\n\n// Initialize accessibility manager\nexport const accessibilityManager = AccessibilityManager.getInstance();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,iBAAiB,EAAEC,QAAQ,QAAQ,cAAc;AAiE1D,WAAaC,oBAAoB;EAAA,SAAAA,qBAAA;IAAAC,eAAA,OAAAD,oBAAA;IAAA,KAEvBE,qBAAqB,IAAAC,aAAA,GAAAC,CAAA,OAAG,KAAK;IAAA,KAC7BC,qBAAqB,IAAAF,aAAA,GAAAC,CAAA,OAAG,KAAK;IAAA,KAC7BE,2BAA2B,IAAAH,aAAA,GAAAC,CAAA,OAAG,KAAK;IAAA,KACnCG,SAAS,IAAAJ,aAAA,GAAAC,CAAA,OAAmB,EAAE;EAAA;EAAA,OAAAI,YAAA,CAAAR,oBAAA;IAAAS,GAAA;IAAAC,KAAA;MAAA,IAAAC,WAAA,GAAAC,iBAAA,CAStC,aAAkC;QAAAT,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAChC,IAAI;UAAAD,aAAA,GAAAC,CAAA;UAEF,IAAI,CAACF,qBAAqB,SAASJ,iBAAiB,CAACI,qBAAqB,CAAC,CAAC;UAACC,aAAA,GAAAC,CAAA;UAG7E,IAAIL,QAAQ,CAACe,EAAE,KAAK,KAAK,EAAE;YAAAX,aAAA,GAAAY,CAAA;YAAAZ,aAAA,GAAAC,CAAA;YACzB,IAAI,CAACC,qBAAqB,SAASP,iBAAiB,CAACO,qBAAqB,CAAC,CAAC;YAACF,aAAA,GAAAC,CAAA;YAC7E,IAAI,CAACE,2BAA2B,SAASR,iBAAiB,CAACQ,2BAA2B,CAAC,CAAC;UAC1F,CAAC;YAAAH,aAAA,GAAAY,CAAA;UAAA;UAAAZ,aAAA,GAAAC,CAAA;UAGD,IAAI,CAACY,cAAc,CAAC,CAAC;QACvB,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAAd,aAAA,GAAAC,CAAA;UACdc,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;QACrE;MACF,CAAC;MAAA,SAhBKE,UAAUA,CAAA;QAAA,OAAAR,WAAA,CAAAS,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVF,UAAU;IAAA;EAAA;IAAAV,GAAA;IAAAC,KAAA,EAkBhB,SAAQM,cAAcA,CAAA,EAAS;MAAA,IAAAM,KAAA;MAAAnB,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MAC7BN,iBAAiB,CAACyB,gBAAgB,CAAC,qBAAqB,EAAE,UAACC,OAAO,EAAK;QAAArB,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QACrEkB,KAAI,CAACpB,qBAAqB,GAAGsB,OAAO;QAACrB,aAAA,GAAAC,CAAA;QACrCkB,KAAI,CAACG,eAAe,CAAC,CAAC;MACxB,CAAC,CAAC;MAACtB,aAAA,GAAAC,CAAA;MAEH,IAAIL,QAAQ,CAACe,EAAE,KAAK,KAAK,EAAE;QAAAX,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QACzBN,iBAAiB,CAACyB,gBAAgB,CAAC,qBAAqB,EAAE,UAACC,OAAO,EAAK;UAAArB,aAAA,GAAAU,CAAA;UAAAV,aAAA,GAAAC,CAAA;UACrEkB,KAAI,CAACjB,qBAAqB,GAAGmB,OAAO;UAACrB,aAAA,GAAAC,CAAA;UACrCkB,KAAI,CAACG,eAAe,CAAC,CAAC;QACxB,CAAC,CAAC;QAACtB,aAAA,GAAAC,CAAA;QAEHN,iBAAiB,CAACyB,gBAAgB,CAAC,2BAA2B,EAAE,UAACC,OAAO,EAAK;UAAArB,aAAA,GAAAU,CAAA;UAAAV,aAAA,GAAAC,CAAA;UAC3EkB,KAAI,CAAChB,2BAA2B,GAAGkB,OAAO;UAACrB,aAAA,GAAAC,CAAA;UAC3CkB,KAAI,CAACG,eAAe,CAAC,CAAC;QACxB,CAAC,CAAC;MACJ,CAAC;QAAAtB,aAAA,GAAAY,CAAA;MAAA;IACH;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAED,SAAAgB,sBAAsBA,CAAA,EAAY;MAAAvB,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MAChC,OAAO,IAAI,CAACF,qBAAqB;IACnC;EAAC;IAAAO,GAAA;IAAAC,KAAA,EAED,SAAAiB,sBAAsBA,CAAA,EAAY;MAAAxB,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MAChC,OAAO,IAAI,CAACC,qBAAqB;IACnC;EAAC;IAAAI,GAAA;IAAAC,KAAA,EAED,SAAAkB,4BAA4BA,CAAA,EAAY;MAAAzB,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MACtC,OAAO,IAAI,CAACE,2BAA2B;IACzC;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAED,SAAAmB,WAAWA,CAACC,QAAoB,EAAQ;MAAA3B,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MACtC,IAAI,CAACG,SAAS,CAACwB,IAAI,CAACD,QAAQ,CAAC;IAC/B;EAAC;IAAArB,GAAA;IAAAC,KAAA,EAED,SAAAsB,cAAcA,CAACF,QAAoB,EAAQ;MAAA3B,aAAA,GAAAU,CAAA;MACzC,IAAMoB,KAAK,IAAA9B,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACG,SAAS,CAAC2B,OAAO,CAACJ,QAAQ,CAAC;MAAC3B,aAAA,GAAAC,CAAA;MAC/C,IAAI6B,KAAK,GAAG,CAAC,CAAC,EAAE;QAAA9B,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QACd,IAAI,CAACG,SAAS,CAAC4B,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjC,CAAC;QAAA9B,aAAA,GAAAY,CAAA;MAAA;IACH;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAED,SAAQe,eAAeA,CAAA,EAAS;MAAAtB,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MAC9B,IAAI,CAACG,SAAS,CAAC6B,OAAO,CAAC,UAAAN,QAAQ,EAAI;QAAA3B,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QACjC,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACF0B,QAAQ,CAAC,CAAC;QACZ,CAAC,CAAC,OAAOb,KAAK,EAAE;UAAAd,aAAA,GAAAC,CAAA;UACdc,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC1D;MACF,CAAC,CAAC;IACJ;EAAC;IAAAR,GAAA;IAAAC,KAAA,EAKD,SAAA2B,QAAQA,CAACC,OAAe,EAAqD;MAAA,IAAnDC,QAAgC,GAAAlB,SAAA,CAAAmB,MAAA,QAAAnB,SAAA,QAAAoB,SAAA,GAAApB,SAAA,OAAAlB,aAAA,GAAAY,CAAA,UAAG,QAAQ;MAAAZ,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MACnE,IAAI,IAAI,CAACF,qBAAqB,EAAE;QAAAC,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAC9BN,iBAAiB,CAAC4C,wBAAwB,CAACJ,OAAO,CAAC;MACrD,CAAC;QAAAnC,aAAA,GAAAY,CAAA;MAAA;IACH;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAKD,SAAAiC,QAAQA,CAACC,QAAgB,EAAQ;MAAAzC,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MAC/BN,iBAAiB,CAAC+C,qBAAqB,CAACD,QAAQ,CAAC;IACnD;EAAC;IAAAnC,GAAA;IAAAC,KAAA,EA3FD,SAAOoC,WAAWA,CAAA,EAAyB;MAAA3C,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MACzC,IAAI,CAACJ,oBAAoB,CAAC+C,QAAQ,EAAE;QAAA5C,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAClCJ,oBAAoB,CAAC+C,QAAQ,GAAG,IAAI/C,oBAAoB,CAAC,CAAC;MAC5D,CAAC;QAAAG,aAAA,GAAAY,CAAA;MAAA;MAAAZ,aAAA,GAAAC,CAAA;MACD,OAAOJ,oBAAoB,CAAC+C,QAAQ;IACtC;EAAC;AAAA;AA4FH,OAAO,IAAMC,oBAAoB,IAAA7C,aAAA,GAAAC,CAAA,QAAG;EAIlC6C,MAAM,WAANA,MAAMA,CAACC,KAAa,EAAEC,IAAa,EAAwC;IAAA,IAAtCC,QAAQ,GAAA/B,SAAA,CAAAmB,MAAA,QAAAnB,SAAA,QAAAoB,SAAA,GAAApB,SAAA,OAAAlB,aAAA,GAAAY,CAAA,UAAG,KAAK;IAAAZ,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IACnD,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,QAAQ;MAC3BC,kBAAkB,EAAEL,KAAK;MACzBM,iBAAiB,EAAEL,IAAI;MACvBM,kBAAkB,EAAE;QAAEL,QAAQ,EAARA;MAAS;IACjC,CAAC;EACH,CAAC;EAKDM,IAAI,WAAJA,IAAIA,CAACR,KAAa,EAAEC,IAAa,EAAsB;IAAAhD,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IACrD,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,MAAM;MACzBC,kBAAkB,EAAEL,KAAK;MACzBM,iBAAiB,EAAEL;IACrB,CAAC;EACH,CAAC;EAKDQ,KAAK,WAALA,KAAKA,CAACC,WAAmB,EAA0C;IAAA,IAAxCC,UAAU,GAAAxC,SAAA,CAAAmB,MAAA,QAAAnB,SAAA,QAAAoB,SAAA,GAAApB,SAAA,OAAAlB,aAAA,GAAAY,CAAA,UAAG,KAAK;IAAAZ,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAC3C,IAAIyD,UAAU,EAAE;MAAA1D,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACd,OAAO;QACLiD,UAAU,EAAE,KAAK;QACjBS,yBAAyB,EAAE;MAC7B,CAAC;IACH,CAAC;MAAA3D,aAAA,GAAAY,CAAA;IAAA;IAAAZ,aAAA,GAAAC,CAAA;IAED,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,OAAO;MAC1BC,kBAAkB,EAAEK;IACtB,CAAC;EACH,CAAC;EAKDG,MAAM,WAANA,MAAMA,CAACC,IAAY,EAAiC;IAAA,IAA/BC,KAAK,GAAA5C,SAAA,CAAAmB,MAAA,QAAAnB,SAAA,QAAAoB,SAAA,GAAApB,SAAA,OAAAlB,aAAA,GAAAY,CAAA,UAAG,CAAC;IAAAZ,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAC5B,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,QAAQ;MAC3BC,kBAAkB,EAAES,IAAI;MACxBE,kBAAkB,EAAE;QAAEF,IAAI,EAAE,iBAAiBC,KAAK;MAAG;IACvD,CAAC;EACH,CAAC;EAKDE,SAAS,WAATA,SAASA,CAACjB,KAAa,EAAEC,IAAa,EAAwC;IAAA,IAAtCiB,QAAQ,GAAA/C,SAAA,CAAAmB,MAAA,QAAAnB,SAAA,QAAAoB,SAAA,GAAApB,SAAA,OAAAlB,aAAA,GAAAY,CAAA,WAAG,KAAK;IAAAZ,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IACtD,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBE,kBAAkB,EAAEL,KAAK,IAAIkB,QAAQ,IAAAjE,aAAA,GAAAY,CAAA,WAAG,aAAa,KAAAZ,aAAA,GAAAY,CAAA,WAAG,EAAE,EAAC;MAC3DyC,iBAAiB,EAAEL;IACrB,CAAC;EACH,CAAC;EAKDkB,QAAQ,WAARA,QAAQA,CAACnB,KAAa,EAAEoB,OAAgB,EAAEnB,IAAa,EAAsB;IAAAhD,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAC3E,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,UAAU;MAC7BC,kBAAkB,EAAEL,KAAK;MACzBM,iBAAiB,EAAEL,IAAI;MACvBM,kBAAkB,EAAE;QAAEa,OAAO,EAAPA;MAAQ;IAChC,CAAC;EACH,CAAC;EAKDC,MAAM,WAANA,OAAMA,CAACrB,KAAa,EAAExC,KAAc,EAAEyC,IAAa,EAAsB;IAAAhD,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IACvE,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,QAAQ;MAC3BC,kBAAkB,EAAEL,KAAK;MACzBM,iBAAiB,EAAEL,IAAI;MACvBM,kBAAkB,EAAE;QAAEa,OAAO,EAAE5D;MAAM;IACvC,CAAC;EACH,CAAC;EAKD8D,WAAW,WAAXA,WAAWA,CAACtB,KAAa,EAAExC,KAAa,EAA0C;IAAA,IAAxC+D,GAAG,GAAApD,SAAA,CAAAmB,MAAA,QAAAnB,SAAA,QAAAoB,SAAA,GAAApB,SAAA,OAAAlB,aAAA,GAAAY,CAAA,WAAG,CAAC;IAAA,IAAE2D,GAAG,GAAArD,SAAA,CAAAmB,MAAA,QAAAnB,SAAA,QAAAoB,SAAA,GAAApB,SAAA,OAAAlB,aAAA,GAAAY,CAAA,WAAG,GAAG;IAAAZ,aAAA,GAAAU,CAAA;IAC1D,IAAM8D,UAAU,IAAAxE,aAAA,GAAAC,CAAA,QAAGwE,IAAI,CAACC,KAAK,CAAE,CAACnE,KAAK,GAAG+D,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAAC,GAAI,GAAG,CAAC;IAACtE,aAAA,GAAAC,CAAA;IAEnE,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,aAAa;MAChCC,kBAAkB,EAAEL,KAAK;MACzBgB,kBAAkB,EAAE;QAClBO,GAAG,EAAHA,GAAG;QACHC,GAAG,EAAHA,GAAG;QACHI,GAAG,EAAEpE,KAAK;QACVsD,IAAI,EAAE,GAAGW,UAAU;MACrB;IACF,CAAC;EACH,CAAC;EAKDI,GAAG,WAAHA,GAAGA,CAAC7B,KAAa,EAAE8B,QAAiB,EAAE/C,KAAa,EAAEgD,KAAa,EAAsB;IAAA9E,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IACtF,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,KAAK;MACxBC,kBAAkB,EAAEL,KAAK;MACzBO,kBAAkB,EAAE;QAAEuB,QAAQ,EAARA;MAAS,CAAC;MAChCd,kBAAkB,EAAE;QAAEF,IAAI,EAAE,OAAO/B,KAAK,GAAG,CAAC,OAAOgD,KAAK;MAAG;IAC7D,CAAC;EACH,CAAC;EAKDC,KAAK,WAALA,KAAKA,CAAC5C,OAAe,EAA+E;IAAA,IAA7E6C,IAA8C,GAAA9D,SAAA,CAAAmB,MAAA,QAAAnB,SAAA,QAAAoB,SAAA,GAAApB,SAAA,OAAAlB,aAAA,GAAAY,CAAA,WAAG,MAAM;IAAAZ,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAC5E,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,OAAO;MAC1BC,kBAAkB,EAAE,GAAG4B,IAAI,WAAW7C,OAAO,EAAE;MAC/C8C,uBAAuB,EAAED,IAAI,KAAK,OAAO,IAAAhF,aAAA,GAAAY,CAAA,WAAG,WAAW,KAAAZ,aAAA,GAAAY,CAAA,WAAG,QAAQ;IACpE,CAAC;EACH,CAAC;EAKDsE,QAAQ,WAARA,QAAQA,CAACnC,KAAa,EAAEjB,KAAa,EAAEgD,KAAa,EAAE9B,IAAa,EAAsB;IAAAhD,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IACvF,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBE,kBAAkB,EAAEL,KAAK;MACzBM,iBAAiB,EAAEL,IAAI;MACvBe,kBAAkB,EAAE;QAAEF,IAAI,EAAE,QAAQ/B,KAAK,GAAG,CAAC,OAAOgD,KAAK;MAAG;IAC9D,CAAC;EACH,CAAC;EAKDK,IAAI,WAAJA,IAAIA,CAACC,KAAa,EAAE3B,WAAoB,EAAsB;IAAAzD,aAAA,GAAAU,CAAA;IAC5D,IAAMqC,KAAK,IAAA/C,aAAA,GAAAC,CAAA,QAAGwD,WAAW,IAAAzD,aAAA,GAAAY,CAAA,WAAG,GAAGwE,KAAK,KAAK3B,WAAW,EAAE,KAAAzD,aAAA,GAAAY,CAAA,WAAGwE,KAAK;IAACpF,aAAA,GAAAC,CAAA;IAE/D,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,QAAQ;MAC3BC,kBAAkB,EAAEL,KAAK;MACzBM,iBAAiB,EAAE;IACrB,CAAC;EACH;AACF,CAAC;AAKD,OAAO,IAAMgC,0BAA0B,IAAArF,aAAA,GAAAC,CAAA,QAAG;EAIxCqF,KAAK,WAALA,KAAKA,CAACC,WAAmB,EAAEC,aAAqB,EAAEC,GAAY,EAAU;IAAAzF,aAAA,GAAAU,CAAA;IACtE,IAAMgF,UAAU,IAAA1F,aAAA,GAAAC,CAAA,QAAG,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;IACzD,IAAM0F,eAAe,IAAA3F,aAAA,GAAAC,CAAA,QAAG,CAAAD,aAAA,GAAAY,CAAA,WAAA8E,UAAU,CAACH,WAAW,CAAC,MAAAvF,aAAA,GAAAY,CAAA,WAAI2E,WAAW,CAACK,QAAQ,CAAC,CAAC;IACzE,IAAMC,iBAAiB,IAAA7F,aAAA,GAAAC,CAAA,QAAG,CAAAD,aAAA,GAAAY,CAAA,WAAA8E,UAAU,CAACF,aAAa,CAAC,MAAAxF,aAAA,GAAAY,CAAA,WAAI4E,aAAa,CAACI,QAAQ,CAAC,CAAC;IAE/E,IAAME,OAAO,IAAA9F,aAAA,GAAAC,CAAA,QAAGwF,GAAG,IAAAzF,aAAA,GAAAY,CAAA,WAAG,WAAW6E,GAAG,EAAE,KAAAzF,aAAA,GAAAY,CAAA,WAAG,EAAE;IAACZ,aAAA,GAAAC,CAAA;IAC5C,OAAO,UAAU0F,eAAe,OAAOE,iBAAiB,GAAGC,OAAO,EAAE;EACtE,CAAC;EAKDC,gBAAgB,WAAhBA,gBAAgBA,CAACC,IAAY,EAAElB,KAAa,EAAEmB,WAAmB,EAAsB;IAAAjG,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IACrF,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBE,kBAAkB,EAAE,QAAQ4C,IAAI,OAAOlB,KAAK,KAAKmB,WAAW,EAAE;MAC9D9C,iBAAiB,EAAE;IACrB,CAAC;EACH,CAAC;EAKD+C,cAAc,WAAdA,cAAcA,CAACZ,KAAa,EAAEa,QAAgB,EAAsB;IAAAnG,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAClE,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBE,kBAAkB,EAAE,6BAA6BkC,KAAK,gBAAgBa,QAAQ,EAAE;MAChFhD,iBAAiB,EAAE,MAAM;MACzB8B,uBAAuB,EAAE;IAC3B,CAAC;EACH,CAAC;EAKDmB,aAAa,WAAbA,aAAaA,CAACC,MAA8C,EAAEC,QAAiB,EAAsB;IAAAtG,aAAA,GAAAU,CAAA;IACnG,IAAIqC,KAAK,IAAA/C,aAAA,GAAAC,CAAA,QAAG,EAAE;IAACD,aAAA,GAAAC,CAAA;IAEf,QAAQoG,MAAM;MACZ,KAAK,WAAW;QAAArG,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QACd8C,KAAK,GAAGuD,QAAQ,IAAAtG,aAAA,GAAAY,CAAA,WAAG,oCAAoC0F,QAAQ,UAAU,KAAAtG,aAAA,GAAAY,CAAA,WAAG,uBAAuB;QAACZ,aAAA,GAAAC,CAAA;QACpG;MACF,KAAK,WAAW;QAAAD,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QACd8C,KAAK,GAAG,+BAA+B;QAAC/C,aAAA,GAAAC,CAAA;QACxC;MACF,KAAK,UAAU;QAAAD,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QACb8C,KAAK,GAAG,2BAA2B;QAAC/C,aAAA,GAAAC,CAAA;QACpC;IACJ;IAACD,aAAA,GAAAC,CAAA;IAED,OAAO;MACLiD,UAAU,EAAE,IAAI;MAChBE,kBAAkB,EAAEL,KAAK;MACzBI,iBAAiB,EAAE,MAAM;MACzB8B,uBAAuB,EAAE;IAC3B,CAAC;EACH,CAAC;EAKDsB,UAAU,WAAVA,UAAUA,CAACC,KAAwD,EAAU;IAAAxG,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAC3E,OAAO,qBAAqBuG,KAAK,CAACC,IAAI,UAAUD,KAAK,CAACE,OAAO,aAAaF,KAAK,CAACG,MAAM,kBAAkB;EAC1G;AACF,CAAC;AAKD,OAAO,IAAMC,YAAY,IAAA5G,aAAA,GAAAC,CAAA,QAAG;EAI1B4G,SAAS,WAATA,SAASA,CAAA,EAAS;IAAA7G,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAEhBc,OAAO,CAAC+F,GAAG,CAAC,8BAA8B,CAAC;EAC7C,CAAC;EAKDC,aAAa,WAAbA,aAAaA,CAAA,EAAS;IAAA/G,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAEpBc,OAAO,CAAC+F,GAAG,CAAC,kCAAkC,CAAC;EACjD,CAAC;EAKDE,SAAS,WAATA,SAASA,CAACC,YAAiB,EAAc;IAAAjH,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAEvCc,OAAO,CAAC+F,GAAG,CAAC,6BAA6B,CAAC;IAAC9G,aAAA,GAAAC,CAAA;IAE3C,OAAO,YAAM;MAAAD,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MACXc,OAAO,CAAC+F,GAAG,CAAC,sBAAsB,CAAC;IACrC,CAAC;EACH;AACF,CAAC;AAGD,OAAO,IAAMI,oBAAoB,IAAAlH,aAAA,GAAAC,CAAA,QAAGJ,oBAAoB,CAAC8C,WAAW,CAAC,CAAC", "ignoreList": []}