7e99ed19fbbc5ea56d2a25af8f88cb3b
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _View = _interopRequireDefault(require("../View"));
var RootTagContext = React.createContext(null);
var AppContainer = React.forwardRef(function (props, forwardedRef) {
  var children = props.children,
    WrapperComponent = props.WrapperComponent;
  var innerView = React.createElement(_View.default, {
    children: children,
    key: 1,
    style: styles.appContainer
  });
  if (WrapperComponent) {
    innerView = React.createElement(WrapperComponent, null, innerView);
  }
  return React.createElement(RootTagContext.Provider, {
    value: props.rootTag
  }, React.createElement(_View.default, {
    ref: forwardedRef,
    style: styles.appContainer
  }, innerView));
});
AppContainer.displayName = 'AppContainer';
var _default = exports.default = AppContainer;
var styles = _StyleSheet.default.create({
  appContainer: {
    flex: 1,
    pointerEvents: 'box-none'
  }
});
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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