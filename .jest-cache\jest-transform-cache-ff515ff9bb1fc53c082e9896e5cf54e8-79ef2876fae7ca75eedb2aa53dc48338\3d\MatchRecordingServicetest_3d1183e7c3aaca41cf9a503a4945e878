283ba73fbf0510cb4f76d548401cae95
_getJestObj().mock("../../lib/supabase", function () {
  return {
    supabase: {
      from: jest.fn()
    }
  };
});
_getJestObj().mock("../../utils/performance", function () {
  return {
    performanceMonitor: {
      start: jest.fn(),
      end: jest.fn()
    }
  };
});
_getJestObj().mock("../../utils/errorHandling", function () {
  return {
    handleError: jest.fn(function (error) {
      return {
        userMessage: error.message
      };
    })
  };
});
_getJestObj().mock('expo-file-system', function () {
  return {
    getInfoAsync: jest.fn(),
    readAsStringAsync: jest.fn(),
    copyAsync: jest.fn(),
    cacheDirectory: '/mock/cache/'
  };
});
_getJestObj().mock('expo-camera', function () {
  return {
    Camera: {
      requestCameraPermissionsAsync: jest.fn(),
      requestMicrophonePermissionsAsync: jest.fn()
    }
  };
});
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockMatchRepository = {
  createMatch: jest.fn(),
  updateMatch: jest.fn(),
  getRecentMatches: jest.fn()
};
var mockVideoRecordingService = {
  initialize: jest.fn(),
  startRecording: jest.fn(),
  stopRecording: jest.fn()
};
describe('MatchRecordingService Core Functionality', function () {
  beforeEach(function () {
    jest.clearAllMocks();
  });
  describe('Match Recording Logic', function () {
    it('should validate match metadata correctly', function () {
      var validMetadata = {
        userId: 'user123',
        opponentName: 'John Doe',
        matchType: 'friendly',
        matchFormat: 'best_of_3',
        surface: 'hard',
        location: 'Local Tennis Club',
        startTime: new Date().toISOString()
      };
      expect(validMetadata.userId).toBeTruthy();
      expect(validMetadata.opponentName).toBeTruthy();
      expect(validMetadata.matchType).toBe('friendly');
      expect(validMetadata.matchFormat).toBe('best_of_3');
      expect(validMetadata.surface).toBe('hard');
    });
    it('should handle invalid metadata', function () {
      var invalidMetadata = {
        userId: '',
        opponentName: '',
        matchType: 'friendly',
        matchFormat: 'best_of_3',
        surface: 'hard',
        location: 'Local Tennis Club',
        startTime: new Date().toISOString()
      };
      expect(invalidMetadata.userId).toBeFalsy();
      expect(invalidMetadata.opponentName).toBeFalsy();
    });
    it('should calculate match statistics correctly', function () {
      var mockStatistics = {
        aces: 5,
        doubleFaults: 2,
        winners: 12,
        unforcedErrors: 8,
        totalPointsWon: 45,
        totalPointsPlayed: 80
      };
      var winPercentage = mockStatistics.totalPointsWon / mockStatistics.totalPointsPlayed * 100;
      expect(winPercentage).toBeCloseTo(56.25);
      var errorRate = mockStatistics.unforcedErrors / mockStatistics.totalPointsPlayed * 100;
      expect(errorRate).toBeCloseTo(10);
    });
    it('should handle score tracking correctly', function () {
      var mockScore = {
        sets: [{
          setNumber: 1,
          userGames: 6,
          opponentGames: 4,
          isCompleted: true
        }, {
          setNumber: 2,
          userGames: 4,
          opponentGames: 6,
          isCompleted: true
        }, {
          setNumber: 3,
          userGames: 3,
          opponentGames: 2,
          isCompleted: false
        }],
        setsWon: 1,
        setsLost: 1
      };
      expect(mockScore.sets).toHaveLength(3);
      expect(mockScore.sets[0].isCompleted).toBe(true);
      expect(mockScore.sets[2].isCompleted).toBe(false);
      expect(mockScore.setsWon).toBe(1);
      expect(mockScore.setsLost).toBe(1);
    });
  });
  describe('Point Recording Logic', function () {
    it('should validate game events correctly', function () {
      var validGameEvent = {
        eventType: 'winner',
        player: 'user',
        shotType: 'forehand',
        courtPosition: {
          x: 0.5,
          y: 0.3
        },
        timestamp: Date.now()
      };
      expect(validGameEvent.eventType).toBe('winner');
      expect(validGameEvent.player).toBe('user');
      expect(validGameEvent.shotType).toBe('forehand');
      expect(validGameEvent.courtPosition.x).toBeGreaterThanOrEqual(0);
      expect(validGameEvent.courtPosition.x).toBeLessThanOrEqual(1);
      expect(validGameEvent.timestamp).toBeGreaterThan(0);
    });
    it('should handle different event types', function () {
      var eventTypes = ['ace', 'winner', 'unforced_error', 'forced_error', 'double_fault'];
      eventTypes.forEach(function (eventType) {
        var event = {
          eventType: eventType,
          player: 'user',
          shotType: 'serve',
          courtPosition: {
            x: 0.5,
            y: 0.5
          },
          timestamp: Date.now()
        };
        expect(event.eventType).toBe(eventType);
        expect(['ace', 'winner', 'unforced_error', 'forced_error', 'double_fault']).toContain(event.eventType);
      });
    });
    it('should track statistics updates', function () {
      var initialStats = {
        aces: 0,
        winners: 0,
        unforcedErrors: 0,
        totalPointsWon: 0
      };
      var aceStats = Object.assign({}, initialStats, {
        aces: initialStats.aces + 1,
        totalPointsWon: initialStats.totalPointsWon + 1
      });
      expect(aceStats.aces).toBe(1);
      expect(aceStats.totalPointsWon).toBe(1);
      var winnerStats = Object.assign({}, aceStats, {
        winners: aceStats.winners + 1,
        totalPointsWon: aceStats.totalPointsWon + 1
      });
      expect(winnerStats.winners).toBe(1);
      expect(winnerStats.totalPointsWon).toBe(2);
    });
  });
  describe('Match Completion Logic', function () {
    it('should determine match completion correctly', function () {
      var completedMatch = {
        sets: [{
          setNumber: 1,
          userGames: 6,
          opponentGames: 4,
          isCompleted: true
        }, {
          setNumber: 2,
          userGames: 6,
          opponentGames: 3,
          isCompleted: true
        }],
        setsWon: 2,
        setsLost: 0,
        finalScore: '6-4, 6-3',
        result: 'win'
      };
      expect(completedMatch.setsWon).toBe(2);
      expect(completedMatch.result).toBe('win');
      expect(completedMatch.finalScore).toBe('6-4, 6-3');
    });
    it('should calculate match duration', function () {
      var startTime = new Date('2024-01-15T10:00:00Z');
      var endTime = new Date('2024-01-15T11:30:00Z');
      var durationMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60);
      expect(durationMinutes).toBe(90);
    });
    it('should generate final statistics', function () {
      var finalStats = {
        totalPointsWon: 65,
        totalPointsPlayed: 120,
        aces: 8,
        doubleFaults: 3,
        winners: 25,
        unforcedErrors: 15,
        firstServePercentage: 68
      };
      var winPercentage = finalStats.totalPointsWon / finalStats.totalPointsPlayed * 100;
      expect(winPercentage).toBeCloseTo(54.17, 2);
      var acePercentage = finalStats.aces / finalStats.totalPointsPlayed * 100;
      expect(acePercentage).toBeCloseTo(6.67, 2);
    });
  });
  describe('Database Integration Logic', function () {
    it('should format match data for database correctly', function () {
      var matchData = {
        id: 'match123',
        user_id: 'user123',
        opponent_name: 'John Doe',
        match_type: 'friendly',
        match_format: 'best_of_3',
        surface: 'hard',
        location: 'Local Tennis Club',
        status: 'recording',
        created_at: new Date().toISOString()
      };
      expect(matchData.id).toBe('match123');
      expect(matchData.user_id).toBe('user123');
      expect(matchData.opponent_name).toBe('John Doe');
      expect(matchData.match_type).toBe('friendly');
      expect(matchData.status).toBe('recording');
    });
    it('should handle offline data synchronization', function () {
      var offlineQueue = [{
        timestamp: Date.now(),
        data: {
          score: {
            setsWon: 1,
            setsLost: 0
          },
          statistics: {
            aces: 3,
            winners: 8
          },
          status: 'recording'
        }
      }];
      expect(offlineQueue).toHaveLength(1);
      expect(offlineQueue[0].data.score.setsWon).toBe(1);
      expect(offlineQueue[0].data.statistics.aces).toBe(3);
    });
    it('should validate data before database operations', function () {
      var validData = {
        user_id: 'user123',
        opponent_name: 'John Doe',
        match_type: 'friendly',
        status: 'recording'
      };
      var invalidData = {
        user_id: '',
        opponent_name: '',
        match_type: '',
        status: ''
      };
      var isValidData = function isValidData(data) {
        return data.user_id && data.opponent_name && data.match_type && data.status;
      };
      expect(isValidData(validData)).toBe(true);
      expect(isValidData(invalidData)).toBe(false);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfZ2V0SmVzdE9iaiIsIm1vY2siLCJzdXBhYmFzZSIsImZyb20iLCJqZXN0IiwiZm4iLCJwZXJmb3JtYW5jZU1vbml0b3IiLCJzdGFydCIsImVuZCIsImhhbmRsZUVycm9yIiwiZXJyb3IiLCJ1c2VyTWVzc2FnZSIsIm1lc3NhZ2UiLCJnZXRJbmZvQXN5bmMiLCJyZWFkQXNTdHJpbmdBc3luYyIsImNvcHlBc3luYyIsImNhY2hlRGlyZWN0b3J5IiwiQ2FtZXJhIiwicmVxdWVzdENhbWVyYVBlcm1pc3Npb25zQXN5bmMiLCJyZXF1ZXN0TWljcm9waG9uZVBlcm1pc3Npb25zQXN5bmMiLCJfcmVxdWlyZSIsInJlcXVpcmUiLCJtb2NrTWF0Y2hSZXBvc2l0b3J5IiwiY3JlYXRlTWF0Y2giLCJ1cGRhdGVNYXRjaCIsImdldFJlY2VudE1hdGNoZXMiLCJtb2NrVmlkZW9SZWNvcmRpbmdTZXJ2aWNlIiwiaW5pdGlhbGl6ZSIsInN0YXJ0UmVjb3JkaW5nIiwic3RvcFJlY29yZGluZyIsImRlc2NyaWJlIiwiYmVmb3JlRWFjaCIsImNsZWFyQWxsTW9ja3MiLCJpdCIsInZhbGlkTWV0YWRhdGEiLCJ1c2VySWQiLCJvcHBvbmVudE5hbWUiLCJtYXRjaFR5cGUiLCJtYXRjaEZvcm1hdCIsInN1cmZhY2UiLCJsb2NhdGlvbiIsInN0YXJ0VGltZSIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsImV4cGVjdCIsInRvQmVUcnV0aHkiLCJ0b0JlIiwiaW52YWxpZE1ldGFkYXRhIiwidG9CZUZhbHN5IiwibW9ja1N0YXRpc3RpY3MiLCJhY2VzIiwiZG91YmxlRmF1bHRzIiwid2lubmVycyIsInVuZm9yY2VkRXJyb3JzIiwidG90YWxQb2ludHNXb24iLCJ0b3RhbFBvaW50c1BsYXllZCIsIndpblBlcmNlbnRhZ2UiLCJ0b0JlQ2xvc2VUbyIsImVycm9yUmF0ZSIsIm1vY2tTY29yZSIsInNldHMiLCJzZXROdW1iZXIiLCJ1c2VyR2FtZXMiLCJvcHBvbmVudEdhbWVzIiwiaXNDb21wbGV0ZWQiLCJzZXRzV29uIiwic2V0c0xvc3QiLCJ0b0hhdmVMZW5ndGgiLCJ2YWxpZEdhbWVFdmVudCIsImV2ZW50VHlwZSIsInBsYXllciIsInNob3RUeXBlIiwiY291cnRQb3NpdGlvbiIsIngiLCJ5IiwidGltZXN0YW1wIiwibm93IiwidG9CZUdyZWF0ZXJUaGFuT3JFcXVhbCIsInRvQmVMZXNzVGhhbk9yRXF1YWwiLCJ0b0JlR3JlYXRlclRoYW4iLCJldmVudFR5cGVzIiwiZm9yRWFjaCIsImV2ZW50IiwidG9Db250YWluIiwiaW5pdGlhbFN0YXRzIiwiYWNlU3RhdHMiLCJPYmplY3QiLCJhc3NpZ24iLCJ3aW5uZXJTdGF0cyIsImNvbXBsZXRlZE1hdGNoIiwiZmluYWxTY29yZSIsInJlc3VsdCIsImVuZFRpbWUiLCJkdXJhdGlvbk1pbnV0ZXMiLCJnZXRUaW1lIiwiZmluYWxTdGF0cyIsImZpcnN0U2VydmVQZXJjZW50YWdlIiwiYWNlUGVyY2VudGFnZSIsIm1hdGNoRGF0YSIsImlkIiwidXNlcl9pZCIsIm9wcG9uZW50X25hbWUiLCJtYXRjaF90eXBlIiwibWF0Y2hfZm9ybWF0Iiwic3RhdHVzIiwiY3JlYXRlZF9hdCIsIm9mZmxpbmVRdWV1ZSIsImRhdGEiLCJzY29yZSIsInN0YXRpc3RpY3MiLCJ2YWxpZERhdGEiLCJpbnZhbGlkRGF0YSIsImlzVmFsaWREYXRhIl0sInNvdXJjZXMiOlsiTWF0Y2hSZWNvcmRpbmdTZXJ2aWNlLnRlc3QudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBNYXRjaCBSZWNvcmRpbmcgU2VydmljZSBUZXN0c1xuICogVGVzdHMgZm9yIHJlYWwgbWF0Y2ggcmVjb3JkaW5nIGZ1bmN0aW9uYWxpdHkgd2l0aCBkYXRhYmFzZSBpbnRlZ3JhdGlvblxuICovXG5cbi8vIE1vY2sgYWxsIGV4dGVybmFsIGRlcGVuZGVuY2llcyBmaXJzdFxuamVzdC5tb2NrKCdAL2xpYi9zdXBhYmFzZScsICgpID0+ICh7XG4gIHN1cGFiYXNlOiB7XG4gICAgZnJvbTogamVzdC5mbigpLFxuICB9LFxufSkpO1xuXG5qZXN0Lm1vY2soJ0AvdXRpbHMvcGVyZm9ybWFuY2UnLCAoKSA9PiAoe1xuICBwZXJmb3JtYW5jZU1vbml0b3I6IHtcbiAgICBzdGFydDogamVzdC5mbigpLFxuICAgIGVuZDogamVzdC5mbigpLFxuICB9LFxufSkpO1xuXG5qZXN0Lm1vY2soJ0AvdXRpbHMvZXJyb3JIYW5kbGluZycsICgpID0+ICh7XG4gIGhhbmRsZUVycm9yOiBqZXN0LmZuKChlcnJvcikgPT4gKHsgdXNlck1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UgfSkpLFxufSkpO1xuXG4vLyBNb2NrIEV4cG8gbW9kdWxlc1xuamVzdC5tb2NrKCdleHBvLWZpbGUtc3lzdGVtJywgKCkgPT4gKHtcbiAgZ2V0SW5mb0FzeW5jOiBqZXN0LmZuKCksXG4gIHJlYWRBc1N0cmluZ0FzeW5jOiBqZXN0LmZuKCksXG4gIGNvcHlBc3luYzogamVzdC5mbigpLFxuICBjYWNoZURpcmVjdG9yeTogJy9tb2NrL2NhY2hlLycsXG59KSk7XG5cbmplc3QubW9jaygnZXhwby1jYW1lcmEnLCAoKSA9PiAoe1xuICBDYW1lcmE6IHtcbiAgICByZXF1ZXN0Q2FtZXJhUGVybWlzc2lvbnNBc3luYzogamVzdC5mbigpLFxuICAgIHJlcXVlc3RNaWNyb3Bob25lUGVybWlzc2lvbnNBc3luYzogamVzdC5mbigpLFxuICB9LFxufSkpO1xuXG4vLyBDcmVhdGUgbW9jayBzZXJ2aWNlc1xuY29uc3QgbW9ja01hdGNoUmVwb3NpdG9yeSA9IHtcbiAgY3JlYXRlTWF0Y2g6IGplc3QuZm4oKSxcbiAgdXBkYXRlTWF0Y2g6IGplc3QuZm4oKSxcbiAgZ2V0UmVjZW50TWF0Y2hlczogamVzdC5mbigpLFxufTtcblxuY29uc3QgbW9ja1ZpZGVvUmVjb3JkaW5nU2VydmljZSA9IHtcbiAgaW5pdGlhbGl6ZTogamVzdC5mbigpLFxuICBzdGFydFJlY29yZGluZzogamVzdC5mbigpLFxuICBzdG9wUmVjb3JkaW5nOiBqZXN0LmZuKCksXG59O1xuXG4vLyBTaW1wbGUgdGVzdCBpbXBsZW1lbnRhdGlvbiB3aXRob3V0IGNvbXBsZXggc2VydmljZSBkZXBlbmRlbmNpZXNcbmRlc2NyaWJlKCdNYXRjaFJlY29yZGluZ1NlcnZpY2UgQ29yZSBGdW5jdGlvbmFsaXR5JywgKCkgPT4ge1xuICBiZWZvcmVFYWNoKCgpID0+IHtcbiAgICBqZXN0LmNsZWFyQWxsTW9ja3MoKTtcbiAgfSk7XG5cbiAgZGVzY3JpYmUoJ01hdGNoIFJlY29yZGluZyBMb2dpYycsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIHZhbGlkYXRlIG1hdGNoIG1ldGFkYXRhIGNvcnJlY3RseScsICgpID0+IHtcbiAgICAgIGNvbnN0IHZhbGlkTWV0YWRhdGEgPSB7XG4gICAgICAgIHVzZXJJZDogJ3VzZXIxMjMnLFxuICAgICAgICBvcHBvbmVudE5hbWU6ICdKb2huIERvZScsXG4gICAgICAgIG1hdGNoVHlwZTogJ2ZyaWVuZGx5JyBhcyBjb25zdCxcbiAgICAgICAgbWF0Y2hGb3JtYXQ6ICdiZXN0X29mXzMnIGFzIGNvbnN0LFxuICAgICAgICBzdXJmYWNlOiAnaGFyZCcgYXMgY29uc3QsXG4gICAgICAgIGxvY2F0aW9uOiAnTG9jYWwgVGVubmlzIENsdWInLFxuICAgICAgICBzdGFydFRpbWU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIH07XG5cbiAgICAgIC8vIFRlc3QgdmFsaWRhdGlvbiBsb2dpY1xuICAgICAgZXhwZWN0KHZhbGlkTWV0YWRhdGEudXNlcklkKS50b0JlVHJ1dGh5KCk7XG4gICAgICBleHBlY3QodmFsaWRNZXRhZGF0YS5vcHBvbmVudE5hbWUpLnRvQmVUcnV0aHkoKTtcbiAgICAgIGV4cGVjdCh2YWxpZE1ldGFkYXRhLm1hdGNoVHlwZSkudG9CZSgnZnJpZW5kbHknKTtcbiAgICAgIGV4cGVjdCh2YWxpZE1ldGFkYXRhLm1hdGNoRm9ybWF0KS50b0JlKCdiZXN0X29mXzMnKTtcbiAgICAgIGV4cGVjdCh2YWxpZE1ldGFkYXRhLnN1cmZhY2UpLnRvQmUoJ2hhcmQnKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgaGFuZGxlIGludmFsaWQgbWV0YWRhdGEnLCAoKSA9PiB7XG4gICAgICBjb25zdCBpbnZhbGlkTWV0YWRhdGEgPSB7XG4gICAgICAgIHVzZXJJZDogJycsXG4gICAgICAgIG9wcG9uZW50TmFtZTogJycsXG4gICAgICAgIG1hdGNoVHlwZTogJ2ZyaWVuZGx5JyBhcyBjb25zdCxcbiAgICAgICAgbWF0Y2hGb3JtYXQ6ICdiZXN0X29mXzMnIGFzIGNvbnN0LFxuICAgICAgICBzdXJmYWNlOiAnaGFyZCcgYXMgY29uc3QsXG4gICAgICAgIGxvY2F0aW9uOiAnTG9jYWwgVGVubmlzIENsdWInLFxuICAgICAgICBzdGFydFRpbWU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIH07XG5cbiAgICAgIC8vIFRlc3QgdmFsaWRhdGlvblxuICAgICAgZXhwZWN0KGludmFsaWRNZXRhZGF0YS51c2VySWQpLnRvQmVGYWxzeSgpO1xuICAgICAgZXhwZWN0KGludmFsaWRNZXRhZGF0YS5vcHBvbmVudE5hbWUpLnRvQmVGYWxzeSgpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCBjYWxjdWxhdGUgbWF0Y2ggc3RhdGlzdGljcyBjb3JyZWN0bHknLCAoKSA9PiB7XG4gICAgICBjb25zdCBtb2NrU3RhdGlzdGljcyA9IHtcbiAgICAgICAgYWNlczogNSxcbiAgICAgICAgZG91YmxlRmF1bHRzOiAyLFxuICAgICAgICB3aW5uZXJzOiAxMixcbiAgICAgICAgdW5mb3JjZWRFcnJvcnM6IDgsXG4gICAgICAgIHRvdGFsUG9pbnRzV29uOiA0NSxcbiAgICAgICAgdG90YWxQb2ludHNQbGF5ZWQ6IDgwLFxuICAgICAgfTtcblxuICAgICAgLy8gVGVzdCBzdGF0aXN0aWNzIGNhbGN1bGF0aW9uc1xuICAgICAgY29uc3Qgd2luUGVyY2VudGFnZSA9IChtb2NrU3RhdGlzdGljcy50b3RhbFBvaW50c1dvbiAvIG1vY2tTdGF0aXN0aWNzLnRvdGFsUG9pbnRzUGxheWVkKSAqIDEwMDtcbiAgICAgIGV4cGVjdCh3aW5QZXJjZW50YWdlKS50b0JlQ2xvc2VUbyg1Ni4yNSk7XG5cbiAgICAgIGNvbnN0IGVycm9yUmF0ZSA9IChtb2NrU3RhdGlzdGljcy51bmZvcmNlZEVycm9ycyAvIG1vY2tTdGF0aXN0aWNzLnRvdGFsUG9pbnRzUGxheWVkKSAqIDEwMDtcbiAgICAgIGV4cGVjdChlcnJvclJhdGUpLnRvQmVDbG9zZVRvKDEwKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgaGFuZGxlIHNjb3JlIHRyYWNraW5nIGNvcnJlY3RseScsICgpID0+IHtcbiAgICAgIGNvbnN0IG1vY2tTY29yZSA9IHtcbiAgICAgICAgc2V0czogW1xuICAgICAgICAgIHsgc2V0TnVtYmVyOiAxLCB1c2VyR2FtZXM6IDYsIG9wcG9uZW50R2FtZXM6IDQsIGlzQ29tcGxldGVkOiB0cnVlIH0sXG4gICAgICAgICAgeyBzZXROdW1iZXI6IDIsIHVzZXJHYW1lczogNCwgb3Bwb25lbnRHYW1lczogNiwgaXNDb21wbGV0ZWQ6IHRydWUgfSxcbiAgICAgICAgICB7IHNldE51bWJlcjogMywgdXNlckdhbWVzOiAzLCBvcHBvbmVudEdhbWVzOiAyLCBpc0NvbXBsZXRlZDogZmFsc2UgfSxcbiAgICAgICAgXSxcbiAgICAgICAgc2V0c1dvbjogMSxcbiAgICAgICAgc2V0c0xvc3Q6IDEsXG4gICAgICB9O1xuXG4gICAgICAvLyBUZXN0IHNjb3JlIHZhbGlkYXRpb25cbiAgICAgIGV4cGVjdChtb2NrU2NvcmUuc2V0cykudG9IYXZlTGVuZ3RoKDMpO1xuICAgICAgZXhwZWN0KG1vY2tTY29yZS5zZXRzWzBdLmlzQ29tcGxldGVkKS50b0JlKHRydWUpO1xuICAgICAgZXhwZWN0KG1vY2tTY29yZS5zZXRzWzJdLmlzQ29tcGxldGVkKS50b0JlKGZhbHNlKTtcbiAgICAgIGV4cGVjdChtb2NrU2NvcmUuc2V0c1dvbikudG9CZSgxKTtcbiAgICAgIGV4cGVjdChtb2NrU2NvcmUuc2V0c0xvc3QpLnRvQmUoMSk7XG4gICAgfSk7XG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdQb2ludCBSZWNvcmRpbmcgTG9naWMnLCAoKSA9PiB7XG4gICAgaXQoJ3Nob3VsZCB2YWxpZGF0ZSBnYW1lIGV2ZW50cyBjb3JyZWN0bHknLCAoKSA9PiB7XG4gICAgICBjb25zdCB2YWxpZEdhbWVFdmVudCA9IHtcbiAgICAgICAgZXZlbnRUeXBlOiAnd2lubmVyJyBhcyBjb25zdCxcbiAgICAgICAgcGxheWVyOiAndXNlcicgYXMgY29uc3QsXG4gICAgICAgIHNob3RUeXBlOiAnZm9yZWhhbmQnLFxuICAgICAgICBjb3VydFBvc2l0aW9uOiB7IHg6IDAuNSwgeTogMC4zIH0sXG4gICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgIH07XG5cbiAgICAgIC8vIFRlc3QgZXZlbnQgdmFsaWRhdGlvblxuICAgICAgZXhwZWN0KHZhbGlkR2FtZUV2ZW50LmV2ZW50VHlwZSkudG9CZSgnd2lubmVyJyk7XG4gICAgICBleHBlY3QodmFsaWRHYW1lRXZlbnQucGxheWVyKS50b0JlKCd1c2VyJyk7XG4gICAgICBleHBlY3QodmFsaWRHYW1lRXZlbnQuc2hvdFR5cGUpLnRvQmUoJ2ZvcmVoYW5kJyk7XG4gICAgICBleHBlY3QodmFsaWRHYW1lRXZlbnQuY291cnRQb3NpdGlvbi54KS50b0JlR3JlYXRlclRoYW5PckVxdWFsKDApO1xuICAgICAgZXhwZWN0KHZhbGlkR2FtZUV2ZW50LmNvdXJ0UG9zaXRpb24ueCkudG9CZUxlc3NUaGFuT3JFcXVhbCgxKTtcbiAgICAgIGV4cGVjdCh2YWxpZEdhbWVFdmVudC50aW1lc3RhbXApLnRvQmVHcmVhdGVyVGhhbigwKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgaGFuZGxlIGRpZmZlcmVudCBldmVudCB0eXBlcycsICgpID0+IHtcbiAgICAgIGNvbnN0IGV2ZW50VHlwZXMgPSBbJ2FjZScsICd3aW5uZXInLCAndW5mb3JjZWRfZXJyb3InLCAnZm9yY2VkX2Vycm9yJywgJ2RvdWJsZV9mYXVsdCddO1xuXG4gICAgICBldmVudFR5cGVzLmZvckVhY2goZXZlbnRUeXBlID0+IHtcbiAgICAgICAgY29uc3QgZXZlbnQgPSB7XG4gICAgICAgICAgZXZlbnRUeXBlOiBldmVudFR5cGUgYXMgYW55LFxuICAgICAgICAgIHBsYXllcjogJ3VzZXInIGFzIGNvbnN0LFxuICAgICAgICAgIHNob3RUeXBlOiAnc2VydmUnLFxuICAgICAgICAgIGNvdXJ0UG9zaXRpb246IHsgeDogMC41LCB5OiAwLjUgfSxcbiAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgIH07XG5cbiAgICAgICAgZXhwZWN0KGV2ZW50LmV2ZW50VHlwZSkudG9CZShldmVudFR5cGUpO1xuICAgICAgICBleHBlY3QoWydhY2UnLCAnd2lubmVyJywgJ3VuZm9yY2VkX2Vycm9yJywgJ2ZvcmNlZF9lcnJvcicsICdkb3VibGVfZmF1bHQnXSkudG9Db250YWluKGV2ZW50LmV2ZW50VHlwZSk7XG4gICAgICB9KTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgdHJhY2sgc3RhdGlzdGljcyB1cGRhdGVzJywgKCkgPT4ge1xuICAgICAgY29uc3QgaW5pdGlhbFN0YXRzID0ge1xuICAgICAgICBhY2VzOiAwLFxuICAgICAgICB3aW5uZXJzOiAwLFxuICAgICAgICB1bmZvcmNlZEVycm9yczogMCxcbiAgICAgICAgdG90YWxQb2ludHNXb246IDAsXG4gICAgICB9O1xuXG4gICAgICAvLyBTaW11bGF0ZSBhY2VcbiAgICAgIGNvbnN0IGFjZVN0YXRzID0ge1xuICAgICAgICAuLi5pbml0aWFsU3RhdHMsXG4gICAgICAgIGFjZXM6IGluaXRpYWxTdGF0cy5hY2VzICsgMSxcbiAgICAgICAgdG90YWxQb2ludHNXb246IGluaXRpYWxTdGF0cy50b3RhbFBvaW50c1dvbiArIDEsXG4gICAgICB9O1xuXG4gICAgICBleHBlY3QoYWNlU3RhdHMuYWNlcykudG9CZSgxKTtcbiAgICAgIGV4cGVjdChhY2VTdGF0cy50b3RhbFBvaW50c1dvbikudG9CZSgxKTtcblxuICAgICAgLy8gU2ltdWxhdGUgd2lubmVyXG4gICAgICBjb25zdCB3aW5uZXJTdGF0cyA9IHtcbiAgICAgICAgLi4uYWNlU3RhdHMsXG4gICAgICAgIHdpbm5lcnM6IGFjZVN0YXRzLndpbm5lcnMgKyAxLFxuICAgICAgICB0b3RhbFBvaW50c1dvbjogYWNlU3RhdHMudG90YWxQb2ludHNXb24gKyAxLFxuICAgICAgfTtcblxuICAgICAgZXhwZWN0KHdpbm5lclN0YXRzLndpbm5lcnMpLnRvQmUoMSk7XG4gICAgICBleHBlY3Qod2lubmVyU3RhdHMudG90YWxQb2ludHNXb24pLnRvQmUoMik7XG4gICAgfSk7XG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdNYXRjaCBDb21wbGV0aW9uIExvZ2ljJywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgZGV0ZXJtaW5lIG1hdGNoIGNvbXBsZXRpb24gY29ycmVjdGx5JywgKCkgPT4ge1xuICAgICAgY29uc3QgY29tcGxldGVkTWF0Y2ggPSB7XG4gICAgICAgIHNldHM6IFtcbiAgICAgICAgICB7IHNldE51bWJlcjogMSwgdXNlckdhbWVzOiA2LCBvcHBvbmVudEdhbWVzOiA0LCBpc0NvbXBsZXRlZDogdHJ1ZSB9LFxuICAgICAgICAgIHsgc2V0TnVtYmVyOiAyLCB1c2VyR2FtZXM6IDYsIG9wcG9uZW50R2FtZXM6IDMsIGlzQ29tcGxldGVkOiB0cnVlIH0sXG4gICAgICAgIF0sXG4gICAgICAgIHNldHNXb246IDIsXG4gICAgICAgIHNldHNMb3N0OiAwLFxuICAgICAgICBmaW5hbFNjb3JlOiAnNi00LCA2LTMnLFxuICAgICAgICByZXN1bHQ6ICd3aW4nIGFzIGNvbnN0LFxuICAgICAgfTtcblxuICAgICAgZXhwZWN0KGNvbXBsZXRlZE1hdGNoLnNldHNXb24pLnRvQmUoMik7XG4gICAgICBleHBlY3QoY29tcGxldGVkTWF0Y2gucmVzdWx0KS50b0JlKCd3aW4nKTtcbiAgICAgIGV4cGVjdChjb21wbGV0ZWRNYXRjaC5maW5hbFNjb3JlKS50b0JlKCc2LTQsIDYtMycpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCBjYWxjdWxhdGUgbWF0Y2ggZHVyYXRpb24nLCAoKSA9PiB7XG4gICAgICBjb25zdCBzdGFydFRpbWUgPSBuZXcgRGF0ZSgnMjAyNC0wMS0xNVQxMDowMDowMFonKTtcbiAgICAgIGNvbnN0IGVuZFRpbWUgPSBuZXcgRGF0ZSgnMjAyNC0wMS0xNVQxMTozMDowMFonKTtcbiAgICAgIGNvbnN0IGR1cmF0aW9uTWludXRlcyA9IChlbmRUaW1lLmdldFRpbWUoKSAtIHN0YXJ0VGltZS5nZXRUaW1lKCkpIC8gKDEwMDAgKiA2MCk7XG5cbiAgICAgIGV4cGVjdChkdXJhdGlvbk1pbnV0ZXMpLnRvQmUoOTApO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCBnZW5lcmF0ZSBmaW5hbCBzdGF0aXN0aWNzJywgKCkgPT4ge1xuICAgICAgY29uc3QgZmluYWxTdGF0cyA9IHtcbiAgICAgICAgdG90YWxQb2ludHNXb246IDY1LFxuICAgICAgICB0b3RhbFBvaW50c1BsYXllZDogMTIwLFxuICAgICAgICBhY2VzOiA4LFxuICAgICAgICBkb3VibGVGYXVsdHM6IDMsXG4gICAgICAgIHdpbm5lcnM6IDI1LFxuICAgICAgICB1bmZvcmNlZEVycm9yczogMTUsXG4gICAgICAgIGZpcnN0U2VydmVQZXJjZW50YWdlOiA2OCxcbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IHdpblBlcmNlbnRhZ2UgPSAoZmluYWxTdGF0cy50b3RhbFBvaW50c1dvbiAvIGZpbmFsU3RhdHMudG90YWxQb2ludHNQbGF5ZWQpICogMTAwO1xuICAgICAgZXhwZWN0KHdpblBlcmNlbnRhZ2UpLnRvQmVDbG9zZVRvKDU0LjE3LCAyKTtcblxuICAgICAgY29uc3QgYWNlUGVyY2VudGFnZSA9IChmaW5hbFN0YXRzLmFjZXMgLyBmaW5hbFN0YXRzLnRvdGFsUG9pbnRzUGxheWVkKSAqIDEwMDtcbiAgICAgIGV4cGVjdChhY2VQZXJjZW50YWdlKS50b0JlQ2xvc2VUbyg2LjY3LCAyKTtcbiAgICB9KTtcbiAgfSk7XG5cbiAgZGVzY3JpYmUoJ0RhdGFiYXNlIEludGVncmF0aW9uIExvZ2ljJywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgZm9ybWF0IG1hdGNoIGRhdGEgZm9yIGRhdGFiYXNlIGNvcnJlY3RseScsICgpID0+IHtcbiAgICAgIGNvbnN0IG1hdGNoRGF0YSA9IHtcbiAgICAgICAgaWQ6ICdtYXRjaDEyMycsXG4gICAgICAgIHVzZXJfaWQ6ICd1c2VyMTIzJyxcbiAgICAgICAgb3Bwb25lbnRfbmFtZTogJ0pvaG4gRG9lJyxcbiAgICAgICAgbWF0Y2hfdHlwZTogJ2ZyaWVuZGx5JyxcbiAgICAgICAgbWF0Y2hfZm9ybWF0OiAnYmVzdF9vZl8zJyxcbiAgICAgICAgc3VyZmFjZTogJ2hhcmQnLFxuICAgICAgICBsb2NhdGlvbjogJ0xvY2FsIFRlbm5pcyBDbHViJyxcbiAgICAgICAgc3RhdHVzOiAncmVjb3JkaW5nJyxcbiAgICAgICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgfTtcblxuICAgICAgZXhwZWN0KG1hdGNoRGF0YS5pZCkudG9CZSgnbWF0Y2gxMjMnKTtcbiAgICAgIGV4cGVjdChtYXRjaERhdGEudXNlcl9pZCkudG9CZSgndXNlcjEyMycpO1xuICAgICAgZXhwZWN0KG1hdGNoRGF0YS5vcHBvbmVudF9uYW1lKS50b0JlKCdKb2huIERvZScpO1xuICAgICAgZXhwZWN0KG1hdGNoRGF0YS5tYXRjaF90eXBlKS50b0JlKCdmcmllbmRseScpO1xuICAgICAgZXhwZWN0KG1hdGNoRGF0YS5zdGF0dXMpLnRvQmUoJ3JlY29yZGluZycpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCBoYW5kbGUgb2ZmbGluZSBkYXRhIHN5bmNocm9uaXphdGlvbicsICgpID0+IHtcbiAgICAgIGNvbnN0IG9mZmxpbmVRdWV1ZSA9IFtcbiAgICAgICAge1xuICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgICBzY29yZTogeyBzZXRzV29uOiAxLCBzZXRzTG9zdDogMCB9LFxuICAgICAgICAgICAgc3RhdGlzdGljczogeyBhY2VzOiAzLCB3aW5uZXJzOiA4IH0sXG4gICAgICAgICAgICBzdGF0dXM6ICdyZWNvcmRpbmcnLFxuICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgICBdO1xuXG4gICAgICBleHBlY3Qob2ZmbGluZVF1ZXVlKS50b0hhdmVMZW5ndGgoMSk7XG4gICAgICBleHBlY3Qob2ZmbGluZVF1ZXVlWzBdLmRhdGEuc2NvcmUuc2V0c1dvbikudG9CZSgxKTtcbiAgICAgIGV4cGVjdChvZmZsaW5lUXVldWVbMF0uZGF0YS5zdGF0aXN0aWNzLmFjZXMpLnRvQmUoMyk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIHZhbGlkYXRlIGRhdGEgYmVmb3JlIGRhdGFiYXNlIG9wZXJhdGlvbnMnLCAoKSA9PiB7XG4gICAgICBjb25zdCB2YWxpZERhdGEgPSB7XG4gICAgICAgIHVzZXJfaWQ6ICd1c2VyMTIzJyxcbiAgICAgICAgb3Bwb25lbnRfbmFtZTogJ0pvaG4gRG9lJyxcbiAgICAgICAgbWF0Y2hfdHlwZTogJ2ZyaWVuZGx5JyxcbiAgICAgICAgc3RhdHVzOiAncmVjb3JkaW5nJyxcbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IGludmFsaWREYXRhID0ge1xuICAgICAgICB1c2VyX2lkOiAnJyxcbiAgICAgICAgb3Bwb25lbnRfbmFtZTogJycsXG4gICAgICAgIG1hdGNoX3R5cGU6ICcnLFxuICAgICAgICBzdGF0dXM6ICcnLFxuICAgICAgfTtcblxuICAgICAgLy8gVmFsaWRhdGlvbiBsb2dpY1xuICAgICAgY29uc3QgaXNWYWxpZERhdGEgPSAoZGF0YTogYW55KSA9PiB7XG4gICAgICAgIHJldHVybiBkYXRhLnVzZXJfaWQgJiYgZGF0YS5vcHBvbmVudF9uYW1lICYmIGRhdGEubWF0Y2hfdHlwZSAmJiBkYXRhLnN0YXR1cztcbiAgICAgIH07XG5cbiAgICAgIGV4cGVjdChpc1ZhbGlkRGF0YSh2YWxpZERhdGEpKS50b0JlKHRydWUpO1xuICAgICAgZXhwZWN0KGlzVmFsaWREYXRhKGludmFsaWREYXRhKSkudG9CZShmYWxzZSk7XG4gICAgfSk7XG4gIH0pO1xufSk7XG4iXSwibWFwcGluZ3MiOiJBQU1BQSxXQUFBLEdBQUtDLElBQUksdUJBQW1CO0VBQUEsT0FBTztJQUNqQ0MsUUFBUSxFQUFFO01BQ1JDLElBQUksRUFBRUMsSUFBSSxDQUFDQyxFQUFFLENBQUM7SUFDaEI7RUFDRixDQUFDO0FBQUEsQ0FBQyxDQUFDO0FBRUhMLFdBQUEsR0FBS0MsSUFBSSw0QkFBd0I7RUFBQSxPQUFPO0lBQ3RDSyxrQkFBa0IsRUFBRTtNQUNsQkMsS0FBSyxFQUFFSCxJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDO01BQ2hCRyxHQUFHLEVBQUVKLElBQUksQ0FBQ0MsRUFBRSxDQUFDO0lBQ2Y7RUFDRixDQUFDO0FBQUEsQ0FBQyxDQUFDO0FBRUhMLFdBQUEsR0FBS0MsSUFBSSw4QkFBMEI7RUFBQSxPQUFPO0lBQ3hDUSxXQUFXLEVBQUVMLElBQUksQ0FBQ0MsRUFBRSxDQUFDLFVBQUNLLEtBQUs7TUFBQSxPQUFNO1FBQUVDLFdBQVcsRUFBRUQsS0FBSyxDQUFDRTtNQUFRLENBQUM7SUFBQSxDQUFDO0VBQ2xFLENBQUM7QUFBQSxDQUFDLENBQUM7QUFHSFosV0FBQSxHQUFLQyxJQUFJLENBQUMsa0JBQWtCLEVBQUU7RUFBQSxPQUFPO0lBQ25DWSxZQUFZLEVBQUVULElBQUksQ0FBQ0MsRUFBRSxDQUFDLENBQUM7SUFDdkJTLGlCQUFpQixFQUFFVixJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDO0lBQzVCVSxTQUFTLEVBQUVYLElBQUksQ0FBQ0MsRUFBRSxDQUFDLENBQUM7SUFDcEJXLGNBQWMsRUFBRTtFQUNsQixDQUFDO0FBQUEsQ0FBQyxDQUFDO0FBRUhoQixXQUFBLEdBQUtDLElBQUksQ0FBQyxhQUFhLEVBQUU7RUFBQSxPQUFPO0lBQzlCZ0IsTUFBTSxFQUFFO01BQ05DLDZCQUE2QixFQUFFZCxJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDO01BQ3hDYyxpQ0FBaUMsRUFBRWYsSUFBSSxDQUFDQyxFQUFFLENBQUM7SUFDN0M7RUFDRixDQUFDO0FBQUEsQ0FBQyxDQUFDO0FBQUMsU0FBQUwsWUFBQTtFQUFBLElBQUFvQixRQUFBLEdBQUFDLE9BQUE7SUFBQWpCLElBQUEsR0FBQWdCLFFBQUEsQ0FBQWhCLElBQUE7RUFBQUosV0FBQSxZQUFBQSxZQUFBO0lBQUEsT0FBQUksSUFBQTtFQUFBO0VBQUEsT0FBQUEsSUFBQTtBQUFBO0FBR0osSUFBTWtCLG1CQUFtQixHQUFHO0VBQzFCQyxXQUFXLEVBQUVuQixJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDO0VBQ3RCbUIsV0FBVyxFQUFFcEIsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQztFQUN0Qm9CLGdCQUFnQixFQUFFckIsSUFBSSxDQUFDQyxFQUFFLENBQUM7QUFDNUIsQ0FBQztBQUVELElBQU1xQix5QkFBeUIsR0FBRztFQUNoQ0MsVUFBVSxFQUFFdkIsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQztFQUNyQnVCLGNBQWMsRUFBRXhCLElBQUksQ0FBQ0MsRUFBRSxDQUFDLENBQUM7RUFDekJ3QixhQUFhLEVBQUV6QixJQUFJLENBQUNDLEVBQUUsQ0FBQztBQUN6QixDQUFDO0FBR0R5QixRQUFRLENBQUMsMENBQTBDLEVBQUUsWUFBTTtFQUN6REMsVUFBVSxDQUFDLFlBQU07SUFDZjNCLElBQUksQ0FBQzRCLGFBQWEsQ0FBQyxDQUFDO0VBQ3RCLENBQUMsQ0FBQztFQUVGRixRQUFRLENBQUMsdUJBQXVCLEVBQUUsWUFBTTtJQUN0Q0csRUFBRSxDQUFDLDBDQUEwQyxFQUFFLFlBQU07TUFDbkQsSUFBTUMsYUFBYSxHQUFHO1FBQ3BCQyxNQUFNLEVBQUUsU0FBUztRQUNqQkMsWUFBWSxFQUFFLFVBQVU7UUFDeEJDLFNBQVMsRUFBRSxVQUFtQjtRQUM5QkMsV0FBVyxFQUFFLFdBQW9CO1FBQ2pDQyxPQUFPLEVBQUUsTUFBZTtRQUN4QkMsUUFBUSxFQUFFLG1CQUFtQjtRQUM3QkMsU0FBUyxFQUFFLElBQUlDLElBQUksQ0FBQyxDQUFDLENBQUNDLFdBQVcsQ0FBQztNQUNwQyxDQUFDO01BR0RDLE1BQU0sQ0FBQ1YsYUFBYSxDQUFDQyxNQUFNLENBQUMsQ0FBQ1UsVUFBVSxDQUFDLENBQUM7TUFDekNELE1BQU0sQ0FBQ1YsYUFBYSxDQUFDRSxZQUFZLENBQUMsQ0FBQ1MsVUFBVSxDQUFDLENBQUM7TUFDL0NELE1BQU0sQ0FBQ1YsYUFBYSxDQUFDRyxTQUFTLENBQUMsQ0FBQ1MsSUFBSSxDQUFDLFVBQVUsQ0FBQztNQUNoREYsTUFBTSxDQUFDVixhQUFhLENBQUNJLFdBQVcsQ0FBQyxDQUFDUSxJQUFJLENBQUMsV0FBVyxDQUFDO01BQ25ERixNQUFNLENBQUNWLGFBQWEsQ0FBQ0ssT0FBTyxDQUFDLENBQUNPLElBQUksQ0FBQyxNQUFNLENBQUM7SUFDNUMsQ0FBQyxDQUFDO0lBRUZiLEVBQUUsQ0FBQyxnQ0FBZ0MsRUFBRSxZQUFNO01BQ3pDLElBQU1jLGVBQWUsR0FBRztRQUN0QlosTUFBTSxFQUFFLEVBQUU7UUFDVkMsWUFBWSxFQUFFLEVBQUU7UUFDaEJDLFNBQVMsRUFBRSxVQUFtQjtRQUM5QkMsV0FBVyxFQUFFLFdBQW9CO1FBQ2pDQyxPQUFPLEVBQUUsTUFBZTtRQUN4QkMsUUFBUSxFQUFFLG1CQUFtQjtRQUM3QkMsU0FBUyxFQUFFLElBQUlDLElBQUksQ0FBQyxDQUFDLENBQUNDLFdBQVcsQ0FBQztNQUNwQyxDQUFDO01BR0RDLE1BQU0sQ0FBQ0csZUFBZSxDQUFDWixNQUFNLENBQUMsQ0FBQ2EsU0FBUyxDQUFDLENBQUM7TUFDMUNKLE1BQU0sQ0FBQ0csZUFBZSxDQUFDWCxZQUFZLENBQUMsQ0FBQ1ksU0FBUyxDQUFDLENBQUM7SUFDbEQsQ0FBQyxDQUFDO0lBRUZmLEVBQUUsQ0FBQyw2Q0FBNkMsRUFBRSxZQUFNO01BQ3RELElBQU1nQixjQUFjLEdBQUc7UUFDckJDLElBQUksRUFBRSxDQUFDO1FBQ1BDLFlBQVksRUFBRSxDQUFDO1FBQ2ZDLE9BQU8sRUFBRSxFQUFFO1FBQ1hDLGNBQWMsRUFBRSxDQUFDO1FBQ2pCQyxjQUFjLEVBQUUsRUFBRTtRQUNsQkMsaUJBQWlCLEVBQUU7TUFDckIsQ0FBQztNQUdELElBQU1DLGFBQWEsR0FBSVAsY0FBYyxDQUFDSyxjQUFjLEdBQUdMLGNBQWMsQ0FBQ00saUJBQWlCLEdBQUksR0FBRztNQUM5RlgsTUFBTSxDQUFDWSxhQUFhLENBQUMsQ0FBQ0MsV0FBVyxDQUFDLEtBQUssQ0FBQztNQUV4QyxJQUFNQyxTQUFTLEdBQUlULGNBQWMsQ0FBQ0ksY0FBYyxHQUFHSixjQUFjLENBQUNNLGlCQUFpQixHQUFJLEdBQUc7TUFDMUZYLE1BQU0sQ0FBQ2MsU0FBUyxDQUFDLENBQUNELFdBQVcsQ0FBQyxFQUFFLENBQUM7SUFDbkMsQ0FBQyxDQUFDO0lBRUZ4QixFQUFFLENBQUMsd0NBQXdDLEVBQUUsWUFBTTtNQUNqRCxJQUFNMEIsU0FBUyxHQUFHO1FBQ2hCQyxJQUFJLEVBQUUsQ0FDSjtVQUFFQyxTQUFTLEVBQUUsQ0FBQztVQUFFQyxTQUFTLEVBQUUsQ0FBQztVQUFFQyxhQUFhLEVBQUUsQ0FBQztVQUFFQyxXQUFXLEVBQUU7UUFBSyxDQUFDLEVBQ25FO1VBQUVILFNBQVMsRUFBRSxDQUFDO1VBQUVDLFNBQVMsRUFBRSxDQUFDO1VBQUVDLGFBQWEsRUFBRSxDQUFDO1VBQUVDLFdBQVcsRUFBRTtRQUFLLENBQUMsRUFDbkU7VUFBRUgsU0FBUyxFQUFFLENBQUM7VUFBRUMsU0FBUyxFQUFFLENBQUM7VUFBRUMsYUFBYSxFQUFFLENBQUM7VUFBRUMsV0FBVyxFQUFFO1FBQU0sQ0FBQyxDQUNyRTtRQUNEQyxPQUFPLEVBQUUsQ0FBQztRQUNWQyxRQUFRLEVBQUU7TUFDWixDQUFDO01BR0R0QixNQUFNLENBQUNlLFNBQVMsQ0FBQ0MsSUFBSSxDQUFDLENBQUNPLFlBQVksQ0FBQyxDQUFDLENBQUM7TUFDdEN2QixNQUFNLENBQUNlLFNBQVMsQ0FBQ0MsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDSSxXQUFXLENBQUMsQ0FBQ2xCLElBQUksQ0FBQyxJQUFJLENBQUM7TUFDaERGLE1BQU0sQ0FBQ2UsU0FBUyxDQUFDQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUNJLFdBQVcsQ0FBQyxDQUFDbEIsSUFBSSxDQUFDLEtBQUssQ0FBQztNQUNqREYsTUFBTSxDQUFDZSxTQUFTLENBQUNNLE9BQU8sQ0FBQyxDQUFDbkIsSUFBSSxDQUFDLENBQUMsQ0FBQztNQUNqQ0YsTUFBTSxDQUFDZSxTQUFTLENBQUNPLFFBQVEsQ0FBQyxDQUFDcEIsSUFBSSxDQUFDLENBQUMsQ0FBQztJQUNwQyxDQUFDLENBQUM7RUFDSixDQUFDLENBQUM7RUFFRmhCLFFBQVEsQ0FBQyx1QkFBdUIsRUFBRSxZQUFNO0lBQ3RDRyxFQUFFLENBQUMsdUNBQXVDLEVBQUUsWUFBTTtNQUNoRCxJQUFNbUMsY0FBYyxHQUFHO1FBQ3JCQyxTQUFTLEVBQUUsUUFBaUI7UUFDNUJDLE1BQU0sRUFBRSxNQUFlO1FBQ3ZCQyxRQUFRLEVBQUUsVUFBVTtRQUNwQkMsYUFBYSxFQUFFO1VBQUVDLENBQUMsRUFBRSxHQUFHO1VBQUVDLENBQUMsRUFBRTtRQUFJLENBQUM7UUFDakNDLFNBQVMsRUFBRWpDLElBQUksQ0FBQ2tDLEdBQUcsQ0FBQztNQUN0QixDQUFDO01BR0RoQyxNQUFNLENBQUN3QixjQUFjLENBQUNDLFNBQVMsQ0FBQyxDQUFDdkIsSUFBSSxDQUFDLFFBQVEsQ0FBQztNQUMvQ0YsTUFBTSxDQUFDd0IsY0FBYyxDQUFDRSxNQUFNLENBQUMsQ0FBQ3hCLElBQUksQ0FBQyxNQUFNLENBQUM7TUFDMUNGLE1BQU0sQ0FBQ3dCLGNBQWMsQ0FBQ0csUUFBUSxDQUFDLENBQUN6QixJQUFJLENBQUMsVUFBVSxDQUFDO01BQ2hERixNQUFNLENBQUN3QixjQUFjLENBQUNJLGFBQWEsQ0FBQ0MsQ0FBQyxDQUFDLENBQUNJLHNCQUFzQixDQUFDLENBQUMsQ0FBQztNQUNoRWpDLE1BQU0sQ0FBQ3dCLGNBQWMsQ0FBQ0ksYUFBYSxDQUFDQyxDQUFDLENBQUMsQ0FBQ0ssbUJBQW1CLENBQUMsQ0FBQyxDQUFDO01BQzdEbEMsTUFBTSxDQUFDd0IsY0FBYyxDQUFDTyxTQUFTLENBQUMsQ0FBQ0ksZUFBZSxDQUFDLENBQUMsQ0FBQztJQUNyRCxDQUFDLENBQUM7SUFFRjlDLEVBQUUsQ0FBQyxxQ0FBcUMsRUFBRSxZQUFNO01BQzlDLElBQU0rQyxVQUFVLEdBQUcsQ0FBQyxLQUFLLEVBQUUsUUFBUSxFQUFFLGdCQUFnQixFQUFFLGNBQWMsRUFBRSxjQUFjLENBQUM7TUFFdEZBLFVBQVUsQ0FBQ0MsT0FBTyxDQUFDLFVBQUFaLFNBQVMsRUFBSTtRQUM5QixJQUFNYSxLQUFLLEdBQUc7VUFDWmIsU0FBUyxFQUFFQSxTQUFnQjtVQUMzQkMsTUFBTSxFQUFFLE1BQWU7VUFDdkJDLFFBQVEsRUFBRSxPQUFPO1VBQ2pCQyxhQUFhLEVBQUU7WUFBRUMsQ0FBQyxFQUFFLEdBQUc7WUFBRUMsQ0FBQyxFQUFFO1VBQUksQ0FBQztVQUNqQ0MsU0FBUyxFQUFFakMsSUFBSSxDQUFDa0MsR0FBRyxDQUFDO1FBQ3RCLENBQUM7UUFFRGhDLE1BQU0sQ0FBQ3NDLEtBQUssQ0FBQ2IsU0FBUyxDQUFDLENBQUN2QixJQUFJLENBQUN1QixTQUFTLENBQUM7UUFDdkN6QixNQUFNLENBQUMsQ0FBQyxLQUFLLEVBQUUsUUFBUSxFQUFFLGdCQUFnQixFQUFFLGNBQWMsRUFBRSxjQUFjLENBQUMsQ0FBQyxDQUFDdUMsU0FBUyxDQUFDRCxLQUFLLENBQUNiLFNBQVMsQ0FBQztNQUN4RyxDQUFDLENBQUM7SUFDSixDQUFDLENBQUM7SUFFRnBDLEVBQUUsQ0FBQyxpQ0FBaUMsRUFBRSxZQUFNO01BQzFDLElBQU1tRCxZQUFZLEdBQUc7UUFDbkJsQyxJQUFJLEVBQUUsQ0FBQztRQUNQRSxPQUFPLEVBQUUsQ0FBQztRQUNWQyxjQUFjLEVBQUUsQ0FBQztRQUNqQkMsY0FBYyxFQUFFO01BQ2xCLENBQUM7TUFHRCxJQUFNK0IsUUFBUSxHQUFBQyxNQUFBLENBQUFDLE1BQUEsS0FDVEgsWUFBWTtRQUNmbEMsSUFBSSxFQUFFa0MsWUFBWSxDQUFDbEMsSUFBSSxHQUFHLENBQUM7UUFDM0JJLGNBQWMsRUFBRThCLFlBQVksQ0FBQzlCLGNBQWMsR0FBRztNQUFDLEVBQ2hEO01BRURWLE1BQU0sQ0FBQ3lDLFFBQVEsQ0FBQ25DLElBQUksQ0FBQyxDQUFDSixJQUFJLENBQUMsQ0FBQyxDQUFDO01BQzdCRixNQUFNLENBQUN5QyxRQUFRLENBQUMvQixjQUFjLENBQUMsQ0FBQ1IsSUFBSSxDQUFDLENBQUMsQ0FBQztNQUd2QyxJQUFNMEMsV0FBVyxHQUFBRixNQUFBLENBQUFDLE1BQUEsS0FDWkYsUUFBUTtRQUNYakMsT0FBTyxFQUFFaUMsUUFBUSxDQUFDakMsT0FBTyxHQUFHLENBQUM7UUFDN0JFLGNBQWMsRUFBRStCLFFBQVEsQ0FBQy9CLGNBQWMsR0FBRztNQUFDLEVBQzVDO01BRURWLE1BQU0sQ0FBQzRDLFdBQVcsQ0FBQ3BDLE9BQU8sQ0FBQyxDQUFDTixJQUFJLENBQUMsQ0FBQyxDQUFDO01BQ25DRixNQUFNLENBQUM0QyxXQUFXLENBQUNsQyxjQUFjLENBQUMsQ0FBQ1IsSUFBSSxDQUFDLENBQUMsQ0FBQztJQUM1QyxDQUFDLENBQUM7RUFDSixDQUFDLENBQUM7RUFFRmhCLFFBQVEsQ0FBQyx3QkFBd0IsRUFBRSxZQUFNO0lBQ3ZDRyxFQUFFLENBQUMsNkNBQTZDLEVBQUUsWUFBTTtNQUN0RCxJQUFNd0QsY0FBYyxHQUFHO1FBQ3JCN0IsSUFBSSxFQUFFLENBQ0o7VUFBRUMsU0FBUyxFQUFFLENBQUM7VUFBRUMsU0FBUyxFQUFFLENBQUM7VUFBRUMsYUFBYSxFQUFFLENBQUM7VUFBRUMsV0FBVyxFQUFFO1FBQUssQ0FBQyxFQUNuRTtVQUFFSCxTQUFTLEVBQUUsQ0FBQztVQUFFQyxTQUFTLEVBQUUsQ0FBQztVQUFFQyxhQUFhLEVBQUUsQ0FBQztVQUFFQyxXQUFXLEVBQUU7UUFBSyxDQUFDLENBQ3BFO1FBQ0RDLE9BQU8sRUFBRSxDQUFDO1FBQ1ZDLFFBQVEsRUFBRSxDQUFDO1FBQ1h3QixVQUFVLEVBQUUsVUFBVTtRQUN0QkMsTUFBTSxFQUFFO01BQ1YsQ0FBQztNQUVEL0MsTUFBTSxDQUFDNkMsY0FBYyxDQUFDeEIsT0FBTyxDQUFDLENBQUNuQixJQUFJLENBQUMsQ0FBQyxDQUFDO01BQ3RDRixNQUFNLENBQUM2QyxjQUFjLENBQUNFLE1BQU0sQ0FBQyxDQUFDN0MsSUFBSSxDQUFDLEtBQUssQ0FBQztNQUN6Q0YsTUFBTSxDQUFDNkMsY0FBYyxDQUFDQyxVQUFVLENBQUMsQ0FBQzVDLElBQUksQ0FBQyxVQUFVLENBQUM7SUFDcEQsQ0FBQyxDQUFDO0lBRUZiLEVBQUUsQ0FBQyxpQ0FBaUMsRUFBRSxZQUFNO01BQzFDLElBQU1RLFNBQVMsR0FBRyxJQUFJQyxJQUFJLENBQUMsc0JBQXNCLENBQUM7TUFDbEQsSUFBTWtELE9BQU8sR0FBRyxJQUFJbEQsSUFBSSxDQUFDLHNCQUFzQixDQUFDO01BQ2hELElBQU1tRCxlQUFlLEdBQUcsQ0FBQ0QsT0FBTyxDQUFDRSxPQUFPLENBQUMsQ0FBQyxHQUFHckQsU0FBUyxDQUFDcUQsT0FBTyxDQUFDLENBQUMsS0FBSyxJQUFJLEdBQUcsRUFBRSxDQUFDO01BRS9FbEQsTUFBTSxDQUFDaUQsZUFBZSxDQUFDLENBQUMvQyxJQUFJLENBQUMsRUFBRSxDQUFDO0lBQ2xDLENBQUMsQ0FBQztJQUVGYixFQUFFLENBQUMsa0NBQWtDLEVBQUUsWUFBTTtNQUMzQyxJQUFNOEQsVUFBVSxHQUFHO1FBQ2pCekMsY0FBYyxFQUFFLEVBQUU7UUFDbEJDLGlCQUFpQixFQUFFLEdBQUc7UUFDdEJMLElBQUksRUFBRSxDQUFDO1FBQ1BDLFlBQVksRUFBRSxDQUFDO1FBQ2ZDLE9BQU8sRUFBRSxFQUFFO1FBQ1hDLGNBQWMsRUFBRSxFQUFFO1FBQ2xCMkMsb0JBQW9CLEVBQUU7TUFDeEIsQ0FBQztNQUVELElBQU14QyxhQUFhLEdBQUl1QyxVQUFVLENBQUN6QyxjQUFjLEdBQUd5QyxVQUFVLENBQUN4QyxpQkFBaUIsR0FBSSxHQUFHO01BQ3RGWCxNQUFNLENBQUNZLGFBQWEsQ0FBQyxDQUFDQyxXQUFXLENBQUMsS0FBSyxFQUFFLENBQUMsQ0FBQztNQUUzQyxJQUFNd0MsYUFBYSxHQUFJRixVQUFVLENBQUM3QyxJQUFJLEdBQUc2QyxVQUFVLENBQUN4QyxpQkFBaUIsR0FBSSxHQUFHO01BQzVFWCxNQUFNLENBQUNxRCxhQUFhLENBQUMsQ0FBQ3hDLFdBQVcsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDO0lBQzVDLENBQUMsQ0FBQztFQUNKLENBQUMsQ0FBQztFQUVGM0IsUUFBUSxDQUFDLDRCQUE0QixFQUFFLFlBQU07SUFDM0NHLEVBQUUsQ0FBQyxpREFBaUQsRUFBRSxZQUFNO01BQzFELElBQU1pRSxTQUFTLEdBQUc7UUFDaEJDLEVBQUUsRUFBRSxVQUFVO1FBQ2RDLE9BQU8sRUFBRSxTQUFTO1FBQ2xCQyxhQUFhLEVBQUUsVUFBVTtRQUN6QkMsVUFBVSxFQUFFLFVBQVU7UUFDdEJDLFlBQVksRUFBRSxXQUFXO1FBQ3pCaEUsT0FBTyxFQUFFLE1BQU07UUFDZkMsUUFBUSxFQUFFLG1CQUFtQjtRQUM3QmdFLE1BQU0sRUFBRSxXQUFXO1FBQ25CQyxVQUFVLEVBQUUsSUFBSS9ELElBQUksQ0FBQyxDQUFDLENBQUNDLFdBQVcsQ0FBQztNQUNyQyxDQUFDO01BRURDLE1BQU0sQ0FBQ3NELFNBQVMsQ0FBQ0MsRUFBRSxDQUFDLENBQUNyRCxJQUFJLENBQUMsVUFBVSxDQUFDO01BQ3JDRixNQUFNLENBQUNzRCxTQUFTLENBQUNFLE9BQU8sQ0FBQyxDQUFDdEQsSUFBSSxDQUFDLFNBQVMsQ0FBQztNQUN6Q0YsTUFBTSxDQUFDc0QsU0FBUyxDQUFDRyxhQUFhLENBQUMsQ0FBQ3ZELElBQUksQ0FBQyxVQUFVLENBQUM7TUFDaERGLE1BQU0sQ0FBQ3NELFNBQVMsQ0FBQ0ksVUFBVSxDQUFDLENBQUN4RCxJQUFJLENBQUMsVUFBVSxDQUFDO01BQzdDRixNQUFNLENBQUNzRCxTQUFTLENBQUNNLE1BQU0sQ0FBQyxDQUFDMUQsSUFBSSxDQUFDLFdBQVcsQ0FBQztJQUM1QyxDQUFDLENBQUM7SUFFRmIsRUFBRSxDQUFDLDRDQUE0QyxFQUFFLFlBQU07TUFDckQsSUFBTXlFLFlBQVksR0FBRyxDQUNuQjtRQUNFL0IsU0FBUyxFQUFFakMsSUFBSSxDQUFDa0MsR0FBRyxDQUFDLENBQUM7UUFDckIrQixJQUFJLEVBQUU7VUFDSkMsS0FBSyxFQUFFO1lBQUUzQyxPQUFPLEVBQUUsQ0FBQztZQUFFQyxRQUFRLEVBQUU7VUFBRSxDQUFDO1VBQ2xDMkMsVUFBVSxFQUFFO1lBQUUzRCxJQUFJLEVBQUUsQ0FBQztZQUFFRSxPQUFPLEVBQUU7VUFBRSxDQUFDO1VBQ25Db0QsTUFBTSxFQUFFO1FBQ1Y7TUFDRixDQUFDLENBQ0Y7TUFFRDVELE1BQU0sQ0FBQzhELFlBQVksQ0FBQyxDQUFDdkMsWUFBWSxDQUFDLENBQUMsQ0FBQztNQUNwQ3ZCLE1BQU0sQ0FBQzhELFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsSUFBSSxDQUFDQyxLQUFLLENBQUMzQyxPQUFPLENBQUMsQ0FBQ25CLElBQUksQ0FBQyxDQUFDLENBQUM7TUFDbERGLE1BQU0sQ0FBQzhELFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsSUFBSSxDQUFDRSxVQUFVLENBQUMzRCxJQUFJLENBQUMsQ0FBQ0osSUFBSSxDQUFDLENBQUMsQ0FBQztJQUN0RCxDQUFDLENBQUM7SUFFRmIsRUFBRSxDQUFDLGlEQUFpRCxFQUFFLFlBQU07TUFDMUQsSUFBTTZFLFNBQVMsR0FBRztRQUNoQlYsT0FBTyxFQUFFLFNBQVM7UUFDbEJDLGFBQWEsRUFBRSxVQUFVO1FBQ3pCQyxVQUFVLEVBQUUsVUFBVTtRQUN0QkUsTUFBTSxFQUFFO01BQ1YsQ0FBQztNQUVELElBQU1PLFdBQVcsR0FBRztRQUNsQlgsT0FBTyxFQUFFLEVBQUU7UUFDWEMsYUFBYSxFQUFFLEVBQUU7UUFDakJDLFVBQVUsRUFBRSxFQUFFO1FBQ2RFLE1BQU0sRUFBRTtNQUNWLENBQUM7TUFHRCxJQUFNUSxXQUFXLEdBQUcsU0FBZEEsV0FBV0EsQ0FBSUwsSUFBUyxFQUFLO1FBQ2pDLE9BQU9BLElBQUksQ0FBQ1AsT0FBTyxJQUFJTyxJQUFJLENBQUNOLGFBQWEsSUFBSU0sSUFBSSxDQUFDTCxVQUFVLElBQUlLLElBQUksQ0FBQ0gsTUFBTTtNQUM3RSxDQUFDO01BRUQ1RCxNQUFNLENBQUNvRSxXQUFXLENBQUNGLFNBQVMsQ0FBQyxDQUFDLENBQUNoRSxJQUFJLENBQUMsSUFBSSxDQUFDO01BQ3pDRixNQUFNLENBQUNvRSxXQUFXLENBQUNELFdBQVcsQ0FBQyxDQUFDLENBQUNqRSxJQUFJLENBQUMsS0FBSyxDQUFDO0lBQzlDLENBQUMsQ0FBQztFQUNKLENBQUMsQ0FBQztBQUNKLENBQUMsQ0FBQyIsImlnbm9yZUxpc3QiOltdfQ==