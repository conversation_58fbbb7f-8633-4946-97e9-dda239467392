{"version": 3, "names": ["useState", "useCallback", "useEffect", "aiAnalysisService", "useAuth", "useRealtime", "useAICoaching", "cov_1yhg68p8kn", "f", "_ref", "s", "_ref2", "_slicedToArray", "liveSession", "setLiveSession", "_ref3", "_ref4", "insights", "setInsights", "_ref5", "_ref6", "isAnalyzing", "setIsAnalyzing", "_ref7", "_ref8", "recentActions", "setRecentActions", "_ref9", "user", "_ref0", "sendCoachingMessage", "startLiveCoaching", "_asyncToGenerator", "b", "sessionId", "Date", "now", "newSession", "isActive", "currentFocus", "suggestions", "encouragement", "performanceMetrics", "shotsAnalyzed", "accuracy", "consistency", "improvement", "error", "console", "stopLiveCoaching", "prev", "Object", "assign", "generateInsights", "recordAction", "_ref10", "action", "quality", "actionData", "timestamp", "concat", "_toConsumableArray", "slice", "newShotsAnalyzed", "newAccuracy", "Math", "round", "calculateConsistency", "calculateImprovement", "length", "realtimeCoaching", "generateRealTimeCoaching", "skillLevel", "adaptiveCoaching", "nextFocus", "liveAnalysis", "_x", "_x2", "apply", "arguments", "averageQuality", "reduce", "sum", "strengths", "improvements", "nextSteps", "push", "motivationalMessages", "newInsights", "motivationalMessage", "floor", "random", "getTrainingRecommendations", "recommendations", "generateTrainingRecommendations", "map", "title", "actions", "qualities", "a", "average", "q", "variance", "pow", "standardDeviation", "sqrt", "max", "min", "firstHalf", "secondHalf", "firstAvg", "secondAvg"], "sources": ["useAICoaching.ts"], "sourcesContent": ["import { useState, useCallback, useEffect } from 'react';\nimport { aiAnalysisService } from '@/services/aiAnalysis';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRealtime } from '@/hooks/useRealtime';\n\nexport interface LiveCoachingSession {\n  sessionId: string;\n  isActive: boolean;\n  currentFocus: string;\n  suggestions: string[];\n  encouragement: string;\n  performanceMetrics: {\n    shotsAnalyzed: number;\n    accuracy: number;\n    consistency: number;\n    improvement: number;\n  };\n}\n\nexport interface AICoachingInsights {\n  strengths: string[];\n  improvements: string[];\n  nextSteps: string[];\n  motivationalMessage: string;\n}\n\ninterface UseAICoachingReturn {\n  liveSession: LiveCoachingSession | null;\n  insights: AICoachingInsights | null;\n  isAnalyzing: boolean;\n  startLiveCoaching: () => Promise<void>;\n  stopLiveCoaching: () => void;\n  recordAction: (action: string, quality: number) => Promise<void>;\n  generateInsights: () => Promise<void>;\n  getTrainingRecommendations: () => Promise<any>;\n}\n\nexport function useAICoaching(): UseAICoachingReturn {\n  const [liveSession, setLiveSession] = useState<LiveCoachingSession | null>(null);\n  const [insights, setInsights] = useState<AICoachingInsights | null>(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [recentActions, setRecentActions] = useState<any[]>([]);\n  const { user } = useAuth();\n  const { sendCoachingMessage } = useRealtime();\n\n  /**\n   * Start a live AI coaching session\n   */\n  const startLiveCoaching = useCallback(async () => {\n    if (!user) return;\n\n    try {\n      setIsAnalyzing(true);\n      \n      const sessionId = `live-${Date.now()}`;\n      const newSession: LiveCoachingSession = {\n        sessionId,\n        isActive: true,\n        currentFocus: 'Getting started with your practice session',\n        suggestions: [\n          'Start with a proper warm-up',\n          'Focus on your form over power',\n          'Take your time between shots'\n        ],\n        encouragement: 'Ready to improve your game! Let\\'s start strong.',\n        performanceMetrics: {\n          shotsAnalyzed: 0,\n          accuracy: 0,\n          consistency: 0,\n          improvement: 0,\n        },\n      };\n\n      setLiveSession(newSession);\n      \n      // Send welcome coaching message\n      await sendCoachingMessage(\n        'Welcome to your AI coaching session! I\\'ll be analyzing your technique and providing real-time feedback.',\n        'encouragement'\n      );\n\n      setIsAnalyzing(false);\n    } catch (error) {\n      console.error('Error starting live coaching:', error);\n      setIsAnalyzing(false);\n    }\n  }, [user, sendCoachingMessage]);\n\n  /**\n   * Stop the live coaching session\n   */\n  const stopLiveCoaching = useCallback(() => {\n    if (liveSession) {\n      setLiveSession(prev => prev ? { ...prev, isActive: false } : null);\n      \n      // Generate final session insights\n      generateInsights();\n    }\n  }, [liveSession]);\n\n  /**\n   * Record a tennis action for real-time analysis\n   */\n  const recordAction = useCallback(async (action: string, quality: number) => {\n    if (!liveSession || !user) return;\n\n    try {\n      const actionData = {\n        action,\n        quality,\n        timestamp: Date.now(),\n        sessionId: liveSession.sessionId,\n      };\n\n      // Add to recent actions\n      setRecentActions(prev => [...prev.slice(-9), actionData]);\n\n      // Update session metrics\n      setLiveSession(prev => {\n        if (!prev) return null;\n        \n        const newShotsAnalyzed = prev.performanceMetrics.shotsAnalyzed + 1;\n        const newAccuracy = ((prev.performanceMetrics.accuracy * prev.performanceMetrics.shotsAnalyzed) + quality) / newShotsAnalyzed;\n        \n        return {\n          ...prev,\n          performanceMetrics: {\n            ...prev.performanceMetrics,\n            shotsAnalyzed: newShotsAnalyzed,\n            accuracy: Math.round(newAccuracy),\n            consistency: Math.round(calculateConsistency([...recentActions, actionData])),\n            improvement: Math.round(calculateImprovement([...recentActions, actionData])),\n          },\n        };\n      });\n\n      // Generate real-time coaching\n      if (recentActions.length >= 2) {\n        const realtimeCoaching = await aiAnalysisService.generateRealTimeCoaching(\n          action,\n          [...recentActions, actionData],\n          { skillLevel: 'intermediate' } // Would get from user profile\n        );\n\n        // Update session with new suggestions\n        setLiveSession(prev => {\n          if (!prev) return null;\n          return {\n            ...prev,\n            currentFocus: realtimeCoaching.adaptiveCoaching.nextFocus,\n            suggestions: realtimeCoaching.liveAnalysis.suggestions,\n            encouragement: realtimeCoaching.liveAnalysis.encouragement,\n          };\n        });\n\n        // Send coaching message\n        await sendCoachingMessage(\n          realtimeCoaching.liveAnalysis.encouragement,\n          'encouragement'\n        );\n\n        // Send technical suggestion if needed\n        if (quality < 70 && realtimeCoaching.liveAnalysis.suggestions.length > 0) {\n          await sendCoachingMessage(\n            realtimeCoaching.liveAnalysis.suggestions[0],\n            'correction'\n          );\n        }\n      }\n    } catch (error) {\n      console.error('Error recording action:', error);\n    }\n  }, [liveSession, user, recentActions, sendCoachingMessage]);\n\n  /**\n   * Generate comprehensive AI insights\n   */\n  const generateInsights = useCallback(async () => {\n    if (!user || recentActions.length === 0) return;\n\n    try {\n      setIsAnalyzing(true);\n\n      // Analyze recent performance\n      const averageQuality = recentActions.reduce((sum, action) => sum + action.quality, 0) / recentActions.length;\n      const consistency = calculateConsistency(recentActions);\n      const improvement = calculateImprovement(recentActions);\n\n      // Generate AI insights based on performance\n      const strengths: string[] = [];\n      const improvements: string[] = [];\n      const nextSteps: string[] = [];\n\n      if (averageQuality > 75) {\n        strengths.push('Excellent shot quality overall');\n      }\n      if (consistency > 70) {\n        strengths.push('Good consistency in technique');\n      }\n      if (improvement > 0) {\n        strengths.push('Showing improvement during session');\n      }\n\n      if (averageQuality < 60) {\n        improvements.push('Focus on shot accuracy and placement');\n        nextSteps.push('Practice basic technique drills');\n      }\n      if (consistency < 50) {\n        improvements.push('Work on consistency between shots');\n        nextSteps.push('Slow down and focus on form');\n      }\n\n      const motivationalMessages = [\n        'Great work today! Keep practicing and you\\'ll see continued improvement.',\n        'Your dedication is showing in your technique. Keep it up!',\n        'Every practice session makes you better. Stay focused on your goals.',\n        'Excellent effort! Remember, consistency beats perfection.',\n      ];\n\n      const newInsights: AICoachingInsights = {\n        strengths: strengths.length > 0 ? strengths : ['Good effort and focus during practice'],\n        improvements: improvements.length > 0 ? improvements : ['Continue working on fundamentals'],\n        nextSteps: nextSteps.length > 0 ? nextSteps : ['Keep practicing regularly'],\n        motivationalMessage: motivationalMessages[Math.floor(Math.random() * motivationalMessages.length)],\n      };\n\n      setInsights(newInsights);\n      setIsAnalyzing(false);\n    } catch (error) {\n      console.error('Error generating insights:', error);\n      setIsAnalyzing(false);\n    }\n  }, [user, recentActions]);\n\n  /**\n   * Get personalized training recommendations\n   */\n  const getTrainingRecommendations = useCallback(async () => {\n    if (!user) return null;\n\n    try {\n      const recommendations = await aiAnalysisService.generateTrainingRecommendations(\n        { skillLevel: 'intermediate' }, // Would get from user profile\n        recentActions.map(action => ({ title: action.action })),\n        ['improve consistency', 'increase accuracy']\n      );\n\n      return recommendations;\n    } catch (error) {\n      console.error('Error getting training recommendations:', error);\n      return null;\n    }\n  }, [user, recentActions]);\n\n  // Clean up session when component unmounts\n  useEffect(() => {\n    return () => {\n      if (liveSession?.isActive) {\n        stopLiveCoaching();\n      }\n    };\n  }, []);\n\n  return {\n    liveSession,\n    insights,\n    isAnalyzing,\n    startLiveCoaching,\n    stopLiveCoaching,\n    recordAction,\n    generateInsights,\n    getTrainingRecommendations,\n  };\n}\n\n// Helper functions\n\nfunction calculateConsistency(actions: any[]): number {\n  if (actions.length < 2) return 50;\n  \n  const qualities = actions.map(a => a.quality);\n  const average = qualities.reduce((sum, q) => sum + q, 0) / qualities.length;\n  const variance = qualities.reduce((sum, q) => sum + Math.pow(q - average, 2), 0) / qualities.length;\n  const standardDeviation = Math.sqrt(variance);\n  \n  // Lower standard deviation = higher consistency\n  return Math.max(0, Math.min(100, 100 - (standardDeviation * 2)));\n}\n\nfunction calculateImprovement(actions: any[]): number {\n  if (actions.length < 3) return 0;\n  \n  const firstHalf = actions.slice(0, Math.floor(actions.length / 2));\n  const secondHalf = actions.slice(Math.floor(actions.length / 2));\n  \n  const firstAvg = firstHalf.reduce((sum, a) => sum + a.quality, 0) / firstHalf.length;\n  const secondAvg = secondHalf.reduce((sum, a) => sum + a.quality, 0) / secondHalf.length;\n  \n  return Math.round(((secondAvg - firstAvg) / firstAvg) * 100);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACxD,SAASC,iBAAiB;AAC1B,SAASC,OAAO;AAChB,SAASC,WAAW;AAkCpB,OAAO,SAASC,aAAaA,CAAA,EAAwB;EAAAC,cAAA,GAAAC,CAAA;EACnD,IAAAC,IAAA,IAAAF,cAAA,GAAAG,CAAA,OAAsCV,QAAQ,CAA6B,IAAI,CAAC;IAAAW,KAAA,GAAAC,cAAA,CAAAH,IAAA;IAAzEI,WAAW,GAAAF,KAAA;IAAEG,cAAc,GAAAH,KAAA;EAClC,IAAAI,KAAA,IAAAR,cAAA,GAAAG,CAAA,OAAgCV,QAAQ,CAA4B,IAAI,CAAC;IAAAgB,KAAA,GAAAJ,cAAA,CAAAG,KAAA;IAAlEE,QAAQ,GAAAD,KAAA;IAAEE,WAAW,GAAAF,KAAA;EAC5B,IAAAG,KAAA,IAAAZ,cAAA,GAAAG,CAAA,OAAsCV,QAAQ,CAAC,KAAK,CAAC;IAAAoB,KAAA,GAAAR,cAAA,CAAAO,KAAA;IAA9CE,WAAW,GAAAD,KAAA;IAAEE,cAAc,GAAAF,KAAA;EAClC,IAAAG,KAAA,IAAAhB,cAAA,GAAAG,CAAA,OAA0CV,QAAQ,CAAQ,EAAE,CAAC;IAAAwB,KAAA,GAAAZ,cAAA,CAAAW,KAAA;IAAtDE,aAAa,GAAAD,KAAA;IAAEE,gBAAgB,GAAAF,KAAA;EACtC,IAAAG,KAAA,IAAApB,cAAA,GAAAG,CAAA,OAAiBN,OAAO,CAAC,CAAC;IAAlBwB,IAAI,GAAAD,KAAA,CAAJC,IAAI;EACZ,IAAAC,KAAA,IAAAtB,cAAA,GAAAG,CAAA,OAAgCL,WAAW,CAAC,CAAC;IAArCyB,mBAAmB,GAAAD,KAAA,CAAnBC,mBAAmB;EAK3B,IAAMC,iBAAiB,IAAAxB,cAAA,GAAAG,CAAA,OAAGT,WAAW,CAAA+B,iBAAA,CAAC,aAAY;IAAAzB,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAChD,IAAI,CAACkB,IAAI,EAAE;MAAArB,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAG,CAAA;MAAA;IAAM,CAAC;MAAAH,cAAA,GAAA0B,CAAA;IAAA;IAAA1B,cAAA,GAAAG,CAAA;IAElB,IAAI;MAAAH,cAAA,GAAAG,CAAA;MACFY,cAAc,CAAC,IAAI,CAAC;MAEpB,IAAMY,SAAS,IAAA3B,cAAA,GAAAG,CAAA,QAAG,QAAQyB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACtC,IAAMC,UAA+B,IAAA9B,cAAA,GAAAG,CAAA,QAAG;QACtCwB,SAAS,EAATA,SAAS;QACTI,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE,4CAA4C;QAC1DC,WAAW,EAAE,CACX,6BAA6B,EAC7B,+BAA+B,EAC/B,8BAA8B,CAC/B;QACDC,aAAa,EAAE,kDAAkD;QACjEC,kBAAkB,EAAE;UAClBC,aAAa,EAAE,CAAC;UAChBC,QAAQ,EAAE,CAAC;UACXC,WAAW,EAAE,CAAC;UACdC,WAAW,EAAE;QACf;MACF,CAAC;MAACvC,cAAA,GAAAG,CAAA;MAEFI,cAAc,CAACuB,UAAU,CAAC;MAAC9B,cAAA,GAAAG,CAAA;MAG3B,MAAMoB,mBAAmB,CACvB,0GAA0G,EAC1G,eACF,CAAC;MAACvB,cAAA,GAAAG,CAAA;MAEFY,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOyB,KAAK,EAAE;MAAAxC,cAAA,GAAAG,CAAA;MACdsC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MAACxC,cAAA,GAAAG,CAAA;MACtDY,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,GAAE,CAACM,IAAI,EAAEE,mBAAmB,CAAC,CAAC;EAK/B,IAAMmB,gBAAgB,IAAA1C,cAAA,GAAAG,CAAA,QAAGT,WAAW,CAAC,YAAM;IAAAM,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IACzC,IAAIG,WAAW,EAAE;MAAAN,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAG,CAAA;MACfI,cAAc,CAAC,UAAAoC,IAAI,EAAI;QAAA3C,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QAAA,OAAAwC,IAAI,IAAA3C,cAAA,GAAA0B,CAAA,UAAAkB,MAAA,CAAAC,MAAA,KAAQF,IAAI;UAAEZ,QAAQ,EAAE;QAAK,OAAA/B,cAAA,GAAA0B,CAAA,UAAK,IAAI;MAAD,CAAC,CAAC;MAAC1B,cAAA,GAAAG,CAAA;MAGnE2C,gBAAgB,CAAC,CAAC;IACpB,CAAC;MAAA9C,cAAA,GAAA0B,CAAA;IAAA;EACH,CAAC,EAAE,CAACpB,WAAW,CAAC,CAAC;EAKjB,IAAMyC,YAAY,IAAA/C,cAAA,GAAAG,CAAA,QAAGT,WAAW;IAAA,IAAAsD,MAAA,GAAAvB,iBAAA,CAAC,WAAOwB,MAAc,EAAEC,OAAe,EAAK;MAAAlD,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MAC1E,IAAI,CAAAH,cAAA,GAAA0B,CAAA,WAACpB,WAAW,MAAAN,cAAA,GAAA0B,CAAA,UAAI,CAACL,IAAI,GAAE;QAAArB,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAG,CAAA;QAAA;MAAM,CAAC;QAAAH,cAAA,GAAA0B,CAAA;MAAA;MAAA1B,cAAA,GAAAG,CAAA;MAElC,IAAI;QACF,IAAMgD,UAAU,IAAAnD,cAAA,GAAAG,CAAA,QAAG;UACjB8C,MAAM,EAANA,MAAM;UACNC,OAAO,EAAPA,OAAO;UACPE,SAAS,EAAExB,IAAI,CAACC,GAAG,CAAC,CAAC;UACrBF,SAAS,EAAErB,WAAW,CAACqB;QACzB,CAAC;QAAC3B,cAAA,GAAAG,CAAA;QAGFgB,gBAAgB,CAAC,UAAAwB,IAAI,EAAI;UAAA3C,cAAA,GAAAC,CAAA;UAAAD,cAAA,GAAAG,CAAA;UAAA,UAAAkD,MAAA,CAAAC,kBAAA,CAAIX,IAAI,CAACY,KAAK,CAAC,CAAC,CAAC,CAAC,IAAEJ,UAAU;QAAA,CAAC,CAAC;QAACnD,cAAA,GAAAG,CAAA;QAG1DI,cAAc,CAAC,UAAAoC,IAAI,EAAI;UAAA3C,cAAA,GAAAC,CAAA;UAAAD,cAAA,GAAAG,CAAA;UACrB,IAAI,CAACwC,IAAI,EAAE;YAAA3C,cAAA,GAAA0B,CAAA;YAAA1B,cAAA,GAAAG,CAAA;YAAA,OAAO,IAAI;UAAA,CAAC;YAAAH,cAAA,GAAA0B,CAAA;UAAA;UAEvB,IAAM8B,gBAAgB,IAAAxD,cAAA,GAAAG,CAAA,QAAGwC,IAAI,CAACR,kBAAkB,CAACC,aAAa,GAAG,CAAC;UAClE,IAAMqB,WAAW,IAAAzD,cAAA,GAAAG,CAAA,QAAG,CAAEwC,IAAI,CAACR,kBAAkB,CAACE,QAAQ,GAAGM,IAAI,CAACR,kBAAkB,CAACC,aAAa,GAAIc,OAAO,IAAIM,gBAAgB;UAACxD,cAAA,GAAAG,CAAA;UAE9H,OAAAyC,MAAA,CAAAC,MAAA,KACKF,IAAI;YACPR,kBAAkB,EAAAS,MAAA,CAAAC,MAAA,KACbF,IAAI,CAACR,kBAAkB;cAC1BC,aAAa,EAAEoB,gBAAgB;cAC/BnB,QAAQ,EAAEqB,IAAI,CAACC,KAAK,CAACF,WAAW,CAAC;cACjCnB,WAAW,EAAEoB,IAAI,CAACC,KAAK,CAACC,oBAAoB,IAAAP,MAAA,CAAAC,kBAAA,CAAKpC,aAAa,IAAEiC,UAAU,EAAC,CAAC,CAAC;cAC7EZ,WAAW,EAAEmB,IAAI,CAACC,KAAK,CAACE,oBAAoB,IAAAR,MAAA,CAAAC,kBAAA,CAAKpC,aAAa,IAAEiC,UAAU,EAAC,CAAC;YAAC;UAC9E;QAEL,CAAC,CAAC;QAACnD,cAAA,GAAAG,CAAA;QAGH,IAAIe,aAAa,CAAC4C,MAAM,IAAI,CAAC,EAAE;UAAA9D,cAAA,GAAA0B,CAAA;UAC7B,IAAMqC,gBAAgB,IAAA/D,cAAA,GAAAG,CAAA,cAASP,iBAAiB,CAACoE,wBAAwB,CACvEf,MAAM,KAAAI,MAAA,CAAAC,kBAAA,CACFpC,aAAa,IAAEiC,UAAU,IAC7B;YAAEc,UAAU,EAAE;UAAe,CAC/B,CAAC;UAACjE,cAAA,GAAAG,CAAA;UAGFI,cAAc,CAAC,UAAAoC,IAAI,EAAI;YAAA3C,cAAA,GAAAC,CAAA;YAAAD,cAAA,GAAAG,CAAA;YACrB,IAAI,CAACwC,IAAI,EAAE;cAAA3C,cAAA,GAAA0B,CAAA;cAAA1B,cAAA,GAAAG,CAAA;cAAA,OAAO,IAAI;YAAA,CAAC;cAAAH,cAAA,GAAA0B,CAAA;YAAA;YAAA1B,cAAA,GAAAG,CAAA;YACvB,OAAAyC,MAAA,CAAAC,MAAA,KACKF,IAAI;cACPX,YAAY,EAAE+B,gBAAgB,CAACG,gBAAgB,CAACC,SAAS;cACzDlC,WAAW,EAAE8B,gBAAgB,CAACK,YAAY,CAACnC,WAAW;cACtDC,aAAa,EAAE6B,gBAAgB,CAACK,YAAY,CAAClC;YAAa;UAE9D,CAAC,CAAC;UAAClC,cAAA,GAAAG,CAAA;UAGH,MAAMoB,mBAAmB,CACvBwC,gBAAgB,CAACK,YAAY,CAAClC,aAAa,EAC3C,eACF,CAAC;UAAClC,cAAA,GAAAG,CAAA;UAGF,IAAI,CAAAH,cAAA,GAAA0B,CAAA,UAAAwB,OAAO,GAAG,EAAE,MAAAlD,cAAA,GAAA0B,CAAA,UAAIqC,gBAAgB,CAACK,YAAY,CAACnC,WAAW,CAAC6B,MAAM,GAAG,CAAC,GAAE;YAAA9D,cAAA,GAAA0B,CAAA;YAAA1B,cAAA,GAAAG,CAAA;YACxE,MAAMoB,mBAAmB,CACvBwC,gBAAgB,CAACK,YAAY,CAACnC,WAAW,CAAC,CAAC,CAAC,EAC5C,YACF,CAAC;UACH,CAAC;YAAAjC,cAAA,GAAA0B,CAAA;UAAA;QACH,CAAC;UAAA1B,cAAA,GAAA0B,CAAA;QAAA;MACH,CAAC,CAAC,OAAOc,KAAK,EAAE;QAAAxC,cAAA,GAAAG,CAAA;QACdsC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;IACF,CAAC;IAAA,iBAAA6B,EAAA,EAAAC,GAAA;MAAA,OAAAtB,MAAA,CAAAuB,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,CAAClE,WAAW,EAAEe,IAAI,EAAEH,aAAa,EAAEK,mBAAmB,CAAC,CAAC;EAK3D,IAAMuB,gBAAgB,IAAA9C,cAAA,GAAAG,CAAA,QAAGT,WAAW,CAAA+B,iBAAA,CAAC,aAAY;IAAAzB,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAC/C,IAAI,CAAAH,cAAA,GAAA0B,CAAA,YAACL,IAAI,MAAArB,cAAA,GAAA0B,CAAA,WAAIR,aAAa,CAAC4C,MAAM,KAAK,CAAC,GAAE;MAAA9D,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAG,CAAA;MAAA;IAAM,CAAC;MAAAH,cAAA,GAAA0B,CAAA;IAAA;IAAA1B,cAAA,GAAAG,CAAA;IAEhD,IAAI;MAAAH,cAAA,GAAAG,CAAA;MACFY,cAAc,CAAC,IAAI,CAAC;MAGpB,IAAM0D,cAAc,IAAAzE,cAAA,GAAAG,CAAA,QAAGe,aAAa,CAACwD,MAAM,CAAC,UAACC,GAAG,EAAE1B,MAAM,EAAK;QAAAjD,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QAAA,OAAAwE,GAAG,GAAG1B,MAAM,CAACC,OAAO;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGhC,aAAa,CAAC4C,MAAM;MAC5G,IAAMxB,WAAW,IAAAtC,cAAA,GAAAG,CAAA,QAAGyD,oBAAoB,CAAC1C,aAAa,CAAC;MACvD,IAAMqB,WAAW,IAAAvC,cAAA,GAAAG,CAAA,QAAG0D,oBAAoB,CAAC3C,aAAa,CAAC;MAGvD,IAAM0D,SAAmB,IAAA5E,cAAA,GAAAG,CAAA,QAAG,EAAE;MAC9B,IAAM0E,YAAsB,IAAA7E,cAAA,GAAAG,CAAA,QAAG,EAAE;MACjC,IAAM2E,SAAmB,IAAA9E,cAAA,GAAAG,CAAA,QAAG,EAAE;MAACH,cAAA,GAAAG,CAAA;MAE/B,IAAIsE,cAAc,GAAG,EAAE,EAAE;QAAAzE,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAG,CAAA;QACvByE,SAAS,CAACG,IAAI,CAAC,gCAAgC,CAAC;MAClD,CAAC;QAAA/E,cAAA,GAAA0B,CAAA;MAAA;MAAA1B,cAAA,GAAAG,CAAA;MACD,IAAImC,WAAW,GAAG,EAAE,EAAE;QAAAtC,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAG,CAAA;QACpByE,SAAS,CAACG,IAAI,CAAC,+BAA+B,CAAC;MACjD,CAAC;QAAA/E,cAAA,GAAA0B,CAAA;MAAA;MAAA1B,cAAA,GAAAG,CAAA;MACD,IAAIoC,WAAW,GAAG,CAAC,EAAE;QAAAvC,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAG,CAAA;QACnByE,SAAS,CAACG,IAAI,CAAC,oCAAoC,CAAC;MACtD,CAAC;QAAA/E,cAAA,GAAA0B,CAAA;MAAA;MAAA1B,cAAA,GAAAG,CAAA;MAED,IAAIsE,cAAc,GAAG,EAAE,EAAE;QAAAzE,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAG,CAAA;QACvB0E,YAAY,CAACE,IAAI,CAAC,sCAAsC,CAAC;QAAC/E,cAAA,GAAAG,CAAA;QAC1D2E,SAAS,CAACC,IAAI,CAAC,iCAAiC,CAAC;MACnD,CAAC;QAAA/E,cAAA,GAAA0B,CAAA;MAAA;MAAA1B,cAAA,GAAAG,CAAA;MACD,IAAImC,WAAW,GAAG,EAAE,EAAE;QAAAtC,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAG,CAAA;QACpB0E,YAAY,CAACE,IAAI,CAAC,mCAAmC,CAAC;QAAC/E,cAAA,GAAAG,CAAA;QACvD2E,SAAS,CAACC,IAAI,CAAC,6BAA6B,CAAC;MAC/C,CAAC;QAAA/E,cAAA,GAAA0B,CAAA;MAAA;MAED,IAAMsD,oBAAoB,IAAAhF,cAAA,GAAAG,CAAA,QAAG,CAC3B,0EAA0E,EAC1E,2DAA2D,EAC3D,sEAAsE,EACtE,2DAA2D,CAC5D;MAED,IAAM8E,WAA+B,IAAAjF,cAAA,GAAAG,CAAA,QAAG;QACtCyE,SAAS,EAAEA,SAAS,CAACd,MAAM,GAAG,CAAC,IAAA9D,cAAA,GAAA0B,CAAA,WAAGkD,SAAS,KAAA5E,cAAA,GAAA0B,CAAA,WAAG,CAAC,uCAAuC,CAAC;QACvFmD,YAAY,EAAEA,YAAY,CAACf,MAAM,GAAG,CAAC,IAAA9D,cAAA,GAAA0B,CAAA,WAAGmD,YAAY,KAAA7E,cAAA,GAAA0B,CAAA,WAAG,CAAC,kCAAkC,CAAC;QAC3FoD,SAAS,EAAEA,SAAS,CAAChB,MAAM,GAAG,CAAC,IAAA9D,cAAA,GAAA0B,CAAA,WAAGoD,SAAS,KAAA9E,cAAA,GAAA0B,CAAA,WAAG,CAAC,2BAA2B,CAAC;QAC3EwD,mBAAmB,EAAEF,oBAAoB,CAACtB,IAAI,CAACyB,KAAK,CAACzB,IAAI,CAAC0B,MAAM,CAAC,CAAC,GAAGJ,oBAAoB,CAAClB,MAAM,CAAC;MACnG,CAAC;MAAC9D,cAAA,GAAAG,CAAA;MAEFQ,WAAW,CAACsE,WAAW,CAAC;MAACjF,cAAA,GAAAG,CAAA;MACzBY,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOyB,KAAK,EAAE;MAAAxC,cAAA,GAAAG,CAAA;MACdsC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAACxC,cAAA,GAAAG,CAAA;MACnDY,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,GAAE,CAACM,IAAI,EAAEH,aAAa,CAAC,CAAC;EAKzB,IAAMmE,0BAA0B,IAAArF,cAAA,GAAAG,CAAA,QAAGT,WAAW,CAAA+B,iBAAA,CAAC,aAAY;IAAAzB,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IACzD,IAAI,CAACkB,IAAI,EAAE;MAAArB,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAG,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;MAAAH,cAAA,GAAA0B,CAAA;IAAA;IAAA1B,cAAA,GAAAG,CAAA;IAEvB,IAAI;MACF,IAAMmF,eAAe,IAAAtF,cAAA,GAAAG,CAAA,cAASP,iBAAiB,CAAC2F,+BAA+B,CAC7E;QAAEtB,UAAU,EAAE;MAAe,CAAC,EAC9B/C,aAAa,CAACsE,GAAG,CAAC,UAAAvC,MAAM,EAAK;QAAAjD,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QAAA;UAAEsF,KAAK,EAAExC,MAAM,CAACA;QAAO,CAAC;MAAD,CAAE,CAAC,EACvD,CAAC,qBAAqB,EAAE,mBAAmB,CAC7C,CAAC;MAACjD,cAAA,GAAAG,CAAA;MAEF,OAAOmF,eAAe;IACxB,CAAC,CAAC,OAAO9C,KAAK,EAAE;MAAAxC,cAAA,GAAAG,CAAA;MACdsC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAACxC,cAAA,GAAAG,CAAA;MAChE,OAAO,IAAI;IACb;EACF,CAAC,GAAE,CAACkB,IAAI,EAAEH,aAAa,CAAC,CAAC;EAAClB,cAAA,GAAAG,CAAA;EAG1BR,SAAS,CAAC,YAAM;IAAAK,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IACd,OAAO,YAAM;MAAAH,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MACX,IAAIG,WAAW,YAAXA,WAAW,CAAEyB,QAAQ,EAAE;QAAA/B,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAG,CAAA;QACzBuC,gBAAgB,CAAC,CAAC;MACpB,CAAC;QAAA1C,cAAA,GAAA0B,CAAA;MAAA;IACH,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAAC1B,cAAA,GAAAG,CAAA;EAEP,OAAO;IACLG,WAAW,EAAXA,WAAW;IACXI,QAAQ,EAARA,QAAQ;IACRI,WAAW,EAAXA,WAAW;IACXU,iBAAiB,EAAjBA,iBAAiB;IACjBkB,gBAAgB,EAAhBA,gBAAgB;IAChBK,YAAY,EAAZA,YAAY;IACZD,gBAAgB,EAAhBA,gBAAgB;IAChBuC,0BAA0B,EAA1BA;EACF,CAAC;AACH;AAIA,SAASzB,oBAAoBA,CAAC8B,OAAc,EAAU;EAAA1F,cAAA,GAAAC,CAAA;EAAAD,cAAA,GAAAG,CAAA;EACpD,IAAIuF,OAAO,CAAC5B,MAAM,GAAG,CAAC,EAAE;IAAA9D,cAAA,GAAA0B,CAAA;IAAA1B,cAAA,GAAAG,CAAA;IAAA,OAAO,EAAE;EAAA,CAAC;IAAAH,cAAA,GAAA0B,CAAA;EAAA;EAElC,IAAMiE,SAAS,IAAA3F,cAAA,GAAAG,CAAA,QAAGuF,OAAO,CAACF,GAAG,CAAC,UAAAI,CAAC,EAAI;IAAA5F,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAAA,OAAAyF,CAAC,CAAC1C,OAAO;EAAD,CAAC,CAAC;EAC7C,IAAM2C,OAAO,IAAA7F,cAAA,GAAAG,CAAA,QAAGwF,SAAS,CAACjB,MAAM,CAAC,UAACC,GAAG,EAAEmB,CAAC,EAAK;IAAA9F,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAAA,OAAAwE,GAAG,GAAGmB,CAAC;EAAD,CAAC,EAAE,CAAC,CAAC,GAAGH,SAAS,CAAC7B,MAAM;EAC3E,IAAMiC,QAAQ,IAAA/F,cAAA,GAAAG,CAAA,QAAGwF,SAAS,CAACjB,MAAM,CAAC,UAACC,GAAG,EAAEmB,CAAC,EAAK;IAAA9F,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAAA,OAAAwE,GAAG,GAAGjB,IAAI,CAACsC,GAAG,CAACF,CAAC,GAAGD,OAAO,EAAE,CAAC,CAAC;EAAD,CAAC,EAAE,CAAC,CAAC,GAAGF,SAAS,CAAC7B,MAAM;EACnG,IAAMmC,iBAAiB,IAAAjG,cAAA,GAAAG,CAAA,QAAGuD,IAAI,CAACwC,IAAI,CAACH,QAAQ,CAAC;EAAC/F,cAAA,GAAAG,CAAA;EAG9C,OAAOuD,IAAI,CAACyC,GAAG,CAAC,CAAC,EAAEzC,IAAI,CAAC0C,GAAG,CAAC,GAAG,EAAE,GAAG,GAAIH,iBAAiB,GAAG,CAAE,CAAC,CAAC;AAClE;AAEA,SAASpC,oBAAoBA,CAAC6B,OAAc,EAAU;EAAA1F,cAAA,GAAAC,CAAA;EAAAD,cAAA,GAAAG,CAAA;EACpD,IAAIuF,OAAO,CAAC5B,MAAM,GAAG,CAAC,EAAE;IAAA9D,cAAA,GAAA0B,CAAA;IAAA1B,cAAA,GAAAG,CAAA;IAAA,OAAO,CAAC;EAAA,CAAC;IAAAH,cAAA,GAAA0B,CAAA;EAAA;EAEjC,IAAM2E,SAAS,IAAArG,cAAA,GAAAG,CAAA,SAAGuF,OAAO,CAACnC,KAAK,CAAC,CAAC,EAAEG,IAAI,CAACyB,KAAK,CAACO,OAAO,CAAC5B,MAAM,GAAG,CAAC,CAAC,CAAC;EAClE,IAAMwC,UAAU,IAAAtG,cAAA,GAAAG,CAAA,SAAGuF,OAAO,CAACnC,KAAK,CAACG,IAAI,CAACyB,KAAK,CAACO,OAAO,CAAC5B,MAAM,GAAG,CAAC,CAAC,CAAC;EAEhE,IAAMyC,QAAQ,IAAAvG,cAAA,GAAAG,CAAA,SAAGkG,SAAS,CAAC3B,MAAM,CAAC,UAACC,GAAG,EAAEiB,CAAC,EAAK;IAAA5F,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAAA,OAAAwE,GAAG,GAAGiB,CAAC,CAAC1C,OAAO;EAAD,CAAC,EAAE,CAAC,CAAC,GAAGmD,SAAS,CAACvC,MAAM;EACpF,IAAM0C,SAAS,IAAAxG,cAAA,GAAAG,CAAA,SAAGmG,UAAU,CAAC5B,MAAM,CAAC,UAACC,GAAG,EAAEiB,CAAC,EAAK;IAAA5F,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAAA,OAAAwE,GAAG,GAAGiB,CAAC,CAAC1C,OAAO;EAAD,CAAC,EAAE,CAAC,CAAC,GAAGoD,UAAU,CAACxC,MAAM;EAAC9D,cAAA,GAAAG,CAAA;EAExF,OAAOuD,IAAI,CAACC,KAAK,CAAE,CAAC6C,SAAS,GAAGD,QAAQ,IAAIA,QAAQ,GAAI,GAAG,CAAC;AAC9D", "ignoreList": []}