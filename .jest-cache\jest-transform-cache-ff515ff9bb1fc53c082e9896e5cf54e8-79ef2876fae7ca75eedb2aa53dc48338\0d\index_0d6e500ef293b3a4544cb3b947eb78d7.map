{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_dismissKeyboard", "Keyboard", "isVisible", "addListener", "remove", "dismiss", "removeAllListeners", "removeListener", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _dismissKeyboard = _interopRequireDefault(require(\"../../modules/dismissKeyboard\"));\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n// in the future we can use https://github.com/w3c/virtual-keyboard\nvar Keyboard = {\n  isVisible() {\n    return false;\n  },\n  addListener() {\n    return {\n      remove: () => {}\n    };\n  },\n  dismiss() {\n    (0, _dismissKeyboard.default)();\n  },\n  removeAllListeners() {},\n  removeListener() {}\n};\nvar _default = exports.default = Keyboard;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,gBAAgB,GAAGL,sBAAsB,CAACC,OAAO,gCAAgC,CAAC,CAAC;AAYvF,IAAIK,QAAQ,GAAG;EACbC,SAAS,WAATA,SAASA,CAAA,EAAG;IACV,OAAO,KAAK;EACd,CAAC;EACDC,WAAW,WAAXA,WAAWA,CAAA,EAAG;IACZ,OAAO;MACLC,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ,CAAC;IACjB,CAAC;EACH,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,CAAC,CAAC,EAAEL,gBAAgB,CAACH,OAAO,EAAE,CAAC;EACjC,CAAC;EACDS,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG,CAAC,CAAC;EACvBC,cAAc,WAAdA,cAAcA,CAAA,EAAG,CAAC;AACpB,CAAC;AACD,IAAIC,QAAQ,GAAGV,OAAO,CAACD,OAAO,GAAGI,QAAQ;AACzCQ,MAAM,CAACX,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}