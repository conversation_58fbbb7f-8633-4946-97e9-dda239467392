1017bdbdc9e2447486d1f85fa65aa0dc
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.matchRepository = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _supabase = require("../../../lib/supabase");
function cov_q5ev797dr() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\services\\database\\MatchRepository.ts";
  var hash = "cef1bdde1ea453cffc9118ee4a42b0c9763f46a5";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\services\\database\\MatchRepository.ts",
    statementMap: {
      "0": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 98,
          column: 5
        }
      },
      "1": {
        start: {
          line: 83,
          column: 30
        },
        end: {
          line: 87,
          column: 17
        }
      },
      "2": {
        start: {
          line: 89,
          column: 6
        },
        end: {
          line: 92,
          column: 7
        }
      },
      "3": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 90,
          column: 54
        }
      },
      "4": {
        start: {
          line: 91,
          column: 8
        },
        end: {
          line: 91,
          column: 52
        }
      },
      "5": {
        start: {
          line: 94,
          column: 6
        },
        end: {
          line: 94,
          column: 35
        }
      },
      "6": {
        start: {
          line: 96,
          column: 6
        },
        end: {
          line: 96,
          column: 52
        }
      },
      "7": {
        start: {
          line: 97,
          column: 6
        },
        end: {
          line: 97,
          column: 61
        }
      },
      "8": {
        start: {
          line: 108,
          column: 4
        },
        end: {
          line: 125,
          column: 5
        }
      },
      "9": {
        start: {
          line: 109,
          column: 30
        },
        end: {
          line: 114,
          column: 17
        }
      },
      "10": {
        start: {
          line: 116,
          column: 6
        },
        end: {
          line: 119,
          column: 7
        }
      },
      "11": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 117,
          column: 54
        }
      },
      "12": {
        start: {
          line: 118,
          column: 8
        },
        end: {
          line: 118,
          column: 52
        }
      },
      "13": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 121,
          column: 35
        }
      },
      "14": {
        start: {
          line: 123,
          column: 6
        },
        end: {
          line: 123,
          column: 52
        }
      },
      "15": {
        start: {
          line: 124,
          column: 6
        },
        end: {
          line: 124,
          column: 61
        }
      },
      "16": {
        start: {
          line: 132,
          column: 4
        },
        end: {
          line: 148,
          column: 5
        }
      },
      "17": {
        start: {
          line: 133,
          column: 30
        },
        end: {
          line: 137,
          column: 17
        }
      },
      "18": {
        start: {
          line: 139,
          column: 6
        },
        end: {
          line: 142,
          column: 7
        }
      },
      "19": {
        start: {
          line: 140,
          column: 8
        },
        end: {
          line: 140,
          column: 54
        }
      },
      "20": {
        start: {
          line: 141,
          column: 8
        },
        end: {
          line: 141,
          column: 52
        }
      },
      "21": {
        start: {
          line: 144,
          column: 6
        },
        end: {
          line: 144,
          column: 35
        }
      },
      "22": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 146,
          column: 52
        }
      },
      "23": {
        start: {
          line: 147,
          column: 6
        },
        end: {
          line: 147,
          column: 60
        }
      },
      "24": {
        start: {
          line: 159,
          column: 4
        },
        end: {
          line: 176,
          column: 5
        }
      },
      "25": {
        start: {
          line: 160,
          column: 30
        },
        end: {
          line: 165,
          column: 42
        }
      },
      "26": {
        start: {
          line: 167,
          column: 6
        },
        end: {
          line: 170,
          column: 7
        }
      },
      "27": {
        start: {
          line: 168,
          column: 8
        },
        end: {
          line: 168,
          column: 61
        }
      },
      "28": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 169,
          column: 52
        }
      },
      "29": {
        start: {
          line: 172,
          column: 6
        },
        end: {
          line: 172,
          column: 35
        }
      },
      "30": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 59
        }
      },
      "31": {
        start: {
          line: 175,
          column: 6
        },
        end: {
          line: 175,
          column: 62
        }
      },
      "32": {
        start: {
          line: 183,
          column: 4
        },
        end: {
          line: 198,
          column: 5
        }
      },
      "33": {
        start: {
          line: 184,
          column: 24
        },
        end: {
          line: 187,
          column: 26
        }
      },
      "34": {
        start: {
          line: 189,
          column: 6
        },
        end: {
          line: 192,
          column: 7
        }
      },
      "35": {
        start: {
          line: 190,
          column: 8
        },
        end: {
          line: 190,
          column: 54
        }
      },
      "36": {
        start: {
          line: 191,
          column: 8
        },
        end: {
          line: 191,
          column: 40
        }
      },
      "37": {
        start: {
          line: 194,
          column: 6
        },
        end: {
          line: 194,
          column: 29
        }
      },
      "38": {
        start: {
          line: 196,
          column: 6
        },
        end: {
          line: 196,
          column: 52
        }
      },
      "39": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 197,
          column: 49
        }
      },
      "40": {
        start: {
          line: 208,
          column: 4
        },
        end: {
          line: 228,
          column: 5
        }
      },
      "41": {
        start: {
          line: 209,
          column: 23
        },
        end: {
          line: 212,
          column: 9
        }
      },
      "42": {
        start: {
          line: 209,
          column: 40
        },
        end: {
          line: 212,
          column: 7
        }
      },
      "43": {
        start: {
          line: 214,
          column: 30
        },
        end: {
          line: 217,
          column: 17
        }
      },
      "44": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 222,
          column: 7
        }
      },
      "45": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 220,
          column: 59
        }
      },
      "46": {
        start: {
          line: 221,
          column: 8
        },
        end: {
          line: 221,
          column: 52
        }
      },
      "47": {
        start: {
          line: 224,
          column: 6
        },
        end: {
          line: 224,
          column: 35
        }
      },
      "48": {
        start: {
          line: 226,
          column: 6
        },
        end: {
          line: 226,
          column: 57
        }
      },
      "49": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 66
        }
      },
      "50": {
        start: {
          line: 235,
          column: 4
        },
        end: {
          line: 251,
          column: 5
        }
      },
      "51": {
        start: {
          line: 236,
          column: 30
        },
        end: {
          line: 240,
          column: 28
        }
      },
      "52": {
        start: {
          line: 242,
          column: 6
        },
        end: {
          line: 245,
          column: 7
        }
      },
      "53": {
        start: {
          line: 243,
          column: 8
        },
        end: {
          line: 243,
          column: 59
        }
      },
      "54": {
        start: {
          line: 244,
          column: 8
        },
        end: {
          line: 244,
          column: 52
        }
      },
      "55": {
        start: {
          line: 247,
          column: 6
        },
        end: {
          line: 247,
          column: 35
        }
      },
      "56": {
        start: {
          line: 249,
          column: 6
        },
        end: {
          line: 249,
          column: 57
        }
      },
      "57": {
        start: {
          line: 250,
          column: 6
        },
        end: {
          line: 250,
          column: 65
        }
      },
      "58": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 276,
          column: 5
        }
      },
      "59": {
        start: {
          line: 261,
          column: 30
        },
        end: {
          line: 265,
          column: 17
        }
      },
      "60": {
        start: {
          line: 267,
          column: 6
        },
        end: {
          line: 270,
          column: 7
        }
      },
      "61": {
        start: {
          line: 268,
          column: 8
        },
        end: {
          line: 268,
          column: 65
        }
      },
      "62": {
        start: {
          line: 269,
          column: 8
        },
        end: {
          line: 269,
          column: 52
        }
      },
      "63": {
        start: {
          line: 272,
          column: 6
        },
        end: {
          line: 272,
          column: 35
        }
      },
      "64": {
        start: {
          line: 274,
          column: 6
        },
        end: {
          line: 274,
          column: 63
        }
      },
      "65": {
        start: {
          line: 275,
          column: 6
        },
        end: {
          line: 275,
          column: 72
        }
      },
      "66": {
        start: {
          line: 287,
          column: 4
        },
        end: {
          line: 305,
          column: 5
        }
      },
      "67": {
        start: {
          line: 288,
          column: 30
        },
        end: {
          line: 294,
          column: 17
        }
      },
      "68": {
        start: {
          line: 296,
          column: 6
        },
        end: {
          line: 299,
          column: 7
        }
      },
      "69": {
        start: {
          line: 297,
          column: 8
        },
        end: {
          line: 297,
          column: 65
        }
      },
      "70": {
        start: {
          line: 298,
          column: 8
        },
        end: {
          line: 298,
          column: 52
        }
      },
      "71": {
        start: {
          line: 301,
          column: 6
        },
        end: {
          line: 301,
          column: 35
        }
      },
      "72": {
        start: {
          line: 303,
          column: 6
        },
        end: {
          line: 303,
          column: 63
        }
      },
      "73": {
        start: {
          line: 304,
          column: 6
        },
        end: {
          line: 304,
          column: 72
        }
      },
      "74": {
        start: {
          line: 315,
          column: 4
        },
        end: {
          line: 332,
          column: 5
        }
      },
      "75": {
        start: {
          line: 316,
          column: 30
        },
        end: {
          line: 321,
          column: 17
        }
      },
      "76": {
        start: {
          line: 323,
          column: 6
        },
        end: {
          line: 326,
          column: 7
        }
      },
      "77": {
        start: {
          line: 324,
          column: 8
        },
        end: {
          line: 324,
          column: 65
        }
      },
      "78": {
        start: {
          line: 325,
          column: 8
        },
        end: {
          line: 325,
          column: 52
        }
      },
      "79": {
        start: {
          line: 328,
          column: 6
        },
        end: {
          line: 328,
          column: 35
        }
      },
      "80": {
        start: {
          line: 330,
          column: 6
        },
        end: {
          line: 330,
          column: 63
        }
      },
      "81": {
        start: {
          line: 331,
          column: 6
        },
        end: {
          line: 331,
          column: 71
        }
      },
      "82": {
        start: {
          line: 339,
          column: 4
        },
        end: {
          line: 362,
          column: 6
        }
      },
      "83": {
        start: {
          line: 369,
          column: 4
        },
        end: {
          line: 425,
          column: 6
        }
      },
      "84": {
        start: {
          line: 430,
          column: 31
        },
        end: {
          line: 430,
          column: 52
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 81,
            column: 2
          },
          end: {
            line: 81,
            column: 3
          }
        },
        loc: {
          start: {
            line: 81,
            column: 118
          },
          end: {
            line: 99,
            column: 3
          }
        },
        line: 81
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 104,
            column: 2
          },
          end: {
            line: 104,
            column: 3
          }
        },
        loc: {
          start: {
            line: 107,
            column: 67
          },
          end: {
            line: 126,
            column: 3
          }
        },
        line: 107
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 131,
            column: 2
          },
          end: {
            line: 131,
            column: 3
          }
        },
        loc: {
          start: {
            line: 131,
            column: 97
          },
          end: {
            line: 149,
            column: 3
          }
        },
        line: 131
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 154,
            column: 2
          },
          end: {
            line: 154,
            column: 3
          }
        },
        loc: {
          start: {
            line: 158,
            column: 69
          },
          end: {
            line: 177,
            column: 3
          }
        },
        line: 158
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 182,
            column: 2
          },
          end: {
            line: 182,
            column: 3
          }
        },
        loc: {
          start: {
            line: 182,
            column: 72
          },
          end: {
            line: 199,
            column: 3
          }
        },
        line: 182
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 204,
            column: 2
          },
          end: {
            line: 204,
            column: 3
          }
        },
        loc: {
          start: {
            line: 207,
            column: 72
          },
          end: {
            line: 229,
            column: 3
          }
        },
        line: 207
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 209,
            column: 32
          },
          end: {
            line: 209,
            column: 33
          }
        },
        loc: {
          start: {
            line: 209,
            column: 40
          },
          end: {
            line: 212,
            column: 7
          }
        },
        line: 209
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 234,
            column: 2
          },
          end: {
            line: 234,
            column: 3
          }
        },
        loc: {
          start: {
            line: 234,
            column: 106
          },
          end: {
            line: 252,
            column: 3
          }
        },
        line: 234
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 257,
            column: 2
          },
          end: {
            line: 257,
            column: 3
          }
        },
        loc: {
          start: {
            line: 259,
            column: 77
          },
          end: {
            line: 277,
            column: 3
          }
        },
        line: 259
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 282,
            column: 2
          },
          end: {
            line: 282,
            column: 3
          }
        },
        loc: {
          start: {
            line: 286,
            column: 77
          },
          end: {
            line: 306,
            column: 3
          }
        },
        line: 286
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 311,
            column: 2
          },
          end: {
            line: 311,
            column: 3
          }
        },
        loc: {
          start: {
            line: 314,
            column: 77
          },
          end: {
            line: 333,
            column: 3
          }
        },
        line: 314
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 338,
            column: 2
          },
          end: {
            line: 338,
            column: 3
          }
        },
        loc: {
          start: {
            line: 338,
            column: 67
          },
          end: {
            line: 363,
            column: 3
          }
        },
        line: 338
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 368,
            column: 2
          },
          end: {
            line: 368,
            column: 3
          }
        },
        loc: {
          start: {
            line: 368,
            column: 62
          },
          end: {
            line: 426,
            column: 3
          }
        },
        line: 368
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 89,
            column: 6
          },
          end: {
            line: 92,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 89,
            column: 6
          },
          end: {
            line: 92,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 89
      },
      "1": {
        loc: {
          start: {
            line: 116,
            column: 6
          },
          end: {
            line: 119,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 116,
            column: 6
          },
          end: {
            line: 119,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 116
      },
      "2": {
        loc: {
          start: {
            line: 139,
            column: 6
          },
          end: {
            line: 142,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 6
          },
          end: {
            line: 142,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 139
      },
      "3": {
        loc: {
          start: {
            line: 156,
            column: 4
          },
          end: {
            line: 156,
            column: 22
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 156,
            column: 20
          },
          end: {
            line: 156,
            column: 22
          }
        }],
        line: 156
      },
      "4": {
        loc: {
          start: {
            line: 157,
            column: 4
          },
          end: {
            line: 157,
            column: 22
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 157,
            column: 21
          },
          end: {
            line: 157,
            column: 22
          }
        }],
        line: 157
      },
      "5": {
        loc: {
          start: {
            line: 167,
            column: 6
          },
          end: {
            line: 170,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 167,
            column: 6
          },
          end: {
            line: 170,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 167
      },
      "6": {
        loc: {
          start: {
            line: 189,
            column: 6
          },
          end: {
            line: 192,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 189,
            column: 6
          },
          end: {
            line: 192,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 189
      },
      "7": {
        loc: {
          start: {
            line: 219,
            column: 6
          },
          end: {
            line: 222,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 6
          },
          end: {
            line: 222,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 219
      },
      "8": {
        loc: {
          start: {
            line: 242,
            column: 6
          },
          end: {
            line: 245,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 242,
            column: 6
          },
          end: {
            line: 245,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 242
      },
      "9": {
        loc: {
          start: {
            line: 267,
            column: 6
          },
          end: {
            line: 270,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 267,
            column: 6
          },
          end: {
            line: 270,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 267
      },
      "10": {
        loc: {
          start: {
            line: 296,
            column: 6
          },
          end: {
            line: 299,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 296,
            column: 6
          },
          end: {
            line: 299,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 296
      },
      "11": {
        loc: {
          start: {
            line: 323,
            column: 6
          },
          end: {
            line: 326,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 323,
            column: 6
          },
          end: {
            line: 326,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 323
      },
      "12": {
        loc: {
          start: {
            line: 347,
            column: 26
          },
          end: {
            line: 350,
            column: 14
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 347,
            column: 61
          },
          end: {
            line: 350,
            column: 7
          }
        }, {
          start: {
            line: 350,
            column: 10
          },
          end: {
            line: 350,
            column: 14
          }
        }],
        line: 347
      },
      "13": {
        loc: {
          start: {
            line: 386,
            column: 13
          },
          end: {
            line: 392,
            column: 7
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 386,
            column: 13
          },
          end: {
            line: 386,
            column: 32
          }
        }, {
          start: {
            line: 386,
            column: 36
          },
          end: {
            line: 392,
            column: 7
          }
        }],
        line: 386
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0],
      "4": [0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "cef1bdde1ea453cffc9118ee4a42b0c9763f46a5"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_q5ev797dr = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_q5ev797dr();
var MatchRepository = function () {
  function MatchRepository() {
    (0, _classCallCheck2.default)(this, MatchRepository);
  }
  return (0, _createClass2.default)(MatchRepository, [{
    key: "createMatch",
    value: (function () {
      var _createMatch = (0, _asyncToGenerator2.default)(function* (matchData) {
        cov_q5ev797dr().f[0]++;
        cov_q5ev797dr().s[0]++;
        try {
          var _ref = (cov_q5ev797dr().s[1]++, yield _supabase.supabase.from('matches').insert([matchData]).select().single()),
            data = _ref.data,
            error = _ref.error;
          cov_q5ev797dr().s[2]++;
          if (error) {
            cov_q5ev797dr().b[0][0]++;
            cov_q5ev797dr().s[3]++;
            console.error('Error creating match:', error);
            cov_q5ev797dr().s[4]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[0][1]++;
          }
          cov_q5ev797dr().s[5]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[6]++;
          console.error('Error creating match:', error);
          cov_q5ev797dr().s[7]++;
          return {
            data: null,
            error: 'Failed to create match'
          };
        }
      });
      function createMatch(_x) {
        return _createMatch.apply(this, arguments);
      }
      return createMatch;
    }())
  }, {
    key: "updateMatch",
    value: (function () {
      var _updateMatch = (0, _asyncToGenerator2.default)(function* (matchId, updates) {
        cov_q5ev797dr().f[1]++;
        cov_q5ev797dr().s[8]++;
        try {
          var _ref2 = (cov_q5ev797dr().s[9]++, yield _supabase.supabase.from('matches').update(Object.assign({}, updates, {
              updated_at: new Date().toISOString()
            })).eq('id', matchId).select().single()),
            data = _ref2.data,
            error = _ref2.error;
          cov_q5ev797dr().s[10]++;
          if (error) {
            cov_q5ev797dr().b[1][0]++;
            cov_q5ev797dr().s[11]++;
            console.error('Error updating match:', error);
            cov_q5ev797dr().s[12]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[1][1]++;
          }
          cov_q5ev797dr().s[13]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[14]++;
          console.error('Error updating match:', error);
          cov_q5ev797dr().s[15]++;
          return {
            data: null,
            error: 'Failed to update match'
          };
        }
      });
      function updateMatch(_x2, _x3) {
        return _updateMatch.apply(this, arguments);
      }
      return updateMatch;
    }())
  }, {
    key: "getMatch",
    value: (function () {
      var _getMatch = (0, _asyncToGenerator2.default)(function* (matchId) {
        cov_q5ev797dr().f[2]++;
        cov_q5ev797dr().s[16]++;
        try {
          var _ref3 = (cov_q5ev797dr().s[17]++, yield _supabase.supabase.from('matches').select('*').eq('id', matchId).single()),
            data = _ref3.data,
            error = _ref3.error;
          cov_q5ev797dr().s[18]++;
          if (error) {
            cov_q5ev797dr().b[2][0]++;
            cov_q5ev797dr().s[19]++;
            console.error('Error fetching match:', error);
            cov_q5ev797dr().s[20]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[2][1]++;
          }
          cov_q5ev797dr().s[21]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[22]++;
          console.error('Error fetching match:', error);
          cov_q5ev797dr().s[23]++;
          return {
            data: null,
            error: 'Failed to fetch match'
          };
        }
      });
      function getMatch(_x4) {
        return _getMatch.apply(this, arguments);
      }
      return getMatch;
    }())
  }, {
    key: "getUserMatches",
    value: (function () {
      var _getUserMatches = (0, _asyncToGenerator2.default)(function* (userId) {
        var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_q5ev797dr().b[3][0]++, 50);
        var offset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_q5ev797dr().b[4][0]++, 0);
        cov_q5ev797dr().f[3]++;
        cov_q5ev797dr().s[24]++;
        try {
          var _ref4 = (cov_q5ev797dr().s[25]++, yield _supabase.supabase.from('matches').select('*').eq('user_id', userId).order('created_at', {
              ascending: false
            }).range(offset, offset + limit - 1)),
            data = _ref4.data,
            error = _ref4.error;
          cov_q5ev797dr().s[26]++;
          if (error) {
            cov_q5ev797dr().b[5][0]++;
            cov_q5ev797dr().s[27]++;
            console.error('Error fetching user matches:', error);
            cov_q5ev797dr().s[28]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[5][1]++;
          }
          cov_q5ev797dr().s[29]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[30]++;
          console.error('Error fetching user matches:', error);
          cov_q5ev797dr().s[31]++;
          return {
            data: null,
            error: 'Failed to fetch matches'
          };
        }
      });
      function getUserMatches(_x5) {
        return _getUserMatches.apply(this, arguments);
      }
      return getUserMatches;
    }())
  }, {
    key: "deleteMatch",
    value: (function () {
      var _deleteMatch = (0, _asyncToGenerator2.default)(function* (matchId) {
        cov_q5ev797dr().f[4]++;
        cov_q5ev797dr().s[32]++;
        try {
          var _ref5 = (cov_q5ev797dr().s[33]++, yield _supabase.supabase.from('matches').delete().eq('id', matchId)),
            error = _ref5.error;
          cov_q5ev797dr().s[34]++;
          if (error) {
            cov_q5ev797dr().b[6][0]++;
            cov_q5ev797dr().s[35]++;
            console.error('Error deleting match:', error);
            cov_q5ev797dr().s[36]++;
            return {
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[6][1]++;
          }
          cov_q5ev797dr().s[37]++;
          return {
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[38]++;
          console.error('Error deleting match:', error);
          cov_q5ev797dr().s[39]++;
          return {
            error: 'Failed to delete match'
          };
        }
      });
      function deleteMatch(_x6) {
        return _deleteMatch.apply(this, arguments);
      }
      return deleteMatch;
    }())
  }, {
    key: "createMatchSets",
    value: (function () {
      var _createMatchSets = (0, _asyncToGenerator2.default)(function* (matchId, sets) {
        cov_q5ev797dr().f[5]++;
        cov_q5ev797dr().s[40]++;
        try {
          var setsData = (cov_q5ev797dr().s[41]++, sets.map(function (set) {
            cov_q5ev797dr().f[6]++;
            cov_q5ev797dr().s[42]++;
            return Object.assign({}, set, {
              match_id: matchId
            });
          }));
          var _ref6 = (cov_q5ev797dr().s[43]++, yield _supabase.supabase.from('match_sets').insert(setsData).select()),
            data = _ref6.data,
            error = _ref6.error;
          cov_q5ev797dr().s[44]++;
          if (error) {
            cov_q5ev797dr().b[7][0]++;
            cov_q5ev797dr().s[45]++;
            console.error('Error creating match sets:', error);
            cov_q5ev797dr().s[46]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[7][1]++;
          }
          cov_q5ev797dr().s[47]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[48]++;
          console.error('Error creating match sets:', error);
          cov_q5ev797dr().s[49]++;
          return {
            data: null,
            error: 'Failed to create match sets'
          };
        }
      });
      function createMatchSets(_x7, _x8) {
        return _createMatchSets.apply(this, arguments);
      }
      return createMatchSets;
    }())
  }, {
    key: "getMatchSets",
    value: (function () {
      var _getMatchSets = (0, _asyncToGenerator2.default)(function* (matchId) {
        cov_q5ev797dr().f[7]++;
        cov_q5ev797dr().s[50]++;
        try {
          var _ref7 = (cov_q5ev797dr().s[51]++, yield _supabase.supabase.from('match_sets').select('*').eq('match_id', matchId).order('set_number')),
            data = _ref7.data,
            error = _ref7.error;
          cov_q5ev797dr().s[52]++;
          if (error) {
            cov_q5ev797dr().b[8][0]++;
            cov_q5ev797dr().s[53]++;
            console.error('Error fetching match sets:', error);
            cov_q5ev797dr().s[54]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[8][1]++;
          }
          cov_q5ev797dr().s[55]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[56]++;
          console.error('Error fetching match sets:', error);
          cov_q5ev797dr().s[57]++;
          return {
            data: null,
            error: 'Failed to fetch match sets'
          };
        }
      });
      function getMatchSets(_x9) {
        return _getMatchSets.apply(this, arguments);
      }
      return getMatchSets;
    }())
  }, {
    key: "createMatchStatistics",
    value: (function () {
      var _createMatchStatistics = (0, _asyncToGenerator2.default)(function* (statistics) {
        cov_q5ev797dr().f[8]++;
        cov_q5ev797dr().s[58]++;
        try {
          var _ref8 = (cov_q5ev797dr().s[59]++, yield _supabase.supabase.from('match_statistics').insert([statistics]).select().single()),
            data = _ref8.data,
            error = _ref8.error;
          cov_q5ev797dr().s[60]++;
          if (error) {
            cov_q5ev797dr().b[9][0]++;
            cov_q5ev797dr().s[61]++;
            console.error('Error creating match statistics:', error);
            cov_q5ev797dr().s[62]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[9][1]++;
          }
          cov_q5ev797dr().s[63]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[64]++;
          console.error('Error creating match statistics:', error);
          cov_q5ev797dr().s[65]++;
          return {
            data: null,
            error: 'Failed to create match statistics'
          };
        }
      });
      function createMatchStatistics(_x0) {
        return _createMatchStatistics.apply(this, arguments);
      }
      return createMatchStatistics;
    }())
  }, {
    key: "updateMatchStatistics",
    value: (function () {
      var _updateMatchStatistics = (0, _asyncToGenerator2.default)(function* (matchId, userId, updates) {
        cov_q5ev797dr().f[9]++;
        cov_q5ev797dr().s[66]++;
        try {
          var _ref9 = (cov_q5ev797dr().s[67]++, yield _supabase.supabase.from('match_statistics').update(updates).eq('match_id', matchId).eq('user_id', userId).select().single()),
            data = _ref9.data,
            error = _ref9.error;
          cov_q5ev797dr().s[68]++;
          if (error) {
            cov_q5ev797dr().b[10][0]++;
            cov_q5ev797dr().s[69]++;
            console.error('Error updating match statistics:', error);
            cov_q5ev797dr().s[70]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[10][1]++;
          }
          cov_q5ev797dr().s[71]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[72]++;
          console.error('Error updating match statistics:', error);
          cov_q5ev797dr().s[73]++;
          return {
            data: null,
            error: 'Failed to update match statistics'
          };
        }
      });
      function updateMatchStatistics(_x1, _x10, _x11) {
        return _updateMatchStatistics.apply(this, arguments);
      }
      return updateMatchStatistics;
    }())
  }, {
    key: "getMatchStatistics",
    value: (function () {
      var _getMatchStatistics = (0, _asyncToGenerator2.default)(function* (matchId, userId) {
        cov_q5ev797dr().f[10]++;
        cov_q5ev797dr().s[74]++;
        try {
          var _ref0 = (cov_q5ev797dr().s[75]++, yield _supabase.supabase.from('match_statistics').select('*').eq('match_id', matchId).eq('user_id', userId).single()),
            data = _ref0.data,
            error = _ref0.error;
          cov_q5ev797dr().s[76]++;
          if (error) {
            cov_q5ev797dr().b[11][0]++;
            cov_q5ev797dr().s[77]++;
            console.error('Error fetching match statistics:', error);
            cov_q5ev797dr().s[78]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[11][1]++;
          }
          cov_q5ev797dr().s[79]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[80]++;
          console.error('Error fetching match statistics:', error);
          cov_q5ev797dr().s[81]++;
          return {
            data: null,
            error: 'Failed to fetch match statistics'
          };
        }
      });
      function getMatchStatistics(_x12, _x13) {
        return _getMatchStatistics.apply(this, arguments);
      }
      return getMatchStatistics;
    }())
  }, {
    key: "convertToDatabase",
    value: function convertToDatabase(match) {
      cov_q5ev797dr().f[11]++;
      cov_q5ev797dr().s[82]++;
      return {
        user_id: match.metadata.userId,
        opponent_name: match.metadata.opponentName,
        opponent_id: match.metadata.opponentId,
        match_type: match.metadata.matchType,
        match_format: match.metadata.matchFormat,
        court_type: match.metadata.surface,
        court_location: match.metadata.location,
        weather_conditions: match.metadata.weatherConditions ? (cov_q5ev797dr().b[12][0]++, {
          conditions: match.metadata.weatherConditions,
          temperature: match.metadata.temperature
        }) : (cov_q5ev797dr().b[12][1]++, null),
        match_status: match.status,
        start_time: match.metadata.startTime,
        end_time: match.metadata.endTime,
        duration_minutes: match.metadata.durationMinutes,
        final_score: match.score,
        match_notes: '',
        video_url: match.videoUrl,
        video_thumbnail_url: match.videoThumbnailUrl,
        video_duration_seconds: match.videoDurationSeconds,
        video_file_size_bytes: match.videoFileSizeBytes,
        analysis_status: 'pending'
      };
    }
  }, {
    key: "convertFromDatabase",
    value: function convertFromDatabase(dbMatch) {
      var _dbMatch$weather_cond, _dbMatch$weather_cond2;
      cov_q5ev797dr().f[12]++;
      cov_q5ev797dr().s[83]++;
      return {
        id: dbMatch.id,
        metadata: {
          userId: dbMatch.user_id,
          opponentName: dbMatch.opponent_name,
          opponentId: dbMatch.opponent_id,
          matchType: dbMatch.match_type,
          matchFormat: dbMatch.match_format,
          surface: dbMatch.court_type,
          location: dbMatch.court_location,
          weatherConditions: (_dbMatch$weather_cond = dbMatch.weather_conditions) == null ? void 0 : _dbMatch$weather_cond.conditions,
          temperature: (_dbMatch$weather_cond2 = dbMatch.weather_conditions) == null ? void 0 : _dbMatch$weather_cond2.temperature,
          matchDate: dbMatch.created_at.split('T')[0],
          startTime: dbMatch.start_time,
          endTime: dbMatch.end_time,
          durationMinutes: dbMatch.duration_minutes
        },
        score: (cov_q5ev797dr().b[13][0]++, dbMatch.final_score) || (cov_q5ev797dr().b[13][1]++, {
          sets: [],
          finalScore: '',
          result: 'win',
          setsWon: 0,
          setsLost: 0
        }),
        statistics: {
          matchId: dbMatch.id,
          userId: dbMatch.user_id,
          aces: 0,
          doubleFaults: 0,
          firstServesIn: 0,
          firstServesAttempted: 0,
          firstServePointsWon: 0,
          secondServePointsWon: 0,
          firstServeReturnPointsWon: 0,
          secondServeReturnPointsWon: 0,
          breakPointsConverted: 0,
          breakPointsFaced: 0,
          winners: 0,
          unforcedErrors: 0,
          forcedErrors: 0,
          totalPointsWon: 0,
          totalPointsPlayed: 0,
          netPointsAttempted: 0,
          netPointsWon: 0,
          forehandWinners: 0,
          backhandWinners: 0,
          forehandErrors: 0,
          backhandErrors: 0
        },
        videoUrl: dbMatch.video_url,
        videoThumbnailUrl: dbMatch.video_thumbnail_url,
        videoDurationSeconds: dbMatch.video_duration_seconds,
        videoFileSizeBytes: dbMatch.video_file_size_bytes,
        status: dbMatch.match_status,
        createdAt: dbMatch.created_at,
        updatedAt: dbMatch.updated_at
      };
    }
  }]);
}();
var matchRepository = exports.matchRepository = (cov_q5ev797dr().s[84]++, new MatchRepository());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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