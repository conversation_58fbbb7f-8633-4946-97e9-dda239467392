{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_interopRequireDefault", "exports", "__esModule", "_Animation", "_NativeAnimatedHelper", "DecayAnimation", "_Animation$default", "config", "_this", "_config$deceleration", "_config$isInteraction", "_config$iterations", "_deceleration", "deceleration", "_velocity", "velocity", "_useNativeDriver", "shouldUseNativeDriver", "__isInteraction", "isInteraction", "__iterations", "iterations", "key", "value", "__getNativeAnimationConfig", "type", "start", "fromValue", "onUpdate", "onEnd", "previousAnimation", "animatedValue", "__active", "_lastValue", "_fromValue", "_onUpdate", "__onEnd", "_startTime", "Date", "now", "__startNativeAnimation", "_animationFrame", "requestAnimationFrame", "bind", "Math", "exp", "abs", "__debouncedOnEnd", "finished", "stop", "global", "cancelAnimationFrame", "_default", "module"], "sources": ["DecayAnimation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _Animation = _interopRequireDefault(require(\"./Animation\"));\nvar _NativeAnimatedHelper = require(\"../NativeAnimatedHelper\");\nclass DecayAnimation extends _Animation.default {\n  constructor(config) {\n    var _config$deceleration, _config$isInteraction, _config$iterations;\n    super();\n    this._deceleration = (_config$deceleration = config.deceleration) !== null && _config$deceleration !== void 0 ? _config$deceleration : 0.998;\n    this._velocity = config.velocity;\n    this._useNativeDriver = (0, _NativeAnimatedHelper.shouldUseNativeDriver)(config);\n    this.__isInteraction = (_config$isInteraction = config.isInteraction) !== null && _config$isInteraction !== void 0 ? _config$isInteraction : !this._useNativeDriver;\n    this.__iterations = (_config$iterations = config.iterations) !== null && _config$iterations !== void 0 ? _config$iterations : 1;\n  }\n  __getNativeAnimationConfig() {\n    return {\n      type: 'decay',\n      deceleration: this._deceleration,\n      velocity: this._velocity,\n      iterations: this.__iterations\n    };\n  }\n  start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {\n    this.__active = true;\n    this._lastValue = fromValue;\n    this._fromValue = fromValue;\n    this._onUpdate = onUpdate;\n    this.__onEnd = onEnd;\n    this._startTime = Date.now();\n    if (this._useNativeDriver) {\n      this.__startNativeAnimation(animatedValue);\n    } else {\n      this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n    }\n  }\n  onUpdate() {\n    var now = Date.now();\n    var value = this._fromValue + this._velocity / (1 - this._deceleration) * (1 - Math.exp(-(1 - this._deceleration) * (now - this._startTime)));\n    this._onUpdate(value);\n    if (Math.abs(this._lastValue - value) < 0.1) {\n      this.__debouncedOnEnd({\n        finished: true\n      });\n      return;\n    }\n    this._lastValue = value;\n    if (this.__active) {\n      this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n    }\n  }\n  stop() {\n    super.stop();\n    this.__active = false;\n    global.cancelAnimationFrame(this._animationFrame);\n    this.__debouncedOnEnd({\n      finished: false\n    });\n  }\n}\nvar _default = exports.default = DecayAnimation;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,KAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,IAAAM,UAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAA,SAAAO,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,SAAAa,cAAAb,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAY,CAAA,QAAAC,CAAA,OAAAlB,KAAA,CAAAM,OAAA,MAAAP,gBAAA,CAAAO,OAAA,MAAAW,CAAA,GAAAd,CAAA,CAAAU,SAAA,GAAAV,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAY,CAAA,yBAAAC,CAAA,aAAAf,CAAA,WAAAe,CAAA,CAAAP,KAAA,CAAAN,CAAA,EAAAF,CAAA,OAAAe,CAAA;AAEb,IAAIC,sBAAsB,GAAGxB,OAAO,CAAC,8CAA8C,CAAC,CAACW,OAAO;AAC5Fc,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACd,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIgB,UAAU,GAAGH,sBAAsB,CAACxB,OAAO,cAAc,CAAC,CAAC;AAC/D,IAAI4B,qBAAqB,GAAG5B,OAAO,0BAA0B,CAAC;AAAC,IACzD6B,cAAc,aAAAC,kBAAA;EAClB,SAAAD,eAAYE,MAAM,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAA/B,gBAAA,CAAAU,OAAA,QAAAkB,cAAA;IAClB,IAAII,oBAAoB,EAAEC,qBAAqB,EAAEC,kBAAkB;IACnEH,KAAA,GAAAzB,UAAA,OAAAsB,cAAA;IACAG,KAAA,CAAKI,aAAa,GAAG,CAACH,oBAAoB,GAAGF,MAAM,CAACM,YAAY,MAAM,IAAI,IAAIJ,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAG,KAAK;IAC5ID,KAAA,CAAKM,SAAS,GAAGP,MAAM,CAACQ,QAAQ;IAChCP,KAAA,CAAKQ,gBAAgB,GAAG,CAAC,CAAC,EAAEZ,qBAAqB,CAACa,qBAAqB,EAAEV,MAAM,CAAC;IAChFC,KAAA,CAAKU,eAAe,GAAG,CAACR,qBAAqB,GAAGH,MAAM,CAACY,aAAa,MAAM,IAAI,IAAIT,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAACF,KAAA,CAAKQ,gBAAgB;IACnKR,KAAA,CAAKY,YAAY,GAAG,CAACT,kBAAkB,GAAGJ,MAAM,CAACc,UAAU,MAAM,IAAI,IAAIV,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,CAAC;IAAC,OAAAH,KAAA;EAClI;EAAC,IAAA1B,UAAA,CAAAK,OAAA,EAAAkB,cAAA,EAAAC,kBAAA;EAAA,WAAA5B,aAAA,CAAAS,OAAA,EAAAkB,cAAA;IAAAiB,GAAA;IAAAC,KAAA,EACD,SAAAC,0BAA0BA,CAAA,EAAG;MAC3B,OAAO;QACLC,IAAI,EAAE,OAAO;QACbZ,YAAY,EAAE,IAAI,CAACD,aAAa;QAChCG,QAAQ,EAAE,IAAI,CAACD,SAAS;QACxBO,UAAU,EAAE,IAAI,CAACD;MACnB,CAAC;IACH;EAAC;IAAAE,GAAA;IAAAC,KAAA,EACD,SAAAG,KAAKA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,aAAa,EAAE;MAClE,IAAI,CAACC,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACC,UAAU,GAAGN,SAAS;MAC3B,IAAI,CAACO,UAAU,GAAGP,SAAS;MAC3B,IAAI,CAACQ,SAAS,GAAGP,QAAQ;MACzB,IAAI,CAACQ,OAAO,GAAGP,KAAK;MACpB,IAAI,CAACQ,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,IAAI,IAAI,CAACvB,gBAAgB,EAAE;QACzB,IAAI,CAACwB,sBAAsB,CAACT,aAAa,CAAC;MAC5C,CAAC,MAAM;QACL,IAAI,CAACU,eAAe,GAAGC,qBAAqB,CAAC,IAAI,CAACd,QAAQ,CAACe,IAAI,CAAC,IAAI,CAAC,CAAC;MACxE;IACF;EAAC;IAAArB,GAAA;IAAAC,KAAA,EACD,SAAAK,QAAQA,CAAA,EAAG;MACT,IAAIW,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACpB,IAAIhB,KAAK,GAAG,IAAI,CAACW,UAAU,GAAG,IAAI,CAACpB,SAAS,IAAI,CAAC,GAAG,IAAI,CAACF,aAAa,CAAC,IAAI,CAAC,GAAGgC,IAAI,CAACC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAACjC,aAAa,CAAC,IAAI2B,GAAG,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC,CAAC;MAC7I,IAAI,CAACF,SAAS,CAACZ,KAAK,CAAC;MACrB,IAAIqB,IAAI,CAACE,GAAG,CAAC,IAAI,CAACb,UAAU,GAAGV,KAAK,CAAC,GAAG,GAAG,EAAE;QAC3C,IAAI,CAACwB,gBAAgB,CAAC;UACpBC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF;MACF;MACA,IAAI,CAACf,UAAU,GAAGV,KAAK;MACvB,IAAI,IAAI,CAACS,QAAQ,EAAE;QACjB,IAAI,CAACS,eAAe,GAAGC,qBAAqB,CAAC,IAAI,CAACd,QAAQ,CAACe,IAAI,CAAC,IAAI,CAAC,CAAC;MACxE;IACF;EAAC;IAAArB,GAAA;IAAAC,KAAA,EACD,SAAA0B,IAAIA,CAAA,EAAG;MACLpD,aAAA,CAAAQ,cAAA;MACA,IAAI,CAAC2B,QAAQ,GAAG,KAAK;MACrBkB,MAAM,CAACC,oBAAoB,CAAC,IAAI,CAACV,eAAe,CAAC;MACjD,IAAI,CAACM,gBAAgB,CAAC;QACpBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EAAC;AAAA,EArD0B7C,UAAU,CAAChB,OAAO;AAuD/C,IAAIiE,QAAQ,GAAGnD,OAAO,CAACd,OAAO,GAAGkB,cAAc;AAC/CgD,MAAM,CAACpD,OAAO,GAAGA,OAAO,CAACd,OAAO", "ignoreList": []}