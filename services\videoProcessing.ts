/**
 * Video Processing Service
 * Handles video upload, frame extraction, and MediaPipe pose analysis
 */

import { mediaPipeService } from '@/src/services/ai/MediaPipeService';
import { deepseekService } from './deepseek';

export interface VideoProcessingOptions {
  frameRate?: number; // Frames per second to process
  maxDuration?: number; // Maximum video duration in seconds
  outputFormat?: 'json' | 'csv';
  includeVisualization?: boolean;
}

export interface PoseAnalysisResult {
  frameIndex: number;
  timestamp: number;
  poses: any[];
  movements: any[];
  technicalFeedback: string[];
  confidence: number;
}

export interface VideoAnalysisResult {
  videoMetadata: {
    duration: number;
    frameCount: number;
    resolution: { width: number; height: number };
    frameRate: number;
  };
  poseAnalysis: PoseAnalysisResult[];
  overallAnalysis: {
    dominantMovements: string[];
    technicalScore: number;
    improvements: string[];
    strengths: string[];
  };
  aiCoachingFeedback: string;
}

class VideoProcessingService {
  private isInitialized = false;

  /**
   * Initialize the video processing service
   */
  async initialize(): Promise<void> {
    try {
      await mediaPipeService.initialize();
      this.isInitialized = true;
      console.log('Video processing service initialized');
    } catch (error) {
      console.error('Failed to initialize video processing service:', error);
      throw error;
    }
  }

  /**
   * Process uploaded video file for pose analysis
   */
  async processVideo(
    videoFile: File | Blob,
    options: VideoProcessingOptions = {}
  ): Promise<VideoAnalysisResult> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const {
      frameRate = 10, // Process 10 frames per second
      maxDuration = 60, // Max 60 seconds
      outputFormat = 'json',
      includeVisualization = false
    } = options;

    try {
      console.log('Starting video processing...');
      
      // Extract video metadata
      const videoMetadata = await this.extractVideoMetadata(videoFile);
      
      // Limit processing duration
      const processingDuration = Math.min(videoMetadata.duration, maxDuration);
      
      // Process video with MediaPipe
      const poseAnalysis = await this.analyzeVideoWithMediaPipe(
        videoFile,
        frameRate,
        processingDuration
      );
      
      // Generate overall analysis
      const overallAnalysis = this.generateOverallAnalysis(poseAnalysis);
      
      // Generate AI coaching feedback
      const aiCoachingFeedback = await this.generateAIFeedback(
        poseAnalysis,
        overallAnalysis
      );
      
      return {
        videoMetadata: {
          ...videoMetadata,
          frameCount: poseAnalysis.length,
          frameRate
        },
        poseAnalysis,
        overallAnalysis,
        aiCoachingFeedback
      };
    } catch (error) {
      console.error('Video processing failed:', error);
      throw new Error(`Video processing failed: ${error.message}`);
    }
  }

  /**
   * Extract video metadata
   */
  private async extractVideoMetadata(videoFile: File | Blob): Promise<{
    duration: number;
    resolution: { width: number; height: number };
    frameRate: number;
  }> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      
      video.onloadedmetadata = () => {
        resolve({
          duration: video.duration,
          resolution: {
            width: video.videoWidth,
            height: video.videoHeight
          },
          frameRate: 30 // Default frame rate
        });
      };
      
      video.onerror = () => reject(new Error('Failed to load video metadata'));
      video.src = URL.createObjectURL(videoFile);
    });
  }

  /**
   * Analyze video with MediaPipe pose detection
   */
  private async analyzeVideoWithMediaPipe(
    videoFile: File | Blob,
    frameRate: number,
    duration: number
  ): Promise<PoseAnalysisResult[]> {
    const video = document.createElement('video');
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Could not get canvas context');
    }

    return new Promise((resolve, reject) => {
      video.onloadedmetadata = async () => {
        try {
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;
          
          const results: PoseAnalysisResult[] = [];
          const frameInterval = 1 / frameRate;
          const totalFrames = Math.floor(duration * frameRate);
          
          console.log(`Processing ${totalFrames} frames at ${frameRate} fps`);
          
          for (let i = 0; i < totalFrames; i++) {
            const time = i * frameInterval;
            video.currentTime = time;
            
            // Wait for seek to complete
            await new Promise<void>((seekResolve) => {
              video.onseeked = () => seekResolve();
            });
            
            // Draw frame to canvas
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            
            // Get image data
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            
            // Detect pose with MediaPipe
            const poseResult = await mediaPipeService.detectPose(imageData, i);
            
            if (poseResult) {
              // Analyze tennis movement
              const movementAnalysis = mediaPipeService.analyzeTennisMovement(poseResult);
              
              const frameAnalysis: PoseAnalysisResult = {
                frameIndex: i,
                timestamp: time,
                poses: [poseResult],
                movements: [movementAnalysis],
                technicalFeedback: this.generateFrameFeedback(movementAnalysis),
                confidence: poseResult.confidence
              };
              
              results.push(frameAnalysis);
            }
            
            // Progress logging
            if (i % 10 === 0) {
              console.log(`Processed frame ${i}/${totalFrames}`);
            }
          }
          
          resolve(results);
        } catch (error) {
          reject(error);
        }
      };
      
      video.onerror = () => reject(new Error('Failed to load video'));
      video.src = URL.createObjectURL(videoFile);
    });
  }

  /**
   * Generate frame-level technical feedback
   */
  private generateFrameFeedback(movementAnalysis: any): string[] {
    const feedback: string[] = [];
    
    if (movementAnalysis.technicalMetrics.followThrough === 'none') {
      feedback.push('Incomplete follow-through detected');
    }
    
    if (movementAnalysis.bodyPosition.balance < 0.7) {
      feedback.push('Balance needs improvement');
    }
    
    if (movementAnalysis.technicalMetrics.footwork === 'needs_improvement') {
      feedback.push('Focus on footwork positioning');
    }
    
    return feedback;
  }

  /**
   * Generate overall analysis from all frames
   */
  private generateOverallAnalysis(poseAnalysis: PoseAnalysisResult[]): {
    dominantMovements: string[];
    technicalScore: number;
    improvements: string[];
    strengths: string[];
  } {
    if (poseAnalysis.length === 0) {
      return {
        dominantMovements: [],
        technicalScore: 0,
        improvements: ['No pose data detected'],
        strengths: []
      };
    }

    // Analyze movement patterns
    const movementCounts: { [key: string]: number } = {};
    let totalConfidence = 0;
    const allFeedback: string[] = [];

    poseAnalysis.forEach(frame => {
      frame.movements.forEach(movement => {
        movementCounts[movement.movementType] = (movementCounts[movement.movementType] || 0) + 1;
      });
      totalConfidence += frame.confidence;
      allFeedback.push(...frame.technicalFeedback);
    });

    // Get dominant movements
    const dominantMovements = Object.entries(movementCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([movement]) => movement);

    // Calculate technical score
    const averageConfidence = totalConfidence / poseAnalysis.length;
    const technicalScore = Math.round(averageConfidence * 100);

    // Generate improvements and strengths
    const feedbackCounts: { [key: string]: number } = {};
    allFeedback.forEach(feedback => {
      feedbackCounts[feedback] = (feedbackCounts[feedback] || 0) + 1;
    });

    const improvements = Object.entries(feedbackCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([feedback]) => feedback);

    const strengths = technicalScore > 80 ? 
      ['Good pose detection confidence', 'Consistent movement patterns'] :
      ['Room for improvement in technique'];

    return {
      dominantMovements,
      technicalScore,
      improvements,
      strengths
    };
  }

  /**
   * Generate AI coaching feedback using DeepSeek
   */
  private async generateAIFeedback(
    poseAnalysis: PoseAnalysisResult[],
    overallAnalysis: any
  ): Promise<string> {
    try {
      const prompt = {
        videoDescription: `Tennis video analysis with ${poseAnalysis.length} frames processed`,
        detectedMovements: overallAnalysis.dominantMovements,
        poseKeypoints: poseAnalysis.slice(0, 5).map(frame => frame.poses[0]?.landmarks), // Sample keypoints
        skillLevel: 'intermediate' // Default skill level
      };

      const feedback = await deepseekService.analyzeVideoTechnique(prompt);
      
      return `Technical Score: ${overallAnalysis.technicalScore}/100\n\n` +
             `Dominant Movements: ${overallAnalysis.dominantMovements.join(', ')}\n\n` +
             `AI Analysis:\n${feedback.technicalFeedback.join('\n')}\n\n` +
             `Improvements: ${feedback.improvements.join(', ')}\n\n` +
             `Strengths: ${feedback.strengths.join(', ')}`;
    } catch (error) {
      console.error('Failed to generate AI feedback:', error);
      return `Analysis complete. Technical score: ${overallAnalysis.technicalScore}/100. ` +
             `Dominant movements: ${overallAnalysis.dominantMovements.join(', ')}.`;
    }
  }

  /**
   * Check if service is ready
   */
  isReady(): boolean {
    return this.isInitialized && mediaPipeService.isReady();
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    mediaPipeService.cleanup();
    this.isInitialized = false;
  }
}

// Export singleton instance
export const videoProcessingService = new VideoProcessingService();
