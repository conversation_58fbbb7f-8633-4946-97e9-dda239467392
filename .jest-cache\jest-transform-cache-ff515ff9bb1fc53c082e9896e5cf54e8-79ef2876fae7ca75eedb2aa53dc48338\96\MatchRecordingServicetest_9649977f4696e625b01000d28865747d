a3399b092a8446ea2c193611e32e6927
_getJestObj().mock("../../repositories/MatchRepository");
_getJestObj().mock("../../src/services/video/VideoRecordingService");
_getJestObj().mock("../../utils/performance");
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _MatchRecordingService = require("../../src/services/match/MatchRecordingService");
var _MatchRepository = require("../../repositories/MatchRepository");
var _VideoRecordingService = require("../../src/services/video/VideoRecordingService");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockMatchRepository = _MatchRepository.matchRepository;
var mockVideoRecordingService = _VideoRecordingService.videoRecordingService;
describe('MatchRecordingService', function () {
  beforeEach(function () {
    jest.clearAllMocks();
  });
  describe('startMatch', function () {
    var mockMetadata = {
      userId: 'user123',
      opponentName: 'John Doe',
      matchType: 'friendly',
      matchFormat: 'best_of_3',
      surface: 'hard',
      location: 'Local Tennis Club',
      courtName: 'Court 1',
      weather: 'sunny',
      temperature: 25,
      startTime: new Date().toISOString()
    };
    var mockOptions = {
      enableVideoRecording: true,
      enableStatisticsTracking: true,
      autoSave: true
    };
    it('should start a new match recording session successfully', (0, _asyncToGenerator2.default)(function* () {
      mockMatchRepository.createMatch.mockResolvedValue({
        success: true,
        data: {
          id: 'match123',
          databaseId: 'db123'
        }
      });
      var session = yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
      expect(session).toBeDefined();
      expect(session.match.metadata.userId).toBe('user123');
      expect(session.match.metadata.opponentName).toBe('John Doe');
      expect(session.match.status).toBe('recording');
      expect(mockMatchRepository.createMatch).toHaveBeenCalledWith(expect.objectContaining({
        user_id: 'user123',
        opponent_name: 'John Doe',
        status: 'recording'
      }));
    }));
    it('should handle database save failure gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockMatchRepository.createMatch.mockResolvedValue({
        success: false,
        error: 'Database connection failed'
      });
      yield expect(_MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions)).rejects.toThrow('Database connection failed');
    }));
    it('should validate required metadata fields', (0, _asyncToGenerator2.default)(function* () {
      var invalidMetadata = Object.assign({}, mockMetadata, {
        opponentName: ''
      });
      yield expect(_MatchRecordingService.matchRecordingService.startMatch(invalidMetadata, mockOptions)).rejects.toThrow('Opponent name is required');
    }));
    it('should prevent starting multiple sessions', (0, _asyncToGenerator2.default)(function* () {
      mockMatchRepository.createMatch.mockResolvedValue({
        success: true,
        data: {
          id: 'match123',
          databaseId: 'db123'
        }
      });
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
      yield expect(_MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions)).rejects.toThrow('Another match recording is already in progress');
    }));
  });
  describe('recordPoint', function () {
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      mockMatchRepository.createMatch.mockResolvedValue({
        success: true,
        data: {
          id: 'match123',
          databaseId: 'db123'
        }
      });
      var mockMetadata = {
        userId: 'user123',
        opponentName: 'John Doe',
        matchType: 'friendly',
        matchFormat: 'best_of_3',
        surface: 'hard',
        location: 'Local Tennis Club',
        startTime: new Date().toISOString()
      };
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, {});
    }));
    it('should record a point successfully', (0, _asyncToGenerator2.default)(function* () {
      var gameEvent = {
        eventType: 'winner',
        player: 'user',
        shotType: 'forehand',
        courtPosition: {
          x: 0.5,
          y: 0.3
        },
        timestamp: Date.now()
      };
      var result = yield _MatchRecordingService.matchRecordingService.recordPoint(1, 1, 'user', gameEvent);
      expect(result.success).toBe(true);
      expect(result.updatedScore).toBeDefined();
      expect(result.updatedStatistics).toBeDefined();
    }));
    it('should update statistics correctly', (0, _asyncToGenerator2.default)(function* () {
      var _result$updatedStatis, _result$updatedStatis2;
      var aceEvent = {
        eventType: 'ace',
        player: 'user',
        shotType: 'serve',
        courtPosition: {
          x: 0.5,
          y: 0.1
        },
        timestamp: Date.now()
      };
      var result = yield _MatchRecordingService.matchRecordingService.recordPoint(1, 1, 'user', aceEvent);
      expect(result.success).toBe(true);
      expect((_result$updatedStatis = result.updatedStatistics) == null ? void 0 : _result$updatedStatis.aces).toBe(1);
      expect((_result$updatedStatis2 = result.updatedStatistics) == null ? void 0 : _result$updatedStatis2.totalPointsWon).toBe(1);
    }));
    it('should handle invalid session state', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.stopMatch();
      var gameEvent = {
        eventType: 'winner',
        player: 'user',
        shotType: 'forehand',
        courtPosition: {
          x: 0.5,
          y: 0.3
        },
        timestamp: Date.now()
      };
      var result = yield _MatchRecordingService.matchRecordingService.recordPoint(1, 1, 'user', gameEvent);
      expect(result.success).toBe(false);
      expect(result.error).toBe('No active match recording session');
    }));
  });
  describe('stopMatch', function () {
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      mockMatchRepository.createMatch.mockResolvedValue({
        success: true,
        data: {
          id: 'match123',
          databaseId: 'db123'
        }
      });
      var mockMetadata = {
        userId: 'user123',
        opponentName: 'John Doe',
        matchType: 'friendly',
        matchFormat: 'best_of_3',
        surface: 'hard',
        location: 'Local Tennis Club',
        startTime: new Date().toISOString()
      };
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, {});
    }));
    it('should stop match recording successfully', (0, _asyncToGenerator2.default)(function* () {
      var _result$finalMatch;
      mockMatchRepository.updateMatch.mockResolvedValue({
        success: true
      });
      var result = yield _MatchRecordingService.matchRecordingService.stopMatch();
      expect(result.success).toBe(true);
      expect(result.finalMatch).toBeDefined();
      expect((_result$finalMatch = result.finalMatch) == null ? void 0 : _result$finalMatch.status).toBe('completed');
      expect(mockMatchRepository.updateMatch).toHaveBeenCalledWith(expect.any(String), expect.objectContaining({
        status: 'completed',
        end_time: expect.any(String)
      }));
    }));
    it('should handle database update failure', (0, _asyncToGenerator2.default)(function* () {
      mockMatchRepository.updateMatch.mockResolvedValue({
        success: false,
        error: 'Update failed'
      });
      var result = yield _MatchRecordingService.matchRecordingService.stopMatch();
      expect(result.success).toBe(false);
      expect(result.error).toBe('Update failed');
    }));
    it('should handle no active session', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.stopMatch();
      var result = yield _MatchRecordingService.matchRecordingService.stopMatch();
      expect(result.success).toBe(false);
      expect(result.error).toBe('No active match recording session');
    }));
  });
  describe('pauseMatch', function () {
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      mockMatchRepository.createMatch.mockResolvedValue({
        success: true,
        data: {
          id: 'match123',
          databaseId: 'db123'
        }
      });
      var mockMetadata = {
        userId: 'user123',
        opponentName: 'John Doe',
        matchType: 'friendly',
        matchFormat: 'best_of_3',
        surface: 'hard',
        location: 'Local Tennis Club',
        startTime: new Date().toISOString()
      };
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, {});
    }));
    it('should pause match recording successfully', (0, _asyncToGenerator2.default)(function* () {
      var result = yield _MatchRecordingService.matchRecordingService.pauseMatch();
      expect(result.success).toBe(true);
      var currentSession = _MatchRecordingService.matchRecordingService.getCurrentSession();
      expect(currentSession == null ? void 0 : currentSession.match.status).toBe('paused');
    }));
    it('should resume match recording successfully', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.pauseMatch();
      var result = yield _MatchRecordingService.matchRecordingService.resumeMatch();
      expect(result.success).toBe(true);
      var currentSession = _MatchRecordingService.matchRecordingService.getCurrentSession();
      expect(currentSession == null ? void 0 : currentSession.match.status).toBe('recording');
    }));
  });
  describe('auto-save functionality', function () {
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      mockMatchRepository.createMatch.mockResolvedValue({
        success: true,
        data: {
          id: 'match123',
          databaseId: 'db123'
        }
      });
      var mockMetadata = {
        userId: 'user123',
        opponentName: 'John Doe',
        matchType: 'friendly',
        matchFormat: 'best_of_3',
        surface: 'hard',
        location: 'Local Tennis Club',
        startTime: new Date().toISOString()
      };
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, {
        autoSave: true
      });
    }));
    it('should auto-save match data periodically', (0, _asyncToGenerator2.default)(function* () {
      mockMatchRepository.updateMatch.mockResolvedValue({
        success: true
      });
      var gameEvent = {
        eventType: 'winner',
        player: 'user',
        shotType: 'forehand',
        courtPosition: {
          x: 0.5,
          y: 0.3
        },
        timestamp: Date.now()
      };
      yield _MatchRecordingService.matchRecordingService.recordPoint(1, 1, 'user', gameEvent);
      yield new Promise(function (resolve) {
        return setTimeout(resolve, 100);
      });
      expect(mockMatchRepository.updateMatch).toHaveBeenCalled();
    }));
  });
  describe('offline sync functionality', function () {
    it('should queue updates when offline', (0, _asyncToGenerator2.default)(function* () {
      mockMatchRepository.updateMatch.mockRejectedValue(new Error('Network error'));
      mockMatchRepository.createMatch.mockResolvedValue({
        success: true,
        data: {
          id: 'match123',
          databaseId: 'db123'
        }
      });
      var mockMetadata = {
        userId: 'user123',
        opponentName: 'John Doe',
        matchType: 'friendly',
        matchFormat: 'best_of_3',
        surface: 'hard',
        location: 'Local Tennis Club',
        startTime: new Date().toISOString()
      };
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, {});
      var gameEvent = {
        eventType: 'winner',
        player: 'user',
        shotType: 'forehand',
        courtPosition: {
          x: 0.5,
          y: 0.3
        },
        timestamp: Date.now()
      };
      var result = yield _MatchRecordingService.matchRecordingService.recordPoint(1, 1, 'user', gameEvent);
      expect(result.success).toBe(true);
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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