9d4f18dbd166707714184c8a37e055b5
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_u1vjgm4te() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\social\\messaging.tsx";
  var hash = "8a140255aaac6529b685174f5ad46727204137f3";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\social\\messaging.tsx",
    statementMap: {
      "0": {
        start: {
          line: 31,
          column: 15
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "1": {
        start: {
          line: 48,
          column: 52
        },
        end: {
          line: 91,
          column: 1
        }
      },
      "2": {
        start: {
          line: 49,
          column: 21
        },
        end: {
          line: 52,
          column: 3
        }
      },
      "3": {
        start: {
          line: 50,
          column: 17
        },
        end: {
          line: 50,
          column: 36
        }
      },
      "4": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 51,
          column: 79
        }
      },
      "5": {
        start: {
          line: 54,
          column: 2
        },
        end: {
          line: 90,
          column: 4
        }
      },
      "6": {
        start: {
          line: 94,
          column: 19
        },
        end: {
          line: 94,
          column: 28
        }
      },
      "7": {
        start: {
          line: 95,
          column: 31
        },
        end: {
          line: 95,
          column: 91
        }
      },
      "8": {
        start: {
          line: 96,
          column: 34
        },
        end: {
          line: 96,
          column: 57
        }
      },
      "9": {
        start: {
          line: 97,
          column: 38
        },
        end: {
          line: 97,
          column: 50
        }
      },
      "10": {
        start: {
          line: 98,
          column: 32
        },
        end: {
          line: 98,
          column: 46
        }
      },
      "11": {
        start: {
          line: 99,
          column: 32
        },
        end: {
          line: 99,
          column: 47
        }
      },
      "12": {
        start: {
          line: 100,
          column: 36
        },
        end: {
          line: 100,
          column: 69
        }
      },
      "13": {
        start: {
          line: 101,
          column: 22
        },
        end: {
          line: 101,
          column: 44
        }
      },
      "14": {
        start: {
          line: 103,
          column: 2
        },
        end: {
          line: 106,
          column: 15
        }
      },
      "15": {
        start: {
          line: 104,
          column: 4
        },
        end: {
          line: 104,
          column: 23
        }
      },
      "16": {
        start: {
          line: 105,
          column: 4
        },
        end: {
          line: 105,
          column: 20
        }
      },
      "17": {
        start: {
          line: 108,
          column: 2
        },
        end: {
          line: 113,
          column: 35
        }
      },
      "18": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 112,
          column: 5
        }
      },
      "19": {
        start: {
          line: 111,
          column: 6
        },
        end: {
          line: 111,
          column: 63
        }
      },
      "20": {
        start: {
          line: 115,
          column: 27
        },
        end: {
          line: 128,
          column: 3
        }
      },
      "21": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 116,
          column: 37
        }
      },
      "22": {
        start: {
          line: 116,
          column: 30
        },
        end: {
          line: 116,
          column: 37
        }
      },
      "23": {
        start: {
          line: 118,
          column: 4
        },
        end: {
          line: 127,
          column: 5
        }
      },
      "24": {
        start: {
          line: 119,
          column: 6
        },
        end: {
          line: 119,
          column: 23
        }
      },
      "25": {
        start: {
          line: 120,
          column: 27
        },
        end: {
          line: 120,
          column: 79
        }
      },
      "26": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 121,
          column: 42
        }
      },
      "27": {
        start: {
          line: 123,
          column: 6
        },
        end: {
          line: 123,
          column: 59
        }
      },
      "28": {
        start: {
          line: 124,
          column: 6
        },
        end: {
          line: 124,
          column: 54
        }
      },
      "29": {
        start: {
          line: 126,
          column: 6
        },
        end: {
          line: 126,
          column: 24
        }
      },
      "30": {
        start: {
          line: 130,
          column: 24
        },
        end: {
          line: 139,
          column: 3
        }
      },
      "31": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 131,
          column: 24
        }
      },
      "32": {
        start: {
          line: 131,
          column: 17
        },
        end: {
          line: 131,
          column: 24
        }
      },
      "33": {
        start: {
          line: 133,
          column: 4
        },
        end: {
          line: 138,
          column: 5
        }
      },
      "34": {
        start: {
          line: 134,
          column: 26
        },
        end: {
          line: 134,
          column: 68
        }
      },
      "35": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 135,
          column: 32
        }
      },
      "36": {
        start: {
          line: 137,
          column: 6
        },
        end: {
          line: 137,
          column: 59
        }
      },
      "37": {
        start: {
          line: 141,
          column: 22
        },
        end: {
          line: 164,
          column: 3
        }
      },
      "38": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 142,
          column: 70
        }
      },
      "39": {
        start: {
          line: 142,
          column: 63
        },
        end: {
          line: 142,
          column: 70
        }
      },
      "40": {
        start: {
          line: 144,
          column: 27
        },
        end: {
          line: 144,
          column: 44
        }
      },
      "41": {
        start: {
          line: 145,
          column: 4
        },
        end: {
          line: 145,
          column: 22
        }
      },
      "42": {
        start: {
          line: 146,
          column: 4
        },
        end: {
          line: 146,
          column: 21
        }
      },
      "43": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 163,
          column: 5
        }
      },
      "44": {
        start: {
          line: 149,
          column: 22
        },
        end: {
          line: 149,
          column: 86
        }
      },
      "45": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 156,
          column: 7
        }
      },
      "46": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 151,
          column: 48
        }
      },
      "47": {
        start: {
          line: 151,
          column: 28
        },
        end: {
          line: 151,
          column: 46
        }
      },
      "48": {
        start: {
          line: 153,
          column: 8
        },
        end: {
          line: 155,
          column: 16
        }
      },
      "49": {
        start: {
          line: 154,
          column: 10
        },
        end: {
          line: 154,
          column: 63
        }
      },
      "50": {
        start: {
          line: 158,
          column: 6
        },
        end: {
          line: 158,
          column: 54
        }
      },
      "51": {
        start: {
          line: 159,
          column: 6
        },
        end: {
          line: 159,
          column: 53
        }
      },
      "52": {
        start: {
          line: 160,
          column: 6
        },
        end: {
          line: 160,
          column: 36
        }
      },
      "53": {
        start: {
          line: 162,
          column: 6
        },
        end: {
          line: 162,
          column: 24
        }
      },
      "54": {
        start: {
          line: 166,
          column: 26
        },
        end: {
          line: 196,
          column: 3
        }
      },
      "55": {
        start: {
          line: 167,
          column: 4
        },
        end: {
          line: 195,
          column: 6
        }
      },
      "56": {
        start: {
          line: 175,
          column: 12
        },
        end: {
          line: 175,
          column: 45
        }
      },
      "57": {
        start: {
          line: 175,
          column: 38
        },
        end: {
          line: 175,
          column: 45
        }
      },
      "58": {
        start: {
          line: 177,
          column: 12
        },
        end: {
          line: 191,
          column: 13
        }
      },
      "59": {
        start: {
          line: 178,
          column: 14
        },
        end: {
          line: 187,
          column: 16
        }
      },
      "60": {
        start: {
          line: 188,
          column: 14
        },
        end: {
          line: 188,
          column: 33
        }
      },
      "61": {
        start: {
          line: 190,
          column: 14
        },
        end: {
          line: 190,
          column: 70
        }
      },
      "62": {
        start: {
          line: 198,
          column: 24
        },
        end: {
          line: 211,
          column: 3
        }
      },
      "63": {
        start: {
          line: 199,
          column: 18
        },
        end: {
          line: 199,
          column: 44
        }
      },
      "64": {
        start: {
          line: 200,
          column: 24
        },
        end: {
          line: 200,
          column: 62
        }
      },
      "65": {
        start: {
          line: 201,
          column: 26
        },
        end: {
          line: 202,
          column: 93
        }
      },
      "66": {
        start: {
          line: 204,
          column: 4
        },
        end: {
          line: 210,
          column: 6
        }
      },
      "67": {
        start: {
          line: 213,
          column: 2
        },
        end: {
          line: 305,
          column: 4
        }
      },
      "68": {
        start: {
          line: 222,
          column: 45
        },
        end: {
          line: 222,
          column: 58
        }
      },
      "69": {
        start: {
          line: 257,
          column: 40
        },
        end: {
          line: 257,
          column: 47
        }
      },
      "70": {
        start: {
          line: 261,
          column: 43
        },
        end: {
          line: 261,
          column: 96
        }
      },
      "71": {
        start: {
          line: 308,
          column: 15
        },
        end: {
          line: 507,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 48,
            column: 52
          },
          end: {
            line: 48,
            column: 53
          }
        },
        loc: {
          start: {
            line: 48,
            column: 91
          },
          end: {
            line: 91,
            column: 1
          }
        },
        line: 48
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 49,
            column: 21
          },
          end: {
            line: 49,
            column: 22
          }
        },
        loc: {
          start: {
            line: 49,
            column: 44
          },
          end: {
            line: 52,
            column: 3
          }
        },
        line: 49
      },
      "2": {
        name: "MessagingScreen",
        decl: {
          start: {
            line: 93,
            column: 24
          },
          end: {
            line: 93,
            column: 39
          }
        },
        loc: {
          start: {
            line: 93,
            column: 42
          },
          end: {
            line: 306,
            column: 1
          }
        },
        line: 93
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 103,
            column: 12
          },
          end: {
            line: 103,
            column: 13
          }
        },
        loc: {
          start: {
            line: 103,
            column: 18
          },
          end: {
            line: 106,
            column: 3
          }
        },
        line: 103
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 108,
            column: 12
          },
          end: {
            line: 108,
            column: 13
          }
        },
        loc: {
          start: {
            line: 108,
            column: 18
          },
          end: {
            line: 113,
            column: 3
          }
        },
        line: 108
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 115,
            column: 27
          },
          end: {
            line: 115,
            column: 28
          }
        },
        loc: {
          start: {
            line: 115,
            column: 39
          },
          end: {
            line: 128,
            column: 3
          }
        },
        line: 115
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 130,
            column: 24
          },
          end: {
            line: 130,
            column: 25
          }
        },
        loc: {
          start: {
            line: 130,
            column: 36
          },
          end: {
            line: 139,
            column: 3
          }
        },
        line: 130
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 141,
            column: 22
          },
          end: {
            line: 141,
            column: 23
          }
        },
        loc: {
          start: {
            line: 141,
            column: 34
          },
          end: {
            line: 164,
            column: 3
          }
        },
        line: 141
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 151,
            column: 20
          },
          end: {
            line: 151,
            column: 21
          }
        },
        loc: {
          start: {
            line: 151,
            column: 28
          },
          end: {
            line: 151,
            column: 46
          }
        },
        line: 151
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 153,
            column: 19
          },
          end: {
            line: 153,
            column: 20
          }
        },
        loc: {
          start: {
            line: 153,
            column: 25
          },
          end: {
            line: 155,
            column: 9
          }
        },
        line: 153
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 166,
            column: 26
          },
          end: {
            line: 166,
            column: 27
          }
        },
        loc: {
          start: {
            line: 166,
            column: 32
          },
          end: {
            line: 196,
            column: 3
          }
        },
        line: 166
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 174,
            column: 19
          },
          end: {
            line: 174,
            column: 20
          }
        },
        loc: {
          start: {
            line: 174,
            column: 31
          },
          end: {
            line: 192,
            column: 11
          }
        },
        line: 174
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 198,
            column: 24
          },
          end: {
            line: 198,
            column: 25
          }
        },
        loc: {
          start: {
            line: 198,
            column: 79
          },
          end: {
            line: 211,
            column: 3
          }
        },
        line: 198
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 222,
            column: 39
          },
          end: {
            line: 222,
            column: 40
          }
        },
        loc: {
          start: {
            line: 222,
            column: 45
          },
          end: {
            line: 222,
            column: 58
          }
        },
        line: 222
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 257,
            column: 30
          },
          end: {
            line: 257,
            column: 31
          }
        },
        loc: {
          start: {
            line: 257,
            column: 40
          },
          end: {
            line: 257,
            column: 47
          }
        },
        line: 257
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 261,
            column: 37
          },
          end: {
            line: 261,
            column: 38
          }
        },
        loc: {
          start: {
            line: 261,
            column: 43
          },
          end: {
            line: 261,
            column: 96
          }
        },
        line: 261
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 55,
            column: 43
          },
          end: {
            line: 55,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 51
          },
          end: {
            line: 55,
            column: 68
          }
        }, {
          start: {
            line: 55,
            column: 71
          },
          end: {
            line: 55,
            column: 90
          }
        }],
        line: 55
      },
      "1": {
        loc: {
          start: {
            line: 56,
            column: 42
          },
          end: {
            line: 56,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 56,
            column: 50
          },
          end: {
            line: 56,
            column: 66
          }
        }, {
          start: {
            line: 56,
            column: 69
          },
          end: {
            line: 56,
            column: 87
          }
        }],
        line: 56
      },
      "2": {
        loc: {
          start: {
            line: 57,
            column: 9
          },
          end: {
            line: 61,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 57,
            column: 9
          },
          end: {
            line: 57,
            column: 32
          }
        }, {
          start: {
            line: 58,
            column: 10
          },
          end: {
            line: 60,
            column: 17
          }
        }],
        line: 57
      },
      "3": {
        loc: {
          start: {
            line: 58,
            column: 44
          },
          end: {
            line: 58,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 58,
            column: 52
          },
          end: {
            line: 58,
            column: 66
          }
        }, {
          start: {
            line: 58,
            column: 69
          },
          end: {
            line: 58,
            column: 85
          }
        }],
        line: 58
      },
      "4": {
        loc: {
          start: {
            line: 62,
            column: 9
          },
          end: {
            line: 81,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 62,
            column: 9
          },
          end: {
            line: 62,
            column: 40
          }
        }, {
          start: {
            line: 63,
            column: 10
          },
          end: {
            line: 80,
            column: 17
          }
        }],
        line: 62
      },
      "5": {
        loc: {
          start: {
            line: 64,
            column: 46
          },
          end: {
            line: 64,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 64,
            column: 54
          },
          end: {
            line: 64,
            column: 68
          }
        }, {
          start: {
            line: 64,
            column: 71
          },
          end: {
            line: 64,
            column: 87
          }
        }],
        line: 64
      },
      "6": {
        loc: {
          start: {
            line: 67,
            column: 48
          },
          end: {
            line: 67,
            column: 89
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 67,
            column: 56
          },
          end: {
            line: 67,
            column: 70
          }
        }, {
          start: {
            line: 67,
            column: 73
          },
          end: {
            line: 67,
            column: 89
          }
        }],
        line: 67
      },
      "7": {
        loc: {
          start: {
            line: 70,
            column: 13
          },
          end: {
            line: 79,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 70,
            column: 13
          },
          end: {
            line: 70,
            column: 19
          }
        }, {
          start: {
            line: 71,
            column: 14
          },
          end: {
            line: 78,
            column: 21
          }
        }],
        line: 70
      },
      "8": {
        loc: {
          start: {
            line: 82,
            column: 9
          },
          end: {
            line: 86,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 82,
            column: 9
          },
          end: {
            line: 82,
            column: 22
          }
        }, {
          start: {
            line: 83,
            column: 10
          },
          end: {
            line: 85,
            column: 17
          }
        }],
        line: 82
      },
      "9": {
        loc: {
          start: {
            line: 83,
            column: 42
          },
          end: {
            line: 83,
            column: 93
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 83,
            column: 50
          },
          end: {
            line: 83,
            column: 69
          }
        }, {
          start: {
            line: 83,
            column: 72
          },
          end: {
            line: 83,
            column: 93
          }
        }],
        line: 83
      },
      "10": {
        loc: {
          start: {
            line: 88,
            column: 7
          },
          end: {
            line: 88,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 88,
            column: 7
          },
          end: {
            line: 88,
            column: 22
          }
        }, {
          start: {
            line: 88,
            column: 26
          },
          end: {
            line: 88,
            column: 32
          }
        }, {
          start: {
            line: 88,
            column: 36
          },
          end: {
            line: 88,
            column: 75
          }
        }],
        line: 88
      },
      "11": {
        loc: {
          start: {
            line: 110,
            column: 4
          },
          end: {
            line: 112,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 110,
            column: 4
          },
          end: {
            line: 112,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 110
      },
      "12": {
        loc: {
          start: {
            line: 110,
            column: 8
          },
          end: {
            line: 110,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 110,
            column: 8
          },
          end: {
            line: 110,
            column: 27
          }
        }, {
          start: {
            line: 110,
            column: 31
          },
          end: {
            line: 110,
            column: 37
          }
        }],
        line: 110
      },
      "13": {
        loc: {
          start: {
            line: 111,
            column: 39
          },
          end: {
            line: 111,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 111,
            column: 39
          },
          end: {
            line: 111,
            column: 47
          }
        }, {
          start: {
            line: 111,
            column: 51
          },
          end: {
            line: 111,
            column: 53
          }
        }],
        line: 111
      },
      "14": {
        loc: {
          start: {
            line: 116,
            column: 4
          },
          end: {
            line: 116,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 116,
            column: 4
          },
          end: {
            line: 116,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 116
      },
      "15": {
        loc: {
          start: {
            line: 116,
            column: 8
          },
          end: {
            line: 116,
            column: 28
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 116,
            column: 8
          },
          end: {
            line: 116,
            column: 17
          }
        }, {
          start: {
            line: 116,
            column: 21
          },
          end: {
            line: 116,
            column: 28
          }
        }],
        line: 116
      },
      "16": {
        loc: {
          start: {
            line: 131,
            column: 4
          },
          end: {
            line: 131,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 131,
            column: 4
          },
          end: {
            line: 131,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 131
      },
      "17": {
        loc: {
          start: {
            line: 142,
            column: 4
          },
          end: {
            line: 142,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 4
          },
          end: {
            line: 142,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 142
      },
      "18": {
        loc: {
          start: {
            line: 142,
            column: 8
          },
          end: {
            line: 142,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 142,
            column: 8
          },
          end: {
            line: 142,
            column: 26
          }
        }, {
          start: {
            line: 142,
            column: 30
          },
          end: {
            line: 142,
            column: 39
          }
        }, {
          start: {
            line: 142,
            column: 43
          },
          end: {
            line: 142,
            column: 50
          }
        }, {
          start: {
            line: 142,
            column: 54
          },
          end: {
            line: 142,
            column: 61
          }
        }],
        line: 142
      },
      "19": {
        loc: {
          start: {
            line: 150,
            column: 6
          },
          end: {
            line: 156,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 150,
            column: 6
          },
          end: {
            line: 156,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 150
      },
      "20": {
        loc: {
          start: {
            line: 175,
            column: 12
          },
          end: {
            line: 175,
            column: 45
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 12
          },
          end: {
            line: 175,
            column: 45
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 175
      },
      "21": {
        loc: {
          start: {
            line: 175,
            column: 16
          },
          end: {
            line: 175,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 175,
            column: 16
          },
          end: {
            line: 175,
            column: 25
          }
        }, {
          start: {
            line: 175,
            column: 29
          },
          end: {
            line: 175,
            column: 36
          }
        }],
        line: 175
      },
      "22": {
        loc: {
          start: {
            line: 200,
            column: 24
          },
          end: {
            line: 200,
            column: 62
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 200,
            column: 36
          },
          end: {
            line: 200,
            column: 55
          }
        }, {
          start: {
            line: 200,
            column: 58
          },
          end: {
            line: 200,
            column: 62
          }
        }],
        line: 200
      },
      "23": {
        loc: {
          start: {
            line: 201,
            column: 26
          },
          end: {
            line: 202,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 201,
            column: 26
          },
          end: {
            line: 201,
            column: 38
          }
        }, {
          start: {
            line: 202,
            column: 6
          },
          end: {
            line: 202,
            column: 93
          }
        }],
        line: 201
      },
      "24": {
        loc: {
          start: {
            line: 227,
            column: 48
          },
          end: {
            line: 227,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 227,
            column: 48
          },
          end: {
            line: 227,
            column: 56
          }
        }, {
          start: {
            line: 227,
            column: 60
          },
          end: {
            line: 227,
            column: 79
          }
        }, {
          start: {
            line: 227,
            column: 83
          },
          end: {
            line: 227,
            column: 89
          }
        }],
        line: 227
      },
      "25": {
        loc: {
          start: {
            line: 228,
            column: 15
          },
          end: {
            line: 230,
            column: 15
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 228,
            column: 15
          },
          end: {
            line: 228,
            column: 34
          }
        }, {
          start: {
            line: 229,
            column: 16
          },
          end: {
            line: 229,
            column: 63
          }
        }],
        line: 228
      },
      "26": {
        loc: {
          start: {
            line: 248,
            column: 13
          },
          end: {
            line: 263,
            column: 13
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 249,
            column: 14
          },
          end: {
            line: 251,
            column: 21
          }
        }, {
          start: {
            line: 253,
            column: 14
          },
          end: {
            line: 262,
            column: 16
          }
        }],
        line: 248
      },
      "27": {
        loc: {
          start: {
            line: 268,
            column: 22
          },
          end: {
            line: 268,
            column: 66
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 268,
            column: 46
          },
          end: {
            line: 268,
            column: 55
          }
        }, {
          start: {
            line: 268,
            column: 58
          },
          end: {
            line: 268,
            column: 66
          }
        }],
        line: 268
      },
      "28": {
        loc: {
          start: {
            line: 294,
            column: 43
          },
          end: {
            line: 294,
            column: 103
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 294,
            column: 44
          },
          end: {
            line: 294,
            column: 62
          }
        }, {
          start: {
            line: 294,
            column: 66
          },
          end: {
            line: 294,
            column: 73
          }
        }, {
          start: {
            line: 294,
            column: 78
          },
          end: {
            line: 294,
            column: 103
          }
        }],
        line: 294
      },
      "29": {
        loc: {
          start: {
            line: 296,
            column: 26
          },
          end: {
            line: 296,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 296,
            column: 26
          },
          end: {
            line: 296,
            column: 44
          }
        }, {
          start: {
            line: 296,
            column: 48
          },
          end: {
            line: 296,
            column: 55
          }
        }],
        line: 296
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0, 0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0, 0],
      "29": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8a140255aaac6529b685174f5ad46727204137f3"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_u1vjgm4te = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_u1vjgm4te();
import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, SafeAreaView, FlatList, TextInput, TouchableOpacity, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Send, Camera, MoreVertical, Phone, Video } from 'lucide-react-native';
import { socialService } from "../../services/socialService";
import { useAuth } from "../../contexts/AuthContext";
import ErrorBoundary from "../../components/ui/ErrorBoundary";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_u1vjgm4te().s[0]++, {
  primary: '#23ba16',
  yellow: '#ffe600',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  blue: '#3b82f6',
  green: '#10b981'
});
cov_u1vjgm4te().s[1]++;
var MessageBubble = function MessageBubble(_ref) {
  var message = _ref.message,
    isOwn = _ref.isOwn,
    showTimestamp = _ref.showTimestamp;
  cov_u1vjgm4te().f[0]++;
  cov_u1vjgm4te().s[2]++;
  var formatTime = function formatTime(timestamp) {
    cov_u1vjgm4te().f[1]++;
    var date = (cov_u1vjgm4te().s[3]++, new Date(timestamp));
    cov_u1vjgm4te().s[4]++;
    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  cov_u1vjgm4te().s[5]++;
  return _jsxs(View, {
    style: [styles.messageContainer, isOwn ? (cov_u1vjgm4te().b[0][0]++, styles.ownMessage) : (cov_u1vjgm4te().b[0][1]++, styles.otherMessage)],
    children: [_jsxs(View, {
      style: [styles.messageBubble, isOwn ? (cov_u1vjgm4te().b[1][0]++, styles.ownBubble) : (cov_u1vjgm4te().b[1][1]++, styles.otherBubble)],
      children: [(cov_u1vjgm4te().b[2][0]++, message.type === 'text') && (cov_u1vjgm4te().b[2][1]++, _jsx(Text, {
        style: [styles.messageText, isOwn ? (cov_u1vjgm4te().b[3][0]++, styles.ownText) : (cov_u1vjgm4te().b[3][1]++, styles.otherText)],
        children: message.content
      })), (cov_u1vjgm4te().b[4][0]++, message.type === 'match_invite') && (cov_u1vjgm4te().b[4][1]++, _jsxs(View, {
        style: styles.inviteContainer,
        children: [_jsx(Text, {
          style: [styles.messageText, isOwn ? (cov_u1vjgm4te().b[5][0]++, styles.ownText) : (cov_u1vjgm4te().b[5][1]++, styles.otherText)],
          children: "\uD83C\uDFBE Match Invitation"
        }), _jsx(Text, {
          style: [styles.inviteDetails, isOwn ? (cov_u1vjgm4te().b[6][0]++, styles.ownText) : (cov_u1vjgm4te().b[6][1]++, styles.otherText)],
          children: message.content
        }), (cov_u1vjgm4te().b[7][0]++, !isOwn) && (cov_u1vjgm4te().b[7][1]++, _jsxs(View, {
          style: styles.inviteActions,
          children: [_jsx(TouchableOpacity, {
            style: styles.acceptButton,
            children: _jsx(Text, {
              style: styles.acceptButtonText,
              children: "Accept"
            })
          }), _jsx(TouchableOpacity, {
            style: styles.declineButton,
            children: _jsx(Text, {
              style: styles.declineButtonText,
              children: "Decline"
            })
          })]
        }))]
      })), (cov_u1vjgm4te().b[8][0]++, showTimestamp) && (cov_u1vjgm4te().b[8][1]++, _jsx(Text, {
        style: [styles.timestamp, isOwn ? (cov_u1vjgm4te().b[9][0]++, styles.ownTimestamp) : (cov_u1vjgm4te().b[9][1]++, styles.otherTimestamp)],
        children: formatTime(message.timestamp)
      }))]
    }), (cov_u1vjgm4te().b[10][0]++, !message.isRead) && (cov_u1vjgm4te().b[10][1]++, !isOwn) && (cov_u1vjgm4te().b[10][2]++, _jsx(View, {
      style: styles.unreadIndicator
    }))]
  });
};
export default function MessagingScreen() {
  cov_u1vjgm4te().f[2]++;
  var _ref2 = (cov_u1vjgm4te().s[6]++, useAuth()),
    user = _ref2.user;
  var _ref3 = (cov_u1vjgm4te().s[7]++, useLocalSearchParams()),
    userId = _ref3.userId,
    username = _ref3.username;
  var _ref4 = (cov_u1vjgm4te().s[8]++, useState([])),
    _ref5 = _slicedToArray(_ref4, 2),
    messages = _ref5[0],
    setMessages = _ref5[1];
  var _ref6 = (cov_u1vjgm4te().s[9]++, useState('')),
    _ref7 = _slicedToArray(_ref6, 2),
    newMessage = _ref7[0],
    setNewMessage = _ref7[1];
  var _ref8 = (cov_u1vjgm4te().s[10]++, useState(true)),
    _ref9 = _slicedToArray(_ref8, 2),
    loading = _ref9[0],
    setLoading = _ref9[1];
  var _ref0 = (cov_u1vjgm4te().s[11]++, useState(false)),
    _ref1 = _slicedToArray(_ref0, 2),
    sending = _ref1[0],
    setSending = _ref1[1];
  var _ref10 = (cov_u1vjgm4te().s[12]++, useState(null)),
    _ref11 = _slicedToArray(_ref10, 2),
    otherUser = _ref11[0],
    setOtherUser = _ref11[1];
  var flatListRef = (cov_u1vjgm4te().s[13]++, useRef(null));
  cov_u1vjgm4te().s[14]++;
  useEffect(function () {
    cov_u1vjgm4te().f[3]++;
    cov_u1vjgm4te().s[15]++;
    loadConversation();
    cov_u1vjgm4te().s[16]++;
    loadOtherUser();
  }, [userId]);
  cov_u1vjgm4te().s[17]++;
  useEffect(function () {
    cov_u1vjgm4te().f[4]++;
    cov_u1vjgm4te().s[18]++;
    if ((cov_u1vjgm4te().b[12][0]++, messages.length > 0) && (cov_u1vjgm4te().b[12][1]++, userId)) {
      cov_u1vjgm4te().b[11][0]++;
      cov_u1vjgm4te().s[19]++;
      socialService.markMessagesAsRead((cov_u1vjgm4te().b[13][0]++, user == null ? void 0 : user.id) || (cov_u1vjgm4te().b[13][1]++, ''), userId);
    } else {
      cov_u1vjgm4te().b[11][1]++;
    }
  }, [messages, userId, user == null ? void 0 : user.id]);
  cov_u1vjgm4te().s[20]++;
  var loadConversation = function () {
    var _ref12 = _asyncToGenerator(function* () {
      cov_u1vjgm4te().f[5]++;
      cov_u1vjgm4te().s[21]++;
      if ((cov_u1vjgm4te().b[15][0]++, !(user != null && user.id)) || (cov_u1vjgm4te().b[15][1]++, !userId)) {
        cov_u1vjgm4te().b[14][0]++;
        cov_u1vjgm4te().s[22]++;
        return;
      } else {
        cov_u1vjgm4te().b[14][1]++;
      }
      cov_u1vjgm4te().s[23]++;
      try {
        cov_u1vjgm4te().s[24]++;
        setLoading(true);
        var conversation = (cov_u1vjgm4te().s[25]++, yield socialService.getConversation(user.id, userId));
        cov_u1vjgm4te().s[26]++;
        setMessages(conversation.reverse());
      } catch (error) {
        cov_u1vjgm4te().s[27]++;
        console.error('Failed to load conversation:', error);
        cov_u1vjgm4te().s[28]++;
        Alert.alert('Error', 'Failed to load messages');
      } finally {
        cov_u1vjgm4te().s[29]++;
        setLoading(false);
      }
    });
    return function loadConversation() {
      return _ref12.apply(this, arguments);
    };
  }();
  cov_u1vjgm4te().s[30]++;
  var loadOtherUser = function () {
    var _ref13 = _asyncToGenerator(function* () {
      cov_u1vjgm4te().f[6]++;
      cov_u1vjgm4te().s[31]++;
      if (!userId) {
        cov_u1vjgm4te().b[16][0]++;
        cov_u1vjgm4te().s[32]++;
        return;
      } else {
        cov_u1vjgm4te().b[16][1]++;
      }
      cov_u1vjgm4te().s[33]++;
      try {
        var userProfile = (cov_u1vjgm4te().s[34]++, yield socialService.getUserProfile(userId));
        cov_u1vjgm4te().s[35]++;
        setOtherUser(userProfile);
      } catch (error) {
        cov_u1vjgm4te().s[36]++;
        console.error('Failed to load user profile:', error);
      }
    });
    return function loadOtherUser() {
      return _ref13.apply(this, arguments);
    };
  }();
  cov_u1vjgm4te().s[37]++;
  var sendMessage = function () {
    var _ref14 = _asyncToGenerator(function* () {
      cov_u1vjgm4te().f[7]++;
      cov_u1vjgm4te().s[38]++;
      if ((cov_u1vjgm4te().b[18][0]++, !newMessage.trim()) || (cov_u1vjgm4te().b[18][1]++, !(user != null && user.id)) || (cov_u1vjgm4te().b[18][2]++, !userId) || (cov_u1vjgm4te().b[18][3]++, sending)) {
        cov_u1vjgm4te().b[17][0]++;
        cov_u1vjgm4te().s[39]++;
        return;
      } else {
        cov_u1vjgm4te().b[17][1]++;
      }
      var messageContent = (cov_u1vjgm4te().s[40]++, newMessage.trim());
      cov_u1vjgm4te().s[41]++;
      setNewMessage('');
      cov_u1vjgm4te().s[42]++;
      setSending(true);
      cov_u1vjgm4te().s[43]++;
      try {
        var message = (cov_u1vjgm4te().s[44]++, yield socialService.sendMessage(user.id, userId, messageContent));
        cov_u1vjgm4te().s[45]++;
        if (message) {
          cov_u1vjgm4te().b[19][0]++;
          cov_u1vjgm4te().s[46]++;
          setMessages(function (prev) {
            cov_u1vjgm4te().f[8]++;
            cov_u1vjgm4te().s[47]++;
            return [].concat(_toConsumableArray(prev), [message]);
          });
          cov_u1vjgm4te().s[48]++;
          setTimeout(function () {
            var _flatListRef$current;
            cov_u1vjgm4te().f[9]++;
            cov_u1vjgm4te().s[49]++;
            (_flatListRef$current = flatListRef.current) == null || _flatListRef$current.scrollToEnd({
              animated: true
            });
          }, 100);
        } else {
          cov_u1vjgm4te().b[19][1]++;
        }
      } catch (error) {
        cov_u1vjgm4te().s[50]++;
        console.error('Failed to send message:', error);
        cov_u1vjgm4te().s[51]++;
        Alert.alert('Error', 'Failed to send message');
        cov_u1vjgm4te().s[52]++;
        setNewMessage(messageContent);
      } finally {
        cov_u1vjgm4te().s[53]++;
        setSending(false);
      }
    });
    return function sendMessage() {
      return _ref14.apply(this, arguments);
    };
  }();
  cov_u1vjgm4te().s[54]++;
  var sendMatchInvite = function sendMatchInvite() {
    cov_u1vjgm4te().f[10]++;
    cov_u1vjgm4te().s[55]++;
    Alert.alert('Send Match Invitation', 'Would you like to invite this player to a match?', [{
      text: 'Cancel',
      style: 'cancel'
    }, {
      text: 'Send Invite',
      onPress: function () {
        var _onPress = _asyncToGenerator(function* () {
          cov_u1vjgm4te().f[11]++;
          cov_u1vjgm4te().s[56]++;
          if ((cov_u1vjgm4te().b[21][0]++, !(user != null && user.id)) || (cov_u1vjgm4te().b[21][1]++, !userId)) {
            cov_u1vjgm4te().b[20][0]++;
            cov_u1vjgm4te().s[57]++;
            return;
          } else {
            cov_u1vjgm4te().b[20][1]++;
          }
          cov_u1vjgm4te().s[58]++;
          try {
            cov_u1vjgm4te().s[59]++;
            yield socialService.sendMessage(user.id, userId, 'Would you like to play a match? Let me know when you\'re available!', 'match_invite', {
              type: 'match_invitation',
              proposedTimes: ['Today 6 PM', 'Tomorrow 10 AM']
            });
            cov_u1vjgm4te().s[60]++;
            loadConversation();
          } catch (error) {
            cov_u1vjgm4te().s[61]++;
            Alert.alert('Error', 'Failed to send match invitation');
          }
        });
        function onPress() {
          return _onPress.apply(this, arguments);
        }
        return onPress;
      }()
    }]);
  };
  cov_u1vjgm4te().s[62]++;
  var renderMessage = function renderMessage(_ref15) {
    var item = _ref15.item,
      index = _ref15.index;
    cov_u1vjgm4te().f[12]++;
    var isOwn = (cov_u1vjgm4te().s[63]++, item.senderId === (user == null ? void 0 : user.id));
    var prevMessage = (cov_u1vjgm4te().s[64]++, index > 0 ? (cov_u1vjgm4te().b[22][0]++, messages[index - 1]) : (cov_u1vjgm4te().b[22][1]++, null));
    var showTimestamp = (cov_u1vjgm4te().s[65]++, (cov_u1vjgm4te().b[23][0]++, !prevMessage) || (cov_u1vjgm4te().b[23][1]++, new Date(item.timestamp).getTime() - new Date(prevMessage.timestamp).getTime() > 300000));
    cov_u1vjgm4te().s[66]++;
    return _jsx(MessageBubble, {
      message: item,
      isOwn: isOwn,
      showTimestamp: showTimestamp
    });
  };
  cov_u1vjgm4te().s[67]++;
  return _jsx(ErrorBoundary, {
    context: "MessagingScreen",
    children: _jsx(SafeAreaView, {
      style: styles.container,
      children: _jsxs(LinearGradient, {
        colors: ['#1e3a8a', '#3b82f6', '#60a5fa'],
        style: styles.gradient,
        children: [_jsxs(View, {
          style: styles.header,
          children: [_jsx(TouchableOpacity, {
            onPress: function onPress() {
              cov_u1vjgm4te().f[13]++;
              cov_u1vjgm4te().s[68]++;
              return router.back();
            },
            style: styles.backButton,
            children: _jsx(ArrowLeft, {
              size: 24,
              color: "white"
            })
          }), _jsxs(View, {
            style: styles.headerInfo,
            children: [_jsx(Text, {
              style: styles.headerTitle,
              children: (cov_u1vjgm4te().b[24][0]++, username) || (cov_u1vjgm4te().b[24][1]++, otherUser == null ? void 0 : otherUser.username) || (cov_u1vjgm4te().b[24][2]++, 'User')
            }), (cov_u1vjgm4te().b[25][0]++, otherUser == null ? void 0 : otherUser.isOnline) && (cov_u1vjgm4te().b[25][1]++, _jsx(Text, {
              style: styles.onlineStatus,
              children: "Online"
            }))]
          }), _jsxs(View, {
            style: styles.headerActions,
            children: [_jsx(TouchableOpacity, {
              style: styles.headerButton,
              children: _jsx(Phone, {
                size: 20,
                color: "white"
              })
            }), _jsx(TouchableOpacity, {
              style: styles.headerButton,
              children: _jsx(Video, {
                size: 20,
                color: "white"
              })
            }), _jsx(TouchableOpacity, {
              style: styles.headerButton,
              children: _jsx(MoreVertical, {
                size: 20,
                color: "white"
              })
            })]
          })]
        }), _jsx(View, {
          style: styles.messagesContainer,
          children: loading ? (cov_u1vjgm4te().b[26][0]++, _jsx(View, {
            style: styles.loadingContainer,
            children: _jsx(Text, {
              style: styles.loadingText,
              children: "Loading messages..."
            })
          })) : (cov_u1vjgm4te().b[26][1]++, _jsx(FlatList, {
            ref: flatListRef,
            data: messages,
            renderItem: renderMessage,
            keyExtractor: function keyExtractor(item) {
              cov_u1vjgm4te().f[14]++;
              cov_u1vjgm4te().s[69]++;
              return item.id;
            },
            style: styles.messagesList,
            contentContainerStyle: styles.messagesContent,
            showsVerticalScrollIndicator: false,
            onContentSizeChange: function onContentSizeChange() {
              var _flatListRef$current2;
              cov_u1vjgm4te().f[15]++;
              cov_u1vjgm4te().s[70]++;
              return (_flatListRef$current2 = flatListRef.current) == null ? void 0 : _flatListRef$current2.scrollToEnd({
                animated: false
              });
            }
          }))
        }), _jsx(KeyboardAvoidingView, {
          behavior: Platform.OS === 'ios' ? (cov_u1vjgm4te().b[27][0]++, 'padding') : (cov_u1vjgm4te().b[27][1]++, 'height'),
          style: styles.inputContainer,
          children: _jsxs(View, {
            style: styles.inputRow,
            children: [_jsx(TouchableOpacity, {
              style: styles.attachButton,
              children: _jsx(Camera, {
                size: 24,
                color: colors.gray
              })
            }), _jsx(TextInput, {
              style: styles.textInput,
              value: newMessage,
              onChangeText: setNewMessage,
              placeholder: "Type a message...",
              placeholderTextColor: colors.gray,
              multiline: true,
              maxLength: 1000,
              returnKeyType: "send",
              onSubmitEditing: sendMessage,
              blurOnSubmit: false
            }), _jsx(TouchableOpacity, {
              style: styles.attachButton,
              onPress: sendMatchInvite,
              children: _jsx(Text, {
                style: styles.inviteIcon,
                children: "\uD83C\uDFBE"
              })
            }), _jsx(TouchableOpacity, {
              style: [styles.sendButton, ((cov_u1vjgm4te().b[28][0]++, !newMessage.trim()) || (cov_u1vjgm4te().b[28][1]++, sending)) && (cov_u1vjgm4te().b[28][2]++, styles.sendButtonDisabled)],
              onPress: sendMessage,
              disabled: (cov_u1vjgm4te().b[29][0]++, !newMessage.trim()) || (cov_u1vjgm4te().b[29][1]++, sending),
              children: _jsx(Send, {
                size: 20,
                color: "white"
              })
            })]
          })
        })]
      })
    })
  });
}
var styles = (cov_u1vjgm4te().s[71]++, StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)'
  },
  backButton: {
    padding: 8,
    marginRight: 8
  },
  headerInfo: {
    flex: 1
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.white
  },
  onlineStatus: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.green,
    marginTop: 2
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8
  },
  headerButton: {
    padding: 8
  },
  messagesContainer: {
    flex: 1,
    backgroundColor: colors.white
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: colors.gray
  },
  messagesList: {
    flex: 1
  },
  messagesContent: {
    padding: 16
  },
  messageContainer: {
    marginVertical: 4,
    maxWidth: '80%'
  },
  ownMessage: {
    alignSelf: 'flex-end'
  },
  otherMessage: {
    alignSelf: 'flex-start'
  },
  messageBubble: {
    borderRadius: 18,
    paddingHorizontal: 16,
    paddingVertical: 10,
    position: 'relative'
  },
  ownBubble: {
    backgroundColor: colors.primary
  },
  otherBubble: {
    backgroundColor: colors.lightGray
  },
  messageText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    lineHeight: 20
  },
  ownText: {
    color: colors.white
  },
  otherText: {
    color: colors.dark
  },
  timestamp: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    marginTop: 4,
    opacity: 0.7
  },
  ownTimestamp: {
    color: colors.white,
    textAlign: 'right'
  },
  otherTimestamp: {
    color: colors.gray
  },
  unreadIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.primary,
    position: 'absolute',
    right: -4,
    bottom: 4
  },
  inviteContainer: {
    minWidth: 200
  },
  inviteDetails: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginTop: 4,
    opacity: 0.8
  },
  inviteActions: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 12
  },
  acceptButton: {
    backgroundColor: colors.green,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    flex: 1
  },
  acceptButtonText: {
    color: colors.white,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    textAlign: 'center'
  },
  declineButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.white,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    flex: 1
  },
  declineButtonText: {
    color: colors.white,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    textAlign: 'center'
  },
  inputContainer: {
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.lightGray
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12
  },
  attachButton: {
    padding: 8
  },
  inviteIcon: {
    fontSize: 20
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: colors.dark,
    maxHeight: 100
  },
  sendButton: {
    backgroundColor: colors.primary,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center'
  },
  sendButtonDisabled: {
    backgroundColor: colors.gray,
    opacity: 0.5
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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