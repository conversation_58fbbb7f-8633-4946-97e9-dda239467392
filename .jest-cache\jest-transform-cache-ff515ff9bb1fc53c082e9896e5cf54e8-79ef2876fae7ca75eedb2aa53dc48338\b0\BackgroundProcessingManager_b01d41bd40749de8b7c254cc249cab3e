5ddf90067d32ee0585f3cbf4f3fab5db
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_1z72a6923t() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\native\\BackgroundProcessingManager.ts";
  var hash = "2229c1c602b4fc434da1ccc30b5c2a296cb95e14";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\native\\BackgroundProcessingManager.ts",
    statementMap: {
      "0": {
        start: {
          line: 95,
          column: 57
        },
        end: {
          line: 95,
          column: 66
        }
      },
      "1": {
        start: {
          line: 96,
          column: 32
        },
        end: {
          line: 96,
          column: 34
        }
      },
      "2": {
        start: {
          line: 97,
          column: 52
        },
        end: {
          line: 97,
          column: 61
        }
      },
      "3": {
        start: {
          line: 98,
          column: 46
        },
        end: {
          line: 98,
          column: 48
        }
      },
      "4": {
        start: {
          line: 103,
          column: 65
        },
        end: {
          line: 167,
          column: 3
        }
      },
      "5": {
        start: {
          line: 170,
          column: 4
        },
        end: {
          line: 170,
          column: 57
        }
      },
      "6": {
        start: {
          line: 171,
          column: 4
        },
        end: {
          line: 171,
          column: 45
        }
      },
      "7": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 172,
          column: 49
        }
      },
      "8": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 174,
          column: 42
        }
      },
      "9": {
        start: {
          line: 181,
          column: 4
        },
        end: {
          line: 200,
          column: 5
        }
      },
      "10": {
        start: {
          line: 183,
          column: 6
        },
        end: {
          line: 183,
          column: 44
        }
      },
      "11": {
        start: {
          line: 186,
          column: 6
        },
        end: {
          line: 186,
          column: 41
        }
      },
      "12": {
        start: {
          line: 189,
          column: 6
        },
        end: {
          line: 189,
          column: 33
        }
      },
      "13": {
        start: {
          line: 192,
          column: 6
        },
        end: {
          line: 192,
          column: 37
        }
      },
      "14": {
        start: {
          line: 195,
          column: 6
        },
        end: {
          line: 195,
          column: 34
        }
      },
      "15": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 197,
          column: 76
        }
      },
      "16": {
        start: {
          line: 199,
          column: 6
        },
        end: {
          line: 199,
          column: 82
        }
      },
      "17": {
        start: {
          line: 207,
          column: 37
        },
        end: {
          line: 215,
          column: 5
        }
      },
      "18": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 217,
          column: 48
        }
      },
      "19": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 222,
          column: 5
        }
      },
      "20": {
        start: {
          line: 221,
          column: 6
        },
        end: {
          line: 221,
          column: 33
        }
      },
      "21": {
        start: {
          line: 224,
          column: 4
        },
        end: {
          line: 224,
          column: 58
        }
      },
      "22": {
        start: {
          line: 225,
          column: 4
        },
        end: {
          line: 225,
          column: 19
        }
      },
      "23": {
        start: {
          line: 232,
          column: 17
        },
        end: {
          line: 232,
          column: 49
        }
      },
      "24": {
        start: {
          line: 233,
          column: 4
        },
        end: {
          line: 236,
          column: 5
        }
      },
      "25": {
        start: {
          line: 234,
          column: 6
        },
        end: {
          line: 234,
          column: 48
        }
      },
      "26": {
        start: {
          line: 235,
          column: 6
        },
        end: {
          line: 235,
          column: 13
        }
      },
      "27": {
        start: {
          line: 238,
          column: 4
        },
        end: {
          line: 244,
          column: 5
        }
      },
      "28": {
        start: {
          line: 239,
          column: 6
        },
        end: {
          line: 241,
          column: 16
        }
      },
      "29": {
        start: {
          line: 240,
          column: 8
        },
        end: {
          line: 240,
          column: 32
        }
      },
      "30": {
        start: {
          line: 243,
          column: 6
        },
        end: {
          line: 243,
          column: 30
        }
      },
      "31": {
        start: {
          line: 251,
          column: 17
        },
        end: {
          line: 251,
          column: 49
        }
      },
      "32": {
        start: {
          line: 252,
          column: 4
        },
        end: {
          line: 255,
          column: 5
        }
      },
      "33": {
        start: {
          line: 253,
          column: 6
        },
        end: {
          line: 253,
          column: 48
        }
      },
      "34": {
        start: {
          line: 254,
          column: 6
        },
        end: {
          line: 254,
          column: 18
        }
      },
      "35": {
        start: {
          line: 257,
          column: 4
        },
        end: {
          line: 257,
          column: 40
        }
      },
      "36": {
        start: {
          line: 278,
          column: 28
        },
        end: {
          line: 278,
          column: 56
        }
      },
      "37": {
        start: {
          line: 279,
          column: 33
        },
        end: {
          line: 279,
          column: 84
        }
      },
      "38": {
        start: {
          line: 279,
          column: 67
        },
        end: {
          line: 279,
          column: 76
        }
      },
      "39": {
        start: {
          line: 281,
          column: 33
        },
        end: {
          line: 283,
          column: 9
        }
      },
      "40": {
        start: {
          line: 282,
          column: 49
        },
        end: {
          line: 282,
          column: 81
        }
      },
      "41": {
        start: {
          line: 285,
          column: 24
        },
        end: {
          line: 285,
          column: 96
        }
      },
      "42": {
        start: {
          line: 288,
          column: 57
        },
        end: {
          line: 288,
          column: 59
        }
      },
      "43": {
        start: {
          line: 289,
          column: 4
        },
        end: {
          line: 294,
          column: 7
        }
      },
      "44": {
        start: {
          line: 290,
          column: 19
        },
        end: {
          line: 290,
          column: 61
        }
      },
      "45": {
        start: {
          line: 291,
          column: 6
        },
        end: {
          line: 293,
          column: 7
        }
      },
      "46": {
        start: {
          line: 292,
          column: 8
        },
        end: {
          line: 292,
          column: 85
        }
      },
      "47": {
        start: {
          line: 296,
          column: 4
        },
        end: {
          line: 305,
          column: 6
        }
      },
      "48": {
        start: {
          line: 312,
          column: 4
        },
        end: {
          line: 325,
          column: 5
        }
      },
      "49": {
        start: {
          line: 313,
          column: 32
        },
        end: {
          line: 313,
          column: 65
        }
      },
      "50": {
        start: {
          line: 314,
          column: 30
        },
        end: {
          line: 314,
          column: 75
        }
      },
      "51": {
        start: {
          line: 316,
          column: 6
        },
        end: {
          line: 322,
          column: 7
        }
      },
      "52": {
        start: {
          line: 317,
          column: 8
        },
        end: {
          line: 317,
          column: 80
        }
      },
      "53": {
        start: {
          line: 318,
          column: 8
        },
        end: {
          line: 318,
          column: 47
        }
      },
      "54": {
        start: {
          line: 321,
          column: 8
        },
        end: {
          line: 321,
          column: 50
        }
      },
      "55": {
        start: {
          line: 324,
          column: 6
        },
        end: {
          line: 324,
          column: 70
        }
      },
      "56": {
        start: {
          line: 331,
          column: 4
        },
        end: {
          line: 334,
          column: 5
        }
      },
      "57": {
        start: {
          line: 332,
          column: 6
        },
        end: {
          line: 332,
          column: 34
        }
      },
      "58": {
        start: {
          line: 333,
          column: 6
        },
        end: {
          line: 333,
          column: 27
        }
      },
      "59": {
        start: {
          line: 338,
          column: 4
        },
        end: {
          line: 352,
          column: 7
        }
      },
      "60": {
        start: {
          line: 339,
          column: 20
        },
        end: {
          line: 339,
          column: 47
        }
      },
      "61": {
        start: {
          line: 340,
          column: 20
        },
        end: {
          line: 340,
          column: 47
        }
      },
      "62": {
        start: {
          line: 342,
          column: 6
        },
        end: {
          line: 342,
          column: 37
        }
      },
      "63": {
        start: {
          line: 342,
          column: 28
        },
        end: {
          line: 342,
          column: 37
        }
      },
      "64": {
        start: {
          line: 345,
          column: 28
        },
        end: {
          line: 345,
          column: 71
        }
      },
      "65": {
        start: {
          line: 346,
          column: 27
        },
        end: {
          line: 346,
          column: 88
        }
      },
      "66": {
        start: {
          line: 348,
          column: 6
        },
        end: {
          line: 348,
          column: 50
        }
      },
      "67": {
        start: {
          line: 348,
          column: 30
        },
        end: {
          line: 348,
          column: 50
        }
      },
      "68": {
        start: {
          line: 351,
          column: 6
        },
        end: {
          line: 351,
          column: 63
        }
      },
      "69": {
        start: {
          line: 356,
          column: 37
        },
        end: {
          line: 362,
          column: 5
        }
      },
      "70": {
        start: {
          line: 364,
          column: 4
        },
        end: {
          line: 415,
          column: 5
        }
      },
      "71": {
        start: {
          line: 366,
          column: 6
        },
        end: {
          line: 368,
          column: 7
        }
      },
      "72": {
        start: {
          line: 367,
          column: 8
        },
        end: {
          line: 367,
          column: 61
        }
      },
      "73": {
        start: {
          line: 371,
          column: 27
        },
        end: {
          line: 371,
          column: 65
        }
      },
      "74": {
        start: {
          line: 374,
          column: 6
        },
        end: {
          line: 374,
          column: 47
        }
      },
      "75": {
        start: {
          line: 375,
          column: 21
        },
        end: {
          line: 375,
          column: 42
        }
      },
      "76": {
        start: {
          line: 378,
          column: 25
        },
        end: {
          line: 378,
          column: 63
        }
      },
      "77": {
        start: {
          line: 379,
          column: 6
        },
        end: {
          line: 384,
          column: 8
        }
      },
      "78": {
        start: {
          line: 386,
          column: 6
        },
        end: {
          line: 386,
          column: 31
        }
      },
      "79": {
        start: {
          line: 387,
          column: 6
        },
        end: {
          line: 387,
          column: 32
        }
      },
      "80": {
        start: {
          line: 388,
          column: 6
        },
        end: {
          line: 388,
          column: 37
        }
      },
      "81": {
        start: {
          line: 391,
          column: 6
        },
        end: {
          line: 391,
          column: 32
        }
      },
      "82": {
        start: {
          line: 392,
          column: 6
        },
        end: {
          line: 392,
          column: 46
        }
      },
      "83": {
        start: {
          line: 393,
          column: 6
        },
        end: {
          line: 394,
          column: 94
        }
      },
      "84": {
        start: {
          line: 396,
          column: 6
        },
        end: {
          line: 396,
          column: 60
        }
      },
      "85": {
        start: {
          line: 399,
          column: 6
        },
        end: {
          line: 399,
          column: 32
        }
      },
      "86": {
        start: {
          line: 400,
          column: 6
        },
        end: {
          line: 400,
          column: 38
        }
      },
      "87": {
        start: {
          line: 401,
          column: 6
        },
        end: {
          line: 401,
          column: 37
        }
      },
      "88": {
        start: {
          line: 403,
          column: 6
        },
        end: {
          line: 403,
          column: 32
        }
      },
      "89": {
        start: {
          line: 404,
          column: 6
        },
        end: {
          line: 404,
          column: 46
        }
      },
      "90": {
        start: {
          line: 406,
          column: 6
        },
        end: {
          line: 406,
          column: 64
        }
      },
      "91": {
        start: {
          line: 408,
          column: 6
        },
        end: {
          line: 408,
          column: 39
        }
      },
      "92": {
        start: {
          line: 409,
          column: 6
        },
        end: {
          line: 409,
          column: 44
        }
      },
      "93": {
        start: {
          line: 412,
          column: 6
        },
        end: {
          line: 414,
          column: 7
        }
      },
      "94": {
        start: {
          line: 413,
          column: 8
        },
        end: {
          line: 413,
          column: 38
        }
      },
      "95": {
        start: {
          line: 417,
          column: 4
        },
        end: {
          line: 417,
          column: 21
        }
      },
      "96": {
        start: {
          line: 422,
          column: 23
        },
        end: {
          line: 422,
          column: 66
        }
      },
      "97": {
        start: {
          line: 425,
          column: 4
        },
        end: {
          line: 427,
          column: 5
        }
      },
      "98": {
        start: {
          line: 426,
          column: 6
        },
        end: {
          line: 426,
          column: 19
        }
      },
      "99": {
        start: {
          line: 430,
          column: 4
        },
        end: {
          line: 432,
          column: 5
        }
      },
      "100": {
        start: {
          line: 431,
          column: 6
        },
        end: {
          line: 431,
          column: 19
        }
      },
      "101": {
        start: {
          line: 435,
          column: 4
        },
        end: {
          line: 437,
          column: 5
        }
      },
      "102": {
        start: {
          line: 436,
          column: 6
        },
        end: {
          line: 436,
          column: 19
        }
      },
      "103": {
        start: {
          line: 440,
          column: 4
        },
        end: {
          line: 442,
          column: 5
        }
      },
      "104": {
        start: {
          line: 441,
          column: 6
        },
        end: {
          line: 441,
          column: 19
        }
      },
      "105": {
        start: {
          line: 445,
          column: 25
        },
        end: {
          line: 445,
          column: 63
        }
      },
      "106": {
        start: {
          line: 446,
          column: 4
        },
        end: {
          line: 448,
          column: 5
        }
      },
      "107": {
        start: {
          line: 447,
          column: 6
        },
        end: {
          line: 447,
          column: 19
        }
      },
      "108": {
        start: {
          line: 450,
          column: 4
        },
        end: {
          line: 452,
          column: 5
        }
      },
      "109": {
        start: {
          line: 451,
          column: 6
        },
        end: {
          line: 451,
          column: 19
        }
      },
      "110": {
        start: {
          line: 454,
          column: 4
        },
        end: {
          line: 454,
          column: 16
        }
      },
      "111": {
        start: {
          line: 458,
          column: 4
        },
        end: {
          line: 458,
          column: 55
        }
      },
      "112": {
        start: {
          line: 463,
          column: 27
        },
        end: {
          line: 487,
          column: 6
        }
      },
      "113": {
        start: {
          line: 464,
          column: 18
        },
        end: {
          line: 464,
          column: 19
        }
      },
      "114": {
        start: {
          line: 467,
          column: 6
        },
        end: {
          line: 469,
          column: 7
        }
      },
      "115": {
        start: {
          line: 468,
          column: 8
        },
        end: {
          line: 468,
          column: 20
        }
      },
      "116": {
        start: {
          line: 472,
          column: 6
        },
        end: {
          line: 474,
          column: 7
        }
      },
      "117": {
        start: {
          line: 473,
          column: 8
        },
        end: {
          line: 473,
          column: 20
        }
      },
      "118": {
        start: {
          line: 477,
          column: 6
        },
        end: {
          line: 479,
          column: 7
        }
      },
      "119": {
        start: {
          line: 478,
          column: 8
        },
        end: {
          line: 478,
          column: 20
        }
      },
      "120": {
        start: {
          line: 482,
          column: 6
        },
        end: {
          line: 484,
          column: 7
        }
      },
      "121": {
        start: {
          line: 483,
          column: 8
        },
        end: {
          line: 483,
          column: 20
        }
      },
      "122": {
        start: {
          line: 486,
          column: 6
        },
        end: {
          line: 486,
          column: 33
        }
      },
      "123": {
        start: {
          line: 490,
          column: 4
        },
        end: {
          line: 490,
          column: 53
        }
      },
      "124": {
        start: {
          line: 490,
          column: 34
        },
        end: {
          line: 490,
          column: 51
        }
      },
      "125": {
        start: {
          line: 491,
          column: 4
        },
        end: {
          line: 491,
          column: 38
        }
      },
      "126": {
        start: {
          line: 496,
          column: 26
        },
        end: {
          line: 496,
          column: 72
        }
      },
      "127": {
        start: {
          line: 498,
          column: 4
        },
        end: {
          line: 505,
          column: 5
        }
      },
      "128": {
        start: {
          line: 499,
          column: 29
        },
        end: {
          line: 499,
          column: 85
        }
      },
      "129": {
        start: {
          line: 501,
          column: 6
        },
        end: {
          line: 504,
          column: 7
        }
      },
      "130": {
        start: {
          line: 503,
          column: 8
        },
        end: {
          line: 503,
          column: 73
        }
      },
      "131": {
        start: {
          line: 510,
          column: 4
        },
        end: {
          line: 512,
          column: 13
        }
      },
      "132": {
        start: {
          line: 511,
          column: 6
        },
        end: {
          line: 511,
          column: 30
        }
      },
      "133": {
        start: {
          line: 515,
          column: 4
        },
        end: {
          line: 517,
          column: 14
        }
      },
      "134": {
        start: {
          line: 516,
          column: 6
        },
        end: {
          line: 516,
          column: 40
        }
      },
      "135": {
        start: {
          line: 521,
          column: 4
        },
        end: {
          line: 521,
          column: 44
        }
      },
      "136": {
        start: {
          line: 521,
          column: 37
        },
        end: {
          line: 521,
          column: 44
        }
      },
      "137": {
        start: {
          line: 523,
          column: 26
        },
        end: {
          line: 523,
          column: 72
        }
      },
      "138": {
        start: {
          line: 524,
          column: 27
        },
        end: {
          line: 524,
          column: 64
        }
      },
      "139": {
        start: {
          line: 526,
          column: 4
        },
        end: {
          line: 526,
          column: 36
        }
      },
      "140": {
        start: {
          line: 526,
          column: 29
        },
        end: {
          line: 526,
          column: 36
        }
      },
      "141": {
        start: {
          line: 529,
          column: 27
        },
        end: {
          line: 529,
          column: 67
        }
      },
      "142": {
        start: {
          line: 531,
          column: 4
        },
        end: {
          line: 539,
          column: 5
        }
      },
      "143": {
        start: {
          line: 532,
          column: 19
        },
        end: {
          line: 532,
          column: 51
        }
      },
      "144": {
        start: {
          line: 533,
          column: 6
        },
        end: {
          line: 538,
          column: 7
        }
      },
      "145": {
        start: {
          line: 534,
          column: 8
        },
        end: {
          line: 534,
          column: 31
        }
      },
      "146": {
        start: {
          line: 537,
          column: 8
        },
        end: {
          line: 537,
          column: 36
        }
      },
      "147": {
        start: {
          line: 543,
          column: 4
        },
        end: {
          line: 547,
          column: 7
        }
      },
      "148": {
        start: {
          line: 544,
          column: 6
        },
        end: {
          line: 544,
          column: 59
        }
      },
      "149": {
        start: {
          line: 546,
          column: 6
        },
        end: {
          line: 546,
          column: 40
        }
      },
      "150": {
        start: {
          line: 552,
          column: 25
        },
        end: {
          line: 610,
          column: 5
        }
      },
      "151": {
        start: {
          line: 578,
          column: 10
        },
        end: {
          line: 578,
          column: 60
        }
      },
      "152": {
        start: {
          line: 579,
          column: 10
        },
        end: {
          line: 579,
          column: 35
        }
      },
      "153": {
        start: {
          line: 607,
          column: 10
        },
        end: {
          line: 607,
          column: 46
        }
      },
      "154": {
        start: {
          line: 612,
          column: 4
        },
        end: {
          line: 612,
          column: 58
        }
      },
      "155": {
        start: {
          line: 612,
          column: 33
        },
        end: {
          line: 612,
          column: 56
        }
      },
      "156": {
        start: {
          line: 621,
          column: 4
        },
        end: {
          line: 621,
          column: 46
        }
      },
      "157": {
        start: {
          line: 629,
          column: 25
        },
        end: {
          line: 629,
          column: 70
        }
      },
      "158": {
        start: {
          line: 630,
          column: 30
        },
        end: {
          line: 635,
          column: 3
        }
      },
      "159": {
        start: {
          line: 639,
          column: 4
        },
        end: {
          line: 641,
          column: 14
        }
      },
      "160": {
        start: {
          line: 640,
          column: 6
        },
        end: {
          line: 640,
          column: 33
        }
      },
      "161": {
        start: {
          line: 643,
          column: 4
        },
        end: {
          line: 643,
          column: 44
        }
      },
      "162": {
        start: {
          line: 647,
          column: 4
        },
        end: {
          line: 647,
          column: 36
        }
      },
      "163": {
        start: {
          line: 651,
          column: 4
        },
        end: {
          line: 651,
          column: 41
        }
      },
      "164": {
        start: {
          line: 656,
          column: 4
        },
        end: {
          line: 661,
          column: 6
        }
      },
      "165": {
        start: {
          line: 664,
          column: 4
        },
        end: {
          line: 669,
          column: 6
        }
      },
      "166": {
        start: {
          line: 674,
          column: 43
        },
        end: {
          line: 674,
          column: 76
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 169,
            column: 2
          },
          end: {
            line: 169,
            column: 3
          }
        },
        loc: {
          start: {
            line: 169,
            column: 16
          },
          end: {
            line: 175,
            column: 3
          }
        },
        line: 169
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 180,
            column: 2
          },
          end: {
            line: 180,
            column: 3
          }
        },
        loc: {
          start: {
            line: 180,
            column: 64
          },
          end: {
            line: 201,
            column: 3
          }
        },
        line: 180
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 206,
            column: 2
          },
          end: {
            line: 206,
            column: 3
          }
        },
        loc: {
          start: {
            line: 206,
            column: 64
          },
          end: {
            line: 226,
            column: 3
          }
        },
        line: 206
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 231,
            column: 2
          },
          end: {
            line: 231,
            column: 3
          }
        },
        loc: {
          start: {
            line: 231,
            column: 56
          },
          end: {
            line: 245,
            column: 3
          }
        },
        line: 231
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 239,
            column: 17
          },
          end: {
            line: 239,
            column: 18
          }
        },
        loc: {
          start: {
            line: 239,
            column: 23
          },
          end: {
            line: 241,
            column: 7
          }
        },
        line: 239
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 250,
            column: 2
          },
          end: {
            line: 250,
            column: 3
          }
        },
        loc: {
          start: {
            line: 250,
            column: 78
          },
          end: {
            line: 258,
            column: 3
          }
        },
        line: 250
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 263,
            column: 2
          },
          end: {
            line: 263,
            column: 3
          }
        },
        loc: {
          start: {
            line: 277,
            column: 4
          },
          end: {
            line: 306,
            column: 3
          }
        },
        line: 277
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 279,
            column: 62
          },
          end: {
            line: 279,
            column: 63
          }
        },
        loc: {
          start: {
            line: 279,
            column: 67
          },
          end: {
            line: 279,
            column: 76
          }
        },
        line: 279
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 282,
            column: 37
          },
          end: {
            line: 282,
            column: 38
          }
        },
        loc: {
          start: {
            line: 282,
            column: 49
          },
          end: {
            line: 282,
            column: 81
          }
        },
        line: 282
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 289,
            column: 34
          },
          end: {
            line: 289,
            column: 35
          }
        },
        loc: {
          start: {
            line: 289,
            column: 47
          },
          end: {
            line: 294,
            column: 5
          }
        },
        line: 289
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 311,
            column: 2
          },
          end: {
            line: 311,
            column: 3
          }
        },
        loc: {
          start: {
            line: 311,
            column: 52
          },
          end: {
            line: 326,
            column: 3
          }
        },
        line: 311
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 330,
            column: 2
          },
          end: {
            line: 330,
            column: 3
          }
        },
        loc: {
          start: {
            line: 330,
            column: 43
          },
          end: {
            line: 335,
            column: 3
          }
        },
        line: 330
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 337,
            column: 2
          },
          end: {
            line: 337,
            column: 3
          }
        },
        loc: {
          start: {
            line: 337,
            column: 32
          },
          end: {
            line: 353,
            column: 3
          }
        },
        line: 337
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 338,
            column: 24
          },
          end: {
            line: 338,
            column: 25
          }
        },
        loc: {
          start: {
            line: 338,
            column: 34
          },
          end: {
            line: 352,
            column: 5
          }
        },
        line: 338
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 355,
            column: 2
          },
          end: {
            line: 355,
            column: 3
          }
        },
        loc: {
          start: {
            line: 355,
            column: 74
          },
          end: {
            line: 418,
            column: 3
          }
        },
        line: 355
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 420,
            column: 2
          },
          end: {
            line: 420,
            column: 3
          }
        },
        loc: {
          start: {
            line: 420,
            column: 56
          },
          end: {
            line: 455,
            column: 3
          }
        },
        line: 420
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 457,
            column: 2
          },
          end: {
            line: 457,
            column: 3
          }
        },
        loc: {
          start: {
            line: 457,
            column: 53
          },
          end: {
            line: 459,
            column: 3
          }
        },
        line: 457
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 461,
            column: 2
          },
          end: {
            line: 461,
            column: 3
          }
        },
        loc: {
          start: {
            line: 461,
            column: 69
          },
          end: {
            line: 492,
            column: 3
          }
        },
        line: 461
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 463,
            column: 58
          },
          end: {
            line: 463,
            column: 59
          }
        },
        loc: {
          start: {
            line: 463,
            column: 70
          },
          end: {
            line: 487,
            column: 5
          }
        },
        line: 463
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 490,
            column: 24
          },
          end: {
            line: 490,
            column: 25
          }
        },
        loc: {
          start: {
            line: 490,
            column: 34
          },
          end: {
            line: 490,
            column: 51
          }
        },
        line: 490
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 494,
            column: 2
          },
          end: {
            line: 494,
            column: 3
          }
        },
        loc: {
          start: {
            line: 494,
            column: 62
          },
          end: {
            line: 506,
            column: 3
          }
        },
        line: 494
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 508,
            column: 2
          },
          end: {
            line: 508,
            column: 3
          }
        },
        loc: {
          start: {
            line: 508,
            column: 38
          },
          end: {
            line: 518,
            column: 3
          }
        },
        line: 508
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 510,
            column: 16
          },
          end: {
            line: 510,
            column: 17
          }
        },
        loc: {
          start: {
            line: 510,
            column: 22
          },
          end: {
            line: 512,
            column: 5
          }
        },
        line: 510
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 515,
            column: 16
          },
          end: {
            line: 515,
            column: 17
          }
        },
        loc: {
          start: {
            line: 515,
            column: 22
          },
          end: {
            line: 517,
            column: 5
          }
        },
        line: 515
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 520,
            column: 2
          },
          end: {
            line: 520,
            column: 3
          }
        },
        loc: {
          start: {
            line: 520,
            column: 50
          },
          end: {
            line: 540,
            column: 3
          }
        },
        line: 520
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 542,
            column: 2
          },
          end: {
            line: 542,
            column: 3
          }
        },
        loc: {
          start: {
            line: 542,
            column: 42
          },
          end: {
            line: 548,
            column: 3
          }
        },
        line: 542
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 543,
            column: 40
          },
          end: {
            line: 543,
            column: 41
          }
        },
        loc: {
          start: {
            line: 543,
            column: 58
          },
          end: {
            line: 547,
            column: 5
          }
        },
        line: 543
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 550,
            column: 2
          },
          end: {
            line: 550,
            column: 3
          }
        },
        loc: {
          start: {
            line: 550,
            column: 39
          },
          end: {
            line: 613,
            column: 3
          }
        },
        line: 550
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 576,
            column: 18
          },
          end: {
            line: 576,
            column: 19
          }
        },
        loc: {
          start: {
            line: 576,
            column: 30
          },
          end: {
            line: 580,
            column: 9
          }
        },
        line: 576
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 605,
            column: 18
          },
          end: {
            line: 605,
            column: 19
          }
        },
        loc: {
          start: {
            line: 605,
            column: 30
          },
          end: {
            line: 608,
            column: 9
          }
        },
        line: 605
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 612,
            column: 25
          },
          end: {
            line: 612,
            column: 26
          }
        },
        loc: {
          start: {
            line: 612,
            column: 33
          },
          end: {
            line: 612,
            column: 56
          }
        },
        line: 612
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 620,
            column: 2
          },
          end: {
            line: 620,
            column: 3
          }
        },
        loc: {
          start: {
            line: 620,
            column: 36
          },
          end: {
            line: 622,
            column: 3
          }
        },
        line: 620
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 637,
            column: 2
          },
          end: {
            line: 637,
            column: 3
          }
        },
        loc: {
          start: {
            line: 637,
            column: 31
          },
          end: {
            line: 644,
            column: 3
          }
        },
        line: 637
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 639,
            column: 16
          },
          end: {
            line: 639,
            column: 17
          }
        },
        loc: {
          start: {
            line: 639,
            column: 22
          },
          end: {
            line: 641,
            column: 5
          }
        },
        line: 639
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 646,
            column: 2
          },
          end: {
            line: 646,
            column: 3
          }
        },
        loc: {
          start: {
            line: 646,
            column: 20
          },
          end: {
            line: 648,
            column: 3
          }
        },
        line: 646
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 650,
            column: 2
          },
          end: {
            line: 650,
            column: 3
          }
        },
        loc: {
          start: {
            line: 650,
            column: 25
          },
          end: {
            line: 652,
            column: 3
          }
        },
        line: 650
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 654,
            column: 2
          },
          end: {
            line: 654,
            column: 3
          }
        },
        loc: {
          start: {
            line: 654,
            column: 38
          },
          end: {
            line: 670,
            column: 3
          }
        },
        line: 654
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 220,
            column: 4
          },
          end: {
            line: 222,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 220,
            column: 4
          },
          end: {
            line: 222,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 220
      },
      "1": {
        loc: {
          start: {
            line: 231,
            column: 31
          },
          end: {
            line: 231,
            column: 48
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 231,
            column: 47
          },
          end: {
            line: 231,
            column: 48
          }
        }],
        line: 231
      },
      "2": {
        loc: {
          start: {
            line: 233,
            column: 4
          },
          end: {
            line: 236,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 233,
            column: 4
          },
          end: {
            line: 236,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 233
      },
      "3": {
        loc: {
          start: {
            line: 238,
            column: 4
          },
          end: {
            line: 244,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 238,
            column: 4
          },
          end: {
            line: 244,
            column: 5
          }
        }, {
          start: {
            line: 242,
            column: 11
          },
          end: {
            line: 244,
            column: 5
          }
        }],
        line: 238
      },
      "4": {
        loc: {
          start: {
            line: 252,
            column: 4
          },
          end: {
            line: 255,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 252,
            column: 4
          },
          end: {
            line: 255,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 252
      },
      "5": {
        loc: {
          start: {
            line: 281,
            column: 33
          },
          end: {
            line: 283,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 282,
            column: 8
          },
          end: {
            line: 282,
            column: 103
          }
        }, {
          start: {
            line: 283,
            column: 8
          },
          end: {
            line: 283,
            column: 9
          }
        }],
        line: 281
      },
      "6": {
        loc: {
          start: {
            line: 285,
            column: 24
          },
          end: {
            line: 285,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 285,
            column: 46
          },
          end: {
            line: 285,
            column: 92
          }
        }, {
          start: {
            line: 285,
            column: 95
          },
          end: {
            line: 285,
            column: 96
          }
        }],
        line: 285
      },
      "7": {
        loc: {
          start: {
            line: 291,
            column: 6
          },
          end: {
            line: 293,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 291,
            column: 6
          },
          end: {
            line: 293,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 291
      },
      "8": {
        loc: {
          start: {
            line: 292,
            column: 43
          },
          end: {
            line: 292,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 292,
            column: 43
          },
          end: {
            line: 292,
            column: 74
          }
        }, {
          start: {
            line: 292,
            column: 78
          },
          end: {
            line: 292,
            column: 79
          }
        }],
        line: 292
      },
      "9": {
        loc: {
          start: {
            line: 316,
            column: 6
          },
          end: {
            line: 322,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 316,
            column: 6
          },
          end: {
            line: 322,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 316
      },
      "10": {
        loc: {
          start: {
            line: 331,
            column: 4
          },
          end: {
            line: 334,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 331,
            column: 4
          },
          end: {
            line: 334,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 331
      },
      "11": {
        loc: {
          start: {
            line: 342,
            column: 6
          },
          end: {
            line: 342,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 342,
            column: 6
          },
          end: {
            line: 342,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 342
      },
      "12": {
        loc: {
          start: {
            line: 342,
            column: 10
          },
          end: {
            line: 342,
            column: 26
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 342,
            column: 10
          },
          end: {
            line: 342,
            column: 16
          }
        }, {
          start: {
            line: 342,
            column: 20
          },
          end: {
            line: 342,
            column: 26
          }
        }],
        line: 342
      },
      "13": {
        loc: {
          start: {
            line: 348,
            column: 6
          },
          end: {
            line: 348,
            column: 50
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 348,
            column: 6
          },
          end: {
            line: 348,
            column: 50
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 348
      },
      "14": {
        loc: {
          start: {
            line: 366,
            column: 6
          },
          end: {
            line: 368,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 366,
            column: 6
          },
          end: {
            line: 368,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 366
      },
      "15": {
        loc: {
          start: {
            line: 412,
            column: 6
          },
          end: {
            line: 414,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 412,
            column: 6
          },
          end: {
            line: 414,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 412
      },
      "16": {
        loc: {
          start: {
            line: 425,
            column: 4
          },
          end: {
            line: 427,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 425,
            column: 4
          },
          end: {
            line: 427,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 425
      },
      "17": {
        loc: {
          start: {
            line: 430,
            column: 4
          },
          end: {
            line: 432,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 430,
            column: 4
          },
          end: {
            line: 432,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 430
      },
      "18": {
        loc: {
          start: {
            line: 435,
            column: 4
          },
          end: {
            line: 437,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 435,
            column: 4
          },
          end: {
            line: 437,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 435
      },
      "19": {
        loc: {
          start: {
            line: 435,
            column: 8
          },
          end: {
            line: 435,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 435,
            column: 8
          },
          end: {
            line: 435,
            column: 41
          }
        }, {
          start: {
            line: 435,
            column: 45
          },
          end: {
            line: 435,
            column: 67
          }
        }],
        line: 435
      },
      "20": {
        loc: {
          start: {
            line: 440,
            column: 4
          },
          end: {
            line: 442,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 440,
            column: 4
          },
          end: {
            line: 442,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 440
      },
      "21": {
        loc: {
          start: {
            line: 440,
            column: 8
          },
          end: {
            line: 440,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 440,
            column: 8
          },
          end: {
            line: 440,
            column: 40
          }
        }, {
          start: {
            line: 440,
            column: 44
          },
          end: {
            line: 440,
            column: 77
          }
        }],
        line: 440
      },
      "22": {
        loc: {
          start: {
            line: 446,
            column: 4
          },
          end: {
            line: 448,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 446,
            column: 4
          },
          end: {
            line: 448,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 446
      },
      "23": {
        loc: {
          start: {
            line: 450,
            column: 4
          },
          end: {
            line: 452,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 450,
            column: 4
          },
          end: {
            line: 452,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 450
      },
      "24": {
        loc: {
          start: {
            line: 467,
            column: 6
          },
          end: {
            line: 469,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 467,
            column: 6
          },
          end: {
            line: 469,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 467
      },
      "25": {
        loc: {
          start: {
            line: 472,
            column: 6
          },
          end: {
            line: 474,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 472,
            column: 6
          },
          end: {
            line: 474,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 472
      },
      "26": {
        loc: {
          start: {
            line: 477,
            column: 6
          },
          end: {
            line: 479,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 477,
            column: 6
          },
          end: {
            line: 479,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 477
      },
      "27": {
        loc: {
          start: {
            line: 482,
            column: 6
          },
          end: {
            line: 484,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 482,
            column: 6
          },
          end: {
            line: 484,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 482
      },
      "28": {
        loc: {
          start: {
            line: 482,
            column: 10
          },
          end: {
            line: 482,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 482,
            column: 10
          },
          end: {
            line: 482,
            column: 31
          }
        }, {
          start: {
            line: 482,
            column: 35
          },
          end: {
            line: 482,
            column: 66
          }
        }],
        line: 482
      },
      "29": {
        loc: {
          start: {
            line: 498,
            column: 4
          },
          end: {
            line: 505,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 498,
            column: 4
          },
          end: {
            line: 505,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 498
      },
      "30": {
        loc: {
          start: {
            line: 521,
            column: 4
          },
          end: {
            line: 521,
            column: 44
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 521,
            column: 4
          },
          end: {
            line: 521,
            column: 44
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 521
      },
      "31": {
        loc: {
          start: {
            line: 526,
            column: 4
          },
          end: {
            line: 526,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 526,
            column: 4
          },
          end: {
            line: 526,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 526
      },
      "32": {
        loc: {
          start: {
            line: 533,
            column: 6
          },
          end: {
            line: 538,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 533,
            column: 6
          },
          end: {
            line: 538,
            column: 7
          }
        }, {
          start: {
            line: 535,
            column: 13
          },
          end: {
            line: 538,
            column: 7
          }
        }],
        line: 533
      },
      "33": {
        loc: {
          start: {
            line: 533,
            column: 10
          },
          end: {
            line: 533,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 533,
            column: 10
          },
          end: {
            line: 533,
            column: 14
          }
        }, {
          start: {
            line: 533,
            column: 18
          },
          end: {
            line: 533,
            column: 43
          }
        }],
        line: 533
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0
    },
    b: {
      "0": [0, 0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "2229c1c602b4fc434da1ccc30b5c2a296cb95e14"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1z72a6923t = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1z72a6923t();
import { AppState } from 'react-native';
import { advancedMemoryManager } from "./AdvancedMemoryManager";
var BackgroundProcessingManager = function () {
  function BackgroundProcessingManager() {
    _classCallCheck(this, BackgroundProcessingManager);
    this.backgroundTasks = (cov_1z72a6923t().s[0]++, new Map());
    this.taskQueue = (cov_1z72a6923t().s[1]++, []);
    this.activeTasks = (cov_1z72a6923t().s[2]++, new Map());
    this.executionHistory = (cov_1z72a6923t().s[3]++, []);
    this.PROCESSING_STRATEGIES = (cov_1z72a6923t().s[4]++, [{
      name: 'battery_saver',
      description: 'Minimal processing to preserve battery',
      conditions: {
        batteryLevel: 20,
        networkType: ['wifi'],
        appState: ['background']
      },
      limits: {
        maxConcurrentTasks: 1,
        maxCPUUsage: 0.1,
        maxMemoryUsage: 32 * 1024 * 1024,
        maxNetworkUsage: 1024 * 1024
      },
      prioritization: {
        criticalFirst: true,
        batteryAware: true,
        networkAware: true,
        timeAware: false
      }
    }, {
      name: 'balanced',
      description: 'Balanced processing for normal conditions',
      conditions: {
        batteryLevel: 50,
        networkType: ['wifi', '4g', '5g'],
        appState: ['active', 'background']
      },
      limits: {
        maxConcurrentTasks: 3,
        maxCPUUsage: 0.3,
        maxMemoryUsage: 128 * 1024 * 1024,
        maxNetworkUsage: 10 * 1024 * 1024
      },
      prioritization: {
        criticalFirst: true,
        batteryAware: true,
        networkAware: true,
        timeAware: true
      }
    }, {
      name: 'performance',
      description: 'High performance processing when charging',
      conditions: {
        batteryLevel: 80,
        networkType: ['wifi', '5g'],
        appState: ['active', 'background']
      },
      limits: {
        maxConcurrentTasks: 5,
        maxCPUUsage: 0.6,
        maxMemoryUsage: 256 * 1024 * 1024,
        maxNetworkUsage: 50 * 1024 * 1024
      },
      prioritization: {
        criticalFirst: false,
        batteryAware: false,
        networkAware: false,
        timeAware: true
      }
    }]);
    cov_1z72a6923t().f[0]++;
    cov_1z72a6923t().s[5]++;
    this.currentStrategy = this.PROCESSING_STRATEGIES[1];
    cov_1z72a6923t().s[6]++;
    this.taskScheduler = new TaskScheduler();
    cov_1z72a6923t().s[7]++;
    this.resourceMonitor = new ResourceMonitor();
    cov_1z72a6923t().s[8]++;
    this.initializeBackgroundProcessing();
  }
  return _createClass(BackgroundProcessingManager, [{
    key: "initializeBackgroundProcessing",
    value: (function () {
      var _initializeBackgroundProcessing = _asyncToGenerator(function* () {
        cov_1z72a6923t().f[1]++;
        cov_1z72a6923t().s[9]++;
        try {
          cov_1z72a6923t().s[10]++;
          yield this.taskScheduler.initialize();
          cov_1z72a6923t().s[11]++;
          yield this.resourceMonitor.start();
          cov_1z72a6923t().s[12]++;
          this.startTaskProcessing();
          cov_1z72a6923t().s[13]++;
          this.setupAppStateMonitoring();
          cov_1z72a6923t().s[14]++;
          this.registerDefaultTasks();
          cov_1z72a6923t().s[15]++;
          console.log('Background Processing Manager initialized successfully');
        } catch (error) {
          cov_1z72a6923t().s[16]++;
          console.error('Failed to initialize Background Processing Manager:', error);
        }
      });
      function initializeBackgroundProcessing() {
        return _initializeBackgroundProcessing.apply(this, arguments);
      }
      return initializeBackgroundProcessing;
    }())
  }, {
    key: "registerTask",
    value: function registerTask(task) {
      cov_1z72a6923t().f[2]++;
      var fullTask = (cov_1z72a6923t().s[17]++, Object.assign({}, task, {
        execution: {
          attempts: 0,
          maxAttempts: 3,
          averageExecutionTime: task.estimatedDuration,
          successRate: 100
        }
      }));
      cov_1z72a6923t().s[18]++;
      this.backgroundTasks.set(task.id, fullTask);
      cov_1z72a6923t().s[19]++;
      if (task.schedule.immediate) {
        cov_1z72a6923t().b[0][0]++;
        cov_1z72a6923t().s[20]++;
        this.scheduleTask(task.id);
      } else {
        cov_1z72a6923t().b[0][1]++;
      }
      cov_1z72a6923t().s[21]++;
      console.log(`Registered background task: ${task.id}`);
      cov_1z72a6923t().s[22]++;
      return task.id;
    }
  }, {
    key: "scheduleTask",
    value: function scheduleTask(taskId) {
      var _this = this;
      var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1z72a6923t().b[1][0]++, 0);
      cov_1z72a6923t().f[3]++;
      var task = (cov_1z72a6923t().s[23]++, this.backgroundTasks.get(taskId));
      cov_1z72a6923t().s[24]++;
      if (!task) {
        cov_1z72a6923t().b[2][0]++;
        cov_1z72a6923t().s[25]++;
        console.warn(`Task not found: ${taskId}`);
        cov_1z72a6923t().s[26]++;
        return;
      } else {
        cov_1z72a6923t().b[2][1]++;
      }
      cov_1z72a6923t().s[27]++;
      if (delay > 0) {
        cov_1z72a6923t().b[3][0]++;
        cov_1z72a6923t().s[28]++;
        setTimeout(function () {
          cov_1z72a6923t().f[4]++;
          cov_1z72a6923t().s[29]++;
          _this.addToQueue(taskId);
        }, delay);
      } else {
        cov_1z72a6923t().b[3][1]++;
        cov_1z72a6923t().s[30]++;
        this.addToQueue(taskId);
      }
    }
  }, {
    key: "executeTaskImmediately",
    value: (function () {
      var _executeTaskImmediately = _asyncToGenerator(function* (taskId) {
        cov_1z72a6923t().f[5]++;
        var task = (cov_1z72a6923t().s[31]++, this.backgroundTasks.get(taskId));
        cov_1z72a6923t().s[32]++;
        if (!task) {
          cov_1z72a6923t().b[4][0]++;
          cov_1z72a6923t().s[33]++;
          console.warn(`Task not found: ${taskId}`);
          cov_1z72a6923t().s[34]++;
          return null;
        } else {
          cov_1z72a6923t().b[4][1]++;
        }
        cov_1z72a6923t().s[35]++;
        return yield this.executeTask(task);
      });
      function executeTaskImmediately(_x) {
        return _executeTaskImmediately.apply(this, arguments);
      }
      return executeTaskImmediately;
    }())
  }, {
    key: "getProcessingMetrics",
    value: function getProcessingMetrics() {
      var _this2 = this;
      cov_1z72a6923t().f[6]++;
      var totalExecutions = (cov_1z72a6923t().s[36]++, this.executionHistory.length);
      var successfulExecutions = (cov_1z72a6923t().s[37]++, this.executionHistory.filter(function (e) {
        cov_1z72a6923t().f[7]++;
        cov_1z72a6923t().s[38]++;
        return e.success;
      }).length);
      var averageExecutionTime = (cov_1z72a6923t().s[39]++, totalExecutions > 0 ? (cov_1z72a6923t().b[5][0]++, this.executionHistory.reduce(function (sum, e) {
        cov_1z72a6923t().f[8]++;
        cov_1z72a6923t().s[40]++;
        return sum + (e.endTime - e.startTime);
      }, 0) / totalExecutions) : (cov_1z72a6923t().b[5][1]++, 0));
      var successRate = (cov_1z72a6923t().s[41]++, totalExecutions > 0 ? (cov_1z72a6923t().b[6][0]++, successfulExecutions / totalExecutions * 100) : (cov_1z72a6923t().b[6][1]++, 0));
      var taskTypeDistribution = (cov_1z72a6923t().s[42]++, {});
      cov_1z72a6923t().s[43]++;
      this.executionHistory.forEach(function (execution) {
        cov_1z72a6923t().f[9]++;
        var task = (cov_1z72a6923t().s[44]++, _this2.backgroundTasks.get(execution.taskId));
        cov_1z72a6923t().s[45]++;
        if (task) {
          cov_1z72a6923t().b[7][0]++;
          cov_1z72a6923t().s[46]++;
          taskTypeDistribution[task.type] = ((cov_1z72a6923t().b[8][0]++, taskTypeDistribution[task.type]) || (cov_1z72a6923t().b[8][1]++, 0)) + 1;
        } else {
          cov_1z72a6923t().b[7][1]++;
        }
      });
      cov_1z72a6923t().s[47]++;
      return {
        activeTaskCount: this.activeTasks.size,
        queuedTaskCount: this.taskQueue.length,
        totalTasksExecuted: totalExecutions,
        averageExecutionTime: averageExecutionTime,
        successRate: successRate,
        currentStrategy: this.currentStrategy.name,
        resourceUsage: this.resourceMonitor.getCurrentUsage(),
        taskTypeDistribution: taskTypeDistribution
      };
    }
  }, {
    key: "optimizeProcessingStrategy",
    value: (function () {
      var _optimizeProcessingStrategy = _asyncToGenerator(function* () {
        cov_1z72a6923t().f[10]++;
        cov_1z72a6923t().s[48]++;
        try {
          var currentConditions = (cov_1z72a6923t().s[49]++, yield this.getCurrentConditions());
          var optimalStrategy = (cov_1z72a6923t().s[50]++, this.selectOptimalStrategy(currentConditions));
          cov_1z72a6923t().s[51]++;
          if (optimalStrategy.name !== this.currentStrategy.name) {
            cov_1z72a6923t().b[9][0]++;
            cov_1z72a6923t().s[52]++;
            console.log(`Switching to ${optimalStrategy.name} processing strategy`);
            cov_1z72a6923t().s[53]++;
            this.currentStrategy = optimalStrategy;
            cov_1z72a6923t().s[54]++;
            yield this.adjustActiveTasksForStrategy();
          } else {
            cov_1z72a6923t().b[9][1]++;
          }
        } catch (error) {
          cov_1z72a6923t().s[55]++;
          console.error('Failed to optimize processing strategy:', error);
        }
      });
      function optimizeProcessingStrategy() {
        return _optimizeProcessingStrategy.apply(this, arguments);
      }
      return optimizeProcessingStrategy;
    }())
  }, {
    key: "addToQueue",
    value: function addToQueue(taskId) {
      cov_1z72a6923t().f[11]++;
      cov_1z72a6923t().s[56]++;
      if (!this.taskQueue.includes(taskId)) {
        cov_1z72a6923t().b[10][0]++;
        cov_1z72a6923t().s[57]++;
        this.taskQueue.push(taskId);
        cov_1z72a6923t().s[58]++;
        this.sortTaskQueue();
      } else {
        cov_1z72a6923t().b[10][1]++;
      }
    }
  }, {
    key: "sortTaskQueue",
    value: function sortTaskQueue() {
      var _this3 = this;
      cov_1z72a6923t().f[12]++;
      cov_1z72a6923t().s[59]++;
      this.taskQueue.sort(function (a, b) {
        cov_1z72a6923t().f[13]++;
        var taskA = (cov_1z72a6923t().s[60]++, _this3.backgroundTasks.get(a));
        var taskB = (cov_1z72a6923t().s[61]++, _this3.backgroundTasks.get(b));
        cov_1z72a6923t().s[62]++;
        if ((cov_1z72a6923t().b[12][0]++, !taskA) || (cov_1z72a6923t().b[12][1]++, !taskB)) {
          cov_1z72a6923t().b[11][0]++;
          cov_1z72a6923t().s[63]++;
          return 0;
        } else {
          cov_1z72a6923t().b[11][1]++;
        }
        var priorityOrder = (cov_1z72a6923t().s[64]++, {
          critical: 4,
          high: 3,
          medium: 2,
          low: 1
        });
        var priorityDiff = (cov_1z72a6923t().s[65]++, priorityOrder[taskB.priority] - priorityOrder[taskA.priority]);
        cov_1z72a6923t().s[66]++;
        if (priorityDiff !== 0) {
          cov_1z72a6923t().b[13][0]++;
          cov_1z72a6923t().s[67]++;
          return priorityDiff;
        } else {
          cov_1z72a6923t().b[13][1]++;
        }
        cov_1z72a6923t().s[68]++;
        return taskA.estimatedDuration - taskB.estimatedDuration;
      });
    }
  }, {
    key: "executeTask",
    value: function () {
      var _executeTask = _asyncToGenerator(function* (task) {
        cov_1z72a6923t().f[14]++;
        var execution = (cov_1z72a6923t().s[69]++, {
          taskId: task.id,
          startTime: Date.now(),
          success: false,
          resourceUsage: {
            cpu: 0,
            memory: 0,
            network: 0,
            battery: 0
          },
          conditions: yield this.getCurrentConditions()
        });
        cov_1z72a6923t().s[70]++;
        try {
          cov_1z72a6923t().s[71]++;
          if (!this.canExecuteTask(task)) {
            cov_1z72a6923t().b[14][0]++;
            cov_1z72a6923t().s[72]++;
            throw new Error('Task execution conditions not met');
          } else {
            cov_1z72a6923t().b[14][1]++;
          }
          var initialUsage = (cov_1z72a6923t().s[73]++, this.resourceMonitor.getCurrentUsage());
          cov_1z72a6923t().s[74]++;
          this.activeTasks.set(task.id, execution);
          var result = (cov_1z72a6923t().s[75]++, yield task.callback());
          var finalUsage = (cov_1z72a6923t().s[76]++, this.resourceMonitor.getCurrentUsage());
          cov_1z72a6923t().s[77]++;
          execution.resourceUsage = {
            cpu: finalUsage.cpu - initialUsage.cpu,
            memory: finalUsage.memory - initialUsage.memory,
            network: finalUsage.network - initialUsage.network,
            battery: finalUsage.battery - initialUsage.battery
          };
          cov_1z72a6923t().s[78]++;
          execution.success = true;
          cov_1z72a6923t().s[79]++;
          execution.result = result;
          cov_1z72a6923t().s[80]++;
          execution.endTime = Date.now();
          cov_1z72a6923t().s[81]++;
          task.execution.attempts++;
          cov_1z72a6923t().s[82]++;
          task.execution.lastSuccess = Date.now();
          cov_1z72a6923t().s[83]++;
          task.execution.averageExecutionTime = (task.execution.averageExecutionTime + (execution.endTime - execution.startTime)) / 2;
          cov_1z72a6923t().s[84]++;
          console.log(`Task executed successfully: ${task.id}`);
        } catch (error) {
          cov_1z72a6923t().s[85]++;
          execution.success = false;
          cov_1z72a6923t().s[86]++;
          execution.error = error.message;
          cov_1z72a6923t().s[87]++;
          execution.endTime = Date.now();
          cov_1z72a6923t().s[88]++;
          task.execution.attempts++;
          cov_1z72a6923t().s[89]++;
          task.execution.lastAttempt = Date.now();
          cov_1z72a6923t().s[90]++;
          console.error(`Task execution failed: ${task.id}`, error);
        } finally {
          cov_1z72a6923t().s[91]++;
          this.activeTasks.delete(task.id);
          cov_1z72a6923t().s[92]++;
          this.executionHistory.push(execution);
          cov_1z72a6923t().s[93]++;
          if (this.executionHistory.length > 1000) {
            cov_1z72a6923t().b[15][0]++;
            cov_1z72a6923t().s[94]++;
            this.executionHistory.shift();
          } else {
            cov_1z72a6923t().b[15][1]++;
          }
        }
        cov_1z72a6923t().s[95]++;
        return execution;
      });
      function executeTask(_x2) {
        return _executeTask.apply(this, arguments);
      }
      return executeTask;
    }()
  }, {
    key: "canExecuteTask",
    value: function canExecuteTask(task) {
      cov_1z72a6923t().f[15]++;
      var conditions = (cov_1z72a6923t().s[96]++, this.resourceMonitor.getCurrentConditions());
      cov_1z72a6923t().s[97]++;
      if (!task.constraints.allowedStates.includes(conditions.appState)) {
        cov_1z72a6923t().b[16][0]++;
        cov_1z72a6923t().s[98]++;
        return false;
      } else {
        cov_1z72a6923t().b[16][1]++;
      }
      cov_1z72a6923t().s[99]++;
      if (conditions.batteryLevel < task.constraints.maxBatteryDrain) {
        cov_1z72a6923t().b[17][0]++;
        cov_1z72a6923t().s[100]++;
        return false;
      } else {
        cov_1z72a6923t().b[17][1]++;
      }
      cov_1z72a6923t().s[101]++;
      if ((cov_1z72a6923t().b[19][0]++, task.constraints.requiresCharging) && (cov_1z72a6923t().b[19][1]++, !conditions.isCharging)) {
        cov_1z72a6923t().b[18][0]++;
        cov_1z72a6923t().s[102]++;
        return false;
      } else {
        cov_1z72a6923t().b[18][1]++;
      }
      cov_1z72a6923t().s[103]++;
      if ((cov_1z72a6923t().b[21][0]++, task.constraints.requiresNetwork) && (cov_1z72a6923t().b[21][1]++, conditions.networkType === 'none')) {
        cov_1z72a6923t().b[20][0]++;
        cov_1z72a6923t().s[104]++;
        return false;
      } else {
        cov_1z72a6923t().b[20][1]++;
      }
      var currentUsage = (cov_1z72a6923t().s[105]++, this.resourceMonitor.getCurrentUsage());
      cov_1z72a6923t().s[106]++;
      if (currentUsage.cpu + task.estimatedCPU > this.currentStrategy.limits.maxCPUUsage) {
        cov_1z72a6923t().b[22][0]++;
        cov_1z72a6923t().s[107]++;
        return false;
      } else {
        cov_1z72a6923t().b[22][1]++;
      }
      cov_1z72a6923t().s[108]++;
      if (currentUsage.memory + task.estimatedMemory > this.currentStrategy.limits.maxMemoryUsage) {
        cov_1z72a6923t().b[23][0]++;
        cov_1z72a6923t().s[109]++;
        return false;
      } else {
        cov_1z72a6923t().b[23][1]++;
      }
      cov_1z72a6923t().s[110]++;
      return true;
    }
  }, {
    key: "getCurrentConditions",
    value: function () {
      var _getCurrentConditions = _asyncToGenerator(function* () {
        cov_1z72a6923t().f[16]++;
        cov_1z72a6923t().s[111]++;
        return this.resourceMonitor.getCurrentConditions();
      });
      function getCurrentConditions() {
        return _getCurrentConditions.apply(this, arguments);
      }
      return getCurrentConditions;
    }()
  }, {
    key: "selectOptimalStrategy",
    value: function selectOptimalStrategy(conditions) {
      cov_1z72a6923t().f[17]++;
      var strategyScores = (cov_1z72a6923t().s[112]++, this.PROCESSING_STRATEGIES.map(function (strategy) {
        cov_1z72a6923t().f[18]++;
        var score = (cov_1z72a6923t().s[113]++, 0);
        cov_1z72a6923t().s[114]++;
        if (conditions.batteryLevel >= strategy.conditions.batteryLevel) {
          cov_1z72a6923t().b[24][0]++;
          cov_1z72a6923t().s[115]++;
          score += 30;
        } else {
          cov_1z72a6923t().b[24][1]++;
        }
        cov_1z72a6923t().s[116]++;
        if (strategy.conditions.networkType.includes(conditions.networkType)) {
          cov_1z72a6923t().b[25][0]++;
          cov_1z72a6923t().s[117]++;
          score += 25;
        } else {
          cov_1z72a6923t().b[25][1]++;
        }
        cov_1z72a6923t().s[118]++;
        if (strategy.conditions.appState.includes(conditions.appState)) {
          cov_1z72a6923t().b[26][0]++;
          cov_1z72a6923t().s[119]++;
          score += 20;
        } else {
          cov_1z72a6923t().b[26][1]++;
        }
        cov_1z72a6923t().s[120]++;
        if ((cov_1z72a6923t().b[28][0]++, conditions.isCharging) && (cov_1z72a6923t().b[28][1]++, strategy.name === 'performance')) {
          cov_1z72a6923t().b[27][0]++;
          cov_1z72a6923t().s[121]++;
          score += 25;
        } else {
          cov_1z72a6923t().b[27][1]++;
        }
        cov_1z72a6923t().s[122]++;
        return {
          strategy: strategy,
          score: score
        };
      }));
      cov_1z72a6923t().s[123]++;
      strategyScores.sort(function (a, b) {
        cov_1z72a6923t().f[19]++;
        cov_1z72a6923t().s[124]++;
        return b.score - a.score;
      });
      cov_1z72a6923t().s[125]++;
      return strategyScores[0].strategy;
    }
  }, {
    key: "adjustActiveTasksForStrategy",
    value: function () {
      var _adjustActiveTasksForStrategy = _asyncToGenerator(function* () {
        cov_1z72a6923t().f[20]++;
        var maxConcurrent = (cov_1z72a6923t().s[126]++, this.currentStrategy.limits.maxConcurrentTasks);
        cov_1z72a6923t().s[127]++;
        if (this.activeTasks.size > maxConcurrent) {
          cov_1z72a6923t().b[29][0]++;
          var tasksToSuspend = (cov_1z72a6923t().s[128]++, Array.from(this.activeTasks.keys()).slice(maxConcurrent));
          cov_1z72a6923t().s[129]++;
          for (var taskId of tasksToSuspend) {
            cov_1z72a6923t().s[130]++;
            console.log(`Suspending task due to strategy change: ${taskId}`);
          }
        } else {
          cov_1z72a6923t().b[29][1]++;
        }
      });
      function adjustActiveTasksForStrategy() {
        return _adjustActiveTasksForStrategy.apply(this, arguments);
      }
      return adjustActiveTasksForStrategy;
    }()
  }, {
    key: "startTaskProcessing",
    value: function startTaskProcessing() {
      var _this4 = this;
      cov_1z72a6923t().f[21]++;
      cov_1z72a6923t().s[131]++;
      setInterval(function () {
        cov_1z72a6923t().f[22]++;
        cov_1z72a6923t().s[132]++;
        _this4.processTaskQueue();
      }, 5000);
      cov_1z72a6923t().s[133]++;
      setInterval(function () {
        cov_1z72a6923t().f[23]++;
        cov_1z72a6923t().s[134]++;
        _this4.optimizeProcessingStrategy();
      }, 60000);
    }
  }, {
    key: "processTaskQueue",
    value: function () {
      var _processTaskQueue = _asyncToGenerator(function* () {
        cov_1z72a6923t().f[24]++;
        cov_1z72a6923t().s[135]++;
        if (this.taskQueue.length === 0) {
          cov_1z72a6923t().b[30][0]++;
          cov_1z72a6923t().s[136]++;
          return;
        } else {
          cov_1z72a6923t().b[30][1]++;
        }
        var maxConcurrent = (cov_1z72a6923t().s[137]++, this.currentStrategy.limits.maxConcurrentTasks);
        var availableSlots = (cov_1z72a6923t().s[138]++, maxConcurrent - this.activeTasks.size);
        cov_1z72a6923t().s[139]++;
        if (availableSlots <= 0) {
          cov_1z72a6923t().b[31][0]++;
          cov_1z72a6923t().s[140]++;
          return;
        } else {
          cov_1z72a6923t().b[31][1]++;
        }
        var tasksToProcess = (cov_1z72a6923t().s[141]++, this.taskQueue.splice(0, availableSlots));
        cov_1z72a6923t().s[142]++;
        for (var taskId of tasksToProcess) {
          var task = (cov_1z72a6923t().s[143]++, this.backgroundTasks.get(taskId));
          cov_1z72a6923t().s[144]++;
          if ((cov_1z72a6923t().b[33][0]++, task) && (cov_1z72a6923t().b[33][1]++, this.canExecuteTask(task))) {
            cov_1z72a6923t().b[32][0]++;
            cov_1z72a6923t().s[145]++;
            this.executeTask(task);
          } else {
            cov_1z72a6923t().b[32][1]++;
            cov_1z72a6923t().s[146]++;
            this.taskQueue.push(taskId);
          }
        }
      });
      function processTaskQueue() {
        return _processTaskQueue.apply(this, arguments);
      }
      return processTaskQueue;
    }()
  }, {
    key: "setupAppStateMonitoring",
    value: function setupAppStateMonitoring() {
      var _this5 = this;
      cov_1z72a6923t().f[25]++;
      cov_1z72a6923t().s[147]++;
      AppState.addEventListener('change', function (nextAppState) {
        cov_1z72a6923t().f[26]++;
        cov_1z72a6923t().s[148]++;
        console.log(`App state changed to: ${nextAppState}`);
        cov_1z72a6923t().s[149]++;
        _this5.optimizeProcessingStrategy();
      });
    }
  }, {
    key: "registerDefaultTasks",
    value: function registerDefaultTasks() {
      var _this6 = this;
      cov_1z72a6923t().f[27]++;
      var defaultTasks = (cov_1z72a6923t().s[150]++, [{
        id: 'cache_cleanup',
        name: 'Cache Cleanup',
        type: 'maintenance',
        priority: 'low',
        estimatedDuration: 30000,
        estimatedCPU: 0.1,
        estimatedMemory: 16 * 1024 * 1024,
        estimatedNetwork: 0,
        constraints: {
          requiresNetwork: false,
          requiresCharging: false,
          requiresIdle: true,
          maxBatteryDrain: 10,
          allowedStates: ['background']
        },
        schedule: {
          immediate: false,
          recurring: true,
          interval: 3600000,
          preferredTime: 'idle'
        },
        dependencies: [],
        callback: function () {
          var _callback = _asyncToGenerator(function* () {
            cov_1z72a6923t().f[28]++;
            cov_1z72a6923t().s[151]++;
            yield advancedMemoryManager.optimizeMemory(false);
            cov_1z72a6923t().s[152]++;
            return {
              cleaned: true
            };
          });
          function callback() {
            return _callback.apply(this, arguments);
          }
          return callback;
        }()
      }, {
        id: 'analytics_sync',
        name: 'Analytics Sync',
        type: 'analytics',
        priority: 'medium',
        estimatedDuration: 15000,
        estimatedCPU: 0.05,
        estimatedMemory: 8 * 1024 * 1024,
        estimatedNetwork: 1024 * 1024,
        constraints: {
          requiresNetwork: true,
          requiresCharging: false,
          requiresIdle: false,
          maxBatteryDrain: 5,
          allowedStates: ['active', 'background']
        },
        schedule: {
          immediate: false,
          recurring: true,
          interval: 1800000,
          preferredTime: 'wifi'
        },
        dependencies: [],
        callback: function () {
          var _callback2 = _asyncToGenerator(function* () {
            cov_1z72a6923t().f[29]++;
            cov_1z72a6923t().s[153]++;
            return {
              synced: true,
              events: 42
            };
          });
          function callback() {
            return _callback2.apply(this, arguments);
          }
          return callback;
        }()
      }]);
      cov_1z72a6923t().s[154]++;
      defaultTasks.forEach(function (task) {
        cov_1z72a6923t().f[30]++;
        cov_1z72a6923t().s[155]++;
        return _this6.registerTask(task);
      });
    }
  }]);
}();
var TaskScheduler = function () {
  function TaskScheduler() {
    _classCallCheck(this, TaskScheduler);
  }
  return _createClass(TaskScheduler, [{
    key: "initialize",
    value: function () {
      var _initialize = _asyncToGenerator(function* () {
        cov_1z72a6923t().f[31]++;
        cov_1z72a6923t().s[156]++;
        console.log('Task Scheduler initialized');
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }()
  }]);
}();
var ResourceMonitor = function () {
  function ResourceMonitor() {
    _classCallCheck(this, ResourceMonitor);
    this.currentUsage = (cov_1z72a6923t().s[157]++, {
      cpu: 0,
      memory: 0,
      network: 0,
      battery: 0
    });
    this.currentConditions = (cov_1z72a6923t().s[158]++, {
      appState: 'active',
      batteryLevel: 100,
      networkType: 'wifi',
      isCharging: false
    });
  }
  return _createClass(ResourceMonitor, [{
    key: "start",
    value: function () {
      var _start = _asyncToGenerator(function* () {
        var _this7 = this;
        cov_1z72a6923t().f[32]++;
        cov_1z72a6923t().s[159]++;
        setInterval(function () {
          cov_1z72a6923t().f[33]++;
          cov_1z72a6923t().s[160]++;
          _this7.updateResourceUsage();
        }, 10000);
        cov_1z72a6923t().s[161]++;
        console.log('Resource Monitor started');
      });
      function start() {
        return _start.apply(this, arguments);
      }
      return start;
    }()
  }, {
    key: "getCurrentUsage",
    value: function getCurrentUsage() {
      cov_1z72a6923t().f[34]++;
      cov_1z72a6923t().s[162]++;
      return Object.assign({}, this.currentUsage);
    }
  }, {
    key: "getCurrentConditions",
    value: function getCurrentConditions() {
      cov_1z72a6923t().f[35]++;
      cov_1z72a6923t().s[163]++;
      return Object.assign({}, this.currentConditions);
    }
  }, {
    key: "updateResourceUsage",
    value: function updateResourceUsage() {
      cov_1z72a6923t().f[36]++;
      cov_1z72a6923t().s[164]++;
      this.currentUsage = {
        cpu: Math.random() * 0.5,
        memory: Math.random() * 128 * 1024 * 1024,
        network: Math.random() * 1024 * 1024,
        battery: Math.random() * 5
      };
      cov_1z72a6923t().s[165]++;
      this.currentConditions = {
        appState: AppState.currentState,
        batteryLevel: 75 + Math.random() * 25,
        networkType: 'wifi',
        isCharging: Math.random() > 0.7
      };
    }
  }]);
}();
export var backgroundProcessingManager = (cov_1z72a6923t().s[166]++, new BackgroundProcessingManager());
export default backgroundProcessingManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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