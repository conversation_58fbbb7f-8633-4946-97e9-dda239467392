{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "API", "addWhitelistedInterpolationParam", "addWhitelistedStyleProp", "addWhitelistedTransformProp", "assertNativeAnimatedModule", "generateNewAnimationId", "generateNewNodeTag", "isSupportedColorStyleProp", "isSupportedInterpolationParam", "isSupportedStyleProp", "isSupportedTransformProp", "shouldUseNativeDriver", "transformDataType", "validateInterpolation", "validateStyles", "validateTransform", "_objectSpread2", "_NativeAnimatedModule", "_NativeAnimatedTurboModule", "_NativeEventEmitter", "_Platform", "_ReactNativeFeatureFlags", "_invariant", "_RCTDeviceEventEmitter", "NativeAnimatedModule", "OS", "global", "RN$Bridgeless", "__nativeAnimatedNodeTagCount", "__nativeAnimationIdCount", "nativeEventEmitter", "waitingForQueuedOperations", "Set", "queueOperations", "queue", "singleOpQueue", "useSingleOpBatching", "queueAndExecuteBatchedOperations", "animatedShouldUseSingleOp", "flushQueueTimeout", "eventListenerGetValueCallbacks", "eventListenerAnimationFinishedCallbacks", "globalEventEmitterGetValueListener", "globalEventEmitterAnimationFinishedListener", "nativeOps", "apis", "reduce", "acc", "functionName", "i", "getValue", "tag", "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "queueOperation", "setWaitingForIdentifier", "id", "add", "animatedShouldDebounceQueueFlush", "clearTimeout", "unsetWaitingForIdentifier", "delete", "size", "disableQueue", "prevTimeout", "clearImmediate", "setImmediate", "flushQueue", "fn", "_len", "arguments", "length", "args", "Array", "_key", "push", "apply", "concat", "createAnimatedNode", "config", "updateAnimatedNodeConfig", "startListeningToAnimatedNodeValue", "stopListeningToAnimatedNodeValue", "connectAnimatedNodes", "parentTag", "childTag", "disconnectAnimatedNodes", "startAnimatingNode", "animationId", "nodeTag", "endCallback", "stopAnimation", "setAnimatedNodeValue", "value", "setAnimatedNodeOffset", "offset", "flattenAnimatedNodeOffset", "extractAnimatedNodeOffset", "connectAnimatedNodeToView", "viewTag", "disconnectAnimatedNodeFromView", "restoreDefaultValues", "dropAnimatedNode", "addAnimatedEventToView", "eventName", "eventMapping", "removeAnimatedEventFromView", "animatedNodeTag", "setupGlobalEventEmitterListeners", "addListener", "params", "callback", "SUPPORTED_COLOR_STYLES", "backgroundColor", "borderBottomColor", "borderColor", "borderEndColor", "borderLeftColor", "borderRightColor", "borderStartColor", "borderTopColor", "color", "tintColor", "SUPPORTED_STYLES", "borderBottomEndRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "borderBottomStartRadius", "borderRadius", "borderTopEndRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderTopStartRadius", "elevation", "opacity", "transform", "zIndex", "shadowOpacity", "shadowRadius", "scaleX", "scaleY", "translateX", "translateY", "SUPPORTED_TRANSFORMS", "scale", "rotate", "rotateX", "rotateY", "rotateZ", "perspective", "SUPPORTED_INTERPOLATION_PARAMS", "inputRange", "outputRange", "extrapolate", "extrapolateRight", "extrapolateLeft", "prop", "param", "hasOwnProperty", "configs", "for<PERSON>ach", "property", "Error", "styles", "_key2", "_key3", "_warnedMissingNativeAnimated", "useNativeDriver", "console", "warn", "test", "degrees", "parseFloat", "radians", "Math", "PI", "_default"], "sources": ["NativeAnimatedHelper.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.API = void 0;\nexports.addWhitelistedInterpolationParam = addWhitelistedInterpolationParam;\nexports.addWhitelistedStyleProp = addWhitelistedStyleProp;\nexports.addWhitelistedTransformProp = addWhitelistedTransformProp;\nexports.assertNativeAnimatedModule = assertNativeAnimatedModule;\nexports.default = void 0;\nexports.generateNewAnimationId = generateNewAnimationId;\nexports.generateNewNodeTag = generateNewNodeTag;\nexports.isSupportedColorStyleProp = isSupportedColorStyleProp;\nexports.isSupportedInterpolationParam = isSupportedInterpolationParam;\nexports.isSupportedStyleProp = isSupportedStyleProp;\nexports.isSupportedTransformProp = isSupportedTransformProp;\nexports.shouldUseNativeDriver = shouldUseNativeDriver;\nexports.transformDataType = transformDataType;\nexports.validateInterpolation = validateInterpolation;\nexports.validateStyles = validateStyles;\nexports.validateTransform = validateTransform;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _NativeAnimatedModule = _interopRequireDefault(require(\"./NativeAnimatedModule\"));\nvar _NativeAnimatedTurboModule = _interopRequireDefault(require(\"./NativeAnimatedTurboModule\"));\nvar _NativeEventEmitter = _interopRequireDefault(require(\"../EventEmitter/NativeEventEmitter\"));\nvar _Platform = _interopRequireDefault(require(\"../Utilities/Platform\"));\nvar _ReactNativeFeatureFlags = _interopRequireDefault(require(\"../ReactNative/ReactNativeFeatureFlags\"));\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nvar _RCTDeviceEventEmitter = _interopRequireDefault(require(\"../EventEmitter/RCTDeviceEventEmitter\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n// TODO ********* @petetheheat - Delete this fork when Fabric ships to 100%.\nvar NativeAnimatedModule = _Platform.default.OS === 'ios' && global.RN$Bridgeless === true ? _NativeAnimatedTurboModule.default : _NativeAnimatedModule.default;\nvar __nativeAnimatedNodeTagCount = 1; /* used for animated nodes */\nvar __nativeAnimationIdCount = 1; /* used for started animations */\n\nvar nativeEventEmitter;\nvar waitingForQueuedOperations = new Set();\nvar queueOperations = false;\nvar queue = [];\n// $FlowFixMe\nvar singleOpQueue = [];\nvar useSingleOpBatching = false;\n_Platform.default.OS === 'android' && !!(NativeAnimatedModule != null && NativeAnimatedModule.queueAndExecuteBatchedOperations) && _ReactNativeFeatureFlags.default.animatedShouldUseSingleOp();\nvar flushQueueTimeout = null;\nvar eventListenerGetValueCallbacks = {};\nvar eventListenerAnimationFinishedCallbacks = {};\nvar globalEventEmitterGetValueListener = null;\nvar globalEventEmitterAnimationFinishedListener = null;\nvar nativeOps = useSingleOpBatching ? function () {\n  var apis = ['createAnimatedNode',\n  // 1\n  'updateAnimatedNodeConfig',\n  // 2\n  'getValue',\n  // 3\n  'startListeningToAnimatedNodeValue',\n  // 4\n  'stopListeningToAnimatedNodeValue',\n  // 5\n  'connectAnimatedNodes',\n  // 6\n  'disconnectAnimatedNodes',\n  // 7\n  'startAnimatingNode',\n  // 8\n  'stopAnimation',\n  // 9\n  'setAnimatedNodeValue',\n  // 10\n  'setAnimatedNodeOffset',\n  // 11\n  'flattenAnimatedNodeOffset',\n  // 12\n  'extractAnimatedNodeOffset',\n  // 13\n  'connectAnimatedNodeToView',\n  // 14\n  'disconnectAnimatedNodeFromView',\n  // 15\n  'restoreDefaultValues',\n  // 16\n  'dropAnimatedNode',\n  // 17\n  'addAnimatedEventToView',\n  // 18\n  'removeAnimatedEventFromView',\n  // 19\n  'addListener',\n  // 20\n  'removeListener' // 21\n  ];\n  return apis.reduce((acc, functionName, i) => {\n    // These indices need to be kept in sync with the indices in native (see NativeAnimatedModule in Java, or the equivalent for any other native platform).\n    // $FlowFixMe[prop-missing]\n    acc[functionName] = i + 1;\n    return acc;\n  }, {});\n}() : NativeAnimatedModule;\n\n/**\n * Wrappers around NativeAnimatedModule to provide flow and autocomplete support for\n * the native module methods, and automatic queue management on Android\n */\nvar API = exports.API = {\n  getValue: function getValue(tag, saveValueCallback) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    if (useSingleOpBatching) {\n      if (saveValueCallback) {\n        eventListenerGetValueCallbacks[tag] = saveValueCallback;\n      }\n      // $FlowFixMe\n      API.queueOperation(nativeOps.getValue, tag);\n    } else {\n      API.queueOperation(nativeOps.getValue, tag, saveValueCallback);\n    }\n  },\n  setWaitingForIdentifier: function setWaitingForIdentifier(id) {\n    waitingForQueuedOperations.add(id);\n    queueOperations = true;\n    if (_ReactNativeFeatureFlags.default.animatedShouldDebounceQueueFlush() && flushQueueTimeout) {\n      clearTimeout(flushQueueTimeout);\n    }\n  },\n  unsetWaitingForIdentifier: function unsetWaitingForIdentifier(id) {\n    waitingForQueuedOperations.delete(id);\n    if (waitingForQueuedOperations.size === 0) {\n      queueOperations = false;\n      API.disableQueue();\n    }\n  },\n  disableQueue: function disableQueue() {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    if (_ReactNativeFeatureFlags.default.animatedShouldDebounceQueueFlush()) {\n      var prevTimeout = flushQueueTimeout;\n      clearImmediate(prevTimeout);\n      flushQueueTimeout = setImmediate(API.flushQueue);\n    } else {\n      API.flushQueue();\n    }\n  },\n  flushQueue: function flushQueue() {\n    /*\n    invariant(NativeAnimatedModule, 'Native animated module is not available');\n    flushQueueTimeout = null;\n     // Early returns before calling any APIs\n    if (useSingleOpBatching && singleOpQueue.length === 0) {\n      return;\n    }\n    if (!useSingleOpBatching && queue.length === 0) {\n      return;\n    }\n     if (useSingleOpBatching) {\n      // Set up event listener for callbacks if it's not set up\n      if (\n        !globalEventEmitterGetValueListener ||\n        !globalEventEmitterAnimationFinishedListener\n      ) {\n        setupGlobalEventEmitterListeners();\n      }\n      // Single op batching doesn't use callback functions, instead we\n      // use RCTDeviceEventEmitter. This reduces overhead of sending lots of\n      // JSI functions across to native code; but also, TM infrastructure currently\n      // does not support packing a function into native arrays.\n      NativeAnimatedModule.queueAndExecuteBatchedOperations?.(singleOpQueue);\n      singleOpQueue.length = 0;\n    } else {\n      Platform.OS === 'android' && NativeAnimatedModule.startOperationBatch?.();\n      for (let q = 0, l = queue.length; q < l; q++) {\n        queue[q]();\n      }\n      queue.length = 0;\n      Platform.OS === 'android' &&\n        NativeAnimatedModule.finishOperationBatch?.();\n    }\n    */\n  },\n  queueOperation: function queueOperation(fn) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    if (useSingleOpBatching) {\n      // Get the command ID from the queued function, and push that ID and any arguments needed to execute the operation\n      // $FlowFixMe: surprise, fn is actually a number\n      singleOpQueue.push(fn, ...args);\n      return;\n    }\n\n    // If queueing is explicitly on, *or* the queue has not yet\n    // been flushed, use the queue. This is to prevent operations\n    // from being executed out of order.\n    if (queueOperations || queue.length !== 0) {\n      queue.push(() => fn(...args));\n    } else {\n      fn(...args);\n    }\n  },\n  createAnimatedNode: function createAnimatedNode(tag, config) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.createAnimatedNode, tag, config);\n  },\n  updateAnimatedNodeConfig: function updateAnimatedNodeConfig(tag, config) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    //if (nativeOps.updateAnimatedNodeConfig) {\n    //  API.queueOperation(nativeOps.updateAnimatedNodeConfig, tag, config);\n    //}\n  },\n  startListeningToAnimatedNodeValue: function startListeningToAnimatedNodeValue(tag) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.startListeningToAnimatedNodeValue, tag);\n  },\n  stopListeningToAnimatedNodeValue: function stopListeningToAnimatedNodeValue(tag) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.stopListeningToAnimatedNodeValue, tag);\n  },\n  connectAnimatedNodes: function connectAnimatedNodes(parentTag, childTag) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.connectAnimatedNodes, parentTag, childTag);\n  },\n  disconnectAnimatedNodes: function disconnectAnimatedNodes(parentTag, childTag) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.disconnectAnimatedNodes, parentTag, childTag);\n  },\n  startAnimatingNode: function startAnimatingNode(animationId, nodeTag, config, endCallback) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    if (useSingleOpBatching) {\n      if (endCallback) {\n        eventListenerAnimationFinishedCallbacks[animationId] = endCallback;\n      }\n      // $FlowFixMe\n      API.queueOperation(nativeOps.startAnimatingNode, animationId, nodeTag, config);\n    } else {\n      API.queueOperation(nativeOps.startAnimatingNode, animationId, nodeTag, config, endCallback);\n    }\n  },\n  stopAnimation: function stopAnimation(animationId) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.stopAnimation, animationId);\n  },\n  setAnimatedNodeValue: function setAnimatedNodeValue(nodeTag, value) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.setAnimatedNodeValue, nodeTag, value);\n  },\n  setAnimatedNodeOffset: function setAnimatedNodeOffset(nodeTag, offset) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.setAnimatedNodeOffset, nodeTag, offset);\n  },\n  flattenAnimatedNodeOffset: function flattenAnimatedNodeOffset(nodeTag) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.flattenAnimatedNodeOffset, nodeTag);\n  },\n  extractAnimatedNodeOffset: function extractAnimatedNodeOffset(nodeTag) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.extractAnimatedNodeOffset, nodeTag);\n  },\n  connectAnimatedNodeToView: function connectAnimatedNodeToView(nodeTag, viewTag) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.connectAnimatedNodeToView, nodeTag, viewTag);\n  },\n  disconnectAnimatedNodeFromView: function disconnectAnimatedNodeFromView(nodeTag, viewTag) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.disconnectAnimatedNodeFromView, nodeTag, viewTag);\n  },\n  restoreDefaultValues: function restoreDefaultValues(nodeTag) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    // Backwards compat with older native runtimes, can be removed later.\n    if (nativeOps.restoreDefaultValues != null) {\n      API.queueOperation(nativeOps.restoreDefaultValues, nodeTag);\n    }\n  },\n  dropAnimatedNode: function dropAnimatedNode(tag) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.dropAnimatedNode, tag);\n  },\n  addAnimatedEventToView: function addAnimatedEventToView(viewTag, eventName, eventMapping) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.addAnimatedEventToView, viewTag, eventName, eventMapping);\n  },\n  removeAnimatedEventFromView(viewTag, eventName, animatedNodeTag) {\n    (0, _invariant.default)(nativeOps, 'Native animated module is not available');\n    API.queueOperation(nativeOps.removeAnimatedEventFromView, viewTag, eventName, animatedNodeTag);\n  }\n};\nfunction setupGlobalEventEmitterListeners() {\n  globalEventEmitterGetValueListener = _RCTDeviceEventEmitter.default.addListener('onNativeAnimatedModuleGetValue', function (params) {\n    var tag = params.tag;\n    var callback = eventListenerGetValueCallbacks[tag];\n    if (!callback) {\n      return;\n    }\n    callback(params.value);\n    delete eventListenerGetValueCallbacks[tag];\n  });\n  globalEventEmitterAnimationFinishedListener = _RCTDeviceEventEmitter.default.addListener('onNativeAnimatedModuleAnimationFinished', function (params) {\n    var animationId = params.animationId;\n    var callback = eventListenerAnimationFinishedCallbacks[animationId];\n    if (!callback) {\n      return;\n    }\n    callback(params);\n    delete eventListenerAnimationFinishedCallbacks[animationId];\n  });\n}\n\n/**\n * Styles allowed by the native animated implementation.\n *\n * In general native animated implementation should support any numeric or color property that\n * doesn't need to be updated through the shadow view hierarchy (all non-layout properties).\n */\nvar SUPPORTED_COLOR_STYLES = {\n  backgroundColor: true,\n  borderBottomColor: true,\n  borderColor: true,\n  borderEndColor: true,\n  borderLeftColor: true,\n  borderRightColor: true,\n  borderStartColor: true,\n  borderTopColor: true,\n  color: true,\n  tintColor: true\n};\nvar SUPPORTED_STYLES = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, SUPPORTED_COLOR_STYLES), {}, {\n  borderBottomEndRadius: true,\n  borderBottomLeftRadius: true,\n  borderBottomRightRadius: true,\n  borderBottomStartRadius: true,\n  borderRadius: true,\n  borderTopEndRadius: true,\n  borderTopLeftRadius: true,\n  borderTopRightRadius: true,\n  borderTopStartRadius: true,\n  elevation: true,\n  opacity: true,\n  transform: true,\n  zIndex: true,\n  /* ios styles */\n  shadowOpacity: true,\n  shadowRadius: true,\n  /* legacy android transform properties */\n  scaleX: true,\n  scaleY: true,\n  translateX: true,\n  translateY: true\n});\nvar SUPPORTED_TRANSFORMS = {\n  translateX: true,\n  translateY: true,\n  scale: true,\n  scaleX: true,\n  scaleY: true,\n  rotate: true,\n  rotateX: true,\n  rotateY: true,\n  rotateZ: true,\n  perspective: true\n};\nvar SUPPORTED_INTERPOLATION_PARAMS = {\n  inputRange: true,\n  outputRange: true,\n  extrapolate: true,\n  extrapolateRight: true,\n  extrapolateLeft: true\n};\nfunction addWhitelistedStyleProp(prop) {\n  SUPPORTED_STYLES[prop] = true;\n}\nfunction addWhitelistedTransformProp(prop) {\n  SUPPORTED_TRANSFORMS[prop] = true;\n}\nfunction addWhitelistedInterpolationParam(param) {\n  SUPPORTED_INTERPOLATION_PARAMS[param] = true;\n}\nfunction isSupportedColorStyleProp(prop) {\n  return SUPPORTED_COLOR_STYLES.hasOwnProperty(prop);\n}\nfunction isSupportedStyleProp(prop) {\n  return SUPPORTED_STYLES.hasOwnProperty(prop);\n}\nfunction isSupportedTransformProp(prop) {\n  return SUPPORTED_TRANSFORMS.hasOwnProperty(prop);\n}\nfunction isSupportedInterpolationParam(param) {\n  return SUPPORTED_INTERPOLATION_PARAMS.hasOwnProperty(param);\n}\nfunction validateTransform(configs) {\n  configs.forEach(config => {\n    if (!isSupportedTransformProp(config.property)) {\n      throw new Error(\"Property '\" + config.property + \"' is not supported by native animated module\");\n    }\n  });\n}\nfunction validateStyles(styles) {\n  for (var _key2 in styles) {\n    if (!isSupportedStyleProp(_key2)) {\n      throw new Error(\"Style property '\" + _key2 + \"' is not supported by native animated module\");\n    }\n  }\n}\nfunction validateInterpolation(config) {\n  for (var _key3 in config) {\n    if (!isSupportedInterpolationParam(_key3)) {\n      throw new Error(\"Interpolation property '\" + _key3 + \"' is not supported by native animated module\");\n    }\n  }\n}\nfunction generateNewNodeTag() {\n  return __nativeAnimatedNodeTagCount++;\n}\nfunction generateNewAnimationId() {\n  return __nativeAnimationIdCount++;\n}\nfunction assertNativeAnimatedModule() {\n  (0, _invariant.default)(NativeAnimatedModule, 'Native animated module is not available');\n}\nvar _warnedMissingNativeAnimated = false;\nfunction shouldUseNativeDriver(config) {\n  if (config.useNativeDriver == null) {\n    console.warn('Animated: `useNativeDriver` was not specified. This is a required ' + 'option and must be explicitly set to `true` or `false`');\n  }\n  if (config.useNativeDriver === true && !NativeAnimatedModule) {\n    if (!_warnedMissingNativeAnimated) {\n      console.warn('Animated: `useNativeDriver` is not supported because the native ' + 'animated module is missing. Falling back to JS-based animation. To ' + 'resolve this, add `RCTAnimation` module to this app, or remove ' + '`useNativeDriver`. ' + 'Make sure to run `bundle exec pod install` first. Read more about autolinking: https://github.com/react-native-community/cli/blob/master/docs/autolinking.md');\n      _warnedMissingNativeAnimated = true;\n    }\n    return false;\n  }\n  return config.useNativeDriver || false;\n}\nfunction transformDataType(value) {\n  // Change the string type to number type so we can reuse the same logic in\n  // iOS and Android platform\n  if (typeof value !== 'string') {\n    return value;\n  }\n  if (/deg$/.test(value)) {\n    var degrees = parseFloat(value) || 0;\n    var radians = degrees * Math.PI / 180.0;\n    return radians;\n  } else {\n    return value;\n  }\n}\nvar _default = exports.default = {\n  API,\n  isSupportedColorStyleProp,\n  isSupportedStyleProp,\n  isSupportedTransformProp,\n  isSupportedInterpolationParam,\n  addWhitelistedStyleProp,\n  addWhitelistedTransformProp,\n  addWhitelistedInterpolationParam,\n  validateStyles,\n  validateTransform,\n  validateInterpolation,\n  generateNewNodeTag,\n  generateNewAnimationId,\n  assertNativeAnimatedModule,\n  shouldUseNativeDriver,\n  transformDataType,\n  // $FlowExpectedError[unsafe-getters-setters] - unsafe getter lint suppresion\n  // $FlowExpectedError[missing-type-arg] - unsafe getter lint suppresion\n  get nativeEventEmitter() {\n    if (!nativeEventEmitter) {\n      nativeEventEmitter = new _NativeEventEmitter.default(\n      // *********: NativeEventEmitter only used this parameter on iOS. Now it uses it on all platforms, so this code was modified automatically to preserve its behavior\n      // If you want to use the native module on other platforms, please remove this condition and test its behavior\n      _Platform.default.OS !== 'ios' ? null : NativeAnimatedModule);\n    }\n    return nativeEventEmitter;\n  }\n};"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,GAAG,GAAG,KAAK,CAAC;AACpBF,OAAO,CAACG,gCAAgC,GAAGA,gCAAgC;AAC3EH,OAAO,CAACI,uBAAuB,GAAGA,uBAAuB;AACzDJ,OAAO,CAACK,2BAA2B,GAAGA,2BAA2B;AACjEL,OAAO,CAACM,0BAA0B,GAAGA,0BAA0B;AAC/DN,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxBC,OAAO,CAACO,sBAAsB,GAAGA,sBAAsB;AACvDP,OAAO,CAACQ,kBAAkB,GAAGA,kBAAkB;AAC/CR,OAAO,CAACS,yBAAyB,GAAGA,yBAAyB;AAC7DT,OAAO,CAACU,6BAA6B,GAAGA,6BAA6B;AACrEV,OAAO,CAACW,oBAAoB,GAAGA,oBAAoB;AACnDX,OAAO,CAACY,wBAAwB,GAAGA,wBAAwB;AAC3DZ,OAAO,CAACa,qBAAqB,GAAGA,qBAAqB;AACrDb,OAAO,CAACc,iBAAiB,GAAGA,iBAAiB;AAC7Cd,OAAO,CAACe,qBAAqB,GAAGA,qBAAqB;AACrDf,OAAO,CAACgB,cAAc,GAAGA,cAAc;AACvChB,OAAO,CAACiB,iBAAiB,GAAGA,iBAAiB;AAC7C,IAAIC,cAAc,GAAGrB,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAIqB,qBAAqB,GAAGtB,sBAAsB,CAACC,OAAO,yBAAyB,CAAC,CAAC;AACrF,IAAIsB,0BAA0B,GAAGvB,sBAAsB,CAACC,OAAO,8BAA8B,CAAC,CAAC;AAC/F,IAAIuB,mBAAmB,GAAGxB,sBAAsB,CAACC,OAAO,qCAAqC,CAAC,CAAC;AAC/F,IAAIwB,SAAS,GAAGzB,sBAAsB,CAACC,OAAO,wBAAwB,CAAC,CAAC;AACxE,IAAIyB,wBAAwB,GAAG1B,sBAAsB,CAACC,OAAO,yCAAyC,CAAC,CAAC;AACxG,IAAI0B,UAAU,GAAG3B,sBAAsB,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAI2B,sBAAsB,GAAG5B,sBAAsB,CAACC,OAAO,wCAAwC,CAAC,CAAC;AAYrG,IAAI4B,oBAAoB,GAAGJ,SAAS,CAACvB,OAAO,CAAC4B,EAAE,KAAK,KAAK,IAAIC,MAAM,CAACC,aAAa,KAAK,IAAI,GAAGT,0BAA0B,CAACrB,OAAO,GAAGoB,qBAAqB,CAACpB,OAAO;AAC/J,IAAI+B,4BAA4B,GAAG,CAAC;AACpC,IAAIC,wBAAwB,GAAG,CAAC;AAEhC,IAAIC,kBAAkB;AACtB,IAAIC,0BAA0B,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC1C,IAAIC,eAAe,GAAG,KAAK;AAC3B,IAAIC,KAAK,GAAG,EAAE;AAEd,IAAIC,aAAa,GAAG,EAAE;AACtB,IAAIC,mBAAmB,GAAG,KAAK;AAC/BhB,SAAS,CAACvB,OAAO,CAAC4B,EAAE,KAAK,SAAS,IAAI,CAAC,EAAED,oBAAoB,IAAI,IAAI,IAAIA,oBAAoB,CAACa,gCAAgC,CAAC,IAAIhB,wBAAwB,CAACxB,OAAO,CAACyC,yBAAyB,CAAC,CAAC;AAC/L,IAAIC,iBAAiB,GAAG,IAAI;AAC5B,IAAIC,8BAA8B,GAAG,CAAC,CAAC;AACvC,IAAIC,uCAAuC,GAAG,CAAC,CAAC;AAChD,IAAIC,kCAAkC,GAAG,IAAI;AAC7C,IAAIC,2CAA2C,GAAG,IAAI;AACtD,IAAIC,SAAS,GAAGR,mBAAmB,GAAG,YAAY;EAChD,IAAIS,IAAI,GAAG,CAAC,oBAAoB,EAEhC,0BAA0B,EAE1B,UAAU,EAEV,mCAAmC,EAEnC,kCAAkC,EAElC,sBAAsB,EAEtB,yBAAyB,EAEzB,oBAAoB,EAEpB,eAAe,EAEf,sBAAsB,EAEtB,uBAAuB,EAEvB,2BAA2B,EAE3B,2BAA2B,EAE3B,2BAA2B,EAE3B,gCAAgC,EAEhC,sBAAsB,EAEtB,kBAAkB,EAElB,wBAAwB,EAExB,6BAA6B,EAE7B,aAAa,EAEb,gBAAgB,CACf;EACD,OAAOA,IAAI,CAACC,MAAM,CAAC,UAACC,GAAG,EAAEC,YAAY,EAAEC,CAAC,EAAK;IAG3CF,GAAG,CAACC,YAAY,CAAC,GAAGC,CAAC,GAAG,CAAC;IACzB,OAAOF,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC,CAAC,CAAC,GAAGvB,oBAAoB;AAM1B,IAAIxB,GAAG,GAAGF,OAAO,CAACE,GAAG,GAAG;EACtBkD,QAAQ,EAAE,SAASA,QAAQA,CAACC,GAAG,EAAEC,iBAAiB,EAAE;IAClD,CAAC,CAAC,EAAE9B,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E,IAAIR,mBAAmB,EAAE;MACvB,IAAIgB,iBAAiB,EAAE;QACrBZ,8BAA8B,CAACW,GAAG,CAAC,GAAGC,iBAAiB;MACzD;MAEApD,GAAG,CAACqD,cAAc,CAACT,SAAS,CAACM,QAAQ,EAAEC,GAAG,CAAC;IAC7C,CAAC,MAAM;MACLnD,GAAG,CAACqD,cAAc,CAACT,SAAS,CAACM,QAAQ,EAAEC,GAAG,EAAEC,iBAAiB,CAAC;IAChE;EACF,CAAC;EACDE,uBAAuB,EAAE,SAASA,uBAAuBA,CAACC,EAAE,EAAE;IAC5DxB,0BAA0B,CAACyB,GAAG,CAACD,EAAE,CAAC;IAClCtB,eAAe,GAAG,IAAI;IACtB,IAAIZ,wBAAwB,CAACxB,OAAO,CAAC4D,gCAAgC,CAAC,CAAC,IAAIlB,iBAAiB,EAAE;MAC5FmB,YAAY,CAACnB,iBAAiB,CAAC;IACjC;EACF,CAAC;EACDoB,yBAAyB,EAAE,SAASA,yBAAyBA,CAACJ,EAAE,EAAE;IAChExB,0BAA0B,CAAC6B,MAAM,CAACL,EAAE,CAAC;IACrC,IAAIxB,0BAA0B,CAAC8B,IAAI,KAAK,CAAC,EAAE;MACzC5B,eAAe,GAAG,KAAK;MACvBjC,GAAG,CAAC8D,YAAY,CAAC,CAAC;IACpB;EACF,CAAC;EACDA,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;IACpC,CAAC,CAAC,EAAExC,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E,IAAIvB,wBAAwB,CAACxB,OAAO,CAAC4D,gCAAgC,CAAC,CAAC,EAAE;MACvE,IAAIM,WAAW,GAAGxB,iBAAiB;MACnCyB,cAAc,CAACD,WAAW,CAAC;MAC3BxB,iBAAiB,GAAG0B,YAAY,CAACjE,GAAG,CAACkE,UAAU,CAAC;IAClD,CAAC,MAAM;MACLlE,GAAG,CAACkE,UAAU,CAAC,CAAC;IAClB;EACF,CAAC;EACDA,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG,CAmClC,CAAC;EACDb,cAAc,EAAE,SAASA,cAAcA,CAACc,EAAE,EAAE;IAC1C,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAClC;IACA,IAAIrC,mBAAmB,EAAE;MAGvBD,aAAa,CAACuC,IAAI,CAAAC,KAAA,CAAlBxC,aAAa,GAAMgC,EAAE,EAAAS,MAAA,CAAKL,IAAI,EAAC;MAC/B;IACF;IAKA,IAAItC,eAAe,IAAIC,KAAK,CAACoC,MAAM,KAAK,CAAC,EAAE;MACzCpC,KAAK,CAACwC,IAAI,CAAC;QAAA,OAAMP,EAAE,CAAAQ,KAAA,SAAIJ,IAAI,CAAC;MAAA,EAAC;IAC/B,CAAC,MAAM;MACLJ,EAAE,CAAAQ,KAAA,SAAIJ,IAAI,CAAC;IACb;EACF,CAAC;EACDM,kBAAkB,EAAE,SAASA,kBAAkBA,CAAC1B,GAAG,EAAE2B,MAAM,EAAE;IAC3D,CAAC,CAAC,EAAExD,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAACiC,kBAAkB,EAAE1B,GAAG,EAAE2B,MAAM,CAAC;EAC/D,CAAC;EACDC,wBAAwB,EAAE,SAASA,wBAAwBA,CAAC5B,GAAG,EAAE2B,MAAM,EAAE;IACvE,CAAC,CAAC,EAAExD,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;EAI/E,CAAC;EACDoC,iCAAiC,EAAE,SAASA,iCAAiCA,CAAC7B,GAAG,EAAE;IACjF,CAAC,CAAC,EAAE7B,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAACoC,iCAAiC,EAAE7B,GAAG,CAAC;EACtE,CAAC;EACD8B,gCAAgC,EAAE,SAASA,gCAAgCA,CAAC9B,GAAG,EAAE;IAC/E,CAAC,CAAC,EAAE7B,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAACqC,gCAAgC,EAAE9B,GAAG,CAAC;EACrE,CAAC;EACD+B,oBAAoB,EAAE,SAASA,oBAAoBA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IACvE,CAAC,CAAC,EAAE9D,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAACsC,oBAAoB,EAAEC,SAAS,EAAEC,QAAQ,CAAC;EACzE,CAAC;EACDC,uBAAuB,EAAE,SAASA,uBAAuBA,CAACF,SAAS,EAAEC,QAAQ,EAAE;IAC7E,CAAC,CAAC,EAAE9D,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAACyC,uBAAuB,EAAEF,SAAS,EAAEC,QAAQ,CAAC;EAC5E,CAAC;EACDE,kBAAkB,EAAE,SAASA,kBAAkBA,CAACC,WAAW,EAAEC,OAAO,EAAEV,MAAM,EAAEW,WAAW,EAAE;IACzF,CAAC,CAAC,EAAEnE,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E,IAAIR,mBAAmB,EAAE;MACvB,IAAIqD,WAAW,EAAE;QACfhD,uCAAuC,CAAC8C,WAAW,CAAC,GAAGE,WAAW;MACpE;MAEAzF,GAAG,CAACqD,cAAc,CAACT,SAAS,CAAC0C,kBAAkB,EAAEC,WAAW,EAAEC,OAAO,EAAEV,MAAM,CAAC;IAChF,CAAC,MAAM;MACL9E,GAAG,CAACqD,cAAc,CAACT,SAAS,CAAC0C,kBAAkB,EAAEC,WAAW,EAAEC,OAAO,EAAEV,MAAM,EAAEW,WAAW,CAAC;IAC7F;EACF,CAAC;EACDC,aAAa,EAAE,SAASA,aAAaA,CAACH,WAAW,EAAE;IACjD,CAAC,CAAC,EAAEjE,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAAC8C,aAAa,EAAEH,WAAW,CAAC;EAC1D,CAAC;EACDI,oBAAoB,EAAE,SAASA,oBAAoBA,CAACH,OAAO,EAAEI,KAAK,EAAE;IAClE,CAAC,CAAC,EAAEtE,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAAC+C,oBAAoB,EAAEH,OAAO,EAAEI,KAAK,CAAC;EACpE,CAAC;EACDC,qBAAqB,EAAE,SAASA,qBAAqBA,CAACL,OAAO,EAAEM,MAAM,EAAE;IACrE,CAAC,CAAC,EAAExE,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAACiD,qBAAqB,EAAEL,OAAO,EAAEM,MAAM,CAAC;EACtE,CAAC;EACDC,yBAAyB,EAAE,SAASA,yBAAyBA,CAACP,OAAO,EAAE;IACrE,CAAC,CAAC,EAAElE,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAACmD,yBAAyB,EAAEP,OAAO,CAAC;EAClE,CAAC;EACDQ,yBAAyB,EAAE,SAASA,yBAAyBA,CAACR,OAAO,EAAE;IACrE,CAAC,CAAC,EAAElE,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAACoD,yBAAyB,EAAER,OAAO,CAAC;EAClE,CAAC;EACDS,yBAAyB,EAAE,SAASA,yBAAyBA,CAACT,OAAO,EAAEU,OAAO,EAAE;IAC9E,CAAC,CAAC,EAAE5E,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAACqD,yBAAyB,EAAET,OAAO,EAAEU,OAAO,CAAC;EAC3E,CAAC;EACDC,8BAA8B,EAAE,SAASA,8BAA8BA,CAACX,OAAO,EAAEU,OAAO,EAAE;IACxF,CAAC,CAAC,EAAE5E,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAACuD,8BAA8B,EAAEX,OAAO,EAAEU,OAAO,CAAC;EAChF,CAAC;EACDE,oBAAoB,EAAE,SAASA,oBAAoBA,CAACZ,OAAO,EAAE;IAC3D,CAAC,CAAC,EAAElE,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAE7E,IAAIA,SAAS,CAACwD,oBAAoB,IAAI,IAAI,EAAE;MAC1CpG,GAAG,CAACqD,cAAc,CAACT,SAAS,CAACwD,oBAAoB,EAAEZ,OAAO,CAAC;IAC7D;EACF,CAAC;EACDa,gBAAgB,EAAE,SAASA,gBAAgBA,CAAClD,GAAG,EAAE;IAC/C,CAAC,CAAC,EAAE7B,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAACyD,gBAAgB,EAAElD,GAAG,CAAC;EACrD,CAAC;EACDmD,sBAAsB,EAAE,SAASA,sBAAsBA,CAACJ,OAAO,EAAEK,SAAS,EAAEC,YAAY,EAAE;IACxF,CAAC,CAAC,EAAElF,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAAC0D,sBAAsB,EAAEJ,OAAO,EAAEK,SAAS,EAAEC,YAAY,CAAC;EACxF,CAAC;EACDC,2BAA2B,WAA3BA,2BAA2BA,CAACP,OAAO,EAAEK,SAAS,EAAEG,eAAe,EAAE;IAC/D,CAAC,CAAC,EAAEpF,UAAU,CAACzB,OAAO,EAAE+C,SAAS,EAAE,yCAAyC,CAAC;IAC7E5C,GAAG,CAACqD,cAAc,CAACT,SAAS,CAAC6D,2BAA2B,EAAEP,OAAO,EAAEK,SAAS,EAAEG,eAAe,CAAC;EAChG;AACF,CAAC;AACD,SAASC,gCAAgCA,CAAA,EAAG;EAC1CjE,kCAAkC,GAAGnB,sBAAsB,CAAC1B,OAAO,CAAC+G,WAAW,CAAC,gCAAgC,EAAE,UAAUC,MAAM,EAAE;IAClI,IAAI1D,GAAG,GAAG0D,MAAM,CAAC1D,GAAG;IACpB,IAAI2D,QAAQ,GAAGtE,8BAA8B,CAACW,GAAG,CAAC;IAClD,IAAI,CAAC2D,QAAQ,EAAE;MACb;IACF;IACAA,QAAQ,CAACD,MAAM,CAACjB,KAAK,CAAC;IACtB,OAAOpD,8BAA8B,CAACW,GAAG,CAAC;EAC5C,CAAC,CAAC;EACFR,2CAA2C,GAAGpB,sBAAsB,CAAC1B,OAAO,CAAC+G,WAAW,CAAC,yCAAyC,EAAE,UAAUC,MAAM,EAAE;IACpJ,IAAItB,WAAW,GAAGsB,MAAM,CAACtB,WAAW;IACpC,IAAIuB,QAAQ,GAAGrE,uCAAuC,CAAC8C,WAAW,CAAC;IACnE,IAAI,CAACuB,QAAQ,EAAE;MACb;IACF;IACAA,QAAQ,CAACD,MAAM,CAAC;IAChB,OAAOpE,uCAAuC,CAAC8C,WAAW,CAAC;EAC7D,CAAC,CAAC;AACJ;AAQA,IAAIwB,sBAAsB,GAAG;EAC3BC,eAAe,EAAE,IAAI;EACrBC,iBAAiB,EAAE,IAAI;EACvBC,WAAW,EAAE,IAAI;EACjBC,cAAc,EAAE,IAAI;EACpBC,eAAe,EAAE,IAAI;EACrBC,gBAAgB,EAAE,IAAI;EACtBC,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,IAAI;EACpBC,KAAK,EAAE,IAAI;EACXC,SAAS,EAAE;AACb,CAAC;AACD,IAAIC,gBAAgB,GAAG,CAAC,CAAC,EAAE1G,cAAc,CAACnB,OAAO,EAAE,CAAC,CAAC,EAAEmB,cAAc,CAACnB,OAAO,EAAE,CAAC,CAAC,EAAEkH,sBAAsB,CAAC,EAAE,CAAC,CAAC,EAAE;EAC9GY,qBAAqB,EAAE,IAAI;EAC3BC,sBAAsB,EAAE,IAAI;EAC5BC,uBAAuB,EAAE,IAAI;EAC7BC,uBAAuB,EAAE,IAAI;EAC7BC,YAAY,EAAE,IAAI;EAClBC,kBAAkB,EAAE,IAAI;EACxBC,mBAAmB,EAAE,IAAI;EACzBC,oBAAoB,EAAE,IAAI;EAC1BC,oBAAoB,EAAE,IAAI;EAC1BC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,IAAI;EACbC,SAAS,EAAE,IAAI;EACfC,MAAM,EAAE,IAAI;EAEZC,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,IAAI;EAElBC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE;AACd,CAAC,CAAC;AACF,IAAIC,oBAAoB,GAAG;EACzBF,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBE,KAAK,EAAE,IAAI;EACXL,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZK,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE,IAAI;EACbC,WAAW,EAAE;AACf,CAAC;AACD,IAAIC,8BAA8B,GAAG;EACnCC,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,gBAAgB,EAAE,IAAI;EACtBC,eAAe,EAAE;AACnB,CAAC;AACD,SAASxJ,uBAAuBA,CAACyJ,IAAI,EAAE;EACrCjC,gBAAgB,CAACiC,IAAI,CAAC,GAAG,IAAI;AAC/B;AACA,SAASxJ,2BAA2BA,CAACwJ,IAAI,EAAE;EACzCb,oBAAoB,CAACa,IAAI,CAAC,GAAG,IAAI;AACnC;AACA,SAAS1J,gCAAgCA,CAAC2J,KAAK,EAAE;EAC/CP,8BAA8B,CAACO,KAAK,CAAC,GAAG,IAAI;AAC9C;AACA,SAASrJ,yBAAyBA,CAACoJ,IAAI,EAAE;EACvC,OAAO5C,sBAAsB,CAAC8C,cAAc,CAACF,IAAI,CAAC;AACpD;AACA,SAASlJ,oBAAoBA,CAACkJ,IAAI,EAAE;EAClC,OAAOjC,gBAAgB,CAACmC,cAAc,CAACF,IAAI,CAAC;AAC9C;AACA,SAASjJ,wBAAwBA,CAACiJ,IAAI,EAAE;EACtC,OAAOb,oBAAoB,CAACe,cAAc,CAACF,IAAI,CAAC;AAClD;AACA,SAASnJ,6BAA6BA,CAACoJ,KAAK,EAAE;EAC5C,OAAOP,8BAA8B,CAACQ,cAAc,CAACD,KAAK,CAAC;AAC7D;AACA,SAAS7I,iBAAiBA,CAAC+I,OAAO,EAAE;EAClCA,OAAO,CAACC,OAAO,CAAC,UAAAjF,MAAM,EAAI;IACxB,IAAI,CAACpE,wBAAwB,CAACoE,MAAM,CAACkF,QAAQ,CAAC,EAAE;MAC9C,MAAM,IAAIC,KAAK,CAAC,YAAY,GAAGnF,MAAM,CAACkF,QAAQ,GAAG,8CAA8C,CAAC;IAClG;EACF,CAAC,CAAC;AACJ;AACA,SAASlJ,cAAcA,CAACoJ,MAAM,EAAE;EAC9B,KAAK,IAAIC,KAAK,IAAID,MAAM,EAAE;IACxB,IAAI,CAACzJ,oBAAoB,CAAC0J,KAAK,CAAC,EAAE;MAChC,MAAM,IAAIF,KAAK,CAAC,kBAAkB,GAAGE,KAAK,GAAG,8CAA8C,CAAC;IAC9F;EACF;AACF;AACA,SAAStJ,qBAAqBA,CAACiE,MAAM,EAAE;EACrC,KAAK,IAAIsF,KAAK,IAAItF,MAAM,EAAE;IACxB,IAAI,CAACtE,6BAA6B,CAAC4J,KAAK,CAAC,EAAE;MACzC,MAAM,IAAIH,KAAK,CAAC,0BAA0B,GAAGG,KAAK,GAAG,8CAA8C,CAAC;IACtG;EACF;AACF;AACA,SAAS9J,kBAAkBA,CAAA,EAAG;EAC5B,OAAOsB,4BAA4B,EAAE;AACvC;AACA,SAASvB,sBAAsBA,CAAA,EAAG;EAChC,OAAOwB,wBAAwB,EAAE;AACnC;AACA,SAASzB,0BAA0BA,CAAA,EAAG;EACpC,CAAC,CAAC,EAAEkB,UAAU,CAACzB,OAAO,EAAE2B,oBAAoB,EAAE,yCAAyC,CAAC;AAC1F;AACA,IAAI6I,4BAA4B,GAAG,KAAK;AACxC,SAAS1J,qBAAqBA,CAACmE,MAAM,EAAE;EACrC,IAAIA,MAAM,CAACwF,eAAe,IAAI,IAAI,EAAE;IAClCC,OAAO,CAACC,IAAI,CAAC,oEAAoE,GAAG,wDAAwD,CAAC;EAC/I;EACA,IAAI1F,MAAM,CAACwF,eAAe,KAAK,IAAI,IAAI,CAAC9I,oBAAoB,EAAE;IAC5D,IAAI,CAAC6I,4BAA4B,EAAE;MACjCE,OAAO,CAACC,IAAI,CAAC,kEAAkE,GAAG,qEAAqE,GAAG,iEAAiE,GAAG,qBAAqB,GAAG,8JAA8J,CAAC;MACrZH,4BAA4B,GAAG,IAAI;IACrC;IACA,OAAO,KAAK;EACd;EACA,OAAOvF,MAAM,CAACwF,eAAe,IAAI,KAAK;AACxC;AACA,SAAS1J,iBAAiBA,CAACgF,KAAK,EAAE;EAGhC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd;EACA,IAAI,MAAM,CAAC6E,IAAI,CAAC7E,KAAK,CAAC,EAAE;IACtB,IAAI8E,OAAO,GAAGC,UAAU,CAAC/E,KAAK,CAAC,IAAI,CAAC;IACpC,IAAIgF,OAAO,GAAGF,OAAO,GAAGG,IAAI,CAACC,EAAE,GAAG,KAAK;IACvC,OAAOF,OAAO;EAChB,CAAC,MAAM;IACL,OAAOhF,KAAK;EACd;AACF;AACA,IAAImF,QAAQ,GAAGjL,OAAO,CAACD,OAAO,GAAG;EAC/BG,GAAG,EAAHA,GAAG;EACHO,yBAAyB,EAAzBA,yBAAyB;EACzBE,oBAAoB,EAApBA,oBAAoB;EACpBC,wBAAwB,EAAxBA,wBAAwB;EACxBF,6BAA6B,EAA7BA,6BAA6B;EAC7BN,uBAAuB,EAAvBA,uBAAuB;EACvBC,2BAA2B,EAA3BA,2BAA2B;EAC3BF,gCAAgC,EAAhCA,gCAAgC;EAChCa,cAAc,EAAdA,cAAc;EACdC,iBAAiB,EAAjBA,iBAAiB;EACjBF,qBAAqB,EAArBA,qBAAqB;EACrBP,kBAAkB,EAAlBA,kBAAkB;EAClBD,sBAAsB,EAAtBA,sBAAsB;EACtBD,0BAA0B,EAA1BA,0BAA0B;EAC1BO,qBAAqB,EAArBA,qBAAqB;EACrBC,iBAAiB,EAAjBA,iBAAiB;EAGjB,IAAIkB,kBAAkBA,CAAA,EAAG;IACvB,IAAI,CAACA,kBAAkB,EAAE;MACvBA,kBAAkB,GAAG,IAAIX,mBAAmB,CAACtB,OAAO,CAGpDuB,SAAS,CAACvB,OAAO,CAAC4B,EAAE,KAAK,KAAK,GAAG,IAAI,GAAGD,oBAAoB,CAAC;IAC/D;IACA,OAAOM,kBAAkB;EAC3B;AACF,CAAC", "ignoreList": []}