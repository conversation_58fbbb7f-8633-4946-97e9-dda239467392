{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_interopRequireDefault", "_interopRequireWildcard", "exports", "__esModule", "_extends2", "_objectWithoutPropertiesLoose2", "_Platform", "React", "_VirtualizedSectionList", "_excluded", "SectionList", "_React$PureComponent", "_this", "arguments", "_captureRef", "ref", "_wrapperListRef", "key", "value", "scrollToLocation", "params", "recordInteraction", "listRef", "getListRef", "flashScrollIndicators", "getScrollResponder", "getScrollableNode", "render", "_this$props", "props", "_stickySectionHeadersEnabled", "stickySectionHeadersEnabled", "restProps", "OS", "createElement", "getItemCount", "items", "length", "getItem", "index", "PureComponent", "module"], "sources": ["index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _Platform = _interopRequireDefault(require(\"../../../exports/Platform\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _VirtualizedSectionList = _interopRequireDefault(require(\"../VirtualizedSectionList\"));\nvar _excluded = [\"stickySectionHeadersEnabled\"];\n/**\n * A performant interface for rendering sectioned lists, supporting the most handy features:\n *\n *  - Fully cross-platform.\n *  - Configurable viewability callbacks.\n *  - List header support.\n *  - List footer support.\n *  - Item separator support.\n *  - Section header support.\n *  - Section separator support.\n *  - Heterogeneous data and item rendering support.\n *  - Pull to Refresh.\n *  - Scroll loading.\n *\n * If you don't need section support and want a simpler interface, use\n * [`<FlatList>`](https://reactnative.dev/docs/flatlist).\n *\n * Simple Examples:\n *\n *     <SectionList\n *       renderItem={({item}) => <ListItem title={item} />}\n *       renderSectionHeader={({section}) => <Header title={section.title} />}\n *       sections={[ // homogeneous rendering between sections\n *         {data: [...], title: ...},\n *         {data: [...], title: ...},\n *         {data: [...], title: ...},\n *       ]}\n *     />\n *\n *     <SectionList\n *       sections={[ // heterogeneous rendering between sections\n *         {data: [...], renderItem: ...},\n *         {data: [...], renderItem: ...},\n *         {data: [...], renderItem: ...},\n *       ]}\n *     />\n *\n * This is a convenience wrapper around [`<VirtualizedList>`](docs/virtualizedlist),\n * and thus inherits its props (as well as those of `ScrollView`) that aren't explicitly listed\n * here, along with the following caveats:\n *\n * - Internal state is not preserved when content scrolls out of the render window. Make sure all\n *   your data is captured in the item data or external stores like Flux, Redux, or Relay.\n * - This is a `PureComponent` which means that it will not re-render if `props` remain shallow-\n *   equal. Make sure that everything your `renderItem` function depends on is passed as a prop\n *   (e.g. `extraData`) that is not `===` after updates, otherwise your UI may not update on\n *   changes. This includes the `data` prop and parent component state.\n * - In order to constrain memory and enable smooth scrolling, content is rendered asynchronously\n *   offscreen. This means it's possible to scroll faster than the fill rate and momentarily see\n *   blank content. This is a tradeoff that can be adjusted to suit the needs of each application,\n *   and we are working on improving it behind the scenes.\n * - By default, the list looks for a `key` prop on each item and uses that for the React key.\n *   Alternatively, you can provide a custom `keyExtractor` prop.\n *\n */\nclass SectionList extends React.PureComponent {\n  constructor() {\n    super(...arguments);\n    this._captureRef = ref => {\n      this._wrapperListRef = ref;\n    };\n  }\n  /**\n   * Scrolls to the item at the specified `sectionIndex` and `itemIndex` (within the section)\n   * positioned in the viewable area such that `viewPosition` 0 places it at the top (and may be\n   * covered by a sticky header), 1 at the bottom, and 0.5 centered in the middle. `viewOffset` is a\n   * fixed number of pixels to offset the final target position, e.g. to compensate for sticky\n   * headers.\n   *\n   * Note: cannot scroll to locations outside the render window without specifying the\n   * `getItemLayout` prop.\n   */\n  scrollToLocation(params) {\n    if (this._wrapperListRef != null) {\n      this._wrapperListRef.scrollToLocation(params);\n    }\n  }\n\n  /**\n   * Tells the list an interaction has occurred, which should trigger viewability calculations, e.g.\n   * if `waitForInteractions` is true and the user has not scrolled. This is typically called by\n   * taps on items or by navigation actions.\n   */\n  recordInteraction() {\n    var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();\n    listRef && listRef.recordInteraction();\n  }\n\n  /**\n   * Displays the scroll indicators momentarily.\n   *\n   * @platform ios\n   */\n  flashScrollIndicators() {\n    var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();\n    listRef && listRef.flashScrollIndicators();\n  }\n\n  /**\n   * Provides a handle to the underlying scroll responder.\n   */\n  getScrollResponder() {\n    var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();\n    if (listRef) {\n      return listRef.getScrollResponder();\n    }\n  }\n  getScrollableNode() {\n    var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();\n    if (listRef) {\n      return listRef.getScrollableNode();\n    }\n  }\n  render() {\n    var _this$props = this.props,\n      _stickySectionHeadersEnabled = _this$props.stickySectionHeadersEnabled,\n      restProps = (0, _objectWithoutPropertiesLoose2.default)(_this$props, _excluded);\n    var stickySectionHeadersEnabled = _stickySectionHeadersEnabled !== null && _stickySectionHeadersEnabled !== void 0 ? _stickySectionHeadersEnabled : _Platform.default.OS === 'ios';\n    return /*#__PURE__*/React.createElement(_VirtualizedSectionList.default, (0, _extends2.default)({}, restProps, {\n      stickySectionHeadersEnabled: stickySectionHeadersEnabled,\n      ref: this._captureRef,\n      getItemCount: items => items.length,\n      getItem: (items, index) => items[index]\n    }));\n  }\n}\nexports.default = SectionList;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,UAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,SAAAM,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAJ,gBAAA,CAAAM,OAAA,EAAAF,CAAA,OAAAL,2BAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAL,gBAAA,CAAAM,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAEb,IAAIa,sBAAsB,GAAGpB,OAAO,CAAC,8CAA8C,CAAC,CAACU,OAAO;AAC5F,IAAIW,uBAAuB,GAAGrB,OAAO,CAAC,+CAA+C,CAAC,CAACU,OAAO;AAC9FY,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACZ,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIc,SAAS,GAAGJ,sBAAsB,CAACpB,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIyB,8BAA8B,GAAGL,sBAAsB,CAACpB,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAI0B,SAAS,GAAGN,sBAAsB,CAACpB,OAAO,4BAA4B,CAAC,CAAC;AAC5E,IAAI2B,KAAK,GAAGN,uBAAuB,CAACrB,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAI4B,uBAAuB,GAAGR,sBAAsB,CAACpB,OAAO,4BAA4B,CAAC,CAAC;AAC1F,IAAI6B,SAAS,GAAG,CAAC,6BAA6B,CAAC;AAAC,IAwD1CC,WAAW,aAAAC,oBAAA;EACf,SAAAD,YAAA,EAAc;IAAA,IAAAE,KAAA;IAAA,IAAA/B,gBAAA,CAAAS,OAAA,QAAAoB,WAAA;IACZE,KAAA,GAAA1B,UAAA,OAAAwB,WAAA,EAASG,SAAS;IAClBD,KAAA,CAAKE,WAAW,GAAG,UAAAC,GAAG,EAAI;MACxBH,KAAA,CAAKI,eAAe,GAAGD,GAAG;IAC5B,CAAC;IAAC,OAAAH,KAAA;EACJ;EAAC,IAAA3B,UAAA,CAAAK,OAAA,EAAAoB,WAAA,EAAAC,oBAAA;EAAA,WAAA7B,aAAA,CAAAQ,OAAA,EAAAoB,WAAA;IAAAO,GAAA;IAAAC,KAAA,EAWD,SAAAC,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAI,IAAI,CAACJ,eAAe,IAAI,IAAI,EAAE;QAChC,IAAI,CAACA,eAAe,CAACG,gBAAgB,CAACC,MAAM,CAAC;MAC/C;IACF;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAOD,SAAAG,iBAAiBA,CAAA,EAAG;MAClB,IAAIC,OAAO,GAAG,IAAI,CAACN,eAAe,IAAI,IAAI,CAACA,eAAe,CAACO,UAAU,CAAC,CAAC;MACvED,OAAO,IAAIA,OAAO,CAACD,iBAAiB,CAAC,CAAC;IACxC;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAOD,SAAAM,qBAAqBA,CAAA,EAAG;MACtB,IAAIF,OAAO,GAAG,IAAI,CAACN,eAAe,IAAI,IAAI,CAACA,eAAe,CAACO,UAAU,CAAC,CAAC;MACvED,OAAO,IAAIA,OAAO,CAACE,qBAAqB,CAAC,CAAC;IAC5C;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAKD,SAAAO,kBAAkBA,CAAA,EAAG;MACnB,IAAIH,OAAO,GAAG,IAAI,CAACN,eAAe,IAAI,IAAI,CAACA,eAAe,CAACO,UAAU,CAAC,CAAC;MACvE,IAAID,OAAO,EAAE;QACX,OAAOA,OAAO,CAACG,kBAAkB,CAAC,CAAC;MACrC;IACF;EAAC;IAAAR,GAAA;IAAAC,KAAA,EACD,SAAAQ,iBAAiBA,CAAA,EAAG;MAClB,IAAIJ,OAAO,GAAG,IAAI,CAACN,eAAe,IAAI,IAAI,CAACA,eAAe,CAACO,UAAU,CAAC,CAAC;MACvE,IAAID,OAAO,EAAE;QACX,OAAOA,OAAO,CAACI,iBAAiB,CAAC,CAAC;MACpC;IACF;EAAC;IAAAT,GAAA;IAAAC,KAAA,EACD,SAAAS,MAAMA,CAAA,EAAG;MACP,IAAIC,WAAW,GAAG,IAAI,CAACC,KAAK;QAC1BC,4BAA4B,GAAGF,WAAW,CAACG,2BAA2B;QACtEC,SAAS,GAAG,CAAC,CAAC,EAAE3B,8BAA8B,CAACf,OAAO,EAAEsC,WAAW,EAAEnB,SAAS,CAAC;MACjF,IAAIsB,2BAA2B,GAAGD,4BAA4B,KAAK,IAAI,IAAIA,4BAA4B,KAAK,KAAK,CAAC,GAAGA,4BAA4B,GAAGxB,SAAS,CAAChB,OAAO,CAAC2C,EAAE,KAAK,KAAK;MAClL,OAAoB1B,KAAK,CAAC2B,aAAa,CAAC1B,uBAAuB,CAAClB,OAAO,EAAE,CAAC,CAAC,EAAEc,SAAS,CAACd,OAAO,EAAE,CAAC,CAAC,EAAE0C,SAAS,EAAE;QAC7GD,2BAA2B,EAAEA,2BAA2B;QACxDhB,GAAG,EAAE,IAAI,CAACD,WAAW;QACrBqB,YAAY,EAAE,SAAdA,YAAYA,CAAEC,KAAK;UAAA,OAAIA,KAAK,CAACC,MAAM;QAAA;QACnCC,OAAO,EAAE,SAATA,OAAOA,CAAGF,KAAK,EAAEG,KAAK;UAAA,OAAKH,KAAK,CAACG,KAAK,CAAC;QAAA;MACzC,CAAC,CAAC,CAAC;IACL;EAAC;AAAA,EArEuBhC,KAAK,CAACiC,aAAa;AAuE7CtC,OAAO,CAACZ,OAAO,GAAGoB,WAAW;AAC7B+B,MAAM,CAACvC,OAAO,GAAGA,OAAO,CAACZ,OAAO", "ignoreList": []}