dec046aacc5db24dfd08a8106482d144
'use strict';
exports.__esModule = true;
exports.default = void 0;
var deepDiffer = function deepDiffer(one, two, maxDepth) {
  if (maxDepth === void 0) {
    maxDepth = -1;
  }
  if (maxDepth === 0) {
    return true;
  }
  if (one === two) {
    return false;
  }
  if (typeof one === 'function' && typeof two === 'function') {
    return false;
  }
  if (typeof one !== 'object' || one === null) {
    return one !== two;
  }
  if (typeof two !== 'object' || two === null) {
    return true;
  }
  if (one.constructor !== two.constructor) {
    return true;
  }
  if (Array.isArray(one)) {
    var len = one.length;
    if (two.length !== len) {
      return true;
    }
    for (var ii = 0; ii < len; ii++) {
      if (deepDiffer(one[ii], two[ii], maxDepth - 1)) {
        return true;
      }
    }
  } else {
    for (var key in one) {
      if (deepDiffer(one[key], two[key], maxDepth - 1)) {
        return true;
      }
    }
    for (var twoKey in two) {
      if (one[twoKey] === undefined && two[twoKey] !== undefined) {
        return true;
      }
    }
  }
  return false;
};
var _default = exports.default = deepDiffer;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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