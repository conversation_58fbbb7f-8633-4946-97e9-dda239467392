6bd0e8256bbe23a63ec41e162e77e2f0
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_9l5fvvg8o() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\social\\SocialFeedScreen.tsx";
  var hash = "b44e34bb792e5b6876f5b8c356a1b8c003c13e46";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\social\\SocialFeedScreen.tsx",
    statementMap: {
      "0": {
        start: {
          line: 29,
          column: 30
        },
        end: {
          line: 29,
          column: 39
        }
      },
      "1": {
        start: {
          line: 30,
          column: 28
        },
        end: {
          line: 30,
          column: 54
        }
      },
      "2": {
        start: {
          line: 31,
          column: 32
        },
        end: {
          line: 31,
          column: 46
        }
      },
      "3": {
        start: {
          line: 32,
          column: 38
        },
        end: {
          line: 32,
          column: 53
        }
      },
      "4": {
        start: {
          line: 33,
          column: 40
        },
        end: {
          line: 33,
          column: 55
        }
      },
      "5": {
        start: {
          line: 35,
          column: 2
        },
        end: {
          line: 37,
          column: 9
        }
      },
      "6": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 36,
          column: 15
        }
      },
      "7": {
        start: {
          line: 39,
          column: 19
        },
        end: {
          line: 60,
          column: 3
        }
      },
      "8": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 59,
          column: 5
        }
      },
      "9": {
        start: {
          line: 41,
          column: 6
        },
        end: {
          line: 45,
          column: 7
        }
      },
      "10": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 28
        }
      },
      "11": {
        start: {
          line: 44,
          column: 8
        },
        end: {
          line: 44,
          column: 25
        }
      },
      "12": {
        start: {
          line: 47,
          column: 37
        },
        end: {
          line: 47,
          column: 77
        }
      },
      "13": {
        start: {
          line: 49,
          column: 6
        },
        end: {
          line: 53,
          column: 7
        }
      },
      "14": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 36
        }
      },
      "15": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 52,
          column: 23
        }
      },
      "16": {
        start: {
          line: 55,
          column: 6
        },
        end: {
          line: 55,
          column: 57
        }
      },
      "17": {
        start: {
          line: 57,
          column: 6
        },
        end: {
          line: 57,
          column: 24
        }
      },
      "18": {
        start: {
          line: 58,
          column: 6
        },
        end: {
          line: 58,
          column: 27
        }
      },
      "19": {
        start: {
          line: 62,
          column: 24
        },
        end: {
          line: 79,
          column: 3
        }
      },
      "20": {
        start: {
          line: 63,
          column: 4
        },
        end: {
          line: 63,
          column: 28
        }
      },
      "21": {
        start: {
          line: 63,
          column: 21
        },
        end: {
          line: 63,
          column: 28
        }
      },
      "22": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 78,
          column: 5
        }
      },
      "23": {
        start: {
          line: 66,
          column: 6
        },
        end: {
          line: 66,
          column: 27
        }
      },
      "24": {
        start: {
          line: 67,
          column: 37
        },
        end: {
          line: 67,
          column: 88
        }
      },
      "25": {
        start: {
          line: 69,
          column: 6
        },
        end: {
          line: 73,
          column: 7
        }
      },
      "26": {
        start: {
          line: 70,
          column: 8
        },
        end: {
          line: 70,
          column: 58
        }
      },
      "27": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 72,
          column: 45
        }
      },
      "28": {
        start: {
          line: 72,
          column: 25
        },
        end: {
          line: 72,
          column: 43
        }
      },
      "29": {
        start: {
          line: 75,
          column: 6
        },
        end: {
          line: 75,
          column: 56
        }
      },
      "30": {
        start: {
          line: 77,
          column: 6
        },
        end: {
          line: 77,
          column: 28
        }
      },
      "31": {
        start: {
          line: 81,
          column: 25
        },
        end: {
          line: 102,
          column: 3
        }
      },
      "32": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 101,
          column: 5
        }
      },
      "33": {
        start: {
          line: 83,
          column: 42
        },
        end: {
          line: 83,
          column: 84
        }
      },
      "34": {
        start: {
          line: 85,
          column: 6
        },
        end: {
          line: 98,
          column: 7
        }
      },
      "35": {
        start: {
          line: 86,
          column: 8
        },
        end: {
          line: 86,
          column: 36
        }
      },
      "36": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 97,
          column: 11
        }
      },
      "37": {
        start: {
          line: 89,
          column: 25
        },
        end: {
          line: 97,
          column: 9
        }
      },
      "38": {
        start: {
          line: 90,
          column: 10
        },
        end: {
          line: 96,
          column: 18
        }
      },
      "39": {
        start: {
          line: 100,
          column: 6
        },
        end: {
          line: 100,
          column: 50
        }
      },
      "40": {
        start: {
          line: 104,
          column: 22
        },
        end: {
          line: 119,
          column: 3
        }
      },
      "41": {
        start: {
          line: 105,
          column: 4
        },
        end: {
          line: 118,
          column: 5
        }
      },
      "42": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 107,
          column: 24
        }
      },
      "43": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 109,
          column: 23
        }
      },
      "44": {
        start: {
          line: 111,
          column: 8
        },
        end: {
          line: 111,
          column: 24
        }
      },
      "45": {
        start: {
          line: 113,
          column: 8
        },
        end: {
          line: 113,
          column: 26
        }
      },
      "46": {
        start: {
          line: 115,
          column: 8
        },
        end: {
          line: 115,
          column: 25
        }
      },
      "47": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 117,
          column: 28
        }
      },
      "48": {
        start: {
          line: 121,
          column: 27
        },
        end: {
          line: 136,
          column: 3
        }
      },
      "49": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 135,
          column: 5
        }
      },
      "50": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 124,
          column: 30
        }
      },
      "51": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 126,
          column: 29
        }
      },
      "52": {
        start: {
          line: 128,
          column: 8
        },
        end: {
          line: 128,
          column: 23
        }
      },
      "53": {
        start: {
          line: 130,
          column: 8
        },
        end: {
          line: 130,
          column: 23
        }
      },
      "54": {
        start: {
          line: 132,
          column: 8
        },
        end: {
          line: 132,
          column: 26
        }
      },
      "55": {
        start: {
          line: 134,
          column: 8
        },
        end: {
          line: 134,
          column: 22
        }
      },
      "56": {
        start: {
          line: 138,
          column: 24
        },
        end: {
          line: 155,
          column: 3
        }
      },
      "57": {
        start: {
          line: 139,
          column: 17
        },
        end: {
          line: 139,
          column: 37
        }
      },
      "58": {
        start: {
          line: 140,
          column: 16
        },
        end: {
          line: 140,
          column: 26
        }
      },
      "59": {
        start: {
          line: 141,
          column: 26
        },
        end: {
          line: 141,
          column: 77
        }
      },
      "60": {
        start: {
          line: 143,
          column: 4
        },
        end: {
          line: 154,
          column: 5
        }
      },
      "61": {
        start: {
          line: 144,
          column: 6
        },
        end: {
          line: 144,
          column: 24
        }
      },
      "62": {
        start: {
          line: 145,
          column: 11
        },
        end: {
          line: 154,
          column: 5
        }
      },
      "63": {
        start: {
          line: 146,
          column: 22
        },
        end: {
          line: 146,
          column: 52
        }
      },
      "64": {
        start: {
          line: 147,
          column: 6
        },
        end: {
          line: 147,
          column: 31
        }
      },
      "65": {
        start: {
          line: 148,
          column: 11
        },
        end: {
          line: 154,
          column: 5
        }
      },
      "66": {
        start: {
          line: 149,
          column: 20
        },
        end: {
          line: 149,
          column: 52
        }
      },
      "67": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 29
        }
      },
      "68": {
        start: {
          line: 152,
          column: 19
        },
        end: {
          line: 152,
          column: 52
        }
      },
      "69": {
        start: {
          line: 153,
          column: 6
        },
        end: {
          line: 153,
          column: 28
        }
      },
      "70": {
        start: {
          line: 157,
          column: 21
        },
        end: {
          line: 269,
          column: 3
        }
      },
      "71": {
        start: {
          line: 158,
          column: 4
        },
        end: {
          line: 268,
          column: 6
        }
      },
      "72": {
        start: {
          line: 163,
          column: 25
        },
        end: {
          line: 163,
          column: 60
        }
      },
      "73": {
        start: {
          line: 198,
          column: 25
        },
        end: {
          line: 198,
          column: 52
        }
      },
      "74": {
        start: {
          line: 236,
          column: 27
        },
        end: {
          line: 236,
          column: 50
        }
      },
      "75": {
        start: {
          line: 271,
          column: 33
        },
        end: {
          line: 305,
          column: 3
        }
      },
      "76": {
        start: {
          line: 272,
          column: 4
        },
        end: {
          line: 272,
          column: 40
        }
      },
      "77": {
        start: {
          line: 272,
          column: 28
        },
        end: {
          line: 272,
          column: 40
        }
      },
      "78": {
        start: {
          line: 274,
          column: 4
        },
        end: {
          line: 304,
          column: 6
        }
      },
      "79": {
        start: {
          line: 307,
          column: 2
        },
        end: {
          line: 314,
          column: 3
        }
      },
      "80": {
        start: {
          line: 308,
          column: 4
        },
        end: {
          line: 313,
          column: 6
        }
      },
      "81": {
        start: {
          line: 316,
          column: 2
        },
        end: {
          line: 364,
          column: 4
        }
      },
      "82": {
        start: {
          line: 321,
          column: 67
        },
        end: {
          line: 321,
          column: 81
        }
      },
      "83": {
        start: {
          line: 324,
          column: 68
        },
        end: {
          line: 324,
          column: 79
        }
      },
      "84": {
        start: {
          line: 325,
          column: 34
        },
        end: {
          line: 325,
          column: 103
        }
      },
      "85": {
        start: {
          line: 327,
          column: 10
        },
        end: {
          line: 329,
          column: 11
        }
      },
      "86": {
        start: {
          line: 328,
          column: 12
        },
        end: {
          line: 328,
          column: 28
        }
      },
      "87": {
        start: {
          line: 367,
          column: 15
        },
        end: {
          line: 589,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "SocialFeedScreen",
        decl: {
          start: {
            line: 28,
            column: 16
          },
          end: {
            line: 28,
            column: 32
          }
        },
        loc: {
          start: {
            line: 28,
            column: 99
          },
          end: {
            line: 365,
            column: 1
          }
        },
        line: 28
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 35,
            column: 12
          },
          end: {
            line: 35,
            column: 13
          }
        },
        loc: {
          start: {
            line: 35,
            column: 18
          },
          end: {
            line: 37,
            column: 3
          }
        },
        line: 35
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 20
          }
        },
        loc: {
          start: {
            line: 39,
            column: 46
          },
          end: {
            line: 60,
            column: 3
          }
        },
        line: 39
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 62,
            column: 24
          },
          end: {
            line: 62,
            column: 25
          }
        },
        loc: {
          start: {
            line: 62,
            column: 36
          },
          end: {
            line: 79,
            column: 3
          }
        },
        line: 62
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 72,
            column: 17
          },
          end: {
            line: 72,
            column: 18
          }
        },
        loc: {
          start: {
            line: 72,
            column: 25
          },
          end: {
            line: 72,
            column: 43
          }
        },
        line: 72
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 81,
            column: 25
          },
          end: {
            line: 81,
            column: 26
          }
        },
        loc: {
          start: {
            line: 81,
            column: 51
          },
          end: {
            line: 102,
            column: 3
          }
        },
        line: 81
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 89,
            column: 17
          },
          end: {
            line: 89,
            column: 18
          }
        },
        loc: {
          start: {
            line: 89,
            column: 25
          },
          end: {
            line: 97,
            column: 9
          }
        },
        line: 89
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 89,
            column: 34
          },
          end: {
            line: 89,
            column: 35
          }
        },
        loc: {
          start: {
            line: 90,
            column: 10
          },
          end: {
            line: 96,
            column: 18
          }
        },
        line: 90
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 104,
            column: 22
          },
          end: {
            line: 104,
            column: 23
          }
        },
        loc: {
          start: {
            line: 104,
            column: 44
          },
          end: {
            line: 119,
            column: 3
          }
        },
        line: 104
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 121,
            column: 27
          },
          end: {
            line: 121,
            column: 28
          }
        },
        loc: {
          start: {
            line: 121,
            column: 49
          },
          end: {
            line: 136,
            column: 3
          }
        },
        line: 121
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 138,
            column: 24
          },
          end: {
            line: 138,
            column: 25
          }
        },
        loc: {
          start: {
            line: 138,
            column: 48
          },
          end: {
            line: 155,
            column: 3
          }
        },
        line: 138
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 157,
            column: 21
          },
          end: {
            line: 157,
            column: 22
          }
        },
        loc: {
          start: {
            line: 157,
            column: 43
          },
          end: {
            line: 269,
            column: 3
          }
        },
        line: 157
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 163,
            column: 19
          },
          end: {
            line: 163,
            column: 20
          }
        },
        loc: {
          start: {
            line: 163,
            column: 25
          },
          end: {
            line: 163,
            column: 60
          }
        },
        line: 163
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 198,
            column: 19
          },
          end: {
            line: 198,
            column: 20
          }
        },
        loc: {
          start: {
            line: 198,
            column: 25
          },
          end: {
            line: 198,
            column: 52
          }
        },
        line: 198
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 236,
            column: 21
          },
          end: {
            line: 236,
            column: 22
          }
        },
        loc: {
          start: {
            line: 236,
            column: 27
          },
          end: {
            line: 236,
            column: 50
          }
        },
        line: 236
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 271,
            column: 33
          },
          end: {
            line: 271,
            column: 34
          }
        },
        loc: {
          start: {
            line: 271,
            column: 39
          },
          end: {
            line: 305,
            column: 3
          }
        },
        line: 271
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 321,
            column: 61
          },
          end: {
            line: 321,
            column: 62
          }
        },
        loc: {
          start: {
            line: 321,
            column: 67
          },
          end: {
            line: 321,
            column: 81
          }
        },
        line: 321
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 323,
            column: 18
          },
          end: {
            line: 323,
            column: 19
          }
        },
        loc: {
          start: {
            line: 323,
            column: 39
          },
          end: {
            line: 330,
            column: 9
          }
        },
        line: 323
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 39,
            column: 26
          },
          end: {
            line: 39,
            column: 41
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 39,
            column: 36
          },
          end: {
            line: 39,
            column: 41
          }
        }],
        line: 39
      },
      "1": {
        loc: {
          start: {
            line: 41,
            column: 6
          },
          end: {
            line: 45,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 6
          },
          end: {
            line: 45,
            column: 7
          }
        }, {
          start: {
            line: 43,
            column: 13
          },
          end: {
            line: 45,
            column: 7
          }
        }],
        line: 41
      },
      "2": {
        loc: {
          start: {
            line: 49,
            column: 6
          },
          end: {
            line: 53,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 49,
            column: 6
          },
          end: {
            line: 53,
            column: 7
          }
        }, {
          start: {
            line: 51,
            column: 13
          },
          end: {
            line: 53,
            column: 7
          }
        }],
        line: 49
      },
      "3": {
        loc: {
          start: {
            line: 63,
            column: 4
          },
          end: {
            line: 63,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 63,
            column: 4
          },
          end: {
            line: 63,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 63
      },
      "4": {
        loc: {
          start: {
            line: 69,
            column: 6
          },
          end: {
            line: 73,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 6
          },
          end: {
            line: 73,
            column: 7
          }
        }, {
          start: {
            line: 71,
            column: 13
          },
          end: {
            line: 73,
            column: 7
          }
        }],
        line: 69
      },
      "5": {
        loc: {
          start: {
            line: 85,
            column: 6
          },
          end: {
            line: 98,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 85,
            column: 6
          },
          end: {
            line: 98,
            column: 7
          }
        }, {
          start: {
            line: 87,
            column: 13
          },
          end: {
            line: 98,
            column: 7
          }
        }],
        line: 85
      },
      "6": {
        loc: {
          start: {
            line: 90,
            column: 10
          },
          end: {
            line: 96,
            column: 18
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 91,
            column: 14
          },
          end: {
            line: 95,
            column: 15
          }
        }, {
          start: {
            line: 96,
            column: 14
          },
          end: {
            line: 96,
            column: 18
          }
        }],
        line: 90
      },
      "7": {
        loc: {
          start: {
            line: 94,
            column: 49
          },
          end: {
            line: 94,
            column: 65
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 94,
            column: 59
          },
          end: {
            line: 94,
            column: 60
          }
        }, {
          start: {
            line: 94,
            column: 63
          },
          end: {
            line: 94,
            column: 65
          }
        }],
        line: 94
      },
      "8": {
        loc: {
          start: {
            line: 105,
            column: 4
          },
          end: {
            line: 118,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 106,
            column: 6
          },
          end: {
            line: 107,
            column: 24
          }
        }, {
          start: {
            line: 108,
            column: 6
          },
          end: {
            line: 109,
            column: 23
          }
        }, {
          start: {
            line: 110,
            column: 6
          },
          end: {
            line: 111,
            column: 24
          }
        }, {
          start: {
            line: 112,
            column: 6
          },
          end: {
            line: 113,
            column: 26
          }
        }, {
          start: {
            line: 114,
            column: 6
          },
          end: {
            line: 115,
            column: 25
          }
        }, {
          start: {
            line: 116,
            column: 6
          },
          end: {
            line: 117,
            column: 28
          }
        }],
        line: 105
      },
      "9": {
        loc: {
          start: {
            line: 122,
            column: 4
          },
          end: {
            line: 135,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 123,
            column: 6
          },
          end: {
            line: 124,
            column: 30
          }
        }, {
          start: {
            line: 125,
            column: 6
          },
          end: {
            line: 126,
            column: 29
          }
        }, {
          start: {
            line: 127,
            column: 6
          },
          end: {
            line: 128,
            column: 23
          }
        }, {
          start: {
            line: 129,
            column: 6
          },
          end: {
            line: 130,
            column: 23
          }
        }, {
          start: {
            line: 131,
            column: 6
          },
          end: {
            line: 132,
            column: 26
          }
        }, {
          start: {
            line: 133,
            column: 6
          },
          end: {
            line: 134,
            column: 22
          }
        }],
        line: 122
      },
      "10": {
        loc: {
          start: {
            line: 143,
            column: 4
          },
          end: {
            line: 154,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 143,
            column: 4
          },
          end: {
            line: 154,
            column: 5
          }
        }, {
          start: {
            line: 145,
            column: 11
          },
          end: {
            line: 154,
            column: 5
          }
        }],
        line: 143
      },
      "11": {
        loc: {
          start: {
            line: 145,
            column: 11
          },
          end: {
            line: 154,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 145,
            column: 11
          },
          end: {
            line: 154,
            column: 5
          }
        }, {
          start: {
            line: 148,
            column: 11
          },
          end: {
            line: 154,
            column: 5
          }
        }],
        line: 145
      },
      "12": {
        loc: {
          start: {
            line: 148,
            column: 11
          },
          end: {
            line: 154,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 148,
            column: 11
          },
          end: {
            line: 154,
            column: 5
          }
        }, {
          start: {
            line: 151,
            column: 11
          },
          end: {
            line: 154,
            column: 5
          }
        }],
        line: 148
      },
      "13": {
        loc: {
          start: {
            line: 171,
            column: 15
          },
          end: {
            line: 171,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 171,
            column: 15
          },
          end: {
            line: 171,
            column: 46
          }
        }, {
          start: {
            line: 171,
            column: 50
          },
          end: {
            line: 171,
            column: 65
          }
        }],
        line: 171
      },
      "14": {
        loc: {
          start: {
            line: 200,
            column: 11
          },
          end: {
            line: 202,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 200,
            column: 11
          },
          end: {
            line: 200,
            column: 21
          }
        }, {
          start: {
            line: 201,
            column: 12
          },
          end: {
            line: 201,
            column: 62
          }
        }],
        line: 200
      },
      "15": {
        loc: {
          start: {
            line: 204,
            column: 11
          },
          end: {
            line: 206,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 204,
            column: 11
          },
          end: {
            line: 204,
            column: 23
          }
        }, {
          start: {
            line: 205,
            column: 12
          },
          end: {
            line: 205,
            column: 63
          }
        }],
        line: 204
      },
      "16": {
        loc: {
          start: {
            line: 208,
            column: 11
          },
          end: {
            line: 213,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 208,
            column: 11
          },
          end: {
            line: 208,
            column: 29
          }
        }, {
          start: {
            line: 209,
            column: 12
          },
          end: {
            line: 212,
            column: 19
          }
        }],
        line: 208
      },
      "17": {
        loc: {
          start: {
            line: 216,
            column: 11
          },
          end: {
            line: 229,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 216,
            column: 11
          },
          end: {
            line: 216,
            column: 26
          }
        }, {
          start: {
            line: 216,
            column: 30
          },
          end: {
            line: 216,
            column: 56
          }
        }, {
          start: {
            line: 217,
            column: 12
          },
          end: {
            line: 228,
            column: 19
          }
        }],
        line: 216
      },
      "18": {
        loc: {
          start: {
            line: 220,
            column: 24
          },
          end: {
            line: 220,
            column: 76
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 220,
            column: 53
          },
          end: {
            line: 220,
            column: 66
          }
        }, {
          start: {
            line: 220,
            column: 69
          },
          end: {
            line: 220,
            column: 76
          }
        }],
        line: 220
      },
      "19": {
        loc: {
          start: {
            line: 225,
            column: 19
          },
          end: {
            line: 225,
            column: 65
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 225,
            column: 48
          },
          end: {
            line: 225,
            column: 55
          }
        }, {
          start: {
            line: 225,
            column: 58
          },
          end: {
            line: 225,
            column: 65
          }
        }],
        line: 225
      },
      "20": {
        loc: {
          start: {
            line: 235,
            column: 41
          },
          end: {
            line: 235,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 235,
            column: 41
          },
          end: {
            line: 235,
            column: 54
          }
        }, {
          start: {
            line: 235,
            column: 58
          },
          end: {
            line: 235,
            column: 76
          }
        }],
        line: 235
      },
      "21": {
        loc: {
          start: {
            line: 239,
            column: 20
          },
          end: {
            line: 239,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 239,
            column: 36
          },
          end: {
            line: 239,
            column: 43
          }
        }, {
          start: {
            line: 239,
            column: 46
          },
          end: {
            line: 239,
            column: 61
          }
        }],
        line: 239
      },
      "22": {
        loc: {
          start: {
            line: 241,
            column: 21
          },
          end: {
            line: 241,
            column: 58
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 241,
            column: 37
          },
          end: {
            line: 241,
            column: 46
          }
        }, {
          start: {
            line: 241,
            column: 49
          },
          end: {
            line: 241,
            column: 58
          }
        }],
        line: 241
      },
      "23": {
        loc: {
          start: {
            line: 245,
            column: 14
          },
          end: {
            line: 245,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 245,
            column: 14
          },
          end: {
            line: 245,
            column: 27
          }
        }, {
          start: {
            line: 245,
            column: 31
          },
          end: {
            line: 245,
            column: 47
          }
        }],
        line: 245
      },
      "24": {
        loc: {
          start: {
            line: 272,
            column: 4
          },
          end: {
            line: 272,
            column: 40
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 272,
            column: 4
          },
          end: {
            line: 272,
            column: 40
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 272
      },
      "25": {
        loc: {
          start: {
            line: 307,
            column: 2
          },
          end: {
            line: 314,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 307,
            column: 2
          },
          end: {
            line: 314,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 307
      },
      "26": {
        loc: {
          start: {
            line: 327,
            column: 10
          },
          end: {
            line: 329,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 327,
            column: 10
          },
          end: {
            line: 329,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 327
      },
      "27": {
        loc: {
          start: {
            line: 327,
            column: 14
          },
          end: {
            line: 327,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 327,
            column: 14
          },
          end: {
            line: 327,
            column: 29
          }
        }, {
          start: {
            line: 327,
            column: 33
          },
          end: {
            line: 327,
            column: 45
          }
        }],
        line: 327
      },
      "28": {
        loc: {
          start: {
            line: 338,
            column: 9
          },
          end: {
            line: 361,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 339,
            column: 10
          },
          end: {
            line: 348,
            column: 17
          }
        }, {
          start: {
            line: 350,
            column: 10
          },
          end: {
            line: 360,
            column: 13
          }
        }],
        line: 338
      },
      "29": {
        loc: {
          start: {
            line: 343,
            column: 15
          },
          end: {
            line: 345,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 344,
            column: 18
          },
          end: {
            line: 344,
            column: 87
          }
        }, {
          start: {
            line: 345,
            column: 18
          },
          end: {
            line: 345,
            column: 67
          }
        }],
        line: 343
      },
      "30": {
        loc: {
          start: {
            line: 354,
            column: 13
          },
          end: {
            line: 359,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 354,
            column: 13
          },
          end: {
            line: 354,
            column: 24
          }
        }, {
          start: {
            line: 355,
            column: 14
          },
          end: {
            line: 358,
            column: 21
          }
        }],
        line: 354
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0, 0, 0, 0],
      "9": [0, 0, 0, 0, 0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b44e34bb792e5b6876f5b8c356a1b8c003c13e46"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_9l5fvvg8o = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_9l5fvvg8o();
import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, RefreshControl, ActivityIndicator, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { socialService } from "../../services/social/SocialService";
import { useAuth } from "../../contexts/AuthContext";
import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
export function SocialFeedScreen(_ref) {
  var onNavigateToProfile = _ref.onNavigateToProfile,
    onNavigateToPost = _ref.onNavigateToPost;
  cov_9l5fvvg8o().f[0]++;
  var _ref2 = (cov_9l5fvvg8o().s[0]++, useAuth()),
    isAuthenticated = _ref2.isAuthenticated;
  var _ref3 = (cov_9l5fvvg8o().s[1]++, useState([])),
    _ref4 = _slicedToArray(_ref3, 2),
    posts = _ref4[0],
    setPosts = _ref4[1];
  var _ref5 = (cov_9l5fvvg8o().s[2]++, useState(true)),
    _ref6 = _slicedToArray(_ref5, 2),
    loading = _ref6[0],
    setLoading = _ref6[1];
  var _ref7 = (cov_9l5fvvg8o().s[3]++, useState(false)),
    _ref8 = _slicedToArray(_ref7, 2),
    refreshing = _ref8[0],
    setRefreshing = _ref8[1];
  var _ref9 = (cov_9l5fvvg8o().s[4]++, useState(false)),
    _ref0 = _slicedToArray(_ref9, 2),
    loadingMore = _ref0[0],
    setLoadingMore = _ref0[1];
  cov_9l5fvvg8o().s[5]++;
  useEffect(function () {
    cov_9l5fvvg8o().f[1]++;
    cov_9l5fvvg8o().s[6]++;
    loadFeed();
  }, []);
  cov_9l5fvvg8o().s[7]++;
  var loadFeed = function () {
    var _ref1 = _asyncToGenerator(function* () {
      var refresh = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_9l5fvvg8o().b[0][0]++, false);
      cov_9l5fvvg8o().f[2]++;
      cov_9l5fvvg8o().s[8]++;
      try {
        cov_9l5fvvg8o().s[9]++;
        if (refresh) {
          cov_9l5fvvg8o().b[1][0]++;
          cov_9l5fvvg8o().s[10]++;
          setRefreshing(true);
        } else {
          cov_9l5fvvg8o().b[1][1]++;
          cov_9l5fvvg8o().s[11]++;
          setLoading(true);
        }
        var _ref10 = (cov_9l5fvvg8o().s[12]++, yield socialService.getSocialFeed(20, 0)),
          data = _ref10.posts,
          error = _ref10.error;
        cov_9l5fvvg8o().s[13]++;
        if (error) {
          cov_9l5fvvg8o().b[2][0]++;
          cov_9l5fvvg8o().s[14]++;
          Alert.alert('Error', error);
        } else {
          cov_9l5fvvg8o().b[2][1]++;
          cov_9l5fvvg8o().s[15]++;
          setPosts(data);
        }
      } catch (error) {
        cov_9l5fvvg8o().s[16]++;
        Alert.alert('Error', 'Failed to load social feed');
      } finally {
        cov_9l5fvvg8o().s[17]++;
        setLoading(false);
        cov_9l5fvvg8o().s[18]++;
        setRefreshing(false);
      }
    });
    return function loadFeed() {
      return _ref1.apply(this, arguments);
    };
  }();
  cov_9l5fvvg8o().s[19]++;
  var loadMorePosts = function () {
    var _ref11 = _asyncToGenerator(function* () {
      cov_9l5fvvg8o().f[3]++;
      cov_9l5fvvg8o().s[20]++;
      if (loadingMore) {
        cov_9l5fvvg8o().b[3][0]++;
        cov_9l5fvvg8o().s[21]++;
        return;
      } else {
        cov_9l5fvvg8o().b[3][1]++;
      }
      cov_9l5fvvg8o().s[22]++;
      try {
        cov_9l5fvvg8o().s[23]++;
        setLoadingMore(true);
        var _ref12 = (cov_9l5fvvg8o().s[24]++, yield socialService.getSocialFeed(20, posts.length)),
          data = _ref12.posts,
          error = _ref12.error;
        cov_9l5fvvg8o().s[25]++;
        if (error) {
          cov_9l5fvvg8o().b[4][0]++;
          cov_9l5fvvg8o().s[26]++;
          console.error('Error loading more posts:', error);
        } else {
          cov_9l5fvvg8o().b[4][1]++;
          cov_9l5fvvg8o().s[27]++;
          setPosts(function (prev) {
            cov_9l5fvvg8o().f[4]++;
            cov_9l5fvvg8o().s[28]++;
            return [].concat(_toConsumableArray(prev), _toConsumableArray(data));
          });
        }
      } catch (error) {
        cov_9l5fvvg8o().s[29]++;
        console.error('Error loading more posts:', error);
      } finally {
        cov_9l5fvvg8o().s[30]++;
        setLoadingMore(false);
      }
    });
    return function loadMorePosts() {
      return _ref11.apply(this, arguments);
    };
  }();
  cov_9l5fvvg8o().s[31]++;
  var handleLikePost = function () {
    var _ref13 = _asyncToGenerator(function* (postId) {
      cov_9l5fvvg8o().f[5]++;
      cov_9l5fvvg8o().s[32]++;
      try {
        var _ref14 = (cov_9l5fvvg8o().s[33]++, yield socialService.togglePostLike(postId)),
          success = _ref14.success,
          isLiked = _ref14.isLiked,
          error = _ref14.error;
        cov_9l5fvvg8o().s[34]++;
        if (error) {
          cov_9l5fvvg8o().b[5][0]++;
          cov_9l5fvvg8o().s[35]++;
          Alert.alert('Error', error);
        } else {
          cov_9l5fvvg8o().b[5][1]++;
          cov_9l5fvvg8o().s[36]++;
          setPosts(function (prev) {
            cov_9l5fvvg8o().f[6]++;
            cov_9l5fvvg8o().s[37]++;
            return prev.map(function (post) {
              cov_9l5fvvg8o().f[7]++;
              cov_9l5fvvg8o().s[38]++;
              return post.id === postId ? (cov_9l5fvvg8o().b[6][0]++, Object.assign({}, post, {
                is_liked: isLiked,
                likes_count: post.likes_count + (isLiked ? (cov_9l5fvvg8o().b[7][0]++, 1) : (cov_9l5fvvg8o().b[7][1]++, -1))
              })) : (cov_9l5fvvg8o().b[6][1]++, post);
            });
          });
        }
      } catch (error) {
        cov_9l5fvvg8o().s[39]++;
        Alert.alert('Error', 'Failed to like post');
      }
    });
    return function handleLikePost(_x) {
      return _ref13.apply(this, arguments);
    };
  }();
  cov_9l5fvvg8o().s[40]++;
  var getPostIcon = function getPostIcon(postType) {
    cov_9l5fvvg8o().f[8]++;
    cov_9l5fvvg8o().s[41]++;
    switch (postType) {
      case 'match_result':
        cov_9l5fvvg8o().b[8][0]++;
        cov_9l5fvvg8o().s[42]++;
        return 'trophy';
      case 'achievement':
        cov_9l5fvvg8o().b[8][1]++;
        cov_9l5fvvg8o().s[43]++;
        return 'medal';
      case 'photo':
        cov_9l5fvvg8o().b[8][2]++;
        cov_9l5fvvg8o().s[44]++;
        return 'camera';
      case 'video':
        cov_9l5fvvg8o().b[8][3]++;
        cov_9l5fvvg8o().s[45]++;
        return 'videocam';
      case 'training_session':
        cov_9l5fvvg8o().b[8][4]++;
        cov_9l5fvvg8o().s[46]++;
        return 'fitness';
      default:
        cov_9l5fvvg8o().b[8][5]++;
        cov_9l5fvvg8o().s[47]++;
        return 'chatbubble';
    }
  };
  cov_9l5fvvg8o().s[48]++;
  var getPostTypeLabel = function getPostTypeLabel(postType) {
    cov_9l5fvvg8o().f[9]++;
    cov_9l5fvvg8o().s[49]++;
    switch (postType) {
      case 'match_result':
        cov_9l5fvvg8o().b[9][0]++;
        cov_9l5fvvg8o().s[50]++;
        return 'Match Result';
      case 'achievement':
        cov_9l5fvvg8o().b[9][1]++;
        cov_9l5fvvg8o().s[51]++;
        return 'Achievement';
      case 'photo':
        cov_9l5fvvg8o().b[9][2]++;
        cov_9l5fvvg8o().s[52]++;
        return 'Photo';
      case 'video':
        cov_9l5fvvg8o().b[9][3]++;
        cov_9l5fvvg8o().s[53]++;
        return 'Video';
      case 'training_session':
        cov_9l5fvvg8o().b[9][4]++;
        cov_9l5fvvg8o().s[54]++;
        return 'Training';
      default:
        cov_9l5fvvg8o().b[9][5]++;
        cov_9l5fvvg8o().s[55]++;
        return 'Post';
    }
  };
  cov_9l5fvvg8o().s[56]++;
  var formatTimeAgo = function formatTimeAgo(dateString) {
    cov_9l5fvvg8o().f[10]++;
    var date = (cov_9l5fvvg8o().s[57]++, new Date(dateString));
    var now = (cov_9l5fvvg8o().s[58]++, new Date());
    var diffInSeconds = (cov_9l5fvvg8o().s[59]++, Math.floor((now.getTime() - date.getTime()) / 1000));
    cov_9l5fvvg8o().s[60]++;
    if (diffInSeconds < 60) {
      cov_9l5fvvg8o().b[10][0]++;
      cov_9l5fvvg8o().s[61]++;
      return 'Just now';
    } else {
      cov_9l5fvvg8o().b[10][1]++;
      cov_9l5fvvg8o().s[62]++;
      if (diffInSeconds < 3600) {
        cov_9l5fvvg8o().b[11][0]++;
        var minutes = (cov_9l5fvvg8o().s[63]++, Math.floor(diffInSeconds / 60));
        cov_9l5fvvg8o().s[64]++;
        return `${minutes}m ago`;
      } else {
        cov_9l5fvvg8o().b[11][1]++;
        cov_9l5fvvg8o().s[65]++;
        if (diffInSeconds < 86400) {
          cov_9l5fvvg8o().b[12][0]++;
          var hours = (cov_9l5fvvg8o().s[66]++, Math.floor(diffInSeconds / 3600));
          cov_9l5fvvg8o().s[67]++;
          return `${hours}h ago`;
        } else {
          cov_9l5fvvg8o().b[12][1]++;
          var days = (cov_9l5fvvg8o().s[68]++, Math.floor(diffInSeconds / 86400));
          cov_9l5fvvg8o().s[69]++;
          return `${days}d ago`;
        }
      }
    }
  };
  cov_9l5fvvg8o().s[70]++;
  var renderPost = function renderPost(post) {
    var _post$user_profile;
    cov_9l5fvvg8o().f[11]++;
    cov_9l5fvvg8o().s[71]++;
    return _jsxs(View, {
      style: styles.postCard,
      children: [_jsxs(TouchableOpacity, {
        style: styles.postHeader,
        onPress: function onPress() {
          cov_9l5fvvg8o().f[12]++;
          cov_9l5fvvg8o().s[72]++;
          return onNavigateToProfile == null ? void 0 : onNavigateToProfile(post.user_id);
        },
        children: [_jsx(View, {
          style: styles.userAvatar,
          children: _jsx(Ionicons, {
            name: "person",
            size: 20,
            color: "#6B7280"
          })
        }), _jsxs(View, {
          style: styles.userInfo,
          children: [_jsx(Text, {
            style: styles.userName,
            children: (cov_9l5fvvg8o().b[13][0]++, (_post$user_profile = post.user_profile) == null ? void 0 : _post$user_profile.display_name) || (cov_9l5fvvg8o().b[13][1]++, 'Tennis Player')
          }), _jsxs(View, {
            style: styles.postMeta,
            children: [_jsxs(View, {
              style: styles.postType,
              children: [_jsx(Ionicons, {
                name: getPostIcon(post.post_type),
                size: 12,
                color: "#6B7280"
              }), _jsx(Text, {
                style: styles.postTypeText,
                children: getPostTypeLabel(post.post_type)
              })]
            }), _jsx(Text, {
              style: styles.postTime,
              children: formatTimeAgo(post.created_at)
            })]
          })]
        }), _jsx(TouchableOpacity, {
          style: styles.postMenu,
          children: _jsx(Ionicons, {
            name: "ellipsis-horizontal",
            size: 20,
            color: "#6B7280"
          })
        })]
      }), _jsxs(TouchableOpacity, {
        style: styles.postContent,
        onPress: function onPress() {
          cov_9l5fvvg8o().f[13]++;
          cov_9l5fvvg8o().s[73]++;
          return onNavigateToPost == null ? void 0 : onNavigateToPost(post.id);
        },
        children: [(cov_9l5fvvg8o().b[14][0]++, post.title) && (cov_9l5fvvg8o().b[14][1]++, _jsx(Text, {
          style: styles.postTitle,
          children: post.title
        })), (cov_9l5fvvg8o().b[15][0]++, post.content) && (cov_9l5fvvg8o().b[15][1]++, _jsx(Text, {
          style: styles.postText,
          children: post.content
        })), (cov_9l5fvvg8o().b[16][0]++, post.location_name) && (cov_9l5fvvg8o().b[16][1]++, _jsxs(View, {
          style: styles.locationContainer,
          children: [_jsx(Ionicons, {
            name: "location",
            size: 14,
            color: "#6B7280"
          }), _jsx(Text, {
            style: styles.locationText,
            children: post.location_name
          })]
        })), (cov_9l5fvvg8o().b[17][0]++, post.media_urls) && (cov_9l5fvvg8o().b[17][1]++, post.media_urls.length > 0) && (cov_9l5fvvg8o().b[17][2]++, _jsx(View, {
          style: styles.mediaContainer,
          children: _jsxs(View, {
            style: styles.mediaPlaceholder,
            children: [_jsx(Ionicons, {
              name: post.post_type === 'video' ? (cov_9l5fvvg8o().b[18][0]++, 'play-circle') : (cov_9l5fvvg8o().b[18][1]++, 'image'),
              size: 32,
              color: "#6B7280"
            }), _jsx(Text, {
              style: styles.mediaText,
              children: post.post_type === 'video' ? (cov_9l5fvvg8o().b[19][0]++, 'Video') : (cov_9l5fvvg8o().b[19][1]++, 'Photo')
            })]
          })
        }))]
      }), _jsxs(View, {
        style: styles.postActions,
        children: [_jsxs(TouchableOpacity, {
          style: [styles.actionButton, (cov_9l5fvvg8o().b[20][0]++, post.is_liked) && (cov_9l5fvvg8o().b[20][1]++, styles.likedButton)],
          onPress: function onPress() {
            cov_9l5fvvg8o().f[14]++;
            cov_9l5fvvg8o().s[74]++;
            return handleLikePost(post.id);
          },
          children: [_jsx(Ionicons, {
            name: post.is_liked ? (cov_9l5fvvg8o().b[21][0]++, 'heart') : (cov_9l5fvvg8o().b[21][1]++, 'heart-outline'),
            size: 20,
            color: post.is_liked ? (cov_9l5fvvg8o().b[22][0]++, '#EF4444') : (cov_9l5fvvg8o().b[22][1]++, '#6B7280')
          }), _jsx(Text, {
            style: [styles.actionText, (cov_9l5fvvg8o().b[23][0]++, post.is_liked) && (cov_9l5fvvg8o().b[23][1]++, styles.likedText)],
            children: post.likes_count
          })]
        }), _jsxs(TouchableOpacity, {
          style: styles.actionButton,
          children: [_jsx(Ionicons, {
            name: "chatbubble-outline",
            size: 20,
            color: "#6B7280"
          }), _jsx(Text, {
            style: styles.actionText,
            children: post.comments_count
          })]
        }), _jsxs(TouchableOpacity, {
          style: styles.actionButton,
          children: [_jsx(Ionicons, {
            name: "share-outline",
            size: 20,
            color: "#6B7280"
          }), _jsx(Text, {
            style: styles.actionText,
            children: post.shares_count
          })]
        }), _jsx(View, {
          style: styles.actionSpacer
        }), _jsx(TouchableOpacity, {
          style: styles.actionButton,
          children: _jsx(Ionicons, {
            name: "bookmark-outline",
            size: 20,
            color: "#6B7280"
          })
        })]
      })]
    }, post.id);
  };
  cov_9l5fvvg8o().s[75]++;
  var renderCreatePostPrompt = function renderCreatePostPrompt() {
    cov_9l5fvvg8o().f[15]++;
    cov_9l5fvvg8o().s[76]++;
    if (!isAuthenticated()) {
      cov_9l5fvvg8o().b[24][0]++;
      cov_9l5fvvg8o().s[77]++;
      return null;
    } else {
      cov_9l5fvvg8o().b[24][1]++;
    }
    cov_9l5fvvg8o().s[78]++;
    return _jsxs(View, {
      style: styles.createPostCard,
      children: [_jsxs(View, {
        style: styles.createPostHeader,
        children: [_jsx(View, {
          style: styles.userAvatar,
          children: _jsx(Ionicons, {
            name: "person",
            size: 20,
            color: "#6B7280"
          })
        }), _jsx(TouchableOpacity, {
          style: styles.createPostInput,
          children: _jsx(Text, {
            style: styles.createPostPlaceholder,
            children: "Share your tennis journey..."
          })
        })]
      }), _jsxs(View, {
        style: styles.createPostActions,
        children: [_jsxs(TouchableOpacity, {
          style: styles.createPostAction,
          children: [_jsx(Ionicons, {
            name: "camera",
            size: 20,
            color: "#6B7280"
          }), _jsx(Text, {
            style: styles.createPostActionText,
            children: "Photo"
          })]
        }), _jsxs(TouchableOpacity, {
          style: styles.createPostAction,
          children: [_jsx(Ionicons, {
            name: "videocam",
            size: 20,
            color: "#6B7280"
          }), _jsx(Text, {
            style: styles.createPostActionText,
            children: "Video"
          })]
        }), _jsxs(TouchableOpacity, {
          style: styles.createPostAction,
          children: [_jsx(Ionicons, {
            name: "trophy",
            size: 20,
            color: "#6B7280"
          }), _jsx(Text, {
            style: styles.createPostActionText,
            children: "Match"
          })]
        })]
      })]
    });
  };
  cov_9l5fvvg8o().s[79]++;
  if (loading) {
    cov_9l5fvvg8o().b[25][0]++;
    cov_9l5fvvg8o().s[80]++;
    return _jsxs(View, {
      style: styles.loadingContainer,
      children: [_jsx(ActivityIndicator, {
        size: "large",
        color: "#3B82F6"
      }), _jsx(Text, {
        style: styles.loadingText,
        children: "Loading social feed..."
      })]
    });
  } else {
    cov_9l5fvvg8o().b[25][1]++;
  }
  cov_9l5fvvg8o().s[81]++;
  return _jsx(View, {
    style: styles.container,
    children: _jsxs(ScrollView, {
      style: styles.feed,
      refreshControl: _jsx(RefreshControl, {
        refreshing: refreshing,
        onRefresh: function onRefresh() {
          cov_9l5fvvg8o().f[16]++;
          cov_9l5fvvg8o().s[82]++;
          return loadFeed(true);
        }
      }),
      onScroll: function onScroll(_ref15) {
        var nativeEvent = _ref15.nativeEvent;
        cov_9l5fvvg8o().f[17]++;
        var _ref16 = (cov_9l5fvvg8o().s[83]++, nativeEvent),
          layoutMeasurement = _ref16.layoutMeasurement,
          contentOffset = _ref16.contentOffset,
          contentSize = _ref16.contentSize;
        var isCloseToBottom = (cov_9l5fvvg8o().s[84]++, layoutMeasurement.height + contentOffset.y >= contentSize.height - 20);
        cov_9l5fvvg8o().s[85]++;
        if ((cov_9l5fvvg8o().b[27][0]++, isCloseToBottom) && (cov_9l5fvvg8o().b[27][1]++, !loadingMore)) {
          cov_9l5fvvg8o().b[26][0]++;
          cov_9l5fvvg8o().s[86]++;
          loadMorePosts();
        } else {
          cov_9l5fvvg8o().b[26][1]++;
        }
      },
      scrollEventThrottle: 400,
      showsVerticalScrollIndicator: false,
      children: [renderCreatePostPrompt(), posts.length === 0 ? (cov_9l5fvvg8o().b[28][0]++, _jsxs(View, {
        style: styles.emptyState,
        children: [_jsx(Ionicons, {
          name: "chatbubbles-outline",
          size: 64,
          color: "#9CA3AF"
        }), _jsx(Text, {
          style: styles.emptyTitle,
          children: "No Posts Yet"
        }), _jsx(Text, {
          style: styles.emptyText,
          children: isAuthenticated() ? (cov_9l5fvvg8o().b[29][0]++, "Follow other players or create your first post to see content here!") : (cov_9l5fvvg8o().b[29][1]++, "Sign in to see posts from the tennis community!")
        })]
      })) : (cov_9l5fvvg8o().b[28][1]++, _jsxs(_Fragment, {
        children: [posts.map(renderPost), (cov_9l5fvvg8o().b[30][0]++, loadingMore) && (cov_9l5fvvg8o().b[30][1]++, _jsxs(View, {
          style: styles.loadingMoreContainer,
          children: [_jsx(ActivityIndicator, {
            size: "small",
            color: "#3B82F6"
          }), _jsx(Text, {
            style: styles.loadingMoreText,
            children: "Loading more posts..."
          })]
        }))]
      }))]
    })
  });
}
var styles = (cov_9l5fvvg8o().s[87]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB'
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280'
  },
  feed: {
    flex: 1
  },
  createPostCard: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  createPostHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12
  },
  createPostInput: {
    flex: 1,
    marginLeft: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 20
  },
  createPostPlaceholder: {
    fontSize: 16,
    color: '#9CA3AF'
  },
  createPostActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6'
  },
  createPostAction: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    gap: 8
  },
  createPostActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280'
  },
  postCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 12
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center'
  },
  userInfo: {
    flex: 1,
    marginLeft: 12
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 2
  },
  postMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  postType: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4
  },
  postTypeText: {
    fontSize: 12,
    color: '#6B7280'
  },
  postTime: {
    fontSize: 12,
    color: '#9CA3AF'
  },
  postMenu: {
    padding: 8
  },
  postContent: {
    paddingHorizontal: 16,
    paddingBottom: 12
  },
  postTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8
  },
  postText: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    marginBottom: 8
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 4
  },
  locationText: {
    fontSize: 14,
    color: '#6B7280'
  },
  mediaContainer: {
    marginTop: 8
  },
  mediaPlaceholder: {
    height: 200,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center'
  },
  mediaText: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 8
  },
  postActions: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6'
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    gap: 6
  },
  likedButton: {
    backgroundColor: '#FEF2F2'
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280'
  },
  likedText: {
    color: '#EF4444'
  },
  actionSpacer: {
    flex: 1
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 64,
    paddingHorizontal: 24
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24
  },
  loadingMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    gap: 8
  },
  loadingMoreText: {
    fontSize: 14,
    color: '#6B7280'
  }
}));
export default SocialFeedScreen;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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