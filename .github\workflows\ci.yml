name: 🔄 Continuous Integration

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  EXPO_CLI_VERSION: 'latest'

jobs:
  # Code Quality Checks
  code-quality:
    name: 🔍 Code Quality
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔍 Run ESLint
        run: npm run lint
        continue-on-error: false

      - name: 🎨 Check Prettier formatting
        run: npm run format:check
        continue-on-error: false

      - name: 📝 TypeScript type check
        run: npm run type-check
        continue-on-error: false

      - name: 📊 Upload ESLint results
        if: always()
        uses: github/super-linter@v4
        env:
          DEFAULT_BRANCH: main
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VALIDATE_TYPESCRIPT_ES: true
          VALIDATE_JAVASCRIPT_ES: true

  # Frontend Tests
  frontend-tests:
    name: 🧪 Frontend Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: code-quality
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🧪 Run unit tests
        run: npm run test:ci
        env:
          CI: true

      - name: 📊 Upload test coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: frontend
          name: frontend-coverage
          fail_ci_if_error: false

      - name: 📈 SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # Backend Tests
  backend-tests:
    name: 🔧 Backend Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: code-quality
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: 📦 Install backend dependencies
        working-directory: ./backend
        run: npm ci

      - name: 🔨 Build backend
        working-directory: ./backend
        run: npm run build

      - name: 🧪 Run backend tests
        working-directory: ./backend
        run: npm run test:ci
        env:
          NODE_ENV: test
          JWT_SECRET: test-jwt-secret
          ENCRYPTION_KEY: test-encryption-key
          REDIS_URL: redis://localhost:6379

      - name: 📊 Upload backend coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage/lcov.info
          flags: backend
          name: backend-coverage
          fail_ci_if_error: false

  # Security Scanning
  security-scan:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: code-quality
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔍 Run npm audit
        run: npm audit --audit-level=moderate
        continue-on-error: true

      - name: 🛡️ Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high
        continue-on-error: true

      - name: 🔐 CodeQL Analysis
        uses: github/codeql-action/init@v2
        with:
          languages: javascript

      - name: 🔍 Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2

  # Build and Test Mobile App
  mobile-build:
    name: 📱 Mobile Build
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [frontend-tests, backend-tests]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔧 Setup Expo CLI
        run: npm install -g @expo/cli@${{ env.EXPO_CLI_VERSION }}

      - name: 📱 Expo prebuild
        run: npx expo prebuild --platform all --clear
        env:
          EXPO_NO_TELEMETRY: 1

      - name: 🌐 Build web version
        run: npm run build:web
        env:
          EXPO_NO_TELEMETRY: 1

      - name: 📊 Bundle analyzer
        run: npx expo export --platform web --output-dir dist-web
        env:
          EXPO_NO_TELEMETRY: 1

      - name: 📤 Upload web build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: web-build
          path: dist-web/
          retention-days: 7

  # Performance Testing
  performance-tests:
    name: ⚡ Performance Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: mobile-build
    if: github.event_name == 'pull_request'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🌐 Build for performance testing
        run: npm run build:web:prod

      - name: 🚀 Start test server
        run: |
          npm install -g serve
          serve -s dist -l 3000 &
          sleep 10

      - name: ⚡ Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v9
        with:
          configPath: './.lighthouserc.json'
          uploadArtifacts: true
          temporaryPublicStorage: true

  # Integration Tests
  integration-tests:
    name: 🔗 Integration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 25
    needs: [frontend-tests, backend-tests]
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 📦 Install backend dependencies
        working-directory: ./backend
        run: npm ci

      - name: 🔨 Build backend
        working-directory: ./backend
        run: npm run build

      - name: 🚀 Start backend server
        working-directory: ./backend
        run: npm start &
        env:
          NODE_ENV: test
          PORT: 3001
          JWT_SECRET: test-jwt-secret
          REDIS_URL: redis://localhost:6379

      - name: ⏳ Wait for backend
        run: |
          timeout 60 bash -c 'until curl -f http://localhost:3001/health; do sleep 2; done'

      - name: 🧪 Run integration tests
        run: npm run test:integration
        env:
          API_BASE_URL: http://localhost:3001

      - name: 📊 Upload integration test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: integration-test-results
          path: test-results/
          retention-days: 7

  # Deployment Readiness Check
  deployment-check:
    name: 🚀 Deployment Check
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: [security-scan, performance-tests, integration-tests]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔍 Validate deployment configuration
        run: |
          echo "🔍 Checking deployment readiness..."
          
          # Check if required files exist
          if [ ! -f "app.json" ]; then
            echo "❌ app.json not found"
            exit 1
          fi
          
          if [ ! -f "eas.json" ]; then
            echo "❌ eas.json not found"
            exit 1
          fi
          
          if [ ! -f "backend/Dockerfile" ]; then
            echo "❌ Backend Dockerfile not found"
            exit 1
          fi
          
          echo "✅ All deployment files present"

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: ✅ Validate configuration
        run: npm run validate:config

      - name: 🎯 Deployment readiness summary
        run: |
          echo "## 🚀 Deployment Readiness Report" >> $GITHUB_STEP_SUMMARY
          echo "| Component | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Code Quality | ✅ Passed |" >> $GITHUB_STEP_SUMMARY
          echo "| Frontend Tests | ✅ Passed |" >> $GITHUB_STEP_SUMMARY
          echo "| Backend Tests | ✅ Passed |" >> $GITHUB_STEP_SUMMARY
          echo "| Security Scan | ✅ Passed |" >> $GITHUB_STEP_SUMMARY
          echo "| Performance | ✅ Passed |" >> $GITHUB_STEP_SUMMARY
          echo "| Integration | ✅ Passed |" >> $GITHUB_STEP_SUMMARY
          echo "| Configuration | ✅ Valid |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "🎉 **Ready for deployment!**" >> $GITHUB_STEP_SUMMARY
