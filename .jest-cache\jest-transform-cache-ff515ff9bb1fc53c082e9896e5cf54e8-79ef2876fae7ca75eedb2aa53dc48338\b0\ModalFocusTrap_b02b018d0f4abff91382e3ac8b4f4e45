130468d3cd3791c8438e31927309af88
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _View = _interopRequireDefault(require("../View"));
var _createElement = _interopRequireDefault(require("../createElement"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _UIManager = _interopRequireDefault(require("../UIManager"));
var _canUseDom = _interopRequireDefault(require("../../modules/canUseDom"));
var FocusBracket = function FocusBracket() {
  return (0, _createElement.default)('div', {
    role: 'none',
    tabIndex: 0,
    style: styles.focusBracket
  });
};
function attemptFocus(element) {
  if (!_canUseDom.default) {
    return false;
  }
  try {
    element.focus();
  } catch (e) {}
  return document.activeElement === element;
}
function focusFirstDescendant(element) {
  for (var i = 0; i < element.childNodes.length; i++) {
    var child = element.childNodes[i];
    if (attemptFocus(child) || focusFirstDescendant(child)) {
      return true;
    }
  }
  return false;
}
function focusLastDescendant(element) {
  for (var i = element.childNodes.length - 1; i >= 0; i--) {
    var child = element.childNodes[i];
    if (attemptFocus(child) || focusLastDescendant(child)) {
      return true;
    }
  }
  return false;
}
var ModalFocusTrap = function ModalFocusTrap(_ref) {
  var active = _ref.active,
    children = _ref.children;
  var trapElementRef = React.useRef();
  var focusRef = React.useRef({
    trapFocusInProgress: false,
    lastFocusedElement: null
  });
  React.useEffect(function () {
    if (_canUseDom.default) {
      var trapFocus = function trapFocus() {
        if (trapElementRef.current == null || focusRef.current.trapFocusInProgress || !active) {
          return;
        }
        try {
          focusRef.current.trapFocusInProgress = true;
          if (document.activeElement instanceof Node && !trapElementRef.current.contains(document.activeElement)) {
            var hasFocused = focusFirstDescendant(trapElementRef.current);
            if (focusRef.current.lastFocusedElement === document.activeElement) {
              hasFocused = focusLastDescendant(trapElementRef.current);
            }
            if (!hasFocused && trapElementRef.current != null && document.activeElement) {
              _UIManager.default.focus(trapElementRef.current);
            }
          }
        } finally {
          focusRef.current.trapFocusInProgress = false;
        }
        focusRef.current.lastFocusedElement = document.activeElement;
      };
      trapFocus();
      document.addEventListener('focus', trapFocus, true);
      return function () {
        return document.removeEventListener('focus', trapFocus, true);
      };
    }
  }, [active]);
  React.useEffect(function () {
    if (_canUseDom.default) {
      var lastFocusedElementOutsideTrap = document.activeElement;
      return function () {
        if (lastFocusedElementOutsideTrap && document.contains(lastFocusedElementOutsideTrap)) {
          _UIManager.default.focus(lastFocusedElementOutsideTrap);
        }
      };
    }
  }, []);
  return React.createElement(React.Fragment, null, React.createElement(FocusBracket, null), React.createElement(_View.default, {
    ref: trapElementRef
  }, children), React.createElement(FocusBracket, null));
};
var _default = exports.default = ModalFocusTrap;
var styles = _StyleSheet.default.create({
  focusBracket: {
    outlineStyle: 'none'
  }
});
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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