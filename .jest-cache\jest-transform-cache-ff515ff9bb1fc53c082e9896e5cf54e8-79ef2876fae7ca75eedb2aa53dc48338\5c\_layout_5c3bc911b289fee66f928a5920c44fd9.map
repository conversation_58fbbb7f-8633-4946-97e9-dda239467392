{"version": 3, "names": ["Tabs", "Chrome", "Home", "Play", "MessageCircle", "TrendingUp", "User", "Gamepad2", "CreditCard", "Users", "StyleSheet", "jsx", "_jsx", "jsxs", "_jsxs", "colors", "cov_kidrxquod", "s", "primary", "yellow", "white", "dark", "gray", "lightGray", "TabLayout", "f", "screenOptions", "headerShown", "tabBarStyle", "styles", "tabBar", "tabBarActiveTintColor", "tabBarInactiveTintColor", "tabBarLabelStyle", "tabBarLabel", "children", "Screen", "name", "options", "title", "tabBarIcon", "_ref", "size", "color", "strokeWidth", "_ref2", "_ref3", "_ref4", "_ref5", "_ref6", "_ref7", "_ref8", "create", "backgroundColor", "borderTopWidth", "borderTopColor", "paddingTop", "paddingBottom", "height", "fontSize", "fontFamily", "marginTop"], "sources": ["_layout.tsx"], "sourcesContent": ["import { Tabs } from 'expo-router';\nimport { Chrome as Home, Play, MessageCircle, TrendingUp, User, Gamepad2, CreditCard, Users } from 'lucide-react-native';\nimport { View, StyleSheet } from 'react-native';\n\nconst colors = {\n  primary: '#23ba16',\n  yellow: '#ffe600',\n  white: '#ffffff',\n  dark: '#171717',\n  gray: '#6b7280',\n  lightGray: '#f3f4f6',\n};\n\nexport default function TabLayout() {\n  return (\n    <Tabs\n      screenOptions={{\n        headerShown: false,\n        tabBarStyle: styles.tabBar,\n        tabBarActiveTintColor: colors.primary,\n        tabBarInactiveTintColor: colors.gray,\n        tabBarLabelStyle: styles.tabBarLabel,\n      }}>\n      <Tabs.Screen\n        name=\"index\"\n        options={{\n          title: 'Home',\n          tabBarIcon: ({ size, color }) => (\n            <Home size={size} color={color} strokeWidth={2} />\n          ),\n        }}\n      />\n      <Tabs.Screen\n        name=\"training\"\n        options={{\n          title: 'Training',\n          tabBarIcon: ({ size, color }) => (\n            <Play size={size} color={color} strokeWidth={2} />\n          ),\n        }}\n      />\n      <Tabs.Screen\n        name=\"simulation\"\n        options={{\n          title: 'Match Sim',\n          tabBarIcon: ({ size, color }) => (\n            <Gamepad2 size={size} color={color} strokeWidth={2} />\n          ),\n        }}\n      />\n      <Tabs.Screen\n        name=\"coach\"\n        options={{\n          title: 'Coach',\n          tabBarIcon: ({ size, color }) => (\n            <MessageCircle size={size} color={color} strokeWidth={2} />\n          ),\n        }}\n      />\n      <Tabs.Screen\n        name=\"social\"\n        options={{\n          title: 'Social',\n          tabBarIcon: ({ size, color }) => (\n            <Users size={size} color={color} strokeWidth={2} />\n          ),\n        }}\n      />\n      <Tabs.Screen\n        name=\"progress\"\n        options={{\n          title: 'Progress',\n          tabBarIcon: ({ size, color }) => (\n            <TrendingUp size={size} color={color} strokeWidth={2} />\n          ),\n        }}\n      />\n      <Tabs.Screen\n        name=\"subscription\"\n        options={{\n          title: 'Premium',\n          tabBarIcon: ({ size, color }) => (\n            <CreditCard size={size} color={color} strokeWidth={2} />\n          ),\n        }}\n      />\n      <Tabs.Screen\n        name=\"profile\"\n        options={{\n          title: 'Profile',\n          tabBarIcon: ({ size, color }) => (\n            <User size={size} color={color} strokeWidth={2} />\n          ),\n        }}\n      />\n    </Tabs>\n  );\n}\n\nconst styles = StyleSheet.create({\n  tabBar: {\n    backgroundColor: colors.white,\n    borderTopWidth: 1,\n    borderTopColor: colors.lightGray,\n    paddingTop: 8,\n    paddingBottom: 8,\n    height: 70,\n  },\n  tabBarLabel: {\n    fontSize: 12,\n    fontFamily: 'Inter-Medium',\n    marginTop: 4,\n  },\n});"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,IAAI,QAAQ,aAAa;AAClC,SAASC,MAAM,IAAIC,IAAI,EAAEC,IAAI,EAAEC,aAAa,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,QAAQ,qBAAqB;AACxH,SAAeC,UAAU,QAAQ,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEhD,IAAMC,MAAM,IAAAC,aAAA,GAAAC,CAAA,OAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE;AACb,CAAC;AAED,eAAe,SAASC,SAASA,CAAA,EAAG;EAAAR,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAC,CAAA;EAClC,OACEH,KAAA,CAACd,IAAI;IACH0B,aAAa,EAAE;MACbC,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAEC,MAAM,CAACC,MAAM;MAC1BC,qBAAqB,EAAEhB,MAAM,CAACG,OAAO;MACrCc,uBAAuB,EAAEjB,MAAM,CAACO,IAAI;MACpCW,gBAAgB,EAAEJ,MAAM,CAACK;IAC3B,CAAE;IAAAC,QAAA,GACFvB,IAAA,CAACZ,IAAI,CAACoC,MAAM;MACVC,IAAI,EAAC,OAAO;MACZC,OAAO,EAAE;QACPC,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE,SAAZA,UAAUA,CAAAC,IAAA,EACR;UAAA,IADaC,IAAI,GAAAD,IAAA,CAAJC,IAAI;YAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;UAAA3B,aAAA,GAAAS,CAAA;UAAAT,aAAA,GAAAC,CAAA;UACxB,OAAAL,IAAA,CAACV,IAAI;YAACwC,IAAI,EAAEA,IAAK;YAACC,KAAK,EAAEA,KAAM;YAACC,WAAW,EAAE;UAAE,CAAE,CAAC;QAAD;MAErD;IAAE,CACH,CAAC,EACFhC,IAAA,CAACZ,IAAI,CAACoC,MAAM;MACVC,IAAI,EAAC,UAAU;MACfC,OAAO,EAAE;QACPC,KAAK,EAAE,UAAU;QACjBC,UAAU,EAAE,SAAZA,UAAUA,CAAAK,KAAA,EACR;UAAA,IADaH,IAAI,GAAAG,KAAA,CAAJH,IAAI;YAAEC,KAAK,GAAAE,KAAA,CAALF,KAAK;UAAA3B,aAAA,GAAAS,CAAA;UAAAT,aAAA,GAAAC,CAAA;UACxB,OAAAL,IAAA,CAACT,IAAI;YAACuC,IAAI,EAAEA,IAAK;YAACC,KAAK,EAAEA,KAAM;YAACC,WAAW,EAAE;UAAE,CAAE,CAAC;QAAD;MAErD;IAAE,CACH,CAAC,EACFhC,IAAA,CAACZ,IAAI,CAACoC,MAAM;MACVC,IAAI,EAAC,YAAY;MACjBC,OAAO,EAAE;QACPC,KAAK,EAAE,WAAW;QAClBC,UAAU,EAAE,SAAZA,UAAUA,CAAAM,KAAA,EACR;UAAA,IADaJ,IAAI,GAAAI,KAAA,CAAJJ,IAAI;YAAEC,KAAK,GAAAG,KAAA,CAALH,KAAK;UAAA3B,aAAA,GAAAS,CAAA;UAAAT,aAAA,GAAAC,CAAA;UACxB,OAAAL,IAAA,CAACL,QAAQ;YAACmC,IAAI,EAAEA,IAAK;YAACC,KAAK,EAAEA,KAAM;YAACC,WAAW,EAAE;UAAE,CAAE,CAAC;QAAD;MAEzD;IAAE,CACH,CAAC,EACFhC,IAAA,CAACZ,IAAI,CAACoC,MAAM;MACVC,IAAI,EAAC,OAAO;MACZC,OAAO,EAAE;QACPC,KAAK,EAAE,OAAO;QACdC,UAAU,EAAE,SAAZA,UAAUA,CAAAO,KAAA,EACR;UAAA,IADaL,IAAI,GAAAK,KAAA,CAAJL,IAAI;YAAEC,KAAK,GAAAI,KAAA,CAALJ,KAAK;UAAA3B,aAAA,GAAAS,CAAA;UAAAT,aAAA,GAAAC,CAAA;UACxB,OAAAL,IAAA,CAACR,aAAa;YAACsC,IAAI,EAAEA,IAAK;YAACC,KAAK,EAAEA,KAAM;YAACC,WAAW,EAAE;UAAE,CAAE,CAAC;QAAD;MAE9D;IAAE,CACH,CAAC,EACFhC,IAAA,CAACZ,IAAI,CAACoC,MAAM;MACVC,IAAI,EAAC,QAAQ;MACbC,OAAO,EAAE;QACPC,KAAK,EAAE,QAAQ;QACfC,UAAU,EAAE,SAAZA,UAAUA,CAAAQ,KAAA,EACR;UAAA,IADaN,IAAI,GAAAM,KAAA,CAAJN,IAAI;YAAEC,KAAK,GAAAK,KAAA,CAALL,KAAK;UAAA3B,aAAA,GAAAS,CAAA;UAAAT,aAAA,GAAAC,CAAA;UACxB,OAAAL,IAAA,CAACH,KAAK;YAACiC,IAAI,EAAEA,IAAK;YAACC,KAAK,EAAEA,KAAM;YAACC,WAAW,EAAE;UAAE,CAAE,CAAC;QAAD;MAEtD;IAAE,CACH,CAAC,EACFhC,IAAA,CAACZ,IAAI,CAACoC,MAAM;MACVC,IAAI,EAAC,UAAU;MACfC,OAAO,EAAE;QACPC,KAAK,EAAE,UAAU;QACjBC,UAAU,EAAE,SAAZA,UAAUA,CAAAS,KAAA,EACR;UAAA,IADaP,IAAI,GAAAO,KAAA,CAAJP,IAAI;YAAEC,KAAK,GAAAM,KAAA,CAALN,KAAK;UAAA3B,aAAA,GAAAS,CAAA;UAAAT,aAAA,GAAAC,CAAA;UACxB,OAAAL,IAAA,CAACP,UAAU;YAACqC,IAAI,EAAEA,IAAK;YAACC,KAAK,EAAEA,KAAM;YAACC,WAAW,EAAE;UAAE,CAAE,CAAC;QAAD;MAE3D;IAAE,CACH,CAAC,EACFhC,IAAA,CAACZ,IAAI,CAACoC,MAAM;MACVC,IAAI,EAAC,cAAc;MACnBC,OAAO,EAAE;QACPC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,SAAZA,UAAUA,CAAAU,KAAA,EACR;UAAA,IADaR,IAAI,GAAAQ,KAAA,CAAJR,IAAI;YAAEC,KAAK,GAAAO,KAAA,CAALP,KAAK;UAAA3B,aAAA,GAAAS,CAAA;UAAAT,aAAA,GAAAC,CAAA;UACxB,OAAAL,IAAA,CAACJ,UAAU;YAACkC,IAAI,EAAEA,IAAK;YAACC,KAAK,EAAEA,KAAM;YAACC,WAAW,EAAE;UAAE,CAAE,CAAC;QAAD;MAE3D;IAAE,CACH,CAAC,EACFhC,IAAA,CAACZ,IAAI,CAACoC,MAAM;MACVC,IAAI,EAAC,SAAS;MACdC,OAAO,EAAE;QACPC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,SAAZA,UAAUA,CAAAW,KAAA,EACR;UAAA,IADaT,IAAI,GAAAS,KAAA,CAAJT,IAAI;YAAEC,KAAK,GAAAQ,KAAA,CAALR,KAAK;UAAA3B,aAAA,GAAAS,CAAA;UAAAT,aAAA,GAAAC,CAAA;UACxB,OAAAL,IAAA,CAACN,IAAI;YAACoC,IAAI,EAAEA,IAAK;YAACC,KAAK,EAAEA,KAAM;YAACC,WAAW,EAAE;UAAE,CAAE,CAAC;QAAD;MAErD;IAAE,CACH,CAAC;EAAA,CACE,CAAC;AAEX;AAEA,IAAMf,MAAM,IAAAb,aAAA,GAAAC,CAAA,QAAGP,UAAU,CAAC0C,MAAM,CAAC;EAC/BtB,MAAM,EAAE;IACNuB,eAAe,EAAEtC,MAAM,CAACK,KAAK;IAC7BkC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAExC,MAAM,CAACQ,SAAS;IAChCiC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,MAAM,EAAE;EACV,CAAC;EACDxB,WAAW,EAAE;IACXyB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,cAAc;IAC1BC,SAAS,EAAE;EACb;AACF,CAAC,CAAC", "ignoreList": []}