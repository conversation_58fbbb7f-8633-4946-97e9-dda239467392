ca1ad03d72eb0d082f0379b6191591c9
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _react = _interopRequireWildcard(require("react"));
var React = _react;
var _useMergeRefs = _interopRequireDefault(require("../../modules/useMergeRefs"));
var _usePressEvents = _interopRequireDefault(require("../../modules/usePressEvents"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _View = _interopRequireDefault(require("../View"));
var _excluded = ["activeOpacity", "children", "delayPressIn", "delayPressOut", "delayLongPress", "disabled", "focusable", "onHideUnderlay", "onLongPress", "onPress", "onPressIn", "onPressOut", "onShowUnderlay", "rejectResponderTermination", "style", "testOnly_pressed", "underlayColor"];
function createExtraStyles(activeOpacity, underlayColor) {
  return {
    child: {
      opacity: activeOpacity !== null && activeOpacity !== void 0 ? activeOpacity : 0.85
    },
    underlay: {
      backgroundColor: underlayColor === undefined ? 'black' : underlayColor
    }
  };
}
function hasPressHandler(props) {
  return props.onPress != null || props.onPressIn != null || props.onPressOut != null || props.onLongPress != null;
}
function TouchableHighlight(props, forwardedRef) {
  var activeOpacity = props.activeOpacity,
    children = props.children,
    delayPressIn = props.delayPressIn,
    delayPressOut = props.delayPressOut,
    delayLongPress = props.delayLongPress,
    disabled = props.disabled,
    focusable = props.focusable,
    onHideUnderlay = props.onHideUnderlay,
    onLongPress = props.onLongPress,
    onPress = props.onPress,
    onPressIn = props.onPressIn,
    onPressOut = props.onPressOut,
    onShowUnderlay = props.onShowUnderlay,
    rejectResponderTermination = props.rejectResponderTermination,
    style = props.style,
    testOnly_pressed = props.testOnly_pressed,
    underlayColor = props.underlayColor,
    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  var hostRef = (0, _react.useRef)(null);
  var setRef = (0, _useMergeRefs.default)(forwardedRef, hostRef);
  var _useState = (0, _react.useState)(testOnly_pressed === true ? createExtraStyles(activeOpacity, underlayColor) : null),
    extraStyles = _useState[0],
    setExtraStyles = _useState[1];
  var showUnderlay = (0, _react.useCallback)(function () {
    if (!hasPressHandler(props)) {
      return;
    }
    setExtraStyles(createExtraStyles(activeOpacity, underlayColor));
    if (onShowUnderlay != null) {
      onShowUnderlay();
    }
  }, [activeOpacity, onShowUnderlay, props, underlayColor]);
  var hideUnderlay = (0, _react.useCallback)(function () {
    if (testOnly_pressed === true) {
      return;
    }
    if (hasPressHandler(props)) {
      setExtraStyles(null);
      if (onHideUnderlay != null) {
        onHideUnderlay();
      }
    }
  }, [onHideUnderlay, props, testOnly_pressed]);
  var pressConfig = (0, _react.useMemo)(function () {
    return {
      cancelable: !rejectResponderTermination,
      disabled: disabled,
      delayLongPress: delayLongPress,
      delayPressStart: delayPressIn,
      delayPressEnd: delayPressOut,
      onLongPress: onLongPress,
      onPress: onPress,
      onPressStart: function onPressStart(event) {
        showUnderlay();
        if (onPressIn != null) {
          onPressIn(event);
        }
      },
      onPressEnd: function onPressEnd(event) {
        hideUnderlay();
        if (onPressOut != null) {
          onPressOut(event);
        }
      }
    };
  }, [delayLongPress, delayPressIn, delayPressOut, disabled, onLongPress, onPress, onPressIn, onPressOut, rejectResponderTermination, showUnderlay, hideUnderlay]);
  var pressEventHandlers = (0, _usePressEvents.default)(hostRef, pressConfig);
  var child = React.Children.only(children);
  return React.createElement(_View.default, (0, _extends2.default)({}, rest, pressEventHandlers, {
    accessibilityDisabled: disabled,
    focusable: !disabled && focusable !== false,
    pointerEvents: disabled ? 'box-none' : undefined,
    ref: setRef,
    style: [styles.root, style, !disabled && styles.actionable, extraStyles && extraStyles.underlay]
  }), React.cloneElement(child, {
    style: [child.props.style, extraStyles && extraStyles.child]
  }));
}
var styles = _StyleSheet.default.create({
  root: {
    userSelect: 'none'
  },
  actionable: {
    cursor: 'pointer',
    touchAction: 'manipulation'
  }
});
var MemoedTouchableHighlight = React.memo(React.forwardRef(TouchableHighlight));
MemoedTouchableHighlight.displayName = 'TouchableHighlight';
var _default = exports.default = MemoedTouchableHighlight;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsImRlZmF1bHQiLCJfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCIsImV4cG9ydHMiLCJfX2VzTW9kdWxlIiwiX2V4dGVuZHMyIiwiX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UyIiwiX3JlYWN0IiwiUmVhY3QiLCJfdXNlTWVyZ2VSZWZzIiwiX3VzZVByZXNzRXZlbnRzIiwiX1N0eWxlU2hlZXQiLCJfVmlldyIsIl9leGNsdWRlZCIsImNyZWF0ZUV4dHJhU3R5bGVzIiwiYWN0aXZlT3BhY2l0eSIsInVuZGVybGF5Q29sb3IiLCJjaGlsZCIsIm9wYWNpdHkiLCJ1bmRlcmxheSIsImJhY2tncm91bmRDb2xvciIsInVuZGVmaW5lZCIsImhhc1ByZXNzSGFuZGxlciIsInByb3BzIiwib25QcmVzcyIsIm9uUHJlc3NJbiIsIm9uUHJlc3NPdXQiLCJvbkxvbmdQcmVzcyIsIlRvdWNoYWJsZUhpZ2hsaWdodCIsImZvcndhcmRlZFJlZiIsImNoaWxkcmVuIiwiZGVsYXlQcmVzc0luIiwiZGVsYXlQcmVzc091dCIsImRlbGF5TG9uZ1ByZXNzIiwiZGlzYWJsZWQiLCJmb2N1c2FibGUiLCJvbkhpZGVVbmRlcmxheSIsIm9uU2hvd1VuZGVybGF5IiwicmVqZWN0UmVzcG9uZGVyVGVybWluYXRpb24iLCJzdHlsZSIsInRlc3RPbmx5X3ByZXNzZWQiLCJyZXN0IiwiaG9zdFJlZiIsInVzZVJlZiIsInNldFJlZiIsIl91c2VTdGF0ZSIsInVzZVN0YXRlIiwiZXh0cmFTdHlsZXMiLCJzZXRFeHRyYVN0eWxlcyIsInNob3dVbmRlcmxheSIsInVzZUNhbGxiYWNrIiwiaGlkZVVuZGVybGF5IiwicHJlc3NDb25maWciLCJ1c2VNZW1vIiwiY2FuY2VsYWJsZSIsImRlbGF5UHJlc3NTdGFydCIsImRlbGF5UHJlc3NFbmQiLCJvblByZXNzU3RhcnQiLCJldmVudCIsIm9uUHJlc3NFbmQiLCJwcmVzc0V2ZW50SGFuZGxlcnMiLCJDaGlsZHJlbiIsIm9ubHkiLCJjcmVhdGVFbGVtZW50IiwiYWNjZXNzaWJpbGl0eURpc2FibGVkIiwicG9pbnRlckV2ZW50cyIsInJlZiIsInN0eWxlcyIsInJvb3QiLCJhY3Rpb25hYmxlIiwiY2xvbmVFbGVtZW50IiwiY3JlYXRlIiwidXNlclNlbGVjdCIsImN1cnNvciIsInRvdWNoQWN0aW9uIiwiTWVtb2VkVG91Y2hhYmxlSGlnaGxpZ2h0IiwibWVtbyIsImZvcndhcmRSZWYiLCJkaXNwbGF5TmFtZSIsIl9kZWZhdWx0IiwibW9kdWxlIl0sInNvdXJjZXMiOlsiaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogXG4gKiBAZm9ybWF0XG4gKi9cblxuJ3VzZSBjbGllbnQnO1xuXG52YXIgX2ludGVyb3BSZXF1aXJlRGVmYXVsdCA9IHJlcXVpcmUoXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdFwiKS5kZWZhdWx0O1xudmFyIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkID0gcmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVXaWxkY2FyZFwiKS5kZWZhdWx0O1xuZXhwb3J0cy5fX2VzTW9kdWxlID0gdHJ1ZTtcbmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDtcbnZhciBfZXh0ZW5kczIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2V4dGVuZHNcIikpO1xudmFyIF9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZVwiKSk7XG52YXIgX3JlYWN0ID0gX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQocmVxdWlyZShcInJlYWN0XCIpKTtcbnZhciBSZWFjdCA9IF9yZWFjdDtcbnZhciBfdXNlTWVyZ2VSZWZzID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vLi4vbW9kdWxlcy91c2VNZXJnZVJlZnNcIikpO1xudmFyIF91c2VQcmVzc0V2ZW50cyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4uLy4uL21vZHVsZXMvdXNlUHJlc3NFdmVudHNcIikpO1xudmFyIF9TdHlsZVNoZWV0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vU3R5bGVTaGVldFwiKSk7XG52YXIgX1ZpZXcgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuLi9WaWV3XCIpKTtcbnZhciBfZXhjbHVkZWQgPSBbXCJhY3RpdmVPcGFjaXR5XCIsIFwiY2hpbGRyZW5cIiwgXCJkZWxheVByZXNzSW5cIiwgXCJkZWxheVByZXNzT3V0XCIsIFwiZGVsYXlMb25nUHJlc3NcIiwgXCJkaXNhYmxlZFwiLCBcImZvY3VzYWJsZVwiLCBcIm9uSGlkZVVuZGVybGF5XCIsIFwib25Mb25nUHJlc3NcIiwgXCJvblByZXNzXCIsIFwib25QcmVzc0luXCIsIFwib25QcmVzc091dFwiLCBcIm9uU2hvd1VuZGVybGF5XCIsIFwicmVqZWN0UmVzcG9uZGVyVGVybWluYXRpb25cIiwgXCJzdHlsZVwiLCBcInRlc3RPbmx5X3ByZXNzZWRcIiwgXCJ1bmRlcmxheUNvbG9yXCJdO1xuLy9pbXBvcnQgeyB3YXJuT25jZSB9IGZyb20gJy4uLy4uL21vZHVsZXMvd2Fybk9uY2UnO1xuXG5mdW5jdGlvbiBjcmVhdGVFeHRyYVN0eWxlcyhhY3RpdmVPcGFjaXR5LCB1bmRlcmxheUNvbG9yKSB7XG4gIHJldHVybiB7XG4gICAgY2hpbGQ6IHtcbiAgICAgIG9wYWNpdHk6IGFjdGl2ZU9wYWNpdHkgIT09IG51bGwgJiYgYWN0aXZlT3BhY2l0eSAhPT0gdm9pZCAwID8gYWN0aXZlT3BhY2l0eSA6IDAuODVcbiAgICB9LFxuICAgIHVuZGVybGF5OiB7XG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IHVuZGVybGF5Q29sb3IgPT09IHVuZGVmaW5lZCA/ICdibGFjaycgOiB1bmRlcmxheUNvbG9yXG4gICAgfVxuICB9O1xufVxuZnVuY3Rpb24gaGFzUHJlc3NIYW5kbGVyKHByb3BzKSB7XG4gIHJldHVybiBwcm9wcy5vblByZXNzICE9IG51bGwgfHwgcHJvcHMub25QcmVzc0luICE9IG51bGwgfHwgcHJvcHMub25QcmVzc091dCAhPSBudWxsIHx8IHByb3BzLm9uTG9uZ1ByZXNzICE9IG51bGw7XG59XG5cbi8qKlxuICogQSB3cmFwcGVyIGZvciBtYWtpbmcgdmlld3MgcmVzcG9uZCBwcm9wZXJseSB0byB0b3VjaGVzLlxuICogT24gcHJlc3MgZG93biwgdGhlIG9wYWNpdHkgb2YgdGhlIHdyYXBwZWQgdmlldyBpcyBkZWNyZWFzZWQsIHdoaWNoIGFsbG93c1xuICogdGhlIHVuZGVybGF5IGNvbG9yIHRvIHNob3cgdGhyb3VnaCwgZGFya2VuaW5nIG9yIHRpbnRpbmcgdGhlIHZpZXcuXG4gKlxuICogVGhlIHVuZGVybGF5IGNvbWVzIGZyb20gd3JhcHBpbmcgdGhlIGNoaWxkIGluIGEgbmV3IFZpZXcsIHdoaWNoIGNhbiBhZmZlY3RcbiAqIGxheW91dCwgYW5kIHNvbWV0aW1lcyBjYXVzZSB1bndhbnRlZCB2aXN1YWwgYXJ0aWZhY3RzIGlmIG5vdCB1c2VkIGNvcnJlY3RseSxcbiAqIGZvciBleGFtcGxlIGlmIHRoZSBiYWNrZ3JvdW5kQ29sb3Igb2YgdGhlIHdyYXBwZWQgdmlldyBpc24ndCBleHBsaWNpdGx5IHNldFxuICogdG8gYW4gb3BhcXVlIGNvbG9yLlxuICpcbiAqIFRvdWNoYWJsZUhpZ2hsaWdodCBtdXN0IGhhdmUgb25lIGNoaWxkIChub3QgemVybyBvciBtb3JlIHRoYW4gb25lKS5cbiAqIElmIHlvdSB3aXNoIHRvIGhhdmUgc2V2ZXJhbCBjaGlsZCBjb21wb25lbnRzLCB3cmFwIHRoZW0gaW4gYSBWaWV3LlxuICovXG5mdW5jdGlvbiBUb3VjaGFibGVIaWdobGlnaHQocHJvcHMsIGZvcndhcmRlZFJlZikge1xuICAvKlxuICB3YXJuT25jZShcbiAgICAnVG91Y2hhYmxlSGlnaGxpZ2h0JyxcbiAgICAnVG91Y2hhYmxlSGlnaGxpZ2h0IGlzIGRlcHJlY2F0ZWQuIFBsZWFzZSB1c2UgUHJlc3NhYmxlLidcbiAgKTtcbiAgKi9cblxuICB2YXIgYWN0aXZlT3BhY2l0eSA9IHByb3BzLmFjdGl2ZU9wYWNpdHksXG4gICAgY2hpbGRyZW4gPSBwcm9wcy5jaGlsZHJlbixcbiAgICBkZWxheVByZXNzSW4gPSBwcm9wcy5kZWxheVByZXNzSW4sXG4gICAgZGVsYXlQcmVzc091dCA9IHByb3BzLmRlbGF5UHJlc3NPdXQsXG4gICAgZGVsYXlMb25nUHJlc3MgPSBwcm9wcy5kZWxheUxvbmdQcmVzcyxcbiAgICBkaXNhYmxlZCA9IHByb3BzLmRpc2FibGVkLFxuICAgIGZvY3VzYWJsZSA9IHByb3BzLmZvY3VzYWJsZSxcbiAgICBvbkhpZGVVbmRlcmxheSA9IHByb3BzLm9uSGlkZVVuZGVybGF5LFxuICAgIG9uTG9uZ1ByZXNzID0gcHJvcHMub25Mb25nUHJlc3MsXG4gICAgb25QcmVzcyA9IHByb3BzLm9uUHJlc3MsXG4gICAgb25QcmVzc0luID0gcHJvcHMub25QcmVzc0luLFxuICAgIG9uUHJlc3NPdXQgPSBwcm9wcy5vblByZXNzT3V0LFxuICAgIG9uU2hvd1VuZGVybGF5ID0gcHJvcHMub25TaG93VW5kZXJsYXksXG4gICAgcmVqZWN0UmVzcG9uZGVyVGVybWluYXRpb24gPSBwcm9wcy5yZWplY3RSZXNwb25kZXJUZXJtaW5hdGlvbixcbiAgICBzdHlsZSA9IHByb3BzLnN0eWxlLFxuICAgIHRlc3RPbmx5X3ByZXNzZWQgPSBwcm9wcy50ZXN0T25seV9wcmVzc2VkLFxuICAgIHVuZGVybGF5Q29sb3IgPSBwcm9wcy51bmRlcmxheUNvbG9yLFxuICAgIHJlc3QgPSAoMCwgX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UyLmRlZmF1bHQpKHByb3BzLCBfZXhjbHVkZWQpO1xuICB2YXIgaG9zdFJlZiA9ICgwLCBfcmVhY3QudXNlUmVmKShudWxsKTtcbiAgdmFyIHNldFJlZiA9ICgwLCBfdXNlTWVyZ2VSZWZzLmRlZmF1bHQpKGZvcndhcmRlZFJlZiwgaG9zdFJlZik7XG4gIHZhciBfdXNlU3RhdGUgPSAoMCwgX3JlYWN0LnVzZVN0YXRlKSh0ZXN0T25seV9wcmVzc2VkID09PSB0cnVlID8gY3JlYXRlRXh0cmFTdHlsZXMoYWN0aXZlT3BhY2l0eSwgdW5kZXJsYXlDb2xvcikgOiBudWxsKSxcbiAgICBleHRyYVN0eWxlcyA9IF91c2VTdGF0ZVswXSxcbiAgICBzZXRFeHRyYVN0eWxlcyA9IF91c2VTdGF0ZVsxXTtcbiAgdmFyIHNob3dVbmRlcmxheSA9ICgwLCBfcmVhY3QudXNlQ2FsbGJhY2spKCgpID0+IHtcbiAgICBpZiAoIWhhc1ByZXNzSGFuZGxlcihwcm9wcykpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgc2V0RXh0cmFTdHlsZXMoY3JlYXRlRXh0cmFTdHlsZXMoYWN0aXZlT3BhY2l0eSwgdW5kZXJsYXlDb2xvcikpO1xuICAgIGlmIChvblNob3dVbmRlcmxheSAhPSBudWxsKSB7XG4gICAgICBvblNob3dVbmRlcmxheSgpO1xuICAgIH1cbiAgfSwgW2FjdGl2ZU9wYWNpdHksIG9uU2hvd1VuZGVybGF5LCBwcm9wcywgdW5kZXJsYXlDb2xvcl0pO1xuICB2YXIgaGlkZVVuZGVybGF5ID0gKDAsIF9yZWFjdC51c2VDYWxsYmFjaykoKCkgPT4ge1xuICAgIGlmICh0ZXN0T25seV9wcmVzc2VkID09PSB0cnVlKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIGlmIChoYXNQcmVzc0hhbmRsZXIocHJvcHMpKSB7XG4gICAgICBzZXRFeHRyYVN0eWxlcyhudWxsKTtcbiAgICAgIGlmIChvbkhpZGVVbmRlcmxheSAhPSBudWxsKSB7XG4gICAgICAgIG9uSGlkZVVuZGVybGF5KCk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbb25IaWRlVW5kZXJsYXksIHByb3BzLCB0ZXN0T25seV9wcmVzc2VkXSk7XG4gIHZhciBwcmVzc0NvbmZpZyA9ICgwLCBfcmVhY3QudXNlTWVtbykoKCkgPT4gKHtcbiAgICBjYW5jZWxhYmxlOiAhcmVqZWN0UmVzcG9uZGVyVGVybWluYXRpb24sXG4gICAgZGlzYWJsZWQsXG4gICAgZGVsYXlMb25nUHJlc3MsXG4gICAgZGVsYXlQcmVzc1N0YXJ0OiBkZWxheVByZXNzSW4sXG4gICAgZGVsYXlQcmVzc0VuZDogZGVsYXlQcmVzc091dCxcbiAgICBvbkxvbmdQcmVzcyxcbiAgICBvblByZXNzLFxuICAgIG9uUHJlc3NTdGFydChldmVudCkge1xuICAgICAgc2hvd1VuZGVybGF5KCk7XG4gICAgICBpZiAob25QcmVzc0luICE9IG51bGwpIHtcbiAgICAgICAgb25QcmVzc0luKGV2ZW50KTtcbiAgICAgIH1cbiAgICB9LFxuICAgIG9uUHJlc3NFbmQoZXZlbnQpIHtcbiAgICAgIGhpZGVVbmRlcmxheSgpO1xuICAgICAgaWYgKG9uUHJlc3NPdXQgIT0gbnVsbCkge1xuICAgICAgICBvblByZXNzT3V0KGV2ZW50KTtcbiAgICAgIH1cbiAgICB9XG4gIH0pLCBbZGVsYXlMb25nUHJlc3MsIGRlbGF5UHJlc3NJbiwgZGVsYXlQcmVzc091dCwgZGlzYWJsZWQsIG9uTG9uZ1ByZXNzLCBvblByZXNzLCBvblByZXNzSW4sIG9uUHJlc3NPdXQsIHJlamVjdFJlc3BvbmRlclRlcm1pbmF0aW9uLCBzaG93VW5kZXJsYXksIGhpZGVVbmRlcmxheV0pO1xuICB2YXIgcHJlc3NFdmVudEhhbmRsZXJzID0gKDAsIF91c2VQcmVzc0V2ZW50cy5kZWZhdWx0KShob3N0UmVmLCBwcmVzc0NvbmZpZyk7XG4gIHZhciBjaGlsZCA9IFJlYWN0LkNoaWxkcmVuLm9ubHkoY2hpbGRyZW4pO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoX1ZpZXcuZGVmYXVsdCwgKDAsIF9leHRlbmRzMi5kZWZhdWx0KSh7fSwgcmVzdCwgcHJlc3NFdmVudEhhbmRsZXJzLCB7XG4gICAgYWNjZXNzaWJpbGl0eURpc2FibGVkOiBkaXNhYmxlZCxcbiAgICBmb2N1c2FibGU6ICFkaXNhYmxlZCAmJiBmb2N1c2FibGUgIT09IGZhbHNlLFxuICAgIHBvaW50ZXJFdmVudHM6IGRpc2FibGVkID8gJ2JveC1ub25lJyA6IHVuZGVmaW5lZCxcbiAgICByZWY6IHNldFJlZixcbiAgICBzdHlsZTogW3N0eWxlcy5yb290LCBzdHlsZSwgIWRpc2FibGVkICYmIHN0eWxlcy5hY3Rpb25hYmxlLCBleHRyYVN0eWxlcyAmJiBleHRyYVN0eWxlcy51bmRlcmxheV1cbiAgfSksIC8qI19fUFVSRV9fKi9SZWFjdC5jbG9uZUVsZW1lbnQoY2hpbGQsIHtcbiAgICBzdHlsZTogW2NoaWxkLnByb3BzLnN0eWxlLCBleHRyYVN0eWxlcyAmJiBleHRyYVN0eWxlcy5jaGlsZF1cbiAgfSkpO1xufVxudmFyIHN0eWxlcyA9IF9TdHlsZVNoZWV0LmRlZmF1bHQuY3JlYXRlKHtcbiAgcm9vdDoge1xuICAgIHVzZXJTZWxlY3Q6ICdub25lJ1xuICB9LFxuICBhY3Rpb25hYmxlOiB7XG4gICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgdG91Y2hBY3Rpb246ICdtYW5pcHVsYXRpb24nXG4gIH1cbn0pO1xudmFyIE1lbW9lZFRvdWNoYWJsZUhpZ2hsaWdodCA9IC8qI19fUFVSRV9fKi9SZWFjdC5tZW1vKC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKFRvdWNoYWJsZUhpZ2hsaWdodCkpO1xuTWVtb2VkVG91Y2hhYmxlSGlnaGxpZ2h0LmRpc3BsYXlOYW1lID0gJ1RvdWNoYWJsZUhpZ2hsaWdodCc7XG52YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSBNZW1vZWRUb3VjaGFibGVIaWdobGlnaHQ7XG5tb2R1bGUuZXhwb3J0cyA9IGV4cG9ydHMuZGVmYXVsdDsiXSwibWFwcGluZ3MiOiJBQUFBLFlBQVk7QUFXWixZQUFZOztBQUVaLElBQUlBLHNCQUFzQixHQUFHQyxPQUFPLENBQUMsOENBQThDLENBQUMsQ0FBQ0MsT0FBTztBQUM1RixJQUFJQyx1QkFBdUIsR0FBR0YsT0FBTyxDQUFDLCtDQUErQyxDQUFDLENBQUNDLE9BQU87QUFDOUZFLE9BQU8sQ0FBQ0MsVUFBVSxHQUFHLElBQUk7QUFDekJELE9BQU8sQ0FBQ0YsT0FBTyxHQUFHLEtBQUssQ0FBQztBQUN4QixJQUFJSSxTQUFTLEdBQUdOLHNCQUFzQixDQUFDQyxPQUFPLENBQUMsZ0NBQWdDLENBQUMsQ0FBQztBQUNqRixJQUFJTSw4QkFBOEIsR0FBR1Asc0JBQXNCLENBQUNDLE9BQU8sQ0FBQyxxREFBcUQsQ0FBQyxDQUFDO0FBQzNILElBQUlPLE1BQU0sR0FBR0wsdUJBQXVCLENBQUNGLE9BQU8sQ0FBQyxPQUFPLENBQUMsQ0FBQztBQUN0RCxJQUFJUSxLQUFLLEdBQUdELE1BQU07QUFDbEIsSUFBSUUsYUFBYSxHQUFHVixzQkFBc0IsQ0FBQ0MsT0FBTyw2QkFBNkIsQ0FBQyxDQUFDO0FBQ2pGLElBQUlVLGVBQWUsR0FBR1gsc0JBQXNCLENBQUNDLE9BQU8sK0JBQStCLENBQUMsQ0FBQztBQUNyRixJQUFJVyxXQUFXLEdBQUdaLHNCQUFzQixDQUFDQyxPQUFPLGdCQUFnQixDQUFDLENBQUM7QUFDbEUsSUFBSVksS0FBSyxHQUFHYixzQkFBc0IsQ0FBQ0MsT0FBTyxVQUFVLENBQUMsQ0FBQztBQUN0RCxJQUFJYSxTQUFTLEdBQUcsQ0FBQyxlQUFlLEVBQUUsVUFBVSxFQUFFLGNBQWMsRUFBRSxlQUFlLEVBQUUsZ0JBQWdCLEVBQUUsVUFBVSxFQUFFLFdBQVcsRUFBRSxnQkFBZ0IsRUFBRSxhQUFhLEVBQUUsU0FBUyxFQUFFLFdBQVcsRUFBRSxZQUFZLEVBQUUsZ0JBQWdCLEVBQUUsNEJBQTRCLEVBQUUsT0FBTyxFQUFFLGtCQUFrQixFQUFFLGVBQWUsQ0FBQztBQUc5UixTQUFTQyxpQkFBaUJBLENBQUNDLGFBQWEsRUFBRUMsYUFBYSxFQUFFO0VBQ3ZELE9BQU87SUFDTEMsS0FBSyxFQUFFO01BQ0xDLE9BQU8sRUFBRUgsYUFBYSxLQUFLLElBQUksSUFBSUEsYUFBYSxLQUFLLEtBQUssQ0FBQyxHQUFHQSxhQUFhLEdBQUc7SUFDaEYsQ0FBQztJQUNESSxRQUFRLEVBQUU7TUFDUkMsZUFBZSxFQUFFSixhQUFhLEtBQUtLLFNBQVMsR0FBRyxPQUFPLEdBQUdMO0lBQzNEO0VBQ0YsQ0FBQztBQUNIO0FBQ0EsU0FBU00sZUFBZUEsQ0FBQ0MsS0FBSyxFQUFFO0VBQzlCLE9BQU9BLEtBQUssQ0FBQ0MsT0FBTyxJQUFJLElBQUksSUFBSUQsS0FBSyxDQUFDRSxTQUFTLElBQUksSUFBSSxJQUFJRixLQUFLLENBQUNHLFVBQVUsSUFBSSxJQUFJLElBQUlILEtBQUssQ0FBQ0ksV0FBVyxJQUFJLElBQUk7QUFDbEg7QUFlQSxTQUFTQyxrQkFBa0JBLENBQUNMLEtBQUssRUFBRU0sWUFBWSxFQUFFO0VBUS9DLElBQUlkLGFBQWEsR0FBR1EsS0FBSyxDQUFDUixhQUFhO0lBQ3JDZSxRQUFRLEdBQUdQLEtBQUssQ0FBQ08sUUFBUTtJQUN6QkMsWUFBWSxHQUFHUixLQUFLLENBQUNRLFlBQVk7SUFDakNDLGFBQWEsR0FBR1QsS0FBSyxDQUFDUyxhQUFhO0lBQ25DQyxjQUFjLEdBQUdWLEtBQUssQ0FBQ1UsY0FBYztJQUNyQ0MsUUFBUSxHQUFHWCxLQUFLLENBQUNXLFFBQVE7SUFDekJDLFNBQVMsR0FBR1osS0FBSyxDQUFDWSxTQUFTO0lBQzNCQyxjQUFjLEdBQUdiLEtBQUssQ0FBQ2EsY0FBYztJQUNyQ1QsV0FBVyxHQUFHSixLQUFLLENBQUNJLFdBQVc7SUFDL0JILE9BQU8sR0FBR0QsS0FBSyxDQUFDQyxPQUFPO0lBQ3ZCQyxTQUFTLEdBQUdGLEtBQUssQ0FBQ0UsU0FBUztJQUMzQkMsVUFBVSxHQUFHSCxLQUFLLENBQUNHLFVBQVU7SUFDN0JXLGNBQWMsR0FBR2QsS0FBSyxDQUFDYyxjQUFjO0lBQ3JDQywwQkFBMEIsR0FBR2YsS0FBSyxDQUFDZSwwQkFBMEI7SUFDN0RDLEtBQUssR0FBR2hCLEtBQUssQ0FBQ2dCLEtBQUs7SUFDbkJDLGdCQUFnQixHQUFHakIsS0FBSyxDQUFDaUIsZ0JBQWdCO0lBQ3pDeEIsYUFBYSxHQUFHTyxLQUFLLENBQUNQLGFBQWE7SUFDbkN5QixJQUFJLEdBQUcsQ0FBQyxDQUFDLEVBQUVuQyw4QkFBOEIsQ0FBQ0wsT0FBTyxFQUFFc0IsS0FBSyxFQUFFVixTQUFTLENBQUM7RUFDdEUsSUFBSTZCLE9BQU8sR0FBRyxDQUFDLENBQUMsRUFBRW5DLE1BQU0sQ0FBQ29DLE1BQU0sRUFBRSxJQUFJLENBQUM7RUFDdEMsSUFBSUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxFQUFFbkMsYUFBYSxDQUFDUixPQUFPLEVBQUU0QixZQUFZLEVBQUVhLE9BQU8sQ0FBQztFQUM5RCxJQUFJRyxTQUFTLEdBQUcsQ0FBQyxDQUFDLEVBQUV0QyxNQUFNLENBQUN1QyxRQUFRLEVBQUVOLGdCQUFnQixLQUFLLElBQUksR0FBRzFCLGlCQUFpQixDQUFDQyxhQUFhLEVBQUVDLGFBQWEsQ0FBQyxHQUFHLElBQUksQ0FBQztJQUN0SCtCLFdBQVcsR0FBR0YsU0FBUyxDQUFDLENBQUMsQ0FBQztJQUMxQkcsY0FBYyxHQUFHSCxTQUFTLENBQUMsQ0FBQyxDQUFDO0VBQy9CLElBQUlJLFlBQVksR0FBRyxDQUFDLENBQUMsRUFBRTFDLE1BQU0sQ0FBQzJDLFdBQVcsRUFBRSxZQUFNO0lBQy9DLElBQUksQ0FBQzVCLGVBQWUsQ0FBQ0MsS0FBSyxDQUFDLEVBQUU7TUFDM0I7SUFDRjtJQUNBeUIsY0FBYyxDQUFDbEMsaUJBQWlCLENBQUNDLGFBQWEsRUFBRUMsYUFBYSxDQUFDLENBQUM7SUFDL0QsSUFBSXFCLGNBQWMsSUFBSSxJQUFJLEVBQUU7TUFDMUJBLGNBQWMsQ0FBQyxDQUFDO0lBQ2xCO0VBQ0YsQ0FBQyxFQUFFLENBQUN0QixhQUFhLEVBQUVzQixjQUFjLEVBQUVkLEtBQUssRUFBRVAsYUFBYSxDQUFDLENBQUM7RUFDekQsSUFBSW1DLFlBQVksR0FBRyxDQUFDLENBQUMsRUFBRTVDLE1BQU0sQ0FBQzJDLFdBQVcsRUFBRSxZQUFNO0lBQy9DLElBQUlWLGdCQUFnQixLQUFLLElBQUksRUFBRTtNQUM3QjtJQUNGO0lBQ0EsSUFBSWxCLGVBQWUsQ0FBQ0MsS0FBSyxDQUFDLEVBQUU7TUFDMUJ5QixjQUFjLENBQUMsSUFBSSxDQUFDO01BQ3BCLElBQUlaLGNBQWMsSUFBSSxJQUFJLEVBQUU7UUFDMUJBLGNBQWMsQ0FBQyxDQUFDO01BQ2xCO0lBQ0Y7RUFDRixDQUFDLEVBQUUsQ0FBQ0EsY0FBYyxFQUFFYixLQUFLLEVBQUVpQixnQkFBZ0IsQ0FBQyxDQUFDO0VBQzdDLElBQUlZLFdBQVcsR0FBRyxDQUFDLENBQUMsRUFBRTdDLE1BQU0sQ0FBQzhDLE9BQU8sRUFBRTtJQUFBLE9BQU87TUFDM0NDLFVBQVUsRUFBRSxDQUFDaEIsMEJBQTBCO01BQ3ZDSixRQUFRLEVBQVJBLFFBQVE7TUFDUkQsY0FBYyxFQUFkQSxjQUFjO01BQ2RzQixlQUFlLEVBQUV4QixZQUFZO01BQzdCeUIsYUFBYSxFQUFFeEIsYUFBYTtNQUM1QkwsV0FBVyxFQUFYQSxXQUFXO01BQ1hILE9BQU8sRUFBUEEsT0FBTztNQUNQaUMsWUFBWSxXQUFaQSxZQUFZQSxDQUFDQyxLQUFLLEVBQUU7UUFDbEJULFlBQVksQ0FBQyxDQUFDO1FBQ2QsSUFBSXhCLFNBQVMsSUFBSSxJQUFJLEVBQUU7VUFDckJBLFNBQVMsQ0FBQ2lDLEtBQUssQ0FBQztRQUNsQjtNQUNGLENBQUM7TUFDREMsVUFBVSxXQUFWQSxVQUFVQSxDQUFDRCxLQUFLLEVBQUU7UUFDaEJQLFlBQVksQ0FBQyxDQUFDO1FBQ2QsSUFBSXpCLFVBQVUsSUFBSSxJQUFJLEVBQUU7VUFDdEJBLFVBQVUsQ0FBQ2dDLEtBQUssQ0FBQztRQUNuQjtNQUNGO0lBQ0YsQ0FBQztFQUFBLENBQUMsRUFBRSxDQUFDekIsY0FBYyxFQUFFRixZQUFZLEVBQUVDLGFBQWEsRUFBRUUsUUFBUSxFQUFFUCxXQUFXLEVBQUVILE9BQU8sRUFBRUMsU0FBUyxFQUFFQyxVQUFVLEVBQUVZLDBCQUEwQixFQUFFVyxZQUFZLEVBQUVFLFlBQVksQ0FBQyxDQUFDO0VBQ2pLLElBQUlTLGtCQUFrQixHQUFHLENBQUMsQ0FBQyxFQUFFbEQsZUFBZSxDQUFDVCxPQUFPLEVBQUV5QyxPQUFPLEVBQUVVLFdBQVcsQ0FBQztFQUMzRSxJQUFJbkMsS0FBSyxHQUFHVCxLQUFLLENBQUNxRCxRQUFRLENBQUNDLElBQUksQ0FBQ2hDLFFBQVEsQ0FBQztFQUN6QyxPQUFvQnRCLEtBQUssQ0FBQ3VELGFBQWEsQ0FBQ25ELEtBQUssQ0FBQ1gsT0FBTyxFQUFFLENBQUMsQ0FBQyxFQUFFSSxTQUFTLENBQUNKLE9BQU8sRUFBRSxDQUFDLENBQUMsRUFBRXdDLElBQUksRUFBRW1CLGtCQUFrQixFQUFFO0lBQzFHSSxxQkFBcUIsRUFBRTlCLFFBQVE7SUFDL0JDLFNBQVMsRUFBRSxDQUFDRCxRQUFRLElBQUlDLFNBQVMsS0FBSyxLQUFLO0lBQzNDOEIsYUFBYSxFQUFFL0IsUUFBUSxHQUFHLFVBQVUsR0FBR2IsU0FBUztJQUNoRDZDLEdBQUcsRUFBRXRCLE1BQU07SUFDWEwsS0FBSyxFQUFFLENBQUM0QixNQUFNLENBQUNDLElBQUksRUFBRTdCLEtBQUssRUFBRSxDQUFDTCxRQUFRLElBQUlpQyxNQUFNLENBQUNFLFVBQVUsRUFBRXRCLFdBQVcsSUFBSUEsV0FBVyxDQUFDNUIsUUFBUTtFQUNqRyxDQUFDLENBQUMsRUFBZVgsS0FBSyxDQUFDOEQsWUFBWSxDQUFDckQsS0FBSyxFQUFFO0lBQ3pDc0IsS0FBSyxFQUFFLENBQUN0QixLQUFLLENBQUNNLEtBQUssQ0FBQ2dCLEtBQUssRUFBRVEsV0FBVyxJQUFJQSxXQUFXLENBQUM5QixLQUFLO0VBQzdELENBQUMsQ0FBQyxDQUFDO0FBQ0w7QUFDQSxJQUFJa0QsTUFBTSxHQUFHeEQsV0FBVyxDQUFDVixPQUFPLENBQUNzRSxNQUFNLENBQUM7RUFDdENILElBQUksRUFBRTtJQUNKSSxVQUFVLEVBQUU7RUFDZCxDQUFDO0VBQ0RILFVBQVUsRUFBRTtJQUNWSSxNQUFNLEVBQUUsU0FBUztJQUNqQkMsV0FBVyxFQUFFO0VBQ2Y7QUFDRixDQUFDLENBQUM7QUFDRixJQUFJQyx3QkFBd0IsR0FBZ0JuRSxLQUFLLENBQUNvRSxJQUFJLENBQWNwRSxLQUFLLENBQUNxRSxVQUFVLENBQUNqRCxrQkFBa0IsQ0FBQyxDQUFDO0FBQ3pHK0Msd0JBQXdCLENBQUNHLFdBQVcsR0FBRyxvQkFBb0I7QUFDM0QsSUFBSUMsUUFBUSxHQUFHNUUsT0FBTyxDQUFDRixPQUFPLEdBQUcwRSx3QkFBd0I7QUFDekRLLE1BQU0sQ0FBQzdFLE9BQU8sR0FBR0EsT0FBTyxDQUFDRixPQUFPIiwiaWdub3JlTGlzdCI6W119