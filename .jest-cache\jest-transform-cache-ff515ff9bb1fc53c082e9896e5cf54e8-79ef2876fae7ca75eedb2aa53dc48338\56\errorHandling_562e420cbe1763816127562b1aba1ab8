2ae72af9caf0aa0b9dd376fe394e3bac
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
function cov_r896t9nnj() {
  var path = "C:\\_SaaS\\AceMind\\project\\utils\\errorHandling.ts";
  var hash = "0af500f361a429cdec7d6a457dcb03b15c915736";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\utils\\errorHandling.ts",
    statementMap: {
      "0": {
        start: {
          line: 69,
          column: 116
        },
        end: {
          line: 238,
          column: 1
        }
      },
      "1": {
        start: {
          line: 243,
          column: 30
        },
        end: {
          line: 258,
          column: 1
        }
      },
      "2": {
        start: {
          line: 248,
          column: 20
        },
        end: {
          line: 248,
          column: 40
        }
      },
      "3": {
        start: {
          line: 250,
          column: 2
        },
        end: {
          line: 257,
          column: 4
        }
      },
      "4": {
        start: {
          line: 263,
          column: 24
        },
        end: {
          line: 285,
          column: 1
        }
      },
      "5": {
        start: {
          line: 264,
          column: 20
        },
        end: {
          line: 273,
          column: 3
        }
      },
      "6": {
        start: {
          line: 276,
          column: 2
        },
        end: {
          line: 278,
          column: 3
        }
      },
      "7": {
        start: {
          line: 277,
          column: 4
        },
        end: {
          line: 277,
          column: 42
        }
      },
      "8": {
        start: {
          line: 281,
          column: 2
        },
        end: {
          line: 284,
          column: 3
        }
      },
      "9": {
        start: {
          line: 290,
          column: 27
        },
        end: {
          line: 342,
          column: 1
        }
      },
      "10": {
        start: {
          line: 299,
          column: 70
        },
        end: {
          line: 299,
          column: 77
        }
      },
      "11": {
        start: {
          line: 304,
          column: 2
        },
        end: {
          line: 319,
          column: 3
        }
      },
      "12": {
        start: {
          line: 306,
          column: 4
        },
        end: {
          line: 314,
          column: 5
        }
      },
      "13": {
        start: {
          line: 307,
          column: 6
        },
        end: {
          line: 307,
          column: 61
        }
      },
      "14": {
        start: {
          line: 308,
          column: 11
        },
        end: {
          line: 314,
          column: 5
        }
      },
      "15": {
        start: {
          line: 309,
          column: 6
        },
        end: {
          line: 309,
          column: 51
        }
      },
      "16": {
        start: {
          line: 310,
          column: 11
        },
        end: {
          line: 314,
          column: 5
        }
      },
      "17": {
        start: {
          line: 311,
          column: 6
        },
        end: {
          line: 311,
          column: 56
        }
      },
      "18": {
        start: {
          line: 313,
          column: 6
        },
        end: {
          line: 313,
          column: 83
        }
      },
      "19": {
        start: {
          line: 315,
          column: 9
        },
        end: {
          line: 319,
          column: 3
        }
      },
      "20": {
        start: {
          line: 316,
          column: 4
        },
        end: {
          line: 316,
          column: 33
        }
      },
      "21": {
        start: {
          line: 318,
          column: 4
        },
        end: {
          line: 318,
          column: 81
        }
      },
      "22": {
        start: {
          line: 322,
          column: 2
        },
        end: {
          line: 322,
          column: 21
        }
      },
      "23": {
        start: {
          line: 325,
          column: 2
        },
        end: {
          line: 339,
          column: 3
        }
      },
      "24": {
        start: {
          line: 326,
          column: 18
        },
        end: {
          line: 326,
          column: 96
        }
      },
      "25": {
        start: {
          line: 327,
          column: 20
        },
        end: {
          line: 327,
          column: 57
        }
      },
      "26": {
        start: {
          line: 329,
          column: 4
        },
        end: {
          line: 338,
          column: 6
        }
      },
      "27": {
        start: {
          line: 341,
          column: 2
        },
        end: {
          line: 341,
          column: 18
        }
      },
      "28": {
        start: {
          line: 347,
          column: 25
        },
        end: {
          line: 381,
          column: 1
        }
      },
      "29": {
        start: {
          line: 356,
          column: 69
        },
        end: {
          line: 356,
          column: 76
        }
      },
      "30": {
        start: {
          line: 360,
          column: 2
        },
        end: {
          line: 378,
          column: 3
        }
      },
      "31": {
        start: {
          line: 360,
          column: 21
        },
        end: {
          line: 360,
          column: 22
        }
      },
      "32": {
        start: {
          line: 361,
          column: 4
        },
        end: {
          line: 377,
          column: 5
        }
      },
      "33": {
        start: {
          line: 362,
          column: 6
        },
        end: {
          line: 362,
          column: 31
        }
      },
      "34": {
        start: {
          line: 364,
          column: 6
        },
        end: {
          line: 364,
          column: 76
        }
      },
      "35": {
        start: {
          line: 366,
          column: 6
        },
        end: {
          line: 368,
          column: 7
        }
      },
      "36": {
        start: {
          line: 367,
          column: 8
        },
        end: {
          line: 367,
          column: 24
        }
      },
      "37": {
        start: {
          line: 370,
          column: 6
        },
        end: {
          line: 372,
          column: 7
        }
      },
      "38": {
        start: {
          line: 371,
          column: 8
        },
        end: {
          line: 371,
          column: 36
        }
      },
      "39": {
        start: {
          line: 375,
          column: 23
        },
        end: {
          line: 375,
          column: 73
        }
      },
      "40": {
        start: {
          line: 376,
          column: 6
        },
        end: {
          line: 376,
          column: 66
        }
      },
      "41": {
        start: {
          line: 376,
          column: 35
        },
        end: {
          line: 376,
          column: 64
        }
      },
      "42": {
        start: {
          line: 380,
          column: 2
        },
        end: {
          line: 380,
          column: 19
        }
      },
      "43": {
        start: {
          line: 386,
          column: 25
        },
        end: {
          line: 397,
          column: 1
        }
      },
      "44": {
        start: {
          line: 390,
          column: 2
        },
        end: {
          line: 396,
          column: 3
        }
      },
      "45": {
        start: {
          line: 391,
          column: 17
        },
        end: {
          line: 391,
          column: 34
        }
      },
      "46": {
        start: {
          line: 392,
          column: 4
        },
        end: {
          line: 392,
          column: 20
        }
      },
      "47": {
        start: {
          line: 394,
          column: 21
        },
        end: {
          line: 394,
          column: 61
        }
      },
      "48": {
        start: {
          line: 395,
          column: 4
        },
        end: {
          line: 395,
          column: 47
        }
      },
      "49": {
        start: {
          line: 402,
          column: 34
        },
        end: {
          line: 424,
          column: 1
        }
      },
      "50": {
        start: {
          line: 403,
          column: 2
        },
        end: {
          line: 405,
          column: 3
        }
      },
      "51": {
        start: {
          line: 404,
          column: 4
        },
        end: {
          line: 404,
          column: 55
        }
      },
      "52": {
        start: {
          line: 407,
          column: 2
        },
        end: {
          line: 409,
          column: 3
        }
      },
      "53": {
        start: {
          line: 408,
          column: 4
        },
        end: {
          line: 408,
          column: 55
        }
      },
      "54": {
        start: {
          line: 411,
          column: 2
        },
        end: {
          line: 413,
          column: 3
        }
      },
      "55": {
        start: {
          line: 412,
          column: 4
        },
        end: {
          line: 412,
          column: 45
        }
      },
      "56": {
        start: {
          line: 415,
          column: 2
        },
        end: {
          line: 417,
          column: 3
        }
      },
      "57": {
        start: {
          line: 416,
          column: 4
        },
        end: {
          line: 416,
          column: 50
        }
      },
      "58": {
        start: {
          line: 419,
          column: 2
        },
        end: {
          line: 421,
          column: 3
        }
      },
      "59": {
        start: {
          line: 420,
          column: 4
        },
        end: {
          line: 420,
          column: 50
        }
      },
      "60": {
        start: {
          line: 423,
          column: 2
        },
        end: {
          line: 423,
          column: 66
        }
      },
      "61": {
        start: {
          line: 429,
          column: 35
        },
        end: {
          line: 447,
          column: 1
        }
      },
      "62": {
        start: {
          line: 430,
          column: 2
        },
        end: {
          line: 432,
          column: 3
        }
      },
      "63": {
        start: {
          line: 431,
          column: 4
        },
        end: {
          line: 431,
          column: 49
        }
      },
      "64": {
        start: {
          line: 434,
          column: 2
        },
        end: {
          line: 436,
          column: 3
        }
      },
      "65": {
        start: {
          line: 435,
          column: 4
        },
        end: {
          line: 435,
          column: 50
        }
      },
      "66": {
        start: {
          line: 438,
          column: 2
        },
        end: {
          line: 440,
          column: 3
        }
      },
      "67": {
        start: {
          line: 439,
          column: 4
        },
        end: {
          line: 439,
          column: 50
        }
      },
      "68": {
        start: {
          line: 442,
          column: 2
        },
        end: {
          line: 444,
          column: 3
        }
      },
      "69": {
        start: {
          line: 443,
          column: 4
        },
        end: {
          line: 443,
          column: 53
        }
      },
      "70": {
        start: {
          line: 446,
          column: 2
        },
        end: {
          line: 446,
          column: 69
        }
      },
      "71": {
        start: {
          line: 452,
          column: 31
        },
        end: {
          line: 474,
          column: 1
        }
      },
      "72": {
        start: {
          line: 453,
          column: 2
        },
        end: {
          line: 455,
          column: 3
        }
      },
      "73": {
        start: {
          line: 454,
          column: 4
        },
        end: {
          line: 454,
          column: 54
        }
      },
      "74": {
        start: {
          line: 457,
          column: 2
        },
        end: {
          line: 459,
          column: 3
        }
      },
      "75": {
        start: {
          line: 458,
          column: 4
        },
        end: {
          line: 458,
          column: 53
        }
      },
      "76": {
        start: {
          line: 461,
          column: 2
        },
        end: {
          line: 463,
          column: 3
        }
      },
      "77": {
        start: {
          line: 462,
          column: 4
        },
        end: {
          line: 462,
          column: 52
        }
      },
      "78": {
        start: {
          line: 465,
          column: 2
        },
        end: {
          line: 467,
          column: 3
        }
      },
      "79": {
        start: {
          line: 466,
          column: 4
        },
        end: {
          line: 466,
          column: 50
        }
      },
      "80": {
        start: {
          line: 469,
          column: 2
        },
        end: {
          line: 471,
          column: 3
        }
      },
      "81": {
        start: {
          line: 470,
          column: 4
        },
        end: {
          line: 470,
          column: 49
        }
      },
      "82": {
        start: {
          line: 473,
          column: 2
        },
        end: {
          line: 473,
          column: 68
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 243,
            column: 30
          },
          end: {
            line: 243,
            column: 31
          }
        },
        loc: {
          start: {
            line: 247,
            column: 15
          },
          end: {
            line: 258,
            column: 1
          }
        },
        line: 247
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 263,
            column: 24
          },
          end: {
            line: 263,
            column: 25
          }
        },
        loc: {
          start: {
            line: 263,
            column: 90
          },
          end: {
            line: 285,
            column: 1
          }
        },
        line: 263
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 290,
            column: 27
          },
          end: {
            line: 290,
            column: 28
          }
        },
        loc: {
          start: {
            line: 298,
            column: 15
          },
          end: {
            line: 342,
            column: 1
          }
        },
        line: 298
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 347,
            column: 25
          },
          end: {
            line: 347,
            column: 26
          }
        },
        loc: {
          start: {
            line: 355,
            column: 17
          },
          end: {
            line: 381,
            column: 1
          }
        },
        line: 355
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 376,
            column: 24
          },
          end: {
            line: 376,
            column: 25
          }
        },
        loc: {
          start: {
            line: 376,
            column: 35
          },
          end: {
            line: 376,
            column: 64
          }
        },
        line: 376
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 386,
            column: 25
          },
          end: {
            line: 386,
            column: 26
          }
        },
        loc: {
          start: {
            line: 389,
            column: 46
          },
          end: {
            line: 397,
            column: 1
          }
        },
        line: 389
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 402,
            column: 34
          },
          end: {
            line: 402,
            column: 35
          }
        },
        loc: {
          start: {
            line: 402,
            column: 60
          },
          end: {
            line: 424,
            column: 1
          }
        },
        line: 402
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 429,
            column: 35
          },
          end: {
            line: 429,
            column: 36
          }
        },
        loc: {
          start: {
            line: 429,
            column: 61
          },
          end: {
            line: 447,
            column: 1
          }
        },
        line: 429
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 452,
            column: 31
          },
          end: {
            line: 452,
            column: 32
          }
        },
        loc: {
          start: {
            line: 452,
            column: 57
          },
          end: {
            line: 474,
            column: 1
          }
        },
        line: 452
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 253,
            column: 17
          },
          end: {
            line: 253,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 253,
            column: 17
          },
          end: {
            line: 253,
            column: 30
          }
        }, {
          start: {
            line: 253,
            column: 34
          },
          end: {
            line: 253,
            column: 55
          }
        }],
        line: 253
      },
      "1": {
        loc: {
          start: {
            line: 268,
            column: 8
          },
          end: {
            line: 272,
            column: 13
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 268,
            column: 33
          },
          end: {
            line: 272,
            column: 5
          }
        }, {
          start: {
            line: 272,
            column: 8
          },
          end: {
            line: 272,
            column: 13
          }
        }],
        line: 268
      },
      "2": {
        loc: {
          start: {
            line: 276,
            column: 2
          },
          end: {
            line: 278,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 276,
            column: 2
          },
          end: {
            line: 278,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 276
      },
      "3": {
        loc: {
          start: {
            line: 281,
            column: 2
          },
          end: {
            line: 284,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 281,
            column: 2
          },
          end: {
            line: 284,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 281
      },
      "4": {
        loc: {
          start: {
            line: 281,
            column: 6
          },
          end: {
            line: 281,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 281,
            column: 6
          },
          end: {
            line: 281,
            column: 45
          }
        }, {
          start: {
            line: 281,
            column: 49
          },
          end: {
            line: 281,
            column: 86
          }
        }],
        line: 281
      },
      "5": {
        loc: {
          start: {
            line: 292,
            column: 2
          },
          end: {
            line: 297,
            column: 8
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 297,
            column: 6
          },
          end: {
            line: 297,
            column: 8
          }
        }],
        line: 292
      },
      "6": {
        loc: {
          start: {
            line: 299,
            column: 10
          },
          end: {
            line: 299,
            column: 26
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 299,
            column: 22
          },
          end: {
            line: 299,
            column: 26
          }
        }],
        line: 299
      },
      "7": {
        loc: {
          start: {
            line: 304,
            column: 2
          },
          end: {
            line: 319,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 304,
            column: 2
          },
          end: {
            line: 319,
            column: 3
          }
        }, {
          start: {
            line: 315,
            column: 9
          },
          end: {
            line: 319,
            column: 3
          }
        }],
        line: 304
      },
      "8": {
        loc: {
          start: {
            line: 306,
            column: 4
          },
          end: {
            line: 314,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 306,
            column: 4
          },
          end: {
            line: 314,
            column: 5
          }
        }, {
          start: {
            line: 308,
            column: 11
          },
          end: {
            line: 314,
            column: 5
          }
        }],
        line: 306
      },
      "9": {
        loc: {
          start: {
            line: 306,
            column: 8
          },
          end: {
            line: 306,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 306,
            column: 8
          },
          end: {
            line: 306,
            column: 41
          }
        }, {
          start: {
            line: 306,
            column: 45
          },
          end: {
            line: 306,
            column: 76
          }
        }],
        line: 306
      },
      "10": {
        loc: {
          start: {
            line: 308,
            column: 11
          },
          end: {
            line: 314,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 308,
            column: 11
          },
          end: {
            line: 314,
            column: 5
          }
        }, {
          start: {
            line: 310,
            column: 11
          },
          end: {
            line: 314,
            column: 5
          }
        }],
        line: 308
      },
      "11": {
        loc: {
          start: {
            line: 310,
            column: 11
          },
          end: {
            line: 314,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 310,
            column: 11
          },
          end: {
            line: 314,
            column: 5
          }
        }, {
          start: {
            line: 312,
            column: 11
          },
          end: {
            line: 314,
            column: 5
          }
        }],
        line: 310
      },
      "12": {
        loc: {
          start: {
            line: 310,
            column: 15
          },
          end: {
            line: 310,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 310,
            column: 15
          },
          end: {
            line: 310,
            column: 45
          }
        }, {
          start: {
            line: 310,
            column: 49
          },
          end: {
            line: 310,
            column: 87
          }
        }],
        line: 310
      },
      "13": {
        loc: {
          start: {
            line: 315,
            column: 9
          },
          end: {
            line: 319,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 315,
            column: 9
          },
          end: {
            line: 319,
            column: 3
          }
        }, {
          start: {
            line: 317,
            column: 9
          },
          end: {
            line: 319,
            column: 3
          }
        }],
        line: 315
      },
      "14": {
        loc: {
          start: {
            line: 315,
            column: 13
          },
          end: {
            line: 315,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 315,
            column: 13
          },
          end: {
            line: 315,
            column: 38
          }
        }, {
          start: {
            line: 315,
            column: 42
          },
          end: {
            line: 315,
            column: 56
          }
        }, {
          start: {
            line: 315,
            column: 60
          },
          end: {
            line: 315,
            column: 75
          }
        }],
        line: 315
      },
      "15": {
        loc: {
          start: {
            line: 325,
            column: 2
          },
          end: {
            line: 339,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 325,
            column: 2
          },
          end: {
            line: 339,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 325
      },
      "16": {
        loc: {
          start: {
            line: 326,
            column: 18
          },
          end: {
            line: 326,
            column: 96
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 326,
            column: 18
          },
          end: {
            line: 326,
            column: 29
          }
        }, {
          start: {
            line: 326,
            column: 34
          },
          end: {
            line: 326,
            column: 95
          }
        }],
        line: 326
      },
      "17": {
        loc: {
          start: {
            line: 326,
            column: 34
          },
          end: {
            line: 326,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 326,
            column: 69
          },
          end: {
            line: 326,
            column: 85
          }
        }, {
          start: {
            line: 326,
            column: 88
          },
          end: {
            line: 326,
            column: 95
          }
        }],
        line: 326
      },
      "18": {
        loc: {
          start: {
            line: 327,
            column: 20
          },
          end: {
            line: 327,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 327,
            column: 20
          },
          end: {
            line: 327,
            column: 33
          }
        }, {
          start: {
            line: 327,
            column: 37
          },
          end: {
            line: 327,
            column: 57
          }
        }],
        line: 327
      },
      "19": {
        loc: {
          start: {
            line: 349,
            column: 2
          },
          end: {
            line: 354,
            column: 8
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 354,
            column: 6
          },
          end: {
            line: 354,
            column: 8
          }
        }],
        line: 349
      },
      "20": {
        loc: {
          start: {
            line: 356,
            column: 10
          },
          end: {
            line: 356,
            column: 25
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 356,
            column: 24
          },
          end: {
            line: 356,
            column: 25
          }
        }],
        line: 356
      },
      "21": {
        loc: {
          start: {
            line: 356,
            column: 27
          },
          end: {
            line: 356,
            column: 39
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 356,
            column: 35
          },
          end: {
            line: 356,
            column: 39
          }
        }],
        line: 356
      },
      "22": {
        loc: {
          start: {
            line: 356,
            column: 41
          },
          end: {
            line: 356,
            column: 55
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 356,
            column: 51
          },
          end: {
            line: 356,
            column: 55
          }
        }],
        line: 356
      },
      "23": {
        loc: {
          start: {
            line: 364,
            column: 18
          },
          end: {
            line: 364,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 364,
            column: 43
          },
          end: {
            line: 364,
            column: 48
          }
        }, {
          start: {
            line: 364,
            column: 51
          },
          end: {
            line: 364,
            column: 75
          }
        }],
        line: 364
      },
      "24": {
        loc: {
          start: {
            line: 366,
            column: 6
          },
          end: {
            line: 368,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 366,
            column: 6
          },
          end: {
            line: 368,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 366
      },
      "25": {
        loc: {
          start: {
            line: 370,
            column: 6
          },
          end: {
            line: 372,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 370,
            column: 6
          },
          end: {
            line: 372,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 370
      },
      "26": {
        loc: {
          start: {
            line: 375,
            column: 23
          },
          end: {
            line: 375,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 375,
            column: 33
          },
          end: {
            line: 375,
            column: 65
          }
        }, {
          start: {
            line: 375,
            column: 68
          },
          end: {
            line: 375,
            column: 73
          }
        }],
        line: 375
      },
      "27": {
        loc: {
          start: {
            line: 403,
            column: 2
          },
          end: {
            line: 405,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 403,
            column: 2
          },
          end: {
            line: 405,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 403
      },
      "28": {
        loc: {
          start: {
            line: 407,
            column: 2
          },
          end: {
            line: 409,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 407,
            column: 2
          },
          end: {
            line: 409,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 407
      },
      "29": {
        loc: {
          start: {
            line: 407,
            column: 6
          },
          end: {
            line: 407,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 407,
            column: 6
          },
          end: {
            line: 407,
            column: 36
          }
        }, {
          start: {
            line: 407,
            column: 40
          },
          end: {
            line: 407,
            column: 80
          }
        }],
        line: 407
      },
      "30": {
        loc: {
          start: {
            line: 411,
            column: 2
          },
          end: {
            line: 413,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 411,
            column: 2
          },
          end: {
            line: 413,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 411
      },
      "31": {
        loc: {
          start: {
            line: 411,
            column: 6
          },
          end: {
            line: 411,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 411,
            column: 6
          },
          end: {
            line: 411,
            column: 36
          }
        }, {
          start: {
            line: 411,
            column: 40
          },
          end: {
            line: 411,
            column: 74
          }
        }],
        line: 411
      },
      "32": {
        loc: {
          start: {
            line: 415,
            column: 2
          },
          end: {
            line: 417,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 415,
            column: 2
          },
          end: {
            line: 417,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 415
      },
      "33": {
        loc: {
          start: {
            line: 419,
            column: 2
          },
          end: {
            line: 421,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 419,
            column: 2
          },
          end: {
            line: 421,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 419
      },
      "34": {
        loc: {
          start: {
            line: 430,
            column: 2
          },
          end: {
            line: 432,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 430,
            column: 2
          },
          end: {
            line: 432,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 430
      },
      "35": {
        loc: {
          start: {
            line: 430,
            column: 6
          },
          end: {
            line: 430,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 430,
            column: 6
          },
          end: {
            line: 430,
            column: 31
          }
        }, {
          start: {
            line: 430,
            column: 35
          },
          end: {
            line: 430,
            column: 71
          }
        }],
        line: 430
      },
      "36": {
        loc: {
          start: {
            line: 434,
            column: 2
          },
          end: {
            line: 436,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 434,
            column: 2
          },
          end: {
            line: 436,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 434
      },
      "37": {
        loc: {
          start: {
            line: 434,
            column: 6
          },
          end: {
            line: 434,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 434,
            column: 6
          },
          end: {
            line: 434,
            column: 31
          }
        }, {
          start: {
            line: 434,
            column: 35
          },
          end: {
            line: 434,
            column: 79
          }
        }],
        line: 434
      },
      "38": {
        loc: {
          start: {
            line: 438,
            column: 2
          },
          end: {
            line: 440,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 438,
            column: 2
          },
          end: {
            line: 440,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 438
      },
      "39": {
        loc: {
          start: {
            line: 442,
            column: 2
          },
          end: {
            line: 444,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 442,
            column: 2
          },
          end: {
            line: 444,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 442
      },
      "40": {
        loc: {
          start: {
            line: 453,
            column: 2
          },
          end: {
            line: 455,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 453,
            column: 2
          },
          end: {
            line: 455,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 453
      },
      "41": {
        loc: {
          start: {
            line: 457,
            column: 2
          },
          end: {
            line: 459,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 457,
            column: 2
          },
          end: {
            line: 459,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 457
      },
      "42": {
        loc: {
          start: {
            line: 461,
            column: 2
          },
          end: {
            line: 463,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 461,
            column: 2
          },
          end: {
            line: 463,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 461
      },
      "43": {
        loc: {
          start: {
            line: 465,
            column: 2
          },
          end: {
            line: 467,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 465,
            column: 2
          },
          end: {
            line: 467,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 465
      },
      "44": {
        loc: {
          start: {
            line: 469,
            column: 2
          },
          end: {
            line: 471,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 469,
            column: 2
          },
          end: {
            line: 471,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 469
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0],
      "6": [0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0],
      "20": [0],
      "21": [0],
      "22": [0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0af500f361a429cdec7d6a457dcb03b15c915736"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_r896t9nnj = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_r896t9nnj();
import { Alert } from 'react-native';
import env from "../config/environment";
var ERROR_MESSAGES = (cov_r896t9nnj().s[0]++, {
  AUTH_INVALID_CREDENTIALS: {
    message: 'Invalid email or password provided',
    userMessage: 'Invalid email or password. Please check your credentials and try again.',
    severity: 'medium'
  },
  AUTH_USER_NOT_FOUND: {
    message: 'User account not found',
    userMessage: 'No account found with this email address. Please check your email or sign up.',
    severity: 'medium'
  },
  AUTH_EMAIL_NOT_VERIFIED: {
    message: 'Email address not verified',
    userMessage: 'Please verify your email address before signing in. Check your inbox for a verification link.',
    severity: 'medium'
  },
  AUTH_PASSWORD_TOO_WEAK: {
    message: 'Password does not meet security requirements',
    userMessage: 'Password must be at least 6 characters long and contain letters and numbers.',
    severity: 'low'
  },
  AUTH_SESSION_EXPIRED: {
    message: 'User session has expired',
    userMessage: 'Your session has expired. Please sign in again.',
    severity: 'medium'
  },
  AUTH_SIGNUP_FAILED: {
    message: 'Account creation failed',
    userMessage: 'Unable to create your account. Please try again or contact support.',
    severity: 'high'
  },
  AUTH_RESET_PASSWORD_FAILED: {
    message: 'Password reset failed',
    userMessage: 'Unable to send password reset email. Please try again or contact support.',
    severity: 'medium'
  },
  NETWORK_CONNECTION_FAILED: {
    message: 'Network connection failed',
    userMessage: 'Unable to connect to the internet. Please check your connection and try again.',
    severity: 'high'
  },
  NETWORK_TIMEOUT: {
    message: 'Network request timed out',
    userMessage: 'The request is taking too long. Please check your connection and try again.',
    severity: 'medium'
  },
  NETWORK_SERVER_ERROR: {
    message: 'Server error occurred',
    userMessage: 'Something went wrong on our end. Please try again in a few moments.',
    severity: 'high'
  },
  NETWORK_RATE_LIMITED: {
    message: 'Too many requests made',
    userMessage: 'You\'re making requests too quickly. Please wait a moment and try again.',
    severity: 'medium'
  },
  DB_CONNECTION_FAILED: {
    message: 'Database connection failed',
    userMessage: 'Unable to connect to our servers. Please try again later.',
    severity: 'critical'
  },
  DB_QUERY_FAILED: {
    message: 'Database query failed',
    userMessage: 'Unable to retrieve your data. Please try again.',
    severity: 'high'
  },
  DB_RECORD_NOT_FOUND: {
    message: 'Requested record not found',
    userMessage: 'The requested information could not be found.',
    severity: 'medium'
  },
  DB_PERMISSION_DENIED: {
    message: 'Database permission denied',
    userMessage: 'You don\'t have permission to access this information.',
    severity: 'medium'
  },
  DB_CONSTRAINT_VIOLATION: {
    message: 'Database constraint violation',
    userMessage: 'The data you entered conflicts with existing information. Please check and try again.',
    severity: 'medium'
  },
  UPLOAD_FILE_TOO_LARGE: {
    message: 'File size exceeds limit',
    userMessage: 'The file you selected is too large. Please choose a smaller file.',
    severity: 'low'
  },
  UPLOAD_INVALID_FILE_TYPE: {
    message: 'Invalid file type uploaded',
    userMessage: 'This file type is not supported. Please choose a different file.',
    severity: 'low'
  },
  UPLOAD_FAILED: {
    message: 'File upload failed',
    userMessage: 'Unable to upload your file. Please try again.',
    severity: 'medium'
  },
  UPLOAD_QUOTA_EXCEEDED: {
    message: 'Upload quota exceeded',
    userMessage: 'You\'ve reached your upload limit. Please upgrade your plan or delete some files.',
    severity: 'medium'
  },
  PAYMENT_CARD_DECLINED: {
    message: 'Payment card declined',
    userMessage: 'Your card was declined. Please check your card details or try a different payment method.',
    severity: 'medium'
  },
  PAYMENT_INSUFFICIENT_FUNDS: {
    message: 'Insufficient funds',
    userMessage: 'Your card has insufficient funds. Please try a different payment method.',
    severity: 'medium'
  },
  PAYMENT_PROCESSING_ERROR: {
    message: 'Payment processing error',
    userMessage: 'Unable to process your payment. Please try again or contact support.',
    severity: 'high'
  },
  PAYMENT_SUBSCRIPTION_FAILED: {
    message: 'Subscription creation failed',
    userMessage: 'Unable to set up your subscription. Please try again or contact support.',
    severity: 'high'
  },
  VALIDATION_INVALID_INPUT: {
    message: 'Invalid input provided',
    userMessage: 'Please check your input and try again.',
    severity: 'low'
  },
  VALIDATION_REQUIRED_FIELD: {
    message: 'Required field missing',
    userMessage: 'Please fill in all required fields.',
    severity: 'low'
  },
  VALIDATION_FORMAT_ERROR: {
    message: 'Input format error',
    userMessage: 'Please check the format of your input and try again.',
    severity: 'low'
  },
  UNKNOWN_ERROR: {
    message: 'Unknown error occurred',
    userMessage: 'Something unexpected happened. Please try again or contact support.',
    severity: 'high'
  },
  FEATURE_NOT_AVAILABLE: {
    message: 'Feature not available',
    userMessage: 'This feature is not available in your current plan. Please upgrade to access it.',
    severity: 'low'
  },
  MAINTENANCE_MODE: {
    message: 'Application in maintenance mode',
    userMessage: 'The app is currently under maintenance. Please try again later.',
    severity: 'high'
  },
  PERMISSION_DENIED: {
    message: 'Permission denied',
    userMessage: 'You don\'t have permission to perform this action.',
    severity: 'medium'
  }
});
cov_r896t9nnj().s[1]++;
export var createAppError = function createAppError(code, context, customMessage) {
  cov_r896t9nnj().f[0]++;
  var errorInfo = (cov_r896t9nnj().s[2]++, ERROR_MESSAGES[code]);
  cov_r896t9nnj().s[3]++;
  return {
    code: code,
    message: errorInfo.message,
    userMessage: (cov_r896t9nnj().b[0][0]++, customMessage) || (cov_r896t9nnj().b[0][1]++, errorInfo.userMessage),
    severity: errorInfo.severity,
    context: context,
    timestamp: new Date()
  };
};
cov_r896t9nnj().s[4]++;
export var logError = function logError(error, context) {
  cov_r896t9nnj().f[1]++;
  var errorData = (cov_r896t9nnj().s[5]++, Object.assign({
    timestamp: new Date().toISOString(),
    environment: env.getEnvironment(),
    context: context
  }, error instanceof Error ? (cov_r896t9nnj().b[1][0]++, {
    name: error.name,
    message: error.message,
    stack: error.stack
  }) : (cov_r896t9nnj().b[1][1]++, error)));
  cov_r896t9nnj().s[6]++;
  if (env.get('DEBUG_MODE')) {
    cov_r896t9nnj().b[2][0]++;
    cov_r896t9nnj().s[7]++;
    console.error('🚨 Error:', errorData);
  } else {
    cov_r896t9nnj().b[2][1]++;
  }
  cov_r896t9nnj().s[8]++;
  if ((cov_r896t9nnj().b[4][0]++, env.isFeatureEnabled('CRASH_REPORTING')) && (cov_r896t9nnj().b[4][1]++, env.getEnvironment() === 'production')) {
    cov_r896t9nnj().b[3][0]++;
  } else {
    cov_r896t9nnj().b[3][1]++;
  }
};
cov_r896t9nnj().s[9]++;
export var handleError = function handleError(error) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_r896t9nnj().b[5][0]++, {});
  cov_r896t9nnj().f[2]++;
  var _ref = (cov_r896t9nnj().s[10]++, options),
    _ref$showAlert = _ref.showAlert,
    showAlert = _ref$showAlert === void 0 ? (cov_r896t9nnj().b[6][0]++, true) : _ref$showAlert,
    customTitle = _ref.customTitle,
    customMessage = _ref.customMessage,
    onDismiss = _ref.onDismiss;
  var appError;
  cov_r896t9nnj().s[11]++;
  if (error instanceof Error) {
    cov_r896t9nnj().b[7][0]++;
    cov_r896t9nnj().s[12]++;
    if ((cov_r896t9nnj().b[9][0]++, error.message.includes('network')) || (cov_r896t9nnj().b[9][1]++, error.message.includes('fetch'))) {
      cov_r896t9nnj().b[8][0]++;
      cov_r896t9nnj().s[13]++;
      appError = createAppError('NETWORK_CONNECTION_FAILED');
    } else {
      cov_r896t9nnj().b[8][1]++;
      cov_r896t9nnj().s[14]++;
      if (error.message.includes('timeout')) {
        cov_r896t9nnj().b[10][0]++;
        cov_r896t9nnj().s[15]++;
        appError = createAppError('NETWORK_TIMEOUT');
      } else {
        cov_r896t9nnj().b[10][1]++;
        cov_r896t9nnj().s[16]++;
        if ((cov_r896t9nnj().b[12][0]++, error.message.includes('auth')) || (cov_r896t9nnj().b[12][1]++, error.message.includes('unauthorized'))) {
          cov_r896t9nnj().b[11][0]++;
          cov_r896t9nnj().s[17]++;
          appError = createAppError('AUTH_SESSION_EXPIRED');
        } else {
          cov_r896t9nnj().b[11][1]++;
          cov_r896t9nnj().s[18]++;
          appError = createAppError('UNKNOWN_ERROR', {
            originalError: error.message
          });
        }
      }
    }
  } else {
    cov_r896t9nnj().b[7][1]++;
    cov_r896t9nnj().s[19]++;
    if ((cov_r896t9nnj().b[14][0]++, typeof error === 'object') && (cov_r896t9nnj().b[14][1]++, error !== null) && (cov_r896t9nnj().b[14][2]++, 'code' in error)) {
      cov_r896t9nnj().b[13][0]++;
      cov_r896t9nnj().s[20]++;
      appError = error;
    } else {
      cov_r896t9nnj().b[13][1]++;
      cov_r896t9nnj().s[21]++;
      appError = createAppError('UNKNOWN_ERROR', {
        originalError: String(error)
      });
    }
  }
  cov_r896t9nnj().s[22]++;
  logError(appError);
  cov_r896t9nnj().s[23]++;
  if (showAlert) {
    cov_r896t9nnj().b[15][0]++;
    var title = (cov_r896t9nnj().s[24]++, (cov_r896t9nnj().b[16][0]++, customTitle) || (cov_r896t9nnj().b[16][1]++, appError.severity === 'critical' ? (cov_r896t9nnj().b[17][0]++, 'Critical Error') : (cov_r896t9nnj().b[17][1]++, 'Error')));
    var message = (cov_r896t9nnj().s[25]++, (cov_r896t9nnj().b[18][0]++, customMessage) || (cov_r896t9nnj().b[18][1]++, appError.userMessage));
    cov_r896t9nnj().s[26]++;
    Alert.alert(title, message, [{
      text: 'OK',
      onPress: onDismiss
    }]);
  } else {
    cov_r896t9nnj().b[15][1]++;
  }
  cov_r896t9nnj().s[27]++;
  return appError;
};
cov_r896t9nnj().s[28]++;
export var withRetry = function () {
  var _ref2 = _asyncToGenerator(function* (operation) {
    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_r896t9nnj().b[19][0]++, {});
    cov_r896t9nnj().f[3]++;
    var _ref3 = (cov_r896t9nnj().s[29]++, options),
      _ref3$maxAttempts = _ref3.maxAttempts,
      maxAttempts = _ref3$maxAttempts === void 0 ? (cov_r896t9nnj().b[20][0]++, 3) : _ref3$maxAttempts,
      _ref3$delay = _ref3.delay,
      delay = _ref3$delay === void 0 ? (cov_r896t9nnj().b[21][0]++, 1000) : _ref3$delay,
      _ref3$backoff = _ref3.backoff,
      backoff = _ref3$backoff === void 0 ? (cov_r896t9nnj().b[22][0]++, true) : _ref3$backoff,
      onRetry = _ref3.onRetry;
    var lastError;
    cov_r896t9nnj().s[30]++;
    var _loop = function* _loop() {
        cov_r896t9nnj().s[32]++;
        try {
          cov_r896t9nnj().s[33]++;
          return {
            v: yield operation()
          };
        } catch (error) {
          cov_r896t9nnj().s[34]++;
          lastError = error instanceof Error ? (cov_r896t9nnj().b[23][0]++, error) : (cov_r896t9nnj().b[23][1]++, new Error(String(error)));
          cov_r896t9nnj().s[35]++;
          if (attempt === maxAttempts) {
            cov_r896t9nnj().b[24][0]++;
            cov_r896t9nnj().s[36]++;
            throw lastError;
          } else {
            cov_r896t9nnj().b[24][1]++;
          }
          cov_r896t9nnj().s[37]++;
          if (onRetry) {
            cov_r896t9nnj().b[25][0]++;
            cov_r896t9nnj().s[38]++;
            onRetry(attempt, lastError);
          } else {
            cov_r896t9nnj().b[25][1]++;
          }
          var waitTime = (cov_r896t9nnj().s[39]++, backoff ? (cov_r896t9nnj().b[26][0]++, delay * Math.pow(2, attempt - 1)) : (cov_r896t9nnj().b[26][1]++, delay));
          cov_r896t9nnj().s[40]++;
          yield new Promise(function (resolve) {
            cov_r896t9nnj().f[4]++;
            cov_r896t9nnj().s[41]++;
            return setTimeout(resolve, waitTime);
          });
        }
      },
      _ret;
    for (var attempt = (cov_r896t9nnj().s[31]++, 1); attempt <= maxAttempts; attempt++) {
      _ret = yield* _loop();
      if (_ret) return _ret.v;
    }
    cov_r896t9nnj().s[42]++;
    throw lastError;
  });
  return function withRetry(_x) {
    return _ref2.apply(this, arguments);
  };
}();
cov_r896t9nnj().s[43]++;
export var safeAsync = function () {
  var _ref4 = _asyncToGenerator(function* (operation, fallback) {
    cov_r896t9nnj().f[5]++;
    cov_r896t9nnj().s[44]++;
    try {
      var data = (cov_r896t9nnj().s[45]++, yield operation());
      cov_r896t9nnj().s[46]++;
      return {
        data: data
      };
    } catch (error) {
      var appError = (cov_r896t9nnj().s[47]++, handleError(error, {
        showAlert: false
      }));
      cov_r896t9nnj().s[48]++;
      return {
        error: appError,
        data: fallback
      };
    }
  });
  return function safeAsync(_x2, _x3) {
    return _ref4.apply(this, arguments);
  };
}();
cov_r896t9nnj().s[49]++;
export var handleNetworkError = function handleNetworkError(error) {
  var _error$message, _error$message2;
  cov_r896t9nnj().f[6]++;
  cov_r896t9nnj().s[50]++;
  if (!navigator.onLine) {
    cov_r896t9nnj().b[27][0]++;
    cov_r896t9nnj().s[51]++;
    return createAppError('NETWORK_CONNECTION_FAILED');
  } else {
    cov_r896t9nnj().b[27][1]++;
  }
  cov_r896t9nnj().s[52]++;
  if ((cov_r896t9nnj().b[29][0]++, error.code === 'NETWORK_ERROR') || (cov_r896t9nnj().b[29][1]++, (_error$message = error.message) != null && _error$message.includes('Network Error'))) {
    cov_r896t9nnj().b[28][0]++;
    cov_r896t9nnj().s[53]++;
    return createAppError('NETWORK_CONNECTION_FAILED');
  } else {
    cov_r896t9nnj().b[28][1]++;
  }
  cov_r896t9nnj().s[54]++;
  if ((cov_r896t9nnj().b[31][0]++, error.code === 'TIMEOUT_ERROR') || (cov_r896t9nnj().b[31][1]++, (_error$message2 = error.message) != null && _error$message2.includes('timeout'))) {
    cov_r896t9nnj().b[30][0]++;
    cov_r896t9nnj().s[55]++;
    return createAppError('NETWORK_TIMEOUT');
  } else {
    cov_r896t9nnj().b[30][1]++;
  }
  cov_r896t9nnj().s[56]++;
  if (error.status >= 500) {
    cov_r896t9nnj().b[32][0]++;
    cov_r896t9nnj().s[57]++;
    return createAppError('NETWORK_SERVER_ERROR');
  } else {
    cov_r896t9nnj().b[32][1]++;
  }
  cov_r896t9nnj().s[58]++;
  if (error.status === 429) {
    cov_r896t9nnj().b[33][0]++;
    cov_r896t9nnj().s[59]++;
    return createAppError('NETWORK_RATE_LIMITED');
  } else {
    cov_r896t9nnj().b[33][1]++;
  }
  cov_r896t9nnj().s[60]++;
  return createAppError('UNKNOWN_ERROR', {
    networkError: error
  });
};
cov_r896t9nnj().s[61]++;
export var handleDatabaseError = function handleDatabaseError(error) {
  var _error$message3, _error$message4, _error$message5, _error$message6;
  cov_r896t9nnj().f[7]++;
  cov_r896t9nnj().s[62]++;
  if ((cov_r896t9nnj().b[35][0]++, error.code === 'PGRST116') || (cov_r896t9nnj().b[35][1]++, (_error$message3 = error.message) != null && _error$message3.includes('not found'))) {
    cov_r896t9nnj().b[34][0]++;
    cov_r896t9nnj().s[63]++;
    return createAppError('DB_RECORD_NOT_FOUND');
  } else {
    cov_r896t9nnj().b[34][1]++;
  }
  cov_r896t9nnj().s[64]++;
  if ((cov_r896t9nnj().b[37][0]++, error.code === 'PGRST301') || (cov_r896t9nnj().b[37][1]++, (_error$message4 = error.message) != null && _error$message4.includes('permission denied'))) {
    cov_r896t9nnj().b[36][0]++;
    cov_r896t9nnj().s[65]++;
    return createAppError('DB_PERMISSION_DENIED');
  } else {
    cov_r896t9nnj().b[36][1]++;
  }
  cov_r896t9nnj().s[66]++;
  if ((_error$message5 = error.message) != null && _error$message5.includes('connection')) {
    cov_r896t9nnj().b[38][0]++;
    cov_r896t9nnj().s[67]++;
    return createAppError('DB_CONNECTION_FAILED');
  } else {
    cov_r896t9nnj().b[38][1]++;
  }
  cov_r896t9nnj().s[68]++;
  if ((_error$message6 = error.message) != null && _error$message6.includes('constraint')) {
    cov_r896t9nnj().b[39][0]++;
    cov_r896t9nnj().s[69]++;
    return createAppError('DB_CONSTRAINT_VIOLATION');
  } else {
    cov_r896t9nnj().b[39][1]++;
  }
  cov_r896t9nnj().s[70]++;
  return createAppError('DB_QUERY_FAILED', {
    databaseError: error
  });
};
cov_r896t9nnj().s[71]++;
export var handleAuthError = function handleAuthError(error) {
  var _error$message7, _error$message8, _error$message9, _error$message0, _error$message1;
  cov_r896t9nnj().f[8]++;
  cov_r896t9nnj().s[72]++;
  if ((_error$message7 = error.message) != null && _error$message7.includes('Invalid login credentials')) {
    cov_r896t9nnj().b[40][0]++;
    cov_r896t9nnj().s[73]++;
    return createAppError('AUTH_INVALID_CREDENTIALS');
  } else {
    cov_r896t9nnj().b[40][1]++;
  }
  cov_r896t9nnj().s[74]++;
  if ((_error$message8 = error.message) != null && _error$message8.includes('Email not confirmed')) {
    cov_r896t9nnj().b[41][0]++;
    cov_r896t9nnj().s[75]++;
    return createAppError('AUTH_EMAIL_NOT_VERIFIED');
  } else {
    cov_r896t9nnj().b[41][1]++;
  }
  cov_r896t9nnj().s[76]++;
  if ((_error$message9 = error.message) != null && _error$message9.includes('Password should be at least')) {
    cov_r896t9nnj().b[42][0]++;
    cov_r896t9nnj().s[77]++;
    return createAppError('AUTH_PASSWORD_TOO_WEAK');
  } else {
    cov_r896t9nnj().b[42][1]++;
  }
  cov_r896t9nnj().s[78]++;
  if ((_error$message0 = error.message) != null && _error$message0.includes('JWT expired')) {
    cov_r896t9nnj().b[43][0]++;
    cov_r896t9nnj().s[79]++;
    return createAppError('AUTH_SESSION_EXPIRED');
  } else {
    cov_r896t9nnj().b[43][1]++;
  }
  cov_r896t9nnj().s[80]++;
  if ((_error$message1 = error.message) != null && _error$message1.includes('User not found')) {
    cov_r896t9nnj().b[44][0]++;
    cov_r896t9nnj().s[81]++;
    return createAppError('AUTH_USER_NOT_FOUND');
  } else {
    cov_r896t9nnj().b[44][1]++;
  }
  cov_r896t9nnj().s[82]++;
  return createAppError('AUTH_SIGNUP_FAILED', {
    authError: error
  });
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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