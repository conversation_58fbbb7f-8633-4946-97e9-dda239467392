1b059f08d25ac50b864c941f973873a1
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _AnimatedInterpolation = _interopRequireDefault(require("./AnimatedInterpolation"));
var _AnimatedWithChildren = _interopRequireDefault(require("./AnimatedWithChildren"));
var AnimatedModulo = function (_AnimatedWithChildren2) {
  function AnimatedModulo(a, modulus) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedModulo);
    _this = _callSuper(this, AnimatedModulo);
    _this._a = a;
    _this._modulus = modulus;
    return _this;
  }
  (0, _inherits2.default)(AnimatedModulo, _AnimatedWithChildren2);
  return (0, _createClass2.default)(AnimatedModulo, [{
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      this._a.__makeNative(platformConfig);
      _superPropGet(AnimatedModulo, "__makeNative", this, 3)([platformConfig]);
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      return (this._a.__getValue() % this._modulus + this._modulus) % this._modulus;
    }
  }, {
    key: "interpolate",
    value: function interpolate(config) {
      return new _AnimatedInterpolation.default(this, config);
    }
  }, {
    key: "__attach",
    value: function __attach() {
      this._a.__addChild(this);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      this._a.__removeChild(this);
      _superPropGet(AnimatedModulo, "__detach", this, 3)([]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      return {
        type: 'modulus',
        input: this._a.__getNativeTag(),
        modulus: this._modulus
      };
    }
  }]);
}(_AnimatedWithChildren.default);
var _default = exports.default = AnimatedModulo;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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