{"version": 3, "names": ["exports", "__esModule", "default", "clamp", "min", "value", "max", "_default", "module"], "sources": ["clamp.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * \n */\n\n'use strict';\n\nexports.__esModule = true;\nexports.default = void 0;\nfunction clamp(min, value, max) {\n  if (value < min) {\n    return min;\n  }\n  if (value > max) {\n    return max;\n  }\n  return value;\n}\nvar _default = exports.default = clamp;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,SAASC,KAAKA,CAACC,GAAG,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAC9B,IAAID,KAAK,GAAGD,GAAG,EAAE;IACf,OAAOA,GAAG;EACZ;EACA,IAAIC,KAAK,GAAGC,GAAG,EAAE;IACf,OAAOA,GAAG;EACZ;EACA,OAAOD,KAAK;AACd;AACA,IAAIE,QAAQ,GAAGP,OAAO,CAACE,OAAO,GAAGC,KAAK;AACtCK,MAAM,CAACR,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}