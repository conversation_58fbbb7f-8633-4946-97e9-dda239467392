0f946cafcfb805e78911770f089707d2
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = createCSSStyleSheet;
var _canUseDom = _interopRequireDefault(require("../../../modules/canUseDom"));
function createCSSStyleSheet(id, rootNode, textContent) {
  if (_canUseDom.default) {
    var root = rootNode != null ? rootNode : document;
    var element = root.getElementById(id);
    if (element == null) {
      element = document.createElement('style');
      element.setAttribute('id', id);
      if (typeof textContent === 'string') {
        element.appendChild(document.createTextNode(textContent));
      }
      if (root instanceof ShadowRoot) {
        root.insertBefore(element, root.firstChild);
      } else {
        var head = root.head;
        if (head) {
          head.insertBefore(element, head.firstChild);
        }
      }
    }
    return element.sheet;
  } else {
    return null;
  }
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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