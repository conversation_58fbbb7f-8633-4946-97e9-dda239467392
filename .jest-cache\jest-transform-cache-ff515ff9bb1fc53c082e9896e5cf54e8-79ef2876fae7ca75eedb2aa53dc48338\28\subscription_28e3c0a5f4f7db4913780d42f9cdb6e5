c8bf8d0eb10b205ab799bd1b4005fc75
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1o8zk686zb() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\settings\\subscription.tsx";
  var hash = "60887f75d91d5202130905d673fac464db19fb68";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\settings\\subscription.tsx",
    statementMap: {
      "0": {
        start: {
          line: 38,
          column: 15
        },
        end: {
          line: 47,
          column: 1
        }
      },
      "1": {
        start: {
          line: 60,
          column: 22
        },
        end: {
          line: 112,
          column: 1
        }
      },
      "2": {
        start: {
          line: 114,
          column: 28
        },
        end: {
          line: 120,
          column: 1
        }
      },
      "3": {
        start: {
          line: 123,
          column: 19
        },
        end: {
          line: 123,
          column: 28
        }
      },
      "4": {
        start: {
          line: 124,
          column: 42
        },
        end: {
          line: 124,
          column: 76
        }
      },
      "5": {
        start: {
          line: 125,
          column: 44
        },
        end: {
          line: 125,
          column: 85
        }
      },
      "6": {
        start: {
          line: 126,
          column: 36
        },
        end: {
          line: 126,
          column: 68
        }
      },
      "7": {
        start: {
          line: 127,
          column: 50
        },
        end: {
          line: 127,
          column: 85
        }
      },
      "8": {
        start: {
          line: 128,
          column: 32
        },
        end: {
          line: 128,
          column: 46
        }
      },
      "9": {
        start: {
          line: 129,
          column: 36
        },
        end: {
          line: 129,
          column: 51
        }
      },
      "10": {
        start: {
          line: 131,
          column: 2
        },
        end: {
          line: 133,
          column: 9
        }
      },
      "11": {
        start: {
          line: 132,
          column: 4
        },
        end: {
          line: 132,
          column: 27
        }
      },
      "12": {
        start: {
          line: 135,
          column: 31
        },
        end: {
          line: 153,
          column: 3
        }
      },
      "13": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 136,
          column: 26
        }
      },
      "14": {
        start: {
          line: 136,
          column: 19
        },
        end: {
          line: 136,
          column: 26
        }
      },
      "15": {
        start: {
          line: 138,
          column: 4
        },
        end: {
          line: 152,
          column: 5
        }
      },
      "16": {
        start: {
          line: 139,
          column: 6
        },
        end: {
          line: 139,
          column: 23
        }
      },
      "17": {
        start: {
          line: 140,
          column: 52
        },
        end: {
          line: 143,
          column: 8
        }
      },
      "18": {
        start: {
          line: 145,
          column: 6
        },
        end: {
          line: 145,
          column: 38
        }
      },
      "19": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 146,
          column: 44
        }
      },
      "20": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 148,
          column: 64
        }
      },
      "21": {
        start: {
          line: 149,
          column: 6
        },
        end: {
          line: 149,
          column: 70
        }
      },
      "22": {
        start: {
          line: 151,
          column: 6
        },
        end: {
          line: 151,
          column: 24
        }
      },
      "23": {
        start: {
          line: 155,
          column: 24
        },
        end: {
          line: 186,
          column: 3
        }
      },
      "24": {
        start: {
          line: 156,
          column: 4
        },
        end: {
          line: 156,
          column: 26
        }
      },
      "25": {
        start: {
          line: 156,
          column: 19
        },
        end: {
          line: 156,
          column: 26
        }
      },
      "26": {
        start: {
          line: 158,
          column: 4
        },
        end: {
          line: 161,
          column: 5
        }
      },
      "27": {
        start: {
          line: 159,
          column: 6
        },
        end: {
          line: 159,
          column: 78
        }
      },
      "28": {
        start: {
          line: 160,
          column: 6
        },
        end: {
          line: 160,
          column: 13
        }
      },
      "29": {
        start: {
          line: 163,
          column: 17
        },
        end: {
          line: 163,
          column: 49
        }
      },
      "30": {
        start: {
          line: 163,
          column: 33
        },
        end: {
          line: 163,
          column: 48
        }
      },
      "31": {
        start: {
          line: 164,
          column: 4
        },
        end: {
          line: 164,
          column: 22
        }
      },
      "32": {
        start: {
          line: 164,
          column: 15
        },
        end: {
          line: 164,
          column: 22
        }
      },
      "33": {
        start: {
          line: 166,
          column: 4
        },
        end: {
          line: 185,
          column: 5
        }
      },
      "34": {
        start: {
          line: 168,
          column: 6
        },
        end: {
          line: 178,
          column: 8
        }
      },
      "35": {
        start: {
          line: 175,
          column: 27
        },
        end: {
          line: 175,
          column: 51
        }
      },
      "36": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 184,
          column: 9
        }
      },
      "37": {
        start: {
          line: 188,
          column: 27
        },
        end: {
          line: 207,
          column: 3
        }
      },
      "38": {
        start: {
          line: 189,
          column: 4
        },
        end: {
          line: 189,
          column: 34
        }
      },
      "39": {
        start: {
          line: 189,
          column: 27
        },
        end: {
          line: 189,
          column: 34
        }
      },
      "40": {
        start: {
          line: 191,
          column: 4
        },
        end: {
          line: 206,
          column: 5
        }
      },
      "41": {
        start: {
          line: 192,
          column: 6
        },
        end: {
          line: 192,
          column: 25
        }
      },
      "42": {
        start: {
          line: 193,
          column: 22
        },
        end: {
          line: 193,
          column: 94
        }
      },
      "43": {
        start: {
          line: 195,
          column: 6
        },
        end: {
          line: 200,
          column: 7
        }
      },
      "44": {
        start: {
          line: 196,
          column: 8
        },
        end: {
          line: 196,
          column: 70
        }
      },
      "45": {
        start: {
          line: 197,
          column: 8
        },
        end: {
          line: 197,
          column: 31
        }
      },
      "46": {
        start: {
          line: 199,
          column: 8
        },
        end: {
          line: 199,
          column: 62
        }
      },
      "47": {
        start: {
          line: 202,
          column: 6
        },
        end: {
          line: 202,
          column: 53
        }
      },
      "48": {
        start: {
          line: 203,
          column: 6
        },
        end: {
          line: 203,
          column: 60
        }
      },
      "49": {
        start: {
          line: 205,
          column: 6
        },
        end: {
          line: 205,
          column: 26
        }
      },
      "50": {
        start: {
          line: 209,
          column: 35
        },
        end: {
          line: 237,
          column: 3
        }
      },
      "51": {
        start: {
          line: 210,
          column: 4
        },
        end: {
          line: 210,
          column: 34
        }
      },
      "52": {
        start: {
          line: 210,
          column: 27
        },
        end: {
          line: 210,
          column: 34
        }
      },
      "53": {
        start: {
          line: 212,
          column: 4
        },
        end: {
          line: 236,
          column: 6
        }
      },
      "54": {
        start: {
          line: 221,
          column: 12
        },
        end: {
          line: 232,
          column: 13
        }
      },
      "55": {
        start: {
          line: 222,
          column: 30
        },
        end: {
          line: 222,
          column: 97
        }
      },
      "56": {
        start: {
          line: 223,
          column: 14
        },
        end: {
          line: 228,
          column: 15
        }
      },
      "57": {
        start: {
          line: 224,
          column: 16
        },
        end: {
          line: 224,
          column: 162
        }
      },
      "58": {
        start: {
          line: 225,
          column: 16
        },
        end: {
          line: 225,
          column: 39
        }
      },
      "59": {
        start: {
          line: 227,
          column: 16
        },
        end: {
          line: 227,
          column: 70
        }
      },
      "60": {
        start: {
          line: 230,
          column: 14
        },
        end: {
          line: 230,
          column: 69
        }
      },
      "61": {
        start: {
          line: 231,
          column: 14
        },
        end: {
          line: 231,
          column: 68
        }
      },
      "62": {
        start: {
          line: 239,
          column: 30
        },
        end: {
          line: 241,
          column: 3
        }
      },
      "63": {
        start: {
          line: 240,
          column: 4
        },
        end: {
          line: 240,
          column: 51
        }
      },
      "64": {
        start: {
          line: 243,
          column: 35
        },
        end: {
          line: 245,
          column: 3
        }
      },
      "65": {
        start: {
          line: 244,
          column: 4
        },
        end: {
          line: 244,
          column: 51
        }
      },
      "66": {
        start: {
          line: 247,
          column: 29
        },
        end: {
          line: 249,
          column: 3
        }
      },
      "67": {
        start: {
          line: 248,
          column: 4
        },
        end: {
          line: 248,
          column: 59
        }
      },
      "68": {
        start: {
          line: 251,
          column: 25
        },
        end: {
          line: 259,
          column: 3
        }
      },
      "69": {
        start: {
          line: 252,
          column: 4
        },
        end: {
          line: 257,
          column: 5
        }
      },
      "70": {
        start: {
          line: 253,
          column: 26
        },
        end: {
          line: 253,
          column: 36
        }
      },
      "71": {
        start: {
          line: 254,
          column: 25
        },
        end: {
          line: 254,
          column: 35
        }
      },
      "72": {
        start: {
          line: 255,
          column: 22
        },
        end: {
          line: 255,
          column: 46
        }
      },
      "73": {
        start: {
          line: 256,
          column: 6
        },
        end: {
          line: 256,
          column: 48
        }
      },
      "74": {
        start: {
          line: 258,
          column: 4
        },
        end: {
          line: 258,
          column: 16
        }
      },
      "75": {
        start: {
          line: 261,
          column: 2
        },
        end: {
          line: 272,
          column: 3
        }
      },
      "76": {
        start: {
          line: 262,
          column: 4
        },
        end: {
          line: 271,
          column: 6
        }
      },
      "77": {
        start: {
          line: 274,
          column: 2
        },
        end: {
          line: 478,
          column: 4
        }
      },
      "78": {
        start: {
          line: 283,
          column: 45
        },
        end: {
          line: 283,
          column: 58
        }
      },
      "79": {
        start: {
          line: 298,
          column: 31
        },
        end: {
          line: 298,
          column: 64
        }
      },
      "80": {
        start: {
          line: 320,
          column: 31
        },
        end: {
          line: 320,
          column: 58
        }
      },
      "81": {
        start: {
          line: 334,
          column: 31
        },
        end: {
          line: 334,
          column: 57
        }
      },
      "82": {
        start: {
          line: 352,
          column: 12
        },
        end: {
          line: 419,
          column: 19
        }
      },
      "83": {
        start: {
          line: 401,
          column: 18
        },
        end: {
          line: 404,
          column: 25
        }
      },
      "84": {
        start: {
          line: 411,
          column: 33
        },
        end: {
          line: 411,
          column: 55
        }
      },
      "85": {
        start: {
          line: 481,
          column: 15
        },
        end: {
          line: 801,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "SubscriptionScreen",
        decl: {
          start: {
            line: 122,
            column: 24
          },
          end: {
            line: 122,
            column: 42
          }
        },
        loc: {
          start: {
            line: 122,
            column: 45
          },
          end: {
            line: 479,
            column: 1
          }
        },
        line: 122
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 131,
            column: 12
          },
          end: {
            line: 131,
            column: 13
          }
        },
        loc: {
          start: {
            line: 131,
            column: 18
          },
          end: {
            line: 133,
            column: 3
          }
        },
        line: 131
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 135,
            column: 31
          },
          end: {
            line: 135,
            column: 32
          }
        },
        loc: {
          start: {
            line: 135,
            column: 43
          },
          end: {
            line: 153,
            column: 3
          }
        },
        line: 135
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 155,
            column: 24
          },
          end: {
            line: 155,
            column: 25
          }
        },
        loc: {
          start: {
            line: 155,
            column: 50
          },
          end: {
            line: 186,
            column: 3
          }
        },
        line: 155
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 163,
            column: 28
          },
          end: {
            line: 163,
            column: 29
          }
        },
        loc: {
          start: {
            line: 163,
            column: 33
          },
          end: {
            line: 163,
            column: 48
          }
        },
        line: 163
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 175,
            column: 21
          },
          end: {
            line: 175,
            column: 22
          }
        },
        loc: {
          start: {
            line: 175,
            column: 27
          },
          end: {
            line: 175,
            column: 51
          }
        },
        line: 175
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 188,
            column: 27
          },
          end: {
            line: 188,
            column: 28
          }
        },
        loc: {
          start: {
            line: 188,
            column: 53
          },
          end: {
            line: 207,
            column: 3
          }
        },
        line: 188
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 209,
            column: 35
          },
          end: {
            line: 209,
            column: 36
          }
        },
        loc: {
          start: {
            line: 209,
            column: 47
          },
          end: {
            line: 237,
            column: 3
          }
        },
        line: 209
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 220,
            column: 19
          },
          end: {
            line: 220,
            column: 20
          }
        },
        loc: {
          start: {
            line: 220,
            column: 31
          },
          end: {
            line: 233,
            column: 11
          }
        },
        line: 220
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 239,
            column: 30
          },
          end: {
            line: 239,
            column: 31
          }
        },
        loc: {
          start: {
            line: 239,
            column: 36
          },
          end: {
            line: 241,
            column: 3
          }
        },
        line: 239
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 243,
            column: 35
          },
          end: {
            line: 243,
            column: 36
          }
        },
        loc: {
          start: {
            line: 243,
            column: 41
          },
          end: {
            line: 245,
            column: 3
          }
        },
        line: 243
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 247,
            column: 29
          },
          end: {
            line: 247,
            column: 30
          }
        },
        loc: {
          start: {
            line: 247,
            column: 48
          },
          end: {
            line: 249,
            column: 3
          }
        },
        line: 247
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 251,
            column: 25
          },
          end: {
            line: 251,
            column: 26
          }
        },
        loc: {
          start: {
            line: 251,
            column: 44
          },
          end: {
            line: 259,
            column: 3
          }
        },
        line: 251
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 283,
            column: 39
          },
          end: {
            line: 283,
            column: 40
          }
        },
        loc: {
          start: {
            line: 283,
            column: 45
          },
          end: {
            line: 283,
            column: 58
          }
        },
        line: 283
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 298,
            column: 26
          },
          end: {
            line: 298,
            column: 27
          }
        },
        loc: {
          start: {
            line: 298,
            column: 31
          },
          end: {
            line: 298,
            column: 64
          }
        },
        line: 298
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 320,
            column: 25
          },
          end: {
            line: 320,
            column: 26
          }
        },
        loc: {
          start: {
            line: 320,
            column: 31
          },
          end: {
            line: 320,
            column: 58
          }
        },
        line: 320
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 334,
            column: 25
          },
          end: {
            line: 334,
            column: 26
          }
        },
        loc: {
          start: {
            line: 334,
            column: 31
          },
          end: {
            line: 334,
            column: 57
          }
        },
        line: 334
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 351,
            column: 21
          },
          end: {
            line: 351,
            column: 22
          }
        },
        loc: {
          start: {
            line: 352,
            column: 12
          },
          end: {
            line: 419,
            column: 19
          }
        },
        line: 352
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 400,
            column: 35
          },
          end: {
            line: 400,
            column: 36
          }
        },
        loc: {
          start: {
            line: 401,
            column: 18
          },
          end: {
            line: 404,
            column: 25
          }
        },
        line: 401
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 411,
            column: 27
          },
          end: {
            line: 411,
            column: 28
          }
        },
        loc: {
          start: {
            line: 411,
            column: 33
          },
          end: {
            line: 411,
            column: 55
          }
        },
        line: 411
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 136,
            column: 4
          },
          end: {
            line: 136,
            column: 26
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 4
          },
          end: {
            line: 136,
            column: 26
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 136
      },
      "1": {
        loc: {
          start: {
            line: 156,
            column: 4
          },
          end: {
            line: 156,
            column: 26
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 156,
            column: 4
          },
          end: {
            line: 156,
            column: 26
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 156
      },
      "2": {
        loc: {
          start: {
            line: 158,
            column: 4
          },
          end: {
            line: 161,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 158,
            column: 4
          },
          end: {
            line: 161,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 158
      },
      "3": {
        loc: {
          start: {
            line: 164,
            column: 4
          },
          end: {
            line: 164,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 164,
            column: 4
          },
          end: {
            line: 164,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 164
      },
      "4": {
        loc: {
          start: {
            line: 166,
            column: 4
          },
          end: {
            line: 185,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 166,
            column: 4
          },
          end: {
            line: 185,
            column: 5
          }
        }, {
          start: {
            line: 179,
            column: 11
          },
          end: {
            line: 185,
            column: 5
          }
        }],
        line: 166
      },
      "5": {
        loc: {
          start: {
            line: 189,
            column: 4
          },
          end: {
            line: 189,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 189,
            column: 4
          },
          end: {
            line: 189,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 189
      },
      "6": {
        loc: {
          start: {
            line: 195,
            column: 6
          },
          end: {
            line: 200,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 195,
            column: 6
          },
          end: {
            line: 200,
            column: 7
          }
        }, {
          start: {
            line: 198,
            column: 13
          },
          end: {
            line: 200,
            column: 7
          }
        }],
        line: 195
      },
      "7": {
        loc: {
          start: {
            line: 210,
            column: 4
          },
          end: {
            line: 210,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 4
          },
          end: {
            line: 210,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 210
      },
      "8": {
        loc: {
          start: {
            line: 223,
            column: 14
          },
          end: {
            line: 228,
            column: 15
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 223,
            column: 14
          },
          end: {
            line: 228,
            column: 15
          }
        }, {
          start: {
            line: 226,
            column: 21
          },
          end: {
            line: 228,
            column: 15
          }
        }],
        line: 223
      },
      "9": {
        loc: {
          start: {
            line: 248,
            column: 11
          },
          end: {
            line: 248,
            column: 58
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 248,
            column: 40
          },
          end: {
            line: 248,
            column: 50
          }
        }, {
          start: {
            line: 248,
            column: 53
          },
          end: {
            line: 248,
            column: 58
          }
        }],
        line: 248
      },
      "10": {
        loc: {
          start: {
            line: 252,
            column: 4
          },
          end: {
            line: 257,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 252,
            column: 4
          },
          end: {
            line: 257,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 252
      },
      "11": {
        loc: {
          start: {
            line: 252,
            column: 8
          },
          end: {
            line: 252,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 252,
            column: 8
          },
          end: {
            line: 252,
            column: 34
          }
        }, {
          start: {
            line: 252,
            column: 38
          },
          end: {
            line: 252,
            column: 47
          }
        }],
        line: 252
      },
      "12": {
        loc: {
          start: {
            line: 261,
            column: 2
          },
          end: {
            line: 272,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 261,
            column: 2
          },
          end: {
            line: 272,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 261
      },
      "13": {
        loc: {
          start: {
            line: 318,
            column: 18
          },
          end: {
            line: 318,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 318,
            column: 18
          },
          end: {
            line: 318,
            column: 45
          }
        }, {
          start: {
            line: 318,
            column: 49
          },
          end: {
            line: 318,
            column: 69
          }
        }],
        line: 318
      },
      "14": {
        loc: {
          start: {
            line: 324,
            column: 18
          },
          end: {
            line: 324,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 324,
            column: 18
          },
          end: {
            line: 324,
            column: 45
          }
        }, {
          start: {
            line: 324,
            column: 49
          },
          end: {
            line: 324,
            column: 73
          }
        }],
        line: 324
      },
      "15": {
        loc: {
          start: {
            line: 332,
            column: 18
          },
          end: {
            line: 332,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 332,
            column: 18
          },
          end: {
            line: 332,
            column: 44
          }
        }, {
          start: {
            line: 332,
            column: 48
          },
          end: {
            line: 332,
            column: 68
          }
        }],
        line: 332
      },
      "16": {
        loc: {
          start: {
            line: 338,
            column: 18
          },
          end: {
            line: 338,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 338,
            column: 18
          },
          end: {
            line: 338,
            column: 44
          }
        }, {
          start: {
            line: 338,
            column: 48
          },
          end: {
            line: 338,
            column: 72
          }
        }],
        line: 338
      },
      "17": {
        loc: {
          start: {
            line: 356,
            column: 16
          },
          end: {
            line: 356,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 356,
            column: 16
          },
          end: {
            line: 356,
            column: 28
          }
        }, {
          start: {
            line: 356,
            column: 32
          },
          end: {
            line: 356,
            column: 50
          }
        }],
        line: 356
      },
      "18": {
        loc: {
          start: {
            line: 357,
            column: 16
          },
          end: {
            line: 357,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 357,
            column: 16
          },
          end: {
            line: 357,
            column: 52
          }
        }, {
          start: {
            line: 357,
            column: 56
          },
          end: {
            line: 357,
            column: 78
          }
        }],
        line: 357
      },
      "19": {
        loc: {
          start: {
            line: 360,
            column: 15
          },
          end: {
            line: 364,
            column: 15
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 360,
            column: 15
          },
          end: {
            line: 360,
            column: 27
          }
        }, {
          start: {
            line: 361,
            column: 16
          },
          end: {
            line: 363,
            column: 23
          }
        }],
        line: 360
      },
      "20": {
        loc: {
          start: {
            line: 373,
            column: 21
          },
          end: {
            line: 384,
            column: 21
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 374,
            column: 22
          },
          end: {
            line: 374,
            column: 64
          }
        }, {
          start: {
            line: 376,
            column: 22
          },
          end: {
            line: 383,
            column: 25
          }
        }],
        line: 373
      },
      "21": {
        loc: {
          start: {
            line: 381,
            column: 28
          },
          end: {
            line: 381,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 381,
            column: 57
          },
          end: {
            line: 381,
            column: 63
          }
        }, {
          start: {
            line: 381,
            column: 66
          },
          end: {
            line: 381,
            column: 73
          }
        }],
        line: 381
      },
      "22": {
        loc: {
          start: {
            line: 386,
            column: 19
          },
          end: {
            line: 390,
            column: 19
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 386,
            column: 19
          },
          end: {
            line: 386,
            column: 45
          }
        }, {
          start: {
            line: 387,
            column: 20
          },
          end: {
            line: 389,
            column: 27
          }
        }],
        line: 386
      },
      "23": {
        loc: {
          start: {
            line: 392,
            column: 17
          },
          end: {
            line: 396,
            column: 17
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 392,
            column: 17
          },
          end: {
            line: 392,
            column: 53
          }
        }, {
          start: {
            line: 393,
            column: 18
          },
          end: {
            line: 395,
            column: 25
          }
        }],
        line: 392
      },
      "24": {
        loc: {
          start: {
            line: 408,
            column: 15
          },
          end: {
            line: 418,
            column: 15
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 408,
            column: 15
          },
          end: {
            line: 408,
            column: 51
          }
        }, {
          start: {
            line: 409,
            column: 16
          },
          end: {
            line: 417,
            column: 18
          }
        }],
        line: 408
      },
      "25": {
        loc: {
          start: {
            line: 410,
            column: 25
          },
          end: {
            line: 410,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 410,
            column: 44
          },
          end: {
            line: 410,
            column: 55
          }
        }, {
          start: {
            line: 410,
            column: 58
          },
          end: {
            line: 410,
            column: 67
          }
        }],
        line: 410
      },
      "26": {
        loc: {
          start: {
            line: 414,
            column: 20
          },
          end: {
            line: 414,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 414,
            column: 20
          },
          end: {
            line: 414,
            column: 32
          }
        }, {
          start: {
            line: 414,
            column: 36
          },
          end: {
            line: 414,
            column: 56
          }
        }],
        line: 414
      },
      "27": {
        loc: {
          start: {
            line: 416,
            column: 27
          },
          end: {
            line: 416,
            column: 63
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 416,
            column: 42
          },
          end: {
            line: 416,
            column: 51
          }
        }, {
          start: {
            line: 416,
            column: 54
          },
          end: {
            line: 416,
            column: 63
          }
        }],
        line: 416
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "60887f75d91d5202130905d673fac464db19fb68"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1o8zk686zb = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1o8zk686zb();
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, SafeAreaView, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import Card from "../../components/ui/Card";
import Button from "../../components/ui/Button";
import { ArrowLeft, Crown, Check, CreditCard, Star, Shield, Download } from 'lucide-react-native';
import { paymentService } from "../../services/paymentService";
import { useAuth } from "../../contexts/AuthContext";
import ErrorBoundary from "../../components/ui/ErrorBoundary";
import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
var colors = (cov_1o8zk686zb().s[0]++, {
  primary: '#23ba16',
  yellow: '#ffe600',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  premium: '#8b5cf6',
  gold: '#f59e0b'
});
var plans = (cov_1o8zk686zb().s[1]++, [{
  id: 'free',
  name: 'Free',
  price: 0,
  period: 'month',
  features: ['Basic video analysis', '5 training sessions per month', 'Basic progress tracking', 'Community access', 'Standard support'],
  color: colors.gray,
  icon: Shield
}, {
  id: 'premium',
  name: 'Premium',
  price: 9.99,
  period: 'month',
  popular: true,
  features: ['Advanced AI video analysis', 'Unlimited training sessions', 'Detailed progress analytics', 'Personalized coaching tips', 'Priority support', 'Offline mode', 'Export progress reports'],
  color: colors.premium,
  icon: Star
}, {
  id: 'pro',
  name: 'Pro',
  price: 19.99,
  period: 'month',
  features: ['Everything in Premium', 'Live coaching sessions', 'Advanced match analysis', 'Custom training programs', 'Tournament tracking', 'Coach collaboration tools', 'API access', 'White-label options'],
  color: colors.gold,
  icon: Crown
}]);
var currentSubscription = (cov_1o8zk686zb().s[2]++, {
  plan: 'premium',
  status: 'active',
  nextBilling: '2024-02-15',
  paymentMethod: '**** 4242',
  amount: 9.99
});
export default function SubscriptionScreen() {
  var _plans$find;
  cov_1o8zk686zb().f[0]++;
  var _ref = (cov_1o8zk686zb().s[3]++, useAuth()),
    user = _ref.user;
  var _ref2 = (cov_1o8zk686zb().s[4]++, useState(currentSubscription.plan)),
    _ref3 = _slicedToArray(_ref2, 2),
    selectedPlan = _ref3[0],
    setSelectedPlan = _ref3[1];
  var _ref4 = (cov_1o8zk686zb().s[5]++, useState('monthly')),
    _ref5 = _slicedToArray(_ref4, 2),
    billingPeriod = _ref5[0],
    setBillingPeriod = _ref5[1];
  var _ref6 = (cov_1o8zk686zb().s[6]++, useState([])),
    _ref7 = _slicedToArray(_ref6, 2),
    realPlans = _ref7[0],
    setRealPlans = _ref7[1];
  var _ref8 = (cov_1o8zk686zb().s[7]++, useState(null)),
    _ref9 = _slicedToArray(_ref8, 2),
    realSubscription = _ref9[0],
    setRealSubscription = _ref9[1];
  var _ref0 = (cov_1o8zk686zb().s[8]++, useState(true)),
    _ref1 = _slicedToArray(_ref0, 2),
    loading = _ref1[0],
    setLoading = _ref1[1];
  var _ref10 = (cov_1o8zk686zb().s[9]++, useState(false)),
    _ref11 = _slicedToArray(_ref10, 2),
    upgrading = _ref11[0],
    setUpgrading = _ref11[1];
  cov_1o8zk686zb().s[10]++;
  useEffect(function () {
    cov_1o8zk686zb().f[1]++;
    cov_1o8zk686zb().s[11]++;
    loadSubscriptionData();
  }, []);
  cov_1o8zk686zb().s[12]++;
  var loadSubscriptionData = function () {
    var _ref12 = _asyncToGenerator(function* () {
      cov_1o8zk686zb().f[2]++;
      cov_1o8zk686zb().s[13]++;
      if (!(user != null && user.id)) {
        cov_1o8zk686zb().b[0][0]++;
        cov_1o8zk686zb().s[14]++;
        return;
      } else {
        cov_1o8zk686zb().b[0][1]++;
      }
      cov_1o8zk686zb().s[15]++;
      try {
        cov_1o8zk686zb().s[16]++;
        setLoading(true);
        var _ref13 = (cov_1o8zk686zb().s[17]++, yield Promise.all([paymentService.getSubscriptionPlans(), paymentService.getUserSubscription(user.id)])),
          _ref14 = _slicedToArray(_ref13, 2),
          subscriptionPlans = _ref14[0],
          userSubscription = _ref14[1];
        cov_1o8zk686zb().s[18]++;
        setRealPlans(subscriptionPlans);
        cov_1o8zk686zb().s[19]++;
        setRealSubscription(userSubscription);
      } catch (error) {
        cov_1o8zk686zb().s[20]++;
        console.error('Failed to load subscription data:', error);
        cov_1o8zk686zb().s[21]++;
        Alert.alert('Error', 'Failed to load subscription information');
      } finally {
        cov_1o8zk686zb().s[22]++;
        setLoading(false);
      }
    });
    return function loadSubscriptionData() {
      return _ref12.apply(this, arguments);
    };
  }();
  cov_1o8zk686zb().s[23]++;
  var handleUpgrade = function () {
    var _ref15 = _asyncToGenerator(function* (planId) {
      cov_1o8zk686zb().f[3]++;
      cov_1o8zk686zb().s[24]++;
      if (!(user != null && user.id)) {
        cov_1o8zk686zb().b[1][0]++;
        cov_1o8zk686zb().s[25]++;
        return;
      } else {
        cov_1o8zk686zb().b[1][1]++;
      }
      cov_1o8zk686zb().s[26]++;
      if (planId === currentSubscription.plan) {
        cov_1o8zk686zb().b[2][0]++;
        cov_1o8zk686zb().s[27]++;
        Alert.alert('Current Plan', 'You are already subscribed to this plan.');
        cov_1o8zk686zb().s[28]++;
        return;
      } else {
        cov_1o8zk686zb().b[2][1]++;
      }
      var plan = (cov_1o8zk686zb().s[29]++, plans.find(function (p) {
        cov_1o8zk686zb().f[4]++;
        cov_1o8zk686zb().s[30]++;
        return p.id === planId;
      }));
      cov_1o8zk686zb().s[31]++;
      if (!plan) {
        cov_1o8zk686zb().b[3][0]++;
        cov_1o8zk686zb().s[32]++;
        return;
      } else {
        cov_1o8zk686zb().b[3][1]++;
      }
      cov_1o8zk686zb().s[33]++;
      if (realSubscription) {
        cov_1o8zk686zb().b[4][0]++;
        cov_1o8zk686zb().s[34]++;
        Alert.alert('Change Plan', `Are you sure you want to change to ${plan.name}?`, [{
          text: 'Cancel',
          style: 'cancel'
        }, {
          text: 'Change Plan',
          onPress: function onPress() {
            cov_1o8zk686zb().f[5]++;
            cov_1o8zk686zb().s[35]++;
            return handleChangePlan(planId);
          }
        }]);
      } else {
        cov_1o8zk686zb().b[4][1]++;
        cov_1o8zk686zb().s[36]++;
        router.push({
          pathname: '/payment/checkout',
          params: {
            planId: planId,
            planName: plan.name,
            price: plan.price.toString()
          }
        });
      }
    });
    return function handleUpgrade(_x) {
      return _ref15.apply(this, arguments);
    };
  }();
  cov_1o8zk686zb().s[37]++;
  var handleChangePlan = function () {
    var _ref16 = _asyncToGenerator(function* (planId) {
      cov_1o8zk686zb().f[6]++;
      cov_1o8zk686zb().s[38]++;
      if (!realSubscription) {
        cov_1o8zk686zb().b[5][0]++;
        cov_1o8zk686zb().s[39]++;
        return;
      } else {
        cov_1o8zk686zb().b[5][1]++;
      }
      cov_1o8zk686zb().s[40]++;
      try {
        cov_1o8zk686zb().s[41]++;
        setUpgrading(true);
        var success = (cov_1o8zk686zb().s[42]++, yield paymentService.updateSubscriptionPlan(realSubscription.id, planId));
        cov_1o8zk686zb().s[43]++;
        if (success) {
          cov_1o8zk686zb().b[6][0]++;
          cov_1o8zk686zb().s[44]++;
          Alert.alert('Success', 'Your subscription has been updated!');
          cov_1o8zk686zb().s[45]++;
          loadSubscriptionData();
        } else {
          cov_1o8zk686zb().b[6][1]++;
          cov_1o8zk686zb().s[46]++;
          Alert.alert('Error', 'Failed to update subscription');
        }
      } catch (error) {
        cov_1o8zk686zb().s[47]++;
        console.error('Failed to change plan:', error);
        cov_1o8zk686zb().s[48]++;
        Alert.alert('Error', 'Failed to update subscription');
      } finally {
        cov_1o8zk686zb().s[49]++;
        setUpgrading(false);
      }
    });
    return function handleChangePlan(_x2) {
      return _ref16.apply(this, arguments);
    };
  }();
  cov_1o8zk686zb().s[50]++;
  var handleCancelSubscription = function () {
    var _ref17 = _asyncToGenerator(function* () {
      cov_1o8zk686zb().f[7]++;
      cov_1o8zk686zb().s[51]++;
      if (!realSubscription) {
        cov_1o8zk686zb().b[7][0]++;
        cov_1o8zk686zb().s[52]++;
        return;
      } else {
        cov_1o8zk686zb().b[7][1]++;
      }
      cov_1o8zk686zb().s[53]++;
      Alert.alert('Cancel Subscription', 'Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.', [{
        text: 'Keep Subscription',
        style: 'cancel'
      }, {
        text: 'Cancel',
        style: 'destructive',
        onPress: function () {
          var _onPress = _asyncToGenerator(function* () {
            cov_1o8zk686zb().f[8]++;
            cov_1o8zk686zb().s[54]++;
            try {
              var success = (cov_1o8zk686zb().s[55]++, yield paymentService.cancelSubscription(realSubscription.id, false));
              cov_1o8zk686zb().s[56]++;
              if (success) {
                cov_1o8zk686zb().b[8][0]++;
                cov_1o8zk686zb().s[57]++;
                Alert.alert('Subscription Canceled', 'Your subscription has been canceled. You will retain access until the end of your current billing period.');
                cov_1o8zk686zb().s[58]++;
                loadSubscriptionData();
              } else {
                cov_1o8zk686zb().b[8][1]++;
                cov_1o8zk686zb().s[59]++;
                Alert.alert('Error', 'Failed to cancel subscription');
              }
            } catch (error) {
              cov_1o8zk686zb().s[60]++;
              console.error('Failed to cancel subscription:', error);
              cov_1o8zk686zb().s[61]++;
              Alert.alert('Error', 'Failed to cancel subscription');
            }
          });
          function onPress() {
            return _onPress.apply(this, arguments);
          }
          return onPress;
        }()
      }]);
    });
    return function handleCancelSubscription() {
      return _ref17.apply(this, arguments);
    };
  }();
  cov_1o8zk686zb().s[62]++;
  var handleUpdatePayment = function handleUpdatePayment() {
    cov_1o8zk686zb().f[9]++;
    cov_1o8zk686zb().s[63]++;
    router.push('/payment/payment-methods');
  };
  cov_1o8zk686zb().s[64]++;
  var handleViewBillingHistory = function handleViewBillingHistory() {
    cov_1o8zk686zb().f[10]++;
    cov_1o8zk686zb().s[65]++;
    router.push('/payment/billing-history');
  };
  cov_1o8zk686zb().s[66]++;
  var getDiscountedPrice = function getDiscountedPrice(price) {
    cov_1o8zk686zb().f[11]++;
    cov_1o8zk686zb().s[67]++;
    return billingPeriod === 'yearly' ? (cov_1o8zk686zb().b[9][0]++, price * 10) : (cov_1o8zk686zb().b[9][1]++, price);
  };
  cov_1o8zk686zb().s[68]++;
  var getSavingsText = function getSavingsText(price) {
    cov_1o8zk686zb().f[12]++;
    cov_1o8zk686zb().s[69]++;
    if ((cov_1o8zk686zb().b[11][0]++, billingPeriod === 'yearly') && (cov_1o8zk686zb().b[11][1]++, price > 0)) {
      cov_1o8zk686zb().b[10][0]++;
      var monthlyCost = (cov_1o8zk686zb().s[70]++, price * 12);
      var yearlyCost = (cov_1o8zk686zb().s[71]++, price * 10);
      var savings = (cov_1o8zk686zb().s[72]++, monthlyCost - yearlyCost);
      cov_1o8zk686zb().s[73]++;
      return `Save $${savings.toFixed(2)}/year`;
    } else {
      cov_1o8zk686zb().b[10][1]++;
    }
    cov_1o8zk686zb().s[74]++;
    return null;
  };
  cov_1o8zk686zb().s[75]++;
  if (loading) {
    cov_1o8zk686zb().b[12][0]++;
    cov_1o8zk686zb().s[76]++;
    return _jsx(SafeAreaView, {
      style: styles.container,
      children: _jsx(LinearGradient, {
        colors: ['#1e3a8a', '#3b82f6', '#60a5fa'],
        style: styles.gradient,
        children: _jsxs(View, {
          style: styles.loadingContainer,
          children: [_jsx(ActivityIndicator, {
            size: "large",
            color: colors.white
          }), _jsx(Text, {
            style: styles.loadingText,
            children: "Loading subscription plans..."
          })]
        })
      })
    });
  } else {
    cov_1o8zk686zb().b[12][1]++;
  }
  cov_1o8zk686zb().s[77]++;
  return _jsx(ErrorBoundary, {
    context: "SubscriptionScreen",
    children: _jsx(SafeAreaView, {
      style: styles.container,
      children: _jsxs(LinearGradient, {
        colors: ['#1e3a8a', '#3b82f6', '#60a5fa'],
        style: styles.gradient,
        children: [_jsxs(View, {
          style: styles.header,
          children: [_jsx(TouchableOpacity, {
            onPress: function onPress() {
              cov_1o8zk686zb().f[13]++;
              cov_1o8zk686zb().s[78]++;
              return router.back();
            },
            style: styles.backButton,
            children: _jsx(ArrowLeft, {
              size: 24,
              color: "white"
            })
          }), _jsx(Text, {
            style: styles.title,
            children: "Subscription"
          }), _jsx(View, {
            style: styles.headerSpacer
          })]
        }), _jsxs(ScrollView, {
          style: styles.content,
          showsVerticalScrollIndicator: false,
          children: [_jsxs(Card, {
            style: styles.currentCard,
            children: [_jsxs(View, {
              style: styles.currentHeader,
              children: [_jsx(Crown, {
                size: 24,
                color: colors.premium
              }), _jsx(Text, {
                style: styles.currentTitle,
                children: "Current Plan"
              })]
            }), _jsxs(Text, {
              style: styles.currentPlan,
              children: [(_plans$find = plans.find(function (p) {
                cov_1o8zk686zb().f[14]++;
                cov_1o8zk686zb().s[79]++;
                return p.id === currentSubscription.plan;
              })) == null ? void 0 : _plans$find.name, " Plan"]
            }), _jsxs(Text, {
              style: styles.currentStatus,
              children: ["Status: ", _jsx(Text, {
                style: styles.activeStatus,
                children: "Active"
              })]
            }), _jsxs(Text, {
              style: styles.currentBilling,
              children: ["Next billing: ", new Date(currentSubscription.nextBilling).toLocaleDateString()]
            }), _jsxs(Text, {
              style: styles.currentAmount,
              children: ["$", currentSubscription.amount, "/month"]
            })]
          }), _jsxs(Card, {
            style: styles.billingCard,
            children: [_jsx(Text, {
              style: styles.billingTitle,
              children: "Billing Period"
            }), _jsxs(View, {
              style: styles.billingToggle,
              children: [_jsx(TouchableOpacity, {
                style: [styles.billingOption, (cov_1o8zk686zb().b[13][0]++, billingPeriod === 'monthly') && (cov_1o8zk686zb().b[13][1]++, styles.activeBilling)],
                onPress: function onPress() {
                  cov_1o8zk686zb().f[15]++;
                  cov_1o8zk686zb().s[80]++;
                  return setBillingPeriod('monthly');
                },
                children: _jsx(Text, {
                  style: [styles.billingText, (cov_1o8zk686zb().b[14][0]++, billingPeriod === 'monthly') && (cov_1o8zk686zb().b[14][1]++, styles.activeBillingText)],
                  children: "Monthly"
                })
              }), _jsxs(TouchableOpacity, {
                style: [styles.billingOption, (cov_1o8zk686zb().b[15][0]++, billingPeriod === 'yearly') && (cov_1o8zk686zb().b[15][1]++, styles.activeBilling)],
                onPress: function onPress() {
                  cov_1o8zk686zb().f[16]++;
                  cov_1o8zk686zb().s[81]++;
                  return setBillingPeriod('yearly');
                },
                children: [_jsx(Text, {
                  style: [styles.billingText, (cov_1o8zk686zb().b[16][0]++, billingPeriod === 'yearly') && (cov_1o8zk686zb().b[16][1]++, styles.activeBillingText)],
                  children: "Yearly"
                }), _jsx(View, {
                  style: styles.savingsBadge,
                  children: _jsx(Text, {
                    style: styles.savingsText,
                    children: "Save 17%"
                  })
                })]
              })]
            })]
          }), _jsx(Text, {
            style: styles.plansTitle,
            children: "Choose Your Plan"
          }), plans.map(function (plan) {
            cov_1o8zk686zb().f[17]++;
            cov_1o8zk686zb().s[82]++;
            return _jsxs(Card, {
              style: [styles.planCard, (cov_1o8zk686zb().b[17][0]++, plan.popular) && (cov_1o8zk686zb().b[17][1]++, styles.popularPlan), (cov_1o8zk686zb().b[18][0]++, currentSubscription.plan === plan.id) && (cov_1o8zk686zb().b[18][1]++, styles.currentPlanCard)].filter(Boolean),
              children: [(cov_1o8zk686zb().b[19][0]++, plan.popular) && (cov_1o8zk686zb().b[19][1]++, _jsx(View, {
                style: styles.popularBadge,
                children: _jsx(Text, {
                  style: styles.popularText,
                  children: "Most Popular"
                })
              })), _jsxs(View, {
                style: styles.planHeader,
                children: [_jsx(View, {
                  style: [styles.planIcon, {
                    backgroundColor: plan.color
                  }],
                  children: _jsx(plan.icon, {
                    size: 24,
                    color: "white"
                  })
                }), _jsxs(View, {
                  style: styles.planInfo,
                  children: [_jsx(Text, {
                    style: styles.planName,
                    children: plan.name
                  }), _jsx(View, {
                    style: styles.planPricing,
                    children: plan.price === 0 ? (cov_1o8zk686zb().b[20][0]++, _jsx(Text, {
                      style: styles.planPrice,
                      children: "Free"
                    })) : (cov_1o8zk686zb().b[20][1]++, _jsxs(_Fragment, {
                      children: [_jsxs(Text, {
                        style: styles.planPrice,
                        children: ["$", getDiscountedPrice(plan.price).toFixed(2)]
                      }), _jsxs(Text, {
                        style: styles.planPeriod,
                        children: ["/", billingPeriod === 'yearly' ? (cov_1o8zk686zb().b[21][0]++, 'year') : (cov_1o8zk686zb().b[21][1]++, 'month')]
                      })]
                    }))
                  }), (cov_1o8zk686zb().b[22][0]++, getSavingsText(plan.price)) && (cov_1o8zk686zb().b[22][1]++, _jsx(Text, {
                    style: styles.savingsLabel,
                    children: getSavingsText(plan.price)
                  }))]
                }), (cov_1o8zk686zb().b[23][0]++, currentSubscription.plan === plan.id) && (cov_1o8zk686zb().b[23][1]++, _jsx(View, {
                  style: styles.currentBadge,
                  children: _jsx(Text, {
                    style: styles.currentBadgeText,
                    children: "Current"
                  })
                }))]
              }), _jsx(View, {
                style: styles.planFeatures,
                children: plan.features.map(function (feature, index) {
                  cov_1o8zk686zb().f[18]++;
                  cov_1o8zk686zb().s[83]++;
                  return _jsxs(View, {
                    style: styles.featureItem,
                    children: [_jsx(Check, {
                      size: 16,
                      color: colors.primary
                    }), _jsx(Text, {
                      style: styles.featureText,
                      children: feature
                    })]
                  }, index);
                })
              }), (cov_1o8zk686zb().b[24][0]++, currentSubscription.plan !== plan.id) && (cov_1o8zk686zb().b[24][1]++, _jsx(Button, {
                title: plan.price === 0 ? (cov_1o8zk686zb().b[25][0]++, 'Downgrade') : (cov_1o8zk686zb().b[25][1]++, 'Upgrade'),
                onPress: function onPress() {
                  cov_1o8zk686zb().f[19]++;
                  cov_1o8zk686zb().s[84]++;
                  return handleUpgrade(plan.id);
                },
                style: [styles.planButton, (cov_1o8zk686zb().b[26][0]++, plan.popular) && (cov_1o8zk686zb().b[26][1]++, styles.popularButton)].filter(Boolean),
                variant: plan.popular ? (cov_1o8zk686zb().b[27][0]++, 'primary') : (cov_1o8zk686zb().b[27][1]++, 'outline')
              }))]
            }, plan.id);
          }), _jsxs(Card, {
            style: styles.paymentCard,
            children: [_jsx(Text, {
              style: styles.paymentTitle,
              children: "Payment Method"
            }), _jsxs(View, {
              style: styles.paymentMethod,
              children: [_jsx(CreditCard, {
                size: 20,
                color: colors.gray
              }), _jsxs(Text, {
                style: styles.paymentText,
                children: ["Visa ending in ", currentSubscription.paymentMethod]
              }), _jsx(TouchableOpacity, {
                onPress: handleUpdatePayment,
                children: _jsx(Text, {
                  style: styles.updateText,
                  children: "Update"
                })
              })]
            })]
          }), _jsxs(Card, {
            style: styles.historyCard,
            children: [_jsx(Text, {
              style: styles.historyTitle,
              children: "Billing History"
            }), _jsxs(View, {
              style: styles.historyItem,
              children: [_jsxs(View, {
                style: styles.historyInfo,
                children: [_jsx(Text, {
                  style: styles.historyDate,
                  children: "Jan 15, 2024"
                }), _jsx(Text, {
                  style: styles.historyDescription,
                  children: "Premium Plan"
                })]
              }), _jsx(Text, {
                style: styles.historyAmount,
                children: "$9.99"
              }), _jsx(TouchableOpacity, {
                children: _jsx(Download, {
                  size: 16,
                  color: colors.primary
                })
              })]
            }), _jsxs(View, {
              style: styles.historyItem,
              children: [_jsxs(View, {
                style: styles.historyInfo,
                children: [_jsx(Text, {
                  style: styles.historyDate,
                  children: "Dec 15, 2023"
                }), _jsx(Text, {
                  style: styles.historyDescription,
                  children: "Premium Plan"
                })]
              }), _jsx(Text, {
                style: styles.historyAmount,
                children: "$9.99"
              }), _jsx(TouchableOpacity, {
                children: _jsx(Download, {
                  size: 16,
                  color: colors.primary
                })
              })]
            })]
          }), _jsxs(Card, {
            style: styles.cancelCard,
            children: [_jsx(Text, {
              style: styles.cancelTitle,
              children: "Need to cancel?"
            }), _jsx(Text, {
              style: styles.cancelDescription,
              children: "You can cancel your subscription at any time. You'll continue to have access to premium features until the end of your billing period."
            }), _jsx(Button, {
              title: "Cancel Subscription",
              onPress: handleCancelSubscription,
              style: styles.cancelButton,
              variant: "outline"
            })]
          })]
        })]
      })
    })
  });
}
var styles = (cov_1o8zk686zb().s[85]++, StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10
  },
  backButton: {
    padding: 8
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white'
  },
  headerSpacer: {
    width: 40
  },
  content: {
    flex: 1,
    paddingHorizontal: 20
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: colors.white,
    marginTop: 16
  },
  currentCard: {
    padding: 20,
    marginBottom: 15,
    backgroundColor: colors.lightGray
  },
  currentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    marginBottom: 15
  },
  currentTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.dark
  },
  currentPlan: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.dark,
    marginBottom: 8
  },
  currentStatus: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 4
  },
  activeStatus: {
    color: colors.primary,
    fontWeight: '600'
  },
  currentBilling: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 4
  },
  currentAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.dark
  },
  billingCard: {
    padding: 20,
    marginBottom: 15
  },
  billingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.dark,
    marginBottom: 15
  },
  billingToggle: {
    flexDirection: 'row',
    backgroundColor: colors.lightGray,
    borderRadius: 8,
    padding: 4
  },
  billingOption: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 6,
    position: 'relative'
  },
  activeBilling: {
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  billingText: {
    fontSize: 14,
    color: colors.gray
  },
  activeBillingText: {
    color: colors.dark,
    fontWeight: '600'
  },
  savingsBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: colors.primary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8
  },
  savingsText: {
    fontSize: 10,
    color: 'white',
    fontWeight: '600'
  },
  plansTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 15,
    textAlign: 'center'
  },
  planCard: {
    padding: 20,
    marginBottom: 15,
    position: 'relative'
  },
  popularPlan: {
    borderWidth: 2,
    borderColor: colors.premium
  },
  currentPlanCard: {
    backgroundColor: colors.lightGray
  },
  popularBadge: {
    position: 'absolute',
    top: -10,
    left: 20,
    backgroundColor: colors.premium,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12
  },
  popularText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600'
  },
  planHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20
  },
  planIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15
  },
  planInfo: {
    flex: 1
  },
  planName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.dark,
    marginBottom: 4
  },
  planPricing: {
    flexDirection: 'row',
    alignItems: 'baseline'
  },
  planPrice: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.dark
  },
  planPeriod: {
    fontSize: 16,
    color: colors.gray,
    marginLeft: 4
  },
  savingsLabel: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '600',
    marginTop: 2
  },
  currentBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12
  },
  currentBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600'
  },
  planFeatures: {
    marginBottom: 20
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8
  },
  featureText: {
    fontSize: 14,
    color: colors.dark,
    marginLeft: 10
  },
  planButton: {
    marginTop: 10
  },
  popularButton: {
    backgroundColor: colors.premium
  },
  paymentCard: {
    padding: 20,
    marginBottom: 15
  },
  paymentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.dark,
    marginBottom: 15
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12
  },
  paymentText: {
    flex: 1,
    fontSize: 14,
    color: colors.dark
  },
  updateText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '600'
  },
  historyCard: {
    padding: 20,
    marginBottom: 15
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.dark,
    marginBottom: 15
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray
  },
  historyInfo: {
    flex: 1
  },
  historyDate: {
    fontSize: 14,
    color: colors.dark,
    fontWeight: '500'
  },
  historyDescription: {
    fontSize: 12,
    color: colors.gray,
    marginTop: 2
  },
  historyAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.dark,
    marginRight: 15
  },
  cancelCard: {
    padding: 20,
    marginBottom: 30
  },
  cancelTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.dark,
    marginBottom: 10
  },
  cancelDescription: {
    fontSize: 14,
    color: colors.gray,
    lineHeight: 20,
    marginBottom: 15
  },
  cancelButton: {
    borderColor: '#ef4444'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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