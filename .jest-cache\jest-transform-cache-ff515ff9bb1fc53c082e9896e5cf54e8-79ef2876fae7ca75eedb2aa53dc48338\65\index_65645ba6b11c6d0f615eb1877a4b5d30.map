{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "_extends2", "_objectWithoutPropertiesLoose2", "_react", "React", "_useMergeRefs", "_usePressEvents", "_StyleSheet", "_View", "_excluded", "createExtraStyles", "activeOpacity", "underlayColor", "child", "opacity", "underlay", "backgroundColor", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "onPress", "onPressIn", "onPressOut", "onLongPress", "TouchableHighlight", "forwardedRef", "children", "delayPressIn", "delayPressOut", "delayLongPress", "disabled", "focusable", "onHideUnderlay", "onShowUnderlay", "rejectResponderTermination", "style", "testOnly_pressed", "rest", "hostRef", "useRef", "setRef", "_useState", "useState", "extraStyles", "setExtraStyles", "showUnderlay", "useCallback", "<PERSON><PERSON><PERSON><PERSON>", "pressConfig", "useMemo", "cancelable", "delayPressStart", "delayPressEnd", "onPressStart", "event", "onPressEnd", "pressEventHandlers", "Children", "only", "createElement", "accessibilityDisabled", "pointerEvents", "ref", "styles", "root", "actionable", "cloneElement", "create", "userSelect", "cursor", "touchAction", "MemoedTouchableHighlight", "memo", "forwardRef", "displayName", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar React = _react;\nvar _useMergeRefs = _interopRequireDefault(require(\"../../modules/useMergeRefs\"));\nvar _usePressEvents = _interopRequireDefault(require(\"../../modules/usePressEvents\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _View = _interopRequireDefault(require(\"../View\"));\nvar _excluded = [\"activeOpacity\", \"children\", \"delayPressIn\", \"delayPressOut\", \"delayLongPress\", \"disabled\", \"focusable\", \"onHideUnderlay\", \"onLongPress\", \"onPress\", \"onPressIn\", \"onPressOut\", \"onShowUnderlay\", \"rejectResponderTermination\", \"style\", \"testOnly_pressed\", \"underlayColor\"];\n//import { warnOnce } from '../../modules/warnOnce';\n\nfunction createExtraStyles(activeOpacity, underlayColor) {\n  return {\n    child: {\n      opacity: activeOpacity !== null && activeOpacity !== void 0 ? activeOpacity : 0.85\n    },\n    underlay: {\n      backgroundColor: underlayColor === undefined ? 'black' : underlayColor\n    }\n  };\n}\nfunction hasPressHandler(props) {\n  return props.onPress != null || props.onPressIn != null || props.onPressOut != null || props.onLongPress != null;\n}\n\n/**\n * A wrapper for making views respond properly to touches.\n * On press down, the opacity of the wrapped view is decreased, which allows\n * the underlay color to show through, darkening or tinting the view.\n *\n * The underlay comes from wrapping the child in a new View, which can affect\n * layout, and sometimes cause unwanted visual artifacts if not used correctly,\n * for example if the backgroundColor of the wrapped view isn't explicitly set\n * to an opaque color.\n *\n * TouchableHighlight must have one child (not zero or more than one).\n * If you wish to have several child components, wrap them in a View.\n */\nfunction TouchableHighlight(props, forwardedRef) {\n  /*\n  warnOnce(\n    'TouchableHighlight',\n    'TouchableHighlight is deprecated. Please use Pressable.'\n  );\n  */\n\n  var activeOpacity = props.activeOpacity,\n    children = props.children,\n    delayPressIn = props.delayPressIn,\n    delayPressOut = props.delayPressOut,\n    delayLongPress = props.delayLongPress,\n    disabled = props.disabled,\n    focusable = props.focusable,\n    onHideUnderlay = props.onHideUnderlay,\n    onLongPress = props.onLongPress,\n    onPress = props.onPress,\n    onPressIn = props.onPressIn,\n    onPressOut = props.onPressOut,\n    onShowUnderlay = props.onShowUnderlay,\n    rejectResponderTermination = props.rejectResponderTermination,\n    style = props.style,\n    testOnly_pressed = props.testOnly_pressed,\n    underlayColor = props.underlayColor,\n    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  var hostRef = (0, _react.useRef)(null);\n  var setRef = (0, _useMergeRefs.default)(forwardedRef, hostRef);\n  var _useState = (0, _react.useState)(testOnly_pressed === true ? createExtraStyles(activeOpacity, underlayColor) : null),\n    extraStyles = _useState[0],\n    setExtraStyles = _useState[1];\n  var showUnderlay = (0, _react.useCallback)(() => {\n    if (!hasPressHandler(props)) {\n      return;\n    }\n    setExtraStyles(createExtraStyles(activeOpacity, underlayColor));\n    if (onShowUnderlay != null) {\n      onShowUnderlay();\n    }\n  }, [activeOpacity, onShowUnderlay, props, underlayColor]);\n  var hideUnderlay = (0, _react.useCallback)(() => {\n    if (testOnly_pressed === true) {\n      return;\n    }\n    if (hasPressHandler(props)) {\n      setExtraStyles(null);\n      if (onHideUnderlay != null) {\n        onHideUnderlay();\n      }\n    }\n  }, [onHideUnderlay, props, testOnly_pressed]);\n  var pressConfig = (0, _react.useMemo)(() => ({\n    cancelable: !rejectResponderTermination,\n    disabled,\n    delayLongPress,\n    delayPressStart: delayPressIn,\n    delayPressEnd: delayPressOut,\n    onLongPress,\n    onPress,\n    onPressStart(event) {\n      showUnderlay();\n      if (onPressIn != null) {\n        onPressIn(event);\n      }\n    },\n    onPressEnd(event) {\n      hideUnderlay();\n      if (onPressOut != null) {\n        onPressOut(event);\n      }\n    }\n  }), [delayLongPress, delayPressIn, delayPressOut, disabled, onLongPress, onPress, onPressIn, onPressOut, rejectResponderTermination, showUnderlay, hideUnderlay]);\n  var pressEventHandlers = (0, _usePressEvents.default)(hostRef, pressConfig);\n  var child = React.Children.only(children);\n  return /*#__PURE__*/React.createElement(_View.default, (0, _extends2.default)({}, rest, pressEventHandlers, {\n    accessibilityDisabled: disabled,\n    focusable: !disabled && focusable !== false,\n    pointerEvents: disabled ? 'box-none' : undefined,\n    ref: setRef,\n    style: [styles.root, style, !disabled && styles.actionable, extraStyles && extraStyles.underlay]\n  }), /*#__PURE__*/React.cloneElement(child, {\n    style: [child.props.style, extraStyles && extraStyles.child]\n  }));\n}\nvar styles = _StyleSheet.default.create({\n  root: {\n    userSelect: 'none'\n  },\n  actionable: {\n    cursor: 'pointer',\n    touchAction: 'manipulation'\n  }\n});\nvar MemoedTouchableHighlight = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(TouchableHighlight));\nMemoedTouchableHighlight.displayName = 'TouchableHighlight';\nvar _default = exports.default = MemoedTouchableHighlight;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIM,8BAA8B,GAAGP,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIO,MAAM,GAAGL,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACtD,IAAIQ,KAAK,GAAGD,MAAM;AAClB,IAAIE,aAAa,GAAGV,sBAAsB,CAACC,OAAO,6BAA6B,CAAC,CAAC;AACjF,IAAIU,eAAe,GAAGX,sBAAsB,CAACC,OAAO,+BAA+B,CAAC,CAAC;AACrF,IAAIW,WAAW,GAAGZ,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIY,KAAK,GAAGb,sBAAsB,CAACC,OAAO,UAAU,CAAC,CAAC;AACtD,IAAIa,SAAS,GAAG,CAAC,eAAe,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,UAAU,EAAE,WAAW,EAAE,gBAAgB,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAAE,4BAA4B,EAAE,OAAO,EAAE,kBAAkB,EAAE,eAAe,CAAC;AAG9R,SAASC,iBAAiBA,CAACC,aAAa,EAAEC,aAAa,EAAE;EACvD,OAAO;IACLC,KAAK,EAAE;MACLC,OAAO,EAAEH,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG;IAChF,CAAC;IACDI,QAAQ,EAAE;MACRC,eAAe,EAAEJ,aAAa,KAAKK,SAAS,GAAG,OAAO,GAAGL;IAC3D;EACF,CAAC;AACH;AACA,SAASM,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAOA,KAAK,CAACC,OAAO,IAAI,IAAI,IAAID,KAAK,CAACE,SAAS,IAAI,IAAI,IAAIF,KAAK,CAACG,UAAU,IAAI,IAAI,IAAIH,KAAK,CAACI,WAAW,IAAI,IAAI;AAClH;AAeA,SAASC,kBAAkBA,CAACL,KAAK,EAAEM,YAAY,EAAE;EAQ/C,IAAId,aAAa,GAAGQ,KAAK,CAACR,aAAa;IACrCe,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,YAAY,GAAGR,KAAK,CAACQ,YAAY;IACjCC,aAAa,GAAGT,KAAK,CAACS,aAAa;IACnCC,cAAc,GAAGV,KAAK,CAACU,cAAc;IACrCC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,SAAS,GAAGZ,KAAK,CAACY,SAAS;IAC3BC,cAAc,GAAGb,KAAK,CAACa,cAAc;IACrCT,WAAW,GAAGJ,KAAK,CAACI,WAAW;IAC/BH,OAAO,GAAGD,KAAK,CAACC,OAAO;IACvBC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BW,cAAc,GAAGd,KAAK,CAACc,cAAc;IACrCC,0BAA0B,GAAGf,KAAK,CAACe,0BAA0B;IAC7DC,KAAK,GAAGhB,KAAK,CAACgB,KAAK;IACnBC,gBAAgB,GAAGjB,KAAK,CAACiB,gBAAgB;IACzCxB,aAAa,GAAGO,KAAK,CAACP,aAAa;IACnCyB,IAAI,GAAG,CAAC,CAAC,EAAEnC,8BAA8B,CAACL,OAAO,EAAEsB,KAAK,EAAEV,SAAS,CAAC;EACtE,IAAI6B,OAAO,GAAG,CAAC,CAAC,EAAEnC,MAAM,CAACoC,MAAM,EAAE,IAAI,CAAC;EACtC,IAAIC,MAAM,GAAG,CAAC,CAAC,EAAEnC,aAAa,CAACR,OAAO,EAAE4B,YAAY,EAAEa,OAAO,CAAC;EAC9D,IAAIG,SAAS,GAAG,CAAC,CAAC,EAAEtC,MAAM,CAACuC,QAAQ,EAAEN,gBAAgB,KAAK,IAAI,GAAG1B,iBAAiB,CAACC,aAAa,EAAEC,aAAa,CAAC,GAAG,IAAI,CAAC;IACtH+B,WAAW,GAAGF,SAAS,CAAC,CAAC,CAAC;IAC1BG,cAAc,GAAGH,SAAS,CAAC,CAAC,CAAC;EAC/B,IAAII,YAAY,GAAG,CAAC,CAAC,EAAE1C,MAAM,CAAC2C,WAAW,EAAE,YAAM;IAC/C,IAAI,CAAC5B,eAAe,CAACC,KAAK,CAAC,EAAE;MAC3B;IACF;IACAyB,cAAc,CAAClC,iBAAiB,CAACC,aAAa,EAAEC,aAAa,CAAC,CAAC;IAC/D,IAAIqB,cAAc,IAAI,IAAI,EAAE;MAC1BA,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACtB,aAAa,EAAEsB,cAAc,EAAEd,KAAK,EAAEP,aAAa,CAAC,CAAC;EACzD,IAAImC,YAAY,GAAG,CAAC,CAAC,EAAE5C,MAAM,CAAC2C,WAAW,EAAE,YAAM;IAC/C,IAAIV,gBAAgB,KAAK,IAAI,EAAE;MAC7B;IACF;IACA,IAAIlB,eAAe,CAACC,KAAK,CAAC,EAAE;MAC1ByB,cAAc,CAAC,IAAI,CAAC;MACpB,IAAIZ,cAAc,IAAI,IAAI,EAAE;QAC1BA,cAAc,CAAC,CAAC;MAClB;IACF;EACF,CAAC,EAAE,CAACA,cAAc,EAAEb,KAAK,EAAEiB,gBAAgB,CAAC,CAAC;EAC7C,IAAIY,WAAW,GAAG,CAAC,CAAC,EAAE7C,MAAM,CAAC8C,OAAO,EAAE;IAAA,OAAO;MAC3CC,UAAU,EAAE,CAAChB,0BAA0B;MACvCJ,QAAQ,EAARA,QAAQ;MACRD,cAAc,EAAdA,cAAc;MACdsB,eAAe,EAAExB,YAAY;MAC7ByB,aAAa,EAAExB,aAAa;MAC5BL,WAAW,EAAXA,WAAW;MACXH,OAAO,EAAPA,OAAO;MACPiC,YAAY,WAAZA,YAAYA,CAACC,KAAK,EAAE;QAClBT,YAAY,CAAC,CAAC;QACd,IAAIxB,SAAS,IAAI,IAAI,EAAE;UACrBA,SAAS,CAACiC,KAAK,CAAC;QAClB;MACF,CAAC;MACDC,UAAU,WAAVA,UAAUA,CAACD,KAAK,EAAE;QAChBP,YAAY,CAAC,CAAC;QACd,IAAIzB,UAAU,IAAI,IAAI,EAAE;UACtBA,UAAU,CAACgC,KAAK,CAAC;QACnB;MACF;IACF,CAAC;EAAA,CAAC,EAAE,CAACzB,cAAc,EAAEF,YAAY,EAAEC,aAAa,EAAEE,QAAQ,EAAEP,WAAW,EAAEH,OAAO,EAAEC,SAAS,EAAEC,UAAU,EAAEY,0BAA0B,EAAEW,YAAY,EAAEE,YAAY,CAAC,CAAC;EACjK,IAAIS,kBAAkB,GAAG,CAAC,CAAC,EAAElD,eAAe,CAACT,OAAO,EAAEyC,OAAO,EAAEU,WAAW,CAAC;EAC3E,IAAInC,KAAK,GAAGT,KAAK,CAACqD,QAAQ,CAACC,IAAI,CAAChC,QAAQ,CAAC;EACzC,OAAoBtB,KAAK,CAACuD,aAAa,CAACnD,KAAK,CAACX,OAAO,EAAE,CAAC,CAAC,EAAEI,SAAS,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEwC,IAAI,EAAEmB,kBAAkB,EAAE;IAC1GI,qBAAqB,EAAE9B,QAAQ;IAC/BC,SAAS,EAAE,CAACD,QAAQ,IAAIC,SAAS,KAAK,KAAK;IAC3C8B,aAAa,EAAE/B,QAAQ,GAAG,UAAU,GAAGb,SAAS;IAChD6C,GAAG,EAAEtB,MAAM;IACXL,KAAK,EAAE,CAAC4B,MAAM,CAACC,IAAI,EAAE7B,KAAK,EAAE,CAACL,QAAQ,IAAIiC,MAAM,CAACE,UAAU,EAAEtB,WAAW,IAAIA,WAAW,CAAC5B,QAAQ;EACjG,CAAC,CAAC,EAAeX,KAAK,CAAC8D,YAAY,CAACrD,KAAK,EAAE;IACzCsB,KAAK,EAAE,CAACtB,KAAK,CAACM,KAAK,CAACgB,KAAK,EAAEQ,WAAW,IAAIA,WAAW,CAAC9B,KAAK;EAC7D,CAAC,CAAC,CAAC;AACL;AACA,IAAIkD,MAAM,GAAGxD,WAAW,CAACV,OAAO,CAACsE,MAAM,CAAC;EACtCH,IAAI,EAAE;IACJI,UAAU,EAAE;EACd,CAAC;EACDH,UAAU,EAAE;IACVI,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE;EACf;AACF,CAAC,CAAC;AACF,IAAIC,wBAAwB,GAAgBnE,KAAK,CAACoE,IAAI,CAAcpE,KAAK,CAACqE,UAAU,CAACjD,kBAAkB,CAAC,CAAC;AACzG+C,wBAAwB,CAACG,WAAW,GAAG,oBAAoB;AAC3D,IAAIC,QAAQ,GAAG5E,OAAO,CAACF,OAAO,GAAG0E,wBAAwB;AACzDK,MAAM,CAAC7E,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}