6536995313f511a6fe0566dedc8e3d66
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.addModalityListener = addModalityListener;
exports.getActiveModality = getActiveModality;
exports.getModality = getModality;
exports.testOnly_resetActiveModality = testOnly_resetActiveModality;
var _addEventListener = require("../addEventListener");
var _canUseDom = _interopRequireDefault(require("../canUseDom"));
var supportsPointerEvent = function supportsPointerEvent() {
  return !!(typeof window !== 'undefined' && window.PointerEvent != null);
};
var activeModality = 'keyboard';
var modality = 'keyboard';
var previousModality;
var previousActiveModality;
var isEmulatingMouseEvents = false;
var listeners = new Set();
var KEYBOARD = 'keyboard';
var MOUSE = 'mouse';
var TOUCH = 'touch';
var BLUR = 'blur';
var CONTEXTMENU = 'contextmenu';
var FOCUS = 'focus';
var KEYDOWN = 'keydown';
var MOUSEDOWN = 'mousedown';
var MOUSEMOVE = 'mousemove';
var MOUSEUP = 'mouseup';
var POINTERDOWN = 'pointerdown';
var POINTERMOVE = 'pointermove';
var SCROLL = 'scroll';
var SELECTIONCHANGE = 'selectionchange';
var TOUCHCANCEL = 'touchcancel';
var TOUCHMOVE = 'touchmove';
var TOUCHSTART = 'touchstart';
var VISIBILITYCHANGE = 'visibilitychange';
var bubbleOptions = {
  passive: true
};
var captureOptions = {
  capture: true,
  passive: true
};
function restoreModality() {
  if (previousModality != null || previousActiveModality != null) {
    if (previousModality != null) {
      modality = previousModality;
      previousModality = null;
    }
    if (previousActiveModality != null) {
      activeModality = previousActiveModality;
      previousActiveModality = null;
    }
    callListeners();
  }
}
function onBlurWindow() {
  previousModality = modality;
  previousActiveModality = activeModality;
  activeModality = KEYBOARD;
  modality = KEYBOARD;
  callListeners();
  isEmulatingMouseEvents = false;
}
function onFocusWindow() {
  restoreModality();
}
function onKeyDown(event) {
  if (event.metaKey || event.altKey || event.ctrlKey) {
    return;
  }
  if (modality !== KEYBOARD) {
    modality = KEYBOARD;
    activeModality = KEYBOARD;
    callListeners();
  }
}
function onVisibilityChange() {
  if (document.visibilityState !== 'hidden') {
    restoreModality();
  }
}
function onPointerish(event) {
  var eventType = event.type;
  if (supportsPointerEvent()) {
    if (eventType === POINTERDOWN) {
      if (activeModality !== event.pointerType) {
        modality = event.pointerType;
        activeModality = event.pointerType;
        callListeners();
      }
      return;
    }
    if (eventType === POINTERMOVE) {
      if (modality !== event.pointerType) {
        modality = event.pointerType;
        callListeners();
      }
      return;
    }
  } else {
    if (!isEmulatingMouseEvents) {
      if (eventType === MOUSEDOWN) {
        if (activeModality !== MOUSE) {
          modality = MOUSE;
          activeModality = MOUSE;
          callListeners();
        }
      }
      if (eventType === MOUSEMOVE) {
        if (modality !== MOUSE) {
          modality = MOUSE;
          callListeners();
        }
      }
    }
    if (eventType === TOUCHSTART) {
      isEmulatingMouseEvents = true;
      if (event.touches && event.touches.length > 1) {
        isEmulatingMouseEvents = false;
      }
      if (activeModality !== TOUCH) {
        modality = TOUCH;
        activeModality = TOUCH;
        callListeners();
      }
      return;
    }
    if (eventType === CONTEXTMENU || eventType === MOUSEUP || eventType === SELECTIONCHANGE || eventType === SCROLL || eventType === TOUCHCANCEL || eventType === TOUCHMOVE) {
      isEmulatingMouseEvents = false;
    }
  }
}
if (_canUseDom.default) {
  (0, _addEventListener.addEventListener)(window, BLUR, onBlurWindow, bubbleOptions);
  (0, _addEventListener.addEventListener)(window, FOCUS, onFocusWindow, bubbleOptions);
  (0, _addEventListener.addEventListener)(document, KEYDOWN, onKeyDown, captureOptions);
  (0, _addEventListener.addEventListener)(document, VISIBILITYCHANGE, onVisibilityChange, captureOptions);
  (0, _addEventListener.addEventListener)(document, POINTERDOWN, onPointerish, captureOptions);
  (0, _addEventListener.addEventListener)(document, POINTERMOVE, onPointerish, captureOptions);
  (0, _addEventListener.addEventListener)(document, CONTEXTMENU, onPointerish, captureOptions);
  (0, _addEventListener.addEventListener)(document, MOUSEDOWN, onPointerish, captureOptions);
  (0, _addEventListener.addEventListener)(document, MOUSEMOVE, onPointerish, captureOptions);
  (0, _addEventListener.addEventListener)(document, MOUSEUP, onPointerish, captureOptions);
  (0, _addEventListener.addEventListener)(document, TOUCHCANCEL, onPointerish, captureOptions);
  (0, _addEventListener.addEventListener)(document, TOUCHMOVE, onPointerish, captureOptions);
  (0, _addEventListener.addEventListener)(document, TOUCHSTART, onPointerish, captureOptions);
  (0, _addEventListener.addEventListener)(document, SELECTIONCHANGE, onPointerish, captureOptions);
  (0, _addEventListener.addEventListener)(document, SCROLL, onPointerish, captureOptions);
}
function callListeners() {
  var value = {
    activeModality: activeModality,
    modality: modality
  };
  listeners.forEach(function (listener) {
    listener(value);
  });
}
function getActiveModality() {
  return activeModality;
}
function getModality() {
  return modality;
}
function addModalityListener(listener) {
  listeners.add(listener);
  return function () {
    listeners.delete(listener);
  };
}
function testOnly_resetActiveModality() {
  isEmulatingMouseEvents = false;
  activeModality = KEYBOARD;
  modality = KEYBOARD;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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