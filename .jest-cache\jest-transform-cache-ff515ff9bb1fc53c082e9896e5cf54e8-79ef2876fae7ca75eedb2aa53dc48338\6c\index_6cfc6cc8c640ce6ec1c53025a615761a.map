{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_Easing", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _Easing = _interopRequireDefault(require(\"../../vendor/react-native/Animated/Easing\"));\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\nvar _default = exports.default = _Easing.default;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,OAAO,GAAGL,sBAAsB,CAACC,OAAO,4CAA4C,CAAC,CAAC;AAS1F,IAAIK,QAAQ,GAAGH,OAAO,CAACD,OAAO,GAAGG,OAAO,CAACH,OAAO;AAChDK,MAAM,CAACJ,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}