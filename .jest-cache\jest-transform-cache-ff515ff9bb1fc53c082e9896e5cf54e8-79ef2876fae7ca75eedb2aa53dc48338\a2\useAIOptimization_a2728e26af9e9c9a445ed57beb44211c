0eb0d4b55798f40ca3c8e35750c32e8c
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_2crrnrtmks() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useAIOptimization.ts";
  var hash = "46b2e62558f0fdd139a13f53830110113335e5ed";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useAIOptimization.ts",
    statementMap: {
      "0": {
        start: {
          line: 71,
          column: 19
        },
        end: {
          line: 71,
          column: 28
        }
      },
      "1": {
        start: {
          line: 72,
          column: 50
        },
        end: {
          line: 72,
          column: 79
        }
      },
      "2": {
        start: {
          line: 74,
          column: 30
        },
        end: {
          line: 82,
          column: 4
        }
      },
      "3": {
        start: {
          line: 84,
          column: 28
        },
        end: {
          line: 107,
          column: 4
        }
      },
      "4": {
        start: {
          line: 112,
          column: 21
        },
        end: {
          line: 148,
          column: 41
        }
      },
      "5": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 113,
          column: 45
        }
      },
      "6": {
        start: {
          line: 113,
          column: 38
        },
        end: {
          line: 113,
          column: 45
        }
      },
      "7": {
        start: {
          line: 115,
          column: 4
        },
        end: {
          line: 147,
          column: 5
        }
      },
      "8": {
        start: {
          line: 116,
          column: 6
        },
        end: {
          line: 116,
          column: 58
        }
      },
      "9": {
        start: {
          line: 116,
          column: 24
        },
        end: {
          line: 116,
          column: 55
        }
      },
      "10": {
        start: {
          line: 119,
          column: 43
        },
        end: {
          line: 119,
          column: 45
        }
      },
      "11": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 124,
          column: 7
        }
      },
      "12": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 123,
          column: 45
        }
      },
      "13": {
        start: {
          line: 126,
          column: 6
        },
        end: {
          line: 129,
          column: 7
        }
      },
      "14": {
        start: {
          line: 128,
          column: 8
        },
        end: {
          line: 128,
          column: 45
        }
      },
      "15": {
        start: {
          line: 131,
          column: 6
        },
        end: {
          line: 131,
          column: 38
        }
      },
      "16": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 134,
          column: 28
        }
      },
      "17": {
        start: {
          line: 136,
          column: 6
        },
        end: {
          line: 140,
          column: 10
        }
      },
      "18": {
        start: {
          line: 136,
          column: 24
        },
        end: {
          line: 140,
          column: 7
        }
      },
      "19": {
        start: {
          line: 142,
          column: 6
        },
        end: {
          line: 142,
          column: 70
        }
      },
      "20": {
        start: {
          line: 145,
          column: 6
        },
        end: {
          line: 145,
          column: 68
        }
      },
      "21": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 146,
          column: 59
        }
      },
      "22": {
        start: {
          line: 146,
          column: 24
        },
        end: {
          line: 146,
          column: 56
        }
      },
      "23": {
        start: {
          line: 153,
          column: 23
        },
        end: {
          line: 175,
          column: 33
        }
      },
      "24": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 154,
          column: 49
        }
      },
      "25": {
        start: {
          line: 154,
          column: 39
        },
        end: {
          line: 154,
          column: 49
        }
      },
      "26": {
        start: {
          line: 156,
          column: 4
        },
        end: {
          line: 174,
          column: 5
        }
      },
      "27": {
        start: {
          line: 157,
          column: 24
        },
        end: {
          line: 165,
          column: 8
        }
      },
      "28": {
        start: {
          line: 167,
          column: 6
        },
        end: {
          line: 167,
          column: 37
        }
      },
      "29": {
        start: {
          line: 168,
          column: 6
        },
        end: {
          line: 168,
          column: 67
        }
      },
      "30": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 170,
          column: 23
        }
      },
      "31": {
        start: {
          line: 172,
          column: 6
        },
        end: {
          line: 172,
          column: 58
        }
      },
      "32": {
        start: {
          line: 173,
          column: 6
        },
        end: {
          line: 173,
          column: 16
        }
      },
      "33": {
        start: {
          line: 180,
          column: 26
        },
        end: {
          line: 199,
          column: 92
        }
      },
      "34": {
        start: {
          line: 181,
          column: 4
        },
        end: {
          line: 181,
          column: 78
        }
      },
      "35": {
        start: {
          line: 181,
          column: 71
        },
        end: {
          line: 181,
          column: 78
        }
      },
      "36": {
        start: {
          line: 183,
          column: 4
        },
        end: {
          line: 198,
          column: 5
        }
      },
      "37": {
        start: {
          line: 184,
          column: 6
        },
        end: {
          line: 189,
          column: 8
        }
      },
      "38": {
        start: {
          line: 192,
          column: 6
        },
        end: {
          line: 194,
          column: 7
        }
      },
      "39": {
        start: {
          line: 193,
          column: 8
        },
        end: {
          line: 193,
          column: 64
        }
      },
      "40": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 197,
          column: 59
        }
      },
      "41": {
        start: {
          line: 204,
          column: 27
        },
        end: {
          line: 216,
          column: 62
        }
      },
      "42": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 205,
          column: 78
        }
      },
      "43": {
        start: {
          line: 205,
          column: 71
        },
        end: {
          line: 205,
          column: 78
        }
      },
      "44": {
        start: {
          line: 207,
          column: 4
        },
        end: {
          line: 215,
          column: 5
        }
      },
      "45": {
        start: {
          line: 208,
          column: 6
        },
        end: {
          line: 212,
          column: 8
        }
      },
      "46": {
        start: {
          line: 214,
          column: 6
        },
        end: {
          line: 214,
          column: 59
        }
      },
      "47": {
        start: {
          line: 221,
          column: 21
        },
        end: {
          line: 231,
          column: 30
        }
      },
      "48": {
        start: {
          line: 222,
          column: 4
        },
        end: {
          line: 222,
          column: 43
        }
      },
      "49": {
        start: {
          line: 222,
          column: 36
        },
        end: {
          line: 222,
          column: 43
        }
      },
      "50": {
        start: {
          line: 224,
          column: 4
        },
        end: {
          line: 230,
          column: 5
        }
      },
      "51": {
        start: {
          line: 225,
          column: 6
        },
        end: {
          line: 225,
          column: 70
        }
      },
      "52": {
        start: {
          line: 226,
          column: 6
        },
        end: {
          line: 226,
          column: 32
        }
      },
      "53": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 51
        }
      },
      "54": {
        start: {
          line: 229,
          column: 6
        },
        end: {
          line: 229,
          column: 56
        }
      },
      "55": {
        start: {
          line: 236,
          column: 22
        },
        end: {
          line: 277,
          column: 41
        }
      },
      "56": {
        start: {
          line: 237,
          column: 4
        },
        end: {
          line: 237,
          column: 37
        }
      },
      "57": {
        start: {
          line: 237,
          column: 30
        },
        end: {
          line: 237,
          column: 37
        }
      },
      "58": {
        start: {
          line: 239,
          column: 4
        },
        end: {
          line: 276,
          column: 5
        }
      },
      "59": {
        start: {
          line: 240,
          column: 6
        },
        end: {
          line: 240,
          column: 58
        }
      },
      "60": {
        start: {
          line: 240,
          column: 24
        },
        end: {
          line: 240,
          column: 55
        }
      },
      "61": {
        start: {
          line: 242,
          column: 51
        },
        end: {
          line: 242,
          column: 53
        }
      },
      "62": {
        start: {
          line: 245,
          column: 6
        },
        end: {
          line: 249,
          column: 7
        }
      },
      "63": {
        start: {
          line: 246,
          column: 8
        },
        end: {
          line: 248,
          column: 10
        }
      },
      "64": {
        start: {
          line: 252,
          column: 6
        },
        end: {
          line: 256,
          column: 7
        }
      },
      "65": {
        start: {
          line: 253,
          column: 8
        },
        end: {
          line: 255,
          column: 10
        }
      },
      "66": {
        start: {
          line: 259,
          column: 6
        },
        end: {
          line: 263,
          column: 7
        }
      },
      "67": {
        start: {
          line: 260,
          column: 8
        },
        end: {
          line: 262,
          column: 10
        }
      },
      "68": {
        start: {
          line: 265,
          column: 6
        },
        end: {
          line: 265,
          column: 53
        }
      },
      "69": {
        start: {
          line: 268,
          column: 6
        },
        end: {
          line: 268,
          column: 28
        }
      },
      "70": {
        start: {
          line: 270,
          column: 6
        },
        end: {
          line: 270,
          column: 60
        }
      },
      "71": {
        start: {
          line: 273,
          column: 6
        },
        end: {
          line: 273,
          column: 65
        }
      },
      "72": {
        start: {
          line: 275,
          column: 6
        },
        end: {
          line: 275,
          column: 59
        }
      },
      "73": {
        start: {
          line: 275,
          column: 24
        },
        end: {
          line: 275,
          column: 56
        }
      },
      "74": {
        start: {
          line: 282,
          column: 23
        },
        end: {
          line: 284,
          column: 8
        }
      },
      "75": {
        start: {
          line: 283,
          column: 4
        },
        end: {
          line: 283,
          column: 51
        }
      },
      "76": {
        start: {
          line: 283,
          column: 23
        },
        end: {
          line: 283,
          column: 48
        }
      },
      "77": {
        start: {
          line: 289,
          column: 22
        },
        end: {
          line: 298,
          column: 44
        }
      },
      "78": {
        start: {
          line: 290,
          column: 4
        },
        end: {
          line: 290,
          column: 60
        }
      },
      "79": {
        start: {
          line: 290,
          column: 50
        },
        end: {
          line: 290,
          column: 60
        }
      },
      "80": {
        start: {
          line: 292,
          column: 4
        },
        end: {
          line: 297,
          column: 5
        }
      },
      "81": {
        start: {
          line: 293,
          column: 6
        },
        end: {
          line: 293,
          column: 69
        }
      },
      "82": {
        start: {
          line: 295,
          column: 6
        },
        end: {
          line: 295,
          column: 54
        }
      },
      "83": {
        start: {
          line: 296,
          column: 6
        },
        end: {
          line: 296,
          column: 16
        }
      },
      "84": {
        start: {
          line: 303,
          column: 25
        },
        end: {
          line: 332,
          column: 41
        }
      },
      "85": {
        start: {
          line: 304,
          column: 4
        },
        end: {
          line: 304,
          column: 49
        }
      },
      "86": {
        start: {
          line: 304,
          column: 39
        },
        end: {
          line: 304,
          column: 49
        }
      },
      "87": {
        start: {
          line: 306,
          column: 4
        },
        end: {
          line: 331,
          column: 5
        }
      },
      "88": {
        start: {
          line: 307,
          column: 33
        },
        end: {
          line: 307,
          column: 35
        }
      },
      "89": {
        start: {
          line: 310,
          column: 6
        },
        end: {
          line: 316,
          column: 7
        }
      },
      "90": {
        start: {
          line: 311,
          column: 33
        },
        end: {
          line: 311,
          column: 84
        }
      },
      "91": {
        start: {
          line: 312,
          column: 8
        },
        end: {
          line: 315,
          column: 13
        }
      },
      "92": {
        start: {
          line: 312,
          column: 55
        },
        end: {
          line: 315,
          column: 9
        }
      },
      "93": {
        start: {
          line: 319,
          column: 6
        },
        end: {
          line: 325,
          column: 7
        }
      },
      "94": {
        start: {
          line: 320,
          column: 36
        },
        end: {
          line: 320,
          column: 87
        }
      },
      "95": {
        start: {
          line: 321,
          column: 8
        },
        end: {
          line: 324,
          column: 13
        }
      },
      "96": {
        start: {
          line: 321,
          column: 58
        },
        end: {
          line: 324,
          column: 9
        }
      },
      "97": {
        start: {
          line: 327,
          column: 6
        },
        end: {
          line: 327,
          column: 25
        }
      },
      "98": {
        start: {
          line: 329,
          column: 6
        },
        end: {
          line: 329,
          column: 57
        }
      },
      "99": {
        start: {
          line: 330,
          column: 6
        },
        end: {
          line: 330,
          column: 16
        }
      },
      "100": {
        start: {
          line: 337,
          column: 24
        },
        end: {
          line: 396,
          column: 87
        }
      },
      "101": {
        start: {
          line: 338,
          column: 4
        },
        end: {
          line: 395,
          column: 5
        }
      },
      "102": {
        start: {
          line: 339,
          column: 53
        },
        end: {
          line: 339,
          column: 55
        }
      },
      "103": {
        start: {
          line: 342,
          column: 6
        },
        end: {
          line: 353,
          column: 7
        }
      },
      "104": {
        start: {
          line: 343,
          column: 31
        },
        end: {
          line: 343,
          column: 77
        }
      },
      "105": {
        start: {
          line: 344,
          column: 8
        },
        end: {
          line: 344,
          column: 69
        }
      },
      "106": {
        start: {
          line: 346,
          column: 34
        },
        end: {
          line: 346,
          column: 83
        }
      },
      "107": {
        start: {
          line: 347,
          column: 8
        },
        end: {
          line: 352,
          column: 10
        }
      },
      "108": {
        start: {
          line: 356,
          column: 6
        },
        end: {
          line: 359,
          column: 7
        }
      },
      "109": {
        start: {
          line: 357,
          column: 28
        },
        end: {
          line: 357,
          column: 77
        }
      },
      "110": {
        start: {
          line: 358,
          column: 8
        },
        end: {
          line: 358,
          column: 62
        }
      },
      "111": {
        start: {
          line: 362,
          column: 6
        },
        end: {
          line: 369,
          column: 7
        }
      },
      "112": {
        start: {
          line: 363,
          column: 27
        },
        end: {
          line: 363,
          column: 76
        }
      },
      "113": {
        start: {
          line: 364,
          column: 8
        },
        end: {
          line: 364,
          column: 57
        }
      },
      "114": {
        start: {
          line: 365,
          column: 8
        },
        end: {
          line: 368,
          column: 10
        }
      },
      "115": {
        start: {
          line: 372,
          column: 6
        },
        end: {
          line: 375,
          column: 7
        }
      },
      "116": {
        start: {
          line: 373,
          column: 29
        },
        end: {
          line: 373,
          column: 70
        }
      },
      "117": {
        start: {
          line: 374,
          column: 8
        },
        end: {
          line: 374,
          column: 65
        }
      },
      "118": {
        start: {
          line: 378,
          column: 6
        },
        end: {
          line: 381,
          column: 7
        }
      },
      "119": {
        start: {
          line: 379,
          column: 32
        },
        end: {
          line: 379,
          column: 85
        }
      },
      "120": {
        start: {
          line: 380,
          column: 8
        },
        end: {
          line: 380,
          column: 51
        }
      },
      "121": {
        start: {
          line: 384,
          column: 26
        },
        end: {
          line: 384,
          column: 48
        }
      },
      "122": {
        start: {
          line: 385,
          column: 6
        },
        end: {
          line: 389,
          column: 8
        }
      },
      "123": {
        start: {
          line: 386,
          column: 45
        },
        end: {
          line: 386,
          column: 63
        }
      },
      "124": {
        start: {
          line: 386,
          column: 74
        },
        end: {
          line: 386,
          column: 79
        }
      },
      "125": {
        start: {
          line: 387,
          column: 47
        },
        end: {
          line: 387,
          column: 68
        }
      },
      "126": {
        start: {
          line: 391,
          column: 6
        },
        end: {
          line: 391,
          column: 51
        }
      },
      "127": {
        start: {
          line: 391,
          column: 24
        },
        end: {
          line: 391,
          column: 48
        }
      },
      "128": {
        start: {
          line: 394,
          column: 6
        },
        end: {
          line: 394,
          column: 57
        }
      },
      "129": {
        start: {
          line: 399,
          column: 2
        },
        end: {
          line: 407,
          column: 95
        }
      },
      "130": {
        start: {
          line: 400,
          column: 4
        },
        end: {
          line: 400,
          column: 65
        }
      },
      "131": {
        start: {
          line: 400,
          column: 58
        },
        end: {
          line: 400,
          column: 65
        }
      },
      "132": {
        start: {
          line: 402,
          column: 21
        },
        end: {
          line: 404,
          column: 35
        }
      },
      "133": {
        start: {
          line: 403,
          column: 6
        },
        end: {
          line: 403,
          column: 20
        }
      },
      "134": {
        start: {
          line: 406,
          column: 4
        },
        end: {
          line: 406,
          column: 41
        }
      },
      "135": {
        start: {
          line: 406,
          column: 17
        },
        end: {
          line: 406,
          column: 40
        }
      },
      "136": {
        start: {
          line: 410,
          column: 2
        },
        end: {
          line: 418,
          column: 43
        }
      },
      "137": {
        start: {
          line: 411,
          column: 4
        },
        end: {
          line: 411,
          column: 37
        }
      },
      "138": {
        start: {
          line: 411,
          column: 30
        },
        end: {
          line: 411,
          column: 37
        }
      },
      "139": {
        start: {
          line: 413,
          column: 21
        },
        end: {
          line: 415,
          column: 13
        }
      },
      "140": {
        start: {
          line: 414,
          column: 6
        },
        end: {
          line: 414,
          column: 22
        }
      },
      "141": {
        start: {
          line: 417,
          column: 4
        },
        end: {
          line: 417,
          column: 41
        }
      },
      "142": {
        start: {
          line: 417,
          column: 17
        },
        end: {
          line: 417,
          column: 40
        }
      },
      "143": {
        start: {
          line: 421,
          column: 2
        },
        end: {
          line: 425,
          column: 46
        }
      },
      "144": {
        start: {
          line: 422,
          column: 4
        },
        end: {
          line: 424,
          column: 5
        }
      },
      "145": {
        start: {
          line: 423,
          column: 6
        },
        end: {
          line: 423,
          column: 19
        }
      },
      "146": {
        start: {
          line: 428,
          column: 2
        },
        end: {
          line: 454,
          column: 5
        }
      },
      "147": {
        start: {
          line: 428,
          column: 24
        },
        end: {
          line: 442,
          column: 3
        }
      },
      "148": {
        start: {
          line: 459,
          column: 15
        },
        end: {
          line: 459,
          column: 36
        }
      },
      "149": {
        start: {
          line: 460,
          column: 2
        },
        end: {
          line: 460,
          column: 31
        }
      },
      "150": {
        start: {
          line: 460,
          column: 16
        },
        end: {
          line: 460,
          column: 31
        }
      },
      "151": {
        start: {
          line: 461,
          column: 2
        },
        end: {
          line: 461,
          column: 34
        }
      },
      "152": {
        start: {
          line: 461,
          column: 17
        },
        end: {
          line: 461,
          column: 34
        }
      },
      "153": {
        start: {
          line: 462,
          column: 2
        },
        end: {
          line: 462,
          column: 36
        }
      },
      "154": {
        start: {
          line: 462,
          column: 17
        },
        end: {
          line: 462,
          column: 36
        }
      },
      "155": {
        start: {
          line: 463,
          column: 2
        },
        end: {
          line: 463,
          column: 34
        }
      },
      "156": {
        start: {
          line: 463,
          column: 17
        },
        end: {
          line: 463,
          column: 34
        }
      },
      "157": {
        start: {
          line: 464,
          column: 2
        },
        end: {
          line: 464,
          column: 17
        }
      }
    },
    fnMap: {
      "0": {
        name: "useAIOptimization",
        decl: {
          start: {
            line: 68,
            column: 16
          },
          end: {
            line: 68,
            column: 33
          }
        },
        loc: {
          start: {
            line: 70,
            column: 27
          },
          end: {
            line: 455,
            column: 1
          }
        },
        line: 70
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 112,
            column: 33
          },
          end: {
            line: 112,
            column: 34
          }
        },
        loc: {
          start: {
            line: 112,
            column: 45
          },
          end: {
            line: 148,
            column: 3
          }
        },
        line: 112
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 116,
            column: 15
          },
          end: {
            line: 116,
            column: 16
          }
        },
        loc: {
          start: {
            line: 116,
            column: 24
          },
          end: {
            line: 116,
            column: 55
          }
        },
        line: 116
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 136,
            column: 15
          },
          end: {
            line: 136,
            column: 16
          }
        },
        loc: {
          start: {
            line: 136,
            column: 24
          },
          end: {
            line: 140,
            column: 7
          }
        },
        line: 136
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 146,
            column: 15
          },
          end: {
            line: 146,
            column: 16
          }
        },
        loc: {
          start: {
            line: 146,
            column: 24
          },
          end: {
            line: 146,
            column: 56
          }
        },
        line: 146
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 153,
            column: 35
          },
          end: {
            line: 153,
            column: 36
          }
        },
        loc: {
          start: {
            line: 153,
            column: 53
          },
          end: {
            line: 175,
            column: 3
          }
        },
        line: 153
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 180,
            column: 38
          },
          end: {
            line: 180,
            column: 39
          }
        },
        loc: {
          start: {
            line: 180,
            column: 76
          },
          end: {
            line: 199,
            column: 3
          }
        },
        line: 180
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 204,
            column: 39
          },
          end: {
            line: 204,
            column: 40
          }
        },
        loc: {
          start: {
            line: 204,
            column: 61
          },
          end: {
            line: 216,
            column: 3
          }
        },
        line: 204
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 221,
            column: 33
          },
          end: {
            line: 221,
            column: 34
          }
        },
        loc: {
          start: {
            line: 221,
            column: 39
          },
          end: {
            line: 231,
            column: 3
          }
        },
        line: 221
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 236,
            column: 34
          },
          end: {
            line: 236,
            column: 35
          }
        },
        loc: {
          start: {
            line: 236,
            column: 46
          },
          end: {
            line: 277,
            column: 3
          }
        },
        line: 236
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 240,
            column: 15
          },
          end: {
            line: 240,
            column: 16
          }
        },
        loc: {
          start: {
            line: 240,
            column: 24
          },
          end: {
            line: 240,
            column: 55
          }
        },
        line: 240
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 275,
            column: 15
          },
          end: {
            line: 275,
            column: 16
          }
        },
        loc: {
          start: {
            line: 275,
            column: 24
          },
          end: {
            line: 275,
            column: 56
          }
        },
        line: 275
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 282,
            column: 35
          },
          end: {
            line: 282,
            column: 36
          }
        },
        loc: {
          start: {
            line: 282,
            column: 81
          },
          end: {
            line: 284,
            column: 3
          }
        },
        line: 282
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 283,
            column: 14
          },
          end: {
            line: 283,
            column: 15
          }
        },
        loc: {
          start: {
            line: 283,
            column: 23
          },
          end: {
            line: 283,
            column: 48
          }
        },
        line: 283
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 289,
            column: 34
          },
          end: {
            line: 289,
            column: 35
          }
        },
        loc: {
          start: {
            line: 289,
            column: 40
          },
          end: {
            line: 298,
            column: 3
          }
        },
        line: 289
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 303,
            column: 37
          },
          end: {
            line: 303,
            column: 38
          }
        },
        loc: {
          start: {
            line: 303,
            column: 49
          },
          end: {
            line: 332,
            column: 3
          }
        },
        line: 303
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 312,
            column: 49
          },
          end: {
            line: 312,
            column: 50
          }
        },
        loc: {
          start: {
            line: 312,
            column: 55
          },
          end: {
            line: 315,
            column: 9
          }
        },
        line: 312
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 321,
            column: 52
          },
          end: {
            line: 321,
            column: 53
          }
        },
        loc: {
          start: {
            line: 321,
            column: 58
          },
          end: {
            line: 324,
            column: 9
          }
        },
        line: 321
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 337,
            column: 36
          },
          end: {
            line: 337,
            column: 37
          }
        },
        loc: {
          start: {
            line: 337,
            column: 48
          },
          end: {
            line: 396,
            column: 3
          }
        },
        line: 337
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 386,
            column: 40
          },
          end: {
            line: 386,
            column: 41
          }
        },
        loc: {
          start: {
            line: 386,
            column: 45
          },
          end: {
            line: 386,
            column: 63
          }
        },
        line: 386
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 386,
            column: 69
          },
          end: {
            line: 386,
            column: 70
          }
        },
        loc: {
          start: {
            line: 386,
            column: 74
          },
          end: {
            line: 386,
            column: 79
          }
        },
        line: 386
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 387,
            column: 42
          },
          end: {
            line: 387,
            column: 43
          }
        },
        loc: {
          start: {
            line: 387,
            column: 47
          },
          end: {
            line: 387,
            column: 68
          }
        },
        line: 387
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 391,
            column: 15
          },
          end: {
            line: 391,
            column: 16
          }
        },
        loc: {
          start: {
            line: 391,
            column: 24
          },
          end: {
            line: 391,
            column: 48
          }
        },
        line: 391
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 399,
            column: 12
          },
          end: {
            line: 399,
            column: 13
          }
        },
        loc: {
          start: {
            line: 399,
            column: 18
          },
          end: {
            line: 407,
            column: 3
          }
        },
        line: 399
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 402,
            column: 33
          },
          end: {
            line: 402,
            column: 34
          }
        },
        loc: {
          start: {
            line: 402,
            column: 39
          },
          end: {
            line: 404,
            column: 5
          }
        },
        line: 402
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 406,
            column: 11
          },
          end: {
            line: 406,
            column: 12
          }
        },
        loc: {
          start: {
            line: 406,
            column: 17
          },
          end: {
            line: 406,
            column: 40
          }
        },
        line: 406
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 410,
            column: 12
          },
          end: {
            line: 410,
            column: 13
          }
        },
        loc: {
          start: {
            line: 410,
            column: 18
          },
          end: {
            line: 418,
            column: 3
          }
        },
        line: 410
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 413,
            column: 33
          },
          end: {
            line: 413,
            column: 34
          }
        },
        loc: {
          start: {
            line: 413,
            column: 39
          },
          end: {
            line: 415,
            column: 5
          }
        },
        line: 413
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 417,
            column: 11
          },
          end: {
            line: 417,
            column: 12
          }
        },
        loc: {
          start: {
            line: 417,
            column: 17
          },
          end: {
            line: 417,
            column: 40
          }
        },
        line: 417
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 421,
            column: 12
          },
          end: {
            line: 421,
            column: 13
          }
        },
        loc: {
          start: {
            line: 421,
            column: 18
          },
          end: {
            line: 425,
            column: 3
          }
        },
        line: 421
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 428,
            column: 17
          },
          end: {
            line: 428,
            column: 18
          }
        },
        loc: {
          start: {
            line: 428,
            column: 24
          },
          end: {
            line: 442,
            column: 3
          }
        },
        line: 428
      },
      "31": {
        name: "getTimeOfDay",
        decl: {
          start: {
            line: 458,
            column: 9
          },
          end: {
            line: 458,
            column: 21
          }
        },
        loc: {
          start: {
            line: 458,
            column: 71
          },
          end: {
            line: 465,
            column: 1
          }
        },
        line: 458
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 69,
            column: 2
          },
          end: {
            line: 69,
            column: 51
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 69,
            column: 49
          },
          end: {
            line: 69,
            column: 51
          }
        }],
        line: 69
      },
      "1": {
        loc: {
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 113,
            column: 45
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 113,
            column: 45
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "2": {
        loc: {
          start: {
            line: 113,
            column: 8
          },
          end: {
            line: 113,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 113,
            column: 8
          },
          end: {
            line: 113,
            column: 13
          }
        }, {
          start: {
            line: 113,
            column: 17
          },
          end: {
            line: 113,
            column: 36
          }
        }],
        line: 113
      },
      "3": {
        loc: {
          start: {
            line: 121,
            column: 6
          },
          end: {
            line: 124,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 121,
            column: 6
          },
          end: {
            line: 124,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 121
      },
      "4": {
        loc: {
          start: {
            line: 126,
            column: 6
          },
          end: {
            line: 129,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 126,
            column: 6
          },
          end: {
            line: 129,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 126
      },
      "5": {
        loc: {
          start: {
            line: 154,
            column: 4
          },
          end: {
            line: 154,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 154,
            column: 4
          },
          end: {
            line: 154,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 154
      },
      "6": {
        loc: {
          start: {
            line: 154,
            column: 8
          },
          end: {
            line: 154,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 154,
            column: 8
          },
          end: {
            line: 154,
            column: 13
          }
        }, {
          start: {
            line: 154,
            column: 17
          },
          end: {
            line: 154,
            column: 37
          }
        }],
        line: 154
      },
      "7": {
        loc: {
          start: {
            line: 181,
            column: 4
          },
          end: {
            line: 181,
            column: 78
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 181,
            column: 4
          },
          end: {
            line: 181,
            column: 78
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 181
      },
      "8": {
        loc: {
          start: {
            line: 181,
            column: 8
          },
          end: {
            line: 181,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 181,
            column: 8
          },
          end: {
            line: 181,
            column: 13
          }
        }, {
          start: {
            line: 181,
            column: 17
          },
          end: {
            line: 181,
            column: 34
          }
        }, {
          start: {
            line: 181,
            column: 38
          },
          end: {
            line: 181,
            column: 69
          }
        }],
        line: 181
      },
      "9": {
        loc: {
          start: {
            line: 192,
            column: 6
          },
          end: {
            line: 194,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 192,
            column: 6
          },
          end: {
            line: 194,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 192
      },
      "10": {
        loc: {
          start: {
            line: 205,
            column: 4
          },
          end: {
            line: 205,
            column: 78
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 205,
            column: 4
          },
          end: {
            line: 205,
            column: 78
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 205
      },
      "11": {
        loc: {
          start: {
            line: 205,
            column: 8
          },
          end: {
            line: 205,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 205,
            column: 8
          },
          end: {
            line: 205,
            column: 13
          }
        }, {
          start: {
            line: 205,
            column: 17
          },
          end: {
            line: 205,
            column: 34
          }
        }, {
          start: {
            line: 205,
            column: 38
          },
          end: {
            line: 205,
            column: 69
          }
        }],
        line: 205
      },
      "12": {
        loc: {
          start: {
            line: 222,
            column: 4
          },
          end: {
            line: 222,
            column: 43
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 222,
            column: 4
          },
          end: {
            line: 222,
            column: 43
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 222
      },
      "13": {
        loc: {
          start: {
            line: 222,
            column: 8
          },
          end: {
            line: 222,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 222,
            column: 8
          },
          end: {
            line: 222,
            column: 13
          }
        }, {
          start: {
            line: 222,
            column: 17
          },
          end: {
            line: 222,
            column: 34
          }
        }],
        line: 222
      },
      "14": {
        loc: {
          start: {
            line: 237,
            column: 4
          },
          end: {
            line: 237,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 237,
            column: 4
          },
          end: {
            line: 237,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 237
      },
      "15": {
        loc: {
          start: {
            line: 245,
            column: 6
          },
          end: {
            line: 249,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 6
          },
          end: {
            line: 249,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "16": {
        loc: {
          start: {
            line: 252,
            column: 6
          },
          end: {
            line: 256,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 252,
            column: 6
          },
          end: {
            line: 256,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 252
      },
      "17": {
        loc: {
          start: {
            line: 259,
            column: 6
          },
          end: {
            line: 263,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 259,
            column: 6
          },
          end: {
            line: 263,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 259
      },
      "18": {
        loc: {
          start: {
            line: 259,
            column: 10
          },
          end: {
            line: 259,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 259,
            column: 10
          },
          end: {
            line: 259,
            column: 38
          }
        }, {
          start: {
            line: 259,
            column: 42
          },
          end: {
            line: 259,
            column: 46
          }
        }],
        line: 259
      },
      "19": {
        loc: {
          start: {
            line: 290,
            column: 4
          },
          end: {
            line: 290,
            column: 60
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 290,
            column: 4
          },
          end: {
            line: 290,
            column: 60
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 290
      },
      "20": {
        loc: {
          start: {
            line: 290,
            column: 8
          },
          end: {
            line: 290,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 290,
            column: 8
          },
          end: {
            line: 290,
            column: 13
          }
        }, {
          start: {
            line: 290,
            column: 17
          },
          end: {
            line: 290,
            column: 48
          }
        }],
        line: 290
      },
      "21": {
        loc: {
          start: {
            line: 304,
            column: 4
          },
          end: {
            line: 304,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 304,
            column: 4
          },
          end: {
            line: 304,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 304
      },
      "22": {
        loc: {
          start: {
            line: 304,
            column: 8
          },
          end: {
            line: 304,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 304,
            column: 8
          },
          end: {
            line: 304,
            column: 13
          }
        }, {
          start: {
            line: 304,
            column: 17
          },
          end: {
            line: 304,
            column: 37
          }
        }],
        line: 304
      },
      "23": {
        loc: {
          start: {
            line: 310,
            column: 6
          },
          end: {
            line: 316,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 310,
            column: 6
          },
          end: {
            line: 316,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 310
      },
      "24": {
        loc: {
          start: {
            line: 319,
            column: 6
          },
          end: {
            line: 325,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 319,
            column: 6
          },
          end: {
            line: 325,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 319
      },
      "25": {
        loc: {
          start: {
            line: 342,
            column: 6
          },
          end: {
            line: 353,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 342,
            column: 6
          },
          end: {
            line: 353,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 342
      },
      "26": {
        loc: {
          start: {
            line: 344,
            column: 34
          },
          end: {
            line: 344,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 344,
            column: 34
          },
          end: {
            line: 344,
            column: 54
          }
        }, {
          start: {
            line: 344,
            column: 58
          },
          end: {
            line: 344,
            column: 68
          }
        }],
        line: 344
      },
      "27": {
        loc: {
          start: {
            line: 356,
            column: 6
          },
          end: {
            line: 359,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 356,
            column: 6
          },
          end: {
            line: 359,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 356
      },
      "28": {
        loc: {
          start: {
            line: 356,
            column: 10
          },
          end: {
            line: 356,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 356,
            column: 10
          },
          end: {
            line: 356,
            column: 40
          }
        }, {
          start: {
            line: 356,
            column: 44
          },
          end: {
            line: 356,
            column: 48
          }
        }],
        line: 356
      },
      "29": {
        loc: {
          start: {
            line: 358,
            column: 31
          },
          end: {
            line: 358,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 358,
            column: 31
          },
          end: {
            line: 358,
            column: 48
          }
        }, {
          start: {
            line: 358,
            column: 52
          },
          end: {
            line: 358,
            column: 61
          }
        }],
        line: 358
      },
      "30": {
        loc: {
          start: {
            line: 362,
            column: 6
          },
          end: {
            line: 369,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 362,
            column: 6
          },
          end: {
            line: 369,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 362
      },
      "31": {
        loc: {
          start: {
            line: 372,
            column: 6
          },
          end: {
            line: 375,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 372,
            column: 6
          },
          end: {
            line: 375,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 372
      },
      "32": {
        loc: {
          start: {
            line: 378,
            column: 6
          },
          end: {
            line: 381,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 378,
            column: 6
          },
          end: {
            line: 381,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 378
      },
      "33": {
        loc: {
          start: {
            line: 400,
            column: 4
          },
          end: {
            line: 400,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 400,
            column: 4
          },
          end: {
            line: 400,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 400
      },
      "34": {
        loc: {
          start: {
            line: 400,
            column: 8
          },
          end: {
            line: 400,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 400,
            column: 8
          },
          end: {
            line: 400,
            column: 32
          }
        }, {
          start: {
            line: 400,
            column: 36
          },
          end: {
            line: 400,
            column: 56
          }
        }],
        line: 400
      },
      "35": {
        loc: {
          start: {
            line: 411,
            column: 4
          },
          end: {
            line: 411,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 411,
            column: 4
          },
          end: {
            line: 411,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 411
      },
      "36": {
        loc: {
          start: {
            line: 422,
            column: 4
          },
          end: {
            line: 424,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 422,
            column: 4
          },
          end: {
            line: 424,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 422
      },
      "37": {
        loc: {
          start: {
            line: 422,
            column: 8
          },
          end: {
            line: 422,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 422,
            column: 8
          },
          end: {
            line: 422,
            column: 12
          }
        }, {
          start: {
            line: 422,
            column: 16
          },
          end: {
            line: 422,
            column: 36
          }
        }],
        line: 422
      },
      "38": {
        loc: {
          start: {
            line: 460,
            column: 2
          },
          end: {
            line: 460,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 460,
            column: 2
          },
          end: {
            line: 460,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 460
      },
      "39": {
        loc: {
          start: {
            line: 461,
            column: 2
          },
          end: {
            line: 461,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 461,
            column: 2
          },
          end: {
            line: 461,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 461
      },
      "40": {
        loc: {
          start: {
            line: 462,
            column: 2
          },
          end: {
            line: 462,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 462,
            column: 2
          },
          end: {
            line: 462,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 462
      },
      "41": {
        loc: {
          start: {
            line: 463,
            column: 2
          },
          end: {
            line: 463,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 463,
            column: 2
          },
          end: {
            line: 463,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 463
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "46b2e62558f0fdd139a13f53830110113335e5ed"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2crrnrtmks = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2crrnrtmks();
import { useState, useEffect, useCallback, useMemo } from 'react';
import { predictiveCacheEngine } from "../services/ai/PredictiveCacheEngine";
import { adaptivePerformanceManager } from "../services/ai/AdaptivePerformanceManager";
import { behavioralAnalyticsEngine } from "../services/ai/BehavioralAnalyticsEngine";
import { smartResourceManager } from "../services/ai/SmartResourceManager";
import { useAuth } from "../contexts/AuthContext";
export function useAIOptimization() {
  var initialConfig = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_2crrnrtmks().b[0][0]++, {});
  cov_2crrnrtmks().f[0]++;
  var _ref = (cov_2crrnrtmks().s[0]++, useAuth()),
    user = _ref.user;
  var _ref2 = (cov_2crrnrtmks().s[1]++, useState(null)),
    _ref3 = _slicedToArray(_ref2, 2),
    currentSessionId = _ref3[0],
    setCurrentSessionId = _ref3[1];
  var _ref4 = (cov_2crrnrtmks().s[2]++, useState(Object.assign({
      enablePredictiveCache: true,
      enableAdaptivePerformance: true,
      enableBehaviorAnalytics: true,
      enableResourceManagement: true,
      autoOptimization: true,
      optimizationInterval: 30000
    }, initialConfig))),
    _ref5 = _slicedToArray(_ref4, 2),
    config = _ref5[0],
    setConfig = _ref5[1];
  var _ref6 = (cov_2crrnrtmks().s[3]++, useState({
      isInitialized: false,
      isOptimizing: false,
      currentProfile: 'balanced',
      userSegment: 'unknown',
      resourceEfficiency: 0,
      cacheHitRate: 0,
      predictions: {
        nextActions: [],
        resourceUsage: [],
        performanceScore: 0
      },
      recommendations: {
        immediate: [],
        shortTerm: [],
        longTerm: []
      },
      metrics: {
        adaptationCount: 0,
        optimizationGains: 0,
        userSatisfaction: 0,
        systemHealth: 0
      }
    })),
    _ref7 = _slicedToArray(_ref6, 2),
    state = _ref7[0],
    setState = _ref7[1];
  var initialize = (cov_2crrnrtmks().s[4]++, useCallback(_asyncToGenerator(function* () {
    cov_2crrnrtmks().f[1]++;
    cov_2crrnrtmks().s[5]++;
    if ((cov_2crrnrtmks().b[2][0]++, !user) || (cov_2crrnrtmks().b[2][1]++, state.isInitialized)) {
      cov_2crrnrtmks().b[1][0]++;
      cov_2crrnrtmks().s[6]++;
      return;
    } else {
      cov_2crrnrtmks().b[1][1]++;
    }
    cov_2crrnrtmks().s[7]++;
    try {
      cov_2crrnrtmks().s[8]++;
      setState(function (prev) {
        cov_2crrnrtmks().f[2]++;
        cov_2crrnrtmks().s[9]++;
        return Object.assign({}, prev, {
          isOptimizing: true
        });
      });
      var initPromises = (cov_2crrnrtmks().s[10]++, []);
      cov_2crrnrtmks().s[11]++;
      if (config.enableAdaptivePerformance) {
        cov_2crrnrtmks().b[3][0]++;
        cov_2crrnrtmks().s[12]++;
        initPromises.push(Promise.resolve());
      } else {
        cov_2crrnrtmks().b[3][1]++;
      }
      cov_2crrnrtmks().s[13]++;
      if (config.enableResourceManagement) {
        cov_2crrnrtmks().b[4][0]++;
        cov_2crrnrtmks().s[14]++;
        initPromises.push(Promise.resolve());
      } else {
        cov_2crrnrtmks().b[4][1]++;
      }
      cov_2crrnrtmks().s[15]++;
      yield Promise.all(initPromises);
      cov_2crrnrtmks().s[16]++;
      yield updateAIState();
      cov_2crrnrtmks().s[17]++;
      setState(function (prev) {
        cov_2crrnrtmks().f[3]++;
        cov_2crrnrtmks().s[18]++;
        return Object.assign({}, prev, {
          isInitialized: true,
          isOptimizing: false
        });
      });
      cov_2crrnrtmks().s[19]++;
      console.log('AI Optimization systems initialized successfully');
    } catch (error) {
      cov_2crrnrtmks().s[20]++;
      console.error('Failed to initialize AI optimization:', error);
      cov_2crrnrtmks().s[21]++;
      setState(function (prev) {
        cov_2crrnrtmks().f[4]++;
        cov_2crrnrtmks().s[22]++;
        return Object.assign({}, prev, {
          isOptimizing: false
        });
      });
    }
  }), [user, state.isInitialized, config]));
  var startSession = (cov_2crrnrtmks().s[23]++, useCallback(function (context) {
    cov_2crrnrtmks().f[5]++;
    cov_2crrnrtmks().s[24]++;
    if ((cov_2crrnrtmks().b[6][0]++, !user) || (cov_2crrnrtmks().b[6][1]++, !state.isInitialized)) {
      cov_2crrnrtmks().b[5][0]++;
      cov_2crrnrtmks().s[25]++;
      return '';
    } else {
      cov_2crrnrtmks().b[5][1]++;
    }
    cov_2crrnrtmks().s[26]++;
    try {
      var sessionId = (cov_2crrnrtmks().s[27]++, behavioralAnalyticsEngine.startSession(user.id, Object.assign({
        timeOfDay: getTimeOfDay(),
        dayOfWeek: new Date().toLocaleDateString('en', {
          weekday: 'long'
        }),
        networkType: 'wifi',
        batteryLevel: 75,
        deviceOrientation: 'portrait',
        appVersion: '1.0.0'
      }, context)));
      cov_2crrnrtmks().s[28]++;
      setCurrentSessionId(sessionId);
      cov_2crrnrtmks().s[29]++;
      console.log(`Started AI optimization session: ${sessionId}`);
      cov_2crrnrtmks().s[30]++;
      return sessionId;
    } catch (error) {
      cov_2crrnrtmks().s[31]++;
      console.error('Failed to start AI session:', error);
      cov_2crrnrtmks().s[32]++;
      return '';
    }
  }, [user, state.isInitialized]));
  var trackScreenView = (cov_2crrnrtmks().s[33]++, useCallback(function (screen, loadTime) {
    cov_2crrnrtmks().f[6]++;
    cov_2crrnrtmks().s[34]++;
    if ((cov_2crrnrtmks().b[8][0]++, !user) || (cov_2crrnrtmks().b[8][1]++, !currentSessionId) || (cov_2crrnrtmks().b[8][2]++, !config.enableBehaviorAnalytics)) {
      cov_2crrnrtmks().b[7][0]++;
      cov_2crrnrtmks().s[35]++;
      return;
    } else {
      cov_2crrnrtmks().b[7][1]++;
    }
    cov_2crrnrtmks().s[36]++;
    try {
      cov_2crrnrtmks().s[37]++;
      behavioralAnalyticsEngine.trackScreenView(user.id, currentSessionId, screen, loadTime);
      cov_2crrnrtmks().s[38]++;
      if (config.enablePredictiveCache) {
        cov_2crrnrtmks().b[9][0]++;
        cov_2crrnrtmks().s[39]++;
        predictiveCacheEngine.executePredictiveCaching(user.id);
      } else {
        cov_2crrnrtmks().b[9][1]++;
      }
    } catch (error) {
      cov_2crrnrtmks().s[40]++;
      console.error('Failed to track screen view:', error);
    }
  }, [user, currentSessionId, config.enableBehaviorAnalytics, config.enablePredictiveCache]));
  var trackInteraction = (cov_2crrnrtmks().s[41]++, useCallback(function (interaction) {
    cov_2crrnrtmks().f[7]++;
    cov_2crrnrtmks().s[42]++;
    if ((cov_2crrnrtmks().b[11][0]++, !user) || (cov_2crrnrtmks().b[11][1]++, !currentSessionId) || (cov_2crrnrtmks().b[11][2]++, !config.enableBehaviorAnalytics)) {
      cov_2crrnrtmks().b[10][0]++;
      cov_2crrnrtmks().s[43]++;
      return;
    } else {
      cov_2crrnrtmks().b[10][1]++;
    }
    cov_2crrnrtmks().s[44]++;
    try {
      cov_2crrnrtmks().s[45]++;
      behavioralAnalyticsEngine.trackInteraction(user.id, currentSessionId, interaction);
    } catch (error) {
      cov_2crrnrtmks().s[46]++;
      console.error('Failed to track interaction:', error);
    }
  }, [user, currentSessionId, config.enableBehaviorAnalytics]));
  var endSession = (cov_2crrnrtmks().s[47]++, useCallback(function () {
    cov_2crrnrtmks().f[8]++;
    cov_2crrnrtmks().s[48]++;
    if ((cov_2crrnrtmks().b[13][0]++, !user) || (cov_2crrnrtmks().b[13][1]++, !currentSessionId)) {
      cov_2crrnrtmks().b[12][0]++;
      cov_2crrnrtmks().s[49]++;
      return;
    } else {
      cov_2crrnrtmks().b[12][1]++;
    }
    cov_2crrnrtmks().s[50]++;
    try {
      cov_2crrnrtmks().s[51]++;
      behavioralAnalyticsEngine.endSession(user.id, currentSessionId);
      cov_2crrnrtmks().s[52]++;
      setCurrentSessionId(null);
      cov_2crrnrtmks().s[53]++;
      console.log('Ended AI optimization session');
    } catch (error) {
      cov_2crrnrtmks().s[54]++;
      console.error('Failed to end AI session:', error);
    }
  }, [user, currentSessionId]));
  var optimizeNow = (cov_2crrnrtmks().s[55]++, useCallback(_asyncToGenerator(function* () {
    cov_2crrnrtmks().f[9]++;
    cov_2crrnrtmks().s[56]++;
    if (!state.isInitialized) {
      cov_2crrnrtmks().b[14][0]++;
      cov_2crrnrtmks().s[57]++;
      return;
    } else {
      cov_2crrnrtmks().b[14][1]++;
    }
    cov_2crrnrtmks().s[58]++;
    try {
      cov_2crrnrtmks().s[59]++;
      setState(function (prev) {
        cov_2crrnrtmks().f[10]++;
        cov_2crrnrtmks().s[60]++;
        return Object.assign({}, prev, {
          isOptimizing: true
        });
      });
      var optimizationPromises = (cov_2crrnrtmks().s[61]++, []);
      cov_2crrnrtmks().s[62]++;
      if (config.enableAdaptivePerformance) {
        cov_2crrnrtmks().b[15][0]++;
        cov_2crrnrtmks().s[63]++;
        optimizationPromises.push(adaptivePerformanceManager.reevaluatePerformance());
      } else {
        cov_2crrnrtmks().b[15][1]++;
      }
      cov_2crrnrtmks().s[64]++;
      if (config.enableResourceManagement) {
        cov_2crrnrtmks().b[16][0]++;
        cov_2crrnrtmks().s[65]++;
        optimizationPromises.push(smartResourceManager.optimizeResources());
      } else {
        cov_2crrnrtmks().b[16][1]++;
      }
      cov_2crrnrtmks().s[66]++;
      if ((cov_2crrnrtmks().b[18][0]++, config.enablePredictiveCache) && (cov_2crrnrtmks().b[18][1]++, user)) {
        cov_2crrnrtmks().b[17][0]++;
        cov_2crrnrtmks().s[67]++;
        optimizationPromises.push(predictiveCacheEngine.executePredictiveCaching(user.id));
      } else {
        cov_2crrnrtmks().b[17][1]++;
      }
      cov_2crrnrtmks().s[68]++;
      yield Promise.allSettled(optimizationPromises);
      cov_2crrnrtmks().s[69]++;
      yield updateAIState();
      cov_2crrnrtmks().s[70]++;
      console.log('AI optimization completed successfully');
    } catch (error) {
      cov_2crrnrtmks().s[71]++;
      console.error('Failed to execute AI optimization:', error);
    } finally {
      cov_2crrnrtmks().s[72]++;
      setState(function (prev) {
        cov_2crrnrtmks().f[11]++;
        cov_2crrnrtmks().s[73]++;
        return Object.assign({}, prev, {
          isOptimizing: false
        });
      });
    }
  }), [state.isInitialized, config, user]));
  var updateConfig = (cov_2crrnrtmks().s[74]++, useCallback(function (newConfig) {
    cov_2crrnrtmks().f[12]++;
    cov_2crrnrtmks().s[75]++;
    setConfig(function (prev) {
      cov_2crrnrtmks().f[13]++;
      cov_2crrnrtmks().s[76]++;
      return Object.assign({}, prev, newConfig);
    });
  }, []));
  var getInsights = (cov_2crrnrtmks().s[77]++, useCallback(function () {
    cov_2crrnrtmks().f[14]++;
    cov_2crrnrtmks().s[78]++;
    if ((cov_2crrnrtmks().b[20][0]++, !user) || (cov_2crrnrtmks().b[20][1]++, !config.enableBehaviorAnalytics)) {
      cov_2crrnrtmks().b[19][0]++;
      cov_2crrnrtmks().s[79]++;
      return [];
    } else {
      cov_2crrnrtmks().b[19][1]++;
    }
    cov_2crrnrtmks().s[80]++;
    try {
      cov_2crrnrtmks().s[81]++;
      return behavioralAnalyticsEngine.getAnalyticsInsights(user.id);
    } catch (error) {
      cov_2crrnrtmks().s[82]++;
      console.error('Failed to get insights:', error);
      cov_2crrnrtmks().s[83]++;
      return [];
    }
  }, [user, config.enableBehaviorAnalytics]));
  var getPredictions = (cov_2crrnrtmks().s[84]++, useCallback(_asyncToGenerator(function* () {
    cov_2crrnrtmks().f[15]++;
    cov_2crrnrtmks().s[85]++;
    if ((cov_2crrnrtmks().b[22][0]++, !user) || (cov_2crrnrtmks().b[22][1]++, !state.isInitialized)) {
      cov_2crrnrtmks().b[21][0]++;
      cov_2crrnrtmks().s[86]++;
      return [];
    } else {
      cov_2crrnrtmks().b[21][1]++;
    }
    cov_2crrnrtmks().s[87]++;
    try {
      var predictions = (cov_2crrnrtmks().s[88]++, []);
      cov_2crrnrtmks().s[89]++;
      if (config.enablePredictiveCache) {
        cov_2crrnrtmks().b[23][0]++;
        var cachePredictions = (cov_2crrnrtmks().s[90]++, yield predictiveCacheEngine.getPredictions(user.id));
        cov_2crrnrtmks().s[91]++;
        predictions.push.apply(predictions, _toConsumableArray(cachePredictions.map(function (p) {
          cov_2crrnrtmks().f[16]++;
          cov_2crrnrtmks().s[92]++;
          return Object.assign({
            type: 'cache'
          }, p);
        })));
      } else {
        cov_2crrnrtmks().b[23][1]++;
      }
      cov_2crrnrtmks().s[93]++;
      if (config.enableResourceManagement) {
        cov_2crrnrtmks().b[24][0]++;
        var resourcePredictions = (cov_2crrnrtmks().s[94]++, yield smartResourceManager.getResourcePredictions());
        cov_2crrnrtmks().s[95]++;
        predictions.push.apply(predictions, _toConsumableArray(resourcePredictions.map(function (p) {
          cov_2crrnrtmks().f[17]++;
          cov_2crrnrtmks().s[96]++;
          return Object.assign({
            type: 'resource'
          }, p);
        })));
      } else {
        cov_2crrnrtmks().b[24][1]++;
      }
      cov_2crrnrtmks().s[97]++;
      return predictions;
    } catch (error) {
      cov_2crrnrtmks().s[98]++;
      console.error('Failed to get predictions:', error);
      cov_2crrnrtmks().s[99]++;
      return [];
    }
  }), [user, state.isInitialized, config]));
  var updateAIState = (cov_2crrnrtmks().s[100]++, useCallback(_asyncToGenerator(function* () {
    cov_2crrnrtmks().f[18]++;
    cov_2crrnrtmks().s[101]++;
    try {
      var newState = (cov_2crrnrtmks().s[102]++, {});
      cov_2crrnrtmks().s[103]++;
      if (config.enableAdaptivePerformance) {
        cov_2crrnrtmks().b[25][0]++;
        var currentProfile = (cov_2crrnrtmks().s[104]++, adaptivePerformanceManager.getCurrentProfile());
        cov_2crrnrtmks().s[105]++;
        newState.currentProfile = (cov_2crrnrtmks().b[26][0]++, currentProfile == null ? void 0 : currentProfile.name) || (cov_2crrnrtmks().b[26][1]++, 'balanced');
        var adaptationMetrics = (cov_2crrnrtmks().s[106]++, adaptivePerformanceManager.getAdaptationMetrics());
        cov_2crrnrtmks().s[107]++;
        newState.metrics = Object.assign({}, state.metrics, {
          adaptationCount: adaptationMetrics.adaptationCount,
          optimizationGains: adaptationMetrics.averagePerformanceGain,
          userSatisfaction: adaptationMetrics.userSatisfaction
        });
      } else {
        cov_2crrnrtmks().b[25][1]++;
      }
      cov_2crrnrtmks().s[108]++;
      if ((cov_2crrnrtmks().b[28][0]++, config.enableBehaviorAnalytics) && (cov_2crrnrtmks().b[28][1]++, user)) {
        cov_2crrnrtmks().b[27][0]++;
        var userSegment = (cov_2crrnrtmks().s[109]++, behavioralAnalyticsEngine.getUserSegment(user.id));
        cov_2crrnrtmks().s[110]++;
        newState.userSegment = (cov_2crrnrtmks().b[29][0]++, userSegment == null ? void 0 : userSegment.name) || (cov_2crrnrtmks().b[29][1]++, 'unknown');
      } else {
        cov_2crrnrtmks().b[27][1]++;
      }
      cov_2crrnrtmks().s[111]++;
      if (config.enableResourceManagement) {
        cov_2crrnrtmks().b[30][0]++;
        var efficiency = (cov_2crrnrtmks().s[112]++, smartResourceManager.getResourceEfficiencyScore());
        cov_2crrnrtmks().s[113]++;
        newState.resourceEfficiency = efficiency.overall;
        cov_2crrnrtmks().s[114]++;
        newState.metrics = Object.assign({}, newState.metrics, {
          systemHealth: efficiency.overall
        });
      } else {
        cov_2crrnrtmks().b[30][1]++;
      }
      cov_2crrnrtmks().s[115]++;
      if (config.enablePredictiveCache) {
        cov_2crrnrtmks().b[31][0]++;
        var cacheMetrics = (cov_2crrnrtmks().s[116]++, predictiveCacheEngine.getCachingMetrics());
        cov_2crrnrtmks().s[117]++;
        newState.cacheHitRate = cacheMetrics.cacheHitImprovement;
      } else {
        cov_2crrnrtmks().b[31][1]++;
      }
      cov_2crrnrtmks().s[118]++;
      if (config.enableResourceManagement) {
        cov_2crrnrtmks().b[32][0]++;
        var recommendations = (cov_2crrnrtmks().s[119]++, smartResourceManager.getOptimizationRecommendations());
        cov_2crrnrtmks().s[120]++;
        newState.recommendations = recommendations;
      } else {
        cov_2crrnrtmks().b[32][1]++;
      }
      var predictions = (cov_2crrnrtmks().s[121]++, yield getPredictions());
      cov_2crrnrtmks().s[122]++;
      newState.predictions = {
        nextActions: predictions.filter(function (p) {
          cov_2crrnrtmks().f[19]++;
          cov_2crrnrtmks().s[123]++;
          return p.type === 'cache';
        }).map(function (p) {
          cov_2crrnrtmks().f[20]++;
          cov_2crrnrtmks().s[124]++;
          return p.key;
        }),
        resourceUsage: predictions.filter(function (p) {
          cov_2crrnrtmks().f[21]++;
          cov_2crrnrtmks().s[125]++;
          return p.type === 'resource';
        }),
        performanceScore: state.predictions.performanceScore
      };
      cov_2crrnrtmks().s[126]++;
      setState(function (prev) {
        cov_2crrnrtmks().f[22]++;
        cov_2crrnrtmks().s[127]++;
        return Object.assign({}, prev, newState);
      });
    } catch (error) {
      cov_2crrnrtmks().s[128]++;
      console.error('Failed to update AI state:', error);
    }
  }), [config, user, state.metrics, state.predictions.performanceScore, getPredictions]));
  cov_2crrnrtmks().s[129]++;
  useEffect(function () {
    cov_2crrnrtmks().f[23]++;
    cov_2crrnrtmks().s[130]++;
    if ((cov_2crrnrtmks().b[34][0]++, !config.autoOptimization) || (cov_2crrnrtmks().b[34][1]++, !state.isInitialized)) {
      cov_2crrnrtmks().b[33][0]++;
      cov_2crrnrtmks().s[131]++;
      return;
    } else {
      cov_2crrnrtmks().b[33][1]++;
    }
    var interval = (cov_2crrnrtmks().s[132]++, setInterval(function () {
      cov_2crrnrtmks().f[24]++;
      cov_2crrnrtmks().s[133]++;
      optimizeNow();
    }, config.optimizationInterval));
    cov_2crrnrtmks().s[134]++;
    return function () {
      cov_2crrnrtmks().f[25]++;
      cov_2crrnrtmks().s[135]++;
      return clearInterval(interval);
    };
  }, [config.autoOptimization, config.optimizationInterval, state.isInitialized, optimizeNow]);
  cov_2crrnrtmks().s[136]++;
  useEffect(function () {
    cov_2crrnrtmks().f[26]++;
    cov_2crrnrtmks().s[137]++;
    if (!state.isInitialized) {
      cov_2crrnrtmks().b[35][0]++;
      cov_2crrnrtmks().s[138]++;
      return;
    } else {
      cov_2crrnrtmks().b[35][1]++;
    }
    var interval = (cov_2crrnrtmks().s[139]++, setInterval(function () {
      cov_2crrnrtmks().f[27]++;
      cov_2crrnrtmks().s[140]++;
      updateAIState();
    }, 10000));
    cov_2crrnrtmks().s[141]++;
    return function () {
      cov_2crrnrtmks().f[28]++;
      cov_2crrnrtmks().s[142]++;
      return clearInterval(interval);
    };
  }, [state.isInitialized, updateAIState]);
  cov_2crrnrtmks().s[143]++;
  useEffect(function () {
    cov_2crrnrtmks().f[29]++;
    cov_2crrnrtmks().s[144]++;
    if ((cov_2crrnrtmks().b[37][0]++, user) && (cov_2crrnrtmks().b[37][1]++, !state.isInitialized)) {
      cov_2crrnrtmks().b[36][0]++;
      cov_2crrnrtmks().s[145]++;
      initialize();
    } else {
      cov_2crrnrtmks().b[36][1]++;
    }
  }, [user, state.isInitialized, initialize]);
  cov_2crrnrtmks().s[146]++;
  return useMemo(function () {
    cov_2crrnrtmks().f[30]++;
    cov_2crrnrtmks().s[147]++;
    return {
      state: state,
      actions: {
        initialize: initialize,
        startSession: startSession,
        trackScreenView: trackScreenView,
        trackInteraction: trackInteraction,
        endSession: endSession,
        optimizeNow: optimizeNow,
        updateConfig: updateConfig,
        getInsights: getInsights,
        getPredictions: getPredictions
      },
      config: config
    };
  }, [state, initialize, startSession, trackScreenView, trackInteraction, endSession, optimizeNow, updateConfig, getInsights, getPredictions, config]);
}
function getTimeOfDay() {
  cov_2crrnrtmks().f[31]++;
  var hour = (cov_2crrnrtmks().s[148]++, new Date().getHours());
  cov_2crrnrtmks().s[149]++;
  if (hour < 6) {
    cov_2crrnrtmks().b[38][0]++;
    cov_2crrnrtmks().s[150]++;
    return 'night';
  } else {
    cov_2crrnrtmks().b[38][1]++;
  }
  cov_2crrnrtmks().s[151]++;
  if (hour < 12) {
    cov_2crrnrtmks().b[39][0]++;
    cov_2crrnrtmks().s[152]++;
    return 'morning';
  } else {
    cov_2crrnrtmks().b[39][1]++;
  }
  cov_2crrnrtmks().s[153]++;
  if (hour < 18) {
    cov_2crrnrtmks().b[40][0]++;
    cov_2crrnrtmks().s[154]++;
    return 'afternoon';
  } else {
    cov_2crrnrtmks().b[40][1]++;
  }
  cov_2crrnrtmks().s[155]++;
  if (hour < 22) {
    cov_2crrnrtmks().b[41][0]++;
    cov_2crrnrtmks().s[156]++;
    return 'evening';
  } else {
    cov_2crrnrtmks().b[41][1]++;
  }
  cov_2crrnrtmks().s[157]++;
  return 'night';
}
export default useAIOptimization;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwidXNlTWVtbyIsInByZWRpY3RpdmVDYWNoZUVuZ2luZSIsImFkYXB0aXZlUGVyZm9ybWFuY2VNYW5hZ2VyIiwiYmVoYXZpb3JhbEFuYWx5dGljc0VuZ2luZSIsInNtYXJ0UmVzb3VyY2VNYW5hZ2VyIiwidXNlQXV0aCIsInVzZUFJT3B0aW1pemF0aW9uIiwiaW5pdGlhbENvbmZpZyIsImFyZ3VtZW50cyIsImxlbmd0aCIsInVuZGVmaW5lZCIsImNvdl8yY3JybnJ0bWtzIiwiYiIsImYiLCJfcmVmIiwicyIsInVzZXIiLCJfcmVmMiIsIl9yZWYzIiwiX3NsaWNlZFRvQXJyYXkiLCJjdXJyZW50U2Vzc2lvbklkIiwic2V0Q3VycmVudFNlc3Npb25JZCIsIl9yZWY0IiwiT2JqZWN0IiwiYXNzaWduIiwiZW5hYmxlUHJlZGljdGl2ZUNhY2hlIiwiZW5hYmxlQWRhcHRpdmVQZXJmb3JtYW5jZSIsImVuYWJsZUJlaGF2aW9yQW5hbHl0aWNzIiwiZW5hYmxlUmVzb3VyY2VNYW5hZ2VtZW50IiwiYXV0b09wdGltaXphdGlvbiIsIm9wdGltaXphdGlvbkludGVydmFsIiwiX3JlZjUiLCJjb25maWciLCJzZXRDb25maWciLCJfcmVmNiIsImlzSW5pdGlhbGl6ZWQiLCJpc09wdGltaXppbmciLCJjdXJyZW50UHJvZmlsZSIsInVzZXJTZWdtZW50IiwicmVzb3VyY2VFZmZpY2llbmN5IiwiY2FjaGVIaXRSYXRlIiwicHJlZGljdGlvbnMiLCJuZXh0QWN0aW9ucyIsInJlc291cmNlVXNhZ2UiLCJwZXJmb3JtYW5jZVNjb3JlIiwicmVjb21tZW5kYXRpb25zIiwiaW1tZWRpYXRlIiwic2hvcnRUZXJtIiwibG9uZ1Rlcm0iLCJtZXRyaWNzIiwiYWRhcHRhdGlvbkNvdW50Iiwib3B0aW1pemF0aW9uR2FpbnMiLCJ1c2VyU2F0aXNmYWN0aW9uIiwic3lzdGVtSGVhbHRoIiwiX3JlZjciLCJzdGF0ZSIsInNldFN0YXRlIiwiaW5pdGlhbGl6ZSIsIl9hc3luY1RvR2VuZXJhdG9yIiwicHJldiIsImluaXRQcm9taXNlcyIsInB1c2giLCJQcm9taXNlIiwicmVzb2x2ZSIsImFsbCIsInVwZGF0ZUFJU3RhdGUiLCJjb25zb2xlIiwibG9nIiwiZXJyb3IiLCJzdGFydFNlc3Npb24iLCJjb250ZXh0Iiwic2Vzc2lvbklkIiwiaWQiLCJ0aW1lT2ZEYXkiLCJnZXRUaW1lT2ZEYXkiLCJkYXlPZldlZWsiLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwid2Vla2RheSIsIm5ldHdvcmtUeXBlIiwiYmF0dGVyeUxldmVsIiwiZGV2aWNlT3JpZW50YXRpb24iLCJhcHBWZXJzaW9uIiwidHJhY2tTY3JlZW5WaWV3Iiwic2NyZWVuIiwibG9hZFRpbWUiLCJleGVjdXRlUHJlZGljdGl2ZUNhY2hpbmciLCJ0cmFja0ludGVyYWN0aW9uIiwiaW50ZXJhY3Rpb24iLCJlbmRTZXNzaW9uIiwib3B0aW1pemVOb3ciLCJvcHRpbWl6YXRpb25Qcm9taXNlcyIsInJlZXZhbHVhdGVQZXJmb3JtYW5jZSIsIm9wdGltaXplUmVzb3VyY2VzIiwiYWxsU2V0dGxlZCIsInVwZGF0ZUNvbmZpZyIsIm5ld0NvbmZpZyIsImdldEluc2lnaHRzIiwiZ2V0QW5hbHl0aWNzSW5zaWdodHMiLCJnZXRQcmVkaWN0aW9ucyIsImNhY2hlUHJlZGljdGlvbnMiLCJhcHBseSIsIl90b0NvbnN1bWFibGVBcnJheSIsIm1hcCIsInAiLCJ0eXBlIiwicmVzb3VyY2VQcmVkaWN0aW9ucyIsImdldFJlc291cmNlUHJlZGljdGlvbnMiLCJuZXdTdGF0ZSIsImdldEN1cnJlbnRQcm9maWxlIiwibmFtZSIsImFkYXB0YXRpb25NZXRyaWNzIiwiZ2V0QWRhcHRhdGlvbk1ldHJpY3MiLCJhdmVyYWdlUGVyZm9ybWFuY2VHYWluIiwiZ2V0VXNlclNlZ21lbnQiLCJlZmZpY2llbmN5IiwiZ2V0UmVzb3VyY2VFZmZpY2llbmN5U2NvcmUiLCJvdmVyYWxsIiwiY2FjaGVNZXRyaWNzIiwiZ2V0Q2FjaGluZ01ldHJpY3MiLCJjYWNoZUhpdEltcHJvdmVtZW50IiwiZ2V0T3B0aW1pemF0aW9uUmVjb21tZW5kYXRpb25zIiwiZmlsdGVyIiwia2V5IiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiLCJhY3Rpb25zIiwiaG91ciIsImdldEhvdXJzIl0sInNvdXJjZXMiOlsidXNlQUlPcHRpbWl6YXRpb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBBSSBPcHRpbWl6YXRpb24gSG9va1xuICogXG4gKiBVbmlmaWVkIGhvb2sgdGhhdCBpbnRlZ3JhdGVzIGFsbCBBSS1wb3dlcmVkIG9wdGltaXphdGlvbiBzeXN0ZW1zXG4gKiBmb3Igc2VhbWxlc3MgcGVyZm9ybWFuY2UgZW5oYW5jZW1lbnQgYW5kIGludGVsbGlnZW50IHVzZXIgZXhwZXJpZW5jZS5cbiAqL1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjaywgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHByZWRpY3RpdmVDYWNoZUVuZ2luZSB9IGZyb20gJ0Avc2VydmljZXMvYWkvUHJlZGljdGl2ZUNhY2hlRW5naW5lJztcbmltcG9ydCB7IGFkYXB0aXZlUGVyZm9ybWFuY2VNYW5hZ2VyIH0gZnJvbSAnQC9zZXJ2aWNlcy9haS9BZGFwdGl2ZVBlcmZvcm1hbmNlTWFuYWdlcic7XG5pbXBvcnQgeyBiZWhhdmlvcmFsQW5hbHl0aWNzRW5naW5lIH0gZnJvbSAnQC9zZXJ2aWNlcy9haS9CZWhhdmlvcmFsQW5hbHl0aWNzRW5naW5lJztcbmltcG9ydCB7IHNtYXJ0UmVzb3VyY2VNYW5hZ2VyIH0gZnJvbSAnQC9zZXJ2aWNlcy9haS9TbWFydFJlc291cmNlTWFuYWdlcic7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5cbmludGVyZmFjZSBBSU9wdGltaXphdGlvblN0YXRlIHtcbiAgaXNJbml0aWFsaXplZDogYm9vbGVhbjtcbiAgaXNPcHRpbWl6aW5nOiBib29sZWFuO1xuICBjdXJyZW50UHJvZmlsZTogc3RyaW5nO1xuICB1c2VyU2VnbWVudDogc3RyaW5nO1xuICByZXNvdXJjZUVmZmljaWVuY3k6IG51bWJlcjtcbiAgY2FjaGVIaXRSYXRlOiBudW1iZXI7XG4gIHByZWRpY3Rpb25zOiB7XG4gICAgbmV4dEFjdGlvbnM6IHN0cmluZ1tdO1xuICAgIHJlc291cmNlVXNhZ2U6IGFueVtdO1xuICAgIHBlcmZvcm1hbmNlU2NvcmU6IG51bWJlcjtcbiAgfTtcbiAgcmVjb21tZW5kYXRpb25zOiB7XG4gICAgaW1tZWRpYXRlOiBzdHJpbmdbXTtcbiAgICBzaG9ydFRlcm06IHN0cmluZ1tdO1xuICAgIGxvbmdUZXJtOiBzdHJpbmdbXTtcbiAgfTtcbiAgbWV0cmljczoge1xuICAgIGFkYXB0YXRpb25Db3VudDogbnVtYmVyO1xuICAgIG9wdGltaXphdGlvbkdhaW5zOiBudW1iZXI7XG4gICAgdXNlclNhdGlzZmFjdGlvbjogbnVtYmVyO1xuICAgIHN5c3RlbUhlYWx0aDogbnVtYmVyO1xuICB9O1xufVxuXG5pbnRlcmZhY2UgQUlPcHRpbWl6YXRpb25Db25maWcge1xuICBlbmFibGVQcmVkaWN0aXZlQ2FjaGU6IGJvb2xlYW47XG4gIGVuYWJsZUFkYXB0aXZlUGVyZm9ybWFuY2U6IGJvb2xlYW47XG4gIGVuYWJsZUJlaGF2aW9yQW5hbHl0aWNzOiBib29sZWFuO1xuICBlbmFibGVSZXNvdXJjZU1hbmFnZW1lbnQ6IGJvb2xlYW47XG4gIGF1dG9PcHRpbWl6YXRpb246IGJvb2xlYW47XG4gIG9wdGltaXphdGlvbkludGVydmFsOiBudW1iZXI7XG59XG5cbmludGVyZmFjZSBVc2VBSU9wdGltaXphdGlvblJldHVybiB7XG4gIHN0YXRlOiBBSU9wdGltaXphdGlvblN0YXRlO1xuICBhY3Rpb25zOiB7XG4gICAgaW5pdGlhbGl6ZTogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgICBzdGFydFNlc3Npb246IChjb250ZXh0OiBhbnkpID0+IHN0cmluZztcbiAgICB0cmFja1NjcmVlblZpZXc6IChzY3JlZW46IHN0cmluZywgbG9hZFRpbWU6IG51bWJlcikgPT4gdm9pZDtcbiAgICB0cmFja0ludGVyYWN0aW9uOiAoaW50ZXJhY3Rpb246IGFueSkgPT4gdm9pZDtcbiAgICBlbmRTZXNzaW9uOiAoKSA9PiB2b2lkO1xuICAgIG9wdGltaXplTm93OiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xuICAgIHVwZGF0ZUNvbmZpZzogKGNvbmZpZzogUGFydGlhbDxBSU9wdGltaXphdGlvbkNvbmZpZz4pID0+IHZvaWQ7XG4gICAgZ2V0SW5zaWdodHM6ICgpID0+IGFueVtdO1xuICAgIGdldFByZWRpY3Rpb25zOiAoKSA9PiBQcm9taXNlPGFueVtdPjtcbiAgfTtcbiAgY29uZmlnOiBBSU9wdGltaXphdGlvbkNvbmZpZztcbn1cblxuLyoqXG4gKiBBSS1Qb3dlcmVkIE9wdGltaXphdGlvbiBIb29rXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB1c2VBSU9wdGltaXphdGlvbihcbiAgaW5pdGlhbENvbmZpZzogUGFydGlhbDxBSU9wdGltaXphdGlvbkNvbmZpZz4gPSB7fVxuKTogVXNlQUlPcHRpbWl6YXRpb25SZXR1cm4ge1xuICBjb25zdCB7IHVzZXIgfSA9IHVzZUF1dGgoKTtcbiAgY29uc3QgW2N1cnJlbnRTZXNzaW9uSWQsIHNldEN1cnJlbnRTZXNzaW9uSWRdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIFxuICBjb25zdCBbY29uZmlnLCBzZXRDb25maWddID0gdXNlU3RhdGU8QUlPcHRpbWl6YXRpb25Db25maWc+KHtcbiAgICBlbmFibGVQcmVkaWN0aXZlQ2FjaGU6IHRydWUsXG4gICAgZW5hYmxlQWRhcHRpdmVQZXJmb3JtYW5jZTogdHJ1ZSxcbiAgICBlbmFibGVCZWhhdmlvckFuYWx5dGljczogdHJ1ZSxcbiAgICBlbmFibGVSZXNvdXJjZU1hbmFnZW1lbnQ6IHRydWUsXG4gICAgYXV0b09wdGltaXphdGlvbjogdHJ1ZSxcbiAgICBvcHRpbWl6YXRpb25JbnRlcnZhbDogMzAwMDAsIC8vIDMwIHNlY29uZHNcbiAgICAuLi5pbml0aWFsQ29uZmlnLFxuICB9KTtcblxuICBjb25zdCBbc3RhdGUsIHNldFN0YXRlXSA9IHVzZVN0YXRlPEFJT3B0aW1pemF0aW9uU3RhdGU+KHtcbiAgICBpc0luaXRpYWxpemVkOiBmYWxzZSxcbiAgICBpc09wdGltaXppbmc6IGZhbHNlLFxuICAgIGN1cnJlbnRQcm9maWxlOiAnYmFsYW5jZWQnLFxuICAgIHVzZXJTZWdtZW50OiAndW5rbm93bicsXG4gICAgcmVzb3VyY2VFZmZpY2llbmN5OiAwLFxuICAgIGNhY2hlSGl0UmF0ZTogMCxcbiAgICBwcmVkaWN0aW9uczoge1xuICAgICAgbmV4dEFjdGlvbnM6IFtdLFxuICAgICAgcmVzb3VyY2VVc2FnZTogW10sXG4gICAgICBwZXJmb3JtYW5jZVNjb3JlOiAwLFxuICAgIH0sXG4gICAgcmVjb21tZW5kYXRpb25zOiB7XG4gICAgICBpbW1lZGlhdGU6IFtdLFxuICAgICAgc2hvcnRUZXJtOiBbXSxcbiAgICAgIGxvbmdUZXJtOiBbXSxcbiAgICB9LFxuICAgIG1ldHJpY3M6IHtcbiAgICAgIGFkYXB0YXRpb25Db3VudDogMCxcbiAgICAgIG9wdGltaXphdGlvbkdhaW5zOiAwLFxuICAgICAgdXNlclNhdGlzZmFjdGlvbjogMCxcbiAgICAgIHN5c3RlbUhlYWx0aDogMCxcbiAgICB9LFxuICB9KTtcblxuICAvKipcbiAgICogSW5pdGlhbGl6ZSBhbGwgQUkgb3B0aW1pemF0aW9uIHN5c3RlbXNcbiAgICovXG4gIGNvbnN0IGluaXRpYWxpemUgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF1c2VyIHx8IHN0YXRlLmlzSW5pdGlhbGl6ZWQpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIGlzT3B0aW1pemluZzogdHJ1ZSB9KSk7XG5cbiAgICAgIC8vIEluaXRpYWxpemUgc3lzdGVtcyBiYXNlZCBvbiBjb25maWd1cmF0aW9uXG4gICAgICBjb25zdCBpbml0UHJvbWlzZXM6IFByb21pc2U8YW55PltdID0gW107XG5cbiAgICAgIGlmIChjb25maWcuZW5hYmxlQWRhcHRpdmVQZXJmb3JtYW5jZSkge1xuICAgICAgICAvLyBBZGFwdGl2ZSBwZXJmb3JtYW5jZSBpcyBhdXRvLWluaXRpYWxpemVkXG4gICAgICAgIGluaXRQcm9taXNlcy5wdXNoKFByb21pc2UucmVzb2x2ZSgpKTtcbiAgICAgIH1cblxuICAgICAgaWYgKGNvbmZpZy5lbmFibGVSZXNvdXJjZU1hbmFnZW1lbnQpIHtcbiAgICAgICAgLy8gUmVzb3VyY2UgbWFuYWdlciBpcyBhdXRvLWluaXRpYWxpemVkXG4gICAgICAgIGluaXRQcm9taXNlcy5wdXNoKFByb21pc2UucmVzb2x2ZSgpKTtcbiAgICAgIH1cblxuICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoaW5pdFByb21pc2VzKTtcblxuICAgICAgLy8gR2V0IGluaXRpYWwgc3RhdGVcbiAgICAgIGF3YWl0IHVwZGF0ZUFJU3RhdGUoKTtcblxuICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBpc0luaXRpYWxpemVkOiB0cnVlLFxuICAgICAgICBpc09wdGltaXppbmc6IGZhbHNlLFxuICAgICAgfSkpO1xuXG4gICAgICBjb25zb2xlLmxvZygnQUkgT3B0aW1pemF0aW9uIHN5c3RlbXMgaW5pdGlhbGl6ZWQgc3VjY2Vzc2Z1bGx5Jyk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGluaXRpYWxpemUgQUkgb3B0aW1pemF0aW9uOicsIGVycm9yKTtcbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHsgLi4ucHJldiwgaXNPcHRpbWl6aW5nOiBmYWxzZSB9KSk7XG4gICAgfVxuICB9LCBbdXNlciwgc3RhdGUuaXNJbml0aWFsaXplZCwgY29uZmlnXSk7XG5cbiAgLyoqXG4gICAqIFN0YXJ0IGEgbmV3IHVzZXIgc2Vzc2lvbiB3aXRoIGNvbnRleHRcbiAgICovXG4gIGNvbnN0IHN0YXJ0U2Vzc2lvbiA9IHVzZUNhbGxiYWNrKChjb250ZXh0OiBhbnkpID0+IHtcbiAgICBpZiAoIXVzZXIgfHwgIXN0YXRlLmlzSW5pdGlhbGl6ZWQpIHJldHVybiAnJztcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCBzZXNzaW9uSWQgPSBiZWhhdmlvcmFsQW5hbHl0aWNzRW5naW5lLnN0YXJ0U2Vzc2lvbih1c2VyLmlkLCB7XG4gICAgICAgIHRpbWVPZkRheTogZ2V0VGltZU9mRGF5KCksXG4gICAgICAgIGRheU9mV2VlazogbmV3IERhdGUoKS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuJywgeyB3ZWVrZGF5OiAnbG9uZycgfSksXG4gICAgICAgIG5ldHdvcmtUeXBlOiAnd2lmaScsIC8vIFdvdWxkIGRldGVjdCBhY3R1YWwgbmV0d29ya1xuICAgICAgICBiYXR0ZXJ5TGV2ZWw6IDc1LCAvLyBXb3VsZCBnZXQgYWN0dWFsIGJhdHRlcnkgbGV2ZWxcbiAgICAgICAgZGV2aWNlT3JpZW50YXRpb246ICdwb3J0cmFpdCcsXG4gICAgICAgIGFwcFZlcnNpb246ICcxLjAuMCcsXG4gICAgICAgIC4uLmNvbnRleHQsXG4gICAgICB9KTtcblxuICAgICAgc2V0Q3VycmVudFNlc3Npb25JZChzZXNzaW9uSWQpO1xuICAgICAgY29uc29sZS5sb2coYFN0YXJ0ZWQgQUkgb3B0aW1pemF0aW9uIHNlc3Npb246ICR7c2Vzc2lvbklkfWApO1xuICAgICAgXG4gICAgICByZXR1cm4gc2Vzc2lvbklkO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc3RhcnQgQUkgc2Vzc2lvbjonLCBlcnJvcik7XG4gICAgICByZXR1cm4gJyc7XG4gICAgfVxuICB9LCBbdXNlciwgc3RhdGUuaXNJbml0aWFsaXplZF0pO1xuXG4gIC8qKlxuICAgKiBUcmFjayBzY3JlZW4gdmlldyBmb3IgYmVoYXZpb3JhbCBhbmFseXRpY3NcbiAgICovXG4gIGNvbnN0IHRyYWNrU2NyZWVuVmlldyA9IHVzZUNhbGxiYWNrKChzY3JlZW46IHN0cmluZywgbG9hZFRpbWU6IG51bWJlcikgPT4ge1xuICAgIGlmICghdXNlciB8fCAhY3VycmVudFNlc3Npb25JZCB8fCAhY29uZmlnLmVuYWJsZUJlaGF2aW9yQW5hbHl0aWNzKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgYmVoYXZpb3JhbEFuYWx5dGljc0VuZ2luZS50cmFja1NjcmVlblZpZXcoXG4gICAgICAgIHVzZXIuaWQsXG4gICAgICAgIGN1cnJlbnRTZXNzaW9uSWQsXG4gICAgICAgIHNjcmVlbixcbiAgICAgICAgbG9hZFRpbWVcbiAgICAgICk7XG5cbiAgICAgIC8vIEV4ZWN1dGUgcHJlZGljdGl2ZSBjYWNoaW5nIGlmIGVuYWJsZWRcbiAgICAgIGlmIChjb25maWcuZW5hYmxlUHJlZGljdGl2ZUNhY2hlKSB7XG4gICAgICAgIHByZWRpY3RpdmVDYWNoZUVuZ2luZS5leGVjdXRlUHJlZGljdGl2ZUNhY2hpbmcodXNlci5pZCk7XG4gICAgICB9XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHRyYWNrIHNjcmVlbiB2aWV3OicsIGVycm9yKTtcbiAgICB9XG4gIH0sIFt1c2VyLCBjdXJyZW50U2Vzc2lvbklkLCBjb25maWcuZW5hYmxlQmVoYXZpb3JBbmFseXRpY3MsIGNvbmZpZy5lbmFibGVQcmVkaWN0aXZlQ2FjaGVdKTtcblxuICAvKipcbiAgICogVHJhY2sgdXNlciBpbnRlcmFjdGlvblxuICAgKi9cbiAgY29uc3QgdHJhY2tJbnRlcmFjdGlvbiA9IHVzZUNhbGxiYWNrKChpbnRlcmFjdGlvbjogYW55KSA9PiB7XG4gICAgaWYgKCF1c2VyIHx8ICFjdXJyZW50U2Vzc2lvbklkIHx8ICFjb25maWcuZW5hYmxlQmVoYXZpb3JBbmFseXRpY3MpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBiZWhhdmlvcmFsQW5hbHl0aWNzRW5naW5lLnRyYWNrSW50ZXJhY3Rpb24oXG4gICAgICAgIHVzZXIuaWQsXG4gICAgICAgIGN1cnJlbnRTZXNzaW9uSWQsXG4gICAgICAgIGludGVyYWN0aW9uXG4gICAgICApO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gdHJhY2sgaW50ZXJhY3Rpb246JywgZXJyb3IpO1xuICAgIH1cbiAgfSwgW3VzZXIsIGN1cnJlbnRTZXNzaW9uSWQsIGNvbmZpZy5lbmFibGVCZWhhdmlvckFuYWx5dGljc10pO1xuXG4gIC8qKlxuICAgKiBFbmQgY3VycmVudCBzZXNzaW9uXG4gICAqL1xuICBjb25zdCBlbmRTZXNzaW9uID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmICghdXNlciB8fCAhY3VycmVudFNlc3Npb25JZCkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGJlaGF2aW9yYWxBbmFseXRpY3NFbmdpbmUuZW5kU2Vzc2lvbih1c2VyLmlkLCBjdXJyZW50U2Vzc2lvbklkKTtcbiAgICAgIHNldEN1cnJlbnRTZXNzaW9uSWQobnVsbCk7XG4gICAgICBjb25zb2xlLmxvZygnRW5kZWQgQUkgb3B0aW1pemF0aW9uIHNlc3Npb24nKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGVuZCBBSSBzZXNzaW9uOicsIGVycm9yKTtcbiAgICB9XG4gIH0sIFt1c2VyLCBjdXJyZW50U2Vzc2lvbklkXSk7XG5cbiAgLyoqXG4gICAqIEZvcmNlIGltbWVkaWF0ZSBvcHRpbWl6YXRpb25cbiAgICovXG4gIGNvbnN0IG9wdGltaXplTm93ID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmICghc3RhdGUuaXNJbml0aWFsaXplZCkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHsgLi4ucHJldiwgaXNPcHRpbWl6aW5nOiB0cnVlIH0pKTtcblxuICAgICAgY29uc3Qgb3B0aW1pemF0aW9uUHJvbWlzZXM6IFByb21pc2U8YW55PltdID0gW107XG5cbiAgICAgIC8vIEFkYXB0aXZlIHBlcmZvcm1hbmNlIG9wdGltaXphdGlvblxuICAgICAgaWYgKGNvbmZpZy5lbmFibGVBZGFwdGl2ZVBlcmZvcm1hbmNlKSB7XG4gICAgICAgIG9wdGltaXphdGlvblByb21pc2VzLnB1c2goXG4gICAgICAgICAgYWRhcHRpdmVQZXJmb3JtYW5jZU1hbmFnZXIucmVldmFsdWF0ZVBlcmZvcm1hbmNlKClcbiAgICAgICAgKTtcbiAgICAgIH1cblxuICAgICAgLy8gUmVzb3VyY2Ugb3B0aW1pemF0aW9uXG4gICAgICBpZiAoY29uZmlnLmVuYWJsZVJlc291cmNlTWFuYWdlbWVudCkge1xuICAgICAgICBvcHRpbWl6YXRpb25Qcm9taXNlcy5wdXNoKFxuICAgICAgICAgIHNtYXJ0UmVzb3VyY2VNYW5hZ2VyLm9wdGltaXplUmVzb3VyY2VzKClcbiAgICAgICAgKTtcbiAgICAgIH1cblxuICAgICAgLy8gUHJlZGljdGl2ZSBjYWNoaW5nXG4gICAgICBpZiAoY29uZmlnLmVuYWJsZVByZWRpY3RpdmVDYWNoZSAmJiB1c2VyKSB7XG4gICAgICAgIG9wdGltaXphdGlvblByb21pc2VzLnB1c2goXG4gICAgICAgICAgcHJlZGljdGl2ZUNhY2hlRW5naW5lLmV4ZWN1dGVQcmVkaWN0aXZlQ2FjaGluZyh1c2VyLmlkKVxuICAgICAgICApO1xuICAgICAgfVxuXG4gICAgICBhd2FpdCBQcm9taXNlLmFsbFNldHRsZWQob3B0aW1pemF0aW9uUHJvbWlzZXMpO1xuXG4gICAgICAvLyBVcGRhdGUgc3RhdGUgd2l0aCBuZXcgbWV0cmljc1xuICAgICAgYXdhaXQgdXBkYXRlQUlTdGF0ZSgpO1xuXG4gICAgICBjb25zb2xlLmxvZygnQUkgb3B0aW1pemF0aW9uIGNvbXBsZXRlZCBzdWNjZXNzZnVsbHknKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZXhlY3V0ZSBBSSBvcHRpbWl6YXRpb246JywgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIGlzT3B0aW1pemluZzogZmFsc2UgfSkpO1xuICAgIH1cbiAgfSwgW3N0YXRlLmlzSW5pdGlhbGl6ZWQsIGNvbmZpZywgdXNlcl0pO1xuXG4gIC8qKlxuICAgKiBVcGRhdGUgY29uZmlndXJhdGlvblxuICAgKi9cbiAgY29uc3QgdXBkYXRlQ29uZmlnID0gdXNlQ2FsbGJhY2soKG5ld0NvbmZpZzogUGFydGlhbDxBSU9wdGltaXphdGlvbkNvbmZpZz4pID0+IHtcbiAgICBzZXRDb25maWcocHJldiA9PiAoeyAuLi5wcmV2LCAuLi5uZXdDb25maWcgfSkpO1xuICB9LCBbXSk7XG5cbiAgLyoqXG4gICAqIEdldCBhbmFseXRpY3MgaW5zaWdodHNcbiAgICovXG4gIGNvbnN0IGdldEluc2lnaHRzID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmICghdXNlciB8fCAhY29uZmlnLmVuYWJsZUJlaGF2aW9yQW5hbHl0aWNzKSByZXR1cm4gW107XG5cbiAgICB0cnkge1xuICAgICAgcmV0dXJuIGJlaGF2aW9yYWxBbmFseXRpY3NFbmdpbmUuZ2V0QW5hbHl0aWNzSW5zaWdodHModXNlci5pZCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBnZXQgaW5zaWdodHM6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH1cbiAgfSwgW3VzZXIsIGNvbmZpZy5lbmFibGVCZWhhdmlvckFuYWx5dGljc10pO1xuXG4gIC8qKlxuICAgKiBHZXQgQUkgcHJlZGljdGlvbnNcbiAgICovXG4gIGNvbnN0IGdldFByZWRpY3Rpb25zID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmICghdXNlciB8fCAhc3RhdGUuaXNJbml0aWFsaXplZCkgcmV0dXJuIFtdO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHByZWRpY3Rpb25zOiBhbnlbXSA9IFtdO1xuXG4gICAgICAvLyBDYWNoZSBwcmVkaWN0aW9uc1xuICAgICAgaWYgKGNvbmZpZy5lbmFibGVQcmVkaWN0aXZlQ2FjaGUpIHtcbiAgICAgICAgY29uc3QgY2FjaGVQcmVkaWN0aW9ucyA9IGF3YWl0IHByZWRpY3RpdmVDYWNoZUVuZ2luZS5nZXRQcmVkaWN0aW9ucyh1c2VyLmlkKTtcbiAgICAgICAgcHJlZGljdGlvbnMucHVzaCguLi5jYWNoZVByZWRpY3Rpb25zLm1hcChwID0+ICh7XG4gICAgICAgICAgdHlwZTogJ2NhY2hlJyxcbiAgICAgICAgICAuLi5wLFxuICAgICAgICB9KSkpO1xuICAgICAgfVxuXG4gICAgICAvLyBSZXNvdXJjZSBwcmVkaWN0aW9uc1xuICAgICAgaWYgKGNvbmZpZy5lbmFibGVSZXNvdXJjZU1hbmFnZW1lbnQpIHtcbiAgICAgICAgY29uc3QgcmVzb3VyY2VQcmVkaWN0aW9ucyA9IGF3YWl0IHNtYXJ0UmVzb3VyY2VNYW5hZ2VyLmdldFJlc291cmNlUHJlZGljdGlvbnMoKTtcbiAgICAgICAgcHJlZGljdGlvbnMucHVzaCguLi5yZXNvdXJjZVByZWRpY3Rpb25zLm1hcChwID0+ICh7XG4gICAgICAgICAgdHlwZTogJ3Jlc291cmNlJyxcbiAgICAgICAgICAuLi5wLFxuICAgICAgICB9KSkpO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gcHJlZGljdGlvbnM7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBnZXQgcHJlZGljdGlvbnM6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH1cbiAgfSwgW3VzZXIsIHN0YXRlLmlzSW5pdGlhbGl6ZWQsIGNvbmZpZ10pO1xuXG4gIC8qKlxuICAgKiBVcGRhdGUgQUkgc3RhdGUgd2l0aCBjdXJyZW50IG1ldHJpY3NcbiAgICovXG4gIGNvbnN0IHVwZGF0ZUFJU3RhdGUgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IG5ld1N0YXRlOiBQYXJ0aWFsPEFJT3B0aW1pemF0aW9uU3RhdGU+ID0ge307XG5cbiAgICAgIC8vIFBlcmZvcm1hbmNlIHByb2ZpbGVcbiAgICAgIGlmIChjb25maWcuZW5hYmxlQWRhcHRpdmVQZXJmb3JtYW5jZSkge1xuICAgICAgICBjb25zdCBjdXJyZW50UHJvZmlsZSA9IGFkYXB0aXZlUGVyZm9ybWFuY2VNYW5hZ2VyLmdldEN1cnJlbnRQcm9maWxlKCk7XG4gICAgICAgIG5ld1N0YXRlLmN1cnJlbnRQcm9maWxlID0gY3VycmVudFByb2ZpbGU/Lm5hbWUgfHwgJ2JhbGFuY2VkJztcbiAgICAgICAgXG4gICAgICAgIGNvbnN0IGFkYXB0YXRpb25NZXRyaWNzID0gYWRhcHRpdmVQZXJmb3JtYW5jZU1hbmFnZXIuZ2V0QWRhcHRhdGlvbk1ldHJpY3MoKTtcbiAgICAgICAgbmV3U3RhdGUubWV0cmljcyA9IHtcbiAgICAgICAgICAuLi5zdGF0ZS5tZXRyaWNzLFxuICAgICAgICAgIGFkYXB0YXRpb25Db3VudDogYWRhcHRhdGlvbk1ldHJpY3MuYWRhcHRhdGlvbkNvdW50LFxuICAgICAgICAgIG9wdGltaXphdGlvbkdhaW5zOiBhZGFwdGF0aW9uTWV0cmljcy5hdmVyYWdlUGVyZm9ybWFuY2VHYWluLFxuICAgICAgICAgIHVzZXJTYXRpc2ZhY3Rpb246IGFkYXB0YXRpb25NZXRyaWNzLnVzZXJTYXRpc2ZhY3Rpb24sXG4gICAgICAgIH07XG4gICAgICB9XG5cbiAgICAgIC8vIFVzZXIgc2VnbWVudFxuICAgICAgaWYgKGNvbmZpZy5lbmFibGVCZWhhdmlvckFuYWx5dGljcyAmJiB1c2VyKSB7XG4gICAgICAgIGNvbnN0IHVzZXJTZWdtZW50ID0gYmVoYXZpb3JhbEFuYWx5dGljc0VuZ2luZS5nZXRVc2VyU2VnbWVudCh1c2VyLmlkKTtcbiAgICAgICAgbmV3U3RhdGUudXNlclNlZ21lbnQgPSB1c2VyU2VnbWVudD8ubmFtZSB8fCAndW5rbm93bic7XG4gICAgICB9XG5cbiAgICAgIC8vIFJlc291cmNlIGVmZmljaWVuY3lcbiAgICAgIGlmIChjb25maWcuZW5hYmxlUmVzb3VyY2VNYW5hZ2VtZW50KSB7XG4gICAgICAgIGNvbnN0IGVmZmljaWVuY3kgPSBzbWFydFJlc291cmNlTWFuYWdlci5nZXRSZXNvdXJjZUVmZmljaWVuY3lTY29yZSgpO1xuICAgICAgICBuZXdTdGF0ZS5yZXNvdXJjZUVmZmljaWVuY3kgPSBlZmZpY2llbmN5Lm92ZXJhbGw7XG4gICAgICAgIG5ld1N0YXRlLm1ldHJpY3MgPSB7XG4gICAgICAgICAgLi4ubmV3U3RhdGUubWV0cmljcyxcbiAgICAgICAgICBzeXN0ZW1IZWFsdGg6IGVmZmljaWVuY3kub3ZlcmFsbCxcbiAgICAgICAgfTtcbiAgICAgIH1cblxuICAgICAgLy8gQ2FjaGUgbWV0cmljc1xuICAgICAgaWYgKGNvbmZpZy5lbmFibGVQcmVkaWN0aXZlQ2FjaGUpIHtcbiAgICAgICAgY29uc3QgY2FjaGVNZXRyaWNzID0gcHJlZGljdGl2ZUNhY2hlRW5naW5lLmdldENhY2hpbmdNZXRyaWNzKCk7XG4gICAgICAgIG5ld1N0YXRlLmNhY2hlSGl0UmF0ZSA9IGNhY2hlTWV0cmljcy5jYWNoZUhpdEltcHJvdmVtZW50O1xuICAgICAgfVxuXG4gICAgICAvLyBSZWNvbW1lbmRhdGlvbnNcbiAgICAgIGlmIChjb25maWcuZW5hYmxlUmVzb3VyY2VNYW5hZ2VtZW50KSB7XG4gICAgICAgIGNvbnN0IHJlY29tbWVuZGF0aW9ucyA9IHNtYXJ0UmVzb3VyY2VNYW5hZ2VyLmdldE9wdGltaXphdGlvblJlY29tbWVuZGF0aW9ucygpO1xuICAgICAgICBuZXdTdGF0ZS5yZWNvbW1lbmRhdGlvbnMgPSByZWNvbW1lbmRhdGlvbnM7XG4gICAgICB9XG5cbiAgICAgIC8vIFByZWRpY3Rpb25zXG4gICAgICBjb25zdCBwcmVkaWN0aW9ucyA9IGF3YWl0IGdldFByZWRpY3Rpb25zKCk7XG4gICAgICBuZXdTdGF0ZS5wcmVkaWN0aW9ucyA9IHtcbiAgICAgICAgbmV4dEFjdGlvbnM6IHByZWRpY3Rpb25zLmZpbHRlcihwID0+IHAudHlwZSA9PT0gJ2NhY2hlJykubWFwKHAgPT4gcC5rZXkpLFxuICAgICAgICByZXNvdXJjZVVzYWdlOiBwcmVkaWN0aW9ucy5maWx0ZXIocCA9PiBwLnR5cGUgPT09ICdyZXNvdXJjZScpLFxuICAgICAgICBwZXJmb3JtYW5jZVNjb3JlOiBzdGF0ZS5wcmVkaWN0aW9ucy5wZXJmb3JtYW5jZVNjb3JlLCAvLyBXb3VsZCBjYWxjdWxhdGUgZnJvbSBtZXRyaWNzXG4gICAgICB9O1xuXG4gICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIC4uLm5ld1N0YXRlIH0pKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gdXBkYXRlIEFJIHN0YXRlOicsIGVycm9yKTtcbiAgICB9XG4gIH0sIFtjb25maWcsIHVzZXIsIHN0YXRlLm1ldHJpY3MsIHN0YXRlLnByZWRpY3Rpb25zLnBlcmZvcm1hbmNlU2NvcmUsIGdldFByZWRpY3Rpb25zXSk7XG5cbiAgLy8gQXV0by1vcHRpbWl6YXRpb24gZWZmZWN0XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFjb25maWcuYXV0b09wdGltaXphdGlvbiB8fCAhc3RhdGUuaXNJbml0aWFsaXplZCkgcmV0dXJuO1xuXG4gICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICBvcHRpbWl6ZU5vdygpO1xuICAgIH0sIGNvbmZpZy5vcHRpbWl6YXRpb25JbnRlcnZhbCk7XG5cbiAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XG4gIH0sIFtjb25maWcuYXV0b09wdGltaXphdGlvbiwgY29uZmlnLm9wdGltaXphdGlvbkludGVydmFsLCBzdGF0ZS5pc0luaXRpYWxpemVkLCBvcHRpbWl6ZU5vd10pO1xuXG4gIC8vIFBlcmlvZGljIHN0YXRlIHVwZGF0ZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIXN0YXRlLmlzSW5pdGlhbGl6ZWQpIHJldHVybjtcblxuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgdXBkYXRlQUlTdGF0ZSgpO1xuICAgIH0sIDEwMDAwKTsgLy8gVXBkYXRlIGV2ZXJ5IDEwIHNlY29uZHNcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgfSwgW3N0YXRlLmlzSW5pdGlhbGl6ZWQsIHVwZGF0ZUFJU3RhdGVdKTtcblxuICAvLyBJbml0aWFsaXplIG9uIG1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHVzZXIgJiYgIXN0YXRlLmlzSW5pdGlhbGl6ZWQpIHtcbiAgICAgIGluaXRpYWxpemUoKTtcbiAgICB9XG4gIH0sIFt1c2VyLCBzdGF0ZS5pc0luaXRpYWxpemVkLCBpbml0aWFsaXplXSk7XG5cbiAgLy8gTWVtb2l6ZWQgcmV0dXJuIHZhbHVlXG4gIHJldHVybiB1c2VNZW1vKCgpID0+ICh7XG4gICAgc3RhdGUsXG4gICAgYWN0aW9uczoge1xuICAgICAgaW5pdGlhbGl6ZSxcbiAgICAgIHN0YXJ0U2Vzc2lvbixcbiAgICAgIHRyYWNrU2NyZWVuVmlldyxcbiAgICAgIHRyYWNrSW50ZXJhY3Rpb24sXG4gICAgICBlbmRTZXNzaW9uLFxuICAgICAgb3B0aW1pemVOb3csXG4gICAgICB1cGRhdGVDb25maWcsXG4gICAgICBnZXRJbnNpZ2h0cyxcbiAgICAgIGdldFByZWRpY3Rpb25zLFxuICAgIH0sXG4gICAgY29uZmlnLFxuICB9KSwgW1xuICAgIHN0YXRlLFxuICAgIGluaXRpYWxpemUsXG4gICAgc3RhcnRTZXNzaW9uLFxuICAgIHRyYWNrU2NyZWVuVmlldyxcbiAgICB0cmFja0ludGVyYWN0aW9uLFxuICAgIGVuZFNlc3Npb24sXG4gICAgb3B0aW1pemVOb3csXG4gICAgdXBkYXRlQ29uZmlnLFxuICAgIGdldEluc2lnaHRzLFxuICAgIGdldFByZWRpY3Rpb25zLFxuICAgIGNvbmZpZyxcbiAgXSk7XG59XG5cbi8vIEhlbHBlciBmdW5jdGlvbnNcbmZ1bmN0aW9uIGdldFRpbWVPZkRheSgpOiAnbW9ybmluZycgfCAnYWZ0ZXJub29uJyB8ICdldmVuaW5nJyB8ICduaWdodCcge1xuICBjb25zdCBob3VyID0gbmV3IERhdGUoKS5nZXRIb3VycygpO1xuICBpZiAoaG91ciA8IDYpIHJldHVybiAnbmlnaHQnO1xuICBpZiAoaG91ciA8IDEyKSByZXR1cm4gJ21vcm5pbmcnO1xuICBpZiAoaG91ciA8IDE4KSByZXR1cm4gJ2FmdGVybm9vbic7XG4gIGlmIChob3VyIDwgMjIpIHJldHVybiAnZXZlbmluZyc7XG4gIHJldHVybiAnbmlnaHQnO1xufVxuXG5leHBvcnQgZGVmYXVsdCB1c2VBSU9wdGltaXphdGlvbjtcbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPQSxTQUFTQSxRQUFRLEVBQUVDLFNBQVMsRUFBRUMsV0FBVyxFQUFFQyxPQUFPLFFBQVEsT0FBTztBQUNqRSxTQUFTQyxxQkFBcUI7QUFDOUIsU0FBU0MsMEJBQTBCO0FBQ25DLFNBQVNDLHlCQUF5QjtBQUNsQyxTQUFTQyxvQkFBb0I7QUFDN0IsU0FBU0MsT0FBTztBQXVEaEIsT0FBTyxTQUFTQyxpQkFBaUJBLENBQUEsRUFFTjtFQUFBLElBRHpCQyxhQUE0QyxHQUFBQyxTQUFBLENBQUFDLE1BQUEsUUFBQUQsU0FBQSxRQUFBRSxTQUFBLEdBQUFGLFNBQUEsT0FBQUcsY0FBQSxHQUFBQyxDQUFBLFVBQUcsQ0FBQyxDQUFDO0VBQUFELGNBQUEsR0FBQUUsQ0FBQTtFQUVqRCxJQUFBQyxJQUFBLElBQUFILGNBQUEsR0FBQUksQ0FBQSxPQUFpQlYsT0FBTyxDQUFDLENBQUM7SUFBbEJXLElBQUksR0FBQUYsSUFBQSxDQUFKRSxJQUFJO0VBQ1osSUFBQUMsS0FBQSxJQUFBTixjQUFBLEdBQUFJLENBQUEsT0FBZ0RsQixRQUFRLENBQWdCLElBQUksQ0FBQztJQUFBcUIsS0FBQSxHQUFBQyxjQUFBLENBQUFGLEtBQUE7SUFBdEVHLGdCQUFnQixHQUFBRixLQUFBO0lBQUVHLG1CQUFtQixHQUFBSCxLQUFBO0VBRTVDLElBQUFJLEtBQUEsSUFBQVgsY0FBQSxHQUFBSSxDQUFBLE9BQTRCbEIsUUFBUSxDQUFBMEIsTUFBQSxDQUFBQyxNQUFBO01BQ2xDQyxxQkFBcUIsRUFBRSxJQUFJO01BQzNCQyx5QkFBeUIsRUFBRSxJQUFJO01BQy9CQyx1QkFBdUIsRUFBRSxJQUFJO01BQzdCQyx3QkFBd0IsRUFBRSxJQUFJO01BQzlCQyxnQkFBZ0IsRUFBRSxJQUFJO01BQ3RCQyxvQkFBb0IsRUFBRTtJQUFLLEdBQ3hCdkIsYUFBYSxDQUNqQixDQUFDO0lBQUF3QixLQUFBLEdBQUFaLGNBQUEsQ0FBQUcsS0FBQTtJQVJLVSxNQUFNLEdBQUFELEtBQUE7SUFBRUUsU0FBUyxHQUFBRixLQUFBO0VBVXhCLElBQUFHLEtBQUEsSUFBQXZCLGNBQUEsR0FBQUksQ0FBQSxPQUEwQmxCLFFBQVEsQ0FBc0I7TUFDdERzQyxhQUFhLEVBQUUsS0FBSztNQUNwQkMsWUFBWSxFQUFFLEtBQUs7TUFDbkJDLGNBQWMsRUFBRSxVQUFVO01BQzFCQyxXQUFXLEVBQUUsU0FBUztNQUN0QkMsa0JBQWtCLEVBQUUsQ0FBQztNQUNyQkMsWUFBWSxFQUFFLENBQUM7TUFDZkMsV0FBVyxFQUFFO1FBQ1hDLFdBQVcsRUFBRSxFQUFFO1FBQ2ZDLGFBQWEsRUFBRSxFQUFFO1FBQ2pCQyxnQkFBZ0IsRUFBRTtNQUNwQixDQUFDO01BQ0RDLGVBQWUsRUFBRTtRQUNmQyxTQUFTLEVBQUUsRUFBRTtRQUNiQyxTQUFTLEVBQUUsRUFBRTtRQUNiQyxRQUFRLEVBQUU7TUFDWixDQUFDO01BQ0RDLE9BQU8sRUFBRTtRQUNQQyxlQUFlLEVBQUUsQ0FBQztRQUNsQkMsaUJBQWlCLEVBQUUsQ0FBQztRQUNwQkMsZ0JBQWdCLEVBQUUsQ0FBQztRQUNuQkMsWUFBWSxFQUFFO01BQ2hCO0lBQ0YsQ0FBQyxDQUFDO0lBQUFDLEtBQUEsR0FBQW5DLGNBQUEsQ0FBQWUsS0FBQTtJQXZCS3FCLEtBQUssR0FBQUQsS0FBQTtJQUFFRSxRQUFRLEdBQUFGLEtBQUE7RUE0QnRCLElBQU1HLFVBQVUsSUFBQTlDLGNBQUEsR0FBQUksQ0FBQSxPQUFHaEIsV0FBVyxDQUFBMkQsaUJBQUEsQ0FBQyxhQUFZO0lBQUEvQyxjQUFBLEdBQUFFLENBQUE7SUFBQUYsY0FBQSxHQUFBSSxDQUFBO0lBQ3pDLElBQUksQ0FBQUosY0FBQSxHQUFBQyxDQUFBLFdBQUNJLElBQUksTUFBQUwsY0FBQSxHQUFBQyxDQUFBLFVBQUkyQyxLQUFLLENBQUNwQixhQUFhLEdBQUU7TUFBQXhCLGNBQUEsR0FBQUMsQ0FBQTtNQUFBRCxjQUFBLEdBQUFJLENBQUE7TUFBQTtJQUFNLENBQUM7TUFBQUosY0FBQSxHQUFBQyxDQUFBO0lBQUE7SUFBQUQsY0FBQSxHQUFBSSxDQUFBO0lBRXpDLElBQUk7TUFBQUosY0FBQSxHQUFBSSxDQUFBO01BQ0Z5QyxRQUFRLENBQUMsVUFBQUcsSUFBSSxFQUFLO1FBQUFoRCxjQUFBLEdBQUFFLENBQUE7UUFBQUYsY0FBQSxHQUFBSSxDQUFBO1FBQUEsT0FBQVEsTUFBQSxDQUFBQyxNQUFBLEtBQUttQyxJQUFJO1VBQUV2QixZQUFZLEVBQUU7UUFBSTtNQUFDLENBQUUsQ0FBQztNQUduRCxJQUFNd0IsWUFBNEIsSUFBQWpELGNBQUEsR0FBQUksQ0FBQSxRQUFHLEVBQUU7TUFBQ0osY0FBQSxHQUFBSSxDQUFBO01BRXhDLElBQUlpQixNQUFNLENBQUNOLHlCQUF5QixFQUFFO1FBQUFmLGNBQUEsR0FBQUMsQ0FBQTtRQUFBRCxjQUFBLEdBQUFJLENBQUE7UUFFcEM2QyxZQUFZLENBQUNDLElBQUksQ0FBQ0MsT0FBTyxDQUFDQyxPQUFPLENBQUMsQ0FBQyxDQUFDO01BQ3RDLENBQUM7UUFBQXBELGNBQUEsR0FBQUMsQ0FBQTtNQUFBO01BQUFELGNBQUEsR0FBQUksQ0FBQTtNQUVELElBQUlpQixNQUFNLENBQUNKLHdCQUF3QixFQUFFO1FBQUFqQixjQUFBLEdBQUFDLENBQUE7UUFBQUQsY0FBQSxHQUFBSSxDQUFBO1FBRW5DNkMsWUFBWSxDQUFDQyxJQUFJLENBQUNDLE9BQU8sQ0FBQ0MsT0FBTyxDQUFDLENBQUMsQ0FBQztNQUN0QyxDQUFDO1FBQUFwRCxjQUFBLEdBQUFDLENBQUE7TUFBQTtNQUFBRCxjQUFBLEdBQUFJLENBQUE7TUFFRCxNQUFNK0MsT0FBTyxDQUFDRSxHQUFHLENBQUNKLFlBQVksQ0FBQztNQUFDakQsY0FBQSxHQUFBSSxDQUFBO01BR2hDLE1BQU1rRCxhQUFhLENBQUMsQ0FBQztNQUFDdEQsY0FBQSxHQUFBSSxDQUFBO01BRXRCeUMsUUFBUSxDQUFDLFVBQUFHLElBQUksRUFBSztRQUFBaEQsY0FBQSxHQUFBRSxDQUFBO1FBQUFGLGNBQUEsR0FBQUksQ0FBQTtRQUFBLE9BQUFRLE1BQUEsQ0FBQUMsTUFBQSxLQUNibUMsSUFBSTtVQUNQeEIsYUFBYSxFQUFFLElBQUk7VUFDbkJDLFlBQVksRUFBRTtRQUFLO01BQ3JCLENBQUUsQ0FBQztNQUFDekIsY0FBQSxHQUFBSSxDQUFBO01BRUptRCxPQUFPLENBQUNDLEdBQUcsQ0FBQyxrREFBa0QsQ0FBQztJQUVqRSxDQUFDLENBQUMsT0FBT0MsS0FBSyxFQUFFO01BQUF6RCxjQUFBLEdBQUFJLENBQUE7TUFDZG1ELE9BQU8sQ0FBQ0UsS0FBSyxDQUFDLHVDQUF1QyxFQUFFQSxLQUFLLENBQUM7TUFBQ3pELGNBQUEsR0FBQUksQ0FBQTtNQUM5RHlDLFFBQVEsQ0FBQyxVQUFBRyxJQUFJLEVBQUs7UUFBQWhELGNBQUEsR0FBQUUsQ0FBQTtRQUFBRixjQUFBLEdBQUFJLENBQUE7UUFBQSxPQUFBUSxNQUFBLENBQUFDLE1BQUEsS0FBS21DLElBQUk7VUFBRXZCLFlBQVksRUFBRTtRQUFLO01BQUMsQ0FBRSxDQUFDO0lBQ3REO0VBQ0YsQ0FBQyxHQUFFLENBQUNwQixJQUFJLEVBQUV1QyxLQUFLLENBQUNwQixhQUFhLEVBQUVILE1BQU0sQ0FBQyxDQUFDO0VBS3ZDLElBQU1xQyxZQUFZLElBQUExRCxjQUFBLEdBQUFJLENBQUEsUUFBR2hCLFdBQVcsQ0FBQyxVQUFDdUUsT0FBWSxFQUFLO0lBQUEzRCxjQUFBLEdBQUFFLENBQUE7SUFBQUYsY0FBQSxHQUFBSSxDQUFBO0lBQ2pELElBQUksQ0FBQUosY0FBQSxHQUFBQyxDQUFBLFdBQUNJLElBQUksTUFBQUwsY0FBQSxHQUFBQyxDQUFBLFVBQUksQ0FBQzJDLEtBQUssQ0FBQ3BCLGFBQWEsR0FBRTtNQUFBeEIsY0FBQSxHQUFBQyxDQUFBO01BQUFELGNBQUEsR0FBQUksQ0FBQTtNQUFBLE9BQU8sRUFBRTtJQUFBLENBQUM7TUFBQUosY0FBQSxHQUFBQyxDQUFBO0lBQUE7SUFBQUQsY0FBQSxHQUFBSSxDQUFBO0lBRTdDLElBQUk7TUFDRixJQUFNd0QsU0FBUyxJQUFBNUQsY0FBQSxHQUFBSSxDQUFBLFFBQUdaLHlCQUF5QixDQUFDa0UsWUFBWSxDQUFDckQsSUFBSSxDQUFDd0QsRUFBRSxFQUFBakQsTUFBQSxDQUFBQyxNQUFBO1FBQzlEaUQsU0FBUyxFQUFFQyxZQUFZLENBQUMsQ0FBQztRQUN6QkMsU0FBUyxFQUFFLElBQUlDLElBQUksQ0FBQyxDQUFDLENBQUNDLGtCQUFrQixDQUFDLElBQUksRUFBRTtVQUFFQyxPQUFPLEVBQUU7UUFBTyxDQUFDLENBQUM7UUFDbkVDLFdBQVcsRUFBRSxNQUFNO1FBQ25CQyxZQUFZLEVBQUUsRUFBRTtRQUNoQkMsaUJBQWlCLEVBQUUsVUFBVTtRQUM3QkMsVUFBVSxFQUFFO01BQU8sR0FDaEJaLE9BQU8sQ0FDWCxDQUFDO01BQUMzRCxjQUFBLEdBQUFJLENBQUE7TUFFSE0sbUJBQW1CLENBQUNrRCxTQUFTLENBQUM7TUFBQzVELGNBQUEsR0FBQUksQ0FBQTtNQUMvQm1ELE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG9DQUFvQ0ksU0FBUyxFQUFFLENBQUM7TUFBQzVELGNBQUEsR0FBQUksQ0FBQTtNQUU3RCxPQUFPd0QsU0FBUztJQUNsQixDQUFDLENBQUMsT0FBT0gsS0FBSyxFQUFFO01BQUF6RCxjQUFBLEdBQUFJLENBQUE7TUFDZG1ELE9BQU8sQ0FBQ0UsS0FBSyxDQUFDLDZCQUE2QixFQUFFQSxLQUFLLENBQUM7TUFBQ3pELGNBQUEsR0FBQUksQ0FBQTtNQUNwRCxPQUFPLEVBQUU7SUFDWDtFQUNGLENBQUMsRUFBRSxDQUFDQyxJQUFJLEVBQUV1QyxLQUFLLENBQUNwQixhQUFhLENBQUMsQ0FBQztFQUsvQixJQUFNZ0QsZUFBZSxJQUFBeEUsY0FBQSxHQUFBSSxDQUFBLFFBQUdoQixXQUFXLENBQUMsVUFBQ3FGLE1BQWMsRUFBRUMsUUFBZ0IsRUFBSztJQUFBMUUsY0FBQSxHQUFBRSxDQUFBO0lBQUFGLGNBQUEsR0FBQUksQ0FBQTtJQUN4RSxJQUFJLENBQUFKLGNBQUEsR0FBQUMsQ0FBQSxXQUFDSSxJQUFJLE1BQUFMLGNBQUEsR0FBQUMsQ0FBQSxVQUFJLENBQUNRLGdCQUFnQixNQUFBVCxjQUFBLEdBQUFDLENBQUEsVUFBSSxDQUFDb0IsTUFBTSxDQUFDTCx1QkFBdUIsR0FBRTtNQUFBaEIsY0FBQSxHQUFBQyxDQUFBO01BQUFELGNBQUEsR0FBQUksQ0FBQTtNQUFBO0lBQU0sQ0FBQztNQUFBSixjQUFBLEdBQUFDLENBQUE7SUFBQTtJQUFBRCxjQUFBLEdBQUFJLENBQUE7SUFFMUUsSUFBSTtNQUFBSixjQUFBLEdBQUFJLENBQUE7TUFDRloseUJBQXlCLENBQUNnRixlQUFlLENBQ3ZDbkUsSUFBSSxDQUFDd0QsRUFBRSxFQUNQcEQsZ0JBQWdCLEVBQ2hCZ0UsTUFBTSxFQUNOQyxRQUNGLENBQUM7TUFBQzFFLGNBQUEsR0FBQUksQ0FBQTtNQUdGLElBQUlpQixNQUFNLENBQUNQLHFCQUFxQixFQUFFO1FBQUFkLGNBQUEsR0FBQUMsQ0FBQTtRQUFBRCxjQUFBLEdBQUFJLENBQUE7UUFDaENkLHFCQUFxQixDQUFDcUYsd0JBQXdCLENBQUN0RSxJQUFJLENBQUN3RCxFQUFFLENBQUM7TUFDekQsQ0FBQztRQUFBN0QsY0FBQSxHQUFBQyxDQUFBO01BQUE7SUFFSCxDQUFDLENBQUMsT0FBT3dELEtBQUssRUFBRTtNQUFBekQsY0FBQSxHQUFBSSxDQUFBO01BQ2RtRCxPQUFPLENBQUNFLEtBQUssQ0FBQyw4QkFBOEIsRUFBRUEsS0FBSyxDQUFDO0lBQ3REO0VBQ0YsQ0FBQyxFQUFFLENBQUNwRCxJQUFJLEVBQUVJLGdCQUFnQixFQUFFWSxNQUFNLENBQUNMLHVCQUF1QixFQUFFSyxNQUFNLENBQUNQLHFCQUFxQixDQUFDLENBQUM7RUFLMUYsSUFBTThELGdCQUFnQixJQUFBNUUsY0FBQSxHQUFBSSxDQUFBLFFBQUdoQixXQUFXLENBQUMsVUFBQ3lGLFdBQWdCLEVBQUs7SUFBQTdFLGNBQUEsR0FBQUUsQ0FBQTtJQUFBRixjQUFBLEdBQUFJLENBQUE7SUFDekQsSUFBSSxDQUFBSixjQUFBLEdBQUFDLENBQUEsWUFBQ0ksSUFBSSxNQUFBTCxjQUFBLEdBQUFDLENBQUEsV0FBSSxDQUFDUSxnQkFBZ0IsTUFBQVQsY0FBQSxHQUFBQyxDQUFBLFdBQUksQ0FBQ29CLE1BQU0sQ0FBQ0wsdUJBQXVCLEdBQUU7TUFBQWhCLGNBQUEsR0FBQUMsQ0FBQTtNQUFBRCxjQUFBLEdBQUFJLENBQUE7TUFBQTtJQUFNLENBQUM7TUFBQUosY0FBQSxHQUFBQyxDQUFBO0lBQUE7SUFBQUQsY0FBQSxHQUFBSSxDQUFBO0lBRTFFLElBQUk7TUFBQUosY0FBQSxHQUFBSSxDQUFBO01BQ0ZaLHlCQUF5QixDQUFDb0YsZ0JBQWdCLENBQ3hDdkUsSUFBSSxDQUFDd0QsRUFBRSxFQUNQcEQsZ0JBQWdCLEVBQ2hCb0UsV0FDRixDQUFDO0lBQ0gsQ0FBQyxDQUFDLE9BQU9wQixLQUFLLEVBQUU7TUFBQXpELGNBQUEsR0FBQUksQ0FBQTtNQUNkbUQsT0FBTyxDQUFDRSxLQUFLLENBQUMsOEJBQThCLEVBQUVBLEtBQUssQ0FBQztJQUN0RDtFQUNGLENBQUMsRUFBRSxDQUFDcEQsSUFBSSxFQUFFSSxnQkFBZ0IsRUFBRVksTUFBTSxDQUFDTCx1QkFBdUIsQ0FBQyxDQUFDO0VBSzVELElBQU04RCxVQUFVLElBQUE5RSxjQUFBLEdBQUFJLENBQUEsUUFBR2hCLFdBQVcsQ0FBQyxZQUFNO0lBQUFZLGNBQUEsR0FBQUUsQ0FBQTtJQUFBRixjQUFBLEdBQUFJLENBQUE7SUFDbkMsSUFBSSxDQUFBSixjQUFBLEdBQUFDLENBQUEsWUFBQ0ksSUFBSSxNQUFBTCxjQUFBLEdBQUFDLENBQUEsV0FBSSxDQUFDUSxnQkFBZ0IsR0FBRTtNQUFBVCxjQUFBLEdBQUFDLENBQUE7TUFBQUQsY0FBQSxHQUFBSSxDQUFBO01BQUE7SUFBTSxDQUFDO01BQUFKLGNBQUEsR0FBQUMsQ0FBQTtJQUFBO0lBQUFELGNBQUEsR0FBQUksQ0FBQTtJQUV2QyxJQUFJO01BQUFKLGNBQUEsR0FBQUksQ0FBQTtNQUNGWix5QkFBeUIsQ0FBQ3NGLFVBQVUsQ0FBQ3pFLElBQUksQ0FBQ3dELEVBQUUsRUFBRXBELGdCQUFnQixDQUFDO01BQUNULGNBQUEsR0FBQUksQ0FBQTtNQUNoRU0sbUJBQW1CLENBQUMsSUFBSSxDQUFDO01BQUNWLGNBQUEsR0FBQUksQ0FBQTtNQUMxQm1ELE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLCtCQUErQixDQUFDO0lBQzlDLENBQUMsQ0FBQyxPQUFPQyxLQUFLLEVBQUU7TUFBQXpELGNBQUEsR0FBQUksQ0FBQTtNQUNkbUQsT0FBTyxDQUFDRSxLQUFLLENBQUMsMkJBQTJCLEVBQUVBLEtBQUssQ0FBQztJQUNuRDtFQUNGLENBQUMsRUFBRSxDQUFDcEQsSUFBSSxFQUFFSSxnQkFBZ0IsQ0FBQyxDQUFDO0VBSzVCLElBQU1zRSxXQUFXLElBQUEvRSxjQUFBLEdBQUFJLENBQUEsUUFBR2hCLFdBQVcsQ0FBQTJELGlCQUFBLENBQUMsYUFBWTtJQUFBL0MsY0FBQSxHQUFBRSxDQUFBO0lBQUFGLGNBQUEsR0FBQUksQ0FBQTtJQUMxQyxJQUFJLENBQUN3QyxLQUFLLENBQUNwQixhQUFhLEVBQUU7TUFBQXhCLGNBQUEsR0FBQUMsQ0FBQTtNQUFBRCxjQUFBLEdBQUFJLENBQUE7TUFBQTtJQUFNLENBQUM7TUFBQUosY0FBQSxHQUFBQyxDQUFBO0lBQUE7SUFBQUQsY0FBQSxHQUFBSSxDQUFBO0lBRWpDLElBQUk7TUFBQUosY0FBQSxHQUFBSSxDQUFBO01BQ0Z5QyxRQUFRLENBQUMsVUFBQUcsSUFBSSxFQUFLO1FBQUFoRCxjQUFBLEdBQUFFLENBQUE7UUFBQUYsY0FBQSxHQUFBSSxDQUFBO1FBQUEsT0FBQVEsTUFBQSxDQUFBQyxNQUFBLEtBQUttQyxJQUFJO1VBQUV2QixZQUFZLEVBQUU7UUFBSTtNQUFDLENBQUUsQ0FBQztNQUVuRCxJQUFNdUQsb0JBQW9DLElBQUFoRixjQUFBLEdBQUFJLENBQUEsUUFBRyxFQUFFO01BQUNKLGNBQUEsR0FBQUksQ0FBQTtNQUdoRCxJQUFJaUIsTUFBTSxDQUFDTix5QkFBeUIsRUFBRTtRQUFBZixjQUFBLEdBQUFDLENBQUE7UUFBQUQsY0FBQSxHQUFBSSxDQUFBO1FBQ3BDNEUsb0JBQW9CLENBQUM5QixJQUFJLENBQ3ZCM0QsMEJBQTBCLENBQUMwRixxQkFBcUIsQ0FBQyxDQUNuRCxDQUFDO01BQ0gsQ0FBQztRQUFBakYsY0FBQSxHQUFBQyxDQUFBO01BQUE7TUFBQUQsY0FBQSxHQUFBSSxDQUFBO01BR0QsSUFBSWlCLE1BQU0sQ0FBQ0osd0JBQXdCLEVBQUU7UUFBQWpCLGNBQUEsR0FBQUMsQ0FBQTtRQUFBRCxjQUFBLEdBQUFJLENBQUE7UUFDbkM0RSxvQkFBb0IsQ0FBQzlCLElBQUksQ0FDdkJ6RCxvQkFBb0IsQ0FBQ3lGLGlCQUFpQixDQUFDLENBQ3pDLENBQUM7TUFDSCxDQUFDO1FBQUFsRixjQUFBLEdBQUFDLENBQUE7TUFBQTtNQUFBRCxjQUFBLEdBQUFJLENBQUE7TUFHRCxJQUFJLENBQUFKLGNBQUEsR0FBQUMsQ0FBQSxXQUFBb0IsTUFBTSxDQUFDUCxxQkFBcUIsTUFBQWQsY0FBQSxHQUFBQyxDQUFBLFdBQUlJLElBQUksR0FBRTtRQUFBTCxjQUFBLEdBQUFDLENBQUE7UUFBQUQsY0FBQSxHQUFBSSxDQUFBO1FBQ3hDNEUsb0JBQW9CLENBQUM5QixJQUFJLENBQ3ZCNUQscUJBQXFCLENBQUNxRix3QkFBd0IsQ0FBQ3RFLElBQUksQ0FBQ3dELEVBQUUsQ0FDeEQsQ0FBQztNQUNILENBQUM7UUFBQTdELGNBQUEsR0FBQUMsQ0FBQTtNQUFBO01BQUFELGNBQUEsR0FBQUksQ0FBQTtNQUVELE1BQU0rQyxPQUFPLENBQUNnQyxVQUFVLENBQUNILG9CQUFvQixDQUFDO01BQUNoRixjQUFBLEdBQUFJLENBQUE7TUFHL0MsTUFBTWtELGFBQWEsQ0FBQyxDQUFDO01BQUN0RCxjQUFBLEdBQUFJLENBQUE7TUFFdEJtRCxPQUFPLENBQUNDLEdBQUcsQ0FBQyx3Q0FBd0MsQ0FBQztJQUV2RCxDQUFDLENBQUMsT0FBT0MsS0FBSyxFQUFFO01BQUF6RCxjQUFBLEdBQUFJLENBQUE7TUFDZG1ELE9BQU8sQ0FBQ0UsS0FBSyxDQUFDLG9DQUFvQyxFQUFFQSxLQUFLLENBQUM7SUFDNUQsQ0FBQyxTQUFTO01BQUF6RCxjQUFBLEdBQUFJLENBQUE7TUFDUnlDLFFBQVEsQ0FBQyxVQUFBRyxJQUFJLEVBQUs7UUFBQWhELGNBQUEsR0FBQUUsQ0FBQTtRQUFBRixjQUFBLEdBQUFJLENBQUE7UUFBQSxPQUFBUSxNQUFBLENBQUFDLE1BQUEsS0FBS21DLElBQUk7VUFBRXZCLFlBQVksRUFBRTtRQUFLO01BQUMsQ0FBRSxDQUFDO0lBQ3REO0VBQ0YsQ0FBQyxHQUFFLENBQUNtQixLQUFLLENBQUNwQixhQUFhLEVBQUVILE1BQU0sRUFBRWhCLElBQUksQ0FBQyxDQUFDO0VBS3ZDLElBQU0rRSxZQUFZLElBQUFwRixjQUFBLEdBQUFJLENBQUEsUUFBR2hCLFdBQVcsQ0FBQyxVQUFDaUcsU0FBd0MsRUFBSztJQUFBckYsY0FBQSxHQUFBRSxDQUFBO0lBQUFGLGNBQUEsR0FBQUksQ0FBQTtJQUM3RWtCLFNBQVMsQ0FBQyxVQUFBMEIsSUFBSSxFQUFLO01BQUFoRCxjQUFBLEdBQUFFLENBQUE7TUFBQUYsY0FBQSxHQUFBSSxDQUFBO01BQUEsT0FBQVEsTUFBQSxDQUFBQyxNQUFBLEtBQUttQyxJQUFJLEVBQUtxQyxTQUFTO0lBQUMsQ0FBRSxDQUFDO0VBQ2hELENBQUMsRUFBRSxFQUFFLENBQUM7RUFLTixJQUFNQyxXQUFXLElBQUF0RixjQUFBLEdBQUFJLENBQUEsUUFBR2hCLFdBQVcsQ0FBQyxZQUFNO0lBQUFZLGNBQUEsR0FBQUUsQ0FBQTtJQUFBRixjQUFBLEdBQUFJLENBQUE7SUFDcEMsSUFBSSxDQUFBSixjQUFBLEdBQUFDLENBQUEsWUFBQ0ksSUFBSSxNQUFBTCxjQUFBLEdBQUFDLENBQUEsV0FBSSxDQUFDb0IsTUFBTSxDQUFDTCx1QkFBdUIsR0FBRTtNQUFBaEIsY0FBQSxHQUFBQyxDQUFBO01BQUFELGNBQUEsR0FBQUksQ0FBQTtNQUFBLE9BQU8sRUFBRTtJQUFBLENBQUM7TUFBQUosY0FBQSxHQUFBQyxDQUFBO0lBQUE7SUFBQUQsY0FBQSxHQUFBSSxDQUFBO0lBRXhELElBQUk7TUFBQUosY0FBQSxHQUFBSSxDQUFBO01BQ0YsT0FBT1oseUJBQXlCLENBQUMrRixvQkFBb0IsQ0FBQ2xGLElBQUksQ0FBQ3dELEVBQUUsQ0FBQztJQUNoRSxDQUFDLENBQUMsT0FBT0osS0FBSyxFQUFFO01BQUF6RCxjQUFBLEdBQUFJLENBQUE7TUFDZG1ELE9BQU8sQ0FBQ0UsS0FBSyxDQUFDLHlCQUF5QixFQUFFQSxLQUFLLENBQUM7TUFBQ3pELGNBQUEsR0FBQUksQ0FBQTtNQUNoRCxPQUFPLEVBQUU7SUFDWDtFQUNGLENBQUMsRUFBRSxDQUFDQyxJQUFJLEVBQUVnQixNQUFNLENBQUNMLHVCQUF1QixDQUFDLENBQUM7RUFLMUMsSUFBTXdFLGNBQWMsSUFBQXhGLGNBQUEsR0FBQUksQ0FBQSxRQUFHaEIsV0FBVyxDQUFBMkQsaUJBQUEsQ0FBQyxhQUFZO0lBQUEvQyxjQUFBLEdBQUFFLENBQUE7SUFBQUYsY0FBQSxHQUFBSSxDQUFBO0lBQzdDLElBQUksQ0FBQUosY0FBQSxHQUFBQyxDQUFBLFlBQUNJLElBQUksTUFBQUwsY0FBQSxHQUFBQyxDQUFBLFdBQUksQ0FBQzJDLEtBQUssQ0FBQ3BCLGFBQWEsR0FBRTtNQUFBeEIsY0FBQSxHQUFBQyxDQUFBO01BQUFELGNBQUEsR0FBQUksQ0FBQTtNQUFBLE9BQU8sRUFBRTtJQUFBLENBQUM7TUFBQUosY0FBQSxHQUFBQyxDQUFBO0lBQUE7SUFBQUQsY0FBQSxHQUFBSSxDQUFBO0lBRTdDLElBQUk7TUFDRixJQUFNMEIsV0FBa0IsSUFBQTlCLGNBQUEsR0FBQUksQ0FBQSxRQUFHLEVBQUU7TUFBQ0osY0FBQSxHQUFBSSxDQUFBO01BRzlCLElBQUlpQixNQUFNLENBQUNQLHFCQUFxQixFQUFFO1FBQUFkLGNBQUEsR0FBQUMsQ0FBQTtRQUNoQyxJQUFNd0YsZ0JBQWdCLElBQUF6RixjQUFBLEdBQUFJLENBQUEsY0FBU2QscUJBQXFCLENBQUNrRyxjQUFjLENBQUNuRixJQUFJLENBQUN3RCxFQUFFLENBQUM7UUFBQzdELGNBQUEsR0FBQUksQ0FBQTtRQUM3RTBCLFdBQVcsQ0FBQ29CLElBQUksQ0FBQXdDLEtBQUEsQ0FBaEI1RCxXQUFXLEVBQUE2RCxrQkFBQSxDQUFTRixnQkFBZ0IsQ0FBQ0csR0FBRyxDQUFDLFVBQUFDLENBQUMsRUFBSztVQUFBN0YsY0FBQSxHQUFBRSxDQUFBO1VBQUFGLGNBQUEsR0FBQUksQ0FBQTtVQUFBLE9BQUFRLE1BQUEsQ0FBQUMsTUFBQTtZQUM3Q2lGLElBQUksRUFBRTtVQUFPLEdBQ1ZELENBQUM7UUFDTixDQUFFLENBQUMsRUFBQztNQUNOLENBQUM7UUFBQTdGLGNBQUEsR0FBQUMsQ0FBQTtNQUFBO01BQUFELGNBQUEsR0FBQUksQ0FBQTtNQUdELElBQUlpQixNQUFNLENBQUNKLHdCQUF3QixFQUFFO1FBQUFqQixjQUFBLEdBQUFDLENBQUE7UUFDbkMsSUFBTThGLG1CQUFtQixJQUFBL0YsY0FBQSxHQUFBSSxDQUFBLGNBQVNYLG9CQUFvQixDQUFDdUcsc0JBQXNCLENBQUMsQ0FBQztRQUFDaEcsY0FBQSxHQUFBSSxDQUFBO1FBQ2hGMEIsV0FBVyxDQUFDb0IsSUFBSSxDQUFBd0MsS0FBQSxDQUFoQjVELFdBQVcsRUFBQTZELGtCQUFBLENBQVNJLG1CQUFtQixDQUFDSCxHQUFHLENBQUMsVUFBQUMsQ0FBQyxFQUFLO1VBQUE3RixjQUFBLEdBQUFFLENBQUE7VUFBQUYsY0FBQSxHQUFBSSxDQUFBO1VBQUEsT0FBQVEsTUFBQSxDQUFBQyxNQUFBO1lBQ2hEaUYsSUFBSSxFQUFFO1VBQVUsR0FDYkQsQ0FBQztRQUNOLENBQUUsQ0FBQyxFQUFDO01BQ04sQ0FBQztRQUFBN0YsY0FBQSxHQUFBQyxDQUFBO01BQUE7TUFBQUQsY0FBQSxHQUFBSSxDQUFBO01BRUQsT0FBTzBCLFdBQVc7SUFDcEIsQ0FBQyxDQUFDLE9BQU8yQixLQUFLLEVBQUU7TUFBQXpELGNBQUEsR0FBQUksQ0FBQTtNQUNkbUQsT0FBTyxDQUFDRSxLQUFLLENBQUMsNEJBQTRCLEVBQUVBLEtBQUssQ0FBQztNQUFDekQsY0FBQSxHQUFBSSxDQUFBO01BQ25ELE9BQU8sRUFBRTtJQUNYO0VBQ0YsQ0FBQyxHQUFFLENBQUNDLElBQUksRUFBRXVDLEtBQUssQ0FBQ3BCLGFBQWEsRUFBRUgsTUFBTSxDQUFDLENBQUM7RUFLdkMsSUFBTWlDLGFBQWEsSUFBQXRELGNBQUEsR0FBQUksQ0FBQSxTQUFHaEIsV0FBVyxDQUFBMkQsaUJBQUEsQ0FBQyxhQUFZO0lBQUEvQyxjQUFBLEdBQUFFLENBQUE7SUFBQUYsY0FBQSxHQUFBSSxDQUFBO0lBQzVDLElBQUk7TUFDRixJQUFNNkYsUUFBc0MsSUFBQWpHLGNBQUEsR0FBQUksQ0FBQSxTQUFHLENBQUMsQ0FBQztNQUFDSixjQUFBLEdBQUFJLENBQUE7TUFHbEQsSUFBSWlCLE1BQU0sQ0FBQ04seUJBQXlCLEVBQUU7UUFBQWYsY0FBQSxHQUFBQyxDQUFBO1FBQ3BDLElBQU15QixjQUFjLElBQUExQixjQUFBLEdBQUFJLENBQUEsU0FBR2IsMEJBQTBCLENBQUMyRyxpQkFBaUIsQ0FBQyxDQUFDO1FBQUNsRyxjQUFBLEdBQUFJLENBQUE7UUFDdEU2RixRQUFRLENBQUN2RSxjQUFjLEdBQUcsQ0FBQTFCLGNBQUEsR0FBQUMsQ0FBQSxXQUFBeUIsY0FBYyxvQkFBZEEsY0FBYyxDQUFFeUUsSUFBSSxNQUFBbkcsY0FBQSxHQUFBQyxDQUFBLFdBQUksVUFBVTtRQUU1RCxJQUFNbUcsaUJBQWlCLElBQUFwRyxjQUFBLEdBQUFJLENBQUEsU0FBR2IsMEJBQTBCLENBQUM4RyxvQkFBb0IsQ0FBQyxDQUFDO1FBQUNyRyxjQUFBLEdBQUFJLENBQUE7UUFDNUU2RixRQUFRLENBQUMzRCxPQUFPLEdBQUExQixNQUFBLENBQUFDLE1BQUEsS0FDWCtCLEtBQUssQ0FBQ04sT0FBTztVQUNoQkMsZUFBZSxFQUFFNkQsaUJBQWlCLENBQUM3RCxlQUFlO1VBQ2xEQyxpQkFBaUIsRUFBRTRELGlCQUFpQixDQUFDRSxzQkFBc0I7VUFDM0Q3RCxnQkFBZ0IsRUFBRTJELGlCQUFpQixDQUFDM0Q7UUFBZ0IsRUFDckQ7TUFDSCxDQUFDO1FBQUF6QyxjQUFBLEdBQUFDLENBQUE7TUFBQTtNQUFBRCxjQUFBLEdBQUFJLENBQUE7TUFHRCxJQUFJLENBQUFKLGNBQUEsR0FBQUMsQ0FBQSxXQUFBb0IsTUFBTSxDQUFDTCx1QkFBdUIsTUFBQWhCLGNBQUEsR0FBQUMsQ0FBQSxXQUFJSSxJQUFJLEdBQUU7UUFBQUwsY0FBQSxHQUFBQyxDQUFBO1FBQzFDLElBQU0wQixXQUFXLElBQUEzQixjQUFBLEdBQUFJLENBQUEsU0FBR1oseUJBQXlCLENBQUMrRyxjQUFjLENBQUNsRyxJQUFJLENBQUN3RCxFQUFFLENBQUM7UUFBQzdELGNBQUEsR0FBQUksQ0FBQTtRQUN0RTZGLFFBQVEsQ0FBQ3RFLFdBQVcsR0FBRyxDQUFBM0IsY0FBQSxHQUFBQyxDQUFBLFdBQUEwQixXQUFXLG9CQUFYQSxXQUFXLENBQUV3RSxJQUFJLE1BQUFuRyxjQUFBLEdBQUFDLENBQUEsV0FBSSxTQUFTO01BQ3ZELENBQUM7UUFBQUQsY0FBQSxHQUFBQyxDQUFBO01BQUE7TUFBQUQsY0FBQSxHQUFBSSxDQUFBO01BR0QsSUFBSWlCLE1BQU0sQ0FBQ0osd0JBQXdCLEVBQUU7UUFBQWpCLGNBQUEsR0FBQUMsQ0FBQTtRQUNuQyxJQUFNdUcsVUFBVSxJQUFBeEcsY0FBQSxHQUFBSSxDQUFBLFNBQUdYLG9CQUFvQixDQUFDZ0gsMEJBQTBCLENBQUMsQ0FBQztRQUFDekcsY0FBQSxHQUFBSSxDQUFBO1FBQ3JFNkYsUUFBUSxDQUFDckUsa0JBQWtCLEdBQUc0RSxVQUFVLENBQUNFLE9BQU87UUFBQzFHLGNBQUEsR0FBQUksQ0FBQTtRQUNqRDZGLFFBQVEsQ0FBQzNELE9BQU8sR0FBQTFCLE1BQUEsQ0FBQUMsTUFBQSxLQUNYb0YsUUFBUSxDQUFDM0QsT0FBTztVQUNuQkksWUFBWSxFQUFFOEQsVUFBVSxDQUFDRTtRQUFPLEVBQ2pDO01BQ0gsQ0FBQztRQUFBMUcsY0FBQSxHQUFBQyxDQUFBO01BQUE7TUFBQUQsY0FBQSxHQUFBSSxDQUFBO01BR0QsSUFBSWlCLE1BQU0sQ0FBQ1AscUJBQXFCLEVBQUU7UUFBQWQsY0FBQSxHQUFBQyxDQUFBO1FBQ2hDLElBQU0wRyxZQUFZLElBQUEzRyxjQUFBLEdBQUFJLENBQUEsU0FBR2QscUJBQXFCLENBQUNzSCxpQkFBaUIsQ0FBQyxDQUFDO1FBQUM1RyxjQUFBLEdBQUFJLENBQUE7UUFDL0Q2RixRQUFRLENBQUNwRSxZQUFZLEdBQUc4RSxZQUFZLENBQUNFLG1CQUFtQjtNQUMxRCxDQUFDO1FBQUE3RyxjQUFBLEdBQUFDLENBQUE7TUFBQTtNQUFBRCxjQUFBLEdBQUFJLENBQUE7TUFHRCxJQUFJaUIsTUFBTSxDQUFDSix3QkFBd0IsRUFBRTtRQUFBakIsY0FBQSxHQUFBQyxDQUFBO1FBQ25DLElBQU1pQyxlQUFlLElBQUFsQyxjQUFBLEdBQUFJLENBQUEsU0FBR1gsb0JBQW9CLENBQUNxSCw4QkFBOEIsQ0FBQyxDQUFDO1FBQUM5RyxjQUFBLEdBQUFJLENBQUE7UUFDOUU2RixRQUFRLENBQUMvRCxlQUFlLEdBQUdBLGVBQWU7TUFDNUMsQ0FBQztRQUFBbEMsY0FBQSxHQUFBQyxDQUFBO01BQUE7TUFHRCxJQUFNNkIsV0FBVyxJQUFBOUIsY0FBQSxHQUFBSSxDQUFBLGVBQVNvRixjQUFjLENBQUMsQ0FBQztNQUFDeEYsY0FBQSxHQUFBSSxDQUFBO01BQzNDNkYsUUFBUSxDQUFDbkUsV0FBVyxHQUFHO1FBQ3JCQyxXQUFXLEVBQUVELFdBQVcsQ0FBQ2lGLE1BQU0sQ0FBQyxVQUFBbEIsQ0FBQyxFQUFJO1VBQUE3RixjQUFBLEdBQUFFLENBQUE7VUFBQUYsY0FBQSxHQUFBSSxDQUFBO1VBQUEsT0FBQXlGLENBQUMsQ0FBQ0MsSUFBSSxLQUFLLE9BQU87UUFBRCxDQUFDLENBQUMsQ0FBQ0YsR0FBRyxDQUFDLFVBQUFDLENBQUMsRUFBSTtVQUFBN0YsY0FBQSxHQUFBRSxDQUFBO1VBQUFGLGNBQUEsR0FBQUksQ0FBQTtVQUFBLE9BQUF5RixDQUFDLENBQUNtQixHQUFHO1FBQUQsQ0FBQyxDQUFDO1FBQ3hFaEYsYUFBYSxFQUFFRixXQUFXLENBQUNpRixNQUFNLENBQUMsVUFBQWxCLENBQUMsRUFBSTtVQUFBN0YsY0FBQSxHQUFBRSxDQUFBO1VBQUFGLGNBQUEsR0FBQUksQ0FBQTtVQUFBLE9BQUF5RixDQUFDLENBQUNDLElBQUksS0FBSyxVQUFVO1FBQUQsQ0FBQyxDQUFDO1FBQzdEN0QsZ0JBQWdCLEVBQUVXLEtBQUssQ0FBQ2QsV0FBVyxDQUFDRztNQUN0QyxDQUFDO01BQUNqQyxjQUFBLEdBQUFJLENBQUE7TUFFRnlDLFFBQVEsQ0FBQyxVQUFBRyxJQUFJLEVBQUs7UUFBQWhELGNBQUEsR0FBQUUsQ0FBQTtRQUFBRixjQUFBLEdBQUFJLENBQUE7UUFBQSxPQUFBUSxNQUFBLENBQUFDLE1BQUEsS0FBS21DLElBQUksRUFBS2lELFFBQVE7TUFBQyxDQUFFLENBQUM7SUFFOUMsQ0FBQyxDQUFDLE9BQU94QyxLQUFLLEVBQUU7TUFBQXpELGNBQUEsR0FBQUksQ0FBQTtNQUNkbUQsT0FBTyxDQUFDRSxLQUFLLENBQUMsNEJBQTRCLEVBQUVBLEtBQUssQ0FBQztJQUNwRDtFQUNGLENBQUMsR0FBRSxDQUFDcEMsTUFBTSxFQUFFaEIsSUFBSSxFQUFFdUMsS0FBSyxDQUFDTixPQUFPLEVBQUVNLEtBQUssQ0FBQ2QsV0FBVyxDQUFDRyxnQkFBZ0IsRUFBRXVELGNBQWMsQ0FBQyxDQUFDO0VBQUN4RixjQUFBLEdBQUFJLENBQUE7RUFHdEZqQixTQUFTLENBQUMsWUFBTTtJQUFBYSxjQUFBLEdBQUFFLENBQUE7SUFBQUYsY0FBQSxHQUFBSSxDQUFBO0lBQ2QsSUFBSSxDQUFBSixjQUFBLEdBQUFDLENBQUEsWUFBQ29CLE1BQU0sQ0FBQ0gsZ0JBQWdCLE1BQUFsQixjQUFBLEdBQUFDLENBQUEsV0FBSSxDQUFDMkMsS0FBSyxDQUFDcEIsYUFBYSxHQUFFO01BQUF4QixjQUFBLEdBQUFDLENBQUE7TUFBQUQsY0FBQSxHQUFBSSxDQUFBO01BQUE7SUFBTSxDQUFDO01BQUFKLGNBQUEsR0FBQUMsQ0FBQTtJQUFBO0lBRTdELElBQU1nSCxRQUFRLElBQUFqSCxjQUFBLEdBQUFJLENBQUEsU0FBRzhHLFdBQVcsQ0FBQyxZQUFNO01BQUFsSCxjQUFBLEdBQUFFLENBQUE7TUFBQUYsY0FBQSxHQUFBSSxDQUFBO01BQ2pDMkUsV0FBVyxDQUFDLENBQUM7SUFDZixDQUFDLEVBQUUxRCxNQUFNLENBQUNGLG9CQUFvQixDQUFDO0lBQUNuQixjQUFBLEdBQUFJLENBQUE7SUFFaEMsT0FBTyxZQUFNO01BQUFKLGNBQUEsR0FBQUUsQ0FBQTtNQUFBRixjQUFBLEdBQUFJLENBQUE7TUFBQSxPQUFBK0csYUFBYSxDQUFDRixRQUFRLENBQUM7SUFBRCxDQUFDO0VBQ3RDLENBQUMsRUFBRSxDQUFDNUYsTUFBTSxDQUFDSCxnQkFBZ0IsRUFBRUcsTUFBTSxDQUFDRixvQkFBb0IsRUFBRXlCLEtBQUssQ0FBQ3BCLGFBQWEsRUFBRXVELFdBQVcsQ0FBQyxDQUFDO0VBQUMvRSxjQUFBLEdBQUFJLENBQUE7RUFHN0ZqQixTQUFTLENBQUMsWUFBTTtJQUFBYSxjQUFBLEdBQUFFLENBQUE7SUFBQUYsY0FBQSxHQUFBSSxDQUFBO0lBQ2QsSUFBSSxDQUFDd0MsS0FBSyxDQUFDcEIsYUFBYSxFQUFFO01BQUF4QixjQUFBLEdBQUFDLENBQUE7TUFBQUQsY0FBQSxHQUFBSSxDQUFBO01BQUE7SUFBTSxDQUFDO01BQUFKLGNBQUEsR0FBQUMsQ0FBQTtJQUFBO0lBRWpDLElBQU1nSCxRQUFRLElBQUFqSCxjQUFBLEdBQUFJLENBQUEsU0FBRzhHLFdBQVcsQ0FBQyxZQUFNO01BQUFsSCxjQUFBLEdBQUFFLENBQUE7TUFBQUYsY0FBQSxHQUFBSSxDQUFBO01BQ2pDa0QsYUFBYSxDQUFDLENBQUM7SUFDakIsQ0FBQyxFQUFFLEtBQUssQ0FBQztJQUFDdEQsY0FBQSxHQUFBSSxDQUFBO0lBRVYsT0FBTyxZQUFNO01BQUFKLGNBQUEsR0FBQUUsQ0FBQTtNQUFBRixjQUFBLEdBQUFJLENBQUE7TUFBQSxPQUFBK0csYUFBYSxDQUFDRixRQUFRLENBQUM7SUFBRCxDQUFDO0VBQ3RDLENBQUMsRUFBRSxDQUFDckUsS0FBSyxDQUFDcEIsYUFBYSxFQUFFOEIsYUFBYSxDQUFDLENBQUM7RUFBQ3RELGNBQUEsR0FBQUksQ0FBQTtFQUd6Q2pCLFNBQVMsQ0FBQyxZQUFNO0lBQUFhLGNBQUEsR0FBQUUsQ0FBQTtJQUFBRixjQUFBLEdBQUFJLENBQUE7SUFDZCxJQUFJLENBQUFKLGNBQUEsR0FBQUMsQ0FBQSxXQUFBSSxJQUFJLE1BQUFMLGNBQUEsR0FBQUMsQ0FBQSxXQUFJLENBQUMyQyxLQUFLLENBQUNwQixhQUFhLEdBQUU7TUFBQXhCLGNBQUEsR0FBQUMsQ0FBQTtNQUFBRCxjQUFBLEdBQUFJLENBQUE7TUFDaEMwQyxVQUFVLENBQUMsQ0FBQztJQUNkLENBQUM7TUFBQTlDLGNBQUEsR0FBQUMsQ0FBQTtJQUFBO0VBQ0gsQ0FBQyxFQUFFLENBQUNJLElBQUksRUFBRXVDLEtBQUssQ0FBQ3BCLGFBQWEsRUFBRXNCLFVBQVUsQ0FBQyxDQUFDO0VBQUM5QyxjQUFBLEdBQUFJLENBQUE7RUFHNUMsT0FBT2YsT0FBTyxDQUFDLFlBQU87SUFBQVcsY0FBQSxHQUFBRSxDQUFBO0lBQUFGLGNBQUEsR0FBQUksQ0FBQTtJQUFBO01BQ3BCd0MsS0FBSyxFQUFMQSxLQUFLO01BQ0x3RSxPQUFPLEVBQUU7UUFDUHRFLFVBQVUsRUFBVkEsVUFBVTtRQUNWWSxZQUFZLEVBQVpBLFlBQVk7UUFDWmMsZUFBZSxFQUFmQSxlQUFlO1FBQ2ZJLGdCQUFnQixFQUFoQkEsZ0JBQWdCO1FBQ2hCRSxVQUFVLEVBQVZBLFVBQVU7UUFDVkMsV0FBVyxFQUFYQSxXQUFXO1FBQ1hLLFlBQVksRUFBWkEsWUFBWTtRQUNaRSxXQUFXLEVBQVhBLFdBQVc7UUFDWEUsY0FBYyxFQUFkQTtNQUNGLENBQUM7TUFDRG5FLE1BQU0sRUFBTkE7SUFDRixDQUFDO0VBQUQsQ0FBRSxFQUFFLENBQ0Z1QixLQUFLLEVBQ0xFLFVBQVUsRUFDVlksWUFBWSxFQUNaYyxlQUFlLEVBQ2ZJLGdCQUFnQixFQUNoQkUsVUFBVSxFQUNWQyxXQUFXLEVBQ1hLLFlBQVksRUFDWkUsV0FBVyxFQUNYRSxjQUFjLEVBQ2RuRSxNQUFNLENBQ1AsQ0FBQztBQUNKO0FBR0EsU0FBUzBDLFlBQVlBLENBQUEsRUFBa0Q7RUFBQS9ELGNBQUEsR0FBQUUsQ0FBQTtFQUNyRSxJQUFNbUgsSUFBSSxJQUFBckgsY0FBQSxHQUFBSSxDQUFBLFNBQUcsSUFBSTZELElBQUksQ0FBQyxDQUFDLENBQUNxRCxRQUFRLENBQUMsQ0FBQztFQUFDdEgsY0FBQSxHQUFBSSxDQUFBO0VBQ25DLElBQUlpSCxJQUFJLEdBQUcsQ0FBQyxFQUFFO0lBQUFySCxjQUFBLEdBQUFDLENBQUE7SUFBQUQsY0FBQSxHQUFBSSxDQUFBO0lBQUEsT0FBTyxPQUFPO0VBQUEsQ0FBQztJQUFBSixjQUFBLEdBQUFDLENBQUE7RUFBQTtFQUFBRCxjQUFBLEdBQUFJLENBQUE7RUFDN0IsSUFBSWlILElBQUksR0FBRyxFQUFFLEVBQUU7SUFBQXJILGNBQUEsR0FBQUMsQ0FBQTtJQUFBRCxjQUFBLEdBQUFJLENBQUE7SUFBQSxPQUFPLFNBQVM7RUFBQSxDQUFDO0lBQUFKLGNBQUEsR0FBQUMsQ0FBQTtFQUFBO0VBQUFELGNBQUEsR0FBQUksQ0FBQTtFQUNoQyxJQUFJaUgsSUFBSSxHQUFHLEVBQUUsRUFBRTtJQUFBckgsY0FBQSxHQUFBQyxDQUFBO0lBQUFELGNBQUEsR0FBQUksQ0FBQTtJQUFBLE9BQU8sV0FBVztFQUFBLENBQUM7SUFBQUosY0FBQSxHQUFBQyxDQUFBO0VBQUE7RUFBQUQsY0FBQSxHQUFBSSxDQUFBO0VBQ2xDLElBQUlpSCxJQUFJLEdBQUcsRUFBRSxFQUFFO0lBQUFySCxjQUFBLEdBQUFDLENBQUE7SUFBQUQsY0FBQSxHQUFBSSxDQUFBO0lBQUEsT0FBTyxTQUFTO0VBQUEsQ0FBQztJQUFBSixjQUFBLEdBQUFDLENBQUE7RUFBQTtFQUFBRCxjQUFBLEdBQUFJLENBQUE7RUFDaEMsT0FBTyxPQUFPO0FBQ2hCO0FBRUEsZUFBZVQsaUJBQWlCIiwiaWdub3JlTGlzdCI6W119