eec27c484fcda41df0b9c995aaf2cb97
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _AnimatedValue = _interopRequireDefault(require("./AnimatedValue"));
var _AnimatedWithChildren = _interopRequireDefault(require("./AnimatedWithChildren"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var _uniqueId = 1;
var AnimatedValueXY = function (_AnimatedWithChildren2) {
  function AnimatedValueXY(valueIn) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedValueXY);
    _this = _callSuper(this, AnimatedValueXY);
    var value = valueIn || {
      x: 0,
      y: 0
    };
    if (typeof value.x === 'number' && typeof value.y === 'number') {
      _this.x = new _AnimatedValue.default(value.x);
      _this.y = new _AnimatedValue.default(value.y);
    } else {
      (0, _invariant.default)(value.x instanceof _AnimatedValue.default && value.y instanceof _AnimatedValue.default, 'AnimatedValueXY must be initialized with an object of numbers or ' + 'AnimatedValues.');
      _this.x = value.x;
      _this.y = value.y;
    }
    _this._listeners = {};
    return _this;
  }
  (0, _inherits2.default)(AnimatedValueXY, _AnimatedWithChildren2);
  return (0, _createClass2.default)(AnimatedValueXY, [{
    key: "setValue",
    value: function setValue(value) {
      this.x.setValue(value.x);
      this.y.setValue(value.y);
    }
  }, {
    key: "setOffset",
    value: function setOffset(offset) {
      this.x.setOffset(offset.x);
      this.y.setOffset(offset.y);
    }
  }, {
    key: "flattenOffset",
    value: function flattenOffset() {
      this.x.flattenOffset();
      this.y.flattenOffset();
    }
  }, {
    key: "extractOffset",
    value: function extractOffset() {
      this.x.extractOffset();
      this.y.extractOffset();
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      return {
        x: this.x.__getValue(),
        y: this.y.__getValue()
      };
    }
  }, {
    key: "resetAnimation",
    value: function resetAnimation(callback) {
      this.x.resetAnimation();
      this.y.resetAnimation();
      callback && callback(this.__getValue());
    }
  }, {
    key: "stopAnimation",
    value: function stopAnimation(callback) {
      this.x.stopAnimation();
      this.y.stopAnimation();
      callback && callback(this.__getValue());
    }
  }, {
    key: "addListener",
    value: function addListener(callback) {
      var _this2 = this;
      var id = String(_uniqueId++);
      var jointCallback = function jointCallback(_ref) {
        var number = _ref.value;
        callback(_this2.__getValue());
      };
      this._listeners[id] = {
        x: this.x.addListener(jointCallback),
        y: this.y.addListener(jointCallback)
      };
      return id;
    }
  }, {
    key: "removeListener",
    value: function removeListener(id) {
      this.x.removeListener(this._listeners[id].x);
      this.y.removeListener(this._listeners[id].y);
      delete this._listeners[id];
    }
  }, {
    key: "removeAllListeners",
    value: function removeAllListeners() {
      this.x.removeAllListeners();
      this.y.removeAllListeners();
      this._listeners = {};
    }
  }, {
    key: "getLayout",
    value: function getLayout() {
      return {
        left: this.x,
        top: this.y
      };
    }
  }, {
    key: "getTranslateTransform",
    value: function getTranslateTransform() {
      return [{
        translateX: this.x
      }, {
        translateY: this.y
      }];
    }
  }]);
}(_AnimatedWithChildren.default);
var _default = exports.default = AnimatedValueXY;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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