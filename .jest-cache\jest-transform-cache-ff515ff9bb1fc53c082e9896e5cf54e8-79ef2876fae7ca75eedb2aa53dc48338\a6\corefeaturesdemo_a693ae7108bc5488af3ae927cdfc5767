f0dc0cf96a999be2632b845d75fe6f1e
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_dp1gjrxc() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\core-features-demo.tsx";
  var hash = "448dd460daebb92a23b472964ce7b10fbe460d0b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\core-features-demo.tsx",
    statementMap: {
      "0": {
        start: {
          line: 35,
          column: 32
        },
        end: {
          line: 35,
          column: 47
        }
      },
      "1": {
        start: {
          line: 36,
          column: 40
        },
        end: {
          line: 36,
          column: 59
        }
      },
      "2": {
        start: {
          line: 43,
          column: 6
        },
        end: {
          line: 43,
          column: 17
        }
      },
      "3": {
        start: {
          line: 53,
          column: 6
        },
        end: {
          line: 53,
          column: 16
        }
      },
      "4": {
        start: {
          line: 60,
          column: 6
        },
        end: {
          line: 60,
          column: 24
        }
      },
      "5": {
        start: {
          line: 67,
          column: 6
        },
        end: {
          line: 67,
          column: 18
        }
      },
      "6": {
        start: {
          line: 75,
          column: 6
        },
        end: {
          line: 75,
          column: 17
        }
      },
      "7": {
        start: {
          line: 77,
          column: 27
        },
        end: {
          line: 107,
          column: 3
        }
      },
      "8": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 78,
          column: 21
        }
      },
      "9": {
        start: {
          line: 79,
          column: 4
        },
        end: {
          line: 106,
          column: 5
        }
      },
      "10": {
        start: {
          line: 80,
          column: 6
        },
        end: {
          line: 86,
          column: 7
        }
      },
      "11": {
        start: {
          line: 81,
          column: 24
        },
        end: {
          line: 81,
          column: 56
        }
      },
      "12": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 85,
          column: 9
        }
      },
      "13": {
        start: {
          line: 83,
          column: 10
        },
        end: {
          line: 83,
          column: 93
        }
      },
      "14": {
        start: {
          line: 84,
          column: 10
        },
        end: {
          line: 84,
          column: 17
        }
      },
      "15": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 95,
          column: 9
        }
      },
      "16": {
        start: {
          line: 97,
          column: 6
        },
        end: {
          line: 101,
          column: 8
        }
      },
      "17": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 103,
          column: 94
        }
      },
      "18": {
        start: {
          line: 105,
          column: 6
        },
        end: {
          line: 105,
          column: 24
        }
      },
      "19": {
        start: {
          line: 109,
          column: 26
        },
        end: {
          line: 144,
          column: 3
        }
      },
      "20": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 110,
          column: 21
        }
      },
      "21": {
        start: {
          line: 111,
          column: 4
        },
        end: {
          line: 143,
          column: 5
        }
      },
      "22": {
        start: {
          line: 112,
          column: 6
        },
        end: {
          line: 114,
          column: 7
        }
      },
      "23": {
        start: {
          line: 113,
          column: 8
        },
        end: {
          line: 113,
          column: 32
        }
      },
      "24": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 117,
          column: 115
        }
      },
      "25": {
        start: {
          line: 119,
          column: 6
        },
        end: {
          line: 132,
          column: 9
        }
      },
      "26": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 138,
          column: 8
        }
      },
      "27": {
        start: {
          line: 140,
          column: 6
        },
        end: {
          line: 140,
          column: 103
        }
      },
      "28": {
        start: {
          line: 142,
          column: 6
        },
        end: {
          line: 142,
          column: 24
        }
      },
      "29": {
        start: {
          line: 146,
          column: 34
        },
        end: {
          line: 186,
          column: 3
        }
      },
      "30": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 147,
          column: 21
        }
      },
      "31": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 185,
          column: 5
        }
      },
      "32": {
        start: {
          line: 149,
          column: 6
        },
        end: {
          line: 155,
          column: 7
        }
      },
      "33": {
        start: {
          line: 150,
          column: 28
        },
        end: {
          line: 150,
          column: 59
        }
      },
      "34": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 154,
          column: 9
        }
      },
      "35": {
        start: {
          line: 152,
          column: 10
        },
        end: {
          line: 152,
          column: 86
        }
      },
      "36": {
        start: {
          line: 153,
          column: 10
        },
        end: {
          line: 153,
          column: 17
        }
      },
      "37": {
        start: {
          line: 158,
          column: 6
        },
        end: {
          line: 162,
          column: 9
        }
      },
      "38": {
        start: {
          line: 165,
          column: 6
        },
        end: {
          line: 165,
          column: 32
        }
      },
      "39": {
        start: {
          line: 167,
          column: 6
        },
        end: {
          line: 174,
          column: 9
        }
      },
      "40": {
        start: {
          line: 176,
          column: 6
        },
        end: {
          line: 180,
          column: 8
        }
      },
      "41": {
        start: {
          line: 182,
          column: 6
        },
        end: {
          line: 182,
          column: 93
        }
      },
      "42": {
        start: {
          line: 184,
          column: 6
        },
        end: {
          line: 184,
          column: 24
        }
      },
      "43": {
        start: {
          line: 188,
          column: 28
        },
        end: {
          line: 224,
          column: 3
        }
      },
      "44": {
        start: {
          line: 189,
          column: 4
        },
        end: {
          line: 189,
          column: 21
        }
      },
      "45": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 223,
          column: 5
        }
      },
      "46": {
        start: {
          line: 192,
          column: 6
        },
        end: {
          line: 197,
          column: 9
        }
      },
      "47": {
        start: {
          line: 200,
          column: 6
        },
        end: {
          line: 202,
          column: 7
        }
      },
      "48": {
        start: {
          line: 201,
          column: 8
        },
        end: {
          line: 201,
          column: 35
        }
      },
      "49": {
        start: {
          line: 204,
          column: 6
        },
        end: {
          line: 212,
          column: 9
        }
      },
      "50": {
        start: {
          line: 214,
          column: 6
        },
        end: {
          line: 218,
          column: 8
        }
      },
      "51": {
        start: {
          line: 220,
          column: 6
        },
        end: {
          line: 220,
          column: 99
        }
      },
      "52": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 222,
          column: 24
        }
      },
      "53": {
        start: {
          line: 226,
          column: 27
        },
        end: {
          line: 269,
          column: 3
        }
      },
      "54": {
        start: {
          line: 227,
          column: 4
        },
        end: {
          line: 227,
          column: 21
        }
      },
      "55": {
        start: {
          line: 228,
          column: 4
        },
        end: {
          line: 268,
          column: 5
        }
      },
      "56": {
        start: {
          line: 230,
          column: 21
        },
        end: {
          line: 236,
          column: 8
        }
      },
      "57": {
        start: {
          line: 239,
          column: 22
        },
        end: {
          line: 239,
          column: 76
        }
      },
      "58": {
        start: {
          line: 241,
          column: 6
        },
        end: {
          line: 249,
          column: 9
        }
      },
      "59": {
        start: {
          line: 251,
          column: 6
        },
        end: {
          line: 263,
          column: 8
        }
      },
      "60": {
        start: {
          line: 258,
          column: 27
        },
        end: {
          line: 260,
          column: 13
        }
      },
      "61": {
        start: {
          line: 259,
          column: 14
        },
        end: {
          line: 259,
          column: 72
        }
      },
      "62": {
        start: {
          line: 265,
          column: 6
        },
        end: {
          line: 265,
          column: 106
        }
      },
      "63": {
        start: {
          line: 267,
          column: 6
        },
        end: {
          line: 267,
          column: 24
        }
      },
      "64": {
        start: {
          line: 271,
          column: 28
        },
        end: {
          line: 343,
          column: 3
        }
      },
      "65": {
        start: {
          line: 272,
          column: 4
        },
        end: {
          line: 272,
          column: 34
        }
      },
      "66": {
        start: {
          line: 272,
          column: 22
        },
        end: {
          line: 272,
          column: 34
        }
      },
      "67": {
        start: {
          line: 274,
          column: 4
        },
        end: {
          line: 342,
          column: 5
        }
      },
      "68": {
        start: {
          line: 276,
          column: 8
        },
        end: {
          line: 284,
          column: 10
        }
      },
      "69": {
        start: {
          line: 287,
          column: 8
        },
        end: {
          line: 297,
          column: 10
        }
      },
      "70": {
        start: {
          line: 294,
          column: 14
        },
        end: {
          line: 294,
          column: 75
        }
      },
      "71": {
        start: {
          line: 300,
          column: 8
        },
        end: {
          line: 308,
          column: 10
        }
      },
      "72": {
        start: {
          line: 311,
          column: 8
        },
        end: {
          line: 323,
          column: 10
        }
      },
      "73": {
        start: {
          line: 326,
          column: 8
        },
        end: {
          line: 338,
          column: 10
        }
      },
      "74": {
        start: {
          line: 341,
          column: 8
        },
        end: {
          line: 341,
          column: 20
        }
      },
      "75": {
        start: {
          line: 345,
          column: 2
        },
        end: {
          line: 476,
          column: 4
        }
      },
      "76": {
        start: {
          line: 352,
          column: 43
        },
        end: {
          line: 352,
          column: 56
        }
      },
      "77": {
        start: {
          line: 479,
          column: 15
        },
        end: {
          line: 596,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "CoreFeaturesDemoScreen",
        decl: {
          start: {
            line: 34,
            column: 24
          },
          end: {
            line: 34,
            column: 46
          }
        },
        loc: {
          start: {
            line: 34,
            column: 49
          },
          end: {
            line: 477,
            column: 1
          }
        },
        line: 34
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 77,
            column: 27
          },
          end: {
            line: 77,
            column: 28
          }
        },
        loc: {
          start: {
            line: 77,
            column: 39
          },
          end: {
            line: 107,
            column: 3
          }
        },
        line: 77
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 109,
            column: 26
          },
          end: {
            line: 109,
            column: 27
          }
        },
        loc: {
          start: {
            line: 109,
            column: 38
          },
          end: {
            line: 144,
            column: 3
          }
        },
        line: 109
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 146,
            column: 34
          },
          end: {
            line: 146,
            column: 35
          }
        },
        loc: {
          start: {
            line: 146,
            column: 46
          },
          end: {
            line: 186,
            column: 3
          }
        },
        line: 146
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 188,
            column: 28
          },
          end: {
            line: 188,
            column: 29
          }
        },
        loc: {
          start: {
            line: 188,
            column: 40
          },
          end: {
            line: 224,
            column: 3
          }
        },
        line: 188
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 226,
            column: 27
          },
          end: {
            line: 226,
            column: 28
          }
        },
        loc: {
          start: {
            line: 226,
            column: 39
          },
          end: {
            line: 269,
            column: 3
          }
        },
        line: 226
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 258,
            column: 21
          },
          end: {
            line: 258,
            column: 22
          }
        },
        loc: {
          start: {
            line: 258,
            column: 27
          },
          end: {
            line: 260,
            column: 13
          }
        },
        line: 258
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 258,
            column: 60
          },
          end: {
            line: 258,
            column: 61
          }
        },
        loc: {
          start: {
            line: 259,
            column: 14
          },
          end: {
            line: 259,
            column: 72
          }
        },
        line: 259
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 271,
            column: 28
          },
          end: {
            line: 271,
            column: 29
          }
        },
        loc: {
          start: {
            line: 271,
            column: 34
          },
          end: {
            line: 343,
            column: 3
          }
        },
        line: 271
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 293,
            column: 52
          },
          end: {
            line: 293,
            column: 53
          }
        },
        loc: {
          start: {
            line: 294,
            column: 14
          },
          end: {
            line: 294,
            column: 75
          }
        },
        line: 294
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 352,
            column: 37
          },
          end: {
            line: 352,
            column: 38
          }
        },
        loc: {
          start: {
            line: 352,
            column: 43
          },
          end: {
            line: 352,
            column: 56
          }
        },
        line: 352
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 80,
            column: 6
          },
          end: {
            line: 86,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 6
          },
          end: {
            line: 86,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "1": {
        loc: {
          start: {
            line: 82,
            column: 8
          },
          end: {
            line: 85,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 82,
            column: 8
          },
          end: {
            line: 85,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 82
      },
      "2": {
        loc: {
          start: {
            line: 112,
            column: 6
          },
          end: {
            line: 114,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 112,
            column: 6
          },
          end: {
            line: 114,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 112
      },
      "3": {
        loc: {
          start: {
            line: 149,
            column: 6
          },
          end: {
            line: 155,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 149,
            column: 6
          },
          end: {
            line: 155,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 149
      },
      "4": {
        loc: {
          start: {
            line: 151,
            column: 8
          },
          end: {
            line: 154,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 151,
            column: 8
          },
          end: {
            line: 154,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 151
      },
      "5": {
        loc: {
          start: {
            line: 200,
            column: 6
          },
          end: {
            line: 202,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 200,
            column: 6
          },
          end: {
            line: 202,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 200
      },
      "6": {
        loc: {
          start: {
            line: 216,
            column: 27
          },
          end: {
            line: 216,
            column: 58
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 216,
            column: 38
          },
          end: {
            line: 216,
            column: 46
          }
        }, {
          start: {
            line: 216,
            column: 49
          },
          end: {
            line: 216,
            column: 58
          }
        }],
        line: 216
      },
      "7": {
        loc: {
          start: {
            line: 272,
            column: 4
          },
          end: {
            line: 272,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 272,
            column: 4
          },
          end: {
            line: 272,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 272
      },
      "8": {
        loc: {
          start: {
            line: 274,
            column: 4
          },
          end: {
            line: 342,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 275,
            column: 6
          },
          end: {
            line: 284,
            column: 10
          }
        }, {
          start: {
            line: 286,
            column: 6
          },
          end: {
            line: 297,
            column: 10
          }
        }, {
          start: {
            line: 299,
            column: 6
          },
          end: {
            line: 308,
            column: 10
          }
        }, {
          start: {
            line: 310,
            column: 6
          },
          end: {
            line: 323,
            column: 10
          }
        }, {
          start: {
            line: 325,
            column: 6
          },
          end: {
            line: 338,
            column: 10
          }
        }, {
          start: {
            line: 340,
            column: 6
          },
          end: {
            line: 341,
            column: 20
          }
        }],
        line: 274
      },
      "9": {
        loc: {
          start: {
            line: 315,
            column: 24
          },
          end: {
            line: 315,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 315,
            column: 52
          },
          end: {
            line: 315,
            column: 63
          }
        }, {
          start: {
            line: 315,
            column: 66
          },
          end: {
            line: 315,
            column: 78
          }
        }],
        line: 315
      },
      "10": {
        loc: {
          start: {
            line: 376,
            column: 15
          },
          end: {
            line: 378,
            column: 15
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 376,
            column: 15
          },
          end: {
            line: 376,
            column: 26
          }
        }, {
          start: {
            line: 377,
            column: 16
          },
          end: {
            line: 377,
            column: 100
          }
        }],
        line: 376
      },
      "11": {
        loc: {
          start: {
            line: 393,
            column: 15
          },
          end: {
            line: 393,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 393,
            column: 15
          },
          end: {
            line: 393,
            column: 26
          }
        }, {
          start: {
            line: 393,
            column: 30
          },
          end: {
            line: 393,
            column: 84
          }
        }],
        line: 393
      },
      "12": {
        loc: {
          start: {
            line: 394,
            column: 15
          },
          end: {
            line: 394,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 394,
            column: 15
          },
          end: {
            line: 394,
            column: 25
          }
        }, {
          start: {
            line: 394,
            column: 29
          },
          end: {
            line: 394,
            column: 82
          }
        }],
        line: 394
      },
      "13": {
        loc: {
          start: {
            line: 412,
            column: 15
          },
          end: {
            line: 416,
            column: 15
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 413,
            column: 16
          },
          end: {
            line: 413,
            column: 74
          }
        }, {
          start: {
            line: 415,
            column: 16
          },
          end: {
            line: 415,
            column: 77
          }
        }],
        line: 412
      },
      "14": {
        loc: {
          start: {
            line: 428,
            column: 17
          },
          end: {
            line: 428,
            column: 54
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 428,
            column: 28
          },
          end: {
            line: 428,
            column: 39
          }
        }, {
          start: {
            line: 428,
            column: 42
          },
          end: {
            line: 428,
            column: 54
          }
        }],
        line: 428
      },
      "15": {
        loc: {
          start: {
            line: 441,
            column: 25
          },
          end: {
            line: 441,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 441,
            column: 25
          },
          end: {
            line: 441,
            column: 32
          }
        }, {
          start: {
            line: 441,
            column: 36
          },
          end: {
            line: 441,
            column: 48
          }
        }],
        line: 441
      },
      "16": {
        loc: {
          start: {
            line: 444,
            column: 15
          },
          end: {
            line: 446,
            column: 15
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 444,
            column: 16
          },
          end: {
            line: 444,
            column: 28
          }
        }, {
          start: {
            line: 444,
            column: 32
          },
          end: {
            line: 444,
            column: 44
          }
        }, {
          start: {
            line: 445,
            column: 16
          },
          end: {
            line: 445,
            column: 79
          }
        }],
        line: 444
      },
      "17": {
        loc: {
          start: {
            line: 456,
            column: 38
          },
          end: {
            line: 456,
            column: 89
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 456,
            column: 57
          },
          end: {
            line: 456,
            column: 66
          }
        }, {
          start: {
            line: 456,
            column: 69
          },
          end: {
            line: 456,
            column: 89
          }
        }],
        line: 456
      },
      "18": {
        loc: {
          start: {
            line: 459,
            column: 31
          },
          end: {
            line: 459,
            column: 79
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 459,
            column: 50
          },
          end: {
            line: 459,
            column: 60
          }
        }, {
          start: {
            line: 459,
            column: 63
          },
          end: {
            line: 459,
            column: 79
          }
        }],
        line: 459
      },
      "19": {
        loc: {
          start: {
            line: 462,
            column: 38
          },
          end: {
            line: 462,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 462,
            column: 65
          },
          end: {
            line: 462,
            column: 75
          }
        }, {
          start: {
            line: 462,
            column: 78
          },
          end: {
            line: 462,
            column: 96
          }
        }],
        line: 462
      },
      "20": {
        loc: {
          start: {
            line: 465,
            column: 35
          },
          end: {
            line: 465,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 465,
            column: 46
          },
          end: {
            line: 465,
            column: 56
          }
        }, {
          start: {
            line: 465,
            column: 59
          },
          end: {
            line: 465,
            column: 75
          }
        }],
        line: 465
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0, 0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "448dd460daebb92a23b472964ce7b10fbe460d0b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_dp1gjrxc = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_dp1gjrxc();
import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, SafeAreaView, TouchableOpacity, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import Card from "../components/ui/Card";
import Button from "../components/ui/Button";
import { useCamera } from "../hooks/useCamera";
import { useVoice } from "../hooks/useVoice";
import { useNotifications } from "../hooks/useNotifications";
import { useOffline } from "../hooks/useOffline";
import { useExport } from "../hooks/useExport";
import { Camera, Mic, Bell, Wifi, WifiOff, ArrowLeft, FileText } from 'lucide-react-native';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export default function CoreFeaturesDemoScreen() {
  cov_dp1gjrxc().f[0]++;
  var _ref = (cov_dp1gjrxc().s[0]++, useState(false)),
    _ref2 = _slicedToArray(_ref, 2),
    loading = _ref2[0],
    setLoading = _ref2[1];
  var _ref3 = (cov_dp1gjrxc().s[1]++, useState(null)),
    _ref4 = _slicedToArray(_ref3, 2),
    demoResults = _ref4[0],
    setDemoResults = _ref4[1];
  var _ref5 = (cov_dp1gjrxc().s[2]++, useCamera()),
    cameraPermission = _ref5.hasPermission,
    requestCameraPermissions = _ref5.requestPermissions,
    isRecording = _ref5.isRecording,
    recordingDuration = _ref5.recordingDuration;
  var _ref6 = (cov_dp1gjrxc().s[3]++, useVoice()),
    voiceInitialized = _ref6.isInitialized,
    isListening = _ref6.isListening,
    isSpeaking = _ref6.isSpeaking,
    initializeVoice = _ref6.initializeVoice,
    startListening = _ref6.startListening,
    stopListening = _ref6.stopListening,
    speak = _ref6.speak;
  var _ref7 = (cov_dp1gjrxc().s[4]++, useNotifications()),
    notificationsInitialized = _ref7.isInitialized,
    initializeNotifications = _ref7.initialize,
    sendLocalNotification = _ref7.sendLocalNotification,
    scheduleDailyTips = _ref7.scheduleDailyTips;
  var _ref8 = (cov_dp1gjrxc().s[5]++, useOffline()),
    isOnline = _ref8.isOnline,
    syncStatus = _ref8.syncStatus,
    queueAction = _ref8.queueAction,
    syncPendingActions = _ref8.syncPendingActions;
  var _ref9 = (cov_dp1gjrxc().s[6]++, useExport()),
    generateProgressReport = _ref9.generateProgressReport,
    exportProgressReport = _ref9.exportProgressReport,
    shareExportedFile = _ref9.shareExportedFile,
    isGenerating = _ref9.isGenerating,
    progress = _ref9.progress;
  cov_dp1gjrxc().s[7]++;
  var handleCameraDemo = function () {
    var _ref0 = _asyncToGenerator(function* () {
      cov_dp1gjrxc().f[1]++;
      cov_dp1gjrxc().s[8]++;
      setLoading(true);
      cov_dp1gjrxc().s[9]++;
      try {
        cov_dp1gjrxc().s[10]++;
        if (!cameraPermission) {
          cov_dp1gjrxc().b[0][0]++;
          var granted = (cov_dp1gjrxc().s[11]++, yield requestCameraPermissions());
          cov_dp1gjrxc().s[12]++;
          if (!granted) {
            cov_dp1gjrxc().b[1][0]++;
            cov_dp1gjrxc().s[13]++;
            Alert.alert('Camera Demo', 'Camera permissions are required for video recording.');
            cov_dp1gjrxc().s[14]++;
            return;
          } else {
            cov_dp1gjrxc().b[1][1]++;
          }
        } else {
          cov_dp1gjrxc().b[0][1]++;
        }
        cov_dp1gjrxc().s[15]++;
        setDemoResults({
          type: 'camera',
          data: {
            hasPermission: true,
            isRecording: false,
            message: 'Camera is ready for video recording! This would integrate with the video analysis system.'
          }
        });
        cov_dp1gjrxc().s[16]++;
        Alert.alert('Camera Integration Ready!', 'Camera permissions granted. You can now record training videos for AI analysis.', [{
          text: 'OK'
        }]);
      } catch (error) {
        cov_dp1gjrxc().s[17]++;
        Alert.alert('Camera Demo', 'This demonstrates camera integration for video recording.');
      } finally {
        cov_dp1gjrxc().s[18]++;
        setLoading(false);
      }
    });
    return function handleCameraDemo() {
      return _ref0.apply(this, arguments);
    };
  }();
  cov_dp1gjrxc().s[19]++;
  var handleVoiceDemo = function () {
    var _ref1 = _asyncToGenerator(function* () {
      cov_dp1gjrxc().f[2]++;
      cov_dp1gjrxc().s[20]++;
      setLoading(true);
      cov_dp1gjrxc().s[21]++;
      try {
        cov_dp1gjrxc().s[22]++;
        if (!voiceInitialized) {
          cov_dp1gjrxc().b[2][0]++;
          cov_dp1gjrxc().s[23]++;
          yield initializeVoice();
        } else {
          cov_dp1gjrxc().b[2][1]++;
        }
        cov_dp1gjrxc().s[24]++;
        yield speak('Voice commands are now active. You can say start recording, stop recording, or give me a tip.');
        cov_dp1gjrxc().s[25]++;
        setDemoResults({
          type: 'voice',
          data: {
            isInitialized: true,
            availableCommands: ['Start recording', 'Stop recording', 'Take photo', 'Give me a tip', 'Show stats', 'Start training']
          }
        });
        cov_dp1gjrxc().s[26]++;
        Alert.alert('Voice Input Active!', 'Voice commands are now working. Try saying "Give me a tip" or "Start recording".', [{
          text: 'OK'
        }]);
      } catch (error) {
        cov_dp1gjrxc().s[27]++;
        Alert.alert('Voice Demo', 'This demonstrates voice command integration for hands-free control.');
      } finally {
        cov_dp1gjrxc().s[28]++;
        setLoading(false);
      }
    });
    return function handleVoiceDemo() {
      return _ref1.apply(this, arguments);
    };
  }();
  cov_dp1gjrxc().s[29]++;
  var handleNotificationsDemo = function () {
    var _ref10 = _asyncToGenerator(function* () {
      cov_dp1gjrxc().f[3]++;
      cov_dp1gjrxc().s[30]++;
      setLoading(true);
      cov_dp1gjrxc().s[31]++;
      try {
        cov_dp1gjrxc().s[32]++;
        if (!notificationsInitialized) {
          cov_dp1gjrxc().b[3][0]++;
          var initialized = (cov_dp1gjrxc().s[33]++, yield initializeNotifications());
          cov_dp1gjrxc().s[34]++;
          if (!initialized) {
            cov_dp1gjrxc().b[4][0]++;
            cov_dp1gjrxc().s[35]++;
            Alert.alert('Notifications Demo', 'Notification permissions are required.');
            cov_dp1gjrxc().s[36]++;
            return;
          } else {
            cov_dp1gjrxc().b[4][1]++;
          }
        } else {
          cov_dp1gjrxc().b[3][1]++;
        }
        cov_dp1gjrxc().s[37]++;
        yield sendLocalNotification({
          title: '🎾 AceMind Demo',
          body: 'Push notifications are working! You\'ll receive tips, reminders, and achievement alerts.',
          data: {
            type: 'demo'
          }
        });
        cov_dp1gjrxc().s[38]++;
        yield scheduleDailyTips();
        cov_dp1gjrxc().s[39]++;
        setDemoResults({
          type: 'notifications',
          data: {
            isInitialized: true,
            demoSent: true,
            dailyTipsScheduled: true
          }
        });
        cov_dp1gjrxc().s[40]++;
        Alert.alert('Notifications Active!', 'You should see a demo notification now. Daily tips have been scheduled.', [{
          text: 'OK'
        }]);
      } catch (error) {
        cov_dp1gjrxc().s[41]++;
        Alert.alert('Notifications Demo', 'This demonstrates push notification capabilities.');
      } finally {
        cov_dp1gjrxc().s[42]++;
        setLoading(false);
      }
    });
    return function handleNotificationsDemo() {
      return _ref10.apply(this, arguments);
    };
  }();
  cov_dp1gjrxc().s[43]++;
  var handleOfflineDemo = function () {
    var _ref11 = _asyncToGenerator(function* () {
      cov_dp1gjrxc().f[4]++;
      cov_dp1gjrxc().s[44]++;
      setLoading(true);
      cov_dp1gjrxc().s[45]++;
      try {
        cov_dp1gjrxc().s[46]++;
        yield queueAction('CREATE_TRAINING_SESSION', {
          title: 'Demo Offline Session',
          session_type: 'video_analysis',
          duration_minutes: 30,
          overall_score: 85
        });
        cov_dp1gjrxc().s[47]++;
        if (isOnline) {
          cov_dp1gjrxc().b[5][0]++;
          cov_dp1gjrxc().s[48]++;
          yield syncPendingActions();
        } else {
          cov_dp1gjrxc().b[5][1]++;
        }
        cov_dp1gjrxc().s[49]++;
        setDemoResults({
          type: 'offline',
          data: {
            isOnline: isOnline,
            pendingActions: syncStatus.pendingActions,
            lastSync: syncStatus.lastSyncTime,
            demoActionQueued: true
          }
        });
        cov_dp1gjrxc().s[50]++;
        Alert.alert('Offline Support Active!', `Network status: ${isOnline ? (cov_dp1gjrxc().b[6][0]++, 'Online') : (cov_dp1gjrxc().b[6][1]++, 'Offline')}\nPending actions: ${syncStatus.pendingActions}\n\nData will sync when connection is restored.`, [{
          text: 'OK'
        }]);
      } catch (error) {
        cov_dp1gjrxc().s[51]++;
        Alert.alert('Offline Demo', 'This demonstrates offline data caching and sync capabilities.');
      } finally {
        cov_dp1gjrxc().s[52]++;
        setLoading(false);
      }
    });
    return function handleOfflineDemo() {
      return _ref11.apply(this, arguments);
    };
  }();
  cov_dp1gjrxc().s[53]++;
  var handleExportDemo = function () {
    var _ref12 = _asyncToGenerator(function* () {
      cov_dp1gjrxc().f[5]++;
      cov_dp1gjrxc().s[54]++;
      setLoading(true);
      cov_dp1gjrxc().s[55]++;
      try {
        var report = (cov_dp1gjrxc().s[56]++, yield generateProgressReport('demo-user', {
          format: 'json',
          dateRange: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            end: new Date()
          }
        }));
        var fileUri = (cov_dp1gjrxc().s[57]++, yield exportProgressReport(report, {
          format: 'json'
        }));
        cov_dp1gjrxc().s[58]++;
        setDemoResults({
          type: 'export',
          data: {
            reportGenerated: true,
            fileUri: fileUri,
            format: 'json',
            summary: report.summary
          }
        });
        cov_dp1gjrxc().s[59]++;
        Alert.alert('Export Complete!', `Progress report generated and exported!\n\nTotal Sessions: ${report.summary.totalSessions}\nAverage Score: ${report.summary.averageScore}`, [{
          text: 'OK'
        }, {
          text: 'Share',
          onPress: function onPress() {
            cov_dp1gjrxc().f[6]++;
            cov_dp1gjrxc().s[60]++;
            return shareExportedFile(fileUri).catch(function () {
              cov_dp1gjrxc().f[7]++;
              cov_dp1gjrxc().s[61]++;
              return Alert.alert('Share', 'Sharing not available in demo mode');
            });
          }
        }]);
      } catch (error) {
        cov_dp1gjrxc().s[62]++;
        Alert.alert('Export Demo', 'This demonstrates progress report generation and export capabilities.');
      } finally {
        cov_dp1gjrxc().s[63]++;
        setLoading(false);
      }
    });
    return function handleExportDemo() {
      return _ref12.apply(this, arguments);
    };
  }();
  cov_dp1gjrxc().s[64]++;
  var renderDemoResults = function renderDemoResults() {
    cov_dp1gjrxc().f[8]++;
    cov_dp1gjrxc().s[65]++;
    if (!demoResults) {
      cov_dp1gjrxc().b[7][0]++;
      cov_dp1gjrxc().s[66]++;
      return null;
    } else {
      cov_dp1gjrxc().b[7][1]++;
    }
    cov_dp1gjrxc().s[67]++;
    switch (demoResults.type) {
      case 'camera':
        cov_dp1gjrxc().b[8][0]++;
        cov_dp1gjrxc().s[68]++;
        return _jsxs(Card, {
          style: styles.resultCard,
          children: [_jsx(Text, {
            style: styles.resultTitle,
            children: "\uD83D\uDCF9 Camera Integration"
          }), _jsx(Text, {
            style: styles.resultText,
            children: "\u2705 Camera permissions granted"
          }), _jsx(Text, {
            style: styles.resultText,
            children: "\u2705 Video recording ready"
          }), _jsx(Text, {
            style: styles.resultText,
            children: "\u2705 Photo capture available"
          }), _jsx(Text, {
            style: styles.resultText,
            children: "\u2705 Gallery save functionality"
          })]
        });
      case 'voice':
        cov_dp1gjrxc().b[8][1]++;
        cov_dp1gjrxc().s[69]++;
        return _jsxs(Card, {
          style: styles.resultCard,
          children: [_jsx(Text, {
            style: styles.resultTitle,
            children: "\uD83C\uDFA4 Voice Commands"
          }), _jsx(Text, {
            style: styles.resultText,
            children: "\u2705 Voice recognition initialized"
          }), _jsx(Text, {
            style: styles.resultText,
            children: "\u2705 Text-to-speech working"
          }), _jsx(Text, {
            style: styles.label,
            children: "Available Commands:"
          }), demoResults.data.availableCommands.map(function (cmd, index) {
            cov_dp1gjrxc().f[9]++;
            cov_dp1gjrxc().s[70]++;
            return _jsxs(Text, {
              style: styles.commandText,
              children: ["\u2022 \"", cmd, "\""]
            }, index);
          })]
        });
      case 'notifications':
        cov_dp1gjrxc().b[8][2]++;
        cov_dp1gjrxc().s[71]++;
        return _jsxs(Card, {
          style: styles.resultCard,
          children: [_jsx(Text, {
            style: styles.resultTitle,
            children: "\uD83D\uDD14 Push Notifications"
          }), _jsx(Text, {
            style: styles.resultText,
            children: "\u2705 Notification permissions granted"
          }), _jsx(Text, {
            style: styles.resultText,
            children: "\u2705 Demo notification sent"
          }), _jsx(Text, {
            style: styles.resultText,
            children: "\u2705 Daily tips scheduled"
          }), _jsx(Text, {
            style: styles.resultText,
            children: "\u2705 Achievement alerts ready"
          })]
        });
      case 'offline':
        cov_dp1gjrxc().b[8][3]++;
        cov_dp1gjrxc().s[72]++;
        return _jsxs(Card, {
          style: styles.resultCard,
          children: [_jsx(Text, {
            style: styles.resultTitle,
            children: "\uD83D\uDCF1 Offline Support"
          }), _jsxs(Text, {
            style: styles.resultText,
            children: ["Network: ", demoResults.data.isOnline ? (cov_dp1gjrxc().b[9][0]++, '🟢 Online') : (cov_dp1gjrxc().b[9][1]++, '🔴 Offline')]
          }), _jsxs(Text, {
            style: styles.resultText,
            children: ["Pending Actions: ", demoResults.data.pendingActions]
          }), _jsx(Text, {
            style: styles.resultText,
            children: "\u2705 Demo action queued"
          }), _jsx(Text, {
            style: styles.resultText,
            children: "\u2705 Auto-sync when online"
          })]
        });
      case 'export':
        cov_dp1gjrxc().b[8][4]++;
        cov_dp1gjrxc().s[73]++;
        return _jsxs(Card, {
          style: styles.resultCard,
          children: [_jsx(Text, {
            style: styles.resultTitle,
            children: "\uD83D\uDCCA Export Features"
          }), _jsx(Text, {
            style: styles.resultText,
            children: "\u2705 Progress report generated"
          }), _jsx(Text, {
            style: styles.resultText,
            children: "\u2705 File exported successfully"
          }), _jsxs(Text, {
            style: styles.resultText,
            children: ["Sessions: ", demoResults.data.summary.totalSessions]
          }), _jsxs(Text, {
            style: styles.resultText,
            children: ["Avg Score: ", demoResults.data.summary.averageScore]
          })]
        });
      default:
        cov_dp1gjrxc().b[8][5]++;
        cov_dp1gjrxc().s[74]++;
        return null;
    }
  };
  cov_dp1gjrxc().s[75]++;
  return _jsx(SafeAreaView, {
    style: styles.container,
    children: _jsxs(LinearGradient, {
      colors: ['#1e3a8a', '#3b82f6', '#60a5fa'],
      style: styles.gradient,
      children: [_jsxs(View, {
        style: styles.header,
        children: [_jsx(TouchableOpacity, {
          onPress: function onPress() {
            cov_dp1gjrxc().f[10]++;
            cov_dp1gjrxc().s[76]++;
            return router.back();
          },
          style: styles.backButton,
          children: _jsx(ArrowLeft, {
            size: 24,
            color: "white"
          })
        }), _jsx(Text, {
          style: styles.title,
          children: "Core Features Demo"
        })]
      }), _jsxs(ScrollView, {
        style: styles.content,
        showsVerticalScrollIndicator: false,
        children: [_jsx(Text, {
          style: styles.subtitle,
          children: "Test all core functionality implementations"
        }), _jsxs(View, {
          style: styles.demoGrid,
          children: [_jsxs(Card, {
            style: styles.demoCard,
            children: [_jsx(Camera, {
              size: 32,
              color: "#3b82f6",
              style: styles.demoIcon
            }), _jsx(Text, {
              style: styles.demoTitle,
              children: "Camera Integration"
            }), _jsx(Text, {
              style: styles.demoDescription,
              children: "Video recording and photo capture for training analysis"
            }), _jsx(Button, {
              title: "Test Camera",
              onPress: handleCameraDemo,
              loading: loading,
              style: styles.demoButton
            }), (cov_dp1gjrxc().b[10][0]++, isRecording) && (cov_dp1gjrxc().b[10][1]++, _jsxs(Text, {
              style: styles.statusText,
              children: ["\uD83D\uDD34 Recording: ", recordingDuration.toFixed(1), "s"]
            }))]
          }), _jsxs(Card, {
            style: styles.demoCard,
            children: [_jsx(Mic, {
              size: 32,
              color: "#10b981",
              style: styles.demoIcon
            }), _jsx(Text, {
              style: styles.demoTitle,
              children: "Voice Input"
            }), _jsx(Text, {
              style: styles.demoDescription,
              children: "Voice commands and text-to-speech for hands-free control"
            }), _jsx(Button, {
              title: "Test Voice",
              onPress: handleVoiceDemo,
              loading: loading,
              style: styles.demoButton
            }), (cov_dp1gjrxc().b[11][0]++, isListening) && (cov_dp1gjrxc().b[11][1]++, _jsx(Text, {
              style: styles.statusText,
              children: "\uD83C\uDFA4 Listening..."
            })), (cov_dp1gjrxc().b[12][0]++, isSpeaking) && (cov_dp1gjrxc().b[12][1]++, _jsx(Text, {
              style: styles.statusText,
              children: "\uD83D\uDD0A Speaking..."
            }))]
          }), _jsxs(Card, {
            style: styles.demoCard,
            children: [_jsx(Bell, {
              size: 32,
              color: "#f59e0b",
              style: styles.demoIcon
            }), _jsx(Text, {
              style: styles.demoTitle,
              children: "Push Notifications"
            }), _jsx(Text, {
              style: styles.demoDescription,
              children: "Smart notifications for tips, reminders, and achievements"
            }), _jsx(Button, {
              title: "Test Notifications",
              onPress: handleNotificationsDemo,
              loading: loading,
              style: styles.demoButton
            })]
          }), _jsxs(Card, {
            style: styles.demoCard,
            children: [isOnline ? (cov_dp1gjrxc().b[13][0]++, _jsx(Wifi, {
              size: 32,
              color: "#8b5cf6",
              style: styles.demoIcon
            })) : (cov_dp1gjrxc().b[13][1]++, _jsx(WifiOff, {
              size: 32,
              color: "#ef4444",
              style: styles.demoIcon
            })), _jsx(Text, {
              style: styles.demoTitle,
              children: "Offline Support"
            }), _jsx(Text, {
              style: styles.demoDescription,
              children: "Data caching and sync for offline functionality"
            }), _jsx(Button, {
              title: "Test Offline",
              onPress: handleOfflineDemo,
              loading: loading,
              style: styles.demoButton
            }), _jsxs(Text, {
              style: styles.statusText,
              children: [isOnline ? (cov_dp1gjrxc().b[14][0]++, '🟢 Online') : (cov_dp1gjrxc().b[14][1]++, '🔴 Offline'), " \u2022 ", syncStatus.pendingActions, " pending"]
            })]
          }), _jsxs(Card, {
            style: styles.demoCard,
            children: [_jsx(FileText, {
              size: 32,
              color: "#ef4444",
              style: styles.demoIcon
            }), _jsx(Text, {
              style: styles.demoTitle,
              children: "Export Features"
            }), _jsx(Text, {
              style: styles.demoDescription,
              children: "Generate and export progress reports in multiple formats"
            }), _jsx(Button, {
              title: "Test Export",
              onPress: handleExportDemo,
              loading: (cov_dp1gjrxc().b[15][0]++, loading) || (cov_dp1gjrxc().b[15][1]++, isGenerating),
              style: styles.demoButton
            }), ((cov_dp1gjrxc().b[16][0]++, isGenerating) || (cov_dp1gjrxc().b[16][1]++, progress > 0)) && (cov_dp1gjrxc().b[16][2]++, _jsxs(Text, {
              style: styles.statusText,
              children: ["\uD83D\uDCCA Progress: ", progress, "%"]
            }))]
          })]
        }), renderDemoResults(), _jsxs(Card, {
          style: styles.infoCard,
          children: [_jsx(Text, {
            style: styles.infoTitle,
            children: "\uD83D\uDE80 Core Features Status"
          }), _jsxs(View, {
            style: styles.featureList,
            children: [_jsxs(Text, {
              style: styles.featureItem,
              children: ["\u2705 Camera Integration ", cameraPermission ? (cov_dp1gjrxc().b[17][0]++, '(Ready)') : (cov_dp1gjrxc().b[17][1]++, '(Needs Permission)')]
            }), _jsxs(Text, {
              style: styles.featureItem,
              children: ["\u2705 Voice Input ", voiceInitialized ? (cov_dp1gjrxc().b[18][0]++, '(Active)') : (cov_dp1gjrxc().b[18][1]++, '(Initializing)')]
            }), _jsxs(Text, {
              style: styles.featureItem,
              children: ["\u2705 Push Notifications ", notificationsInitialized ? (cov_dp1gjrxc().b[19][0]++, '(Active)') : (cov_dp1gjrxc().b[19][1]++, '(Setup Required)')]
            }), _jsxs(Text, {
              style: styles.featureItem,
              children: ["\u2705 Offline Support ", isOnline ? (cov_dp1gjrxc().b[20][0]++, '(Online)') : (cov_dp1gjrxc().b[20][1]++, '(Offline Mode)')]
            }), _jsx(Text, {
              style: styles.featureItem,
              children: "\u2705 Export Features (Ready)"
            })]
          }), _jsx(Text, {
            style: styles.infoNote,
            children: "All core functionality is implemented and ready for production use!"
          })]
        })]
      })]
    })
  });
}
var styles = (cov_dp1gjrxc().s[77]++, StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10
  },
  backButton: {
    marginRight: 15
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white'
  },
  content: {
    flex: 1,
    paddingHorizontal: 20
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: 30
  },
  demoGrid: {
    gap: 15,
    marginBottom: 20
  },
  demoCard: {
    padding: 20,
    alignItems: 'center'
  },
  demoIcon: {
    marginBottom: 10
  },
  demoTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
    textAlign: 'center'
  },
  demoDescription: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 15,
    lineHeight: 20
  },
  demoButton: {
    minWidth: 150
  },
  statusText: {
    fontSize: 12,
    color: '#059669',
    marginTop: 8,
    textAlign: 'center'
  },
  resultCard: {
    padding: 20,
    marginBottom: 20
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 15
  },
  resultText: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 5
  },
  label: {
    fontWeight: '600',
    color: '#1f2937',
    marginTop: 10,
    marginBottom: 5
  },
  commandText: {
    fontSize: 13,
    color: '#6b7280',
    marginLeft: 10,
    marginBottom: 3
  },
  infoCard: {
    padding: 20,
    marginBottom: 30
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 15
  },
  featureList: {
    marginBottom: 15
  },
  featureItem: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 5
  },
  infoNote: {
    fontSize: 12,
    color: '#6b7280',
    fontStyle: 'italic',
    textAlign: 'center'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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