fbc414a5052410c38cf622eab58eca69
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.get = get;
exports.getEnforcing = getEnforcing;
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
function get(name) {
  return null;
}
function getEnforcing(name) {
  var module = get(name);
  (0, _invariant.default)(module != null, "TurboModuleRegistry.getEnforcing(...): '" + name + "' could not be found. " + 'Verify that a module by this name is registered in the native binary.');
  return module;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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