8bb8806712c19894c94208f8b425aec1
"use strict";

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _createForOfIteratorHelperLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/createForOfIteratorHelperLoose"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var ChildListCollection = function () {
  function ChildListCollection() {
    (0, _classCallCheck2.default)(this, ChildListCollection);
    this._cellKeyToChildren = new Map();
    this._childrenToCellKey = new Map();
  }
  return (0, _createClass2.default)(ChildListCollection, [{
    key: "add",
    value: function add(list, cellKey) {
      var _this$_cellKeyToChild;
      (0, _invariant.default)(!this._childrenToCellKey.has(list), 'Trying to add already present child list');
      var cellLists = (_this$_cellKeyToChild = this._cellKeyToChildren.get(cellKey)) !== null && _this$_cellKeyToChild !== void 0 ? _this$_cellKeyToChild : new Set();
      cellLists.add(list);
      this._cellKeyToChildren.set(cellKey, cellLists);
      this._childrenToCellKey.set(list, cellKey);
    }
  }, {
    key: "remove",
    value: function remove(list) {
      var cellKey = this._childrenToCellKey.get(list);
      (0, _invariant.default)(cellKey != null, 'Trying to remove non-present child list');
      this._childrenToCellKey.delete(list);
      var cellLists = this._cellKeyToChildren.get(cellKey);
      (0, _invariant.default)(cellLists, '_cellKeyToChildren should contain cellKey');
      cellLists.delete(list);
      if (cellLists.size === 0) {
        this._cellKeyToChildren.delete(cellKey);
      }
    }
  }, {
    key: "forEach",
    value: function forEach(fn) {
      for (var _iterator = (0, _createForOfIteratorHelperLoose2.default)(this._cellKeyToChildren.values()), _step; !(_step = _iterator()).done;) {
        var listSet = _step.value;
        for (var _iterator2 = (0, _createForOfIteratorHelperLoose2.default)(listSet), _step2; !(_step2 = _iterator2()).done;) {
          var list = _step2.value;
          fn(list);
        }
      }
    }
  }, {
    key: "forEachInCell",
    value: function forEachInCell(cellKey, fn) {
      var _this$_cellKeyToChild2;
      var listSet = (_this$_cellKeyToChild2 = this._cellKeyToChildren.get(cellKey)) !== null && _this$_cellKeyToChild2 !== void 0 ? _this$_cellKeyToChild2 : [];
      for (var _iterator3 = (0, _createForOfIteratorHelperLoose2.default)(listSet), _step3; !(_step3 = _iterator3()).done;) {
        var list = _step3.value;
        fn(list);
      }
    }
  }, {
    key: "anyInCell",
    value: function anyInCell(cellKey, fn) {
      var _this$_cellKeyToChild3;
      var listSet = (_this$_cellKeyToChild3 = this._cellKeyToChildren.get(cellKey)) !== null && _this$_cellKeyToChild3 !== void 0 ? _this$_cellKeyToChild3 : [];
      for (var _iterator4 = (0, _createForOfIteratorHelperLoose2.default)(listSet), _step4; !(_step4 = _iterator4()).done;) {
        var list = _step4.value;
        if (fn(list)) {
          return true;
        }
      }
      return false;
    }
  }, {
    key: "size",
    value: function size() {
      return this._childrenToCellKey.size;
    }
  }]);
}();
exports.default = ChildListCollection;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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