df6094a9f30c20fd59390cf2062fda45
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_t17p4ghg0() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\storage.ts";
  var hash = "eea72b807f2158e46df90e2bdab390ca822a8bc9";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\storage.ts",
    statementMap: {
      "0": {
        start: {
          line: 16,
          column: 23
        },
        end: {
          line: 16,
          column: 40
        }
      },
      "1": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 66,
          column: 5
        }
      },
      "2": {
        start: {
          line: 29,
          column: 24
        },
        end: {
          line: 29,
          column: 34
        }
      },
      "3": {
        start: {
          line: 30,
          column: 28
        },
        end: {
          line: 30,
          column: 62
        }
      },
      "4": {
        start: {
          line: 31,
          column: 29
        },
        end: {
          line: 31,
          column: 114
        }
      },
      "5": {
        start: {
          line: 34,
          column: 30
        },
        end: {
          line: 39,
          column: 10
        }
      },
      "6": {
        start: {
          line: 41,
          column: 6
        },
        end: {
          line: 48,
          column: 7
        }
      },
      "7": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 46
        }
      },
      "8": {
        start: {
          line: 43,
          column: 8
        },
        end: {
          line: 47,
          column: 10
        }
      },
      "9": {
        start: {
          line: 51,
          column: 32
        },
        end: {
          line: 53,
          column: 32
        }
      },
      "10": {
        start: {
          line: 55,
          column: 6
        },
        end: {
          line: 58,
          column: 8
        }
      },
      "11": {
        start: {
          line: 60,
          column: 6
        },
        end: {
          line: 60,
          column: 53
        }
      },
      "12": {
        start: {
          line: 61,
          column: 6
        },
        end: {
          line: 65,
          column: 8
        }
      },
      "13": {
        start: {
          line: 73,
          column: 4
        },
        end: {
          line: 93,
          column: 5
        }
      },
      "14": {
        start: {
          line: 74,
          column: 24
        },
        end: {
          line: 76,
          column: 27
        }
      },
      "15": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 84,
          column: 7
        }
      },
      "16": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 79,
          column: 46
        }
      },
      "17": {
        start: {
          line: 80,
          column: 8
        },
        end: {
          line: 83,
          column: 10
        }
      },
      "18": {
        start: {
          line: 86,
          column: 6
        },
        end: {
          line: 86,
          column: 31
        }
      },
      "19": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 88,
          column: 60
        }
      },
      "20": {
        start: {
          line: 89,
          column: 6
        },
        end: {
          line: 92,
          column: 8
        }
      },
      "21": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 122,
          column: 5
        }
      },
      "22": {
        start: {
          line: 101,
          column: 30
        },
        end: {
          line: 103,
          column: 45
        }
      },
      "23": {
        start: {
          line: 105,
          column: 6
        },
        end: {
          line: 111,
          column: 7
        }
      },
      "24": {
        start: {
          line: 106,
          column: 8
        },
        end: {
          line: 106,
          column: 50
        }
      },
      "25": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 110,
          column: 10
        }
      },
      "26": {
        start: {
          line: 113,
          column: 6
        },
        end: {
          line: 115,
          column: 8
        }
      },
      "27": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 117,
          column: 64
        }
      },
      "28": {
        start: {
          line: 118,
          column: 6
        },
        end: {
          line: 121,
          column: 8
        }
      },
      "29": {
        start: {
          line: 129,
          column: 4
        },
        end: {
          line: 155,
          column: 5
        }
      },
      "30": {
        start: {
          line: 130,
          column: 30
        },
        end: {
          line: 136,
          column: 10
        }
      },
      "31": {
        start: {
          line: 138,
          column: 6
        },
        end: {
          line: 144,
          column: 7
        }
      },
      "32": {
        start: {
          line: 139,
          column: 8
        },
        end: {
          line: 139,
          column: 50
        }
      },
      "33": {
        start: {
          line: 140,
          column: 8
        },
        end: {
          line: 143,
          column: 10
        }
      },
      "34": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 148,
          column: 8
        }
      },
      "35": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 58
        }
      },
      "36": {
        start: {
          line: 151,
          column: 6
        },
        end: {
          line: 154,
          column: 8
        }
      },
      "37": {
        start: {
          line: 162,
          column: 4
        },
        end: {
          line: 188,
          column: 5
        }
      },
      "38": {
        start: {
          line: 163,
          column: 30
        },
        end: {
          line: 167,
          column: 10
        }
      },
      "39": {
        start: {
          line: 169,
          column: 6
        },
        end: {
          line: 175,
          column: 7
        }
      },
      "40": {
        start: {
          line: 170,
          column: 8
        },
        end: {
          line: 170,
          column: 49
        }
      },
      "41": {
        start: {
          line: 171,
          column: 8
        },
        end: {
          line: 174,
          column: 10
        }
      },
      "42": {
        start: {
          line: 177,
          column: 23
        },
        end: {
          line: 177,
          column: 63
        }
      },
      "43": {
        start: {
          line: 179,
          column: 6
        },
        end: {
          line: 181,
          column: 8
        }
      },
      "44": {
        start: {
          line: 183,
          column: 6
        },
        end: {
          line: 183,
          column: 63
        }
      },
      "45": {
        start: {
          line: 184,
          column: 6
        },
        end: {
          line: 187,
          column: 8
        }
      },
      "46": {
        start: {
          line: 195,
          column: 4
        },
        end: {
          line: 217,
          column: 5
        }
      },
      "47": {
        start: {
          line: 196,
          column: 30
        },
        end: {
          line: 200,
          column: 8
        }
      },
      "48": {
        start: {
          line: 202,
          column: 6
        },
        end: {
          line: 208,
          column: 7
        }
      },
      "49": {
        start: {
          line: 203,
          column: 8
        },
        end: {
          line: 203,
          column: 53
        }
      },
      "50": {
        start: {
          line: 204,
          column: 8
        },
        end: {
          line: 207,
          column: 10
        }
      },
      "51": {
        start: {
          line: 210,
          column: 6
        },
        end: {
          line: 210,
          column: 31
        }
      },
      "52": {
        start: {
          line: 212,
          column: 6
        },
        end: {
          line: 212,
          column: 67
        }
      },
      "53": {
        start: {
          line: 213,
          column: 6
        },
        end: {
          line: 216,
          column: 8
        }
      },
      "54": {
        start: {
          line: 221,
          column: 30
        },
        end: {
          line: 221,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 21,
            column: 2
          },
          end: {
            line: 21,
            column: 3
          }
        },
        loc: {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 67,
            column: 3
          }
        },
        line: 26
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 72,
            column: 2
          },
          end: {
            line: 72,
            column: 3
          }
        },
        loc: {
          start: {
            line: 72,
            column: 85
          },
          end: {
            line: 94,
            column: 3
          }
        },
        line: 72
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 99,
            column: 2
          },
          end: {
            line: 99,
            column: 3
          }
        },
        loc: {
          start: {
            line: 99,
            column: 107
          },
          end: {
            line: 123,
            column: 3
          }
        },
        line: 99
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 128,
            column: 2
          },
          end: {
            line: 128,
            column: 3
          }
        },
        loc: {
          start: {
            line: 128,
            column: 82
          },
          end: {
            line: 156,
            column: 3
          }
        },
        line: 128
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 161,
            column: 2
          },
          end: {
            line: 161,
            column: 3
          }
        },
        loc: {
          start: {
            line: 161,
            column: 78
          },
          end: {
            line: 189,
            column: 3
          }
        },
        line: 161
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 194,
            column: 2
          },
          end: {
            line: 194,
            column: 3
          }
        },
        loc: {
          start: {
            line: 194,
            column: 70
          },
          end: {
            line: 218,
            column: 3
          }
        },
        line: 194
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 30,
            column: 28
          },
          end: {
            line: 30,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 28
          },
          end: {
            line: 30,
            column: 53
          }
        }, {
          start: {
            line: 30,
            column: 57
          },
          end: {
            line: 30,
            column: 62
          }
        }],
        line: 30
      },
      "1": {
        loc: {
          start: {
            line: 41,
            column: 6
          },
          end: {
            line: 48,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 6
          },
          end: {
            line: 48,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "2": {
        loc: {
          start: {
            line: 64,
            column: 15
          },
          end: {
            line: 64,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 64,
            column: 40
          },
          end: {
            line: 64,
            column: 53
          }
        }, {
          start: {
            line: 64,
            column: 56
          },
          end: {
            line: 64,
            column: 80
          }
        }],
        line: 64
      },
      "3": {
        loc: {
          start: {
            line: 78,
            column: 6
          },
          end: {
            line: 84,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 6
          },
          end: {
            line: 84,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 78
      },
      "4": {
        loc: {
          start: {
            line: 91,
            column: 15
          },
          end: {
            line: 91,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 91,
            column: 40
          },
          end: {
            line: 91,
            column: 53
          }
        }, {
          start: {
            line: 91,
            column: 56
          },
          end: {
            line: 91,
            column: 80
          }
        }],
        line: 91
      },
      "5": {
        loc: {
          start: {
            line: 99,
            column: 39
          },
          end: {
            line: 99,
            column: 63
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 99,
            column: 59
          },
          end: {
            line: 99,
            column: 63
          }
        }],
        line: 99
      },
      "6": {
        loc: {
          start: {
            line: 105,
            column: 6
          },
          end: {
            line: 111,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 105,
            column: 6
          },
          end: {
            line: 111,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 105
      },
      "7": {
        loc: {
          start: {
            line: 120,
            column: 15
          },
          end: {
            line: 120,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 120,
            column: 40
          },
          end: {
            line: 120,
            column: 53
          }
        }, {
          start: {
            line: 120,
            column: 56
          },
          end: {
            line: 120,
            column: 80
          }
        }],
        line: 120
      },
      "8": {
        loc: {
          start: {
            line: 138,
            column: 6
          },
          end: {
            line: 144,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 138,
            column: 6
          },
          end: {
            line: 144,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 138
      },
      "9": {
        loc: {
          start: {
            line: 147,
            column: 15
          },
          end: {
            line: 147,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 147,
            column: 15
          },
          end: {
            line: 147,
            column: 19
          }
        }, {
          start: {
            line: 147,
            column: 23
          },
          end: {
            line: 147,
            column: 25
          }
        }],
        line: 147
      },
      "10": {
        loc: {
          start: {
            line: 153,
            column: 15
          },
          end: {
            line: 153,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 153,
            column: 40
          },
          end: {
            line: 153,
            column: 53
          }
        }, {
          start: {
            line: 153,
            column: 56
          },
          end: {
            line: 153,
            column: 80
          }
        }],
        line: 153
      },
      "11": {
        loc: {
          start: {
            line: 169,
            column: 6
          },
          end: {
            line: 175,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 169,
            column: 6
          },
          end: {
            line: 175,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 169
      },
      "12": {
        loc: {
          start: {
            line: 177,
            column: 23
          },
          end: {
            line: 177,
            column: 63
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 177,
            column: 49
          },
          end: {
            line: 177,
            column: 56
          }
        }, {
          start: {
            line: 177,
            column: 59
          },
          end: {
            line: 177,
            column: 63
          }
        }],
        line: 177
      },
      "13": {
        loc: {
          start: {
            line: 177,
            column: 23
          },
          end: {
            line: 177,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 177,
            column: 23
          },
          end: {
            line: 177,
            column: 27
          }
        }, {
          start: {
            line: 177,
            column: 31
          },
          end: {
            line: 177,
            column: 46
          }
        }],
        line: 177
      },
      "14": {
        loc: {
          start: {
            line: 186,
            column: 15
          },
          end: {
            line: 186,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 186,
            column: 40
          },
          end: {
            line: 186,
            column: 53
          }
        }, {
          start: {
            line: 186,
            column: 56
          },
          end: {
            line: 186,
            column: 80
          }
        }],
        line: 186
      },
      "15": {
        loc: {
          start: {
            line: 202,
            column: 6
          },
          end: {
            line: 208,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 202,
            column: 6
          },
          end: {
            line: 208,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 202
      },
      "16": {
        loc: {
          start: {
            line: 202,
            column: 10
          },
          end: {
            line: 202,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 202,
            column: 10
          },
          end: {
            line: 202,
            column: 15
          }
        }, {
          start: {
            line: 202,
            column: 19
          },
          end: {
            line: 202,
            column: 60
          }
        }],
        line: 202
      },
      "17": {
        loc: {
          start: {
            line: 215,
            column: 15
          },
          end: {
            line: 215,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 215,
            column: 40
          },
          end: {
            line: 215,
            column: 53
          }
        }, {
          start: {
            line: 215,
            column: 56
          },
          end: {
            line: 215,
            column: 80
          }
        }],
        line: 215
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "eea72b807f2158e46df90e2bdab390ca822a8bc9"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_t17p4ghg0 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_t17p4ghg0();
import { supabase } from "../lib/supabase";
var StorageService = function () {
  function StorageService() {
    _classCallCheck(this, StorageService);
    this.bucketName = (cov_t17p4ghg0().s[0]++, 'training-videos');
  }
  return _createClass(StorageService, [{
    key: "uploadVideo",
    value: (function () {
      var _uploadVideo = _asyncToGenerator(function* (file, userId, filename, onProgress) {
        cov_t17p4ghg0().f[0]++;
        cov_t17p4ghg0().s[1]++;
        try {
          var timestamp = (cov_t17p4ghg0().s[2]++, Date.now());
          var fileExtension = (cov_t17p4ghg0().s[3]++, (cov_t17p4ghg0().b[0][0]++, filename.split('.').pop()) || (cov_t17p4ghg0().b[0][1]++, 'mp4'));
          var uniqueFilename = (cov_t17p4ghg0().s[4]++, `${userId}/${timestamp}-${filename.replace(/[^a-zA-Z0-9.-]/g, '_')}.${fileExtension}`);
          var _ref = (cov_t17p4ghg0().s[5]++, yield supabase.storage.from(this.bucketName).upload(uniqueFilename, file, {
              cacheControl: '3600',
              upsert: false
            })),
            data = _ref.data,
            error = _ref.error;
          cov_t17p4ghg0().s[6]++;
          if (error) {
            cov_t17p4ghg0().b[1][0]++;
            cov_t17p4ghg0().s[7]++;
            console.error('Upload error:', error);
            cov_t17p4ghg0().s[8]++;
            return {
              url: '',
              path: '',
              error: error.message
            };
          } else {
            cov_t17p4ghg0().b[1][1]++;
          }
          var _ref2 = (cov_t17p4ghg0().s[9]++, supabase.storage.from(this.bucketName).getPublicUrl(data.path)),
            urlData = _ref2.data;
          cov_t17p4ghg0().s[10]++;
          return {
            url: urlData.publicUrl,
            path: data.path
          };
        } catch (error) {
          cov_t17p4ghg0().s[11]++;
          console.error('Storage service error:', error);
          cov_t17p4ghg0().s[12]++;
          return {
            url: '',
            path: '',
            error: error instanceof Error ? (cov_t17p4ghg0().b[2][0]++, error.message) : (cov_t17p4ghg0().b[2][1]++, 'Unknown error occurred')
          };
        }
      });
      function uploadVideo(_x, _x2, _x3, _x4) {
        return _uploadVideo.apply(this, arguments);
      }
      return uploadVideo;
    }())
  }, {
    key: "deleteVideo",
    value: (function () {
      var _deleteVideo = _asyncToGenerator(function* (filePath) {
        cov_t17p4ghg0().f[1]++;
        cov_t17p4ghg0().s[13]++;
        try {
          var _ref3 = (cov_t17p4ghg0().s[14]++, yield supabase.storage.from(this.bucketName).remove([filePath])),
            error = _ref3.error;
          cov_t17p4ghg0().s[15]++;
          if (error) {
            cov_t17p4ghg0().b[3][0]++;
            cov_t17p4ghg0().s[16]++;
            console.error('Delete error:', error);
            cov_t17p4ghg0().s[17]++;
            return {
              success: false,
              error: error.message
            };
          } else {
            cov_t17p4ghg0().b[3][1]++;
          }
          cov_t17p4ghg0().s[18]++;
          return {
            success: true
          };
        } catch (error) {
          cov_t17p4ghg0().s[19]++;
          console.error('Storage service delete error:', error);
          cov_t17p4ghg0().s[20]++;
          return {
            success: false,
            error: error instanceof Error ? (cov_t17p4ghg0().b[4][0]++, error.message) : (cov_t17p4ghg0().b[4][1]++, 'Unknown error occurred')
          };
        }
      });
      function deleteVideo(_x5) {
        return _deleteVideo.apply(this, arguments);
      }
      return deleteVideo;
    }())
  }, {
    key: "getSignedUrl",
    value: (function () {
      var _getSignedUrl = _asyncToGenerator(function* (filePath) {
        var expiresIn = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_t17p4ghg0().b[5][0]++, 3600);
        cov_t17p4ghg0().f[2]++;
        cov_t17p4ghg0().s[21]++;
        try {
          var _ref4 = (cov_t17p4ghg0().s[22]++, yield supabase.storage.from(this.bucketName).createSignedUrl(filePath, expiresIn)),
            data = _ref4.data,
            error = _ref4.error;
          cov_t17p4ghg0().s[23]++;
          if (error) {
            cov_t17p4ghg0().b[6][0]++;
            cov_t17p4ghg0().s[24]++;
            console.error('Signed URL error:', error);
            cov_t17p4ghg0().s[25]++;
            return {
              url: '',
              error: error.message
            };
          } else {
            cov_t17p4ghg0().b[6][1]++;
          }
          cov_t17p4ghg0().s[26]++;
          return {
            url: data.signedUrl
          };
        } catch (error) {
          cov_t17p4ghg0().s[27]++;
          console.error('Storage service signed URL error:', error);
          cov_t17p4ghg0().s[28]++;
          return {
            url: '',
            error: error instanceof Error ? (cov_t17p4ghg0().b[7][0]++, error.message) : (cov_t17p4ghg0().b[7][1]++, 'Unknown error occurred')
          };
        }
      });
      function getSignedUrl(_x6) {
        return _getSignedUrl.apply(this, arguments);
      }
      return getSignedUrl;
    }())
  }, {
    key: "listUserVideos",
    value: (function () {
      var _listUserVideos = _asyncToGenerator(function* (userId) {
        cov_t17p4ghg0().f[3]++;
        cov_t17p4ghg0().s[29]++;
        try {
          var _ref5 = (cov_t17p4ghg0().s[30]++, yield supabase.storage.from(this.bucketName).list(userId, {
              limit: 100,
              offset: 0,
              sortBy: {
                column: 'created_at',
                order: 'desc'
              }
            })),
            data = _ref5.data,
            error = _ref5.error;
          cov_t17p4ghg0().s[31]++;
          if (error) {
            cov_t17p4ghg0().b[8][0]++;
            cov_t17p4ghg0().s[32]++;
            console.error('List files error:', error);
            cov_t17p4ghg0().s[33]++;
            return {
              files: [],
              error: error.message
            };
          } else {
            cov_t17p4ghg0().b[8][1]++;
          }
          cov_t17p4ghg0().s[34]++;
          return {
            files: (cov_t17p4ghg0().b[9][0]++, data) || (cov_t17p4ghg0().b[9][1]++, [])
          };
        } catch (error) {
          cov_t17p4ghg0().s[35]++;
          console.error('Storage service list error:', error);
          cov_t17p4ghg0().s[36]++;
          return {
            files: [],
            error: error instanceof Error ? (cov_t17p4ghg0().b[10][0]++, error.message) : (cov_t17p4ghg0().b[10][1]++, 'Unknown error occurred')
          };
        }
      });
      function listUserVideos(_x7) {
        return _listUserVideos.apply(this, arguments);
      }
      return listUserVideos;
    }())
  }, {
    key: "getFileInfo",
    value: (function () {
      var _getFileInfo = _asyncToGenerator(function* (filePath) {
        cov_t17p4ghg0().f[4]++;
        cov_t17p4ghg0().s[37]++;
        try {
          var _ref6 = (cov_t17p4ghg0().s[38]++, yield supabase.storage.from(this.bucketName).list('', {
              search: filePath
            })),
            data = _ref6.data,
            error = _ref6.error;
          cov_t17p4ghg0().s[39]++;
          if (error) {
            cov_t17p4ghg0().b[11][0]++;
            cov_t17p4ghg0().s[40]++;
            console.error('File info error:', error);
            cov_t17p4ghg0().s[41]++;
            return {
              info: null,
              error: error.message
            };
          } else {
            cov_t17p4ghg0().b[11][1]++;
          }
          var fileInfo = (cov_t17p4ghg0().s[42]++, (cov_t17p4ghg0().b[13][0]++, data) && (cov_t17p4ghg0().b[13][1]++, data.length > 0) ? (cov_t17p4ghg0().b[12][0]++, data[0]) : (cov_t17p4ghg0().b[12][1]++, null));
          cov_t17p4ghg0().s[43]++;
          return {
            info: fileInfo
          };
        } catch (error) {
          cov_t17p4ghg0().s[44]++;
          console.error('Storage service file info error:', error);
          cov_t17p4ghg0().s[45]++;
          return {
            info: null,
            error: error instanceof Error ? (cov_t17p4ghg0().b[14][0]++, error.message) : (cov_t17p4ghg0().b[14][1]++, 'Unknown error occurred')
          };
        }
      });
      function getFileInfo(_x8) {
        return _getFileInfo.apply(this, arguments);
      }
      return getFileInfo;
    }())
  }, {
    key: "createBucket",
    value: (function () {
      var _createBucket = _asyncToGenerator(function* () {
        cov_t17p4ghg0().f[5]++;
        cov_t17p4ghg0().s[46]++;
        try {
          var _ref7 = (cov_t17p4ghg0().s[47]++, yield supabase.storage.createBucket(this.bucketName, {
              public: false,
              allowedMimeTypes: ['video/mp4', 'video/quicktime', 'video/x-msvideo'],
              fileSizeLimit: 100 * 1024 * 1024
            })),
            data = _ref7.data,
            error = _ref7.error;
          cov_t17p4ghg0().s[48]++;
          if ((cov_t17p4ghg0().b[16][0]++, error) && (cov_t17p4ghg0().b[16][1]++, error.message !== 'Bucket already exists')) {
            cov_t17p4ghg0().b[15][0]++;
            cov_t17p4ghg0().s[49]++;
            console.error('Create bucket error:', error);
            cov_t17p4ghg0().s[50]++;
            return {
              success: false,
              error: error.message
            };
          } else {
            cov_t17p4ghg0().b[15][1]++;
          }
          cov_t17p4ghg0().s[51]++;
          return {
            success: true
          };
        } catch (error) {
          cov_t17p4ghg0().s[52]++;
          console.error('Storage service create bucket error:', error);
          cov_t17p4ghg0().s[53]++;
          return {
            success: false,
            error: error instanceof Error ? (cov_t17p4ghg0().b[17][0]++, error.message) : (cov_t17p4ghg0().b[17][1]++, 'Unknown error occurred')
          };
        }
      });
      function createBucket() {
        return _createBucket.apply(this, arguments);
      }
      return createBucket;
    }())
  }]);
}();
export var storageService = (cov_t17p4ghg0().s[54]++, new StorageService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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