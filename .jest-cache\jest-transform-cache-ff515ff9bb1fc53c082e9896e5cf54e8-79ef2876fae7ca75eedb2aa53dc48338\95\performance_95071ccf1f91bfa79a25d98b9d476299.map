{"version": 3, "names": ["React", "InteractionManager", "Platform", "Dimensions", "AsyncStorage", "PerformanceMonitor", "_classCallCheck", "metrics", "cov_1gdttvm7jx", "s", "Map", "observers", "_createClass", "key", "value", "start", "name", "metadata", "f", "startTime", "Date", "now", "set", "end", "metric", "get", "b", "console", "warn", "endTime", "duration", "completedMetric", "Object", "assign", "notifyObservers", "getMetric", "getAllMetrics", "Array", "from", "values", "clear", "addObserver", "observer", "push", "removeObserver", "index", "indexOf", "splice", "for<PERSON>ach", "error", "trackComponentLoad", "componentName", "loadTime", "log", "trackComponentLoadError", "_measureNetworkLatency", "_asyncToGenerator", "url", "arguments", "length", "undefined", "fetch", "method", "mode", "cache", "measureNetworkLatency", "apply", "measureFrameRate", "Promise", "resolve", "frames", "<PERSON><PERSON><PERSON><PERSON>", "elapsed", "requestAnimationFrame", "fps", "getMemoryUsage", "OS", "performance", "memory", "usedJSHeapSize", "getOptimizationSuggestions", "suggestions", "slowRenders", "filter", "m", "includes", "type", "severity", "message", "action", "memoryUsage", "toFixed", "trackDatabaseQuery", "queryName", "trackDatabaseError", "getCacheHitRate", "performanceMonitor", "measurePerformance", "target", "propertyKey", "descriptor", "originalMethod", "metricName", "constructor", "_len", "args", "_key", "className", "methodName", "result", "debounce", "func", "wait", "immediate", "timeout", "executedFunction", "_len2", "_key2", "later", "callNow", "clearTimeout", "setTimeout", "throttle", "limit", "inThrottle", "_len3", "_key3", "runAfterInteractions", "BatchProcessor", "processor", "batchSize", "delay", "batch", "add", "item", "flush", "scheduleFlush", "items", "_toConsumableArray", "_this", "MemoryMonitor", "startMonitoring", "_this2", "interval", "setInterval", "usage", "totalJSHeapSize", "jsHeapSizeLimit", "timestamp", "getInstance", "instance", "imageOptimization", "getOptimizedUrl", "options", "startsWith", "_ref2", "width", "height", "_ref2$quality", "quality", "_ref2$format", "format", "params", "URLSearchParams", "append", "toString", "separator", "getOptimalDimensions", "originalWidth", "originalHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "aspectRatio", "Math", "round", "NetworkPerformanceMonitor", "requests", "completedRequests", "maxStoredRequests", "startRequest", "id", "toISOString", "endRequest", "status", "size", "request", "completedRequest", "unshift", "pop", "delete", "getNetworkStats", "total", "averageResponseTime", "totalRequests", "failedRequests", "slowRequests", "totalTime", "reduce", "sum", "req", "failed", "slow", "ComponentPerformanceTracker", "renderTimes", "mountTimes", "trackMount", "trackRender", "renderTime", "times", "shift", "getComponentStats", "averageRenderTime", "maxRenderTime", "renderCount", "time", "max", "getAllStats", "stats", "_ref3", "_ref4", "_slicedToArray", "bundleAnalyzer", "logBundleInfo", "__DEV__", "platform", "version", "Version", "trackComponentRender", "count", "parseInt", "global", "AdvancedCache", "expirationTimes", "accessCounts", "maxSize", "ttl", "cleanup", "expirationTime", "accessCount", "_this3", "_ref5", "_ref6", "sortedByAccess", "entries", "sort", "_ref7", "_ref8", "_ref9", "a", "_ref0", "toRemove", "slice", "floor", "_ref1", "_ref10", "getStats", "totalAccess", "mostAccessed", "_ref11", "_ref12", "_ref13", "_ref14", "map", "_ref15", "_ref16", "hitRate", "performanceOptimizations", "lazyLoadComponent", "importFunc", "tracker", "lazy", "component", "optimizeImageLoading", "imageUrl", "_ref18", "pixelRatio", "window", "devicePixelRatio", "uri", "priority", "placeholder", "lowQualityPlaceholder", "batchStateUpdates", "updates", "requestIdleCallback", "update", "getVirtualizedProps", "itemCount", "itemHeight", "_ref19", "visibleItems", "ceil", "getItemLayout", "_", "offset", "initialNumToRender", "min", "maxToRenderPerBatch", "windowSize", "removeClippedSubviews", "GlobalPerformanceManager", "isMonitoring", "performanceData", "_this4", "generateReport", "stopMonitoring", "_generateReport", "networkStats", "componentStats", "cacheStats", "report", "network", "components", "performanceEntries", "setItem", "JSON", "stringify", "_getStoredReports", "stored", "getItem", "parse", "getStoredReports", "networkMonitor", "componentTracker", "advancedCache", "globalPerformanceManager"], "sources": ["performance.ts"], "sourcesContent": ["import React from 'react';\nimport { InteractionManager, Platform, Dimensions } from 'react-native';\nimport AsyncStorage from '@react-native-async-storage/async-storage';\n\n/**\n * Performance monitoring and optimization utilities\n */\n\ninterface PerformanceMetrics {\n  startTime: number;\n  endTime?: number;\n  duration?: number;\n  name: string;\n  metadata?: Record<string, any>;\n}\n\nclass PerformanceMonitor {\n  private metrics: Map<string, PerformanceMetrics> = new Map();\n  private observers: ((metric: PerformanceMetrics) => void)[] = [];\n\n  /**\n   * Start measuring performance for a specific operation\n   */\n  start(name: string, metadata?: Record<string, any>): void {\n    const startTime = Date.now();\n    this.metrics.set(name, {\n      name,\n      startTime,\n      metadata,\n    });\n  }\n\n  /**\n   * End measuring performance for a specific operation\n   */\n  end(name: string): PerformanceMetrics | null {\n    const metric = this.metrics.get(name);\n    if (!metric) {\n      console.warn(`Performance metric \"${name}\" not found`);\n      return null;\n    }\n\n    const endTime = Date.now();\n    const duration = endTime - metric.startTime;\n    \n    const completedMetric: PerformanceMetrics = {\n      ...metric,\n      endTime,\n      duration,\n    };\n\n    this.metrics.set(name, completedMetric);\n    this.notifyObservers(completedMetric);\n\n    return completedMetric;\n  }\n\n  /**\n   * Get performance metric by name\n   */\n  getMetric(name: string): PerformanceMetrics | undefined {\n    return this.metrics.get(name);\n  }\n\n  /**\n   * Get all performance metrics\n   */\n  getAllMetrics(): PerformanceMetrics[] {\n    return Array.from(this.metrics.values());\n  }\n\n  /**\n   * Clear all metrics\n   */\n  clear(): void {\n    this.metrics.clear();\n  }\n\n  /**\n   * Add observer for performance metrics\n   */\n  addObserver(observer: (metric: PerformanceMetrics) => void): void {\n    this.observers.push(observer);\n  }\n\n  /**\n   * Remove observer\n   */\n  removeObserver(observer: (metric: PerformanceMetrics) => void): void {\n    const index = this.observers.indexOf(observer);\n    if (index > -1) {\n      this.observers.splice(index, 1);\n    }\n  }\n\n  private notifyObservers(metric: PerformanceMetrics): void {\n    this.observers.forEach(observer => {\n      try {\n        observer(metric);\n      } catch (error) {\n        console.error('Error in performance observer:', error);\n      }\n    });\n  }\n\n  /**\n   * Track component load performance (for lazy loading)\n   */\n  trackComponentLoad(componentName: string, loadTime: number): void {\n    console.log(`Component ${componentName} loaded in ${loadTime}ms`);\n\n    // Track in performance metrics\n    if (loadTime > 2000) {\n      console.warn(`Slow component load: ${componentName} took ${loadTime}ms`);\n    }\n  }\n\n  /**\n   * Track component load errors\n   */\n  trackComponentLoadError(componentName: string, error: Error): void {\n    console.error(`Component ${componentName} failed to load:`, error);\n  }\n\n  /**\n   * Measure network latency\n   */\n  async measureNetworkLatency(url: string = 'https://www.google.com'): Promise<number> {\n    const start = Date.now();\n\n    try {\n      await fetch(url, {\n        method: 'HEAD',\n        mode: 'no-cors',\n        cache: 'no-cache',\n      });\n      return Date.now() - start;\n    } catch (error) {\n      return -1; // Network error\n    }\n  }\n\n  /**\n   * Estimate frame rate\n   */\n  measureFrameRate(duration: number = 1000): Promise<number> {\n    return new Promise((resolve) => {\n      let frames = 0;\n      const start = Date.now();\n\n      const countFrame = () => {\n        frames++;\n        const elapsed = Date.now() - start;\n\n        if (elapsed < duration) {\n          requestAnimationFrame(countFrame);\n        } else {\n          const fps = (frames * 1000) / elapsed;\n          resolve(fps);\n        }\n      };\n\n      requestAnimationFrame(countFrame);\n    });\n  }\n\n  /**\n   * Get memory usage (web only)\n   */\n  getMemoryUsage(): number {\n    if (Platform.OS === 'web' && 'memory' in performance) {\n      return (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB\n    }\n    return 0;\n  }\n\n  /**\n   * Get optimization suggestions\n   */\n  getOptimizationSuggestions(): Array<{\n    type: 'memory' | 'render' | 'network' | 'bundle';\n    severity: 'low' | 'medium' | 'high' | 'critical';\n    message: string;\n    action: string;\n  }> {\n    const suggestions = [];\n    const metrics = this.getAllMetrics();\n\n    // Check for slow renders\n    const slowRenders = metrics.filter(m =>\n      m.name.includes('render') && m.duration && m.duration > 16\n    );\n\n    if (slowRenders.length > 0) {\n      suggestions.push({\n        type: 'render' as const,\n        severity: 'medium' as const,\n        message: `${slowRenders.length} slow renders detected`,\n        action: 'Use React.memo, optimize re-renders, implement virtualization',\n      });\n    }\n\n    // Check memory usage\n    const memoryUsage = this.getMemoryUsage();\n    if (memoryUsage > 100) {\n      suggestions.push({\n        type: 'memory' as const,\n        severity: memoryUsage > 200 ? 'high' : 'medium',\n        message: `High memory usage: ${memoryUsage.toFixed(1)}MB`,\n        action: 'Implement lazy loading, optimize images, clear unused data',\n      });\n    }\n\n    return suggestions;\n  }\n\n  /**\n   * Track database query performance\n   */\n  trackDatabaseQuery(queryName: string, duration: number): void {\n    console.log(`Database query ${queryName} completed in ${duration}ms`);\n\n    if (duration > 2000) {\n      console.warn(`Slow database query: ${queryName} took ${duration}ms`);\n    }\n  }\n\n  /**\n   * Track database errors\n   */\n  trackDatabaseError(queryName: string, error: Error): void {\n    console.error(`Database query ${queryName} failed:`, error);\n  }\n\n  /**\n   * Get cache hit rate (placeholder for future implementation)\n   */\n  getCacheHitRate(): number {\n    // This would be implemented with actual cache statistics\n    return 0.75; // 75% hit rate as example\n  }\n}\n\nexport const performanceMonitor = new PerformanceMonitor();\n\n/**\n * Decorator for measuring function performance\n */\nexport function measurePerformance(name?: string) {\n  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {\n    const originalMethod = descriptor.value;\n    const metricName = name || `${target.constructor.name}.${propertyKey}`;\n\n    descriptor.value = async function (...args: any[]) {\n      performanceMonitor.start(metricName, {\n        className: target.constructor.name,\n        methodName: propertyKey,\n        args: args.length,\n      });\n\n      try {\n        const result = await originalMethod.apply(this, args);\n        performanceMonitor.end(metricName);\n        return result;\n      } catch (error) {\n        performanceMonitor.end(metricName);\n        throw error;\n      }\n    };\n\n    return descriptor;\n  };\n}\n\n/**\n * Debounce function to limit function calls\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number,\n  immediate = false\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null;\n\n  return function executedFunction(...args: Parameters<T>) {\n    const later = () => {\n      timeout = null;\n      if (!immediate) func(...args);\n    };\n\n    const callNow = immediate && !timeout;\n    \n    if (timeout) clearTimeout(timeout);\n    timeout = setTimeout(later, wait) as any;\n    \n    if (callNow) func(...args);\n  };\n}\n\n/**\n * Throttle function to limit function calls\n */\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  \n  return function executedFunction(...args: Parameters<T>) {\n    if (!inThrottle) {\n      func.apply(null, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n/**\n * Run function after interactions are complete\n */\nexport function runAfterInteractions<T>(func: () => T): Promise<T> {\n  return new Promise((resolve) => {\n    InteractionManager.runAfterInteractions(() => {\n      resolve(func());\n    });\n  });\n}\n\n/**\n * Batch multiple operations together\n */\nexport class BatchProcessor<T> {\n  private batch: T[] = [];\n  private timeout: NodeJS.Timeout | null = null;\n  private processor: (items: T[]) => void;\n  private batchSize: number;\n  private delay: number;\n\n  constructor(\n    processor: (items: T[]) => void,\n    batchSize = 10,\n    delay = 100\n  ) {\n    this.processor = processor;\n    this.batchSize = batchSize;\n    this.delay = delay;\n  }\n\n  add(item: T): void {\n    this.batch.push(item);\n\n    if (this.batch.length >= this.batchSize) {\n      this.flush();\n    } else {\n      this.scheduleFlush();\n    }\n  }\n\n  flush(): void {\n    if (this.timeout) {\n      clearTimeout(this.timeout);\n      this.timeout = null;\n    }\n\n    if (this.batch.length > 0) {\n      const items = [...this.batch];\n      this.batch = [];\n      this.processor(items);\n    }\n  }\n\n  private scheduleFlush(): void {\n    if (this.timeout) return;\n\n    this.timeout = setTimeout(() => {\n      this.flush();\n    }, this.delay) as any;\n  }\n}\n\n/**\n * Memory usage monitoring\n */\nexport class MemoryMonitor {\n  private static instance: MemoryMonitor;\n  private observers: ((usage: any) => void)[] = [];\n\n  static getInstance(): MemoryMonitor {\n    if (!MemoryMonitor.instance) {\n      MemoryMonitor.instance = new MemoryMonitor();\n    }\n    return MemoryMonitor.instance;\n  }\n\n  startMonitoring(interval = 5000): void {\n    if (Platform.OS === 'web' && 'memory' in performance) {\n      setInterval(() => {\n        const memory = (performance as any).memory;\n        const usage = {\n          usedJSHeapSize: memory.usedJSHeapSize,\n          totalJSHeapSize: memory.totalJSHeapSize,\n          jsHeapSizeLimit: memory.jsHeapSizeLimit,\n          timestamp: Date.now(),\n        };\n        this.notifyObservers(usage);\n      }, interval);\n    }\n  }\n\n  addObserver(observer: (usage: any) => void): void {\n    this.observers.push(observer);\n  }\n\n  removeObserver(observer: (usage: any) => void): void {\n    const index = this.observers.indexOf(observer);\n    if (index > -1) {\n      this.observers.splice(index, 1);\n    }\n  }\n\n  private notifyObservers(usage: any): void {\n    this.observers.forEach(observer => {\n      try {\n        observer(usage);\n      } catch (error) {\n        console.error('Error in memory observer:', error);\n      }\n    });\n  }\n}\n\n/**\n * Image optimization utilities\n */\nexport const imageOptimization = {\n  /**\n   * Get optimized image URL with parameters\n   */\n  getOptimizedUrl(\n    url: string,\n    options: {\n      width?: number;\n      height?: number;\n      quality?: number;\n      format?: 'webp' | 'jpeg' | 'png';\n    } = {}\n  ): string {\n    if (!url.startsWith('http')) return url;\n\n    const { width, height, quality = 80, format = 'webp' } = options;\n    const params = new URLSearchParams();\n\n    if (width) params.append('w', width.toString());\n    if (height) params.append('h', height.toString());\n    params.append('q', quality.toString());\n    params.append('f', format);\n    params.append('auto', 'format');\n\n    const separator = url.includes('?') ? '&' : '?';\n    return `${url}${separator}${params.toString()}`;\n  },\n\n  /**\n   * Calculate optimal image dimensions for device\n   */\n  getOptimalDimensions(\n    originalWidth: number,\n    originalHeight: number,\n    maxWidth: number,\n    maxHeight: number\n  ): { width: number; height: number } {\n    const aspectRatio = originalWidth / originalHeight;\n\n    let width = originalWidth;\n    let height = originalHeight;\n\n    if (width > maxWidth) {\n      width = maxWidth;\n      height = width / aspectRatio;\n    }\n\n    if (height > maxHeight) {\n      height = maxHeight;\n      width = height * aspectRatio;\n    }\n\n    return {\n      width: Math.round(width),\n      height: Math.round(height),\n    };\n  },\n};\n\n/**\n * Advanced Network Performance Monitor\n */\nexport class NetworkPerformanceMonitor {\n  private static instance: NetworkPerformanceMonitor;\n  private requests: Map<string, any> = new Map();\n  private completedRequests: any[] = [];\n  private maxStoredRequests = 100;\n\n  static getInstance(): NetworkPerformanceMonitor {\n    if (!NetworkPerformanceMonitor.instance) {\n      NetworkPerformanceMonitor.instance = new NetworkPerformanceMonitor();\n    }\n    return NetworkPerformanceMonitor.instance;\n  }\n\n  startRequest(id: string, url: string, method: string): void {\n    this.requests.set(id, {\n      id,\n      url,\n      method,\n      startTime: Date.now(),\n      timestamp: new Date().toISOString(),\n    });\n  }\n\n  endRequest(id: string, status: number, size: number): void {\n    const request = this.requests.get(id);\n    if (!request) return;\n\n    const endTime = Date.now();\n    const duration = endTime - request.startTime;\n\n    const completedRequest = {\n      ...request,\n      endTime,\n      duration,\n      status,\n      size,\n    };\n\n    this.completedRequests.unshift(completedRequest);\n    if (this.completedRequests.length > this.maxStoredRequests) {\n      this.completedRequests.pop();\n    }\n\n    this.requests.delete(id);\n\n    // Log slow requests\n    if (duration > 3000) {\n      console.warn(`Slow network request: ${request.url} took ${duration}ms`);\n    }\n  }\n\n  getNetworkStats(): {\n    averageResponseTime: number;\n    totalRequests: number;\n    failedRequests: number;\n    slowRequests: number;\n  } {\n    const total = this.completedRequests.length;\n    if (total === 0) {\n      return { averageResponseTime: 0, totalRequests: 0, failedRequests: 0, slowRequests: 0 };\n    }\n\n    const totalTime = this.completedRequests.reduce((sum, req) => sum + req.duration, 0);\n    const failed = this.completedRequests.filter(req => req.status >= 400).length;\n    const slow = this.completedRequests.filter(req => req.duration > 3000).length;\n\n    return {\n      averageResponseTime: Math.round(totalTime / total),\n      totalRequests: total,\n      failedRequests: failed,\n      slowRequests: slow,\n    };\n  }\n}\n\n/**\n * React Component Performance Tracker\n */\nexport class ComponentPerformanceTracker {\n  private static instance: ComponentPerformanceTracker;\n  private renderTimes: Map<string, number[]> = new Map();\n  private mountTimes: Map<string, number> = new Map();\n\n  static getInstance(): ComponentPerformanceTracker {\n    if (!ComponentPerformanceTracker.instance) {\n      ComponentPerformanceTracker.instance = new ComponentPerformanceTracker();\n    }\n    return ComponentPerformanceTracker.instance;\n  }\n\n  trackMount(componentName: string): void {\n    this.mountTimes.set(componentName, Date.now());\n  }\n\n  trackRender(componentName: string, renderTime: number): void {\n    const times = this.renderTimes.get(componentName) || [];\n    times.push(renderTime);\n\n    // Keep only last 50 render times\n    if (times.length > 50) {\n      times.shift();\n    }\n\n    this.renderTimes.set(componentName, times);\n\n    // Warn about slow renders\n    if (renderTime > 16) { // 60fps = 16.67ms per frame\n      console.warn(`Slow render: ${componentName} took ${renderTime}ms`);\n    }\n  }\n\n  getComponentStats(componentName: string): {\n    averageRenderTime: number;\n    maxRenderTime: number;\n    renderCount: number;\n  } {\n    const times = this.renderTimes.get(componentName) || [];\n    if (times.length === 0) {\n      return { averageRenderTime: 0, maxRenderTime: 0, renderCount: 0 };\n    }\n\n    const total = times.reduce((sum, time) => sum + time, 0);\n    const max = Math.max(...times);\n\n    return {\n      averageRenderTime: Math.round(total / times.length * 100) / 100,\n      maxRenderTime: max,\n      renderCount: times.length,\n    };\n  }\n\n  getAllStats(): Record<string, any> {\n    const stats: Record<string, any> = {};\n    for (const [componentName] of this.renderTimes) {\n      stats[componentName] = this.getComponentStats(componentName);\n    }\n    return stats;\n  }\n}\n\n/**\n * Bundle size analyzer\n */\nexport const bundleAnalyzer = {\n  /**\n   * Log bundle information\n   */\n  logBundleInfo(): void {\n    if (__DEV__) {\n      console.log('Bundle Analysis:', {\n        platform: Platform.OS,\n        version: Platform.Version,\n        timestamp: new Date().toISOString(),\n      });\n    }\n  },\n\n  /**\n   * Track component render count\n   */\n  trackComponentRender(componentName: string): void {\n    if (__DEV__) {\n      const key = `render_count_${componentName}`;\n      const count = parseInt((global as any)[key] || '0', 10) + 1;\n      (global as any)[key] = count.toString();\n\n      if (count % 10 === 0) {\n        console.log(`Component ${componentName} rendered ${count} times`);\n      }\n    }\n  },\n};\n\n/**\n * Advanced Caching System\n */\nexport class AdvancedCache {\n  private static instance: AdvancedCache;\n  private cache: Map<string, any> = new Map();\n  private expirationTimes: Map<string, number> = new Map();\n  private accessCounts: Map<string, number> = new Map();\n  private maxSize = 100;\n\n  static getInstance(): AdvancedCache {\n    if (!AdvancedCache.instance) {\n      AdvancedCache.instance = new AdvancedCache();\n    }\n    return AdvancedCache.instance;\n  }\n\n  set(key: string, value: any, ttl = 300000): void { // 5 minutes default TTL\n    // Remove expired items if cache is full\n    if (this.cache.size >= this.maxSize) {\n      this.cleanup();\n    }\n\n    this.cache.set(key, value);\n    this.expirationTimes.set(key, Date.now() + ttl);\n    this.accessCounts.set(key, 0);\n  }\n\n  get(key: string): any {\n    const expirationTime = this.expirationTimes.get(key);\n    if (expirationTime && Date.now() > expirationTime) {\n      this.delete(key);\n      return null;\n    }\n\n    const accessCount = this.accessCounts.get(key) || 0;\n    this.accessCounts.set(key, accessCount + 1);\n\n    return this.cache.get(key);\n  }\n\n  delete(key: string): void {\n    this.cache.delete(key);\n    this.expirationTimes.delete(key);\n    this.accessCounts.delete(key);\n  }\n\n  clear(): void {\n    this.cache.clear();\n    this.expirationTimes.clear();\n    this.accessCounts.clear();\n  }\n\n  private cleanup(): void {\n    const now = Date.now();\n\n    // Remove expired items\n    for (const [key, expirationTime] of this.expirationTimes) {\n      if (now > expirationTime) {\n        this.delete(key);\n      }\n    }\n\n    // If still full, remove least accessed items\n    if (this.cache.size >= this.maxSize) {\n      const sortedByAccess = Array.from(this.accessCounts.entries())\n        .sort(([, a], [, b]) => a - b);\n\n      const toRemove = sortedByAccess.slice(0, Math.floor(this.maxSize * 0.2));\n      toRemove.forEach(([key]) => this.delete(key));\n    }\n  }\n\n  getStats(): {\n    size: number;\n    hitRate: number;\n    mostAccessed: string[];\n  } {\n    const totalAccess = Array.from(this.accessCounts.values()).reduce((sum, count) => sum + count, 0);\n    const mostAccessed = Array.from(this.accessCounts.entries())\n      .sort(([, a], [, b]) => b - a)\n      .slice(0, 5)\n      .map(([key]) => key);\n\n    return {\n      size: this.cache.size,\n      hitRate: totalAccess > 0 ? (this.cache.size / totalAccess) * 100 : 0,\n      mostAccessed,\n    };\n  }\n}\n\n/**\n * Performance Optimization Utilities\n */\nexport const performanceOptimizations = {\n  /**\n   * Lazy load component with performance tracking\n   */\n  lazyLoadComponent: <T extends React.ComponentType<any>>(\n    importFunc: () => Promise<{ default: T }>,\n    componentName: string\n  ) => {\n    const tracker = ComponentPerformanceTracker.getInstance();\n\n    return React.lazy(async () => {\n      const startTime = Date.now();\n      const component = await importFunc();\n      const loadTime = Date.now() - startTime;\n\n      tracker.trackMount(componentName);\n\n      if (loadTime > 1000) {\n        console.warn(`Slow component load: ${componentName} took ${loadTime}ms`);\n      }\n\n      return component;\n    });\n  },\n\n  /**\n   * Optimize image loading with progressive enhancement\n   */\n  optimizeImageLoading: (imageUrl: string, options: {\n    lowQualityPlaceholder?: string;\n    sizes?: string;\n    priority?: boolean;\n  } = {}) => {\n    const { width } = Dimensions.get('window');\n    const pixelRatio = Platform.OS === 'web' ? window.devicePixelRatio : 2;\n\n    return {\n      uri: imageOptimization.getOptimizedUrl(imageUrl, {\n        width: Math.round(width * pixelRatio),\n        quality: options.priority ? 90 : 75,\n        format: 'webp',\n      }),\n      placeholder: options.lowQualityPlaceholder,\n      priority: options.priority || false,\n    };\n  },\n\n  /**\n   * Batch state updates for better performance\n   */\n  batchStateUpdates: <T>(\n    updates: Array<() => void>,\n    delay = 0\n  ): Promise<void> => {\n    return new Promise((resolve) => {\n      if (Platform.OS === 'web' && 'requestIdleCallback' in window) {\n        (window as any).requestIdleCallback(() => {\n          updates.forEach(update => update());\n          resolve();\n        });\n      } else {\n        setTimeout(() => {\n          updates.forEach(update => update());\n          resolve();\n        }, delay);\n      }\n    });\n  },\n\n  /**\n   * Virtualize large lists for better performance\n   */\n  getVirtualizedProps: (itemCount: number, itemHeight: number) => {\n    const { height } = Dimensions.get('window');\n    const visibleItems = Math.ceil(height / itemHeight) + 2; // Buffer\n\n    return {\n      getItemLayout: (_: any, index: number) => ({\n        length: itemHeight,\n        offset: itemHeight * index,\n        index,\n      }),\n      initialNumToRender: Math.min(visibleItems, itemCount),\n      maxToRenderPerBatch: 5,\n      windowSize: 10,\n      removeClippedSubviews: true,\n    };\n  },\n};\n\n/**\n * Global Performance Manager\n */\nexport class GlobalPerformanceManager {\n  private static instance: GlobalPerformanceManager;\n  private isMonitoring = false;\n  private performanceData: any[] = [];\n\n  static getInstance(): GlobalPerformanceManager {\n    if (!GlobalPerformanceManager.instance) {\n      GlobalPerformanceManager.instance = new GlobalPerformanceManager();\n    }\n    return GlobalPerformanceManager.instance;\n  }\n\n  startMonitoring(): void {\n    if (this.isMonitoring) return;\n\n    this.isMonitoring = true;\n\n    // Start memory monitoring\n    MemoryMonitor.getInstance().startMonitoring();\n\n    // Monitor performance metrics\n    performanceMonitor.addObserver((metric) => {\n      this.performanceData.push({\n        type: 'performance',\n        ...metric,\n        timestamp: Date.now(),\n      });\n    });\n\n    // Periodic cleanup and reporting\n    setInterval(() => {\n      this.cleanup();\n      this.generateReport();\n    }, 60000); // Every minute\n  }\n\n  stopMonitoring(): void {\n    this.isMonitoring = false;\n  }\n\n  private cleanup(): void {\n    // Keep only last 1000 performance entries\n    if (this.performanceData.length > 1000) {\n      this.performanceData = this.performanceData.slice(-1000);\n    }\n  }\n\n  private async generateReport(): Promise<void> {\n    if (!__DEV__) return;\n\n    const networkStats = NetworkPerformanceMonitor.getInstance().getNetworkStats();\n    const componentStats = ComponentPerformanceTracker.getInstance().getAllStats();\n    const cacheStats = AdvancedCache.getInstance().getStats();\n\n    const report = {\n      timestamp: new Date().toISOString(),\n      network: networkStats,\n      components: componentStats,\n      cache: cacheStats,\n      performanceEntries: this.performanceData.length,\n    };\n\n    // Store report for debugging\n    try {\n      await AsyncStorage.setItem('performance_report', JSON.stringify(report));\n    } catch (error) {\n      console.warn('Failed to store performance report:', error);\n    }\n\n    console.log('Performance Report:', report);\n  }\n\n  async getStoredReports(): Promise<any[]> {\n    try {\n      const stored = await AsyncStorage.getItem('performance_report');\n      return stored ? [JSON.parse(stored)] : [];\n    } catch (error) {\n      console.warn('Failed to retrieve performance reports:', error);\n      return [];\n    }\n  }\n}\n\n// Initialize global performance monitoring in development\nif (__DEV__) {\n  GlobalPerformanceManager.getInstance().startMonitoring();\n}\n\n// Export instances\nexport const networkMonitor = NetworkPerformanceMonitor.getInstance();\nexport const componentTracker = ComponentPerformanceTracker.getInstance();\nexport const advancedCache = AdvancedCache.getInstance();\nexport const globalPerformanceManager = GlobalPerformanceManager.getInstance();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,kBAAkB,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AACvE,OAAOC,YAAY,MAAM,2CAA2C;AAAC,IAc/DC,kBAAkB;EAAA,SAAAA,mBAAA;IAAAC,eAAA,OAAAD,kBAAA;IAAA,KACdE,OAAO,IAAAC,cAAA,GAAAC,CAAA,OAAoC,IAAIC,GAAG,CAAC,CAAC;IAAA,KACpDC,SAAS,IAAAH,cAAA,GAAAC,CAAA,OAA6C,EAAE;EAAA;EAAA,OAAAG,YAAA,CAAAP,kBAAA;IAAAQ,GAAA;IAAAC,KAAA,EAKhE,SAAAC,KAAKA,CAACC,IAAY,EAAEC,QAA8B,EAAQ;MAAAT,cAAA,GAAAU,CAAA;MACxD,IAAMC,SAAS,IAAAX,cAAA,GAAAC,CAAA,OAAGW,IAAI,CAACC,GAAG,CAAC,CAAC;MAACb,cAAA,GAAAC,CAAA;MAC7B,IAAI,CAACF,OAAO,CAACe,GAAG,CAACN,IAAI,EAAE;QACrBA,IAAI,EAAJA,IAAI;QACJG,SAAS,EAATA,SAAS;QACTF,QAAQ,EAARA;MACF,CAAC,CAAC;IACJ;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAKD,SAAAS,GAAGA,CAACP,IAAY,EAA6B;MAAAR,cAAA,GAAAU,CAAA;MAC3C,IAAMM,MAAM,IAAAhB,cAAA,GAAAC,CAAA,OAAG,IAAI,CAACF,OAAO,CAACkB,GAAG,CAACT,IAAI,CAAC;MAACR,cAAA,GAAAC,CAAA;MACtC,IAAI,CAACe,MAAM,EAAE;QAAAhB,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACXkB,OAAO,CAACC,IAAI,CAAC,uBAAuBZ,IAAI,aAAa,CAAC;QAACR,cAAA,GAAAC,CAAA;QACvD,OAAO,IAAI;MACb,CAAC;QAAAD,cAAA,GAAAkB,CAAA;MAAA;MAED,IAAMG,OAAO,IAAArB,cAAA,GAAAC,CAAA,OAAGW,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1B,IAAMS,QAAQ,IAAAtB,cAAA,GAAAC,CAAA,OAAGoB,OAAO,GAAGL,MAAM,CAACL,SAAS;MAE3C,IAAMY,eAAmC,IAAAvB,cAAA,GAAAC,CAAA,QAAAuB,MAAA,CAAAC,MAAA,KACpCT,MAAM;QACTK,OAAO,EAAPA,OAAO;QACPC,QAAQ,EAARA;MAAQ,GACT;MAACtB,cAAA,GAAAC,CAAA;MAEF,IAAI,CAACF,OAAO,CAACe,GAAG,CAACN,IAAI,EAAEe,eAAe,CAAC;MAACvB,cAAA,GAAAC,CAAA;MACxC,IAAI,CAACyB,eAAe,CAACH,eAAe,CAAC;MAACvB,cAAA,GAAAC,CAAA;MAEtC,OAAOsB,eAAe;IACxB;EAAC;IAAAlB,GAAA;IAAAC,KAAA,EAKD,SAAAqB,SAASA,CAACnB,IAAY,EAAkC;MAAAR,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACtD,OAAO,IAAI,CAACF,OAAO,CAACkB,GAAG,CAACT,IAAI,CAAC;IAC/B;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAKD,SAAAsB,aAAaA,CAAA,EAAyB;MAAA5B,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACpC,OAAO4B,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/B,OAAO,CAACgC,MAAM,CAAC,CAAC,CAAC;IAC1C;EAAC;IAAA1B,GAAA;IAAAC,KAAA,EAKD,SAAA0B,KAAKA,CAAA,EAAS;MAAAhC,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACZ,IAAI,CAACF,OAAO,CAACiC,KAAK,CAAC,CAAC;IACtB;EAAC;IAAA3B,GAAA;IAAAC,KAAA,EAKD,SAAA2B,WAAWA,CAACC,QAA8C,EAAQ;MAAAlC,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAChE,IAAI,CAACE,SAAS,CAACgC,IAAI,CAACD,QAAQ,CAAC;IAC/B;EAAC;IAAA7B,GAAA;IAAAC,KAAA,EAKD,SAAA8B,cAAcA,CAACF,QAA8C,EAAQ;MAAAlC,cAAA,GAAAU,CAAA;MACnE,IAAM2B,KAAK,IAAArC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACE,SAAS,CAACmC,OAAO,CAACJ,QAAQ,CAAC;MAAClC,cAAA,GAAAC,CAAA;MAC/C,IAAIoC,KAAK,GAAG,CAAC,CAAC,EAAE;QAAArC,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACd,IAAI,CAACE,SAAS,CAACoC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjC,CAAC;QAAArC,cAAA,GAAAkB,CAAA;MAAA;IACH;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAED,SAAQoB,eAAeA,CAACV,MAA0B,EAAQ;MAAAhB,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACxD,IAAI,CAACE,SAAS,CAACqC,OAAO,CAAC,UAAAN,QAAQ,EAAI;QAAAlC,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QACjC,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACFiC,QAAQ,CAAClB,MAAM,CAAC;QAClB,CAAC,CAAC,OAAOyB,KAAK,EAAE;UAAAzC,cAAA,GAAAC,CAAA;UACdkB,OAAO,CAACsB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;MACF,CAAC,CAAC;IACJ;EAAC;IAAApC,GAAA;IAAAC,KAAA,EAKD,SAAAoC,kBAAkBA,CAACC,aAAqB,EAAEC,QAAgB,EAAQ;MAAA5C,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAChEkB,OAAO,CAAC0B,GAAG,CAAC,aAAaF,aAAa,cAAcC,QAAQ,IAAI,CAAC;MAAC5C,cAAA,GAAAC,CAAA;MAGlE,IAAI2C,QAAQ,GAAG,IAAI,EAAE;QAAA5C,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACnBkB,OAAO,CAACC,IAAI,CAAC,wBAAwBuB,aAAa,SAASC,QAAQ,IAAI,CAAC;MAC1E,CAAC;QAAA5C,cAAA,GAAAkB,CAAA;MAAA;IACH;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAKD,SAAAwC,uBAAuBA,CAACH,aAAqB,EAAEF,KAAY,EAAQ;MAAAzC,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACjEkB,OAAO,CAACsB,KAAK,CAAC,aAAaE,aAAa,kBAAkB,EAAEF,KAAK,CAAC;IACpE;EAAC;IAAApC,GAAA;IAAAC,KAAA;MAAA,IAAAyC,sBAAA,GAAAC,iBAAA,CAKD,aAAqF;QAAA,IAAzDC,GAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAlD,cAAA,GAAAkB,CAAA,UAAG,wBAAwB;QAAAlB,cAAA,GAAAU,CAAA;QAChE,IAAMH,KAAK,IAAAP,cAAA,GAAAC,CAAA,QAAGW,IAAI,CAACC,GAAG,CAAC,CAAC;QAACb,cAAA,GAAAC,CAAA;QAEzB,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACF,MAAMoD,KAAK,CAACJ,GAAG,EAAE;YACfK,MAAM,EAAE,MAAM;YACdC,IAAI,EAAE,SAAS;YACfC,KAAK,EAAE;UACT,CAAC,CAAC;UAACxD,cAAA,GAAAC,CAAA;UACH,OAAOW,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGN,KAAK;QAC3B,CAAC,CAAC,OAAOkC,KAAK,EAAE;UAAAzC,cAAA,GAAAC,CAAA;UACd,OAAO,CAAC,CAAC;QACX;MACF,CAAC;MAAA,SAbKwD,qBAAqBA,CAAA;QAAA,OAAAV,sBAAA,CAAAW,KAAA,OAAAR,SAAA;MAAA;MAAA,OAArBO,qBAAqB;IAAA;EAAA;IAAApD,GAAA;IAAAC,KAAA,EAkB3B,SAAAqD,gBAAgBA,CAAA,EAA2C;MAAA,IAA1CrC,QAAgB,GAAA4B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAlD,cAAA,GAAAkB,CAAA,UAAG,IAAI;MAAAlB,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACtC,OAAO,IAAI2D,OAAO,CAAC,UAACC,OAAO,EAAK;QAAA7D,cAAA,GAAAU,CAAA;QAC9B,IAAIoD,MAAM,IAAA9D,cAAA,GAAAC,CAAA,QAAG,CAAC;QACd,IAAMM,KAAK,IAAAP,cAAA,GAAAC,CAAA,QAAGW,IAAI,CAACC,GAAG,CAAC,CAAC;QAACb,cAAA,GAAAC,CAAA;QAEzB,IAAM8D,WAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;UAAA/D,cAAA,GAAAU,CAAA;UAAAV,cAAA,GAAAC,CAAA;UACvB6D,MAAM,EAAE;UACR,IAAME,OAAO,IAAAhE,cAAA,GAAAC,CAAA,QAAGW,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGN,KAAK;UAACP,cAAA,GAAAC,CAAA;UAEnC,IAAI+D,OAAO,GAAG1C,QAAQ,EAAE;YAAAtB,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAC,CAAA;YACtBgE,qBAAqB,CAACF,WAAU,CAAC;UACnC,CAAC,MAAM;YAAA/D,cAAA,GAAAkB,CAAA;YACL,IAAMgD,GAAG,IAAAlE,cAAA,GAAAC,CAAA,QAAI6D,MAAM,GAAG,IAAI,GAAIE,OAAO;YAAChE,cAAA,GAAAC,CAAA;YACtC4D,OAAO,CAACK,GAAG,CAAC;UACd;QACF,CAAC;QAAClE,cAAA,GAAAC,CAAA;QAEFgE,qBAAqB,CAACF,WAAU,CAAC;MACnC,CAAC,CAAC;IACJ;EAAC;IAAA1D,GAAA;IAAAC,KAAA,EAKD,SAAA6D,cAAcA,CAAA,EAAW;MAAAnE,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACvB,IAAI,CAAAD,cAAA,GAAAkB,CAAA,UAAAxB,QAAQ,CAAC0E,EAAE,KAAK,KAAK,MAAApE,cAAA,GAAAkB,CAAA,UAAI,QAAQ,IAAImD,WAAW,GAAE;QAAArE,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACpD,OAAQoE,WAAW,CAASC,MAAM,CAACC,cAAc,GAAG,IAAI,GAAG,IAAI;MACjE,CAAC;QAAAvE,cAAA,GAAAkB,CAAA;MAAA;MAAAlB,cAAA,GAAAC,CAAA;MACD,OAAO,CAAC;IACV;EAAC;IAAAI,GAAA;IAAAC,KAAA,EAKD,SAAAkE,0BAA0BA,CAAA,EAKvB;MAAAxE,cAAA,GAAAU,CAAA;MACD,IAAM+D,WAAW,IAAAzE,cAAA,GAAAC,CAAA,QAAG,EAAE;MACtB,IAAMF,OAAO,IAAAC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC2B,aAAa,CAAC,CAAC;MAGpC,IAAM8C,WAAW,IAAA1E,cAAA,GAAAC,CAAA,QAAGF,OAAO,CAAC4E,MAAM,CAAC,UAAAC,CAAC,EAClC;QAAA5E,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAA,QAAAD,cAAA,GAAAkB,CAAA,UAAA0D,CAAC,CAACpE,IAAI,CAACqE,QAAQ,CAAC,QAAQ,CAAC,MAAA7E,cAAA,GAAAkB,CAAA,UAAI0D,CAAC,CAACtD,QAAQ,MAAAtB,cAAA,GAAAkB,CAAA,UAAI0D,CAAC,CAACtD,QAAQ,GAAG,EAAE;MAAD,CAC3D,CAAC;MAACtB,cAAA,GAAAC,CAAA;MAEF,IAAIyE,WAAW,CAACvB,MAAM,GAAG,CAAC,EAAE;QAAAnD,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QAC1BwE,WAAW,CAACtC,IAAI,CAAC;UACf2C,IAAI,EAAE,QAAiB;UACvBC,QAAQ,EAAE,QAAiB;UAC3BC,OAAO,EAAE,GAAGN,WAAW,CAACvB,MAAM,wBAAwB;UACtD8B,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC;QAAAjF,cAAA,GAAAkB,CAAA;MAAA;MAGD,IAAMgE,WAAW,IAAAlF,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACkE,cAAc,CAAC,CAAC;MAACnE,cAAA,GAAAC,CAAA;MAC1C,IAAIiF,WAAW,GAAG,GAAG,EAAE;QAAAlF,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACrBwE,WAAW,CAACtC,IAAI,CAAC;UACf2C,IAAI,EAAE,QAAiB;UACvBC,QAAQ,EAAEG,WAAW,GAAG,GAAG,IAAAlF,cAAA,GAAAkB,CAAA,WAAG,MAAM,KAAAlB,cAAA,GAAAkB,CAAA,WAAG,QAAQ;UAC/C8D,OAAO,EAAE,sBAAsBE,WAAW,CAACC,OAAO,CAAC,CAAC,CAAC,IAAI;UACzDF,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC;QAAAjF,cAAA,GAAAkB,CAAA;MAAA;MAAAlB,cAAA,GAAAC,CAAA;MAED,OAAOwE,WAAW;IACpB;EAAC;IAAApE,GAAA;IAAAC,KAAA,EAKD,SAAA8E,kBAAkBA,CAACC,SAAiB,EAAE/D,QAAgB,EAAQ;MAAAtB,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAC5DkB,OAAO,CAAC0B,GAAG,CAAC,kBAAkBwC,SAAS,iBAAiB/D,QAAQ,IAAI,CAAC;MAACtB,cAAA,GAAAC,CAAA;MAEtE,IAAIqB,QAAQ,GAAG,IAAI,EAAE;QAAAtB,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACnBkB,OAAO,CAACC,IAAI,CAAC,wBAAwBiE,SAAS,SAAS/D,QAAQ,IAAI,CAAC;MACtE,CAAC;QAAAtB,cAAA,GAAAkB,CAAA;MAAA;IACH;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAKD,SAAAgF,kBAAkBA,CAACD,SAAiB,EAAE5C,KAAY,EAAQ;MAAAzC,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACxDkB,OAAO,CAACsB,KAAK,CAAC,kBAAkB4C,SAAS,UAAU,EAAE5C,KAAK,CAAC;IAC7D;EAAC;IAAApC,GAAA;IAAAC,KAAA,EAKD,SAAAiF,eAAeA,CAAA,EAAW;MAAAvF,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAExB,OAAO,IAAI;IACb;EAAC;AAAA;AAGH,OAAO,IAAMuF,kBAAkB,IAAAxF,cAAA,GAAAC,CAAA,QAAG,IAAIJ,kBAAkB,CAAC,CAAC;AAK1D,OAAO,SAAS4F,kBAAkBA,CAACjF,IAAa,EAAE;EAAAR,cAAA,GAAAU,CAAA;EAAAV,cAAA,GAAAC,CAAA;EAChD,OAAO,UAAUyF,MAAW,EAAEC,WAAmB,EAAEC,UAA8B,EAAE;IAAA5F,cAAA,GAAAU,CAAA;IACjF,IAAMmF,cAAc,IAAA7F,cAAA,GAAAC,CAAA,QAAG2F,UAAU,CAACtF,KAAK;IACvC,IAAMwF,UAAU,IAAA9F,cAAA,GAAAC,CAAA,QAAG,CAAAD,cAAA,GAAAkB,CAAA,WAAAV,IAAI,MAAAR,cAAA,GAAAkB,CAAA,WAAI,GAAGwE,MAAM,CAACK,WAAW,CAACvF,IAAI,IAAImF,WAAW,EAAE;IAAC3F,cAAA,GAAAC,CAAA;IAEvE2F,UAAU,CAACtF,KAAK,GAAA0C,iBAAA,CAAG,aAAgC;MAAAhD,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAAA,SAAA+F,IAAA,GAAA9C,SAAA,CAAAC,MAAA,EAAb8C,IAAI,OAAApE,KAAA,CAAAmE,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;QAAJD,IAAI,CAAAC,IAAA,IAAAhD,SAAA,CAAAgD,IAAA;MAAA;MACxCV,kBAAkB,CAACjF,KAAK,CAACuF,UAAU,EAAE;QACnCK,SAAS,EAAET,MAAM,CAACK,WAAW,CAACvF,IAAI;QAClC4F,UAAU,EAAET,WAAW;QACvBM,IAAI,EAAEA,IAAI,CAAC9C;MACb,CAAC,CAAC;MAACnD,cAAA,GAAAC,CAAA;MAEH,IAAI;QACF,IAAMoG,MAAM,IAAArG,cAAA,GAAAC,CAAA,cAAS4F,cAAc,CAACnC,KAAK,CAAC,IAAI,EAAEuC,IAAI,CAAC;QAACjG,cAAA,GAAAC,CAAA;QACtDuF,kBAAkB,CAACzE,GAAG,CAAC+E,UAAU,CAAC;QAAC9F,cAAA,GAAAC,CAAA;QACnC,OAAOoG,MAAM;MACf,CAAC,CAAC,OAAO5D,KAAK,EAAE;QAAAzC,cAAA,GAAAC,CAAA;QACduF,kBAAkB,CAACzE,GAAG,CAAC+E,UAAU,CAAC;QAAC9F,cAAA,GAAAC,CAAA;QACnC,MAAMwC,KAAK;MACb;IACF,CAAC;IAACzC,cAAA,GAAAC,CAAA;IAEF,OAAO2F,UAAU;EACnB,CAAC;AACH;AAKA,OAAO,SAASU,QAAQA,CACtBC,IAAO,EACPC,IAAY,EAEsB;EAAA,IADlCC,SAAS,GAAAvD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAlD,cAAA,GAAAkB,CAAA,WAAG,KAAK;EAAAlB,cAAA,GAAAU,CAAA;EAEjB,IAAIgG,OAA8B,IAAA1G,cAAA,GAAAC,CAAA,QAAG,IAAI;EAACD,cAAA,GAAAC,CAAA;EAE1C,OAAO,SAAS0G,gBAAgBA,CAAA,EAAyB;IAAA,SAAAC,KAAA,GAAA1D,SAAA,CAAAC,MAAA,EAArB8C,IAAI,OAAApE,KAAA,CAAA+E,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJZ,IAAI,CAAAY,KAAA,IAAA3D,SAAA,CAAA2D,KAAA;IAAA;IAAA7G,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAC,CAAA;IACtC,IAAM6G,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAS;MAAA9G,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAClByG,OAAO,GAAG,IAAI;MAAC1G,cAAA,GAAAC,CAAA;MACf,IAAI,CAACwG,SAAS,EAAE;QAAAzG,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QAAAsG,IAAI,CAAA7C,KAAA,SAAIuC,IAAI,CAAC;MAAA,CAAC;QAAAjG,cAAA,GAAAkB,CAAA;MAAA;IAChC,CAAC;IAED,IAAM6F,OAAO,IAAA/G,cAAA,GAAAC,CAAA,QAAG,CAAAD,cAAA,GAAAkB,CAAA,WAAAuF,SAAS,MAAAzG,cAAA,GAAAkB,CAAA,WAAI,CAACwF,OAAO;IAAC1G,cAAA,GAAAC,CAAA;IAEtC,IAAIyG,OAAO,EAAE;MAAA1G,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAC,CAAA;MAAA+G,YAAY,CAACN,OAAO,CAAC;IAAA,CAAC;MAAA1G,cAAA,GAAAkB,CAAA;IAAA;IAAAlB,cAAA,GAAAC,CAAA;IACnCyG,OAAO,GAAGO,UAAU,CAACH,KAAK,EAAEN,IAAI,CAAQ;IAACxG,cAAA,GAAAC,CAAA;IAEzC,IAAI8G,OAAO,EAAE;MAAA/G,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAC,CAAA;MAAAsG,IAAI,CAAA7C,KAAA,SAAIuC,IAAI,CAAC;IAAA,CAAC;MAAAjG,cAAA,GAAAkB,CAAA;IAAA;EAC7B,CAAC;AACH;AAKA,OAAO,SAASgG,QAAQA,CACtBX,IAAO,EACPY,KAAa,EACqB;EAAAnH,cAAA,GAAAU,CAAA;EAClC,IAAI0G,UAAmB;EAACpH,cAAA,GAAAC,CAAA;EAExB,OAAO,SAAS0G,gBAAgBA,CAAA,EAAyB;IAAA3G,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAC,CAAA;IACvD,IAAI,CAACmH,UAAU,EAAE;MAAApH,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAC,CAAA;MAAA,SAAAoH,KAAA,GAAAnE,SAAA,CAAAC,MAAA,EADiB8C,IAAI,OAAApE,KAAA,CAAAwF,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJrB,IAAI,CAAAqB,KAAA,IAAApE,SAAA,CAAAoE,KAAA;MAAA;MAEpCf,IAAI,CAAC7C,KAAK,CAAC,IAAI,EAAEuC,IAAI,CAAC;MAACjG,cAAA,GAAAC,CAAA;MACvBmH,UAAU,GAAG,IAAI;MAACpH,cAAA,GAAAC,CAAA;MAClBgH,UAAU,CAAC,YAAM;QAAAjH,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAA,OAAAmH,UAAU,GAAG,KAAK;MAAD,CAAC,EAAED,KAAK,CAAC;IAC7C,CAAC;MAAAnH,cAAA,GAAAkB,CAAA;IAAA;EACH,CAAC;AACH;AAKA,OAAO,SAASqG,oBAAoBA,CAAIhB,IAAa,EAAc;EAAAvG,cAAA,GAAAU,CAAA;EAAAV,cAAA,GAAAC,CAAA;EACjE,OAAO,IAAI2D,OAAO,CAAC,UAACC,OAAO,EAAK;IAAA7D,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAC,CAAA;IAC9BR,kBAAkB,CAAC8H,oBAAoB,CAAC,YAAM;MAAAvH,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAC5C4D,OAAO,CAAC0C,IAAI,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAKA,WAAaiB,cAAc;EAOzB,SAAAA,eACEC,SAA+B,EAG/B;IAAA,IAFAC,SAAS,GAAAxE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAlD,cAAA,GAAAkB,CAAA,WAAG,EAAE;IAAA,IACdyG,KAAK,GAAAzE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAlD,cAAA,GAAAkB,CAAA,WAAG,GAAG;IAAApB,eAAA,OAAA0H,cAAA;IAAA,KATLI,KAAK,IAAA5H,cAAA,GAAAC,CAAA,QAAQ,EAAE;IAAA,KACfyG,OAAO,IAAA1G,cAAA,GAAAC,CAAA,QAA0B,IAAI;IAAAD,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAC,CAAA;IAU3C,IAAI,CAACwH,SAAS,GAAGA,SAAS;IAACzH,cAAA,GAAAC,CAAA;IAC3B,IAAI,CAACyH,SAAS,GAAGA,SAAS;IAAC1H,cAAA,GAAAC,CAAA;IAC3B,IAAI,CAAC0H,KAAK,GAAGA,KAAK;EACpB;EAAC,OAAAvH,YAAA,CAAAoH,cAAA;IAAAnH,GAAA;IAAAC,KAAA,EAED,SAAAuH,GAAGA,CAACC,IAAO,EAAQ;MAAA9H,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACjB,IAAI,CAAC2H,KAAK,CAACzF,IAAI,CAAC2F,IAAI,CAAC;MAAC9H,cAAA,GAAAC,CAAA;MAEtB,IAAI,IAAI,CAAC2H,KAAK,CAACzE,MAAM,IAAI,IAAI,CAACuE,SAAS,EAAE;QAAA1H,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACvC,IAAI,CAAC8H,KAAK,CAAC,CAAC;MACd,CAAC,MAAM;QAAA/H,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACL,IAAI,CAAC+H,aAAa,CAAC,CAAC;MACtB;IACF;EAAC;IAAA3H,GAAA;IAAAC,KAAA,EAED,SAAAyH,KAAKA,CAAA,EAAS;MAAA/H,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACZ,IAAI,IAAI,CAACyG,OAAO,EAAE;QAAA1G,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QAChB+G,YAAY,CAAC,IAAI,CAACN,OAAO,CAAC;QAAC1G,cAAA,GAAAC,CAAA;QAC3B,IAAI,CAACyG,OAAO,GAAG,IAAI;MACrB,CAAC;QAAA1G,cAAA,GAAAkB,CAAA;MAAA;MAAAlB,cAAA,GAAAC,CAAA;MAED,IAAI,IAAI,CAAC2H,KAAK,CAACzE,MAAM,GAAG,CAAC,EAAE;QAAAnD,cAAA,GAAAkB,CAAA;QACzB,IAAM+G,KAAK,IAAAjI,cAAA,GAAAC,CAAA,SAAAiI,kBAAA,CAAO,IAAI,CAACN,KAAK,EAAC;QAAC5H,cAAA,GAAAC,CAAA;QAC9B,IAAI,CAAC2H,KAAK,GAAG,EAAE;QAAC5H,cAAA,GAAAC,CAAA;QAChB,IAAI,CAACwH,SAAS,CAACQ,KAAK,CAAC;MACvB,CAAC;QAAAjI,cAAA,GAAAkB,CAAA;MAAA;IACH;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAED,SAAQ0H,aAAaA,CAAA,EAAS;MAAA,IAAAG,KAAA;MAAAnI,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAC5B,IAAI,IAAI,CAACyG,OAAO,EAAE;QAAA1G,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QAAA;MAAM,CAAC;QAAAD,cAAA,GAAAkB,CAAA;MAAA;MAAAlB,cAAA,GAAAC,CAAA;MAEzB,IAAI,CAACyG,OAAO,GAAGO,UAAU,CAAC,YAAM;QAAAjH,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAC9BkI,KAAI,CAACJ,KAAK,CAAC,CAAC;MACd,CAAC,EAAE,IAAI,CAACJ,KAAK,CAAQ;IACvB;EAAC;AAAA;AAMH,WAAaS,aAAa;EAAA,SAAAA,cAAA;IAAAtI,eAAA,OAAAsI,aAAA;IAAA,KAEhBjI,SAAS,IAAAH,cAAA,GAAAC,CAAA,SAA6B,EAAE;EAAA;EAAA,OAAAG,YAAA,CAAAgI,aAAA;IAAA/H,GAAA;IAAAC,KAAA,EAShD,SAAA+H,eAAeA,CAAA,EAAwB;MAAA,IAAAC,MAAA;MAAA,IAAvBC,QAAQ,GAAArF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAlD,cAAA,GAAAkB,CAAA,WAAG,IAAI;MAAAlB,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAC7B,IAAI,CAAAD,cAAA,GAAAkB,CAAA,WAAAxB,QAAQ,CAAC0E,EAAE,KAAK,KAAK,MAAApE,cAAA,GAAAkB,CAAA,WAAI,QAAQ,IAAImD,WAAW,GAAE;QAAArE,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACpDuI,WAAW,CAAC,YAAM;UAAAxI,cAAA,GAAAU,CAAA;UAChB,IAAM4D,MAAM,IAAAtE,cAAA,GAAAC,CAAA,SAAIoE,WAAW,CAASC,MAAM;UAC1C,IAAMmE,KAAK,IAAAzI,cAAA,GAAAC,CAAA,SAAG;YACZsE,cAAc,EAAED,MAAM,CAACC,cAAc;YACrCmE,eAAe,EAAEpE,MAAM,CAACoE,eAAe;YACvCC,eAAe,EAAErE,MAAM,CAACqE,eAAe;YACvCC,SAAS,EAAEhI,IAAI,CAACC,GAAG,CAAC;UACtB,CAAC;UAACb,cAAA,GAAAC,CAAA;UACFqI,MAAI,CAAC5G,eAAe,CAAC+G,KAAK,CAAC;QAC7B,CAAC,EAAEF,QAAQ,CAAC;MACd,CAAC;QAAAvI,cAAA,GAAAkB,CAAA;MAAA;IACH;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAED,SAAA2B,WAAWA,CAACC,QAA8B,EAAQ;MAAAlC,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAChD,IAAI,CAACE,SAAS,CAACgC,IAAI,CAACD,QAAQ,CAAC;IAC/B;EAAC;IAAA7B,GAAA;IAAAC,KAAA,EAED,SAAA8B,cAAcA,CAACF,QAA8B,EAAQ;MAAAlC,cAAA,GAAAU,CAAA;MACnD,IAAM2B,KAAK,IAAArC,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACE,SAAS,CAACmC,OAAO,CAACJ,QAAQ,CAAC;MAAClC,cAAA,GAAAC,CAAA;MAC/C,IAAIoC,KAAK,GAAG,CAAC,CAAC,EAAE;QAAArC,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACd,IAAI,CAACE,SAAS,CAACoC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjC,CAAC;QAAArC,cAAA,GAAAkB,CAAA;MAAA;IACH;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAED,SAAQoB,eAAeA,CAAC+G,KAAU,EAAQ;MAAAzI,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACxC,IAAI,CAACE,SAAS,CAACqC,OAAO,CAAC,UAAAN,QAAQ,EAAI;QAAAlC,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QACjC,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACFiC,QAAQ,CAACuG,KAAK,CAAC;QACjB,CAAC,CAAC,OAAOhG,KAAK,EAAE;UAAAzC,cAAA,GAAAC,CAAA;UACdkB,OAAO,CAACsB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACnD;MACF,CAAC,CAAC;IACJ;EAAC;IAAApC,GAAA;IAAAC,KAAA,EAzCD,SAAOuI,WAAWA,CAAA,EAAkB;MAAA7I,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAClC,IAAI,CAACmI,aAAa,CAACU,QAAQ,EAAE;QAAA9I,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QAC3BmI,aAAa,CAACU,QAAQ,GAAG,IAAIV,aAAa,CAAC,CAAC;MAC9C,CAAC;QAAApI,cAAA,GAAAkB,CAAA;MAAA;MAAAlB,cAAA,GAAAC,CAAA;MACD,OAAOmI,aAAa,CAACU,QAAQ;IAC/B;EAAC;AAAA;AA0CH,OAAO,IAAMC,iBAAiB,IAAA/I,cAAA,GAAAC,CAAA,SAAG;EAI/B+I,eAAe,WAAfA,eAAeA,CACb/F,GAAW,EAOH;IAAA,IANRgG,OAKC,GAAA/F,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAlD,cAAA,GAAAkB,CAAA,WAAG,CAAC,CAAC;IAAAlB,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAC,CAAA;IAEN,IAAI,CAACgD,GAAG,CAACiG,UAAU,CAAC,MAAM,CAAC,EAAE;MAAAlJ,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAC,CAAA;MAAA,OAAOgD,GAAG;IAAA,CAAC;MAAAjD,cAAA,GAAAkB,CAAA;IAAA;IAExC,IAAAiI,KAAA,IAAAnJ,cAAA,GAAAC,CAAA,SAAyDgJ,OAAO;MAAxDG,KAAK,GAAAD,KAAA,CAALC,KAAK;MAAEC,MAAM,GAAAF,KAAA,CAANE,MAAM;MAAAC,aAAA,GAAAH,KAAA,CAAEI,OAAO;MAAPA,OAAO,GAAAD,aAAA,eAAAtJ,cAAA,GAAAkB,CAAA,WAAG,EAAE,IAAAoI,aAAA;MAAAE,YAAA,GAAAL,KAAA,CAAEM,MAAM;MAANA,MAAM,GAAAD,YAAA,eAAAxJ,cAAA,GAAAkB,CAAA,WAAG,MAAM,IAAAsI,YAAA;IACpD,IAAME,MAAM,IAAA1J,cAAA,GAAAC,CAAA,SAAG,IAAI0J,eAAe,CAAC,CAAC;IAAC3J,cAAA,GAAAC,CAAA;IAErC,IAAImJ,KAAK,EAAE;MAAApJ,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAC,CAAA;MAAAyJ,MAAM,CAACE,MAAM,CAAC,GAAG,EAAER,KAAK,CAACS,QAAQ,CAAC,CAAC,CAAC;IAAA,CAAC;MAAA7J,cAAA,GAAAkB,CAAA;IAAA;IAAAlB,cAAA,GAAAC,CAAA;IAChD,IAAIoJ,MAAM,EAAE;MAAArJ,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAC,CAAA;MAAAyJ,MAAM,CAACE,MAAM,CAAC,GAAG,EAAEP,MAAM,CAACQ,QAAQ,CAAC,CAAC,CAAC;IAAA,CAAC;MAAA7J,cAAA,GAAAkB,CAAA;IAAA;IAAAlB,cAAA,GAAAC,CAAA;IAClDyJ,MAAM,CAACE,MAAM,CAAC,GAAG,EAAEL,OAAO,CAACM,QAAQ,CAAC,CAAC,CAAC;IAAC7J,cAAA,GAAAC,CAAA;IACvCyJ,MAAM,CAACE,MAAM,CAAC,GAAG,EAAEH,MAAM,CAAC;IAACzJ,cAAA,GAAAC,CAAA;IAC3ByJ,MAAM,CAACE,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC;IAE/B,IAAME,SAAS,IAAA9J,cAAA,GAAAC,CAAA,SAAGgD,GAAG,CAAC4B,QAAQ,CAAC,GAAG,CAAC,IAAA7E,cAAA,GAAAkB,CAAA,WAAG,GAAG,KAAAlB,cAAA,GAAAkB,CAAA,WAAG,GAAG;IAAClB,cAAA,GAAAC,CAAA;IAChD,OAAO,GAAGgD,GAAG,GAAG6G,SAAS,GAAGJ,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE;EACjD,CAAC;EAKDE,oBAAoB,WAApBA,oBAAoBA,CAClBC,aAAqB,EACrBC,cAAsB,EACtBC,QAAgB,EAChBC,SAAiB,EACkB;IAAAnK,cAAA,GAAAU,CAAA;IACnC,IAAM0J,WAAW,IAAApK,cAAA,GAAAC,CAAA,SAAG+J,aAAa,GAAGC,cAAc;IAElD,IAAIb,KAAK,IAAApJ,cAAA,GAAAC,CAAA,SAAG+J,aAAa;IACzB,IAAIX,MAAM,IAAArJ,cAAA,GAAAC,CAAA,SAAGgK,cAAc;IAACjK,cAAA,GAAAC,CAAA;IAE5B,IAAImJ,KAAK,GAAGc,QAAQ,EAAE;MAAAlK,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAC,CAAA;MACpBmJ,KAAK,GAAGc,QAAQ;MAAClK,cAAA,GAAAC,CAAA;MACjBoJ,MAAM,GAAGD,KAAK,GAAGgB,WAAW;IAC9B,CAAC;MAAApK,cAAA,GAAAkB,CAAA;IAAA;IAAAlB,cAAA,GAAAC,CAAA;IAED,IAAIoJ,MAAM,GAAGc,SAAS,EAAE;MAAAnK,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAC,CAAA;MACtBoJ,MAAM,GAAGc,SAAS;MAACnK,cAAA,GAAAC,CAAA;MACnBmJ,KAAK,GAAGC,MAAM,GAAGe,WAAW;IAC9B,CAAC;MAAApK,cAAA,GAAAkB,CAAA;IAAA;IAAAlB,cAAA,GAAAC,CAAA;IAED,OAAO;MACLmJ,KAAK,EAAEiB,IAAI,CAACC,KAAK,CAAClB,KAAK,CAAC;MACxBC,MAAM,EAAEgB,IAAI,CAACC,KAAK,CAACjB,MAAM;IAC3B,CAAC;EACH;AACF,CAAC;AAKD,WAAakB,yBAAyB;EAAA,SAAAA,0BAAA;IAAAzK,eAAA,OAAAyK,yBAAA;IAAA,KAE5BC,QAAQ,IAAAxK,cAAA,GAAAC,CAAA,SAAqB,IAAIC,GAAG,CAAC,CAAC;IAAA,KACtCuK,iBAAiB,IAAAzK,cAAA,GAAAC,CAAA,SAAU,EAAE;IAAA,KAC7ByK,iBAAiB,IAAA1K,cAAA,GAAAC,CAAA,SAAG,GAAG;EAAA;EAAA,OAAAG,YAAA,CAAAmK,yBAAA;IAAAlK,GAAA;IAAAC,KAAA,EAS/B,SAAAqK,YAAYA,CAACC,EAAU,EAAE3H,GAAW,EAAEK,MAAc,EAAQ;MAAAtD,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAC1D,IAAI,CAACuK,QAAQ,CAAC1J,GAAG,CAAC8J,EAAE,EAAE;QACpBA,EAAE,EAAFA,EAAE;QACF3H,GAAG,EAAHA,GAAG;QACHK,MAAM,EAANA,MAAM;QACN3C,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACrB+H,SAAS,EAAE,IAAIhI,IAAI,CAAC,CAAC,CAACiK,WAAW,CAAC;MACpC,CAAC,CAAC;IACJ;EAAC;IAAAxK,GAAA;IAAAC,KAAA,EAED,SAAAwK,UAAUA,CAACF,EAAU,EAAEG,MAAc,EAAEC,IAAY,EAAQ;MAAAhL,cAAA,GAAAU,CAAA;MACzD,IAAMuK,OAAO,IAAAjL,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACuK,QAAQ,CAACvJ,GAAG,CAAC2J,EAAE,CAAC;MAAC5K,cAAA,GAAAC,CAAA;MACtC,IAAI,CAACgL,OAAO,EAAE;QAAAjL,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QAAA;MAAM,CAAC;QAAAD,cAAA,GAAAkB,CAAA;MAAA;MAErB,IAAMG,OAAO,IAAArB,cAAA,GAAAC,CAAA,SAAGW,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1B,IAAMS,QAAQ,IAAAtB,cAAA,GAAAC,CAAA,SAAGoB,OAAO,GAAG4J,OAAO,CAACtK,SAAS;MAE5C,IAAMuK,gBAAgB,IAAAlL,cAAA,GAAAC,CAAA,SAAAuB,MAAA,CAAAC,MAAA,KACjBwJ,OAAO;QACV5J,OAAO,EAAPA,OAAO;QACPC,QAAQ,EAARA,QAAQ;QACRyJ,MAAM,EAANA,MAAM;QACNC,IAAI,EAAJA;MAAI,GACL;MAAChL,cAAA,GAAAC,CAAA;MAEF,IAAI,CAACwK,iBAAiB,CAACU,OAAO,CAACD,gBAAgB,CAAC;MAAClL,cAAA,GAAAC,CAAA;MACjD,IAAI,IAAI,CAACwK,iBAAiB,CAACtH,MAAM,GAAG,IAAI,CAACuH,iBAAiB,EAAE;QAAA1K,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QAC1D,IAAI,CAACwK,iBAAiB,CAACW,GAAG,CAAC,CAAC;MAC9B,CAAC;QAAApL,cAAA,GAAAkB,CAAA;MAAA;MAAAlB,cAAA,GAAAC,CAAA;MAED,IAAI,CAACuK,QAAQ,CAACa,MAAM,CAACT,EAAE,CAAC;MAAC5K,cAAA,GAAAC,CAAA;MAGzB,IAAIqB,QAAQ,GAAG,IAAI,EAAE;QAAAtB,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACnBkB,OAAO,CAACC,IAAI,CAAC,yBAAyB6J,OAAO,CAAChI,GAAG,SAAS3B,QAAQ,IAAI,CAAC;MACzE,CAAC;QAAAtB,cAAA,GAAAkB,CAAA;MAAA;IACH;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAED,SAAAgL,eAAeA,CAAA,EAKb;MAAAtL,cAAA,GAAAU,CAAA;MACA,IAAM6K,KAAK,IAAAvL,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACwK,iBAAiB,CAACtH,MAAM;MAACnD,cAAA,GAAAC,CAAA;MAC5C,IAAIsL,KAAK,KAAK,CAAC,EAAE;QAAAvL,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACf,OAAO;UAAEuL,mBAAmB,EAAE,CAAC;UAAEC,aAAa,EAAE,CAAC;UAAEC,cAAc,EAAE,CAAC;UAAEC,YAAY,EAAE;QAAE,CAAC;MACzF,CAAC;QAAA3L,cAAA,GAAAkB,CAAA;MAAA;MAED,IAAM0K,SAAS,IAAA5L,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACwK,iBAAiB,CAACoB,MAAM,CAAC,UAACC,GAAG,EAAEC,GAAG,EAAK;QAAA/L,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAA,OAAA6L,GAAG,GAAGC,GAAG,CAACzK,QAAQ;MAAD,CAAC,EAAE,CAAC,CAAC;MACpF,IAAM0K,MAAM,IAAAhM,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACwK,iBAAiB,CAAC9F,MAAM,CAAC,UAAAoH,GAAG,EAAI;QAAA/L,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAA,OAAA8L,GAAG,CAAChB,MAAM,IAAI,GAAG;MAAD,CAAC,CAAC,CAAC5H,MAAM;MAC7E,IAAM8I,IAAI,IAAAjM,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACwK,iBAAiB,CAAC9F,MAAM,CAAC,UAAAoH,GAAG,EAAI;QAAA/L,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAA,OAAA8L,GAAG,CAACzK,QAAQ,GAAG,IAAI;MAAD,CAAC,CAAC,CAAC6B,MAAM;MAACnD,cAAA,GAAAC,CAAA;MAE9E,OAAO;QACLuL,mBAAmB,EAAEnB,IAAI,CAACC,KAAK,CAACsB,SAAS,GAAGL,KAAK,CAAC;QAClDE,aAAa,EAAEF,KAAK;QACpBG,cAAc,EAAEM,MAAM;QACtBL,YAAY,EAAEM;MAChB,CAAC;IACH;EAAC;IAAA5L,GAAA;IAAAC,KAAA,EAlED,SAAOuI,WAAWA,CAAA,EAA8B;MAAA7I,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAC9C,IAAI,CAACsK,yBAAyB,CAACzB,QAAQ,EAAE;QAAA9I,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACvCsK,yBAAyB,CAACzB,QAAQ,GAAG,IAAIyB,yBAAyB,CAAC,CAAC;MACtE,CAAC;QAAAvK,cAAA,GAAAkB,CAAA;MAAA;MAAAlB,cAAA,GAAAC,CAAA;MACD,OAAOsK,yBAAyB,CAACzB,QAAQ;IAC3C;EAAC;AAAA;AAmEH,WAAaoD,2BAA2B;EAAA,SAAAA,4BAAA;IAAApM,eAAA,OAAAoM,2BAAA;IAAA,KAE9BC,WAAW,IAAAnM,cAAA,GAAAC,CAAA,SAA0B,IAAIC,GAAG,CAAC,CAAC;IAAA,KAC9CkM,UAAU,IAAApM,cAAA,GAAAC,CAAA,SAAwB,IAAIC,GAAG,CAAC,CAAC;EAAA;EAAA,OAAAE,YAAA,CAAA8L,2BAAA;IAAA7L,GAAA;IAAAC,KAAA,EASnD,SAAA+L,UAAUA,CAAC1J,aAAqB,EAAQ;MAAA3C,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACtC,IAAI,CAACmM,UAAU,CAACtL,GAAG,CAAC6B,aAAa,EAAE/B,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IAChD;EAAC;IAAAR,GAAA;IAAAC,KAAA,EAED,SAAAgM,WAAWA,CAAC3J,aAAqB,EAAE4J,UAAkB,EAAQ;MAAAvM,cAAA,GAAAU,CAAA;MAC3D,IAAM8L,KAAK,IAAAxM,cAAA,GAAAC,CAAA,SAAG,CAAAD,cAAA,GAAAkB,CAAA,eAAI,CAACiL,WAAW,CAAClL,GAAG,CAAC0B,aAAa,CAAC,MAAA3C,cAAA,GAAAkB,CAAA,WAAI,EAAE;MAAClB,cAAA,GAAAC,CAAA;MACxDuM,KAAK,CAACrK,IAAI,CAACoK,UAAU,CAAC;MAACvM,cAAA,GAAAC,CAAA;MAGvB,IAAIuM,KAAK,CAACrJ,MAAM,GAAG,EAAE,EAAE;QAAAnD,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACrBuM,KAAK,CAACC,KAAK,CAAC,CAAC;MACf,CAAC;QAAAzM,cAAA,GAAAkB,CAAA;MAAA;MAAAlB,cAAA,GAAAC,CAAA;MAED,IAAI,CAACkM,WAAW,CAACrL,GAAG,CAAC6B,aAAa,EAAE6J,KAAK,CAAC;MAACxM,cAAA,GAAAC,CAAA;MAG3C,IAAIsM,UAAU,GAAG,EAAE,EAAE;QAAAvM,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACnBkB,OAAO,CAACC,IAAI,CAAC,gBAAgBuB,aAAa,SAAS4J,UAAU,IAAI,CAAC;MACpE,CAAC;QAAAvM,cAAA,GAAAkB,CAAA;MAAA;IACH;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAED,SAAAoM,iBAAiBA,CAAC/J,aAAqB,EAIrC;MAAA3C,cAAA,GAAAU,CAAA;MACA,IAAM8L,KAAK,IAAAxM,cAAA,GAAAC,CAAA,SAAG,CAAAD,cAAA,GAAAkB,CAAA,eAAI,CAACiL,WAAW,CAAClL,GAAG,CAAC0B,aAAa,CAAC,MAAA3C,cAAA,GAAAkB,CAAA,WAAI,EAAE;MAAClB,cAAA,GAAAC,CAAA;MACxD,IAAIuM,KAAK,CAACrJ,MAAM,KAAK,CAAC,EAAE;QAAAnD,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACtB,OAAO;UAAE0M,iBAAiB,EAAE,CAAC;UAAEC,aAAa,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAE,CAAC;MACnE,CAAC;QAAA7M,cAAA,GAAAkB,CAAA;MAAA;MAED,IAAMqK,KAAK,IAAAvL,cAAA,GAAAC,CAAA,SAAGuM,KAAK,CAACX,MAAM,CAAC,UAACC,GAAG,EAAEgB,IAAI,EAAK;QAAA9M,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAA,OAAA6L,GAAG,GAAGgB,IAAI;MAAD,CAAC,EAAE,CAAC,CAAC;MACxD,IAAMC,GAAG,IAAA/M,cAAA,GAAAC,CAAA,SAAGoK,IAAI,CAAC0C,GAAG,CAAArJ,KAAA,CAAR2G,IAAI,EAAAnC,kBAAA,CAAQsE,KAAK,EAAC;MAACxM,cAAA,GAAAC,CAAA;MAE/B,OAAO;QACL0M,iBAAiB,EAAEtC,IAAI,CAACC,KAAK,CAACiB,KAAK,GAAGiB,KAAK,CAACrJ,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG;QAC/DyJ,aAAa,EAAEG,GAAG;QAClBF,WAAW,EAAEL,KAAK,CAACrJ;MACrB,CAAC;IACH;EAAC;IAAA9C,GAAA;IAAAC,KAAA,EAED,SAAA0M,WAAWA,CAAA,EAAwB;MAAAhN,cAAA,GAAAU,CAAA;MACjC,IAAMuM,KAA0B,IAAAjN,cAAA,GAAAC,CAAA,SAAG,CAAC,CAAC;MAACD,cAAA,GAAAC,CAAA;MACtC,SAAAiN,KAAA,IAA8B,IAAI,CAACf,WAAW,EAAE;QAAA,IAAAgB,KAAA,GAAAC,cAAA,CAAAF,KAAA;QAAA,IAApCvK,aAAa,GAAAwK,KAAA;QAAAnN,cAAA,GAAAC,CAAA;QACvBgN,KAAK,CAACtK,aAAa,CAAC,GAAG,IAAI,CAAC+J,iBAAiB,CAAC/J,aAAa,CAAC;MAC9D;MAAC3C,cAAA,GAAAC,CAAA;MACD,OAAOgN,KAAK;IACd;EAAC;IAAA5M,GAAA;IAAAC,KAAA,EAtDD,SAAOuI,WAAWA,CAAA,EAAgC;MAAA7I,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAChD,IAAI,CAACiM,2BAA2B,CAACpD,QAAQ,EAAE;QAAA9I,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACzCiM,2BAA2B,CAACpD,QAAQ,GAAG,IAAIoD,2BAA2B,CAAC,CAAC;MAC1E,CAAC;QAAAlM,cAAA,GAAAkB,CAAA;MAAA;MAAAlB,cAAA,GAAAC,CAAA;MACD,OAAOiM,2BAA2B,CAACpD,QAAQ;IAC7C;EAAC;AAAA;AAuDH,OAAO,IAAMuE,cAAc,IAAArN,cAAA,GAAAC,CAAA,SAAG;EAI5BqN,aAAa,WAAbA,aAAaA,CAAA,EAAS;IAAAtN,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAC,CAAA;IACpB,IAAIsN,OAAO,EAAE;MAAAvN,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAC,CAAA;MACXkB,OAAO,CAAC0B,GAAG,CAAC,kBAAkB,EAAE;QAC9B2K,QAAQ,EAAE9N,QAAQ,CAAC0E,EAAE;QACrBqJ,OAAO,EAAE/N,QAAQ,CAACgO,OAAO;QACzB9E,SAAS,EAAE,IAAIhI,IAAI,CAAC,CAAC,CAACiK,WAAW,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC;MAAA7K,cAAA,GAAAkB,CAAA;IAAA;EACH,CAAC;EAKDyM,oBAAoB,WAApBA,oBAAoBA,CAAChL,aAAqB,EAAQ;IAAA3C,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAC,CAAA;IAChD,IAAIsN,OAAO,EAAE;MAAAvN,cAAA,GAAAkB,CAAA;MACX,IAAMb,GAAG,IAAAL,cAAA,GAAAC,CAAA,SAAG,gBAAgB0C,aAAa,EAAE;MAC3C,IAAMiL,KAAK,IAAA5N,cAAA,GAAAC,CAAA,SAAG4N,QAAQ,CAAC,CAAA7N,cAAA,GAAAkB,CAAA,WAAC4M,MAAM,CAASzN,GAAG,CAAC,MAAAL,cAAA,GAAAkB,CAAA,WAAI,GAAG,GAAE,EAAE,CAAC,GAAG,CAAC;MAAClB,cAAA,GAAAC,CAAA;MAC3D6N,MAAM,CAASzN,GAAG,CAAC,GAAGuN,KAAK,CAAC/D,QAAQ,CAAC,CAAC;MAAC7J,cAAA,GAAAC,CAAA;MAExC,IAAI2N,KAAK,GAAG,EAAE,KAAK,CAAC,EAAE;QAAA5N,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACpBkB,OAAO,CAAC0B,GAAG,CAAC,aAAaF,aAAa,aAAaiL,KAAK,QAAQ,CAAC;MACnE,CAAC;QAAA5N,cAAA,GAAAkB,CAAA;MAAA;IACH,CAAC;MAAAlB,cAAA,GAAAkB,CAAA;IAAA;EACH;AACF,CAAC;AAKD,WAAa6M,aAAa;EAAA,SAAAA,cAAA;IAAAjO,eAAA,OAAAiO,aAAA;IAAA,KAEhBvK,KAAK,IAAAxD,cAAA,GAAAC,CAAA,SAAqB,IAAIC,GAAG,CAAC,CAAC;IAAA,KACnC8N,eAAe,IAAAhO,cAAA,GAAAC,CAAA,SAAwB,IAAIC,GAAG,CAAC,CAAC;IAAA,KAChD+N,YAAY,IAAAjO,cAAA,GAAAC,CAAA,SAAwB,IAAIC,GAAG,CAAC,CAAC;IAAA,KAC7CgO,OAAO,IAAAlO,cAAA,GAAAC,CAAA,SAAG,GAAG;EAAA;EAAA,OAAAG,YAAA,CAAA2N,aAAA;IAAA1N,GAAA;IAAAC,KAAA,EASrB,SAAAQ,GAAGA,CAACT,GAAW,EAAEC,KAAU,EAAsB;MAAA,IAApB6N,GAAG,GAAAjL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAlD,cAAA,GAAAkB,CAAA,WAAG,MAAM;MAAAlB,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAEvC,IAAI,IAAI,CAACuD,KAAK,CAACwH,IAAI,IAAI,IAAI,CAACkD,OAAO,EAAE;QAAAlO,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACnC,IAAI,CAACmO,OAAO,CAAC,CAAC;MAChB,CAAC;QAAApO,cAAA,GAAAkB,CAAA;MAAA;MAAAlB,cAAA,GAAAC,CAAA;MAED,IAAI,CAACuD,KAAK,CAAC1C,GAAG,CAACT,GAAG,EAAEC,KAAK,CAAC;MAACN,cAAA,GAAAC,CAAA;MAC3B,IAAI,CAAC+N,eAAe,CAAClN,GAAG,CAACT,GAAG,EAAEO,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGsN,GAAG,CAAC;MAACnO,cAAA,GAAAC,CAAA;MAChD,IAAI,CAACgO,YAAY,CAACnN,GAAG,CAACT,GAAG,EAAE,CAAC,CAAC;IAC/B;EAAC;IAAAA,GAAA;IAAAC,KAAA,EAED,SAAAW,GAAGA,CAACZ,GAAW,EAAO;MAAAL,cAAA,GAAAU,CAAA;MACpB,IAAM2N,cAAc,IAAArO,cAAA,GAAAC,CAAA,SAAG,IAAI,CAAC+N,eAAe,CAAC/M,GAAG,CAACZ,GAAG,CAAC;MAACL,cAAA,GAAAC,CAAA;MACrD,IAAI,CAAAD,cAAA,GAAAkB,CAAA,WAAAmN,cAAc,MAAArO,cAAA,GAAAkB,CAAA,WAAIN,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGwN,cAAc,GAAE;QAAArO,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACjD,IAAI,CAACoL,MAAM,CAAChL,GAAG,CAAC;QAACL,cAAA,GAAAC,CAAA;QACjB,OAAO,IAAI;MACb,CAAC;QAAAD,cAAA,GAAAkB,CAAA;MAAA;MAED,IAAMoN,WAAW,IAAAtO,cAAA,GAAAC,CAAA,SAAG,CAAAD,cAAA,GAAAkB,CAAA,eAAI,CAAC+M,YAAY,CAAChN,GAAG,CAACZ,GAAG,CAAC,MAAAL,cAAA,GAAAkB,CAAA,WAAI,CAAC;MAAClB,cAAA,GAAAC,CAAA;MACpD,IAAI,CAACgO,YAAY,CAACnN,GAAG,CAACT,GAAG,EAAEiO,WAAW,GAAG,CAAC,CAAC;MAACtO,cAAA,GAAAC,CAAA;MAE5C,OAAO,IAAI,CAACuD,KAAK,CAACvC,GAAG,CAACZ,GAAG,CAAC;IAC5B;EAAC;IAAAA,GAAA;IAAAC,KAAA,EAED,SAAA+K,OAAMA,CAAChL,GAAW,EAAQ;MAAAL,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACxB,IAAI,CAACuD,KAAK,CAAC6H,MAAM,CAAChL,GAAG,CAAC;MAACL,cAAA,GAAAC,CAAA;MACvB,IAAI,CAAC+N,eAAe,CAAC3C,MAAM,CAAChL,GAAG,CAAC;MAACL,cAAA,GAAAC,CAAA;MACjC,IAAI,CAACgO,YAAY,CAAC5C,MAAM,CAAChL,GAAG,CAAC;IAC/B;EAAC;IAAAA,GAAA;IAAAC,KAAA,EAED,SAAA0B,KAAKA,CAAA,EAAS;MAAAhC,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACZ,IAAI,CAACuD,KAAK,CAACxB,KAAK,CAAC,CAAC;MAAChC,cAAA,GAAAC,CAAA;MACnB,IAAI,CAAC+N,eAAe,CAAChM,KAAK,CAAC,CAAC;MAAChC,cAAA,GAAAC,CAAA;MAC7B,IAAI,CAACgO,YAAY,CAACjM,KAAK,CAAC,CAAC;IAC3B;EAAC;IAAA3B,GAAA;IAAAC,KAAA,EAED,SAAQ8N,OAAOA,CAAA,EAAS;MAAA,IAAAG,MAAA;MAAAvO,cAAA,GAAAU,CAAA;MACtB,IAAMG,GAAG,IAAAb,cAAA,GAAAC,CAAA,SAAGW,IAAI,CAACC,GAAG,CAAC,CAAC;MAACb,cAAA,GAAAC,CAAA;MAGvB,SAAAuO,KAAA,IAAoC,IAAI,CAACR,eAAe,EAAE;QAAA,IAAAS,KAAA,GAAArB,cAAA,CAAAoB,KAAA;QAAA,IAA9CnO,GAAG,GAAAoO,KAAA;QAAA,IAAEJ,cAAc,GAAAI,KAAA;QAAAzO,cAAA,GAAAC,CAAA;QAC7B,IAAIY,GAAG,GAAGwN,cAAc,EAAE;UAAArO,cAAA,GAAAkB,CAAA;UAAAlB,cAAA,GAAAC,CAAA;UACxB,IAAI,CAACoL,MAAM,CAAChL,GAAG,CAAC;QAClB,CAAC;UAAAL,cAAA,GAAAkB,CAAA;QAAA;MACH;MAAClB,cAAA,GAAAC,CAAA;MAGD,IAAI,IAAI,CAACuD,KAAK,CAACwH,IAAI,IAAI,IAAI,CAACkD,OAAO,EAAE;QAAAlO,cAAA,GAAAkB,CAAA;QACnC,IAAMwN,cAAc,IAAA1O,cAAA,GAAAC,CAAA,SAAG4B,KAAK,CAACC,IAAI,CAAC,IAAI,CAACmM,YAAY,CAACU,OAAO,CAAC,CAAC,CAAC,CAC3DC,IAAI,CAAC,UAAAC,KAAA,EAAAC,KAAA,EAAkB;UAAA,IAAAC,KAAA,GAAA3B,cAAA,CAAAyB,KAAA;YAAdG,CAAC,GAAAD,KAAA;UAAA,IAAAE,KAAA,GAAA7B,cAAA,CAAA0B,KAAA;YAAM5N,CAAC,GAAA+N,KAAA;UAAAjP,cAAA,GAAAU,CAAA;UAAAV,cAAA,GAAAC,CAAA;UAAM,OAAA+O,CAAC,GAAG9N,CAAC;QAAD,CAAC,CAAC;QAEhC,IAAMgO,QAAQ,IAAAlP,cAAA,GAAAC,CAAA,SAAGyO,cAAc,CAACS,KAAK,CAAC,CAAC,EAAE9E,IAAI,CAAC+E,KAAK,CAAC,IAAI,CAAClB,OAAO,GAAG,GAAG,CAAC,CAAC;QAAClO,cAAA,GAAAC,CAAA;QACzEiP,QAAQ,CAAC1M,OAAO,CAAC,UAAA6M,KAAA,EAAW;UAAA,IAAAC,MAAA,GAAAlC,cAAA,CAAAiC,KAAA;YAAThP,GAAG,GAAAiP,MAAA;UAAAtP,cAAA,GAAAU,CAAA;UAAAV,cAAA,GAAAC,CAAA;UAAM,OAAAsO,MAAI,CAAClD,MAAM,CAAChL,GAAG,CAAC;QAAD,CAAC,CAAC;MAC/C,CAAC;QAAAL,cAAA,GAAAkB,CAAA;MAAA;IACH;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAED,SAAAiP,QAAQA,CAAA,EAIN;MAAAvP,cAAA,GAAAU,CAAA;MACA,IAAM8O,WAAW,IAAAxP,cAAA,GAAAC,CAAA,SAAG4B,KAAK,CAACC,IAAI,CAAC,IAAI,CAACmM,YAAY,CAAClM,MAAM,CAAC,CAAC,CAAC,CAAC8J,MAAM,CAAC,UAACC,GAAG,EAAE8B,KAAK,EAAK;QAAA5N,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAA,OAAA6L,GAAG,GAAG8B,KAAK;MAAD,CAAC,EAAE,CAAC,CAAC;MACjG,IAAM6B,YAAY,IAAAzP,cAAA,GAAAC,CAAA,SAAG4B,KAAK,CAACC,IAAI,CAAC,IAAI,CAACmM,YAAY,CAACU,OAAO,CAAC,CAAC,CAAC,CACzDC,IAAI,CAAC,UAAAc,MAAA,EAAAC,MAAA,EAAkB;QAAA,IAAAC,MAAA,GAAAxC,cAAA,CAAAsC,MAAA;UAAdV,CAAC,GAAAY,MAAA;QAAA,IAAAC,MAAA,GAAAzC,cAAA,CAAAuC,MAAA;UAAMzO,CAAC,GAAA2O,MAAA;QAAA7P,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAM,OAAAiB,CAAC,GAAG8N,CAAC;MAAD,CAAC,CAAC,CAC7BG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACXW,GAAG,CAAC,UAAAC,MAAA,EAAW1P;QAAAA,GAAA,CAAAA,MAAA,CAAAA,CAAA,CAAAA,sBAAA,CAAAA;UAATA,GAAG,GAAA2P,MAAA;QAAAhQ,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAMI,MAAA,CAAAA,GAAG;MAAD,CAAC,CAAC;MAACL,cAAA,GAAAC,CAAA;MAEvB,OAAO;QACL+K,IAAI,EAAE,IAAI,CAACxH,KAAK,CAACwH,IAAI;QACrBiF,OAAO,EAAET,WAAW,GAAG,CAAC,IAAAxP,cAAA,GAAAkB,CAAA,WAAI,IAAI,CAACsC,KAAK,CAACwH,IAAI,GAAGwE,WAAW,GAAI,GAAG,KAAAxP,cAAA,GAAAkB,CAAA,WAAG,CAAC;QACpEuO,YAAY,EAAZA;MACF,CAAC;IACH;EAAC;IAAApP,GAAA;IAAAC,KAAA,EA/ED,SAAOuI,WAAWA,CAAA,EAAkB;MAAA7I,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAClC,IAAI,CAAC8N,aAAa,CAACjF,QAAQ,EAAE;QAAA9I,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QAC3B8N,aAAa,CAACjF,QAAQ,GAAG,IAAIiF,aAAa,CAAC,CAAC;MAC9C,CAAC;QAAA/N,cAAA,GAAAkB,CAAA;MAAA;MAAAlB,cAAA,GAAAC,CAAA;MACD,OAAO8N,aAAa,CAACjF,QAAQ;IAC/B;EAAC;AAAA;AAgFH,OAAO,IAAMoH,wBAAwB,IAAAlQ,cAAA,GAAAC,CAAA,SAAG;EAItCkQ,iBAAiB,EAAE,SAAnBA,iBAAiBA,CACfC,UAAyC,EACzCzN,aAAqB,EAClB;IAAA3C,cAAA,GAAAU,CAAA;IACH,IAAM2P,OAAO,IAAArQ,cAAA,GAAAC,CAAA,SAAGiM,2BAA2B,CAACrD,WAAW,CAAC,CAAC;IAAC7I,cAAA,GAAAC,CAAA;IAE1D,OAAOT,KAAK,CAAC8Q,IAAI,CAAAtN,iBAAA,CAAC,aAAY;MAAAhD,cAAA,GAAAU,CAAA;MAC5B,IAAMC,SAAS,IAAAX,cAAA,GAAAC,CAAA,SAAGW,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,IAAM0P,SAAS,IAAAvQ,cAAA,GAAAC,CAAA,eAASmQ,UAAU,CAAC,CAAC;MACpC,IAAMxN,QAAQ,IAAA5C,cAAA,GAAAC,CAAA,SAAGW,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;MAACX,cAAA,GAAAC,CAAA;MAExCoQ,OAAO,CAAChE,UAAU,CAAC1J,aAAa,CAAC;MAAC3C,cAAA,GAAAC,CAAA;MAElC,IAAI2C,QAAQ,GAAG,IAAI,EAAE;QAAA5C,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACnBkB,OAAO,CAACC,IAAI,CAAC,wBAAwBuB,aAAa,SAASC,QAAQ,IAAI,CAAC;MAC1E,CAAC;QAAA5C,cAAA,GAAAkB,CAAA;MAAA;MAAAlB,cAAA,GAAAC,CAAA;MAED,OAAOsQ,SAAS;IAClB,CAAC,EAAC;EACJ,CAAC;EAKDC,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAGC,QAAgB,EAI5B;IAAA,IAJ8BxH,OAIxC,GAAA/F,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAlD,cAAA,GAAAkB,CAAA,WAAG,CAAC,CAAC;IAAAlB,cAAA,GAAAU,CAAA;IACJ,IAAAgQ,MAAA,IAAA1Q,cAAA,GAAAC,CAAA,SAAkBN,UAAU,CAACsB,GAAG,CAAC,QAAQ,CAAC;MAAlCmI,KAAK,GAAAsH,MAAA,CAALtH,KAAK;IACb,IAAMuH,UAAU,IAAA3Q,cAAA,GAAAC,CAAA,SAAGP,QAAQ,CAAC0E,EAAE,KAAK,KAAK,IAAApE,cAAA,GAAAkB,CAAA,WAAG0P,MAAM,CAACC,gBAAgB,KAAA7Q,cAAA,GAAAkB,CAAA,WAAG,CAAC;IAAClB,cAAA,GAAAC,CAAA;IAEvE,OAAO;MACL6Q,GAAG,EAAE/H,iBAAiB,CAACC,eAAe,CAACyH,QAAQ,EAAE;QAC/CrH,KAAK,EAAEiB,IAAI,CAACC,KAAK,CAAClB,KAAK,GAAGuH,UAAU,CAAC;QACrCpH,OAAO,EAAEN,OAAO,CAAC8H,QAAQ,IAAA/Q,cAAA,GAAAkB,CAAA,WAAG,EAAE,KAAAlB,cAAA,GAAAkB,CAAA,WAAG,EAAE;QACnCuI,MAAM,EAAE;MACV,CAAC,CAAC;MACFuH,WAAW,EAAE/H,OAAO,CAACgI,qBAAqB;MAC1CF,QAAQ,EAAE,CAAA/Q,cAAA,GAAAkB,CAAA,WAAA+H,OAAO,CAAC8H,QAAQ,MAAA/Q,cAAA,GAAAkB,CAAA,WAAI,KAAK;IACrC,CAAC;EACH,CAAC;EAKDgQ,iBAAiB,EAAE,SAAnBA,iBAAiBA,CACfC,OAA0B,EAER;IAAA,IADlBxJ,KAAK,GAAAzE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAlD,cAAA,GAAAkB,CAAA,WAAG,CAAC;IAAAlB,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAC,CAAA;IAET,OAAO,IAAI2D,OAAO,CAAC,UAACC,OAAO,EAAK;MAAA7D,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAC9B,IAAI,CAAAD,cAAA,GAAAkB,CAAA,WAAAxB,QAAQ,CAAC0E,EAAE,KAAK,KAAK,MAAApE,cAAA,GAAAkB,CAAA,WAAI,qBAAqB,IAAI0P,MAAM,GAAE;QAAA5Q,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QAC3D2Q,MAAM,CAASQ,mBAAmB,CAAC,YAAM;UAAApR,cAAA,GAAAU,CAAA;UAAAV,cAAA,GAAAC,CAAA;UACxCkR,OAAO,CAAC3O,OAAO,CAAC,UAAA6O,MAAM,EAAI;YAAArR,cAAA,GAAAU,CAAA;YAAAV,cAAA,GAAAC,CAAA;YAAA,OAAAoR,MAAM,CAAC,CAAC;UAAD,CAAC,CAAC;UAACrR,cAAA,GAAAC,CAAA;UACpC4D,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;MACJ,CAAC,MAAM;QAAA7D,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACLgH,UAAU,CAAC,YAAM;UAAAjH,cAAA,GAAAU,CAAA;UAAAV,cAAA,GAAAC,CAAA;UACfkR,OAAO,CAAC3O,OAAO,CAAC,UAAA6O,MAAM,EAAI;YAAArR,cAAA,GAAAU,CAAA;YAAAV,cAAA,GAAAC,CAAA;YAAA,OAAAoR,MAAM,CAAC,CAAC;UAAD,CAAC,CAAC;UAACrR,cAAA,GAAAC,CAAA;UACpC4D,OAAO,CAAC,CAAC;QACX,CAAC,EAAE8D,KAAK,CAAC;MACX;IACF,CAAC,CAAC;EACJ,CAAC;EAKD2J,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGC,SAAiB,EAAEC,UAAkB,EAAK;IAAAxR,cAAA,GAAAU,CAAA;IAC9D,IAAA+Q,MAAA,IAAAzR,cAAA,GAAAC,CAAA,SAAmBN,UAAU,CAACsB,GAAG,CAAC,QAAQ,CAAC;MAAnCoI,MAAM,GAAAoI,MAAA,CAANpI,MAAM;IACd,IAAMqI,YAAY,IAAA1R,cAAA,GAAAC,CAAA,SAAGoK,IAAI,CAACsH,IAAI,CAACtI,MAAM,GAAGmI,UAAU,CAAC,GAAG,CAAC;IAACxR,cAAA,GAAAC,CAAA;IAExD,OAAO;MACL2R,aAAa,EAAE,SAAfA,aAAaA,CAAGC,CAAM,EAAExP,KAAa,EAAM;QAAArC,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAA;UACzCkD,MAAM,EAAEqO,UAAU;UAClBM,MAAM,EAAEN,UAAU,GAAGnP,KAAK;UAC1BA,KAAK,EAALA;QACF,CAAC;MAAD,CAAE;MACF0P,kBAAkB,EAAE1H,IAAI,CAAC2H,GAAG,CAACN,YAAY,EAAEH,SAAS,CAAC;MACrDU,mBAAmB,EAAE,CAAC;MACtBC,UAAU,EAAE,EAAE;MACdC,qBAAqB,EAAE;IACzB,CAAC;EACH;AACF,CAAC;AAKD,WAAaC,wBAAwB;EAAA,SAAAA,yBAAA;IAAAtS,eAAA,OAAAsS,wBAAA;IAAA,KAE3BC,YAAY,IAAArS,cAAA,GAAAC,CAAA,SAAG,KAAK;IAAA,KACpBqS,eAAe,IAAAtS,cAAA,GAAAC,CAAA,SAAU,EAAE;EAAA;EAAA,OAAAG,YAAA,CAAAgS,wBAAA;IAAA/R,GAAA;IAAAC,KAAA,EASnC,SAAA+H,eAAeA,CAAA,EAAS;MAAA,IAAAkK,MAAA;MAAAvS,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACtB,IAAI,IAAI,CAACoS,YAAY,EAAE;QAAArS,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QAAA;MAAM,CAAC;QAAAD,cAAA,GAAAkB,CAAA;MAAA;MAAAlB,cAAA,GAAAC,CAAA;MAE9B,IAAI,CAACoS,YAAY,GAAG,IAAI;MAACrS,cAAA,GAAAC,CAAA;MAGzBmI,aAAa,CAACS,WAAW,CAAC,CAAC,CAACR,eAAe,CAAC,CAAC;MAACrI,cAAA,GAAAC,CAAA;MAG9CuF,kBAAkB,CAACvD,WAAW,CAAC,UAACjB,MAAM,EAAK;QAAAhB,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QACzCsS,MAAI,CAACD,eAAe,CAACnQ,IAAI,CAAAX,MAAA,CAAAC,MAAA;UACvBqD,IAAI,EAAE;QAAa,GAChB9D,MAAM;UACT4H,SAAS,EAAEhI,IAAI,CAACC,GAAG,CAAC;QAAC,EACtB,CAAC;MACJ,CAAC,CAAC;MAACb,cAAA,GAAAC,CAAA;MAGHuI,WAAW,CAAC,YAAM;QAAAxI,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAChBsS,MAAI,CAACnE,OAAO,CAAC,CAAC;QAACpO,cAAA,GAAAC,CAAA;QACfsS,MAAI,CAACC,cAAc,CAAC,CAAC;MACvB,CAAC,EAAE,KAAK,CAAC;IACX;EAAC;IAAAnS,GAAA;IAAAC,KAAA,EAED,SAAAmS,cAAcA,CAAA,EAAS;MAAAzS,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACrB,IAAI,CAACoS,YAAY,GAAG,KAAK;IAC3B;EAAC;IAAAhS,GAAA;IAAAC,KAAA,EAED,SAAQ8N,OAAOA,CAAA,EAAS;MAAApO,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAEtB,IAAI,IAAI,CAACqS,eAAe,CAACnP,MAAM,GAAG,IAAI,EAAE;QAAAnD,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACtC,IAAI,CAACqS,eAAe,GAAG,IAAI,CAACA,eAAe,CAACnD,KAAK,CAAC,CAAC,IAAI,CAAC;MAC1D,CAAC;QAAAnP,cAAA,GAAAkB,CAAA;MAAA;IACH;EAAC;IAAAb,GAAA;IAAAC,KAAA;MAAA,IAAAoS,eAAA,GAAA1P,iBAAA,CAED,aAA8C;QAAAhD,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAC5C,IAAI,CAACsN,OAAO,EAAE;UAAAvN,cAAA,GAAAkB,CAAA;UAAAlB,cAAA,GAAAC,CAAA;UAAA;QAAM,CAAC;UAAAD,cAAA,GAAAkB,CAAA;QAAA;QAErB,IAAMyR,YAAY,IAAA3S,cAAA,GAAAC,CAAA,SAAGsK,yBAAyB,CAAC1B,WAAW,CAAC,CAAC,CAACyC,eAAe,CAAC,CAAC;QAC9E,IAAMsH,cAAc,IAAA5S,cAAA,GAAAC,CAAA,SAAGiM,2BAA2B,CAACrD,WAAW,CAAC,CAAC,CAACmE,WAAW,CAAC,CAAC;QAC9E,IAAM6F,UAAU,IAAA7S,cAAA,GAAAC,CAAA,SAAG8N,aAAa,CAAClF,WAAW,CAAC,CAAC,CAAC0G,QAAQ,CAAC,CAAC;QAEzD,IAAMuD,MAAM,IAAA9S,cAAA,GAAAC,CAAA,SAAG;UACb2I,SAAS,EAAE,IAAIhI,IAAI,CAAC,CAAC,CAACiK,WAAW,CAAC,CAAC;UACnCkI,OAAO,EAAEJ,YAAY;UACrBK,UAAU,EAAEJ,cAAc;UAC1BpP,KAAK,EAAEqP,UAAU;UACjBI,kBAAkB,EAAE,IAAI,CAACX,eAAe,CAACnP;QAC3C,CAAC;QAACnD,cAAA,GAAAC,CAAA;QAGF,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACF,MAAML,YAAY,CAACsT,OAAO,CAAC,oBAAoB,EAAEC,IAAI,CAACC,SAAS,CAACN,MAAM,CAAC,CAAC;QAC1E,CAAC,CAAC,OAAOrQ,KAAK,EAAE;UAAAzC,cAAA,GAAAC,CAAA;UACdkB,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEqB,KAAK,CAAC;QAC5D;QAACzC,cAAA,GAAAC,CAAA;QAEDkB,OAAO,CAAC0B,GAAG,CAAC,qBAAqB,EAAEiQ,MAAM,CAAC;MAC5C,CAAC;MAAA,SAvBaN,cAAcA,CAAA;QAAA,OAAAE,eAAA,CAAAhP,KAAA,OAAAR,SAAA;MAAA;MAAA,OAAdsP,cAAc;IAAA;EAAA;IAAAnS,GAAA;IAAAC,KAAA;MAAA,IAAA+S,iBAAA,GAAArQ,iBAAA,CAyB5B,aAAyC;QAAAhD,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QACvC,IAAI;UACF,IAAMqT,MAAM,IAAAtT,cAAA,GAAAC,CAAA,eAASL,YAAY,CAAC2T,OAAO,CAAC,oBAAoB,CAAC;UAACvT,cAAA,GAAAC,CAAA;UAChE,OAAOqT,MAAM,IAAAtT,cAAA,GAAAkB,CAAA,WAAG,CAACiS,IAAI,CAACK,KAAK,CAACF,MAAM,CAAC,CAAC,KAAAtT,cAAA,GAAAkB,CAAA,WAAG,EAAE;QAC3C,CAAC,CAAC,OAAOuB,KAAK,EAAE;UAAAzC,cAAA,GAAAC,CAAA;UACdkB,OAAO,CAACC,IAAI,CAAC,yCAAyC,EAAEqB,KAAK,CAAC;UAACzC,cAAA,GAAAC,CAAA;UAC/D,OAAO,EAAE;QACX;MACF,CAAC;MAAA,SARKwT,gBAAgBA,CAAA;QAAA,OAAAJ,iBAAA,CAAA3P,KAAA,OAAAR,SAAA;MAAA;MAAA,OAAhBuQ,gBAAgB;IAAA;EAAA;IAAApT,GAAA;IAAAC,KAAA,EAnEtB,SAAOuI,WAAWA,CAAA,EAA6B;MAAA7I,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAC7C,IAAI,CAACmS,wBAAwB,CAACtJ,QAAQ,EAAE;QAAA9I,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAC,CAAA;QACtCmS,wBAAwB,CAACtJ,QAAQ,GAAG,IAAIsJ,wBAAwB,CAAC,CAAC;MACpE,CAAC;QAAApS,cAAA,GAAAkB,CAAA;MAAA;MAAAlB,cAAA,GAAAC,CAAA;MACD,OAAOmS,wBAAwB,CAACtJ,QAAQ;IAC1C;EAAC;AAAA;AAuEF9I,cAAA,GAAAC,CAAA;AAGD,IAAIsN,OAAO,EAAE;EAAAvN,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAC,CAAA;EACXmS,wBAAwB,CAACvJ,WAAW,CAAC,CAAC,CAACR,eAAe,CAAC,CAAC;AAC1D,CAAC;EAAArI,cAAA,GAAAkB,CAAA;AAAA;AAGD,OAAO,IAAMwS,cAAc,IAAA1T,cAAA,GAAAC,CAAA,SAAGsK,yBAAyB,CAAC1B,WAAW,CAAC,CAAC;AACrE,OAAO,IAAM8K,gBAAgB,IAAA3T,cAAA,GAAAC,CAAA,SAAGiM,2BAA2B,CAACrD,WAAW,CAAC,CAAC;AACzE,OAAO,IAAM+K,aAAa,IAAA5T,cAAA,GAAAC,CAAA,SAAG8N,aAAa,CAAClF,WAAW,CAAC,CAAC;AACxD,OAAO,IAAMgL,wBAAwB,IAAA7T,cAAA,GAAAC,CAAA,SAAGmS,wBAAwB,CAACvJ,WAAW,CAAC,CAAC", "ignoreList": []}