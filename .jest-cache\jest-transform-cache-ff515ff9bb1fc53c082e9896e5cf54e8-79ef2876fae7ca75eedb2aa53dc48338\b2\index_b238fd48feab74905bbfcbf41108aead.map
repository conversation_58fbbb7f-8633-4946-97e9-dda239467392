{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "useMergeRefs", "React", "_mergeRefs", "_len", "arguments", "length", "args", "Array", "_key", "useMemo", "apply", "concat", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = useMergeRefs;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _mergeRefs = _interopRequireDefault(require(\"../mergeRefs\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nfunction useMergeRefs() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  return React.useMemo(() => (0, _mergeRefs.default)(...args),\n  // eslint-disable-next-line\n  [...args]);\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAGI,YAAY;AAC9B,IAAIC,KAAK,GAAGJ,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIO,UAAU,GAAGR,sBAAsB,CAACC,OAAO,eAAe,CAAC,CAAC;AAUhE,SAASK,YAAYA,CAAA,EAAG;EACtB,KAAK,IAAIG,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC9B;EACA,OAAOP,KAAK,CAACQ,OAAO,CAAC;IAAA,OAAM,CAAC,CAAC,EAAEP,UAAU,CAACN,OAAO,EAAAc,KAAA,SAAKJ,IAAI,CAAC;EAAA,MAAAK,MAAA,CAEvDL,IAAI,CAAC,CAAC;AACZ;AACAM,MAAM,CAACd,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}