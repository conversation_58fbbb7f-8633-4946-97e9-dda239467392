0f55dd8ffac995123e08babf480e31ff
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _AnimatedInterpolation = _interopRequireDefault(require("./AnimatedInterpolation"));
var _AnimatedWithChildren = _interopRequireDefault(require("./AnimatedWithChildren"));
var AnimatedDiffClamp = function (_AnimatedWithChildren2) {
  function AnimatedDiffClamp(a, min, max) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedDiffClamp);
    _this = _callSuper(this, AnimatedDiffClamp);
    _this._a = a;
    _this._min = min;
    _this._max = max;
    _this._value = _this._lastValue = _this._a.__getValue();
    return _this;
  }
  (0, _inherits2.default)(AnimatedDiffClamp, _AnimatedWithChildren2);
  return (0, _createClass2.default)(AnimatedDiffClamp, [{
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      this._a.__makeNative(platformConfig);
      _superPropGet(AnimatedDiffClamp, "__makeNative", this, 3)([platformConfig]);
    }
  }, {
    key: "interpolate",
    value: function interpolate(config) {
      return new _AnimatedInterpolation.default(this, config);
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      var value = this._a.__getValue();
      var diff = value - this._lastValue;
      this._lastValue = value;
      this._value = Math.min(Math.max(this._value + diff, this._min), this._max);
      return this._value;
    }
  }, {
    key: "__attach",
    value: function __attach() {
      this._a.__addChild(this);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      this._a.__removeChild(this);
      _superPropGet(AnimatedDiffClamp, "__detach", this, 3)([]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      return {
        type: 'diffclamp',
        input: this._a.__getNativeTag(),
        min: this._min,
        max: this._max
      };
    }
  }]);
}(_AnimatedWithChildren.default);
var _default = exports.default = AnimatedDiffClamp;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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