98b446ad622a5628838762f81c1d4d31
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _AnimatedNode = _interopRequireDefault(require("./AnimatedNode"));
var _AnimatedTransform = _interopRequireDefault(require("./AnimatedTransform"));
var _AnimatedWithChildren = _interopRequireDefault(require("./AnimatedWithChildren"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../NativeAnimatedHelper"));
var _StyleSheet = _interopRequireDefault(require("../../../../exports/StyleSheet"));
var flattenStyle = _StyleSheet.default.flatten;
function createAnimatedStyle(inputStyle) {
  var style = flattenStyle(inputStyle);
  var animatedStyles = {};
  for (var key in style) {
    var value = style[key];
    if (key === 'transform' && Array.isArray(value)) {
      animatedStyles[key] = new _AnimatedTransform.default(value);
    } else if (value instanceof _AnimatedNode.default) {
      animatedStyles[key] = value;
    } else if (value && !Array.isArray(value) && typeof value === 'object') {
      animatedStyles[key] = createAnimatedStyle(value);
    }
  }
  return animatedStyles;
}
var AnimatedStyle = function (_AnimatedWithChildren2) {
  function AnimatedStyle(style) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedStyle);
    _this = _callSuper(this, AnimatedStyle);
    _this._inputStyle = style;
    _this._style = createAnimatedStyle(style);
    return _this;
  }
  (0, _inherits2.default)(AnimatedStyle, _AnimatedWithChildren2);
  return (0, _createClass2.default)(AnimatedStyle, [{
    key: "_walkStyleAndGetValues",
    value: function _walkStyleAndGetValues(style) {
      var updatedStyle = {};
      for (var key in style) {
        var value = style[key];
        if (value instanceof _AnimatedNode.default) {
          if (!value.__isNative) {
            updatedStyle[key] = value.__getValue();
          }
        } else if (value && !Array.isArray(value) && typeof value === 'object') {
          updatedStyle[key] = this._walkStyleAndGetValues(value);
        } else {
          updatedStyle[key] = value;
        }
      }
      return updatedStyle;
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      return [this._inputStyle, this._walkStyleAndGetValues(this._style)];
    }
  }, {
    key: "_walkStyleAndGetAnimatedValues",
    value: function _walkStyleAndGetAnimatedValues(style) {
      var updatedStyle = {};
      for (var key in style) {
        var value = style[key];
        if (value instanceof _AnimatedNode.default) {
          updatedStyle[key] = value.__getAnimatedValue();
        } else if (value && !Array.isArray(value) && typeof value === 'object') {
          updatedStyle[key] = this._walkStyleAndGetAnimatedValues(value);
        }
      }
      return updatedStyle;
    }
  }, {
    key: "__getAnimatedValue",
    value: function __getAnimatedValue() {
      return this._walkStyleAndGetAnimatedValues(this._style);
    }
  }, {
    key: "__attach",
    value: function __attach() {
      for (var key in this._style) {
        var value = this._style[key];
        if (value instanceof _AnimatedNode.default) {
          value.__addChild(this);
        }
      }
    }
  }, {
    key: "__detach",
    value: function __detach() {
      for (var key in this._style) {
        var value = this._style[key];
        if (value instanceof _AnimatedNode.default) {
          value.__removeChild(this);
        }
      }
      _superPropGet(AnimatedStyle, "__detach", this, 3)([]);
    }
  }, {
    key: "__makeNative",
    value: function __makeNative() {
      for (var key in this._style) {
        var value = this._style[key];
        if (value instanceof _AnimatedNode.default) {
          value.__makeNative();
        }
      }
      _superPropGet(AnimatedStyle, "__makeNative", this, 3)([]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      var styleConfig = {};
      for (var styleKey in this._style) {
        if (this._style[styleKey] instanceof _AnimatedNode.default) {
          var style = this._style[styleKey];
          style.__makeNative();
          styleConfig[styleKey] = style.__getNativeTag();
        }
      }
      _NativeAnimatedHelper.default.validateStyles(styleConfig);
      return {
        type: 'style',
        style: styleConfig
      };
    }
  }]);
}(_AnimatedWithChildren.default);
var _default = exports.default = AnimatedStyle;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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