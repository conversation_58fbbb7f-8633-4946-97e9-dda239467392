{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_interopRequireWildcard", "_interopRequireDefault", "exports", "__esModule", "_invariant", "React", "StateSafePureComponent", "_React$PureComponent", "props", "_this", "_inAsyncStateUpdate", "_installSetStateHooks", "key", "value", "setState", "partialState", "callback", "_this2", "state", "ret", "err", "that", "Object", "defineProperty", "get", "set", "newProps", "newState", "PureComponent", "module"], "sources": ["StateSafePureComponent.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nvar React = _interopRequireWildcard(require(\"react\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n/**\n * `setState` is called asynchronously, and should not rely on the value of\n * `this.props` or `this.state`:\n * https://reactjs.org/docs/state-and-lifecycle.html#state-updates-may-be-asynchronous\n *\n * SafePureComponent adds runtime enforcement, to catch cases where these\n * variables are read in a state updater function, instead of the ones passed\n * in.\n */\nclass StateSafePureComponent extends React.PureComponent {\n  constructor(props) {\n    super(props);\n    this._inAsyncStateUpdate = false;\n    this._installSetStateHooks();\n  }\n  setState(partialState, callback) {\n    if (typeof partialState === 'function') {\n      super.setState((state, props) => {\n        this._inAsyncStateUpdate = true;\n        var ret;\n        try {\n          ret = partialState(state, props);\n        } catch (err) {\n          throw err;\n        } finally {\n          this._inAsyncStateUpdate = false;\n        }\n        return ret;\n      }, callback);\n    } else {\n      super.setState(partialState, callback);\n    }\n  }\n  _installSetStateHooks() {\n    var that = this;\n    var props = this.props,\n      state = this.state;\n    Object.defineProperty(this, 'props', {\n      get() {\n        (0, _invariant.default)(!that._inAsyncStateUpdate, '\"this.props\" should not be accessed during state updates');\n        return props;\n      },\n      set(newProps) {\n        props = newProps;\n      }\n    });\n    Object.defineProperty(this, 'state', {\n      get() {\n        (0, _invariant.default)(!that._inAsyncStateUpdate, '\"this.state\" should not be acceessed during state updates');\n        return state;\n      },\n      set(newState) {\n        state = newState;\n      }\n    });\n  }\n}\nexports.default = StateSafePureComponent;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,KAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,IAAAM,UAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAA,SAAAO,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,SAAAa,cAAAb,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAY,CAAA,QAAAC,CAAA,OAAAlB,KAAA,CAAAM,OAAA,MAAAP,gBAAA,CAAAO,OAAA,MAAAW,CAAA,GAAAd,CAAA,CAAAU,SAAA,GAAAV,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAY,CAAA,yBAAAC,CAAA,aAAAf,CAAA,WAAAe,CAAA,CAAAP,KAAA,CAAAN,CAAA,EAAAF,CAAA,OAAAe,CAAA;AAEb,IAAIC,uBAAuB,GAAGxB,OAAO,CAAC,+CAA+C,CAAC,CAACW,OAAO;AAC9F,IAAIc,sBAAsB,GAAGzB,OAAO,CAAC,8CAA8C,CAAC,CAACW,OAAO;AAC5Fe,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACf,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIiB,UAAU,GAAGH,sBAAsB,CAACzB,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAI6B,KAAK,GAAGL,uBAAuB,CAACxB,OAAO,CAAC,OAAO,CAAC,CAAC;AAAC,IAoBhD8B,sBAAsB,aAAAC,oBAAA;EAC1B,SAAAD,uBAAYE,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAhC,gBAAA,CAAAU,OAAA,QAAAmB,sBAAA;IACjBG,KAAA,GAAA1B,UAAA,OAAAuB,sBAAA,GAAME,KAAK;IACXC,KAAA,CAAKC,mBAAmB,GAAG,KAAK;IAChCD,KAAA,CAAKE,qBAAqB,CAAC,CAAC;IAAC,OAAAF,KAAA;EAC/B;EAAC,IAAA3B,UAAA,CAAAK,OAAA,EAAAmB,sBAAA,EAAAC,oBAAA;EAAA,WAAA7B,aAAA,CAAAS,OAAA,EAAAmB,sBAAA;IAAAM,GAAA;IAAAC,KAAA,EACD,SAAAC,QAAQA,CAACC,YAAY,EAAEC,QAAQ,EAAE;MAAA,IAAAC,MAAA;MAC/B,IAAI,OAAOF,YAAY,KAAK,UAAU,EAAE;QACtClB,aAAA,CAAAS,sBAAA,wBAAe,UAACY,KAAK,EAAEV,KAAK,EAAK;UAC/BS,MAAI,CAACP,mBAAmB,GAAG,IAAI;UAC/B,IAAIS,GAAG;UACP,IAAI;YACFA,GAAG,GAAGJ,YAAY,CAACG,KAAK,EAAEV,KAAK,CAAC;UAClC,CAAC,CAAC,OAAOY,GAAG,EAAE;YACZ,MAAMA,GAAG;UACX,CAAC,SAAS;YACRH,MAAI,CAACP,mBAAmB,GAAG,KAAK;UAClC;UACA,OAAOS,GAAG;QACZ,CAAC,EAAEH,QAAQ;MACb,CAAC,MAAM;QACLnB,aAAA,CAAAS,sBAAA,wBAAeS,YAAY,EAAEC,QAAQ;MACvC;IACF;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAF,qBAAqBA,CAAA,EAAG;MACtB,IAAIU,IAAI,GAAG,IAAI;MACf,IAAIb,KAAK,GAAG,IAAI,CAACA,KAAK;QACpBU,KAAK,GAAG,IAAI,CAACA,KAAK;MACpBI,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;QACnCC,GAAG,WAAHA,GAAGA,CAAA,EAAG;UACJ,CAAC,CAAC,EAAEpB,UAAU,CAACjB,OAAO,EAAE,CAACkC,IAAI,CAACX,mBAAmB,EAAE,0DAA0D,CAAC;UAC9G,OAAOF,KAAK;QACd,CAAC;QACDiB,GAAG,WAAHA,GAAGA,CAACC,QAAQ,EAAE;UACZlB,KAAK,GAAGkB,QAAQ;QAClB;MACF,CAAC,CAAC;MACFJ,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;QACnCC,GAAG,WAAHA,GAAGA,CAAA,EAAG;UACJ,CAAC,CAAC,EAAEpB,UAAU,CAACjB,OAAO,EAAE,CAACkC,IAAI,CAACX,mBAAmB,EAAE,2DAA2D,CAAC;UAC/G,OAAOQ,KAAK;QACd,CAAC;QACDO,GAAG,WAAHA,GAAGA,CAACE,QAAQ,EAAE;UACZT,KAAK,GAAGS,QAAQ;QAClB;MACF,CAAC,CAAC;IACJ;EAAC;AAAA,EA9CkCtB,KAAK,CAACuB,aAAa;AAgDxD1B,OAAO,CAACf,OAAO,GAAGmB,sBAAsB;AACxCuB,MAAM,CAAC3B,OAAO,GAAGA,OAAO,CAACf,OAAO", "ignoreList": []}