84e77c6ec109dfa21a9609ef7657e28d
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_4hnbjnpn2() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\edge\\GlobalCDNManager.ts";
  var hash = "d4a4e2a5de14f4183cd8bce59086205260a2cddf";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\edge\\GlobalCDNManager.ts",
    statementMap: {
      "0": {
        start: {
          line: 76,
          column: 51
        },
        end: {
          line: 76,
          column: 60
        }
      },
      "1": {
        start: {
          line: 77,
          column: 58
        },
        end: {
          line: 77,
          column: 67
        }
      },
      "2": {
        start: {
          line: 78,
          column: 50
        },
        end: {
          line: 78,
          column: 59
        }
      },
      "3": {
        start: {
          line: 79,
          column: 46
        },
        end: {
          line: 79,
          column: 55
        }
      },
      "4": {
        start: {
          line: 81,
          column: 50
        },
        end: {
          line: 151,
          column: 3
        }
      },
      "5": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 154,
          column: 32
        }
      },
      "6": {
        start: {
          line: 161,
          column: 4
        },
        end: {
          line: 179,
          column: 5
        }
      },
      "7": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 165,
          column: 9
        }
      },
      "8": {
        start: {
          line: 164,
          column: 8
        },
        end: {
          line: 164,
          column: 53
        }
      },
      "9": {
        start: {
          line: 168,
          column: 6
        },
        end: {
          line: 168,
          column: 45
        }
      },
      "10": {
        start: {
          line: 171,
          column: 6
        },
        end: {
          line: 171,
          column: 40
        }
      },
      "11": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 40
        }
      },
      "12": {
        start: {
          line: 176,
          column: 6
        },
        end: {
          line: 176,
          column: 65
        }
      },
      "13": {
        start: {
          line: 178,
          column: 6
        },
        end: {
          line: 178,
          column: 71
        }
      },
      "14": {
        start: {
          line: 186,
          column: 4
        },
        end: {
          line: 220,
          column: 5
        }
      },
      "15": {
        start: {
          line: 187,
          column: 24
        },
        end: {
          line: 187,
          column: 34
        }
      },
      "16": {
        start: {
          line: 190,
          column: 30
        },
        end: {
          line: 190,
          column: 71
        }
      },
      "17": {
        start: {
          line: 193,
          column: 28
        },
        end: {
          line: 193,
          column: 90
        }
      },
      "18": {
        start: {
          line: 196,
          column: 27
        },
        end: {
          line: 196,
          column: 93
        }
      },
      "19": {
        start: {
          line: 199,
          column: 27
        },
        end: {
          line: 199,
          column: 84
        }
      },
      "20": {
        start: {
          line: 201,
          column: 37
        },
        end: {
          line: 209,
          column: 7
        }
      },
      "21": {
        start: {
          line: 212,
          column: 29
        },
        end: {
          line: 212,
          column: 51
        }
      },
      "22": {
        start: {
          line: 213,
          column: 6
        },
        end: {
          line: 213,
          column: 80
        }
      },
      "23": {
        start: {
          line: 215,
          column: 6
        },
        end: {
          line: 215,
          column: 20
        }
      },
      "24": {
        start: {
          line: 218,
          column: 6
        },
        end: {
          line: 218,
          column: 68
        }
      },
      "25": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 219,
          column: 53
        }
      },
      "26": {
        start: {
          line: 231,
          column: 4
        },
        end: {
          line: 251,
          column: 5
        }
      },
      "27": {
        start: {
          line: 232,
          column: 47
        },
        end: {
          line: 232,
          column: 49
        }
      },
      "28": {
        start: {
          line: 234,
          column: 6
        },
        end: {
          line: 244,
          column: 7
        }
      },
      "29": {
        start: {
          line: 235,
          column: 8
        },
        end: {
          line: 243,
          column: 9
        }
      },
      "30": {
        start: {
          line: 236,
          column: 28
        },
        end: {
          line: 238,
          column: 54
        }
      },
      "31": {
        start: {
          line: 240,
          column: 10
        },
        end: {
          line: 242,
          column: 11
        }
      },
      "32": {
        start: {
          line: 241,
          column: 12
        },
        end: {
          line: 241,
          column: 83
        }
      },
      "33": {
        start: {
          line: 246,
          column: 6
        },
        end: {
          line: 246,
          column: 48
        }
      },
      "34": {
        start: {
          line: 247,
          column: 6
        },
        end: {
          line: 247,
          column: 93
        }
      },
      "35": {
        start: {
          line: 250,
          column: 6
        },
        end: {
          line: 250,
          column: 66
        }
      },
      "36": {
        start: {
          line: 271,
          column: 20
        },
        end: {
          line: 276,
          column: 5
        }
      },
      "37": {
        start: {
          line: 278,
          column: 4
        },
        end: {
          line: 312,
          column: 5
        }
      },
      "38": {
        start: {
          line: 280,
          column: 27
        },
        end: {
          line: 280,
          column: 77
        }
      },
      "39": {
        start: {
          line: 281,
          column: 6
        },
        end: {
          line: 281,
          column: 107
        }
      },
      "40": {
        start: {
          line: 281,
          column: 64
        },
        end: {
          line: 281,
          column: 80
        }
      },
      "41": {
        start: {
          line: 284,
          column: 24
        },
        end: {
          line: 284,
          column: 62
        }
      },
      "42": {
        start: {
          line: 285,
          column: 6
        },
        end: {
          line: 285,
          column: 108
        }
      },
      "43": {
        start: {
          line: 285,
          column: 58
        },
        end: {
          line: 285,
          column: 84
        }
      },
      "44": {
        start: {
          line: 288,
          column: 6
        },
        end: {
          line: 297,
          column: 7
        }
      },
      "45": {
        start: {
          line: 289,
          column: 30
        },
        end: {
          line: 289,
          column: 99
        }
      },
      "46": {
        start: {
          line: 289,
          column: 60
        },
        end: {
          line: 289,
          column: 76
        }
      },
      "47": {
        start: {
          line: 290,
          column: 34
        },
        end: {
          line: 290,
          column: 107
        }
      },
      "48": {
        start: {
          line: 290,
          column: 64
        },
        end: {
          line: 290,
          column: 84
        }
      },
      "49": {
        start: {
          line: 292,
          column: 8
        },
        end: {
          line: 296,
          column: 10
        }
      },
      "50": {
        start: {
          line: 300,
          column: 6
        },
        end: {
          line: 306,
          column: 7
        }
      },
      "51": {
        start: {
          line: 301,
          column: 8
        },
        end: {
          line: 305,
          column: 10
        }
      },
      "52": {
        start: {
          line: 308,
          column: 6
        },
        end: {
          line: 308,
          column: 21
        }
      },
      "53": {
        start: {
          line: 310,
          column: 6
        },
        end: {
          line: 310,
          column: 72
        }
      },
      "54": {
        start: {
          line: 311,
          column: 6
        },
        end: {
          line: 311,
          column: 21
        }
      },
      "55": {
        start: {
          line: 319,
          column: 4
        },
        end: {
          line: 342,
          column: 5
        }
      },
      "56": {
        start: {
          line: 320,
          column: 6
        },
        end: {
          line: 320,
          column: 54
        }
      },
      "57": {
        start: {
          line: 323,
          column: 27
        },
        end: {
          line: 325,
          column: 64
        }
      },
      "58": {
        start: {
          line: 325,
          column: 25
        },
        end: {
          line: 325,
          column: 63
        }
      },
      "59": {
        start: {
          line: 327,
          column: 26
        },
        end: {
          line: 327,
          column: 64
        }
      },
      "60": {
        start: {
          line: 330,
          column: 6
        },
        end: {
          line: 330,
          column: 49
        }
      },
      "61": {
        start: {
          line: 333,
          column: 30
        },
        end: {
          line: 333,
          column: 70
        }
      },
      "62": {
        start: {
          line: 334,
          column: 6
        },
        end: {
          line: 336,
          column: 7
        }
      },
      "63": {
        start: {
          line: 335,
          column: 8
        },
        end: {
          line: 335,
          column: 69
        }
      },
      "64": {
        start: {
          line: 338,
          column: 6
        },
        end: {
          line: 338,
          column: 63
        }
      },
      "65": {
        start: {
          line: 341,
          column: 6
        },
        end: {
          line: 341,
          column: 65
        }
      },
      "66": {
        start: {
          line: 348,
          column: 4
        },
        end: {
          line: 364,
          column: 5
        }
      },
      "67": {
        start: {
          line: 349,
          column: 6
        },
        end: {
          line: 363,
          column: 7
        }
      },
      "68": {
        start: {
          line: 350,
          column: 38
        },
        end: {
          line: 357,
          column: 9
        }
      },
      "69": {
        start: {
          line: 359,
          column: 8
        },
        end: {
          line: 361,
          column: 9
        }
      },
      "70": {
        start: {
          line: 360,
          column: 10
        },
        end: {
          line: 360,
          column: 49
        }
      },
      "71": {
        start: {
          line: 362,
          column: 8
        },
        end: {
          line: 362,
          column: 59
        }
      },
      "72": {
        start: {
          line: 368,
          column: 23
        },
        end: {
          line: 368,
          column: 69
        }
      },
      "73": {
        start: {
          line: 369,
          column: 31
        },
        end: {
          line: 370,
          column: 80
        }
      },
      "74": {
        start: {
          line: 373,
          column: 28
        },
        end: {
          line: 397,
          column: 6
        }
      },
      "75": {
        start: {
          line: 374,
          column: 18
        },
        end: {
          line: 374,
          column: 19
        }
      },
      "76": {
        start: {
          line: 377,
          column: 6
        },
        end: {
          line: 377,
          column: 51
        }
      },
      "77": {
        start: {
          line: 380,
          column: 6
        },
        end: {
          line: 380,
          column: 41
        }
      },
      "78": {
        start: {
          line: 383,
          column: 23
        },
        end: {
          line: 383,
          column: 63
        }
      },
      "79": {
        start: {
          line: 384,
          column: 6
        },
        end: {
          line: 388,
          column: 7
        }
      },
      "80": {
        start: {
          line: 385,
          column: 8
        },
        end: {
          line: 385,
          column: 93
        }
      },
      "81": {
        start: {
          line: 385,
          column: 81
        },
        end: {
          line: 385,
          column: 93
        }
      },
      "82": {
        start: {
          line: 386,
          column: 8
        },
        end: {
          line: 386,
          column: 90
        }
      },
      "83": {
        start: {
          line: 386,
          column: 78
        },
        end: {
          line: 386,
          column: 90
        }
      },
      "84": {
        start: {
          line: 387,
          column: 8
        },
        end: {
          line: 387,
          column: 93
        }
      },
      "85": {
        start: {
          line: 387,
          column: 81
        },
        end: {
          line: 387,
          column: 93
        }
      },
      "86": {
        start: {
          line: 391,
          column: 25
        },
        end: {
          line: 391,
          column: 93
        }
      },
      "87": {
        start: {
          line: 392,
          column: 6
        },
        end: {
          line: 394,
          column: 7
        }
      },
      "88": {
        start: {
          line: 393,
          column: 8
        },
        end: {
          line: 393,
          column: 46
        }
      },
      "89": {
        start: {
          line: 396,
          column: 6
        },
        end: {
          line: 396,
          column: 33
        }
      },
      "90": {
        start: {
          line: 400,
          column: 4
        },
        end: {
          line: 400,
          column: 54
        }
      },
      "91": {
        start: {
          line: 400,
          column: 35
        },
        end: {
          line: 400,
          column: 52
        }
      },
      "92": {
        start: {
          line: 401,
          column: 4
        },
        end: {
          line: 401,
          column: 65
        }
      },
      "93": {
        start: {
          line: 408,
          column: 36
        },
        end: {
          line: 408,
          column: 38
        }
      },
      "94": {
        start: {
          line: 409,
          column: 21
        },
        end: {
          line: 409,
          column: 61
        }
      },
      "95": {
        start: {
          line: 411,
          column: 4
        },
        end: {
          line: 411,
          column: 40
        }
      },
      "96": {
        start: {
          line: 411,
          column: 19
        },
        end: {
          line: 411,
          column: 40
        }
      },
      "97": {
        start: {
          line: 414,
          column: 4
        },
        end: {
          line: 418,
          column: 5
        }
      },
      "98": {
        start: {
          line: 415,
          column: 6
        },
        end: {
          line: 415,
          column: 44
        }
      },
      "99": {
        start: {
          line: 416,
          column: 6
        },
        end: {
          line: 416,
          column: 46
        }
      },
      "100": {
        start: {
          line: 417,
          column: 6
        },
        end: {
          line: 417,
          column: 49
        }
      },
      "101": {
        start: {
          line: 421,
          column: 4
        },
        end: {
          line: 424,
          column: 5
        }
      },
      "102": {
        start: {
          line: 422,
          column: 6
        },
        end: {
          line: 422,
          column: 45
        }
      },
      "103": {
        start: {
          line: 423,
          column: 6
        },
        end: {
          line: 423,
          column: 48
        }
      },
      "104": {
        start: {
          line: 427,
          column: 4
        },
        end: {
          line: 435,
          column: 5
        }
      },
      "105": {
        start: {
          line: 428,
          column: 6
        },
        end: {
          line: 431,
          column: 7
        }
      },
      "106": {
        start: {
          line: 429,
          column: 8
        },
        end: {
          line: 429,
          column: 48
        }
      },
      "107": {
        start: {
          line: 430,
          column: 8
        },
        end: {
          line: 430,
          column: 48
        }
      },
      "108": {
        start: {
          line: 432,
          column: 6
        },
        end: {
          line: 434,
          column: 7
        }
      },
      "109": {
        start: {
          line: 433,
          column: 8
        },
        end: {
          line: 433,
          column: 50
        }
      },
      "110": {
        start: {
          line: 438,
          column: 4
        },
        end: {
          line: 440,
          column: 5
        }
      },
      "111": {
        start: {
          line: 439,
          column: 6
        },
        end: {
          line: 439,
          column: 51
        }
      },
      "112": {
        start: {
          line: 442,
          column: 4
        },
        end: {
          line: 442,
          column: 25
        }
      },
      "113": {
        start: {
          line: 450,
          column: 14
        },
        end: {
          line: 450,
          column: 46
        }
      },
      "114": {
        start: {
          line: 453,
          column: 19
        },
        end: {
          line: 453,
          column: 40
        }
      },
      "115": {
        start: {
          line: 455,
          column: 4
        },
        end: {
          line: 473,
          column: 7
        }
      },
      "116": {
        start: {
          line: 456,
          column: 6
        },
        end: {
          line: 472,
          column: 7
        }
      },
      "117": {
        start: {
          line: 458,
          column: 10
        },
        end: {
          line: 458,
          column: 39
        }
      },
      "118": {
        start: {
          line: 459,
          column: 10
        },
        end: {
          line: 459,
          column: 16
        }
      },
      "119": {
        start: {
          line: 461,
          column: 10
        },
        end: {
          line: 461,
          column: 38
        }
      },
      "120": {
        start: {
          line: 462,
          column: 10
        },
        end: {
          line: 462,
          column: 16
        }
      },
      "121": {
        start: {
          line: 464,
          column: 10
        },
        end: {
          line: 464,
          column: 38
        }
      },
      "122": {
        start: {
          line: 465,
          column: 10
        },
        end: {
          line: 465,
          column: 16
        }
      },
      "123": {
        start: {
          line: 467,
          column: 10
        },
        end: {
          line: 467,
          column: 41
        }
      },
      "124": {
        start: {
          line: 468,
          column: 10
        },
        end: {
          line: 468,
          column: 16
        }
      },
      "125": {
        start: {
          line: 470,
          column: 10
        },
        end: {
          line: 470,
          column: 39
        }
      },
      "126": {
        start: {
          line: 471,
          column: 10
        },
        end: {
          line: 471,
          column: 16
        }
      },
      "127": {
        start: {
          line: 475,
          column: 4
        },
        end: {
          line: 477,
          column: 5
        }
      },
      "128": {
        start: {
          line: 476,
          column: 6
        },
        end: {
          line: 476,
          column: 37
        }
      },
      "129": {
        start: {
          line: 479,
          column: 4
        },
        end: {
          line: 479,
          column: 15
        }
      },
      "130": {
        start: {
          line: 486,
          column: 35
        },
        end: {
          line: 486,
          column: 37
        }
      },
      "131": {
        start: {
          line: 487,
          column: 25
        },
        end: {
          line: 487,
          column: 75
        }
      },
      "132": {
        start: {
          line: 490,
          column: 25
        },
        end: {
          line: 493,
          column: 18
        }
      },
      "133": {
        start: {
          line: 491,
          column: 20
        },
        end: {
          line: 491,
          column: 100
        }
      },
      "134": {
        start: {
          line: 492,
          column: 22
        },
        end: {
          line: 492,
          column: 43
        }
      },
      "135": {
        start: {
          line: 495,
          column: 4
        },
        end: {
          line: 499,
          column: 5
        }
      },
      "136": {
        start: {
          line: 496,
          column: 28
        },
        end: {
          line: 496,
          column: 83
        }
      },
      "137": {
        start: {
          line: 497,
          column: 18
        },
        end: {
          line: 497,
          column: 77
        }
      },
      "138": {
        start: {
          line: 498,
          column: 6
        },
        end: {
          line: 498,
          column: 29
        }
      },
      "139": {
        start: {
          line: 501,
          column: 4
        },
        end: {
          line: 501,
          column: 24
        }
      },
      "140": {
        start: {
          line: 509,
          column: 21
        },
        end: {
          line: 509,
          column: 59
        }
      },
      "141": {
        start: {
          line: 510,
          column: 23
        },
        end: {
          line: 510,
          column: 58
        }
      },
      "142": {
        start: {
          line: 512,
          column: 4
        },
        end: {
          line: 512,
          column: 35
        }
      },
      "143": {
        start: {
          line: 512,
          column: 21
        },
        end: {
          line: 512,
          column: 35
        }
      },
      "144": {
        start: {
          line: 514,
          column: 28
        },
        end: {
          line: 514,
          column: 51
        }
      },
      "145": {
        start: {
          line: 515,
          column: 4
        },
        end: {
          line: 515,
          column: 47
        }
      },
      "146": {
        start: {
          line: 515,
          column: 34
        },
        end: {
          line: 515,
          column: 47
        }
      },
      "147": {
        start: {
          line: 516,
          column: 4
        },
        end: {
          line: 516,
          column: 50
        }
      },
      "148": {
        start: {
          line: 516,
          column: 35
        },
        end: {
          line: 516,
          column: 50
        }
      },
      "149": {
        start: {
          line: 517,
          column: 4
        },
        end: {
          line: 517,
          column: 18
        }
      },
      "150": {
        start: {
          line: 521,
          column: 4
        },
        end: {
          line: 529,
          column: 6
        }
      },
      "151": {
        start: {
          line: 533,
          column: 4
        },
        end: {
          line: 533,
          column: 36
        }
      },
      "152": {
        start: {
          line: 533,
          column: 19
        },
        end: {
          line: 533,
          column: 36
        }
      },
      "153": {
        start: {
          line: 536,
          column: 46
        },
        end: {
          line: 545,
          column: 5
        }
      },
      "154": {
        start: {
          line: 547,
          column: 4
        },
        end: {
          line: 547,
          column: 52
        }
      },
      "155": {
        start: {
          line: 555,
          column: 4
        },
        end: {
          line: 560,
          column: 5
        }
      },
      "156": {
        start: {
          line: 557,
          column: 6
        },
        end: {
          line: 557,
          column: 83
        }
      },
      "157": {
        start: {
          line: 559,
          column: 6
        },
        end: {
          line: 559,
          column: 81
        }
      },
      "158": {
        start: {
          line: 565,
          column: 4
        },
        end: {
          line: 567,
          column: 15
        }
      },
      "159": {
        start: {
          line: 566,
          column: 6
        },
        end: {
          line: 566,
          column: 35
        }
      },
      "160": {
        start: {
          line: 571,
          column: 25
        },
        end: {
          line: 571,
          column: 75
        }
      },
      "161": {
        start: {
          line: 573,
          column: 4
        },
        end: {
          line: 582,
          column: 5
        }
      },
      "162": {
        start: {
          line: 574,
          column: 6
        },
        end: {
          line: 581,
          column: 7
        }
      },
      "163": {
        start: {
          line: 575,
          column: 24
        },
        end: {
          line: 575,
          column: 67
        }
      },
      "164": {
        start: {
          line: 576,
          column: 8
        },
        end: {
          line: 576,
          column: 86
        }
      },
      "165": {
        start: {
          line: 577,
          column: 8
        },
        end: {
          line: 577,
          column: 35
        }
      },
      "166": {
        start: {
          line: 578,
          column: 8
        },
        end: {
          line: 578,
          column: 41
        }
      },
      "167": {
        start: {
          line: 580,
          column: 8
        },
        end: {
          line: 580,
          column: 91
        }
      },
      "168": {
        start: {
          line: 587,
          column: 4
        },
        end: {
          line: 587,
          column: 57
        }
      },
      "169": {
        start: {
          line: 595,
          column: 4
        },
        end: {
          line: 600,
          column: 5
        }
      },
      "170": {
        start: {
          line: 596,
          column: 22
        },
        end: {
          line: 596,
          column: 65
        }
      },
      "171": {
        start: {
          line: 597,
          column: 6
        },
        end: {
          line: 597,
          column: 50
        }
      },
      "172": {
        start: {
          line: 599,
          column: 6
        },
        end: {
          line: 599,
          column: 57
        }
      },
      "173": {
        start: {
          line: 605,
          column: 4
        },
        end: {
          line: 610,
          column: 7
        }
      },
      "174": {
        start: {
          line: 606,
          column: 6
        },
        end: {
          line: 609,
          column: 7
        }
      },
      "175": {
        start: {
          line: 607,
          column: 38
        },
        end: {
          line: 607,
          column: 50
        }
      },
      "176": {
        start: {
          line: 608,
          column: 8
        },
        end: {
          line: 608,
          column: 93
        }
      },
      "177": {
        start: {
          line: 615,
          column: 4
        },
        end: {
          line: 618,
          column: 5
        }
      },
      "178": {
        start: {
          line: 617,
          column: 6
        },
        end: {
          line: 617,
          column: 66
        }
      },
      "179": {
        start: {
          line: 620,
          column: 4
        },
        end: {
          line: 623,
          column: 5
        }
      },
      "180": {
        start: {
          line: 622,
          column: 6
        },
        end: {
          line: 622,
          column: 65
        }
      },
      "181": {
        start: {
          line: 628,
          column: 4
        },
        end: {
          line: 628,
          column: 52
        }
      },
      "182": {
        start: {
          line: 633,
          column: 32
        },
        end: {
          line: 633,
          column: 54
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 153,
            column: 2
          },
          end: {
            line: 153,
            column: 3
          }
        },
        loc: {
          start: {
            line: 153,
            column: 16
          },
          end: {
            line: 155,
            column: 3
          }
        },
        line: 153
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 160,
            column: 2
          },
          end: {
            line: 160,
            column: 3
          }
        },
        loc: {
          start: {
            line: 160,
            column: 54
          },
          end: {
            line: 180,
            column: 3
          }
        },
        line: 160
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 163,
            column: 33
          },
          end: {
            line: 163,
            column: 34
          }
        },
        loc: {
          start: {
            line: 163,
            column: 45
          },
          end: {
            line: 165,
            column: 7
          }
        },
        line: 163
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 185,
            column: 2
          },
          end: {
            line: 185,
            column: 3
          }
        },
        loc: {
          start: {
            line: 185,
            column: 90
          },
          end: {
            line: 221,
            column: 3
          }
        },
        line: 185
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 226,
            column: 2
          },
          end: {
            line: 226,
            column: 3
          }
        },
        loc: {
          start: {
            line: 230,
            column: 19
          },
          end: {
            line: 252,
            column: 3
          }
        },
        line: 230
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 257,
            column: 2
          },
          end: {
            line: 257,
            column: 3
          }
        },
        loc: {
          start: {
            line: 270,
            column: 5
          },
          end: {
            line: 313,
            column: 3
          }
        },
        line: 270
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 281,
            column: 51
          },
          end: {
            line: 281,
            column: 52
          }
        },
        loc: {
          start: {
            line: 281,
            column: 64
          },
          end: {
            line: 281,
            column: 80
          }
        },
        line: 281
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 285,
            column: 46
          },
          end: {
            line: 285,
            column: 47
          }
        },
        loc: {
          start: {
            line: 285,
            column: 58
          },
          end: {
            line: 285,
            column: 84
          }
        },
        line: 285
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 289,
            column: 47
          },
          end: {
            line: 289,
            column: 48
          }
        },
        loc: {
          start: {
            line: 289,
            column: 60
          },
          end: {
            line: 289,
            column: 76
          }
        },
        line: 289
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 290,
            column: 51
          },
          end: {
            line: 290,
            column: 52
          }
        },
        loc: {
          start: {
            line: 290,
            column: 64
          },
          end: {
            line: 290,
            column: 84
          }
        },
        line: 290
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 318,
            column: 2
          },
          end: {
            line: 318,
            column: 3
          }
        },
        loc: {
          start: {
            line: 318,
            column: 47
          },
          end: {
            line: 343,
            column: 3
          }
        },
        line: 318
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 325,
            column: 13
          },
          end: {
            line: 325,
            column: 14
          }
        },
        loc: {
          start: {
            line: 325,
            column: 25
          },
          end: {
            line: 325,
            column: 63
          }
        },
        line: 325
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 347,
            column: 2
          },
          end: {
            line: 347,
            column: 3
          }
        },
        loc: {
          start: {
            line: 347,
            column: 59
          },
          end: {
            line: 365,
            column: 3
          }
        },
        line: 347
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 367,
            column: 2
          },
          end: {
            line: 367,
            column: 3
          }
        },
        loc: {
          start: {
            line: 367,
            column: 93
          },
          end: {
            line: 402,
            column: 3
          }
        },
        line: 367
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 373,
            column: 51
          },
          end: {
            line: 373,
            column: 52
          }
        },
        loc: {
          start: {
            line: 373,
            column: 63
          },
          end: {
            line: 397,
            column: 5
          }
        },
        line: 373
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 400,
            column: 25
          },
          end: {
            line: 400,
            column: 26
          }
        },
        loc: {
          start: {
            line: 400,
            column: 35
          },
          end: {
            line: 400,
            column: 52
          }
        },
        line: 400
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 404,
            column: 2
          },
          end: {
            line: 404,
            column: 3
          }
        },
        loc: {
          start: {
            line: 407,
            column: 23
          },
          end: {
            line: 443,
            column: 3
          }
        },
        line: 407
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 445,
            column: 2
          },
          end: {
            line: 445,
            column: 3
          }
        },
        loc: {
          start: {
            line: 449,
            column: 12
          },
          end: {
            line: 480,
            column: 3
          }
        },
        line: 449
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 455,
            column: 26
          },
          end: {
            line: 455,
            column: 27
          }
        },
        loc: {
          start: {
            line: 455,
            column: 33
          },
          end: {
            line: 473,
            column: 5
          }
        },
        line: 455
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 482,
            column: 2
          },
          end: {
            line: 482,
            column: 3
          }
        },
        loc: {
          start: {
            line: 485,
            column: 23
          },
          end: {
            line: 502,
            column: 3
          }
        },
        line: 485
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 491,
            column: 14
          },
          end: {
            line: 491,
            column: 15
          }
        },
        loc: {
          start: {
            line: 491,
            column: 20
          },
          end: {
            line: 491,
            column: 100
          }
        },
        line: 491
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 492,
            column: 12
          },
          end: {
            line: 492,
            column: 13
          }
        },
        loc: {
          start: {
            line: 492,
            column: 22
          },
          end: {
            line: 492,
            column: 43
          }
        },
        line: 492
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 504,
            column: 2
          },
          end: {
            line: 504,
            column: 3
          }
        },
        loc: {
          start: {
            line: 507,
            column: 39
          },
          end: {
            line: 518,
            column: 3
          }
        },
        line: 507
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 520,
            column: 2
          },
          end: {
            line: 520,
            column: 3
          }
        },
        loc: {
          start: {
            line: 520,
            column: 85
          },
          end: {
            line: 530,
            column: 3
          }
        },
        line: 520
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 532,
            column: 2
          },
          end: {
            line: 532,
            column: 3
          }
        },
        loc: {
          start: {
            line: 532,
            column: 89
          },
          end: {
            line: 548,
            column: 3
          }
        },
        line: 532
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 550,
            column: 2
          },
          end: {
            line: 550,
            column: 3
          }
        },
        loc: {
          start: {
            line: 554,
            column: 19
          },
          end: {
            line: 561,
            column: 3
          }
        },
        line: 554
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 563,
            column: 2
          },
          end: {
            line: 563,
            column: 3
          }
        },
        loc: {
          start: {
            line: 563,
            column: 45
          },
          end: {
            line: 568,
            column: 3
          }
        },
        line: 563
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 565,
            column: 16
          },
          end: {
            line: 565,
            column: 17
          }
        },
        loc: {
          start: {
            line: 565,
            column: 22
          },
          end: {
            line: 567,
            column: 5
          }
        },
        line: 565
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 570,
            column: 2
          },
          end: {
            line: 570,
            column: 3
          }
        },
        loc: {
          start: {
            line: 570,
            column: 55
          },
          end: {
            line: 583,
            column: 3
          }
        },
        line: 570
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 585,
            column: 2
          },
          end: {
            line: 585,
            column: 3
          }
        },
        loc: {
          start: {
            line: 585,
            column: 79
          },
          end: {
            line: 588,
            column: 3
          }
        },
        line: 585
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 590,
            column: 2
          },
          end: {
            line: 590,
            column: 3
          }
        },
        loc: {
          start: {
            line: 594,
            column: 5
          },
          end: {
            line: 601,
            column: 3
          }
        },
        line: 594
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 603,
            column: 2
          },
          end: {
            line: 603,
            column: 3
          }
        },
        loc: {
          start: {
            line: 603,
            column: 92
          },
          end: {
            line: 611,
            column: 3
          }
        },
        line: 603
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 605,
            column: 24
          },
          end: {
            line: 605,
            column: 25
          }
        },
        loc: {
          start: {
            line: 605,
            column: 34
          },
          end: {
            line: 610,
            column: 5
          }
        },
        line: 605
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 613,
            column: 2
          },
          end: {
            line: 613,
            column: 3
          }
        },
        loc: {
          start: {
            line: 613,
            column: 86
          },
          end: {
            line: 624,
            column: 3
          }
        },
        line: 613
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 626,
            column: 2
          },
          end: {
            line: 626,
            column: 3
          }
        },
        loc: {
          start: {
            line: 626,
            column: 54
          },
          end: {
            line: 629,
            column: 3
          }
        },
        line: 626
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 228,
            column: 4
          },
          end: {
            line: 228,
            column: 31
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 228,
            column: 24
          },
          end: {
            line: 228,
            column: 31
          }
        }],
        line: 228
      },
      "1": {
        loc: {
          start: {
            line: 229,
            column: 4
          },
          end: {
            line: 229,
            column: 50
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 229,
            column: 42
          },
          end: {
            line: 229,
            column: 50
          }
        }],
        line: 229
      },
      "2": {
        loc: {
          start: {
            line: 236,
            column: 28
          },
          end: {
            line: 238,
            column: 54
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 237,
            column: 14
          },
          end: {
            line: 237,
            column: 64
          }
        }, {
          start: {
            line: 238,
            column: 14
          },
          end: {
            line: 238,
            column: 54
          }
        }],
        line: 236
      },
      "3": {
        loc: {
          start: {
            line: 238,
            column: 14
          },
          end: {
            line: 238,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 238,
            column: 14
          },
          end: {
            line: 238,
            column: 48
          }
        }, {
          start: {
            line: 238,
            column: 52
          },
          end: {
            line: 238,
            column: 54
          }
        }],
        line: 238
      },
      "4": {
        loc: {
          start: {
            line: 334,
            column: 6
          },
          end: {
            line: 336,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 334,
            column: 6
          },
          end: {
            line: 336,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 334
      },
      "5": {
        loc: {
          start: {
            line: 359,
            column: 8
          },
          end: {
            line: 361,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 359,
            column: 8
          },
          end: {
            line: 361,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 359
      },
      "6": {
        loc: {
          start: {
            line: 369,
            column: 31
          },
          end: {
            line: 370,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 369,
            column: 31
          },
          end: {
            line: 369,
            column: 69
          }
        }, {
          start: {
            line: 370,
            column: 30
          },
          end: {
            line: 370,
            column: 80
          }
        }],
        line: 369
      },
      "7": {
        loc: {
          start: {
            line: 384,
            column: 6
          },
          end: {
            line: 388,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 384,
            column: 6
          },
          end: {
            line: 388,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 384
      },
      "8": {
        loc: {
          start: {
            line: 385,
            column: 8
          },
          end: {
            line: 385,
            column: 93
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 385,
            column: 8
          },
          end: {
            line: 385,
            column: 93
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 385
      },
      "9": {
        loc: {
          start: {
            line: 385,
            column: 12
          },
          end: {
            line: 385,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 385,
            column: 12
          },
          end: {
            line: 385,
            column: 36
          }
        }, {
          start: {
            line: 385,
            column: 40
          },
          end: {
            line: 385,
            column: 79
          }
        }],
        line: 385
      },
      "10": {
        loc: {
          start: {
            line: 386,
            column: 8
          },
          end: {
            line: 386,
            column: 90
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 386,
            column: 8
          },
          end: {
            line: 386,
            column: 90
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 386
      },
      "11": {
        loc: {
          start: {
            line: 386,
            column: 12
          },
          end: {
            line: 386,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 386,
            column: 12
          },
          end: {
            line: 386,
            column: 36
          }
        }, {
          start: {
            line: 386,
            column: 40
          },
          end: {
            line: 386,
            column: 76
          }
        }],
        line: 386
      },
      "12": {
        loc: {
          start: {
            line: 387,
            column: 8
          },
          end: {
            line: 387,
            column: 93
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 387,
            column: 8
          },
          end: {
            line: 387,
            column: 93
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 387
      },
      "13": {
        loc: {
          start: {
            line: 387,
            column: 12
          },
          end: {
            line: 387,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 387,
            column: 12
          },
          end: {
            line: 387,
            column: 39
          }
        }, {
          start: {
            line: 387,
            column: 43
          },
          end: {
            line: 387,
            column: 79
          }
        }],
        line: 387
      },
      "14": {
        loc: {
          start: {
            line: 392,
            column: 6
          },
          end: {
            line: 394,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 392,
            column: 6
          },
          end: {
            line: 394,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 392
      },
      "15": {
        loc: {
          start: {
            line: 401,
            column: 11
          },
          end: {
            line: 401,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 401,
            column: 11
          },
          end: {
            line: 401,
            column: 39
          }
        }, {
          start: {
            line: 401,
            column: 43
          },
          end: {
            line: 401,
            column: 64
          }
        }],
        line: 401
      },
      "16": {
        loc: {
          start: {
            line: 411,
            column: 4
          },
          end: {
            line: 411,
            column: 40
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 411,
            column: 4
          },
          end: {
            line: 411,
            column: 40
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 411
      },
      "17": {
        loc: {
          start: {
            line: 414,
            column: 4
          },
          end: {
            line: 418,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 414,
            column: 4
          },
          end: {
            line: 418,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 414
      },
      "18": {
        loc: {
          start: {
            line: 414,
            column: 8
          },
          end: {
            line: 414,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 414,
            column: 8
          },
          end: {
            line: 414,
            column: 32
          }
        }, {
          start: {
            line: 414,
            column: 36
          },
          end: {
            line: 414,
            column: 75
          }
        }],
        line: 414
      },
      "19": {
        loc: {
          start: {
            line: 421,
            column: 4
          },
          end: {
            line: 424,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 421,
            column: 4
          },
          end: {
            line: 424,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 421
      },
      "20": {
        loc: {
          start: {
            line: 421,
            column: 8
          },
          end: {
            line: 421,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 421,
            column: 8
          },
          end: {
            line: 421,
            column: 32
          }
        }, {
          start: {
            line: 421,
            column: 36
          },
          end: {
            line: 421,
            column: 72
          }
        }],
        line: 421
      },
      "21": {
        loc: {
          start: {
            line: 427,
            column: 4
          },
          end: {
            line: 435,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 427,
            column: 4
          },
          end: {
            line: 435,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 427
      },
      "22": {
        loc: {
          start: {
            line: 428,
            column: 6
          },
          end: {
            line: 431,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 428,
            column: 6
          },
          end: {
            line: 431,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 428
      },
      "23": {
        loc: {
          start: {
            line: 432,
            column: 6
          },
          end: {
            line: 434,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 432,
            column: 6
          },
          end: {
            line: 434,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 432
      },
      "24": {
        loc: {
          start: {
            line: 438,
            column: 4
          },
          end: {
            line: 440,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 438,
            column: 4
          },
          end: {
            line: 440,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 438
      },
      "25": {
        loc: {
          start: {
            line: 456,
            column: 6
          },
          end: {
            line: 472,
            column: 7
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 457,
            column: 8
          },
          end: {
            line: 459,
            column: 16
          }
        }, {
          start: {
            line: 460,
            column: 8
          },
          end: {
            line: 462,
            column: 16
          }
        }, {
          start: {
            line: 463,
            column: 8
          },
          end: {
            line: 465,
            column: 16
          }
        }, {
          start: {
            line: 466,
            column: 8
          },
          end: {
            line: 468,
            column: 16
          }
        }, {
          start: {
            line: 469,
            column: 8
          },
          end: {
            line: 471,
            column: 16
          }
        }],
        line: 456
      },
      "26": {
        loc: {
          start: {
            line: 475,
            column: 4
          },
          end: {
            line: 477,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 475,
            column: 4
          },
          end: {
            line: 477,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 475
      },
      "27": {
        loc: {
          start: {
            line: 491,
            column: 20
          },
          end: {
            line: 491,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 491,
            column: 20
          },
          end: {
            line: 491,
            column: 60
          }
        }, {
          start: {
            line: 491,
            column: 64
          },
          end: {
            line: 491,
            column: 100
          }
        }],
        line: 491
      },
      "28": {
        loc: {
          start: {
            line: 512,
            column: 4
          },
          end: {
            line: 512,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 512,
            column: 4
          },
          end: {
            line: 512,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 512
      },
      "29": {
        loc: {
          start: {
            line: 515,
            column: 4
          },
          end: {
            line: 515,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 515,
            column: 4
          },
          end: {
            line: 515,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 515
      },
      "30": {
        loc: {
          start: {
            line: 516,
            column: 4
          },
          end: {
            line: 516,
            column: 50
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 516,
            column: 4
          },
          end: {
            line: 516,
            column: 50
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 516
      },
      "31": {
        loc: {
          start: {
            line: 533,
            column: 4
          },
          end: {
            line: 533,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 533,
            column: 4
          },
          end: {
            line: 533,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 533
      },
      "32": {
        loc: {
          start: {
            line: 547,
            column: 11
          },
          end: {
            line: 547,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 547,
            column: 11
          },
          end: {
            line: 547,
            column: 38
          }
        }, {
          start: {
            line: 547,
            column: 42
          },
          end: {
            line: 547,
            column: 51
          }
        }],
        line: 547
      },
      "33": {
        loc: {
          start: {
            line: 606,
            column: 6
          },
          end: {
            line: 609,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 606,
            column: 6
          },
          end: {
            line: 609,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 606
      },
      "34": {
        loc: {
          start: {
            line: 606,
            column: 10
          },
          end: {
            line: 606,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 606,
            column: 10
          },
          end: {
            line: 606,
            column: 39
          }
        }, {
          start: {
            line: 606,
            column: 43
          },
          end: {
            line: 606,
            column: 63
          }
        }],
        line: 606
      },
      "35": {
        loc: {
          start: {
            line: 615,
            column: 4
          },
          end: {
            line: 618,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 615,
            column: 4
          },
          end: {
            line: 618,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 615
      },
      "36": {
        loc: {
          start: {
            line: 620,
            column: 4
          },
          end: {
            line: 623,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 620,
            column: 4
          },
          end: {
            line: 623,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 620
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0, 0, 0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d4a4e2a5de14f4183cd8bce59086205260a2cddf"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_4hnbjnpn2 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_4hnbjnpn2();
import { performanceMonitor } from "../../utils/performance";
import { smartResourceManager } from "../ai/SmartResourceManager";
var GlobalCDNManager = function () {
  function GlobalCDNManager() {
    _classCallCheck(this, GlobalCDNManager);
    this.cdnProviders = (cov_4hnbjnpn2().s[0]++, new Map());
    this.regionalEndpoints = (cov_4hnbjnpn2().s[1]++, new Map());
    this.performanceCache = (cov_4hnbjnpn2().s[2]++, new Map());
    this.routingTable = (cov_4hnbjnpn2().s[3]++, new Map());
    this.CDN_PROVIDERS = (cov_4hnbjnpn2().s[4]++, [{
      id: 'cloudflare',
      name: 'Cloudflare',
      baseUrl: 'https://cdn.acemind.app',
      regions: ['us-east', 'us-west', 'eu-west', 'eu-central', 'asia-pacific', 'asia-southeast'],
      capabilities: {
        imageOptimization: true,
        videoStreaming: true,
        edgeFunctions: true,
        realTimeAnalytics: true,
        ddosProtection: true
      },
      pricing: {
        bandwidth: 0.085,
        requests: 0.50,
        storage: 0.015
      },
      performance: {
        averageLatency: 45,
        uptime: 99.99,
        throughput: 100
      }
    }, {
      id: 'aws-cloudfront',
      name: 'AWS CloudFront',
      baseUrl: 'https://d1234567890.cloudfront.net',
      regions: ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1', 'ap-northeast-1'],
      capabilities: {
        imageOptimization: true,
        videoStreaming: true,
        edgeFunctions: true,
        realTimeAnalytics: true,
        ddosProtection: true
      },
      pricing: {
        bandwidth: 0.085,
        requests: 0.75,
        storage: 0.023
      },
      performance: {
        averageLatency: 55,
        uptime: 99.95,
        throughput: 80
      }
    }, {
      id: 'fastly',
      name: 'Fastly',
      baseUrl: 'https://acemind.global.ssl.fastly.net',
      regions: ['us-east', 'us-west', 'eu-west', 'asia-pacific'],
      capabilities: {
        imageOptimization: true,
        videoStreaming: false,
        edgeFunctions: true,
        realTimeAnalytics: true,
        ddosProtection: true
      },
      pricing: {
        bandwidth: 0.12,
        requests: 0.40,
        storage: 0.020
      },
      performance: {
        averageLatency: 40,
        uptime: 99.98,
        throughput: 60
      }
    }]);
    cov_4hnbjnpn2().f[0]++;
    cov_4hnbjnpn2().s[5]++;
    this.initializeCDNManager();
  }
  return _createClass(GlobalCDNManager, [{
    key: "initializeCDNManager",
    value: (function () {
      var _initializeCDNManager = _asyncToGenerator(function* () {
        var _this = this;
        cov_4hnbjnpn2().f[1]++;
        cov_4hnbjnpn2().s[6]++;
        try {
          cov_4hnbjnpn2().s[7]++;
          this.CDN_PROVIDERS.forEach(function (provider) {
            cov_4hnbjnpn2().f[2]++;
            cov_4hnbjnpn2().s[8]++;
            _this.cdnProviders.set(provider.id, provider);
          });
          cov_4hnbjnpn2().s[9]++;
          yield this.discoverRegionalEndpoints();
          cov_4hnbjnpn2().s[10]++;
          this.startPerformanceMonitoring();
          cov_4hnbjnpn2().s[11]++;
          yield this.optimizeRoutingTable();
          cov_4hnbjnpn2().s[12]++;
          console.log('Global CDN Manager initialized successfully');
        } catch (error) {
          cov_4hnbjnpn2().s[13]++;
          console.error('Failed to initialize Global CDN Manager:', error);
        }
      });
      function initializeCDNManager() {
        return _initializeCDNManager.apply(this, arguments);
      }
      return initializeCDNManager;
    }())
  }, {
    key: "getOptimizedDeliveryUrl",
    value: (function () {
      var _getOptimizedDeliveryUrl = _asyncToGenerator(function* (request) {
        cov_4hnbjnpn2().f[3]++;
        cov_4hnbjnpn2().s[14]++;
        try {
          var startTime = (cov_4hnbjnpn2().s[15]++, Date.now());
          var optimalEndpoint = (cov_4hnbjnpn2().s[16]++, yield this.selectOptimalEndpoint(request));
          var optimizations = (cov_4hnbjnpn2().s[17]++, yield this.applyContentOptimizations(request, optimalEndpoint));
          var optimizedUrl = (cov_4hnbjnpn2().s[18]++, this.generateOptimizedUrl(request, optimalEndpoint, optimizations));
          var fallbackUrls = (cov_4hnbjnpn2().s[19]++, yield this.generateFallbackUrls(request, optimalEndpoint));
          var result = (cov_4hnbjnpn2().s[20]++, {
            url: optimizedUrl,
            provider: optimalEndpoint.provider,
            region: optimalEndpoint.region,
            estimatedLatency: optimalEndpoint.latency,
            cacheStatus: yield this.predictCacheStatus(request, optimalEndpoint),
            optimizations: optimizations,
            fallbackUrls: fallbackUrls
          });
          var processingTime = (cov_4hnbjnpn2().s[21]++, Date.now() - startTime);
          cov_4hnbjnpn2().s[22]++;
          performanceMonitor.trackDatabaseQuery('cdn_optimization', processingTime);
          cov_4hnbjnpn2().s[23]++;
          return result;
        } catch (error) {
          cov_4hnbjnpn2().s[24]++;
          console.error('Failed to get optimized delivery URL:', error);
          cov_4hnbjnpn2().s[25]++;
          return this.getFallbackDeliveryResult(request);
        }
      });
      function getOptimizedDeliveryUrl(_x) {
        return _getOptimizedDeliveryUrl.apply(this, arguments);
      }
      return getOptimizedDeliveryUrl;
    }())
  }, {
    key: "preloadContentGlobally",
    value: (function () {
      var _preloadContentGlobally = _asyncToGenerator(function* (paths) {
        var regions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_4hnbjnpn2().b[0][0]++, ['all']);
        var priority = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_4hnbjnpn2().b[1][0]++, 'medium');
        cov_4hnbjnpn2().f[4]++;
        cov_4hnbjnpn2().s[26]++;
        try {
          var preloadPromises = (cov_4hnbjnpn2().s[27]++, []);
          cov_4hnbjnpn2().s[28]++;
          for (var path of paths) {
            cov_4hnbjnpn2().s[29]++;
            for (var region of regions) {
              var endpoints = (cov_4hnbjnpn2().s[30]++, regions.includes('all') ? (cov_4hnbjnpn2().b[2][0]++, Array.from(this.regionalEndpoints.values()).flat()) : (cov_4hnbjnpn2().b[2][1]++, (cov_4hnbjnpn2().b[3][0]++, this.regionalEndpoints.get(region)) || (cov_4hnbjnpn2().b[3][1]++, [])));
              cov_4hnbjnpn2().s[31]++;
              for (var endpoint of endpoints) {
                cov_4hnbjnpn2().s[32]++;
                preloadPromises.push(this.preloadToEndpoint(path, endpoint, priority));
              }
            }
          }
          cov_4hnbjnpn2().s[33]++;
          yield Promise.allSettled(preloadPromises);
          cov_4hnbjnpn2().s[34]++;
          console.log(`Preloaded ${paths.length} assets to ${preloadPromises.length} endpoints`);
        } catch (error) {
          cov_4hnbjnpn2().s[35]++;
          console.error('Failed to preload content globally:', error);
        }
      });
      function preloadContentGlobally(_x2) {
        return _preloadContentGlobally.apply(this, arguments);
      }
      return preloadContentGlobally;
    }())
  }, {
    key: "getGlobalPerformanceMetrics",
    value: (function () {
      var _getGlobalPerformanceMetrics = _asyncToGenerator(function* () {
        cov_4hnbjnpn2().f[5]++;
        var metrics = (cov_4hnbjnpn2().s[36]++, {
          averageLatency: 0,
          globalUptime: 0,
          regionalPerformance: {},
          cdnComparison: {}
        });
        cov_4hnbjnpn2().s[37]++;
        try {
          var allEndpoints = (cov_4hnbjnpn2().s[38]++, Array.from(this.regionalEndpoints.values()).flat());
          cov_4hnbjnpn2().s[39]++;
          metrics.averageLatency = allEndpoints.reduce(function (sum, ep) {
            cov_4hnbjnpn2().f[6]++;
            cov_4hnbjnpn2().s[40]++;
            return sum + ep.latency;
          }, 0) / allEndpoints.length;
          var providers = (cov_4hnbjnpn2().s[41]++, Array.from(this.cdnProviders.values()));
          cov_4hnbjnpn2().s[42]++;
          metrics.globalUptime = providers.reduce(function (sum, p) {
            cov_4hnbjnpn2().f[7]++;
            cov_4hnbjnpn2().s[43]++;
            return sum + p.performance.uptime;
          }, 0) / providers.length;
          cov_4hnbjnpn2().s[44]++;
          for (var _ref of this.regionalEndpoints.entries()) {
            var _ref2 = _slicedToArray(_ref, 2);
            var region = _ref2[0];
            var endpoints = _ref2[1];
            var regionLatency = (cov_4hnbjnpn2().s[45]++, endpoints.reduce(function (sum, ep) {
              cov_4hnbjnpn2().f[8]++;
              cov_4hnbjnpn2().s[46]++;
              return sum + ep.latency;
            }, 0) / endpoints.length);
            var regionReliability = (cov_4hnbjnpn2().s[47]++, endpoints.reduce(function (sum, ep) {
              cov_4hnbjnpn2().f[9]++;
              cov_4hnbjnpn2().s[48]++;
              return sum + ep.reliability;
            }, 0) / endpoints.length);
            cov_4hnbjnpn2().s[49]++;
            metrics.regionalPerformance[region] = {
              latency: regionLatency,
              uptime: regionReliability * 100,
              throughput: 100
            };
          }
          cov_4hnbjnpn2().s[50]++;
          for (var _ref3 of this.cdnProviders.entries()) {
            var _ref4 = _slicedToArray(_ref3, 2);
            var id = _ref4[0];
            var provider = _ref4[1];
            cov_4hnbjnpn2().s[51]++;
            metrics.cdnComparison[id] = {
              performance: 100 - provider.performance.averageLatency,
              cost: 100 - provider.pricing.bandwidth * 100,
              reliability: provider.performance.uptime
            };
          }
          cov_4hnbjnpn2().s[52]++;
          return metrics;
        } catch (error) {
          cov_4hnbjnpn2().s[53]++;
          console.error('Failed to get global performance metrics:', error);
          cov_4hnbjnpn2().s[54]++;
          return metrics;
        }
      });
      function getGlobalPerformanceMetrics() {
        return _getGlobalPerformanceMetrics.apply(this, arguments);
      }
      return getGlobalPerformanceMetrics;
    }())
  }, {
    key: "optimizeGlobalRouting",
    value: (function () {
      var _optimizeGlobalRouting = _asyncToGenerator(function* () {
        var _this2 = this;
        cov_4hnbjnpn2().f[10]++;
        cov_4hnbjnpn2().s[55]++;
        try {
          cov_4hnbjnpn2().s[56]++;
          console.log('Optimizing global CDN routing...');
          var testPromises = (cov_4hnbjnpn2().s[57]++, Array.from(this.regionalEndpoints.values()).flat().map(function (endpoint) {
            cov_4hnbjnpn2().f[11]++;
            cov_4hnbjnpn2().s[58]++;
            return _this2.testEndpointPerformance(endpoint);
          }));
          var testResults = (cov_4hnbjnpn2().s[59]++, yield Promise.allSettled(testPromises));
          cov_4hnbjnpn2().s[60]++;
          yield this.updateRoutingTable(testResults);
          var resourceMetrics = (cov_4hnbjnpn2().s[61]++, smartResourceManager.getCurrentMetrics());
          cov_4hnbjnpn2().s[62]++;
          if (resourceMetrics) {
            cov_4hnbjnpn2().b[4][0]++;
            cov_4hnbjnpn2().s[63]++;
            yield this.adaptRoutingToResourceConditions(resourceMetrics);
          } else {
            cov_4hnbjnpn2().b[4][1]++;
          }
          cov_4hnbjnpn2().s[64]++;
          console.log('Global CDN routing optimization completed');
        } catch (error) {
          cov_4hnbjnpn2().s[65]++;
          console.error('Failed to optimize global routing:', error);
        }
      });
      function optimizeGlobalRouting() {
        return _optimizeGlobalRouting.apply(this, arguments);
      }
      return optimizeGlobalRouting;
    }())
  }, {
    key: "discoverRegionalEndpoints",
    value: function () {
      var _discoverRegionalEndpoints = _asyncToGenerator(function* () {
        cov_4hnbjnpn2().f[12]++;
        cov_4hnbjnpn2().s[66]++;
        for (var provider of this.CDN_PROVIDERS) {
          cov_4hnbjnpn2().s[67]++;
          for (var region of provider.regions) {
            var endpoint = (cov_4hnbjnpn2().s[68]++, {
              url: `${provider.baseUrl}/${region}`,
              provider: provider.id,
              region: region,
              latency: provider.performance.averageLatency + Math.random() * 20,
              reliability: provider.performance.uptime / 100,
              lastTested: Date.now()
            });
            cov_4hnbjnpn2().s[69]++;
            if (!this.regionalEndpoints.has(region)) {
              cov_4hnbjnpn2().b[5][0]++;
              cov_4hnbjnpn2().s[70]++;
              this.regionalEndpoints.set(region, []);
            } else {
              cov_4hnbjnpn2().b[5][1]++;
            }
            cov_4hnbjnpn2().s[71]++;
            this.regionalEndpoints.get(region).push(endpoint);
          }
        }
      });
      function discoverRegionalEndpoints() {
        return _discoverRegionalEndpoints.apply(this, arguments);
      }
      return discoverRegionalEndpoints;
    }()
  }, {
    key: "selectOptimalEndpoint",
    value: function () {
      var _selectOptimalEndpoint = _asyncToGenerator(function* (request) {
        var _this3 = this,
          _scoredEndpoints$;
        cov_4hnbjnpn2().f[13]++;
        var userRegion = (cov_4hnbjnpn2().s[72]++, this.determineUserRegion(request.userLocation));
        var availableEndpoints = (cov_4hnbjnpn2().s[73]++, (cov_4hnbjnpn2().b[6][0]++, this.regionalEndpoints.get(userRegion)) || (cov_4hnbjnpn2().b[6][1]++, Array.from(this.regionalEndpoints.values()).flat()));
        var scoredEndpoints = (cov_4hnbjnpn2().s[74]++, availableEndpoints.map(function (endpoint) {
          cov_4hnbjnpn2().f[14]++;
          var score = (cov_4hnbjnpn2().s[75]++, 0);
          cov_4hnbjnpn2().s[76]++;
          score += (200 - endpoint.latency) / 200 * 40;
          cov_4hnbjnpn2().s[77]++;
          score += endpoint.reliability * 30;
          var provider = (cov_4hnbjnpn2().s[78]++, _this3.cdnProviders.get(endpoint.provider));
          cov_4hnbjnpn2().s[79]++;
          if (provider) {
            cov_4hnbjnpn2().b[7][0]++;
            cov_4hnbjnpn2().s[80]++;
            if ((cov_4hnbjnpn2().b[9][0]++, request.type === 'image') && (cov_4hnbjnpn2().b[9][1]++, provider.capabilities.imageOptimization)) {
              cov_4hnbjnpn2().b[8][0]++;
              cov_4hnbjnpn2().s[81]++;
              score += 15;
            } else {
              cov_4hnbjnpn2().b[8][1]++;
            }
            cov_4hnbjnpn2().s[82]++;
            if ((cov_4hnbjnpn2().b[11][0]++, request.type === 'video') && (cov_4hnbjnpn2().b[11][1]++, provider.capabilities.videoStreaming)) {
              cov_4hnbjnpn2().b[10][0]++;
              cov_4hnbjnpn2().s[83]++;
              score += 15;
            } else {
              cov_4hnbjnpn2().b[10][1]++;
            }
            cov_4hnbjnpn2().s[84]++;
            if ((cov_4hnbjnpn2().b[13][0]++, request.priority === 'high') && (cov_4hnbjnpn2().b[13][1]++, provider.capabilities.ddosProtection)) {
              cov_4hnbjnpn2().b[12][0]++;
              cov_4hnbjnpn2().s[85]++;
              score += 10;
            } else {
              cov_4hnbjnpn2().b[12][1]++;
            }
          } else {
            cov_4hnbjnpn2().b[7][1]++;
          }
          var recentPerf = (cov_4hnbjnpn2().s[86]++, _this3.performanceCache.get(`${endpoint.provider}_${endpoint.region}`));
          cov_4hnbjnpn2().s[87]++;
          if (recentPerf) {
            cov_4hnbjnpn2().b[14][0]++;
            cov_4hnbjnpn2().s[88]++;
            score += (200 - recentPerf) / 200 * 5;
          } else {
            cov_4hnbjnpn2().b[14][1]++;
          }
          cov_4hnbjnpn2().s[89]++;
          return {
            endpoint: endpoint,
            score: score
          };
        }));
        cov_4hnbjnpn2().s[90]++;
        scoredEndpoints.sort(function (a, b) {
          cov_4hnbjnpn2().f[15]++;
          cov_4hnbjnpn2().s[91]++;
          return b.score - a.score;
        });
        cov_4hnbjnpn2().s[92]++;
        return (cov_4hnbjnpn2().b[15][0]++, (_scoredEndpoints$ = scoredEndpoints[0]) == null ? void 0 : _scoredEndpoints$.endpoint) || (cov_4hnbjnpn2().b[15][1]++, availableEndpoints[0]);
      });
      function selectOptimalEndpoint(_x3) {
        return _selectOptimalEndpoint.apply(this, arguments);
      }
      return selectOptimalEndpoint;
    }()
  }, {
    key: "applyContentOptimizations",
    value: function () {
      var _applyContentOptimizations = _asyncToGenerator(function* (request, endpoint) {
        cov_4hnbjnpn2().f[16]++;
        var optimizations = (cov_4hnbjnpn2().s[93]++, []);
        var provider = (cov_4hnbjnpn2().s[94]++, this.cdnProviders.get(endpoint.provider));
        cov_4hnbjnpn2().s[95]++;
        if (!provider) {
          cov_4hnbjnpn2().b[16][0]++;
          cov_4hnbjnpn2().s[96]++;
          return optimizations;
        } else {
          cov_4hnbjnpn2().b[16][1]++;
        }
        cov_4hnbjnpn2().s[97]++;
        if ((cov_4hnbjnpn2().b[18][0]++, request.type === 'image') && (cov_4hnbjnpn2().b[18][1]++, provider.capabilities.imageOptimization)) {
          cov_4hnbjnpn2().b[17][0]++;
          cov_4hnbjnpn2().s[98]++;
          optimizations.push('webp_conversion');
          cov_4hnbjnpn2().s[99]++;
          optimizations.push('responsive_sizing');
          cov_4hnbjnpn2().s[100]++;
          optimizations.push('quality_optimization');
        } else {
          cov_4hnbjnpn2().b[17][1]++;
        }
        cov_4hnbjnpn2().s[101]++;
        if ((cov_4hnbjnpn2().b[20][0]++, request.type === 'video') && (cov_4hnbjnpn2().b[20][1]++, provider.capabilities.videoStreaming)) {
          cov_4hnbjnpn2().b[19][0]++;
          cov_4hnbjnpn2().s[102]++;
          optimizations.push('adaptive_bitrate');
          cov_4hnbjnpn2().s[103]++;
          optimizations.push('format_optimization');
        } else {
          cov_4hnbjnpn2().b[19][1]++;
        }
        cov_4hnbjnpn2().s[104]++;
        if (request.deviceInfo) {
          cov_4hnbjnpn2().b[21][0]++;
          cov_4hnbjnpn2().s[105]++;
          if (request.deviceInfo.connection === 'slow') {
            cov_4hnbjnpn2().b[22][0]++;
            cov_4hnbjnpn2().s[106]++;
            optimizations.push('compression_boost');
            cov_4hnbjnpn2().s[107]++;
            optimizations.push('quality_reduction');
          } else {
            cov_4hnbjnpn2().b[22][1]++;
          }
          cov_4hnbjnpn2().s[108]++;
          if (request.deviceInfo.type === 'mobile') {
            cov_4hnbjnpn2().b[23][0]++;
            cov_4hnbjnpn2().s[109]++;
            optimizations.push('mobile_optimization');
          } else {
            cov_4hnbjnpn2().b[23][1]++;
          }
        } else {
          cov_4hnbjnpn2().b[21][1]++;
        }
        cov_4hnbjnpn2().s[110]++;
        if (request.cacheStrategy === 'aggressive') {
          cov_4hnbjnpn2().b[24][0]++;
          cov_4hnbjnpn2().s[111]++;
          optimizations.push('extended_cache_headers');
        } else {
          cov_4hnbjnpn2().b[24][1]++;
        }
        cov_4hnbjnpn2().s[112]++;
        return optimizations;
      });
      function applyContentOptimizations(_x4, _x5) {
        return _applyContentOptimizations.apply(this, arguments);
      }
      return applyContentOptimizations;
    }()
  }, {
    key: "generateOptimizedUrl",
    value: function generateOptimizedUrl(request, endpoint, optimizations) {
      cov_4hnbjnpn2().f[17]++;
      var url = (cov_4hnbjnpn2().s[113]++, `${endpoint.url}${request.path}`);
      var params = (cov_4hnbjnpn2().s[114]++, new URLSearchParams());
      cov_4hnbjnpn2().s[115]++;
      optimizations.forEach(function (opt) {
        cov_4hnbjnpn2().f[18]++;
        cov_4hnbjnpn2().s[116]++;
        switch (opt) {
          case 'webp_conversion':
            cov_4hnbjnpn2().b[25][0]++;
            cov_4hnbjnpn2().s[117]++;
            params.set('format', 'webp');
            cov_4hnbjnpn2().s[118]++;
            break;
          case 'responsive_sizing':
            cov_4hnbjnpn2().b[25][1]++;
            cov_4hnbjnpn2().s[119]++;
            params.set('width', 'auto');
            cov_4hnbjnpn2().s[120]++;
            break;
          case 'quality_optimization':
            cov_4hnbjnpn2().b[25][2]++;
            cov_4hnbjnpn2().s[121]++;
            params.set('quality', '85');
            cov_4hnbjnpn2().s[122]++;
            break;
          case 'compression_boost':
            cov_4hnbjnpn2().b[25][3]++;
            cov_4hnbjnpn2().s[123]++;
            params.set('compress', 'true');
            cov_4hnbjnpn2().s[124]++;
            break;
          case 'mobile_optimization':
            cov_4hnbjnpn2().b[25][4]++;
            cov_4hnbjnpn2().s[125]++;
            params.set('mobile', 'true');
            cov_4hnbjnpn2().s[126]++;
            break;
        }
      });
      cov_4hnbjnpn2().s[127]++;
      if (params.toString()) {
        cov_4hnbjnpn2().b[26][0]++;
        cov_4hnbjnpn2().s[128]++;
        url += `?${params.toString()}`;
      } else {
        cov_4hnbjnpn2().b[26][1]++;
      }
      cov_4hnbjnpn2().s[129]++;
      return url;
    }
  }, {
    key: "generateFallbackUrls",
    value: function () {
      var _generateFallbackUrls = _asyncToGenerator(function* (request, primaryEndpoint) {
        cov_4hnbjnpn2().f[19]++;
        var fallbackUrls = (cov_4hnbjnpn2().s[130]++, []);
        var allEndpoints = (cov_4hnbjnpn2().s[131]++, Array.from(this.regionalEndpoints.values()).flat());
        var alternatives = (cov_4hnbjnpn2().s[132]++, allEndpoints.filter(function (ep) {
          cov_4hnbjnpn2().f[20]++;
          cov_4hnbjnpn2().s[133]++;
          return (cov_4hnbjnpn2().b[27][0]++, ep.provider !== primaryEndpoint.provider) || (cov_4hnbjnpn2().b[27][1]++, ep.region !== primaryEndpoint.region);
        }).sort(function (a, b) {
          cov_4hnbjnpn2().f[21]++;
          cov_4hnbjnpn2().s[134]++;
          return a.latency - b.latency;
        }).slice(0, 3));
        cov_4hnbjnpn2().s[135]++;
        for (var endpoint of alternatives) {
          var optimizations = (cov_4hnbjnpn2().s[136]++, yield this.applyContentOptimizations(request, endpoint));
          var url = (cov_4hnbjnpn2().s[137]++, this.generateOptimizedUrl(request, endpoint, optimizations));
          cov_4hnbjnpn2().s[138]++;
          fallbackUrls.push(url);
        }
        cov_4hnbjnpn2().s[139]++;
        return fallbackUrls;
      });
      function generateFallbackUrls(_x6, _x7) {
        return _generateFallbackUrls.apply(this, arguments);
      }
      return generateFallbackUrls;
    }()
  }, {
    key: "predictCacheStatus",
    value: function () {
      var _predictCacheStatus = _asyncToGenerator(function* (request, endpoint) {
        cov_4hnbjnpn2().f[22]++;
        var cacheKey = (cov_4hnbjnpn2().s[140]++, `${endpoint.provider}_${request.path}`);
        var lastAccess = (cov_4hnbjnpn2().s[141]++, this.performanceCache.get(cacheKey));
        cov_4hnbjnpn2().s[142]++;
        if (!lastAccess) {
          cov_4hnbjnpn2().b[28][0]++;
          cov_4hnbjnpn2().s[143]++;
          return 'miss';
        } else {
          cov_4hnbjnpn2().b[28][1]++;
        }
        var timeSinceAccess = (cov_4hnbjnpn2().s[144]++, Date.now() - lastAccess);
        cov_4hnbjnpn2().s[145]++;
        if (timeSinceAccess < 300000) {
          cov_4hnbjnpn2().b[29][0]++;
          cov_4hnbjnpn2().s[146]++;
          return 'hit';
        } else {
          cov_4hnbjnpn2().b[29][1]++;
        }
        cov_4hnbjnpn2().s[147]++;
        if (timeSinceAccess < 3600000) {
          cov_4hnbjnpn2().b[30][0]++;
          cov_4hnbjnpn2().s[148]++;
          return 'stale';
        } else {
          cov_4hnbjnpn2().b[30][1]++;
        }
        cov_4hnbjnpn2().s[149]++;
        return 'miss';
      });
      function predictCacheStatus(_x8, _x9) {
        return _predictCacheStatus.apply(this, arguments);
      }
      return predictCacheStatus;
    }()
  }, {
    key: "getFallbackDeliveryResult",
    value: function getFallbackDeliveryResult(request) {
      cov_4hnbjnpn2().f[23]++;
      cov_4hnbjnpn2().s[150]++;
      return {
        url: `https://fallback.acemind.app${request.path}`,
        provider: 'fallback',
        region: 'global',
        estimatedLatency: 200,
        cacheStatus: 'miss',
        optimizations: [],
        fallbackUrls: []
      };
    }
  }, {
    key: "determineUserRegion",
    value: function determineUserRegion(location) {
      cov_4hnbjnpn2().f[24]++;
      cov_4hnbjnpn2().s[151]++;
      if (!location) {
        cov_4hnbjnpn2().b[31][0]++;
        cov_4hnbjnpn2().s[152]++;
        return 'us-east';
      } else {
        cov_4hnbjnpn2().b[31][1]++;
      }
      var regionMap = (cov_4hnbjnpn2().s[153]++, {
        'US': 'us-east',
        'CA': 'us-east',
        'GB': 'eu-west',
        'DE': 'eu-central',
        'FR': 'eu-west',
        'JP': 'asia-pacific',
        'AU': 'asia-pacific',
        'SG': 'asia-southeast'
      });
      cov_4hnbjnpn2().s[154]++;
      return (cov_4hnbjnpn2().b[32][0]++, regionMap[location.country]) || (cov_4hnbjnpn2().b[32][1]++, 'us-east');
    }
  }, {
    key: "preloadToEndpoint",
    value: function () {
      var _preloadToEndpoint = _asyncToGenerator(function* (path, endpoint, priority) {
        cov_4hnbjnpn2().f[25]++;
        cov_4hnbjnpn2().s[155]++;
        try {
          cov_4hnbjnpn2().s[156]++;
          console.log(`Preloading ${path} to ${endpoint.provider}/${endpoint.region}`);
        } catch (error) {
          cov_4hnbjnpn2().s[157]++;
          console.error(`Failed to preload ${path} to ${endpoint.provider}:`, error);
        }
      });
      function preloadToEndpoint(_x0, _x1, _x10) {
        return _preloadToEndpoint.apply(this, arguments);
      }
      return preloadToEndpoint;
    }()
  }, {
    key: "startPerformanceMonitoring",
    value: function startPerformanceMonitoring() {
      var _this4 = this;
      cov_4hnbjnpn2().f[26]++;
      cov_4hnbjnpn2().s[158]++;
      setInterval(function () {
        cov_4hnbjnpn2().f[27]++;
        cov_4hnbjnpn2().s[159]++;
        _this4.monitorCDNPerformance();
      }, 300000);
    }
  }, {
    key: "monitorCDNPerformance",
    value: function () {
      var _monitorCDNPerformance = _asyncToGenerator(function* () {
        cov_4hnbjnpn2().f[28]++;
        var allEndpoints = (cov_4hnbjnpn2().s[160]++, Array.from(this.regionalEndpoints.values()).flat());
        cov_4hnbjnpn2().s[161]++;
        for (var endpoint of allEndpoints) {
          cov_4hnbjnpn2().s[162]++;
          try {
            var latency = (cov_4hnbjnpn2().s[163]++, yield this.measureEndpointLatency(endpoint));
            cov_4hnbjnpn2().s[164]++;
            this.performanceCache.set(`${endpoint.provider}_${endpoint.region}`, latency);
            cov_4hnbjnpn2().s[165]++;
            endpoint.latency = latency;
            cov_4hnbjnpn2().s[166]++;
            endpoint.lastTested = Date.now();
          } catch (error) {
            cov_4hnbjnpn2().s[167]++;
            console.error(`Failed to monitor ${endpoint.provider}/${endpoint.region}:`, error);
          }
        }
      });
      function monitorCDNPerformance() {
        return _monitorCDNPerformance.apply(this, arguments);
      }
      return monitorCDNPerformance;
    }()
  }, {
    key: "measureEndpointLatency",
    value: function () {
      var _measureEndpointLatency = _asyncToGenerator(function* (endpoint) {
        cov_4hnbjnpn2().f[29]++;
        cov_4hnbjnpn2().s[168]++;
        return endpoint.latency + (Math.random() - 0.5) * 20;
      });
      function measureEndpointLatency(_x11) {
        return _measureEndpointLatency.apply(this, arguments);
      }
      return measureEndpointLatency;
    }()
  }, {
    key: "testEndpointPerformance",
    value: function () {
      var _testEndpointPerformance = _asyncToGenerator(function* (endpoint) {
        cov_4hnbjnpn2().f[30]++;
        cov_4hnbjnpn2().s[169]++;
        try {
          var latency = (cov_4hnbjnpn2().s[170]++, yield this.measureEndpointLatency(endpoint));
          cov_4hnbjnpn2().s[171]++;
          return {
            endpoint: endpoint,
            latency: latency,
            success: true
          };
        } catch (error) {
          cov_4hnbjnpn2().s[172]++;
          return {
            endpoint: endpoint,
            latency: 9999,
            success: false
          };
        }
      });
      function testEndpointPerformance(_x12) {
        return _testEndpointPerformance.apply(this, arguments);
      }
      return testEndpointPerformance;
    }()
  }, {
    key: "updateRoutingTable",
    value: function () {
      var _updateRoutingTable = _asyncToGenerator(function* (testResults) {
        var _this5 = this;
        cov_4hnbjnpn2().f[31]++;
        cov_4hnbjnpn2().s[173]++;
        testResults.forEach(function (result) {
          cov_4hnbjnpn2().f[32]++;
          cov_4hnbjnpn2().s[174]++;
          if ((cov_4hnbjnpn2().b[34][0]++, result.status === 'fulfilled') && (cov_4hnbjnpn2().b[34][1]++, result.value.success)) {
            cov_4hnbjnpn2().b[33][0]++;
            var _ref5 = (cov_4hnbjnpn2().s[175]++, result.value),
              endpoint = _ref5.endpoint,
              latency = _ref5.latency;
            cov_4hnbjnpn2().s[176]++;
            _this5.routingTable.set(`${endpoint.provider}_${endpoint.region}`, latency.toString());
          } else {
            cov_4hnbjnpn2().b[33][1]++;
          }
        });
      });
      function updateRoutingTable(_x13) {
        return _updateRoutingTable.apply(this, arguments);
      }
      return updateRoutingTable;
    }()
  }, {
    key: "adaptRoutingToResourceConditions",
    value: function () {
      var _adaptRoutingToResourceConditions = _asyncToGenerator(function* (resourceMetrics) {
        cov_4hnbjnpn2().f[33]++;
        cov_4hnbjnpn2().s[177]++;
        if (resourceMetrics.network.latency > 200) {
          cov_4hnbjnpn2().b[35][0]++;
          cov_4hnbjnpn2().s[178]++;
          console.log('Adapting routing for slow network conditions');
        } else {
          cov_4hnbjnpn2().b[35][1]++;
        }
        cov_4hnbjnpn2().s[179]++;
        if (resourceMetrics.battery.level < 20) {
          cov_4hnbjnpn2().b[36][0]++;
          cov_4hnbjnpn2().s[180]++;
          console.log('Adapting routing for low battery conditions');
        } else {
          cov_4hnbjnpn2().b[36][1]++;
        }
      });
      function adaptRoutingToResourceConditions(_x14) {
        return _adaptRoutingToResourceConditions.apply(this, arguments);
      }
      return adaptRoutingToResourceConditions;
    }()
  }, {
    key: "optimizeRoutingTable",
    value: function () {
      var _optimizeRoutingTable = _asyncToGenerator(function* () {
        cov_4hnbjnpn2().f[34]++;
        cov_4hnbjnpn2().s[181]++;
        console.log('Optimizing initial routing table');
      });
      function optimizeRoutingTable() {
        return _optimizeRoutingTable.apply(this, arguments);
      }
      return optimizeRoutingTable;
    }()
  }]);
}();
export var globalCDNManager = (cov_4hnbjnpn2().s[182]++, new GlobalCDNManager());
export default globalCDNManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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