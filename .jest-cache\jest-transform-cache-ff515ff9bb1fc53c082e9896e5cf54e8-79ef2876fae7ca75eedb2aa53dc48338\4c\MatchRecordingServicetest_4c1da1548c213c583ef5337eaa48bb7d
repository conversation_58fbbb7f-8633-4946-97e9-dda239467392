e5dca230c90842b1b1beff590a817dd2
_getJestObj().mock("../../lib/supabase", function () {
  return {
    supabase: {
      from: jest.fn()
    }
  };
});
_getJestObj().mock("../../utils/performance", function () {
  return {
    performanceMonitor: {
      start: jest.fn(),
      end: jest.fn()
    }
  };
});
_getJestObj().mock("../../utils/errorHandling", function () {
  return {
    handleError: jest.fn(function (error) {
      return {
        userMessage: error.message
      };
    })
  };
});
_getJestObj().mock('expo-file-system', function () {
  return {
    getInfoAsync: jest.fn(),
    readAsStringAsync: jest.fn(),
    copyAsync: jest.fn(),
    cacheDirectory: '/mock/cache/'
  };
});
_getJestObj().mock('expo-camera', function () {
  return {
    Camera: {
      requestCameraPermissionsAsync: jest.fn(),
      requestMicrophonePermissionsAsync: jest.fn()
    }
  };
});
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockMatchRepository = {
  createMatch: jest.fn(),
  updateMatch: jest.fn(),
  getRecentMatches: jest.fn()
};
var mockVideoRecordingService = {
  initialize: jest.fn(),
  startRecording: jest.fn(),
  stopRecording: jest.fn()
};
describe('MatchRecordingService Core Functionality', function () {
  beforeEach(function () {
    jest.clearAllMocks();
  });
  describe('Match Recording Logic', function () {
    it('should validate match metadata correctly', function () {
      var validMetadata = {
        userId: 'user123',
        opponentName: 'John Doe',
        matchType: 'friendly',
        matchFormat: 'best_of_3',
        surface: 'hard',
        location: 'Local Tennis Club',
        startTime: new Date().toISOString()
      };
      expect(validMetadata.userId).toBeTruthy();
      expect(validMetadata.opponentName).toBeTruthy();
      expect(validMetadata.matchType).toBe('friendly');
      expect(validMetadata.matchFormat).toBe('best_of_3');
      expect(validMetadata.surface).toBe('hard');
    });
    it('should handle invalid metadata', function () {
      var invalidMetadata = {
        userId: '',
        opponentName: '',
        matchType: 'friendly',
        matchFormat: 'best_of_3',
        surface: 'hard',
        location: 'Local Tennis Club',
        startTime: new Date().toISOString()
      };
      expect(invalidMetadata.userId).toBeFalsy();
      expect(invalidMetadata.opponentName).toBeFalsy();
    });
    it('should calculate match statistics correctly', function () {
      var mockStatistics = {
        aces: 5,
        doubleFaults: 2,
        winners: 12,
        unforcedErrors: 8,
        totalPointsWon: 45,
        totalPointsPlayed: 80
      };
      var winPercentage = mockStatistics.totalPointsWon / mockStatistics.totalPointsPlayed * 100;
      expect(winPercentage).toBeCloseTo(56.25);
      var errorRate = mockStatistics.unforcedErrors / mockStatistics.totalPointsPlayed * 100;
      expect(errorRate).toBeCloseTo(10);
    });
    it('should handle score tracking correctly', function () {
      var mockScore = {
        sets: [{
          setNumber: 1,
          userGames: 6,
          opponentGames: 4,
          isCompleted: true
        }, {
          setNumber: 2,
          userGames: 4,
          opponentGames: 6,
          isCompleted: true
        }, {
          setNumber: 3,
          userGames: 3,
          opponentGames: 2,
          isCompleted: false
        }],
        setsWon: 1,
        setsLost: 1
      };
      expect(mockScore.sets).toHaveLength(3);
      expect(mockScore.sets[0].isCompleted).toBe(true);
      expect(mockScore.sets[2].isCompleted).toBe(false);
      expect(mockScore.setsWon).toBe(1);
      expect(mockScore.setsLost).toBe(1);
    });
  });
  describe('Point Recording Logic', function () {
    it('should validate game events correctly', function () {
      var validGameEvent = {
        eventType: 'winner',
        player: 'user',
        shotType: 'forehand',
        courtPosition: {
          x: 0.5,
          y: 0.3
        },
        timestamp: Date.now()
      };
      expect(validGameEvent.eventType).toBe('winner');
      expect(validGameEvent.player).toBe('user');
      expect(validGameEvent.shotType).toBe('forehand');
      expect(validGameEvent.courtPosition.x).toBeGreaterThanOrEqual(0);
      expect(validGameEvent.courtPosition.x).toBeLessThanOrEqual(1);
      expect(validGameEvent.timestamp).toBeGreaterThan(0);
    });
    it('should handle different event types', function () {
      var eventTypes = ['ace', 'winner', 'unforced_error', 'forced_error', 'double_fault'];
      eventTypes.forEach(function (eventType) {
        var event = {
          eventType: eventType,
          player: 'user',
          shotType: 'serve',
          courtPosition: {
            x: 0.5,
            y: 0.5
          },
          timestamp: Date.now()
        };
        expect(event.eventType).toBe(eventType);
        expect(['ace', 'winner', 'unforced_error', 'forced_error', 'double_fault']).toContain(event.eventType);
      });
    });
    it('should track statistics updates', function () {
      var initialStats = {
        aces: 0,
        winners: 0,
        unforcedErrors: 0,
        totalPointsWon: 0
      };
      var aceStats = Object.assign({}, initialStats, {
        aces: initialStats.aces + 1,
        totalPointsWon: initialStats.totalPointsWon + 1
      });
      expect(aceStats.aces).toBe(1);
      expect(aceStats.totalPointsWon).toBe(1);
      var winnerStats = Object.assign({}, aceStats, {
        winners: aceStats.winners + 1,
        totalPointsWon: aceStats.totalPointsWon + 1
      });
      expect(winnerStats.winners).toBe(1);
      expect(winnerStats.totalPointsWon).toBe(2);
    });
  });
  describe('Match Completion Logic', function () {
    it('should determine match completion correctly', function () {
      var completedMatch = {
        sets: [{
          setNumber: 1,
          userGames: 6,
          opponentGames: 4,
          isCompleted: true
        }, {
          setNumber: 2,
          userGames: 6,
          opponentGames: 3,
          isCompleted: true
        }],
        setsWon: 2,
        setsLost: 0,
        finalScore: '6-4, 6-3',
        result: 'win'
      };
      expect(completedMatch.setsWon).toBe(2);
      expect(completedMatch.result).toBe('win');
      expect(completedMatch.finalScore).toBe('6-4, 6-3');
    });
    it('should calculate match duration', function () {
      var startTime = new Date('2024-01-15T10:00:00Z');
      var endTime = new Date('2024-01-15T11:30:00Z');
      var durationMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60);
      expect(durationMinutes).toBe(90);
    });
    it('should generate final statistics', function () {
      var finalStats = {
        totalPointsWon: 65,
        totalPointsPlayed: 120,
        aces: 8,
        doubleFaults: 3,
        winners: 25,
        unforcedErrors: 15,
        firstServePercentage: 68
      };
      var winPercentage = finalStats.totalPointsWon / finalStats.totalPointsPlayed * 100;
      expect(winPercentage).toBeCloseTo(54.17, 2);
      var acePercentage = finalStats.aces / finalStats.totalPointsPlayed * 100;
      expect(acePercentage).toBeCloseTo(6.67, 2);
    });
  });
  describe('Database Integration Logic', function () {
    it('should format match data for database correctly', function () {
      var matchData = {
        id: 'match123',
        user_id: 'user123',
        opponent_name: 'John Doe',
        match_type: 'friendly',
        match_format: 'best_of_3',
        surface: 'hard',
        location: 'Local Tennis Club',
        status: 'recording',
        created_at: new Date().toISOString()
      };
      expect(matchData.id).toBe('match123');
      expect(matchData.user_id).toBe('user123');
      expect(matchData.opponent_name).toBe('John Doe');
      expect(matchData.match_type).toBe('friendly');
      expect(matchData.status).toBe('recording');
    });
    it('should handle offline data synchronization', function () {
      var offlineQueue = [{
        timestamp: Date.now(),
        data: {
          score: {
            setsWon: 1,
            setsLost: 0
          },
          statistics: {
            aces: 3,
            winners: 8
          },
          status: 'recording'
        }
      }];
      expect(offlineQueue).toHaveLength(1);
      expect(offlineQueue[0].data.score.setsWon).toBe(1);
      expect(offlineQueue[0].data.statistics.aces).toBe(3);
    });
    it('should validate data before database operations', function () {
      var validData = {
        user_id: 'user123',
        opponent_name: 'John Doe',
        match_type: 'friendly',
        status: 'recording'
      };
      var invalidData = {
        user_id: '',
        opponent_name: '',
        match_type: '',
        status: ''
      };
      var isValidData = function isValidData(data) {
        return !!(data.user_id && data.opponent_name && data.match_type && data.status);
      };
      expect(isValidData(validData)).toBe(true);
      expect(isValidData(invalidData)).toBe(false);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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