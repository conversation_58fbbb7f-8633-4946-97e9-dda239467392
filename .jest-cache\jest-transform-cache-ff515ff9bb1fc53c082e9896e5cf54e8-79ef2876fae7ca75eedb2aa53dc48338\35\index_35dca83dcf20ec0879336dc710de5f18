02f065dd84a549c7351b1f24b1e0d4bc
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var _TaskQueue = _interopRequireDefault(require("./TaskQueue"));
var _EventEmitter = _interopRequireDefault(require("../../vendor/react-native/vendor/emitter/EventEmitter"));
var _requestIdleCallback = _interopRequireDefault(require("../../modules/requestIdleCallback"));
var _emitter = new _EventEmitter.default();
var InteractionManager = {
  Events: {
    interactionStart: 'interactionStart',
    interactionComplete: 'interactionComplete'
  },
  runAfterInteractions: function runAfterInteractions(task) {
    var tasks = [];
    var promise = new Promise(function (resolve) {
      _scheduleUpdate();
      if (task) {
        tasks.push(task);
      }
      tasks.push({
        run: resolve,
        name: 'resolve ' + (task && task.name || '?')
      });
      _taskQueue.enqueueTasks(tasks);
    });
    return {
      then: promise.then.bind(promise),
      done: promise.then.bind(promise),
      cancel: function cancel() {
        _taskQueue.cancelTasks(tasks);
      }
    };
  },
  createInteractionHandle: function createInteractionHandle() {
    _scheduleUpdate();
    var handle = ++_inc;
    _addInteractionSet.add(handle);
    return handle;
  },
  clearInteractionHandle: function clearInteractionHandle(handle) {
    (0, _invariant.default)(!!handle, 'Must provide a handle to clear.');
    _scheduleUpdate();
    _addInteractionSet.delete(handle);
    _deleteInteractionSet.add(handle);
  },
  addListener: _emitter.addListener.bind(_emitter),
  setDeadline: function setDeadline(deadline) {
    _deadline = deadline;
  }
};
var _interactionSet = new Set();
var _addInteractionSet = new Set();
var _deleteInteractionSet = new Set();
var _taskQueue = new _TaskQueue.default({
  onMoreTasks: _scheduleUpdate
});
var _nextUpdateHandle = 0;
var _inc = 0;
var _deadline = -1;
function _scheduleUpdate() {
  if (!_nextUpdateHandle) {
    if (_deadline > 0) {
      _nextUpdateHandle = setTimeout(_processUpdate);
    } else {
      _nextUpdateHandle = (0, _requestIdleCallback.default)(_processUpdate);
    }
  }
}
function _processUpdate() {
  _nextUpdateHandle = 0;
  var interactionCount = _interactionSet.size;
  _addInteractionSet.forEach(function (handle) {
    return _interactionSet.add(handle);
  });
  _deleteInteractionSet.forEach(function (handle) {
    return _interactionSet.delete(handle);
  });
  var nextInteractionCount = _interactionSet.size;
  if (interactionCount !== 0 && nextInteractionCount === 0) {
    _emitter.emit(InteractionManager.Events.interactionComplete);
  } else if (interactionCount === 0 && nextInteractionCount !== 0) {
    _emitter.emit(InteractionManager.Events.interactionStart);
  }
  if (nextInteractionCount === 0) {
    var begin = Date.now();
    while (_taskQueue.hasTasksToProcess()) {
      _taskQueue.processNext();
      if (_deadline > 0 && Date.now() - begin >= _deadline) {
        _scheduleUpdate();
        break;
      }
    }
  }
  _addInteractionSet.clear();
  _deleteInteractionSet.clear();
}
var _default = exports.default = InteractionManager;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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