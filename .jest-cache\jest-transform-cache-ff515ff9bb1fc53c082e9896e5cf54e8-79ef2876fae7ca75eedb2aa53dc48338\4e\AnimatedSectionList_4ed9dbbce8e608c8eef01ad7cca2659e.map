{"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "exports", "__esModule", "_extends2", "React", "_SectionList", "_createAnimatedComponent", "SectionListWithEventThrottle", "forwardRef", "props", "ref", "createElement", "scrollEventThrottle", "_default", "module"], "sources": ["AnimatedSectionList.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _SectionList = _interopRequireDefault(require(\"../../../../exports/SectionList\"));\nvar _createAnimatedComponent = _interopRequireDefault(require(\"../createAnimatedComponent\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n/**\n * @see https://github.com/facebook/react-native/commit/b8c8562\n */\nvar SectionListWithEventThrottle = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(_SectionList.default, (0, _extends2.default)({\n  scrollEventThrottle: 0.0001\n}, props, {\n  ref: ref\n})));\nvar _default = exports.default = (0, _createAnimatedComponent.default)(SectionListWithEventThrottle);\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,SAAS,GAAGH,sBAAsB,CAACF,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIM,KAAK,GAAGP,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIO,YAAY,GAAGL,sBAAsB,CAACF,OAAO,kCAAkC,CAAC,CAAC;AACrF,IAAIQ,wBAAwB,GAAGN,sBAAsB,CAACF,OAAO,6BAA6B,CAAC,CAAC;AAc5F,IAAIS,4BAA4B,GAAgBH,KAAK,CAACI,UAAU,CAAC,UAACC,KAAK,EAAEC,GAAG;EAAA,OAAkBN,KAAK,CAACO,aAAa,CAACN,YAAY,CAACN,OAAO,EAAE,CAAC,CAAC,EAAEI,SAAS,CAACJ,OAAO,EAAE;IAC7Ja,mBAAmB,EAAE;EACvB,CAAC,EAAEH,KAAK,EAAE;IACRC,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AAAA,EAAC;AACJ,IAAIG,QAAQ,GAAGZ,OAAO,CAACF,OAAO,GAAG,CAAC,CAAC,EAAEO,wBAAwB,CAACP,OAAO,EAAEQ,4BAA4B,CAAC;AACpGO,MAAM,CAACb,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}