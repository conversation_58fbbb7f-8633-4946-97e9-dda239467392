974437f84ffb1a97194673082b7f0fd2
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _createElement = _interopRequireDefault(require("../createElement"));
var ANIMATION_DURATION = 300;
function getAnimationStyle(animationType, visible) {
  if (animationType === 'slide') {
    return visible ? animatedSlideInStyles : animatedSlideOutStyles;
  }
  if (animationType === 'fade') {
    return visible ? animatedFadeInStyles : animatedFadeOutStyles;
  }
  return visible ? styles.container : styles.hidden;
}
function ModalAnimation(props) {
  var animationType = props.animationType,
    children = props.children,
    onDismiss = props.onDismiss,
    onShow = props.onShow,
    visible = props.visible;
  var _React$useState = React.useState(false),
    isRendering = _React$useState[0],
    setIsRendering = _React$useState[1];
  var wasVisible = React.useRef(false);
  var wasRendering = React.useRef(false);
  var isAnimated = animationType && animationType !== 'none';
  var animationEndCallback = React.useCallback(function (e) {
    if (e && e.currentTarget !== e.target) {
      return;
    }
    if (visible) {
      if (onShow) {
        onShow();
      }
    } else {
      setIsRendering(false);
    }
  }, [onShow, visible]);
  React.useEffect(function () {
    if (wasRendering.current && !isRendering && onDismiss) {
      onDismiss();
    }
    wasRendering.current = isRendering;
  }, [isRendering, onDismiss]);
  React.useEffect(function () {
    if (visible) {
      setIsRendering(true);
    }
    if (visible !== wasVisible.current && !isAnimated) {
      animationEndCallback();
    }
    wasVisible.current = visible;
  }, [isAnimated, visible, animationEndCallback]);
  return isRendering || visible ? (0, _createElement.default)('div', {
    style: isRendering ? getAnimationStyle(animationType, visible) : styles.hidden,
    onAnimationEnd: animationEndCallback,
    children: children
  }) : null;
}
var styles = _StyleSheet.default.create({
  container: {
    position: 'fixed',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    zIndex: 9999
  },
  animatedIn: {
    animationDuration: ANIMATION_DURATION + "ms",
    animationTimingFunction: 'ease-in'
  },
  animatedOut: {
    pointerEvents: 'none',
    animationDuration: ANIMATION_DURATION + "ms",
    animationTimingFunction: 'ease-out'
  },
  fadeIn: {
    opacity: 1,
    animationKeyframes: {
      '0%': {
        opacity: 0
      },
      '100%': {
        opacity: 1
      }
    }
  },
  fadeOut: {
    opacity: 0,
    animationKeyframes: {
      '0%': {
        opacity: 1
      },
      '100%': {
        opacity: 0
      }
    }
  },
  slideIn: {
    transform: 'translateY(0%)',
    animationKeyframes: {
      '0%': {
        transform: 'translateY(100%)'
      },
      '100%': {
        transform: 'translateY(0%)'
      }
    }
  },
  slideOut: {
    transform: 'translateY(100%)',
    animationKeyframes: {
      '0%': {
        transform: 'translateY(0%)'
      },
      '100%': {
        transform: 'translateY(100%)'
      }
    }
  },
  hidden: {
    opacity: 0
  }
});
var animatedSlideInStyles = [styles.container, styles.animatedIn, styles.slideIn];
var animatedSlideOutStyles = [styles.container, styles.animatedOut, styles.slideOut];
var animatedFadeInStyles = [styles.container, styles.animatedIn, styles.fadeIn];
var animatedFadeOutStyles = [styles.container, styles.animatedOut, styles.fadeOut];
var _default = exports.default = ModalAnimation;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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