{"version": 3, "names": ["performanceMonitor", "SmartLoadBalancer", "_classCallCheck", "serviceEndpoints", "cov_1iz0px6adx", "s", "Map", "trafficHistory", "circuitBreakers", "sessionAffinity", "HEALTH_CHECK_INTERVAL", "CIRCUIT_BREAKER_THRESHOLD", "CIRCUIT_BREAKER_TIMEOUT", "f", "loadBalancingStrategy", "algorithm", "healthCheckInterval", "failoverThreshold", "circuitBreakerEnabled", "stickySession", "geoRouting", "trafficPredictionModel", "TrafficPredictionModel", "initializeLoadBalancer", "_createClass", "key", "value", "_initializeLoadBalancer", "_asyncToGenerator", "discoverServiceEndpoints", "startHealthMonitoring", "initialize", "startTrafficAnalysis", "console", "log", "error", "apply", "arguments", "_routeRequest", "request", "startTime", "Date", "now", "availableEndpoints", "getAvailableEndpoints", "type", "length", "b", "Error", "routingDecision", "selectOptimalEndpoint", "updateEndpointMetrics", "endpoint", "id", "trackRoutingDecision", "processingTime", "trackDatabaseQuery", "getFallbackRoutingDecision", "routeRequest", "_x", "_getTrafficPredictions", "timeHorizon", "undefined", "predict", "expectedRequests", "peakTimes", "regionalDistribution", "typeDistribution", "confidence", "getTrafficPredictions", "getLoadBalancingMetrics", "endpoints", "Array", "from", "values", "healthyEndpoints", "filter", "ep", "health", "status", "totalRPS", "reduce", "sum", "metrics", "requestsPerSecond", "avgResponseTime", "responseTime", "totalRequests", "successfulRequests", "successRate", "errorRate", "for<PERSON>ach", "region", "endpointUtilization", "utilization", "currentConnections", "maxConnections", "totalEndpoints", "averageResponseTime", "totalRequestsPerSecond", "updateStrategy", "strategy", "Object", "assign", "addServiceEndpoint", "fullEndpoint", "<PERSON><PERSON><PERSON><PERSON>", "uptime", "throughput", "set", "removeServiceEndpoint", "endpointId", "delete", "_discoverServiceEndpoints", "url", "weight", "capacity", "cpu", "memory", "bandwidth", "storage", "_this", "isCircuitBreakerOpen", "_selectOptimalEndpoint", "selectedEndpoint", "reason", "_yield$this$aiOptimiz", "aiOptimizedSelection", "leastConnectionsSelection", "weightedRoundRobinSelection", "ipHashSelection", "roundRobinSelection", "fallbackEndpoints", "sort", "a", "slice", "estimatedResponseTime", "estimateResponseTime", "cacheStrategy", "determineCacheStrategy", "_x2", "_x3", "_aiOptimizedSelection", "_this2", "scoredEndpoints", "map", "score", "responseTimeScore", "Math", "max", "capacityScore", "geoScore", "calculateGeographicScore", "loadScore", "sessionId", "get", "selected", "toFixed", "min", "_x4", "_x5", "current", "totalWeight", "random", "currentWeight", "index", "hash", "simpleHash", "headers", "userLocation", "regionMap", "endpointCountries", "includes", "country", "estimatedTime", "loadFactor", "typeFactors", "round", "method", "decision", "push", "timestamp", "response", "splice", "fallbackEndpoint", "breaker", "isOpen", "lastFailure", "failures", "_this3", "setInterval", "performHealthChecks", "_performHealthChecks", "healthResult", "checkEndpointHealth", "updateEndpointHealth", "markEndpointUnhealthy", "_checkEndpointHealth", "success", "_x6", "has", "warn", "_this4", "analyzeTrafficPatterns", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entry", "str", "i", "char", "charCodeAt", "abs", "_initialize", "_predict", "start", "end", "intensity", "_x7", "_x8", "smartLoadBalancer"], "sources": ["SmartLoadBalancer.ts"], "sourcesContent": ["/**\n * Smart Load Balancer\n * \n * AI-driven load balancing system that intelligently distributes traffic\n * based on real-time performance, health metrics, and predictive analytics.\n */\n\nimport { globalCDNManager } from './GlobalCDNManager';\nimport { edgeFunctionManager } from './EdgeFunctionManager';\nimport { smartResourceManager } from '@/services/ai/SmartResourceManager';\nimport { performanceMonitor } from '@/utils/performance';\n\ninterface ServiceEndpoint {\n  id: string;\n  url: string;\n  region: string;\n  type: 'api' | 'cdn' | 'edge_function' | 'database' | 'storage';\n  weight: number;\n  maxConnections: number;\n  currentConnections: number;\n  health: {\n    status: 'healthy' | 'degraded' | 'unhealthy' | 'maintenance';\n    lastCheck: number;\n    responseTime: number;\n    errorRate: number;\n    uptime: number;\n  };\n  capacity: {\n    cpu: number; // percentage\n    memory: number; // percentage\n    bandwidth: number; // Mbps\n    storage: number; // percentage\n  };\n  metrics: {\n    requestsPerSecond: number;\n    averageResponseTime: number;\n    throughput: number;\n    successRate: number;\n  };\n}\n\ninterface LoadBalancingStrategy {\n  algorithm: 'round_robin' | 'weighted_round_robin' | 'least_connections' | 'ip_hash' | 'ai_optimized';\n  healthCheckInterval: number;\n  failoverThreshold: number;\n  circuitBreakerEnabled: boolean;\n  stickySession: boolean;\n  geoRouting: boolean;\n}\n\ninterface TrafficRequest {\n  id: string;\n  type: 'api' | 'cdn' | 'edge_function';\n  path: string;\n  method: string;\n  headers: Record<string, string>;\n  userLocation?: {\n    country: string;\n    region: string;\n    coordinates: { lat: number; lng: number };\n  };\n  priority: 'high' | 'medium' | 'low';\n  sessionId?: string;\n}\n\ninterface RoutingDecision {\n  endpoint: ServiceEndpoint;\n  reason: string;\n  confidence: number;\n  fallbackEndpoints: ServiceEndpoint[];\n  estimatedResponseTime: number;\n  cacheStrategy: 'bypass' | 'cache' | 'cache_and_forward';\n}\n\ninterface TrafficPrediction {\n  timeHorizon: number; // minutes\n  expectedRequests: number;\n  peakTimes: Array<{ start: number; end: number; intensity: number }>;\n  regionalDistribution: Record<string, number>;\n  typeDistribution: Record<string, number>;\n  confidence: number;\n}\n\n/**\n * AI-Powered Smart Load Balancer\n */\nclass SmartLoadBalancer {\n  private serviceEndpoints: Map<string, ServiceEndpoint> = new Map();\n  private loadBalancingStrategy: LoadBalancingStrategy;\n  private trafficHistory: Array<{ timestamp: number; request: TrafficRequest; response: any }> = [];\n  private circuitBreakers: Map<string, { isOpen: boolean; failures: number; lastFailure: number }> = new Map();\n  private sessionAffinity: Map<string, string> = new Map(); // sessionId -> endpointId\n  private trafficPredictionModel: TrafficPredictionModel;\n  \n  private readonly HEALTH_CHECK_INTERVAL = 30000; // 30 seconds\n  private readonly CIRCUIT_BREAKER_THRESHOLD = 5; // failures\n  private readonly CIRCUIT_BREAKER_TIMEOUT = 60000; // 1 minute\n\n  constructor() {\n    this.loadBalancingStrategy = {\n      algorithm: 'ai_optimized',\n      healthCheckInterval: this.HEALTH_CHECK_INTERVAL,\n      failoverThreshold: 0.1, // 10% error rate\n      circuitBreakerEnabled: true,\n      stickySession: true,\n      geoRouting: true,\n    };\n    \n    this.trafficPredictionModel = new TrafficPredictionModel();\n    this.initializeLoadBalancer();\n  }\n\n  /**\n   * Initialize smart load balancer\n   */\n  private async initializeLoadBalancer(): Promise<void> {\n    try {\n      // Discover service endpoints\n      await this.discoverServiceEndpoints();\n      \n      // Start health monitoring\n      this.startHealthMonitoring();\n      \n      // Initialize traffic prediction model\n      await this.trafficPredictionModel.initialize();\n      \n      // Start traffic analysis\n      this.startTrafficAnalysis();\n      \n      console.log('Smart Load Balancer initialized successfully');\n    } catch (error) {\n      console.error('Failed to initialize Smart Load Balancer:', error);\n    }\n  }\n\n  /**\n   * Route traffic request to optimal endpoint\n   */\n  async routeRequest(request: TrafficRequest): Promise<RoutingDecision> {\n    try {\n      const startTime = Date.now();\n      \n      // Get available endpoints for request type\n      const availableEndpoints = this.getAvailableEndpoints(request.type);\n      \n      if (availableEndpoints.length === 0) {\n        throw new Error(`No available endpoints for type: ${request.type}`);\n      }\n\n      // Apply load balancing algorithm\n      const routingDecision = await this.selectOptimalEndpoint(request, availableEndpoints);\n      \n      // Update endpoint metrics\n      this.updateEndpointMetrics(routingDecision.endpoint.id, request);\n      \n      // Track routing decision\n      this.trackRoutingDecision(request, routingDecision);\n      \n      const processingTime = Date.now() - startTime;\n      performanceMonitor.trackDatabaseQuery('load_balancer_routing', processingTime);\n      \n      return routingDecision;\n\n    } catch (error) {\n      console.error('Failed to route request:', error);\n      return this.getFallbackRoutingDecision(request);\n    }\n  }\n\n  /**\n   * Get traffic predictions\n   */\n  async getTrafficPredictions(timeHorizon: number = 60): Promise<TrafficPrediction> {\n    try {\n      return await this.trafficPredictionModel.predict(this.trafficHistory, timeHorizon);\n    } catch (error) {\n      console.error('Failed to get traffic predictions:', error);\n      return {\n        timeHorizon,\n        expectedRequests: 0,\n        peakTimes: [],\n        regionalDistribution: {},\n        typeDistribution: {},\n        confidence: 0,\n      };\n    }\n  }\n\n  /**\n   * Get load balancing metrics\n   */\n  getLoadBalancingMetrics(): {\n    totalEndpoints: number;\n    healthyEndpoints: number;\n    averageResponseTime: number;\n    totalRequestsPerSecond: number;\n    errorRate: number;\n    regionalDistribution: Record<string, number>;\n    endpointUtilization: Record<string, number>;\n  } {\n    const endpoints = Array.from(this.serviceEndpoints.values());\n    const healthyEndpoints = endpoints.filter(ep => ep.health.status === 'healthy');\n    \n    const totalRPS = endpoints.reduce((sum, ep) => sum + ep.metrics.requestsPerSecond, 0);\n    const avgResponseTime = endpoints.length > 0\n      ? endpoints.reduce((sum, ep) => sum + ep.health.responseTime, 0) / endpoints.length\n      : 0;\n    \n    // Calculate error rate\n    const totalRequests = endpoints.reduce((sum, ep) => sum + ep.metrics.requestsPerSecond * 60, 0);\n    const successfulRequests = endpoints.reduce((sum, ep) => \n      sum + (ep.metrics.requestsPerSecond * 60 * ep.metrics.successRate / 100), 0);\n    const errorRate = totalRequests > 0 ? ((totalRequests - successfulRequests) / totalRequests) * 100 : 0;\n    \n    // Regional distribution\n    const regionalDistribution: Record<string, number> = {};\n    endpoints.forEach(ep => {\n      regionalDistribution[ep.region] = (regionalDistribution[ep.region] || 0) + 1;\n    });\n    \n    // Endpoint utilization\n    const endpointUtilization: Record<string, number> = {};\n    endpoints.forEach(ep => {\n      const utilization = (ep.currentConnections / ep.maxConnections) * 100;\n      endpointUtilization[ep.id] = utilization;\n    });\n\n    return {\n      totalEndpoints: endpoints.length,\n      healthyEndpoints: healthyEndpoints.length,\n      averageResponseTime: avgResponseTime,\n      totalRequestsPerSecond: totalRPS,\n      errorRate,\n      regionalDistribution,\n      endpointUtilization,\n    };\n  }\n\n  /**\n   * Update load balancing strategy\n   */\n  updateStrategy(strategy: Partial<LoadBalancingStrategy>): void {\n    this.loadBalancingStrategy = { ...this.loadBalancingStrategy, ...strategy };\n    console.log('Updated load balancing strategy:', this.loadBalancingStrategy);\n  }\n\n  /**\n   * Add service endpoint\n   */\n  addServiceEndpoint(endpoint: Omit<ServiceEndpoint, 'health' | 'metrics'>): void {\n    const fullEndpoint: ServiceEndpoint = {\n      ...endpoint,\n      health: {\n        status: 'healthy',\n        lastCheck: Date.now(),\n        responseTime: 0,\n        errorRate: 0,\n        uptime: 100,\n      },\n      metrics: {\n        requestsPerSecond: 0,\n        averageResponseTime: 0,\n        throughput: 0,\n        successRate: 100,\n      },\n    };\n    \n    this.serviceEndpoints.set(endpoint.id, fullEndpoint);\n    console.log(`Added service endpoint: ${endpoint.id}`);\n  }\n\n  /**\n   * Remove service endpoint\n   */\n  removeServiceEndpoint(endpointId: string): void {\n    this.serviceEndpoints.delete(endpointId);\n    this.circuitBreakers.delete(endpointId);\n    console.log(`Removed service endpoint: ${endpointId}`);\n  }\n\n  // Private helper methods\n\n  private async discoverServiceEndpoints(): Promise<void> {\n    // Discover API endpoints\n    this.addServiceEndpoint({\n      id: 'api_us_east',\n      url: 'https://api-us-east.acemind.app',\n      region: 'us-east-1',\n      type: 'api',\n      weight: 100,\n      maxConnections: 1000,\n      currentConnections: 0,\n      capacity: { cpu: 30, memory: 40, bandwidth: 1000, storage: 20 },\n    });\n\n    this.addServiceEndpoint({\n      id: 'api_eu_west',\n      url: 'https://api-eu-west.acemind.app',\n      region: 'eu-west-1',\n      type: 'api',\n      weight: 100,\n      maxConnections: 800,\n      currentConnections: 0,\n      capacity: { cpu: 25, memory: 35, bandwidth: 800, storage: 15 },\n    });\n\n    // Discover CDN endpoints\n    this.addServiceEndpoint({\n      id: 'cdn_global',\n      url: 'https://cdn.acemind.app',\n      region: 'global',\n      type: 'cdn',\n      weight: 100,\n      maxConnections: 10000,\n      currentConnections: 0,\n      capacity: { cpu: 10, memory: 20, bandwidth: 10000, storage: 50 },\n    });\n\n    console.log('Discovered service endpoints');\n  }\n\n  private getAvailableEndpoints(type: string): ServiceEndpoint[] {\n    return Array.from(this.serviceEndpoints.values())\n      .filter(endpoint => \n        endpoint.type === type &&\n        endpoint.health.status === 'healthy' &&\n        !this.isCircuitBreakerOpen(endpoint.id) &&\n        endpoint.currentConnections < endpoint.maxConnections\n      );\n  }\n\n  private async selectOptimalEndpoint(\n    request: TrafficRequest,\n    availableEndpoints: ServiceEndpoint[]\n  ): Promise<RoutingDecision> {\n    let selectedEndpoint: ServiceEndpoint;\n    let reason: string;\n    let confidence: number;\n\n    switch (this.loadBalancingStrategy.algorithm) {\n      case 'ai_optimized':\n        ({ endpoint: selectedEndpoint, reason, confidence } = await this.aiOptimizedSelection(request, availableEndpoints));\n        break;\n      \n      case 'least_connections':\n        selectedEndpoint = this.leastConnectionsSelection(availableEndpoints);\n        reason = 'Least connections algorithm';\n        confidence = 0.8;\n        break;\n      \n      case 'weighted_round_robin':\n        selectedEndpoint = this.weightedRoundRobinSelection(availableEndpoints);\n        reason = 'Weighted round robin algorithm';\n        confidence = 0.7;\n        break;\n      \n      case 'ip_hash':\n        selectedEndpoint = this.ipHashSelection(request, availableEndpoints);\n        reason = 'IP hash algorithm';\n        confidence = 0.9;\n        break;\n      \n      default:\n        selectedEndpoint = this.roundRobinSelection(availableEndpoints);\n        reason = 'Round robin algorithm';\n        confidence = 0.6;\n    }\n\n    // Generate fallback endpoints\n    const fallbackEndpoints = availableEndpoints\n      .filter(ep => ep.id !== selectedEndpoint.id)\n      .sort((a, b) => a.health.responseTime - b.health.responseTime)\n      .slice(0, 3);\n\n    // Estimate response time\n    const estimatedResponseTime = this.estimateResponseTime(selectedEndpoint, request);\n\n    return {\n      endpoint: selectedEndpoint,\n      reason,\n      confidence,\n      fallbackEndpoints,\n      estimatedResponseTime,\n      cacheStrategy: this.determineCacheStrategy(request, selectedEndpoint),\n    };\n  }\n\n  private async aiOptimizedSelection(\n    request: TrafficRequest,\n    endpoints: ServiceEndpoint[]\n  ): Promise<{ endpoint: ServiceEndpoint; reason: string; confidence: number }> {\n    // AI-powered endpoint selection considering multiple factors\n    const scoredEndpoints = endpoints.map(endpoint => {\n      let score = 0;\n      \n      // Performance score (40% weight)\n      const responseTimeScore = Math.max(0, 100 - endpoint.health.responseTime / 10);\n      score += responseTimeScore * 0.4;\n      \n      // Capacity score (25% weight)\n      const capacityScore = Math.max(0, 100 - (\n        endpoint.capacity.cpu + endpoint.capacity.memory\n      ) / 2);\n      score += capacityScore * 0.25;\n      \n      // Geographic proximity score (20% weight)\n      const geoScore = this.calculateGeographicScore(request, endpoint);\n      score += geoScore * 0.2;\n      \n      // Load score (15% weight)\n      const loadScore = Math.max(0, 100 - (endpoint.currentConnections / endpoint.maxConnections) * 100);\n      score += loadScore * 0.15;\n      \n      // Session affinity bonus\n      if (request.sessionId && this.sessionAffinity.get(request.sessionId) === endpoint.id) {\n        score += 10; // Bonus for session affinity\n      }\n      \n      return { endpoint, score };\n    });\n\n    // Sort by score and select the best\n    scoredEndpoints.sort((a, b) => b.score - a.score);\n    const selected = scoredEndpoints[0];\n    \n    // Update session affinity if needed\n    if (request.sessionId && this.loadBalancingStrategy.stickySession) {\n      this.sessionAffinity.set(request.sessionId, selected.endpoint.id);\n    }\n\n    return {\n      endpoint: selected.endpoint,\n      reason: `AI-optimized selection (score: ${selected.score.toFixed(1)})`,\n      confidence: Math.min(selected.score / 100, 1),\n    };\n  }\n\n  private leastConnectionsSelection(endpoints: ServiceEndpoint[]): ServiceEndpoint {\n    return endpoints.reduce((min, current) => \n      current.currentConnections < min.currentConnections ? current : min\n    );\n  }\n\n  private weightedRoundRobinSelection(endpoints: ServiceEndpoint[]): ServiceEndpoint {\n    // Simple weighted selection based on endpoint weights\n    const totalWeight = endpoints.reduce((sum, ep) => sum + ep.weight, 0);\n    const random = Math.random() * totalWeight;\n    \n    let currentWeight = 0;\n    for (const endpoint of endpoints) {\n      currentWeight += endpoint.weight;\n      if (random <= currentWeight) {\n        return endpoint;\n      }\n    }\n    \n    return endpoints[0]; // Fallback\n  }\n\n  private roundRobinSelection(endpoints: ServiceEndpoint[]): ServiceEndpoint {\n    // Simple round robin (would maintain state in real implementation)\n    const index = Date.now() % endpoints.length;\n    return endpoints[index];\n  }\n\n  private ipHashSelection(request: TrafficRequest, endpoints: ServiceEndpoint[]): ServiceEndpoint {\n    // Hash-based selection for session consistency\n    const hash = this.simpleHash(request.headers['x-forwarded-for'] || 'unknown');\n    const index = hash % endpoints.length;\n    return endpoints[index];\n  }\n\n  private calculateGeographicScore(request: TrafficRequest, endpoint: ServiceEndpoint): number {\n    if (!request.userLocation || !this.loadBalancingStrategy.geoRouting) {\n      return 50; // Neutral score\n    }\n\n    // Simple geographic scoring (would use actual distance calculation)\n    const regionMap: Record<string, string[]> = {\n      'us-east-1': ['US', 'CA'],\n      'eu-west-1': ['GB', 'FR', 'DE', 'ES', 'IT'],\n      'ap-southeast-1': ['SG', 'MY', 'TH', 'ID'],\n    };\n\n    const endpointCountries = regionMap[endpoint.region] || [];\n    if (endpointCountries.includes(request.userLocation.country)) {\n      return 100; // Perfect match\n    }\n\n    return 30; // Distant region\n  }\n\n  private estimateResponseTime(endpoint: ServiceEndpoint, request: TrafficRequest): number {\n    // Base response time from health metrics\n    let estimatedTime = endpoint.health.responseTime;\n    \n    // Add load factor\n    const loadFactor = endpoint.currentConnections / endpoint.maxConnections;\n    estimatedTime += loadFactor * 50; // Up to 50ms additional latency\n    \n    // Add request type factor\n    const typeFactors: Record<string, number> = {\n      'api': 1.0,\n      'cdn': 0.3,\n      'edge_function': 0.5,\n    };\n    estimatedTime *= typeFactors[request.type] || 1.0;\n    \n    return Math.round(estimatedTime);\n  }\n\n  private determineCacheStrategy(\n    request: TrafficRequest,\n    endpoint: ServiceEndpoint\n  ): 'bypass' | 'cache' | 'cache_and_forward' {\n    if (request.type === 'cdn') return 'cache';\n    if (request.method === 'GET' && request.type === 'api') return 'cache_and_forward';\n    return 'bypass';\n  }\n\n  private updateEndpointMetrics(endpointId: string, request: TrafficRequest): void {\n    const endpoint = this.serviceEndpoints.get(endpointId);\n    if (!endpoint) return;\n\n    // Increment current connections\n    endpoint.currentConnections++;\n    \n    // Update metrics (simplified)\n    endpoint.metrics.requestsPerSecond += 0.1; // Increment RPS\n  }\n\n  private trackRoutingDecision(request: TrafficRequest, decision: RoutingDecision): void {\n    this.trafficHistory.push({\n      timestamp: Date.now(),\n      request,\n      response: { endpointId: decision.endpoint.id, responseTime: decision.estimatedResponseTime },\n    });\n    \n    // Limit history size\n    if (this.trafficHistory.length > 10000) {\n      this.trafficHistory.splice(0, 1000); // Remove oldest 1000 entries\n    }\n  }\n\n  private getFallbackRoutingDecision(request: TrafficRequest): RoutingDecision {\n    // Return a fallback decision when routing fails\n    const fallbackEndpoint: ServiceEndpoint = {\n      id: 'fallback',\n      url: 'https://fallback.acemind.app',\n      region: 'global',\n      type: request.type as any,\n      weight: 1,\n      maxConnections: 100,\n      currentConnections: 0,\n      health: {\n        status: 'healthy',\n        lastCheck: Date.now(),\n        responseTime: 500,\n        errorRate: 0,\n        uptime: 99,\n      },\n      capacity: { cpu: 50, memory: 50, bandwidth: 100, storage: 50 },\n      metrics: {\n        requestsPerSecond: 1,\n        averageResponseTime: 500,\n        throughput: 10,\n        successRate: 99,\n      },\n    };\n\n    return {\n      endpoint: fallbackEndpoint,\n      reason: 'Fallback endpoint due to routing failure',\n      confidence: 0.1,\n      fallbackEndpoints: [],\n      estimatedResponseTime: 500,\n      cacheStrategy: 'bypass',\n    };\n  }\n\n  private isCircuitBreakerOpen(endpointId: string): boolean {\n    if (!this.loadBalancingStrategy.circuitBreakerEnabled) return false;\n    \n    const breaker = this.circuitBreakers.get(endpointId);\n    if (!breaker) return false;\n    \n    if (breaker.isOpen) {\n      // Check if timeout has passed\n      if (Date.now() - breaker.lastFailure > this.CIRCUIT_BREAKER_TIMEOUT) {\n        breaker.isOpen = false;\n        breaker.failures = 0;\n        return false;\n      }\n      return true;\n    }\n    \n    return false;\n  }\n\n  private startHealthMonitoring(): void {\n    setInterval(() => {\n      this.performHealthChecks();\n    }, this.loadBalancingStrategy.healthCheckInterval);\n  }\n\n  private async performHealthChecks(): Promise<void> {\n    const endpoints = Array.from(this.serviceEndpoints.values());\n    \n    for (const endpoint of endpoints) {\n      try {\n        const healthResult = await this.checkEndpointHealth(endpoint);\n        this.updateEndpointHealth(endpoint, healthResult);\n      } catch (error) {\n        console.error(`Health check failed for ${endpoint.id}:`, error);\n        this.markEndpointUnhealthy(endpoint);\n      }\n    }\n  }\n\n  private async checkEndpointHealth(endpoint: ServiceEndpoint): Promise<{\n    responseTime: number;\n    status: number;\n    success: boolean;\n  }> {\n    // Simulate health check (in real implementation, would make actual HTTP request)\n    const responseTime = Math.random() * 200 + 50; // 50-250ms\n    const success = Math.random() > 0.05; // 95% success rate\n    \n    return {\n      responseTime,\n      status: success ? 200 : 500,\n      success,\n    };\n  }\n\n  private updateEndpointHealth(endpoint: ServiceEndpoint, healthResult: any): void {\n    endpoint.health.lastCheck = Date.now();\n    endpoint.health.responseTime = healthResult.responseTime;\n    \n    if (healthResult.success) {\n      endpoint.health.status = 'healthy';\n      // Reset circuit breaker\n      const breaker = this.circuitBreakers.get(endpoint.id);\n      if (breaker) {\n        breaker.failures = 0;\n      }\n    } else {\n      this.markEndpointUnhealthy(endpoint);\n    }\n  }\n\n  private markEndpointUnhealthy(endpoint: ServiceEndpoint): void {\n    endpoint.health.status = 'unhealthy';\n    \n    // Update circuit breaker\n    if (!this.circuitBreakers.has(endpoint.id)) {\n      this.circuitBreakers.set(endpoint.id, { isOpen: false, failures: 0, lastFailure: 0 });\n    }\n    \n    const breaker = this.circuitBreakers.get(endpoint.id)!;\n    breaker.failures++;\n    breaker.lastFailure = Date.now();\n    \n    if (breaker.failures >= this.CIRCUIT_BREAKER_THRESHOLD) {\n      breaker.isOpen = true;\n      console.warn(`Circuit breaker opened for endpoint: ${endpoint.id}`);\n    }\n  }\n\n  private startTrafficAnalysis(): void {\n    // Analyze traffic patterns every 5 minutes\n    setInterval(() => {\n      this.analyzeTrafficPatterns();\n    }, 300000);\n  }\n\n  private analyzeTrafficPatterns(): void {\n    // Analyze recent traffic for optimization opportunities\n    const recentTraffic = this.trafficHistory.filter(\n      entry => Date.now() - entry.timestamp < 3600000 // Last hour\n    );\n    \n    if (recentTraffic.length > 0) {\n      console.log(`Analyzed ${recentTraffic.length} traffic entries for optimization`);\n    }\n  }\n\n  private simpleHash(str: string): number {\n    let hash = 0;\n    for (let i = 0; i < str.length; i++) {\n      const char = str.charCodeAt(i);\n      hash = ((hash << 5) - hash) + char;\n      hash = hash & hash; // Convert to 32-bit integer\n    }\n    return Math.abs(hash);\n  }\n}\n\n/**\n * Traffic Prediction Model\n */\nclass TrafficPredictionModel {\n  async initialize(): Promise<void> {\n    console.log('Initialized traffic prediction model');\n  }\n\n  async predict(\n    trafficHistory: Array<{ timestamp: number; request: TrafficRequest; response: any }>,\n    timeHorizon: number\n  ): Promise<TrafficPrediction> {\n    // Simple prediction logic (would use ML in real implementation)\n    const recentTraffic = trafficHistory.filter(\n      entry => Date.now() - entry.timestamp < 3600000 // Last hour\n    );\n\n    const expectedRequests = recentTraffic.length * (timeHorizon / 60); // Scale to time horizon\n    \n    return {\n      timeHorizon,\n      expectedRequests,\n      peakTimes: [\n        { start: Date.now() + 1800000, end: Date.now() + 3600000, intensity: 1.5 }\n      ],\n      regionalDistribution: { 'us-east-1': 0.6, 'eu-west-1': 0.3, 'ap-southeast-1': 0.1 },\n      typeDistribution: { 'api': 0.7, 'cdn': 0.2, 'edge_function': 0.1 },\n      confidence: 0.75,\n    };\n  }\n}\n\n// Export singleton instance\nexport const smartLoadBalancer = new SmartLoadBalancer();\nexport default smartLoadBalancer;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,SAASA,kBAAkB;AAA8B,IA4EnDC,iBAAiB;EAYrB,SAAAA,kBAAA,EAAc;IAAAC,eAAA,OAAAD,iBAAA;IAAA,KAXNE,gBAAgB,IAAAC,cAAA,GAAAC,CAAA,OAAiC,IAAIC,GAAG,CAAC,CAAC;IAAA,KAE1DC,cAAc,IAAAH,cAAA,GAAAC,CAAA,OAAyE,EAAE;IAAA,KACzFG,eAAe,IAAAJ,cAAA,GAAAC,CAAA,OAA4E,IAAIC,GAAG,CAAC,CAAC;IAAA,KACpGG,eAAe,IAAAL,cAAA,GAAAC,CAAA,OAAwB,IAAIC,GAAG,CAAC,CAAC;IAAA,KAGvCI,qBAAqB,IAAAN,cAAA,GAAAC,CAAA,OAAG,KAAK;IAAA,KAC7BM,yBAAyB,IAAAP,cAAA,GAAAC,CAAA,OAAG,CAAC;IAAA,KAC7BO,uBAAuB,IAAAR,cAAA,GAAAC,CAAA,OAAG,KAAK;IAAAD,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAC,CAAA;IAG9C,IAAI,CAACS,qBAAqB,GAAG;MAC3BC,SAAS,EAAE,cAAc;MACzBC,mBAAmB,EAAE,IAAI,CAACN,qBAAqB;MAC/CO,iBAAiB,EAAE,GAAG;MACtBC,qBAAqB,EAAE,IAAI;MAC3BC,aAAa,EAAE,IAAI;MACnBC,UAAU,EAAE;IACd,CAAC;IAAChB,cAAA,GAAAC,CAAA;IAEF,IAAI,CAACgB,sBAAsB,GAAG,IAAIC,sBAAsB,CAAC,CAAC;IAAClB,cAAA,GAAAC,CAAA;IAC3D,IAAI,CAACkB,sBAAsB,CAAC,CAAC;EAC/B;EAAC,OAAAC,YAAA,CAAAvB,iBAAA;IAAAwB,GAAA;IAAAC,KAAA;MAAA,IAAAC,uBAAA,GAAAC,iBAAA,CAKD,aAAsD;QAAAxB,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QACpD,IAAI;UAAAD,cAAA,GAAAC,CAAA;UAEF,MAAM,IAAI,CAACwB,wBAAwB,CAAC,CAAC;UAACzB,cAAA,GAAAC,CAAA;UAGtC,IAAI,CAACyB,qBAAqB,CAAC,CAAC;UAAC1B,cAAA,GAAAC,CAAA;UAG7B,MAAM,IAAI,CAACgB,sBAAsB,CAACU,UAAU,CAAC,CAAC;UAAC3B,cAAA,GAAAC,CAAA;UAG/C,IAAI,CAAC2B,oBAAoB,CAAC,CAAC;UAAC5B,cAAA,GAAAC,CAAA;UAE5B4B,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC7D,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAA/B,cAAA,GAAAC,CAAA;UACd4B,OAAO,CAACE,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACnE;MACF,CAAC;MAAA,SAlBaZ,sBAAsBA,CAAA;QAAA,OAAAI,uBAAA,CAAAS,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAtBd,sBAAsB;IAAA;EAAA;IAAAE,GAAA;IAAAC,KAAA;MAAA,IAAAY,aAAA,GAAAV,iBAAA,CAuBpC,WAAmBW,OAAuB,EAA4B;QAAAnC,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QACpE,IAAI;UACF,IAAMmC,SAAS,IAAApC,cAAA,GAAAC,CAAA,QAAGoC,IAAI,CAACC,GAAG,CAAC,CAAC;UAG5B,IAAMC,kBAAkB,IAAAvC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACuC,qBAAqB,CAACL,OAAO,CAACM,IAAI,CAAC;UAACzC,cAAA,GAAAC,CAAA;UAEpE,IAAIsC,kBAAkB,CAACG,MAAM,KAAK,CAAC,EAAE;YAAA1C,cAAA,GAAA2C,CAAA;YAAA3C,cAAA,GAAAC,CAAA;YACnC,MAAM,IAAI2C,KAAK,CAAC,oCAAoCT,OAAO,CAACM,IAAI,EAAE,CAAC;UACrE,CAAC;YAAAzC,cAAA,GAAA2C,CAAA;UAAA;UAGD,IAAME,eAAe,IAAA7C,cAAA,GAAAC,CAAA,cAAS,IAAI,CAAC6C,qBAAqB,CAACX,OAAO,EAAEI,kBAAkB,CAAC;UAACvC,cAAA,GAAAC,CAAA;UAGtF,IAAI,CAAC8C,qBAAqB,CAACF,eAAe,CAACG,QAAQ,CAACC,EAAE,EAAEd,OAAO,CAAC;UAACnC,cAAA,GAAAC,CAAA;UAGjE,IAAI,CAACiD,oBAAoB,CAACf,OAAO,EAAEU,eAAe,CAAC;UAEnD,IAAMM,cAAc,IAAAnD,cAAA,GAAAC,CAAA,QAAGoC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;UAACpC,cAAA,GAAAC,CAAA;UAC9CL,kBAAkB,CAACwD,kBAAkB,CAAC,uBAAuB,EAAED,cAAc,CAAC;UAACnD,cAAA,GAAAC,CAAA;UAE/E,OAAO4C,eAAe;QAExB,CAAC,CAAC,OAAOd,KAAK,EAAE;UAAA/B,cAAA,GAAAC,CAAA;UACd4B,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAAC/B,cAAA,GAAAC,CAAA;UACjD,OAAO,IAAI,CAACoD,0BAA0B,CAAClB,OAAO,CAAC;QACjD;MACF,CAAC;MAAA,SA7BKmB,YAAYA,CAAAC,EAAA;QAAA,OAAArB,aAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAZqB,YAAY;IAAA;EAAA;IAAAjC,GAAA;IAAAC,KAAA;MAAA,IAAAkC,sBAAA,GAAAhC,iBAAA,CAkClB,aAAkF;QAAA,IAAtDiC,WAAmB,GAAAxB,SAAA,CAAAS,MAAA,QAAAT,SAAA,QAAAyB,SAAA,GAAAzB,SAAA,OAAAjC,cAAA,GAAA2C,CAAA,UAAG,EAAE;QAAA3C,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QAClD,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACF,aAAa,IAAI,CAACgB,sBAAsB,CAAC0C,OAAO,CAAC,IAAI,CAACxD,cAAc,EAAEsD,WAAW,CAAC;QACpF,CAAC,CAAC,OAAO1B,KAAK,EAAE;UAAA/B,cAAA,GAAAC,CAAA;UACd4B,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAAC/B,cAAA,GAAAC,CAAA;UAC3D,OAAO;YACLwD,WAAW,EAAXA,WAAW;YACXG,gBAAgB,EAAE,CAAC;YACnBC,SAAS,EAAE,EAAE;YACbC,oBAAoB,EAAE,CAAC,CAAC;YACxBC,gBAAgB,EAAE,CAAC,CAAC;YACpBC,UAAU,EAAE;UACd,CAAC;QACH;MACF,CAAC;MAAA,SAdKC,qBAAqBA,CAAA;QAAA,OAAAT,sBAAA,CAAAxB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBgC,qBAAqB;IAAA;EAAA;IAAA5C,GAAA;IAAAC,KAAA,EAmB3B,SAAA4C,uBAAuBA,CAAA,EAQrB;MAAAlE,cAAA,GAAAS,CAAA;MACA,IAAM0D,SAAS,IAAAnE,cAAA,GAAAC,CAAA,QAAGmE,KAAK,CAACC,IAAI,CAAC,IAAI,CAACtE,gBAAgB,CAACuE,MAAM,CAAC,CAAC,CAAC;MAC5D,IAAMC,gBAAgB,IAAAvE,cAAA,GAAAC,CAAA,QAAGkE,SAAS,CAACK,MAAM,CAAC,UAAAC,EAAE,EAAI;QAAAzE,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QAAA,OAAAwE,EAAE,CAACC,MAAM,CAACC,MAAM,KAAK,SAAS;MAAD,CAAC,CAAC;MAE/E,IAAMC,QAAQ,IAAA5E,cAAA,GAAAC,CAAA,QAAGkE,SAAS,CAACU,MAAM,CAAC,UAACC,GAAG,EAAEL,EAAE,EAAK;QAAAzE,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QAAA,OAAA6E,GAAG,GAAGL,EAAE,CAACM,OAAO,CAACC,iBAAiB;MAAD,CAAC,EAAE,CAAC,CAAC;MACrF,IAAMC,eAAe,IAAAjF,cAAA,GAAAC,CAAA,QAAGkE,SAAS,CAACzB,MAAM,GAAG,CAAC,IAAA1C,cAAA,GAAA2C,CAAA,UACxCwB,SAAS,CAACU,MAAM,CAAC,UAACC,GAAG,EAAEL,EAAE,EAAK;QAAAzE,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QAAA,OAAA6E,GAAG,GAAGL,EAAE,CAACC,MAAM,CAACQ,YAAY;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGf,SAAS,CAACzB,MAAM,KAAA1C,cAAA,GAAA2C,CAAA,UACjF,CAAC;MAGL,IAAMwC,aAAa,IAAAnF,cAAA,GAAAC,CAAA,QAAGkE,SAAS,CAACU,MAAM,CAAC,UAACC,GAAG,EAAEL,EAAE,EAAK;QAAAzE,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QAAA,OAAA6E,GAAG,GAAGL,EAAE,CAACM,OAAO,CAACC,iBAAiB,GAAG,EAAE;MAAD,CAAC,EAAE,CAAC,CAAC;MAC/F,IAAMI,kBAAkB,IAAApF,cAAA,GAAAC,CAAA,QAAGkE,SAAS,CAACU,MAAM,CAAC,UAACC,GAAG,EAAEL,EAAE,EAClD;QAAAzE,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QAAA,OAAA6E,GAAG,GAAIL,EAAE,CAACM,OAAO,CAACC,iBAAiB,GAAG,EAAE,GAAGP,EAAE,CAACM,OAAO,CAACM,WAAW,GAAG,GAAI;MAAD,CAAC,EAAE,CAAC,CAAC;MAC9E,IAAMC,SAAS,IAAAtF,cAAA,GAAAC,CAAA,QAAGkF,aAAa,GAAG,CAAC,IAAAnF,cAAA,GAAA2C,CAAA,UAAI,CAACwC,aAAa,GAAGC,kBAAkB,IAAID,aAAa,GAAI,GAAG,KAAAnF,cAAA,GAAA2C,CAAA,UAAG,CAAC;MAGtG,IAAMmB,oBAA4C,IAAA9D,cAAA,GAAAC,CAAA,QAAG,CAAC,CAAC;MAACD,cAAA,GAAAC,CAAA;MACxDkE,SAAS,CAACoB,OAAO,CAAC,UAAAd,EAAE,EAAI;QAAAzE,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QACtB6D,oBAAoB,CAACW,EAAE,CAACe,MAAM,CAAC,GAAG,CAAC,CAAAxF,cAAA,GAAA2C,CAAA,UAAAmB,oBAAoB,CAACW,EAAE,CAACe,MAAM,CAAC,MAAAxF,cAAA,GAAA2C,CAAA,UAAI,CAAC,KAAI,CAAC;MAC9E,CAAC,CAAC;MAGF,IAAM8C,mBAA2C,IAAAzF,cAAA,GAAAC,CAAA,QAAG,CAAC,CAAC;MAACD,cAAA,GAAAC,CAAA;MACvDkE,SAAS,CAACoB,OAAO,CAAC,UAAAd,EAAE,EAAI;QAAAzE,cAAA,GAAAS,CAAA;QACtB,IAAMiF,WAAW,IAAA1F,cAAA,GAAAC,CAAA,QAAIwE,EAAE,CAACkB,kBAAkB,GAAGlB,EAAE,CAACmB,cAAc,GAAI,GAAG;QAAC5F,cAAA,GAAAC,CAAA;QACtEwF,mBAAmB,CAAChB,EAAE,CAACxB,EAAE,CAAC,GAAGyC,WAAW;MAC1C,CAAC,CAAC;MAAC1F,cAAA,GAAAC,CAAA;MAEH,OAAO;QACL4F,cAAc,EAAE1B,SAAS,CAACzB,MAAM;QAChC6B,gBAAgB,EAAEA,gBAAgB,CAAC7B,MAAM;QACzCoD,mBAAmB,EAAEb,eAAe;QACpCc,sBAAsB,EAAEnB,QAAQ;QAChCU,SAAS,EAATA,SAAS;QACTxB,oBAAoB,EAApBA,oBAAoB;QACpB2B,mBAAmB,EAAnBA;MACF,CAAC;IACH;EAAC;IAAApE,GAAA;IAAAC,KAAA,EAKD,SAAA0E,cAAcA,CAACC,QAAwC,EAAQ;MAAAjG,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAC,CAAA;MAC7D,IAAI,CAACS,qBAAqB,GAAAwF,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAACzF,qBAAqB,EAAKuF,QAAQ,CAAE;MAACjG,cAAA,GAAAC,CAAA;MAC5E4B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACpB,qBAAqB,CAAC;IAC7E;EAAC;IAAAW,GAAA;IAAAC,KAAA,EAKD,SAAA8E,kBAAkBA,CAACpD,QAAqD,EAAQ;MAAAhD,cAAA,GAAAS,CAAA;MAC9E,IAAM4F,YAA6B,IAAArG,cAAA,GAAAC,CAAA,QAAAiG,MAAA,CAAAC,MAAA,KAC9BnD,QAAQ;QACX0B,MAAM,EAAE;UACNC,MAAM,EAAE,SAAS;UACjB2B,SAAS,EAAEjE,IAAI,CAACC,GAAG,CAAC,CAAC;UACrB4C,YAAY,EAAE,CAAC;UACfI,SAAS,EAAE,CAAC;UACZiB,MAAM,EAAE;QACV,CAAC;QACDxB,OAAO,EAAE;UACPC,iBAAiB,EAAE,CAAC;UACpBc,mBAAmB,EAAE,CAAC;UACtBU,UAAU,EAAE,CAAC;UACbnB,WAAW,EAAE;QACf;MAAC,GACF;MAACrF,cAAA,GAAAC,CAAA;MAEF,IAAI,CAACF,gBAAgB,CAAC0G,GAAG,CAACzD,QAAQ,CAACC,EAAE,EAAEoD,YAAY,CAAC;MAACrG,cAAA,GAAAC,CAAA;MACrD4B,OAAO,CAACC,GAAG,CAAC,2BAA2BkB,QAAQ,CAACC,EAAE,EAAE,CAAC;IACvD;EAAC;IAAA5B,GAAA;IAAAC,KAAA,EAKD,SAAAoF,qBAAqBA,CAACC,UAAkB,EAAQ;MAAA3G,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAC,CAAA;MAC9C,IAAI,CAACF,gBAAgB,CAAC6G,MAAM,CAACD,UAAU,CAAC;MAAC3G,cAAA,GAAAC,CAAA;MACzC,IAAI,CAACG,eAAe,CAACwG,MAAM,CAACD,UAAU,CAAC;MAAC3G,cAAA,GAAAC,CAAA;MACxC4B,OAAO,CAACC,GAAG,CAAC,6BAA6B6E,UAAU,EAAE,CAAC;IACxD;EAAC;IAAAtF,GAAA;IAAAC,KAAA;MAAA,IAAAuF,yBAAA,GAAArF,iBAAA,CAID,aAAwD;QAAAxB,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QAEtD,IAAI,CAACmG,kBAAkB,CAAC;UACtBnD,EAAE,EAAE,aAAa;UACjB6D,GAAG,EAAE,iCAAiC;UACtCtB,MAAM,EAAE,WAAW;UACnB/C,IAAI,EAAE,KAAK;UACXsE,MAAM,EAAE,GAAG;UACXnB,cAAc,EAAE,IAAI;UACpBD,kBAAkB,EAAE,CAAC;UACrBqB,QAAQ,EAAE;YAAEC,GAAG,EAAE,EAAE;YAAEC,MAAM,EAAE,EAAE;YAAEC,SAAS,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAG;QAChE,CAAC,CAAC;QAACpH,cAAA,GAAAC,CAAA;QAEH,IAAI,CAACmG,kBAAkB,CAAC;UACtBnD,EAAE,EAAE,aAAa;UACjB6D,GAAG,EAAE,iCAAiC;UACtCtB,MAAM,EAAE,WAAW;UACnB/C,IAAI,EAAE,KAAK;UACXsE,MAAM,EAAE,GAAG;UACXnB,cAAc,EAAE,GAAG;UACnBD,kBAAkB,EAAE,CAAC;UACrBqB,QAAQ,EAAE;YAAEC,GAAG,EAAE,EAAE;YAAEC,MAAM,EAAE,EAAE;YAAEC,SAAS,EAAE,GAAG;YAAEC,OAAO,EAAE;UAAG;QAC/D,CAAC,CAAC;QAACpH,cAAA,GAAAC,CAAA;QAGH,IAAI,CAACmG,kBAAkB,CAAC;UACtBnD,EAAE,EAAE,YAAY;UAChB6D,GAAG,EAAE,yBAAyB;UAC9BtB,MAAM,EAAE,QAAQ;UAChB/C,IAAI,EAAE,KAAK;UACXsE,MAAM,EAAE,GAAG;UACXnB,cAAc,EAAE,KAAK;UACrBD,kBAAkB,EAAE,CAAC;UACrBqB,QAAQ,EAAE;YAAEC,GAAG,EAAE,EAAE;YAAEC,MAAM,EAAE,EAAE;YAAEC,SAAS,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAG;QACjE,CAAC,CAAC;QAACpH,cAAA,GAAAC,CAAA;QAEH4B,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC7C,CAAC;MAAA,SArCaL,wBAAwBA,CAAA;QAAA,OAAAoF,yBAAA,CAAA7E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAxBR,wBAAwB;IAAA;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EAuCtC,SAAQkB,qBAAqBA,CAACC,IAAY,EAAqB;MAAA,IAAA4E,KAAA;MAAArH,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAC,CAAA;MAC7D,OAAOmE,KAAK,CAACC,IAAI,CAAC,IAAI,CAACtE,gBAAgB,CAACuE,MAAM,CAAC,CAAC,CAAC,CAC9CE,MAAM,CAAC,UAAAxB,QAAQ,EACd;QAAAhD,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QAAA,QAAAD,cAAA,GAAA2C,CAAA,UAAAK,QAAQ,CAACP,IAAI,KAAKA,IAAI,MAAAzC,cAAA,GAAA2C,CAAA,UACtBK,QAAQ,CAAC0B,MAAM,CAACC,MAAM,KAAK,SAAS,MAAA3E,cAAA,GAAA2C,CAAA,UACpC,CAAC0E,KAAI,CAACC,oBAAoB,CAACtE,QAAQ,CAACC,EAAE,CAAC,MAAAjD,cAAA,GAAA2C,CAAA,UACvCK,QAAQ,CAAC2C,kBAAkB,GAAG3C,QAAQ,CAAC4C,cAAc;MAAD,CACtD,CAAC;IACL;EAAC;IAAAvE,GAAA;IAAAC,KAAA;MAAA,IAAAiG,sBAAA,GAAA/F,iBAAA,CAED,WACEW,OAAuB,EACvBI,kBAAqC,EACX;QAAAvC,cAAA,GAAAS,CAAA;QAC1B,IAAI+G,gBAAiC;QACrC,IAAIC,MAAc;QAClB,IAAIzD,UAAkB;QAAChE,cAAA,GAAAC,CAAA;QAEvB,QAAQ,IAAI,CAACS,qBAAqB,CAACC,SAAS;UAC1C,KAAK,cAAc;YAAAX,cAAA,GAAA2C,CAAA;YAAA3C,cAAA,GAAAC,CAAA;YAAA,IAAAyH,qBAAA,SAC2C,IAAI,CAACC,oBAAoB,CAACxF,OAAO,EAAEI,kBAAkB,CAAC;YAArGiF,gBAAgB,GAAAE,qBAAA,CAA1B1E,QAAQ;YAAoByE,MAAM,GAAAC,qBAAA,CAAND,MAAM;YAAEzD,UAAU,GAAA0D,qBAAA,CAAV1D,UAAU;YAAAhE,cAAA,GAAAC,CAAA;YACjD;UAEF,KAAK,mBAAmB;YAAAD,cAAA,GAAA2C,CAAA;YAAA3C,cAAA,GAAAC,CAAA;YACtBuH,gBAAgB,GAAG,IAAI,CAACI,yBAAyB,CAACrF,kBAAkB,CAAC;YAACvC,cAAA,GAAAC,CAAA;YACtEwH,MAAM,GAAG,6BAA6B;YAACzH,cAAA,GAAAC,CAAA;YACvC+D,UAAU,GAAG,GAAG;YAAChE,cAAA,GAAAC,CAAA;YACjB;UAEF,KAAK,sBAAsB;YAAAD,cAAA,GAAA2C,CAAA;YAAA3C,cAAA,GAAAC,CAAA;YACzBuH,gBAAgB,GAAG,IAAI,CAACK,2BAA2B,CAACtF,kBAAkB,CAAC;YAACvC,cAAA,GAAAC,CAAA;YACxEwH,MAAM,GAAG,gCAAgC;YAACzH,cAAA,GAAAC,CAAA;YAC1C+D,UAAU,GAAG,GAAG;YAAChE,cAAA,GAAAC,CAAA;YACjB;UAEF,KAAK,SAAS;YAAAD,cAAA,GAAA2C,CAAA;YAAA3C,cAAA,GAAAC,CAAA;YACZuH,gBAAgB,GAAG,IAAI,CAACM,eAAe,CAAC3F,OAAO,EAAEI,kBAAkB,CAAC;YAACvC,cAAA,GAAAC,CAAA;YACrEwH,MAAM,GAAG,mBAAmB;YAACzH,cAAA,GAAAC,CAAA;YAC7B+D,UAAU,GAAG,GAAG;YAAChE,cAAA,GAAAC,CAAA;YACjB;UAEF;YAAAD,cAAA,GAAA2C,CAAA;YAAA3C,cAAA,GAAAC,CAAA;YACEuH,gBAAgB,GAAG,IAAI,CAACO,mBAAmB,CAACxF,kBAAkB,CAAC;YAACvC,cAAA,GAAAC,CAAA;YAChEwH,MAAM,GAAG,uBAAuB;YAACzH,cAAA,GAAAC,CAAA;YACjC+D,UAAU,GAAG,GAAG;QACpB;QAGA,IAAMgE,iBAAiB,IAAAhI,cAAA,GAAAC,CAAA,QAAGsC,kBAAkB,CACzCiC,MAAM,CAAC,UAAAC,EAAE,EAAI;UAAAzE,cAAA,GAAAS,CAAA;UAAAT,cAAA,GAAAC,CAAA;UAAA,OAAAwE,EAAE,CAACxB,EAAE,KAAKuE,gBAAgB,CAACvE,EAAE;QAAD,CAAC,CAAC,CAC3CgF,IAAI,CAAC,UAACC,CAAC,EAAEvF,CAAC,EAAK;UAAA3C,cAAA,GAAAS,CAAA;UAAAT,cAAA,GAAAC,CAAA;UAAA,OAAAiI,CAAC,CAACxD,MAAM,CAACQ,YAAY,GAAGvC,CAAC,CAAC+B,MAAM,CAACQ,YAAY;QAAD,CAAC,CAAC,CAC7DiD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAGd,IAAMC,qBAAqB,IAAApI,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACoI,oBAAoB,CAACb,gBAAgB,EAAErF,OAAO,CAAC;QAACnC,cAAA,GAAAC,CAAA;QAEnF,OAAO;UACL+C,QAAQ,EAAEwE,gBAAgB;UAC1BC,MAAM,EAANA,MAAM;UACNzD,UAAU,EAAVA,UAAU;UACVgE,iBAAiB,EAAjBA,iBAAiB;UACjBI,qBAAqB,EAArBA,qBAAqB;UACrBE,aAAa,EAAE,IAAI,CAACC,sBAAsB,CAACpG,OAAO,EAAEqF,gBAAgB;QACtE,CAAC;MACH,CAAC;MAAA,SAtDa1E,qBAAqBA,CAAA0F,GAAA,EAAAC,GAAA;QAAA,OAAAlB,sBAAA,CAAAvF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBa,qBAAqB;IAAA;EAAA;IAAAzB,GAAA;IAAAC,KAAA;MAAA,IAAAoH,qBAAA,GAAAlH,iBAAA,CAwDnC,WACEW,OAAuB,EACvBgC,SAA4B,EACgD;QAAA,IAAAwE,MAAA;QAAA3I,cAAA,GAAAS,CAAA;QAE5E,IAAMmI,eAAe,IAAA5I,cAAA,GAAAC,CAAA,QAAGkE,SAAS,CAAC0E,GAAG,CAAC,UAAA7F,QAAQ,EAAI;UAAAhD,cAAA,GAAAS,CAAA;UAChD,IAAIqI,KAAK,IAAA9I,cAAA,GAAAC,CAAA,QAAG,CAAC;UAGb,IAAM8I,iBAAiB,IAAA/I,cAAA,GAAAC,CAAA,QAAG+I,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAGjG,QAAQ,CAAC0B,MAAM,CAACQ,YAAY,GAAG,EAAE,CAAC;UAAClF,cAAA,GAAAC,CAAA;UAC/E6I,KAAK,IAAIC,iBAAiB,GAAG,GAAG;UAGhC,IAAMG,aAAa,IAAAlJ,cAAA,GAAAC,CAAA,QAAG+I,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CACtCjG,QAAQ,CAACgE,QAAQ,CAACC,GAAG,GAAGjE,QAAQ,CAACgE,QAAQ,CAACE,MAAM,IAC9C,CAAC,CAAC;UAAClH,cAAA,GAAAC,CAAA;UACP6I,KAAK,IAAII,aAAa,GAAG,IAAI;UAG7B,IAAMC,QAAQ,IAAAnJ,cAAA,GAAAC,CAAA,QAAG0I,MAAI,CAACS,wBAAwB,CAACjH,OAAO,EAAEa,QAAQ,CAAC;UAAChD,cAAA,GAAAC,CAAA;UAClE6I,KAAK,IAAIK,QAAQ,GAAG,GAAG;UAGvB,IAAME,SAAS,IAAArJ,cAAA,GAAAC,CAAA,QAAG+I,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAIjG,QAAQ,CAAC2C,kBAAkB,GAAG3C,QAAQ,CAAC4C,cAAc,GAAI,GAAG,CAAC;UAAC5F,cAAA,GAAAC,CAAA;UACnG6I,KAAK,IAAIO,SAAS,GAAG,IAAI;UAACrJ,cAAA,GAAAC,CAAA;UAG1B,IAAI,CAAAD,cAAA,GAAA2C,CAAA,UAAAR,OAAO,CAACmH,SAAS,MAAAtJ,cAAA,GAAA2C,CAAA,UAAIgG,MAAI,CAACtI,eAAe,CAACkJ,GAAG,CAACpH,OAAO,CAACmH,SAAS,CAAC,KAAKtG,QAAQ,CAACC,EAAE,GAAE;YAAAjD,cAAA,GAAA2C,CAAA;YAAA3C,cAAA,GAAAC,CAAA;YACpF6I,KAAK,IAAI,EAAE;UACb,CAAC;YAAA9I,cAAA,GAAA2C,CAAA;UAAA;UAAA3C,cAAA,GAAAC,CAAA;UAED,OAAO;YAAE+C,QAAQ,EAARA,QAAQ;YAAE8F,KAAK,EAALA;UAAM,CAAC;QAC5B,CAAC,CAAC;QAAC9I,cAAA,GAAAC,CAAA;QAGH2I,eAAe,CAACX,IAAI,CAAC,UAACC,CAAC,EAAEvF,CAAC,EAAK;UAAA3C,cAAA,GAAAS,CAAA;UAAAT,cAAA,GAAAC,CAAA;UAAA,OAAA0C,CAAC,CAACmG,KAAK,GAAGZ,CAAC,CAACY,KAAK;QAAD,CAAC,CAAC;QACjD,IAAMU,QAAQ,IAAAxJ,cAAA,GAAAC,CAAA,SAAG2I,eAAe,CAAC,CAAC,CAAC;QAAC5I,cAAA,GAAAC,CAAA;QAGpC,IAAI,CAAAD,cAAA,GAAA2C,CAAA,WAAAR,OAAO,CAACmH,SAAS,MAAAtJ,cAAA,GAAA2C,CAAA,WAAI,IAAI,CAACjC,qBAAqB,CAACK,aAAa,GAAE;UAAAf,cAAA,GAAA2C,CAAA;UAAA3C,cAAA,GAAAC,CAAA;UACjE,IAAI,CAACI,eAAe,CAACoG,GAAG,CAACtE,OAAO,CAACmH,SAAS,EAAEE,QAAQ,CAACxG,QAAQ,CAACC,EAAE,CAAC;QACnE,CAAC;UAAAjD,cAAA,GAAA2C,CAAA;QAAA;QAAA3C,cAAA,GAAAC,CAAA;QAED,OAAO;UACL+C,QAAQ,EAAEwG,QAAQ,CAACxG,QAAQ;UAC3ByE,MAAM,EAAE,kCAAkC+B,QAAQ,CAACV,KAAK,CAACW,OAAO,CAAC,CAAC,CAAC,GAAG;UACtEzF,UAAU,EAAEgF,IAAI,CAACU,GAAG,CAACF,QAAQ,CAACV,KAAK,GAAG,GAAG,EAAE,CAAC;QAC9C,CAAC;MACH,CAAC;MAAA,SAhDanB,oBAAoBA,CAAAgC,GAAA,EAAAC,GAAA;QAAA,OAAAlB,qBAAA,CAAA1G,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApB0F,oBAAoB;IAAA;EAAA;IAAAtG,GAAA;IAAAC,KAAA,EAkDlC,SAAQsG,yBAAyBA,CAACzD,SAA4B,EAAmB;MAAAnE,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAC,CAAA;MAC/E,OAAOkE,SAAS,CAACU,MAAM,CAAC,UAAC6E,GAAG,EAAEG,OAAO,EACnC;QAAA7J,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QAAA,OAAA4J,OAAO,CAAClE,kBAAkB,GAAG+D,GAAG,CAAC/D,kBAAkB,IAAA3F,cAAA,GAAA2C,CAAA,WAAGkH,OAAO,KAAA7J,cAAA,GAAA2C,CAAA,WAAG+G,GAAG;MAAD,CACpE,CAAC;IACH;EAAC;IAAArI,GAAA;IAAAC,KAAA,EAED,SAAQuG,2BAA2BA,CAAC1D,SAA4B,EAAmB;MAAAnE,cAAA,GAAAS,CAAA;MAEjF,IAAMqJ,WAAW,IAAA9J,cAAA,GAAAC,CAAA,SAAGkE,SAAS,CAACU,MAAM,CAAC,UAACC,GAAG,EAAEL,EAAE,EAAK;QAAAzE,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QAAA,OAAA6E,GAAG,GAAGL,EAAE,CAACsC,MAAM;MAAD,CAAC,EAAE,CAAC,CAAC;MACrE,IAAMgD,MAAM,IAAA/J,cAAA,GAAAC,CAAA,SAAG+I,IAAI,CAACe,MAAM,CAAC,CAAC,GAAGD,WAAW;MAE1C,IAAIE,aAAa,IAAAhK,cAAA,GAAAC,CAAA,SAAG,CAAC;MAACD,cAAA,GAAAC,CAAA;MACtB,KAAK,IAAM+C,QAAQ,IAAImB,SAAS,EAAE;QAAAnE,cAAA,GAAAC,CAAA;QAChC+J,aAAa,IAAIhH,QAAQ,CAAC+D,MAAM;QAAC/G,cAAA,GAAAC,CAAA;QACjC,IAAI8J,MAAM,IAAIC,aAAa,EAAE;UAAAhK,cAAA,GAAA2C,CAAA;UAAA3C,cAAA,GAAAC,CAAA;UAC3B,OAAO+C,QAAQ;QACjB,CAAC;UAAAhD,cAAA,GAAA2C,CAAA;QAAA;MACH;MAAC3C,cAAA,GAAAC,CAAA;MAED,OAAOkE,SAAS,CAAC,CAAC,CAAC;IACrB;EAAC;IAAA9C,GAAA;IAAAC,KAAA,EAED,SAAQyG,mBAAmBA,CAAC5D,SAA4B,EAAmB;MAAAnE,cAAA,GAAAS,CAAA;MAEzE,IAAMwJ,KAAK,IAAAjK,cAAA,GAAAC,CAAA,SAAGoC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG6B,SAAS,CAACzB,MAAM;MAAC1C,cAAA,GAAAC,CAAA;MAC5C,OAAOkE,SAAS,CAAC8F,KAAK,CAAC;IACzB;EAAC;IAAA5I,GAAA;IAAAC,KAAA,EAED,SAAQwG,eAAeA,CAAC3F,OAAuB,EAAEgC,SAA4B,EAAmB;MAAAnE,cAAA,GAAAS,CAAA;MAE9F,IAAMyJ,IAAI,IAAAlK,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACkK,UAAU,CAAC,CAAAnK,cAAA,GAAA2C,CAAA,WAAAR,OAAO,CAACiI,OAAO,CAAC,iBAAiB,CAAC,MAAApK,cAAA,GAAA2C,CAAA,WAAI,SAAS,EAAC;MAC7E,IAAMsH,KAAK,IAAAjK,cAAA,GAAAC,CAAA,SAAGiK,IAAI,GAAG/F,SAAS,CAACzB,MAAM;MAAC1C,cAAA,GAAAC,CAAA;MACtC,OAAOkE,SAAS,CAAC8F,KAAK,CAAC;IACzB;EAAC;IAAA5I,GAAA;IAAAC,KAAA,EAED,SAAQ8H,wBAAwBA,CAACjH,OAAuB,EAAEa,QAAyB,EAAU;MAAAhD,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAC,CAAA;MAC3F,IAAI,CAAAD,cAAA,GAAA2C,CAAA,YAACR,OAAO,CAACkI,YAAY,MAAArK,cAAA,GAAA2C,CAAA,WAAI,CAAC,IAAI,CAACjC,qBAAqB,CAACM,UAAU,GAAE;QAAAhB,cAAA,GAAA2C,CAAA;QAAA3C,cAAA,GAAAC,CAAA;QACnE,OAAO,EAAE;MACX,CAAC;QAAAD,cAAA,GAAA2C,CAAA;MAAA;MAGD,IAAM2H,SAAmC,IAAAtK,cAAA,GAAAC,CAAA,SAAG;QAC1C,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QACzB,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QAC3C,gBAAgB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;MAC3C,CAAC;MAED,IAAMsK,iBAAiB,IAAAvK,cAAA,GAAAC,CAAA,SAAG,CAAAD,cAAA,GAAA2C,CAAA,WAAA2H,SAAS,CAACtH,QAAQ,CAACwC,MAAM,CAAC,MAAAxF,cAAA,GAAA2C,CAAA,WAAI,EAAE;MAAC3C,cAAA,GAAAC,CAAA;MAC3D,IAAIsK,iBAAiB,CAACC,QAAQ,CAACrI,OAAO,CAACkI,YAAY,CAACI,OAAO,CAAC,EAAE;QAAAzK,cAAA,GAAA2C,CAAA;QAAA3C,cAAA,GAAAC,CAAA;QAC5D,OAAO,GAAG;MACZ,CAAC;QAAAD,cAAA,GAAA2C,CAAA;MAAA;MAAA3C,cAAA,GAAAC,CAAA;MAED,OAAO,EAAE;IACX;EAAC;IAAAoB,GAAA;IAAAC,KAAA,EAED,SAAQ+G,oBAAoBA,CAACrF,QAAyB,EAAEb,OAAuB,EAAU;MAAAnC,cAAA,GAAAS,CAAA;MAEvF,IAAIiK,aAAa,IAAA1K,cAAA,GAAAC,CAAA,SAAG+C,QAAQ,CAAC0B,MAAM,CAACQ,YAAY;MAGhD,IAAMyF,UAAU,IAAA3K,cAAA,GAAAC,CAAA,SAAG+C,QAAQ,CAAC2C,kBAAkB,GAAG3C,QAAQ,CAAC4C,cAAc;MAAC5F,cAAA,GAAAC,CAAA;MACzEyK,aAAa,IAAIC,UAAU,GAAG,EAAE;MAGhC,IAAMC,WAAmC,IAAA5K,cAAA,GAAAC,CAAA,SAAG;QAC1C,KAAK,EAAE,GAAG;QACV,KAAK,EAAE,GAAG;QACV,eAAe,EAAE;MACnB,CAAC;MAACD,cAAA,GAAAC,CAAA;MACFyK,aAAa,IAAI,CAAA1K,cAAA,GAAA2C,CAAA,WAAAiI,WAAW,CAACzI,OAAO,CAACM,IAAI,CAAC,MAAAzC,cAAA,GAAA2C,CAAA,WAAI,GAAG;MAAC3C,cAAA,GAAAC,CAAA;MAElD,OAAO+I,IAAI,CAAC6B,KAAK,CAACH,aAAa,CAAC;IAClC;EAAC;IAAArJ,GAAA;IAAAC,KAAA,EAED,SAAQiH,sBAAsBA,CAC5BpG,OAAuB,EACvBa,QAAyB,EACiB;MAAAhD,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAC,CAAA;MAC1C,IAAIkC,OAAO,CAACM,IAAI,KAAK,KAAK,EAAE;QAAAzC,cAAA,GAAA2C,CAAA;QAAA3C,cAAA,GAAAC,CAAA;QAAA,OAAO,OAAO;MAAA,CAAC;QAAAD,cAAA,GAAA2C,CAAA;MAAA;MAAA3C,cAAA,GAAAC,CAAA;MAC3C,IAAI,CAAAD,cAAA,GAAA2C,CAAA,WAAAR,OAAO,CAAC2I,MAAM,KAAK,KAAK,MAAA9K,cAAA,GAAA2C,CAAA,WAAIR,OAAO,CAACM,IAAI,KAAK,KAAK,GAAE;QAAAzC,cAAA,GAAA2C,CAAA;QAAA3C,cAAA,GAAAC,CAAA;QAAA,OAAO,mBAAmB;MAAA,CAAC;QAAAD,cAAA,GAAA2C,CAAA;MAAA;MAAA3C,cAAA,GAAAC,CAAA;MACnF,OAAO,QAAQ;IACjB;EAAC;IAAAoB,GAAA;IAAAC,KAAA,EAED,SAAQyB,qBAAqBA,CAAC4D,UAAkB,EAAExE,OAAuB,EAAQ;MAAAnC,cAAA,GAAAS,CAAA;MAC/E,IAAMuC,QAAQ,IAAAhD,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACF,gBAAgB,CAACwJ,GAAG,CAAC5C,UAAU,CAAC;MAAC3G,cAAA,GAAAC,CAAA;MACvD,IAAI,CAAC+C,QAAQ,EAAE;QAAAhD,cAAA,GAAA2C,CAAA;QAAA3C,cAAA,GAAAC,CAAA;QAAA;MAAM,CAAC;QAAAD,cAAA,GAAA2C,CAAA;MAAA;MAAA3C,cAAA,GAAAC,CAAA;MAGtB+C,QAAQ,CAAC2C,kBAAkB,EAAE;MAAC3F,cAAA,GAAAC,CAAA;MAG9B+C,QAAQ,CAAC+B,OAAO,CAACC,iBAAiB,IAAI,GAAG;IAC3C;EAAC;IAAA3D,GAAA;IAAAC,KAAA,EAED,SAAQ4B,oBAAoBA,CAACf,OAAuB,EAAE4I,QAAyB,EAAQ;MAAA/K,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAC,CAAA;MACrF,IAAI,CAACE,cAAc,CAAC6K,IAAI,CAAC;QACvBC,SAAS,EAAE5I,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBH,OAAO,EAAPA,OAAO;QACP+I,QAAQ,EAAE;UAAEvE,UAAU,EAAEoE,QAAQ,CAAC/H,QAAQ,CAACC,EAAE;UAAEiC,YAAY,EAAE6F,QAAQ,CAAC3C;QAAsB;MAC7F,CAAC,CAAC;MAACpI,cAAA,GAAAC,CAAA;MAGH,IAAI,IAAI,CAACE,cAAc,CAACuC,MAAM,GAAG,KAAK,EAAE;QAAA1C,cAAA,GAAA2C,CAAA;QAAA3C,cAAA,GAAAC,CAAA;QACtC,IAAI,CAACE,cAAc,CAACgL,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;MACrC,CAAC;QAAAnL,cAAA,GAAA2C,CAAA;MAAA;IACH;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EAED,SAAQ+B,0BAA0BA,CAAClB,OAAuB,EAAmB;MAAAnC,cAAA,GAAAS,CAAA;MAE3E,IAAM2K,gBAAiC,IAAApL,cAAA,GAAAC,CAAA,SAAG;QACxCgD,EAAE,EAAE,UAAU;QACd6D,GAAG,EAAE,8BAA8B;QACnCtB,MAAM,EAAE,QAAQ;QAChB/C,IAAI,EAAEN,OAAO,CAACM,IAAW;QACzBsE,MAAM,EAAE,CAAC;QACTnB,cAAc,EAAE,GAAG;QACnBD,kBAAkB,EAAE,CAAC;QACrBjB,MAAM,EAAE;UACNC,MAAM,EAAE,SAAS;UACjB2B,SAAS,EAAEjE,IAAI,CAACC,GAAG,CAAC,CAAC;UACrB4C,YAAY,EAAE,GAAG;UACjBI,SAAS,EAAE,CAAC;UACZiB,MAAM,EAAE;QACV,CAAC;QACDS,QAAQ,EAAE;UAAEC,GAAG,EAAE,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,SAAS,EAAE,GAAG;UAAEC,OAAO,EAAE;QAAG,CAAC;QAC9DrC,OAAO,EAAE;UACPC,iBAAiB,EAAE,CAAC;UACpBc,mBAAmB,EAAE,GAAG;UACxBU,UAAU,EAAE,EAAE;UACdnB,WAAW,EAAE;QACf;MACF,CAAC;MAACrF,cAAA,GAAAC,CAAA;MAEF,OAAO;QACL+C,QAAQ,EAAEoI,gBAAgB;QAC1B3D,MAAM,EAAE,0CAA0C;QAClDzD,UAAU,EAAE,GAAG;QACfgE,iBAAiB,EAAE,EAAE;QACrBI,qBAAqB,EAAE,GAAG;QAC1BE,aAAa,EAAE;MACjB,CAAC;IACH;EAAC;IAAAjH,GAAA;IAAAC,KAAA,EAED,SAAQgG,oBAAoBA,CAACX,UAAkB,EAAW;MAAA3G,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAC,CAAA;MACxD,IAAI,CAAC,IAAI,CAACS,qBAAqB,CAACI,qBAAqB,EAAE;QAAAd,cAAA,GAAA2C,CAAA;QAAA3C,cAAA,GAAAC,CAAA;QAAA,OAAO,KAAK;MAAA,CAAC;QAAAD,cAAA,GAAA2C,CAAA;MAAA;MAEpE,IAAM0I,OAAO,IAAArL,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACG,eAAe,CAACmJ,GAAG,CAAC5C,UAAU,CAAC;MAAC3G,cAAA,GAAAC,CAAA;MACrD,IAAI,CAACoL,OAAO,EAAE;QAAArL,cAAA,GAAA2C,CAAA;QAAA3C,cAAA,GAAAC,CAAA;QAAA,OAAO,KAAK;MAAA,CAAC;QAAAD,cAAA,GAAA2C,CAAA;MAAA;MAAA3C,cAAA,GAAAC,CAAA;MAE3B,IAAIoL,OAAO,CAACC,MAAM,EAAE;QAAAtL,cAAA,GAAA2C,CAAA;QAAA3C,cAAA,GAAAC,CAAA;QAElB,IAAIoC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG+I,OAAO,CAACE,WAAW,GAAG,IAAI,CAAC/K,uBAAuB,EAAE;UAAAR,cAAA,GAAA2C,CAAA;UAAA3C,cAAA,GAAAC,CAAA;UACnEoL,OAAO,CAACC,MAAM,GAAG,KAAK;UAACtL,cAAA,GAAAC,CAAA;UACvBoL,OAAO,CAACG,QAAQ,GAAG,CAAC;UAACxL,cAAA,GAAAC,CAAA;UACrB,OAAO,KAAK;QACd,CAAC;UAAAD,cAAA,GAAA2C,CAAA;QAAA;QAAA3C,cAAA,GAAAC,CAAA;QACD,OAAO,IAAI;MACb,CAAC;QAAAD,cAAA,GAAA2C,CAAA;MAAA;MAAA3C,cAAA,GAAAC,CAAA;MAED,OAAO,KAAK;IACd;EAAC;IAAAoB,GAAA;IAAAC,KAAA,EAED,SAAQI,qBAAqBA,CAAA,EAAS;MAAA,IAAA+J,MAAA;MAAAzL,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAC,CAAA;MACpCyL,WAAW,CAAC,YAAM;QAAA1L,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QAChBwL,MAAI,CAACE,mBAAmB,CAAC,CAAC;MAC5B,CAAC,EAAE,IAAI,CAACjL,qBAAqB,CAACE,mBAAmB,CAAC;IACpD;EAAC;IAAAS,GAAA;IAAAC,KAAA;MAAA,IAAAsK,oBAAA,GAAApK,iBAAA,CAED,aAAmD;QAAAxB,cAAA,GAAAS,CAAA;QACjD,IAAM0D,SAAS,IAAAnE,cAAA,GAAAC,CAAA,SAAGmE,KAAK,CAACC,IAAI,CAAC,IAAI,CAACtE,gBAAgB,CAACuE,MAAM,CAAC,CAAC,CAAC;QAACtE,cAAA,GAAAC,CAAA;QAE7D,KAAK,IAAM+C,QAAQ,IAAImB,SAAS,EAAE;UAAAnE,cAAA,GAAAC,CAAA;UAChC,IAAI;YACF,IAAM4L,YAAY,IAAA7L,cAAA,GAAAC,CAAA,eAAS,IAAI,CAAC6L,mBAAmB,CAAC9I,QAAQ,CAAC;YAAChD,cAAA,GAAAC,CAAA;YAC9D,IAAI,CAAC8L,oBAAoB,CAAC/I,QAAQ,EAAE6I,YAAY,CAAC;UACnD,CAAC,CAAC,OAAO9J,KAAK,EAAE;YAAA/B,cAAA,GAAAC,CAAA;YACd4B,OAAO,CAACE,KAAK,CAAC,2BAA2BiB,QAAQ,CAACC,EAAE,GAAG,EAAElB,KAAK,CAAC;YAAC/B,cAAA,GAAAC,CAAA;YAChE,IAAI,CAAC+L,qBAAqB,CAAChJ,QAAQ,CAAC;UACtC;QACF;MACF,CAAC;MAAA,SAZa2I,mBAAmBA,CAAA;QAAA,OAAAC,oBAAA,CAAA5J,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnB0J,mBAAmB;IAAA;EAAA;IAAAtK,GAAA;IAAAC,KAAA;MAAA,IAAA2K,oBAAA,GAAAzK,iBAAA,CAcjC,WAAkCwB,QAAyB,EAIxD;QAAAhD,cAAA,GAAAS,CAAA;QAED,IAAMyE,YAAY,IAAAlF,cAAA,GAAAC,CAAA,SAAG+I,IAAI,CAACe,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE;QAC7C,IAAMmC,OAAO,IAAAlM,cAAA,GAAAC,CAAA,SAAG+I,IAAI,CAACe,MAAM,CAAC,CAAC,GAAG,IAAI;QAAC/J,cAAA,GAAAC,CAAA;QAErC,OAAO;UACLiF,YAAY,EAAZA,YAAY;UACZP,MAAM,EAAEuH,OAAO,IAAAlM,cAAA,GAAA2C,CAAA,WAAG,GAAG,KAAA3C,cAAA,GAAA2C,CAAA,WAAG,GAAG;UAC3BuJ,OAAO,EAAPA;QACF,CAAC;MACH,CAAC;MAAA,SAdaJ,mBAAmBA,CAAAK,GAAA;QAAA,OAAAF,oBAAA,CAAAjK,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnB6J,mBAAmB;IAAA;EAAA;IAAAzK,GAAA;IAAAC,KAAA,EAgBjC,SAAQyK,oBAAoBA,CAAC/I,QAAyB,EAAE6I,YAAiB,EAAQ;MAAA7L,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAC,CAAA;MAC/E+C,QAAQ,CAAC0B,MAAM,CAAC4B,SAAS,GAAGjE,IAAI,CAACC,GAAG,CAAC,CAAC;MAACtC,cAAA,GAAAC,CAAA;MACvC+C,QAAQ,CAAC0B,MAAM,CAACQ,YAAY,GAAG2G,YAAY,CAAC3G,YAAY;MAAClF,cAAA,GAAAC,CAAA;MAEzD,IAAI4L,YAAY,CAACK,OAAO,EAAE;QAAAlM,cAAA,GAAA2C,CAAA;QAAA3C,cAAA,GAAAC,CAAA;QACxB+C,QAAQ,CAAC0B,MAAM,CAACC,MAAM,GAAG,SAAS;QAElC,IAAM0G,OAAO,IAAArL,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACG,eAAe,CAACmJ,GAAG,CAACvG,QAAQ,CAACC,EAAE,CAAC;QAACjD,cAAA,GAAAC,CAAA;QACtD,IAAIoL,OAAO,EAAE;UAAArL,cAAA,GAAA2C,CAAA;UAAA3C,cAAA,GAAAC,CAAA;UACXoL,OAAO,CAACG,QAAQ,GAAG,CAAC;QACtB,CAAC;UAAAxL,cAAA,GAAA2C,CAAA;QAAA;MACH,CAAC,MAAM;QAAA3C,cAAA,GAAA2C,CAAA;QAAA3C,cAAA,GAAAC,CAAA;QACL,IAAI,CAAC+L,qBAAqB,CAAChJ,QAAQ,CAAC;MACtC;IACF;EAAC;IAAA3B,GAAA;IAAAC,KAAA,EAED,SAAQ0K,qBAAqBA,CAAChJ,QAAyB,EAAQ;MAAAhD,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAC,CAAA;MAC7D+C,QAAQ,CAAC0B,MAAM,CAACC,MAAM,GAAG,WAAW;MAAC3E,cAAA,GAAAC,CAAA;MAGrC,IAAI,CAAC,IAAI,CAACG,eAAe,CAACgM,GAAG,CAACpJ,QAAQ,CAACC,EAAE,CAAC,EAAE;QAAAjD,cAAA,GAAA2C,CAAA;QAAA3C,cAAA,GAAAC,CAAA;QAC1C,IAAI,CAACG,eAAe,CAACqG,GAAG,CAACzD,QAAQ,CAACC,EAAE,EAAE;UAAEqI,MAAM,EAAE,KAAK;UAAEE,QAAQ,EAAE,CAAC;UAAED,WAAW,EAAE;QAAE,CAAC,CAAC;MACvF,CAAC;QAAAvL,cAAA,GAAA2C,CAAA;MAAA;MAED,IAAM0I,OAAO,IAAArL,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACG,eAAe,CAACmJ,GAAG,CAACvG,QAAQ,CAACC,EAAE,CAAC,CAAC;MAACjD,cAAA,GAAAC,CAAA;MACvDoL,OAAO,CAACG,QAAQ,EAAE;MAACxL,cAAA,GAAAC,CAAA;MACnBoL,OAAO,CAACE,WAAW,GAAGlJ,IAAI,CAACC,GAAG,CAAC,CAAC;MAACtC,cAAA,GAAAC,CAAA;MAEjC,IAAIoL,OAAO,CAACG,QAAQ,IAAI,IAAI,CAACjL,yBAAyB,EAAE;QAAAP,cAAA,GAAA2C,CAAA;QAAA3C,cAAA,GAAAC,CAAA;QACtDoL,OAAO,CAACC,MAAM,GAAG,IAAI;QAACtL,cAAA,GAAAC,CAAA;QACtB4B,OAAO,CAACwK,IAAI,CAAC,wCAAwCrJ,QAAQ,CAACC,EAAE,EAAE,CAAC;MACrE,CAAC;QAAAjD,cAAA,GAAA2C,CAAA;MAAA;IACH;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EAED,SAAQM,oBAAoBA,CAAA,EAAS;MAAA,IAAA0K,MAAA;MAAAtM,cAAA,GAAAS,CAAA;MAAAT,cAAA,GAAAC,CAAA;MAEnCyL,WAAW,CAAC,YAAM;QAAA1L,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QAChBqM,MAAI,CAACC,sBAAsB,CAAC,CAAC;MAC/B,CAAC,EAAE,MAAM,CAAC;IACZ;EAAC;IAAAlL,GAAA;IAAAC,KAAA,EAED,SAAQiL,sBAAsBA,CAAA,EAAS;MAAAvM,cAAA,GAAAS,CAAA;MAErC,IAAM+L,aAAa,IAAAxM,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACE,cAAc,CAACqE,MAAM,CAC9C,UAAAiI,KAAK,EAAI;QAAAzM,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QAAA,OAAAoC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGmK,KAAK,CAACxB,SAAS,GAAG,OAAO;MAAD,CAChD,CAAC;MAACjL,cAAA,GAAAC,CAAA;MAEF,IAAIuM,aAAa,CAAC9J,MAAM,GAAG,CAAC,EAAE;QAAA1C,cAAA,GAAA2C,CAAA;QAAA3C,cAAA,GAAAC,CAAA;QAC5B4B,OAAO,CAACC,GAAG,CAAC,YAAY0K,aAAa,CAAC9J,MAAM,mCAAmC,CAAC;MAClF,CAAC;QAAA1C,cAAA,GAAA2C,CAAA;MAAA;IACH;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EAED,SAAQ6I,UAAUA,CAACuC,GAAW,EAAU;MAAA1M,cAAA,GAAAS,CAAA;MACtC,IAAIyJ,IAAI,IAAAlK,cAAA,GAAAC,CAAA,SAAG,CAAC;MAACD,cAAA,GAAAC,CAAA;MACb,KAAK,IAAI0M,CAAC,IAAA3M,cAAA,GAAAC,CAAA,SAAG,CAAC,GAAE0M,CAAC,GAAGD,GAAG,CAAChK,MAAM,EAAEiK,CAAC,EAAE,EAAE;QACnC,IAAMC,IAAI,IAAA5M,cAAA,GAAAC,CAAA,SAAGyM,GAAG,CAACG,UAAU,CAACF,CAAC,CAAC;QAAC3M,cAAA,GAAAC,CAAA;QAC/BiK,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAI0C,IAAI;QAAC5M,cAAA,GAAAC,CAAA;QACnCiK,IAAI,GAAGA,IAAI,GAAGA,IAAI;MACpB;MAAClK,cAAA,GAAAC,CAAA;MACD,OAAO+I,IAAI,CAAC8D,GAAG,CAAC5C,IAAI,CAAC;IACvB;EAAC;AAAA;AAAA,IAMGhJ,sBAAsB;EAAA,SAAAA,uBAAA;IAAApB,eAAA,OAAAoB,sBAAA;EAAA;EAAA,OAAAE,YAAA,CAAAF,sBAAA;IAAAG,GAAA;IAAAC,KAAA;MAAA,IAAAyL,WAAA,GAAAvL,iBAAA,CAC1B,aAAkC;QAAAxB,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAC,CAAA;QAChC4B,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACrD,CAAC;MAAA,SAFKH,UAAUA,CAAA;QAAA,OAAAoL,WAAA,CAAA/K,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVN,UAAU;IAAA;EAAA;IAAAN,GAAA;IAAAC,KAAA;MAAA,IAAA0L,QAAA,GAAAxL,iBAAA,CAIhB,WACErB,cAAoF,EACpFsD,WAAmB,EACS;QAAAzD,cAAA,GAAAS,CAAA;QAE5B,IAAM+L,aAAa,IAAAxM,cAAA,GAAAC,CAAA,SAAGE,cAAc,CAACqE,MAAM,CACzC,UAAAiI,KAAK,EAAI;UAAAzM,cAAA,GAAAS,CAAA;UAAAT,cAAA,GAAAC,CAAA;UAAA,OAAAoC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGmK,KAAK,CAACxB,SAAS,GAAG,OAAO;QAAD,CAChD,CAAC;QAED,IAAMrH,gBAAgB,IAAA5D,cAAA,GAAAC,CAAA,SAAGuM,aAAa,CAAC9J,MAAM,IAAIe,WAAW,GAAG,EAAE,CAAC;QAACzD,cAAA,GAAAC,CAAA;QAEnE,OAAO;UACLwD,WAAW,EAAXA,WAAW;UACXG,gBAAgB,EAAhBA,gBAAgB;UAChBC,SAAS,EAAE,CACT;YAAEoJ,KAAK,EAAE5K,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO;YAAE4K,GAAG,EAAE7K,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO;YAAE6K,SAAS,EAAE;UAAI,CAAC,CAC3E;UACDrJ,oBAAoB,EAAE;YAAE,WAAW,EAAE,GAAG;YAAE,WAAW,EAAE,GAAG;YAAE,gBAAgB,EAAE;UAAI,CAAC;UACnFC,gBAAgB,EAAE;YAAE,KAAK,EAAE,GAAG;YAAE,KAAK,EAAE,GAAG;YAAE,eAAe,EAAE;UAAI,CAAC;UAClEC,UAAU,EAAE;QACd,CAAC;MACH,CAAC;MAAA,SArBKL,OAAOA,CAAAyJ,GAAA,EAAAC,GAAA;QAAA,OAAAL,QAAA,CAAAhL,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAP0B,OAAO;IAAA;EAAA;AAAA;AAyBf,OAAO,IAAM2J,iBAAiB,IAAAtN,cAAA,GAAAC,CAAA,SAAG,IAAIJ,iBAAiB,CAAC,CAAC;AACxD,eAAeyN,iBAAiB", "ignoreList": []}