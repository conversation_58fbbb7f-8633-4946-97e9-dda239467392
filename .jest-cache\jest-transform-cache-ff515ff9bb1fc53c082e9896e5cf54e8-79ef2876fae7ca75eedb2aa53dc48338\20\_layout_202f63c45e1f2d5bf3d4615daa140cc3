6172179bbae23f89c71b497d00f015b6
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_a7pt6sn8h() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\_layout.tsx";
  var hash = "0a379f96b8444e00a9595768fa560fc740a161ca";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\_layout.tsx",
    statementMap: {
      "0": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 36
        }
      },
      "1": {
        start: {
          line: 13,
          column: 2
        },
        end: {
          line: 13,
          column: 22
        }
      },
      "2": {
        start: {
          line: 15,
          column: 35
        },
        end: {
          line: 20,
          column: 4
        }
      },
      "3": {
        start: {
          line: 22,
          column: 2
        },
        end: {
          line: 26,
          column: 31
        }
      },
      "4": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 25,
          column: 5
        }
      },
      "5": {
        start: {
          line: 24,
          column: 6
        },
        end: {
          line: 24,
          column: 31
        }
      },
      "6": {
        start: {
          line: 28,
          column: 2
        },
        end: {
          line: 30,
          column: 3
        }
      },
      "7": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 16
        }
      },
      "8": {
        start: {
          line: 32,
          column: 2
        },
        end: {
          line: 65,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "RootLayout",
        decl: {
          start: {
            line: 12,
            column: 24
          },
          end: {
            line: 12,
            column: 34
          }
        },
        loc: {
          start: {
            line: 12,
            column: 37
          },
          end: {
            line: 66,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 13
          }
        },
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 26,
            column: 3
          }
        },
        line: 22
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 23,
            column: 4
          },
          end: {
            line: 25,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 4
          },
          end: {
            line: 25,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "1": {
        loc: {
          start: {
            line: 23,
            column: 8
          },
          end: {
            line: 23,
            column: 32
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 8
          },
          end: {
            line: 23,
            column: 19
          }
        }, {
          start: {
            line: 23,
            column: 23
          },
          end: {
            line: 23,
            column: 32
          }
        }],
        line: 23
      },
      "2": {
        loc: {
          start: {
            line: 28,
            column: 2
          },
          end: {
            line: 30,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 2
          },
          end: {
            line: 30,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "3": {
        loc: {
          start: {
            line: 28,
            column: 6
          },
          end: {
            line: 28,
            column: 32
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 6
          },
          end: {
            line: 28,
            column: 18
          }
        }, {
          start: {
            line: 28,
            column: 22
          },
          end: {
            line: 28,
            column: 32
          }
        }],
        line: 28
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0a379f96b8444e00a9595768fa560fc740a161ca"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_a7pt6sn8h = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_a7pt6sn8h();
import { useEffect } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFrameworkReady } from "../hooks/useFrameworkReady";
import { useFonts, Inter_400Regular, Inter_500Medium, Inter_600SemiBold, Inter_700Bold } from '@expo-google-fonts/inter';
import * as SplashScreen from 'expo-splash-screen';
import { AuthProvider } from "../contexts/AuthContext";
import ErrorBoundary from "../components/ui/ErrorBoundary";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
cov_a7pt6sn8h().s[0]++;
SplashScreen.preventAutoHideAsync();
export default function RootLayout() {
  cov_a7pt6sn8h().f[0]++;
  cov_a7pt6sn8h().s[1]++;
  useFrameworkReady();
  var _ref = (cov_a7pt6sn8h().s[2]++, useFonts({
      'Inter-Regular': Inter_400Regular,
      'Inter-Medium': Inter_500Medium,
      'Inter-SemiBold': Inter_600SemiBold,
      'Inter-Bold': Inter_700Bold
    })),
    _ref2 = _slicedToArray(_ref, 2),
    fontsLoaded = _ref2[0],
    fontError = _ref2[1];
  cov_a7pt6sn8h().s[3]++;
  useEffect(function () {
    cov_a7pt6sn8h().f[1]++;
    cov_a7pt6sn8h().s[4]++;
    if ((cov_a7pt6sn8h().b[1][0]++, fontsLoaded) || (cov_a7pt6sn8h().b[1][1]++, fontError)) {
      cov_a7pt6sn8h().b[0][0]++;
      cov_a7pt6sn8h().s[5]++;
      SplashScreen.hideAsync();
    } else {
      cov_a7pt6sn8h().b[0][1]++;
    }
  }, [fontsLoaded, fontError]);
  cov_a7pt6sn8h().s[6]++;
  if ((cov_a7pt6sn8h().b[3][0]++, !fontsLoaded) && (cov_a7pt6sn8h().b[3][1]++, !fontError)) {
    cov_a7pt6sn8h().b[2][0]++;
    cov_a7pt6sn8h().s[7]++;
    return null;
  } else {
    cov_a7pt6sn8h().b[2][1]++;
  }
  cov_a7pt6sn8h().s[8]++;
  return _jsx(ErrorBoundary, {
    context: "RootLayout",
    children: _jsxs(AuthProvider, {
      children: [_jsxs(Stack, {
        screenOptions: {
          headerShown: false
        },
        children: [_jsx(Stack.Screen, {
          name: "onboarding",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "(tabs)",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "auth",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "match-recording",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "ai-demo",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "core-features-demo",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "drills/index",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "drills/[id]",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "matches/index",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "matches/[id]",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "social/index",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "social/create-post",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "settings/edit-profile",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "settings/subscription",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "settings/notifications",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "settings/privacy",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "help/index",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "help/contact",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "social/challenges",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "social/messaging",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "analytics/dashboard",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "payment/checkout",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "payment/payment-methods",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "payment/billing-history",
          options: {
            headerShown: false
          }
        }), _jsx(Stack.Screen, {
          name: "+not-found"
        })]
      }), _jsx(StatusBar, {
        style: "auto"
      })]
    })
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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