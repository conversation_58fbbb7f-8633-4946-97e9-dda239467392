{"version": 3, "names": ["performanceMonitor", "BundleAnalysisService", "_classCallCheck", "metrics", "cov_11ic3jy76j", "s", "loadTimes", "Map", "chunkCache", "_createClass", "key", "value", "_analyzeBundlePerformance", "_asyncToGenerator", "f", "startTime", "Date", "now", "chunks", "analyzeChunks", "dependencies", "analyzeDependencies", "totalSize", "reduce", "sum", "chunk", "size", "gzippedSize", "Math", "round", "loadTime", "parseTime", "estimateParseTime", "error", "console", "analyzeBundlePerformance", "apply", "arguments", "_analyzeChunks", "_this", "name", "modules", "cached", "for<PERSON>ach", "set", "_analyzeDependencies", "version", "type", "treeshakeable", "generateOptimizationSuggestions", "b", "Error", "suggestions", "largeChunks", "filter", "length", "push", "priority", "description", "map", "c", "join", "estimatedSavings", "implementation", "uncachedChunks", "treeshakeableDeps", "dep", "sort", "a", "priorityOrder", "high", "medium", "low", "trackChunkLoad", "chunkName", "has", "times", "get", "shift", "cachedChunk", "time", "getPerformanceReport", "_this2", "averageLoadTimes", "_ref", "entries", "_ref2", "_slicedToArray", "average", "bundleSize", "total", "formatBytes", "gzipped", "compressionRatio", "performance", "totalLoadTime", "averageChunkLoadTime", "Array", "from", "values", "optimizations", "score", "calculatePerformanceScore", "uncachedRatio", "max", "bytes", "k", "sizes", "i", "floor", "log", "parseFloat", "pow", "toFixed", "clearCache", "clear", "bundleAnalysisService", "bundleUtils", "trackLazyLoad", "componentName", "trackComponentLoad", "getOptimizationRecommendations", "_getOptimizationRecommendations", "getPerformanceDashboard", "_getPerformanceDashboard"], "sources": ["bundleAnalysis.ts"], "sourcesContent": ["/**\n * Bundle Analysis Utilities\n * \n * Provides tools for analyzing bundle size, performance metrics,\n * and optimization opportunities in the React Native app.\n */\n\nimport { performanceMonitor } from '@/utils/performance';\n\ninterface BundleMetrics {\n  totalSize: number;\n  gzippedSize: number;\n  chunks: ChunkInfo[];\n  dependencies: DependencyInfo[];\n  loadTime: number;\n  parseTime: number;\n}\n\ninterface ChunkInfo {\n  name: string;\n  size: number;\n  modules: string[];\n  loadTime?: number;\n  cached?: boolean;\n}\n\ninterface DependencyInfo {\n  name: string;\n  version: string;\n  size: number;\n  type: 'production' | 'development';\n  treeshakeable: boolean;\n}\n\ninterface OptimizationSuggestion {\n  type: 'bundle-split' | 'lazy-load' | 'tree-shake' | 'cache' | 'compress';\n  priority: 'high' | 'medium' | 'low';\n  description: string;\n  estimatedSavings: string;\n  implementation: string;\n}\n\n/**\n * Bundle Analysis Service\n */\nclass BundleAnalysisService {\n  private metrics: BundleMetrics | null = null;\n  private loadTimes = new Map<string, number[]>();\n  private chunkCache = new Map<string, ChunkInfo>();\n\n  /**\n   * Analyze current bundle performance\n   */\n  async analyzeBundlePerformance(): Promise<BundleMetrics> {\n    const startTime = Date.now();\n\n    try {\n      // Simulate bundle analysis (in real app, this would use actual bundle data)\n      const chunks = await this.analyzeChunks();\n      const dependencies = await this.analyzeDependencies();\n      \n      const totalSize = chunks.reduce((sum, chunk) => sum + chunk.size, 0);\n      const gzippedSize = Math.round(totalSize * 0.3); // Estimate gzip compression\n      \n      const metrics: BundleMetrics = {\n        totalSize,\n        gzippedSize,\n        chunks,\n        dependencies,\n        loadTime: Date.now() - startTime,\n        parseTime: this.estimateParseTime(totalSize),\n      };\n\n      this.metrics = metrics;\n      return metrics;\n\n    } catch (error) {\n      console.error('Bundle analysis failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Analyze individual chunks\n   */\n  private async analyzeChunks(): Promise<ChunkInfo[]> {\n    // Simulate chunk analysis based on our lazy loading setup\n    const chunks: ChunkInfo[] = [\n      {\n        name: 'main',\n        size: 150000, // 150KB\n        modules: ['App', 'AuthContext', 'Navigation'],\n        loadTime: 200,\n        cached: false,\n      },\n      {\n        name: 'core-screens',\n        size: 120000, // 120KB\n        modules: ['Dashboard', 'Training', 'Progress', 'Profile'],\n        loadTime: 150,\n        cached: true,\n      },\n      {\n        name: 'feature-screens',\n        size: 200000, // 200KB\n        modules: ['VideoAnalysis', 'AICoaching', 'MatchAnalysis'],\n        loadTime: 300,\n        cached: false,\n      },\n      {\n        name: 'social-features',\n        size: 100000, // 100KB\n        modules: ['Social', 'Leaderboard', 'Challenges'],\n        loadTime: 180,\n        cached: false,\n      },\n      {\n        name: 'premium-features',\n        size: 150000, // 150KB\n        modules: ['Premium', 'AdvancedAnalytics', 'Subscription'],\n        loadTime: 250,\n        cached: false,\n      },\n      {\n        name: 'vendor',\n        size: 300000, // 300KB\n        modules: ['react', 'react-native', 'expo', 'supabase'],\n        loadTime: 100,\n        cached: true,\n      },\n    ];\n\n    // Cache chunk information\n    chunks.forEach(chunk => {\n      this.chunkCache.set(chunk.name, chunk);\n    });\n\n    return chunks;\n  }\n\n  /**\n   * Analyze dependencies\n   */\n  private async analyzeDependencies(): Promise<DependencyInfo[]> {\n    // Simulate dependency analysis\n    return [\n      {\n        name: 'react',\n        version: '19.0.0',\n        size: 45000,\n        type: 'production',\n        treeshakeable: false,\n      },\n      {\n        name: 'react-native',\n        version: '0.79.1',\n        size: 120000,\n        type: 'production',\n        treeshakeable: false,\n      },\n      {\n        name: 'expo',\n        version: '53.0.0',\n        size: 80000,\n        type: 'production',\n        treeshakeable: true,\n      },\n      {\n        name: '@supabase/supabase-js',\n        version: '2.50.0',\n        size: 35000,\n        type: 'production',\n        treeshakeable: true,\n      },\n      {\n        name: 'openai',\n        version: '4.104.0',\n        size: 25000,\n        type: 'production',\n        treeshakeable: true,\n      },\n    ];\n  }\n\n  /**\n   * Generate optimization suggestions\n   */\n  generateOptimizationSuggestions(): OptimizationSuggestion[] {\n    if (!this.metrics) {\n      throw new Error('Bundle analysis must be run first');\n    }\n\n    const suggestions: OptimizationSuggestion[] = [];\n\n    // Analyze chunk sizes\n    const largeChunks = this.metrics.chunks.filter(chunk => chunk.size > 150000);\n    if (largeChunks.length > 0) {\n      suggestions.push({\n        type: 'bundle-split',\n        priority: 'high',\n        description: `Split large chunks: ${largeChunks.map(c => c.name).join(', ')}`,\n        estimatedSavings: '20-30% initial load time',\n        implementation: 'Implement more granular lazy loading and code splitting',\n      });\n    }\n\n    // Analyze uncached chunks\n    const uncachedChunks = this.metrics.chunks.filter(chunk => !chunk.cached);\n    if (uncachedChunks.length > 2) {\n      suggestions.push({\n        type: 'cache',\n        priority: 'medium',\n        description: 'Improve caching for frequently used chunks',\n        estimatedSavings: '40-50% repeat load time',\n        implementation: 'Implement service worker caching and chunk versioning',\n      });\n    }\n\n    // Analyze treeshakeable dependencies\n    const treeshakeableDeps = this.metrics.dependencies.filter(dep => dep.treeshakeable);\n    if (treeshakeableDeps.length > 0) {\n      suggestions.push({\n        type: 'tree-shake',\n        priority: 'medium',\n        description: 'Optimize imports for treeshakeable dependencies',\n        estimatedSavings: '10-15% bundle size',\n        implementation: 'Use named imports and configure webpack/metro for better tree shaking',\n      });\n    }\n\n    // Analyze total bundle size\n    if (this.metrics.totalSize > 800000) { // 800KB\n      suggestions.push({\n        type: 'compress',\n        priority: 'high',\n        description: 'Bundle size is large, implement compression',\n        estimatedSavings: '60-70% transfer size',\n        implementation: 'Enable gzip/brotli compression and optimize assets',\n      });\n    }\n\n    return suggestions.sort((a, b) => {\n      const priorityOrder = { high: 0, medium: 1, low: 2 };\n      return priorityOrder[a.priority] - priorityOrder[b.priority];\n    });\n  }\n\n  /**\n   * Track chunk load performance\n   */\n  trackChunkLoad(chunkName: string, loadTime: number): void {\n    if (!this.loadTimes.has(chunkName)) {\n      this.loadTimes.set(chunkName, []);\n    }\n    \n    const times = this.loadTimes.get(chunkName)!;\n    times.push(loadTime);\n    \n    // Keep only last 10 measurements\n    if (times.length > 10) {\n      times.shift();\n    }\n\n    // Update chunk cache with average load time\n    const cachedChunk = this.chunkCache.get(chunkName);\n    if (cachedChunk) {\n      cachedChunk.loadTime = times.reduce((sum, time) => sum + time, 0) / times.length;\n    }\n  }\n\n  /**\n   * Get performance report\n   */\n  getPerformanceReport(): any {\n    if (!this.metrics) {\n      return null;\n    }\n\n    const suggestions = this.generateOptimizationSuggestions();\n    const averageLoadTimes = new Map<string, number>();\n    \n    for (const [chunkName, times] of this.loadTimes.entries()) {\n      const average = times.reduce((sum, time) => sum + time, 0) / times.length;\n      averageLoadTimes.set(chunkName, average);\n    }\n\n    return {\n      bundleSize: {\n        total: this.formatBytes(this.metrics.totalSize),\n        gzipped: this.formatBytes(this.metrics.gzippedSize),\n        compressionRatio: Math.round((this.metrics.gzippedSize / this.metrics.totalSize) * 100),\n      },\n      chunks: this.metrics.chunks.map(chunk => ({\n        name: chunk.name,\n        size: this.formatBytes(chunk.size),\n        loadTime: chunk.loadTime || 0,\n        cached: chunk.cached,\n        modules: chunk.modules.length,\n      })),\n      performance: {\n        totalLoadTime: this.metrics.loadTime,\n        parseTime: this.metrics.parseTime,\n        averageChunkLoadTime: Array.from(averageLoadTimes.values()).reduce((sum, time) => sum + time, 0) / averageLoadTimes.size || 0,\n      },\n      optimizations: suggestions,\n      score: this.calculatePerformanceScore(),\n    };\n  }\n\n  /**\n   * Calculate performance score (0-100)\n   */\n  private calculatePerformanceScore(): number {\n    if (!this.metrics) return 0;\n\n    let score = 100;\n\n    // Penalize large bundle size\n    if (this.metrics.totalSize > 1000000) score -= 30; // 1MB+\n    else if (this.metrics.totalSize > 500000) score -= 15; // 500KB+\n\n    // Penalize slow load times\n    if (this.metrics.loadTime > 3000) score -= 25; // 3s+\n    else if (this.metrics.loadTime > 1000) score -= 10; // 1s+\n\n    // Penalize uncached chunks\n    const uncachedRatio = this.metrics.chunks.filter(c => !c.cached).length / this.metrics.chunks.length;\n    score -= uncachedRatio * 20;\n\n    // Bonus for good compression\n    const compressionRatio = this.metrics.gzippedSize / this.metrics.totalSize;\n    if (compressionRatio < 0.3) score += 10; // Good compression\n\n    return Math.max(0, Math.round(score));\n  }\n\n  /**\n   * Estimate parse time based on bundle size\n   */\n  private estimateParseTime(bundleSize: number): number {\n    // Rough estimate: 1ms per 1KB on average device\n    return Math.round(bundleSize / 1000);\n  }\n\n  /**\n   * Format bytes to human readable format\n   */\n  private formatBytes(bytes: number): string {\n    if (bytes === 0) return '0 B';\n    \n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    \n    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\n  }\n\n  /**\n   * Clear cached data\n   */\n  clearCache(): void {\n    this.metrics = null;\n    this.loadTimes.clear();\n    this.chunkCache.clear();\n  }\n}\n\n// Export singleton instance\nexport const bundleAnalysisService = new BundleAnalysisService();\n\n// Export utility functions\nexport const bundleUtils = {\n  /**\n   * Track component lazy load performance\n   */\n  trackLazyLoad: (componentName: string, loadTime: number) => {\n    bundleAnalysisService.trackChunkLoad(componentName, loadTime);\n    performanceMonitor.trackComponentLoad(componentName, loadTime);\n  },\n\n  /**\n   * Get bundle optimization recommendations\n   */\n  getOptimizationRecommendations: async () => {\n    await bundleAnalysisService.analyzeBundlePerformance();\n    return bundleAnalysisService.generateOptimizationSuggestions();\n  },\n\n  /**\n   * Get performance dashboard data\n   */\n  getPerformanceDashboard: async () => {\n    await bundleAnalysisService.analyzeBundlePerformance();\n    return bundleAnalysisService.getPerformanceReport();\n  },\n};\n\nexport default bundleAnalysisService;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,kBAAkB;AAA8B,IAsCnDC,qBAAqB;EAAA,SAAAA,sBAAA;IAAAC,eAAA,OAAAD,qBAAA;IAAA,KACjBE,OAAO,IAAAC,cAAA,GAAAC,CAAA,OAAyB,IAAI;IAAA,KACpCC,SAAS,IAAAF,cAAA,GAAAC,CAAA,OAAG,IAAIE,GAAG,CAAmB,CAAC;IAAA,KACvCC,UAAU,IAAAJ,cAAA,GAAAC,CAAA,OAAG,IAAIE,GAAG,CAAoB,CAAC;EAAA;EAAA,OAAAE,YAAA,CAAAR,qBAAA;IAAAS,GAAA;IAAAC,KAAA;MAAA,IAAAC,yBAAA,GAAAC,iBAAA,CAKjD,aAAyD;QAAAT,cAAA,GAAAU,CAAA;QACvD,IAAMC,SAAS,IAAAX,cAAA,GAAAC,CAAA,OAAGW,IAAI,CAACC,GAAG,CAAC,CAAC;QAACb,cAAA,GAAAC,CAAA;QAE7B,IAAI;UAEF,IAAMa,MAAM,IAAAd,cAAA,GAAAC,CAAA,aAAS,IAAI,CAACc,aAAa,CAAC,CAAC;UACzC,IAAMC,YAAY,IAAAhB,cAAA,GAAAC,CAAA,aAAS,IAAI,CAACgB,mBAAmB,CAAC,CAAC;UAErD,IAAMC,SAAS,IAAAlB,cAAA,GAAAC,CAAA,OAAGa,MAAM,CAACK,MAAM,CAAC,UAACC,GAAG,EAAEC,KAAK,EAAK;YAAArB,cAAA,GAAAU,CAAA;YAAAV,cAAA,GAAAC,CAAA;YAAA,OAAAmB,GAAG,GAAGC,KAAK,CAACC,IAAI;UAAD,CAAC,EAAE,CAAC,CAAC;UACpE,IAAMC,WAAW,IAAAvB,cAAA,GAAAC,CAAA,OAAGuB,IAAI,CAACC,KAAK,CAACP,SAAS,GAAG,GAAG,CAAC;UAE/C,IAAMnB,OAAsB,IAAAC,cAAA,GAAAC,CAAA,QAAG;YAC7BiB,SAAS,EAATA,SAAS;YACTK,WAAW,EAAXA,WAAW;YACXT,MAAM,EAANA,MAAM;YACNE,YAAY,EAAZA,YAAY;YACZU,QAAQ,EAAEd,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;YAChCgB,SAAS,EAAE,IAAI,CAACC,iBAAiB,CAACV,SAAS;UAC7C,CAAC;UAAClB,cAAA,GAAAC,CAAA;UAEF,IAAI,CAACF,OAAO,GAAGA,OAAO;UAACC,cAAA,GAAAC,CAAA;UACvB,OAAOF,OAAO;QAEhB,CAAC,CAAC,OAAO8B,KAAK,EAAE;UAAA7B,cAAA,GAAAC,CAAA;UACd6B,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAAC7B,cAAA,GAAAC,CAAA;UAChD,MAAM4B,KAAK;QACb;MACF,CAAC;MAAA,SA3BKE,wBAAwBA,CAAA;QAAA,OAAAvB,yBAAA,CAAAwB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAxBF,wBAAwB;IAAA;EAAA;IAAAzB,GAAA;IAAAC,KAAA;MAAA,IAAA2B,cAAA,GAAAzB,iBAAA,CAgC9B,aAAoD;QAAA,IAAA0B,KAAA;QAAAnC,cAAA,GAAAU,CAAA;QAElD,IAAMI,MAAmB,IAAAd,cAAA,GAAAC,CAAA,QAAG,CAC1B;UACEmC,IAAI,EAAE,MAAM;UACZd,IAAI,EAAE,MAAM;UACZe,OAAO,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,YAAY,CAAC;UAC7CX,QAAQ,EAAE,GAAG;UACbY,MAAM,EAAE;QACV,CAAC,EACD;UACEF,IAAI,EAAE,cAAc;UACpBd,IAAI,EAAE,MAAM;UACZe,OAAO,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC;UACzDX,QAAQ,EAAE,GAAG;UACbY,MAAM,EAAE;QACV,CAAC,EACD;UACEF,IAAI,EAAE,iBAAiB;UACvBd,IAAI,EAAE,MAAM;UACZe,OAAO,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,eAAe,CAAC;UACzDX,QAAQ,EAAE,GAAG;UACbY,MAAM,EAAE;QACV,CAAC,EACD;UACEF,IAAI,EAAE,iBAAiB;UACvBd,IAAI,EAAE,MAAM;UACZe,OAAO,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,YAAY,CAAC;UAChDX,QAAQ,EAAE,GAAG;UACbY,MAAM,EAAE;QACV,CAAC,EACD;UACEF,IAAI,EAAE,kBAAkB;UACxBd,IAAI,EAAE,MAAM;UACZe,OAAO,EAAE,CAAC,SAAS,EAAE,mBAAmB,EAAE,cAAc,CAAC;UACzDX,QAAQ,EAAE,GAAG;UACbY,MAAM,EAAE;QACV,CAAC,EACD;UACEF,IAAI,EAAE,QAAQ;UACdd,IAAI,EAAE,MAAM;UACZe,OAAO,EAAE,CAAC,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,UAAU,CAAC;UACtDX,QAAQ,EAAE,GAAG;UACbY,MAAM,EAAE;QACV,CAAC,CACF;QAACtC,cAAA,GAAAC,CAAA;QAGFa,MAAM,CAACyB,OAAO,CAAC,UAAAlB,KAAK,EAAI;UAAArB,cAAA,GAAAU,CAAA;UAAAV,cAAA,GAAAC,CAAA;UACtBkC,KAAI,CAAC/B,UAAU,CAACoC,GAAG,CAACnB,KAAK,CAACe,IAAI,EAAEf,KAAK,CAAC;QACxC,CAAC,CAAC;QAACrB,cAAA,GAAAC,CAAA;QAEH,OAAOa,MAAM;MACf,CAAC;MAAA,SArDaC,aAAaA,CAAA;QAAA,OAAAmB,cAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAblB,aAAa;IAAA;EAAA;IAAAT,GAAA;IAAAC,KAAA;MAAA,IAAAkC,oBAAA,GAAAhC,iBAAA,CA0D3B,aAA+D;QAAAT,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAE7D,OAAO,CACL;UACEmC,IAAI,EAAE,OAAO;UACbM,OAAO,EAAE,QAAQ;UACjBpB,IAAI,EAAE,KAAK;UACXqB,IAAI,EAAE,YAAY;UAClBC,aAAa,EAAE;QACjB,CAAC,EACD;UACER,IAAI,EAAE,cAAc;UACpBM,OAAO,EAAE,QAAQ;UACjBpB,IAAI,EAAE,MAAM;UACZqB,IAAI,EAAE,YAAY;UAClBC,aAAa,EAAE;QACjB,CAAC,EACD;UACER,IAAI,EAAE,MAAM;UACZM,OAAO,EAAE,QAAQ;UACjBpB,IAAI,EAAE,KAAK;UACXqB,IAAI,EAAE,YAAY;UAClBC,aAAa,EAAE;QACjB,CAAC,EACD;UACER,IAAI,EAAE,uBAAuB;UAC7BM,OAAO,EAAE,QAAQ;UACjBpB,IAAI,EAAE,KAAK;UACXqB,IAAI,EAAE,YAAY;UAClBC,aAAa,EAAE;QACjB,CAAC,EACD;UACER,IAAI,EAAE,QAAQ;UACdM,OAAO,EAAE,SAAS;UAClBpB,IAAI,EAAE,KAAK;UACXqB,IAAI,EAAE,YAAY;UAClBC,aAAa,EAAE;QACjB,CAAC,CACF;MACH,CAAC;MAAA,SAvCa3B,mBAAmBA,CAAA;QAAA,OAAAwB,oBAAA,CAAAT,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBhB,mBAAmB;IAAA;EAAA;IAAAX,GAAA;IAAAC,KAAA,EA4CjC,SAAAsC,+BAA+BA,CAAA,EAA6B;MAAA7C,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAC1D,IAAI,CAAC,IAAI,CAACF,OAAO,EAAE;QAAAC,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QACjB,MAAM,IAAI8C,KAAK,CAAC,mCAAmC,CAAC;MACtD,CAAC;QAAA/C,cAAA,GAAA8C,CAAA;MAAA;MAED,IAAME,WAAqC,IAAAhD,cAAA,GAAAC,CAAA,QAAG,EAAE;MAGhD,IAAMgD,WAAW,IAAAjD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,OAAO,CAACe,MAAM,CAACoC,MAAM,CAAC,UAAA7B,KAAK,EAAI;QAAArB,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAA,OAAAoB,KAAK,CAACC,IAAI,GAAG,MAAM;MAAD,CAAC,CAAC;MAACtB,cAAA,GAAAC,CAAA;MAC7E,IAAIgD,WAAW,CAACE,MAAM,GAAG,CAAC,EAAE;QAAAnD,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QAC1B+C,WAAW,CAACI,IAAI,CAAC;UACfT,IAAI,EAAE,cAAc;UACpBU,QAAQ,EAAE,MAAM;UAChBC,WAAW,EAAE,uBAAuBL,WAAW,CAACM,GAAG,CAAC,UAAAC,CAAC,EAAI;YAAAxD,cAAA,GAAAU,CAAA;YAAAV,cAAA,GAAAC,CAAA;YAAA,OAAAuD,CAAC,CAACpB,IAAI;UAAD,CAAC,CAAC,CAACqB,IAAI,CAAC,IAAI,CAAC,EAAE;UAC7EC,gBAAgB,EAAE,0BAA0B;UAC5CC,cAAc,EAAE;QAClB,CAAC,CAAC;MACJ,CAAC;QAAA3D,cAAA,GAAA8C,CAAA;MAAA;MAGD,IAAMc,cAAc,IAAA5D,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,OAAO,CAACe,MAAM,CAACoC,MAAM,CAAC,UAAA7B,KAAK,EAAI;QAAArB,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAA,QAACoB,KAAK,CAACiB,MAAM;MAAD,CAAC,CAAC;MAACtC,cAAA,GAAAC,CAAA;MAC1E,IAAI2D,cAAc,CAACT,MAAM,GAAG,CAAC,EAAE;QAAAnD,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QAC7B+C,WAAW,CAACI,IAAI,CAAC;UACfT,IAAI,EAAE,OAAO;UACbU,QAAQ,EAAE,QAAQ;UAClBC,WAAW,EAAE,4CAA4C;UACzDI,gBAAgB,EAAE,yBAAyB;UAC3CC,cAAc,EAAE;QAClB,CAAC,CAAC;MACJ,CAAC;QAAA3D,cAAA,GAAA8C,CAAA;MAAA;MAGD,IAAMe,iBAAiB,IAAA7D,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,OAAO,CAACiB,YAAY,CAACkC,MAAM,CAAC,UAAAY,GAAG,EAAI;QAAA9D,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAA,OAAA6D,GAAG,CAAClB,aAAa;MAAD,CAAC,CAAC;MAAC5C,cAAA,GAAAC,CAAA;MACrF,IAAI4D,iBAAiB,CAACV,MAAM,GAAG,CAAC,EAAE;QAAAnD,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QAChC+C,WAAW,CAACI,IAAI,CAAC;UACfT,IAAI,EAAE,YAAY;UAClBU,QAAQ,EAAE,QAAQ;UAClBC,WAAW,EAAE,iDAAiD;UAC9DI,gBAAgB,EAAE,oBAAoB;UACtCC,cAAc,EAAE;QAClB,CAAC,CAAC;MACJ,CAAC;QAAA3D,cAAA,GAAA8C,CAAA;MAAA;MAAA9C,cAAA,GAAAC,CAAA;MAGD,IAAI,IAAI,CAACF,OAAO,CAACmB,SAAS,GAAG,MAAM,EAAE;QAAAlB,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QACnC+C,WAAW,CAACI,IAAI,CAAC;UACfT,IAAI,EAAE,UAAU;UAChBU,QAAQ,EAAE,MAAM;UAChBC,WAAW,EAAE,6CAA6C;UAC1DI,gBAAgB,EAAE,sBAAsB;UACxCC,cAAc,EAAE;QAClB,CAAC,CAAC;MACJ,CAAC;QAAA3D,cAAA,GAAA8C,CAAA;MAAA;MAAA9C,cAAA,GAAAC,CAAA;MAED,OAAO+C,WAAW,CAACe,IAAI,CAAC,UAACC,CAAC,EAAElB,CAAC,EAAK;QAAA9C,cAAA,GAAAU,CAAA;QAChC,IAAMuD,aAAa,IAAAjE,cAAA,GAAAC,CAAA,QAAG;UAAEiE,IAAI,EAAE,CAAC;UAAEC,MAAM,EAAE,CAAC;UAAEC,GAAG,EAAE;QAAE,CAAC;QAACpE,cAAA,GAAAC,CAAA;QACrD,OAAOgE,aAAa,CAACD,CAAC,CAACX,QAAQ,CAAC,GAAGY,aAAa,CAACnB,CAAC,CAACO,QAAQ,CAAC;MAC9D,CAAC,CAAC;IACJ;EAAC;IAAA/C,GAAA;IAAAC,KAAA,EAKD,SAAA8D,cAAcA,CAACC,SAAiB,EAAE5C,QAAgB,EAAQ;MAAA1B,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACxD,IAAI,CAAC,IAAI,CAACC,SAAS,CAACqE,GAAG,CAACD,SAAS,CAAC,EAAE;QAAAtE,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QAClC,IAAI,CAACC,SAAS,CAACsC,GAAG,CAAC8B,SAAS,EAAE,EAAE,CAAC;MACnC,CAAC;QAAAtE,cAAA,GAAA8C,CAAA;MAAA;MAED,IAAM0B,KAAK,IAAAxE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACC,SAAS,CAACuE,GAAG,CAACH,SAAS,CAAC,CAAC;MAACtE,cAAA,GAAAC,CAAA;MAC7CuE,KAAK,CAACpB,IAAI,CAAC1B,QAAQ,CAAC;MAAC1B,cAAA,GAAAC,CAAA;MAGrB,IAAIuE,KAAK,CAACrB,MAAM,GAAG,EAAE,EAAE;QAAAnD,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QACrBuE,KAAK,CAACE,KAAK,CAAC,CAAC;MACf,CAAC;QAAA1E,cAAA,GAAA8C,CAAA;MAAA;MAGD,IAAM6B,WAAW,IAAA3E,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACG,UAAU,CAACqE,GAAG,CAACH,SAAS,CAAC;MAACtE,cAAA,GAAAC,CAAA;MACnD,IAAI0E,WAAW,EAAE;QAAA3E,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QACf0E,WAAW,CAACjD,QAAQ,GAAG8C,KAAK,CAACrD,MAAM,CAAC,UAACC,GAAG,EAAEwD,IAAI,EAAK;UAAA5E,cAAA,GAAAU,CAAA;UAAAV,cAAA,GAAAC,CAAA;UAAA,OAAAmB,GAAG,GAAGwD,IAAI;QAAD,CAAC,EAAE,CAAC,CAAC,GAAGJ,KAAK,CAACrB,MAAM;MAClF,CAAC;QAAAnD,cAAA,GAAA8C,CAAA;MAAA;IACH;EAAC;IAAAxC,GAAA;IAAAC,KAAA,EAKD,SAAAsE,oBAAoBA,CAAA,EAAQ;MAAA,IAAAC,MAAA;MAAA9E,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAC1B,IAAI,CAAC,IAAI,CAACF,OAAO,EAAE;QAAAC,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QACjB,OAAO,IAAI;MACb,CAAC;QAAAD,cAAA,GAAA8C,CAAA;MAAA;MAED,IAAME,WAAW,IAAAhD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC4C,+BAA+B,CAAC,CAAC;MAC1D,IAAMkC,gBAAgB,IAAA/E,cAAA,GAAAC,CAAA,QAAG,IAAIE,GAAG,CAAiB,CAAC;MAACH,cAAA,GAAAC,CAAA;MAEnD,SAAA+E,IAAA,IAAiC,IAAI,CAAC9E,SAAS,CAAC+E,OAAO,CAAC,CAAC,EAAE;QAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAH,IAAA;QAAA,IAA/CV,SAAS,GAAAY,KAAA;QAAA,IAAEV,KAAK,GAAAU,KAAA;QAC1B,IAAME,OAAO,IAAApF,cAAA,GAAAC,CAAA,QAAGuE,KAAK,CAACrD,MAAM,CAAC,UAACC,GAAG,EAAEwD,IAAI,EAAK;UAAA5E,cAAA,GAAAU,CAAA;UAAAV,cAAA,GAAAC,CAAA;UAAA,OAAAmB,GAAG,GAAGwD,IAAI;QAAD,CAAC,EAAE,CAAC,CAAC,GAAGJ,KAAK,CAACrB,MAAM;QAACnD,cAAA,GAAAC,CAAA;QAC1E8E,gBAAgB,CAACvC,GAAG,CAAC8B,SAAS,EAAEc,OAAO,CAAC;MAC1C;MAACpF,cAAA,GAAAC,CAAA;MAED,OAAO;QACLoF,UAAU,EAAE;UACVC,KAAK,EAAE,IAAI,CAACC,WAAW,CAAC,IAAI,CAACxF,OAAO,CAACmB,SAAS,CAAC;UAC/CsE,OAAO,EAAE,IAAI,CAACD,WAAW,CAAC,IAAI,CAACxF,OAAO,CAACwB,WAAW,CAAC;UACnDkE,gBAAgB,EAAEjE,IAAI,CAACC,KAAK,CAAE,IAAI,CAAC1B,OAAO,CAACwB,WAAW,GAAG,IAAI,CAACxB,OAAO,CAACmB,SAAS,GAAI,GAAG;QACxF,CAAC;QACDJ,MAAM,EAAE,IAAI,CAACf,OAAO,CAACe,MAAM,CAACyC,GAAG,CAAC,UAAAlC,KAAK,EAAK;UAAArB,cAAA,GAAAU,CAAA;UAAAV,cAAA,GAAAC,CAAA;UAAA;YACxCmC,IAAI,EAAEf,KAAK,CAACe,IAAI;YAChBd,IAAI,EAAEwD,MAAI,CAACS,WAAW,CAAClE,KAAK,CAACC,IAAI,CAAC;YAClCI,QAAQ,EAAE,CAAA1B,cAAA,GAAA8C,CAAA,UAAAzB,KAAK,CAACK,QAAQ,MAAA1B,cAAA,GAAA8C,CAAA,UAAI,CAAC;YAC7BR,MAAM,EAAEjB,KAAK,CAACiB,MAAM;YACpBD,OAAO,EAAEhB,KAAK,CAACgB,OAAO,CAACc;UACzB,CAAC;QAAD,CAAE,CAAC;QACHuC,WAAW,EAAE;UACXC,aAAa,EAAE,IAAI,CAAC5F,OAAO,CAAC2B,QAAQ;UACpCC,SAAS,EAAE,IAAI,CAAC5B,OAAO,CAAC4B,SAAS;UACjCiE,oBAAoB,EAAE,CAAA5F,cAAA,GAAA8C,CAAA,WAAA+C,KAAK,CAACC,IAAI,CAACf,gBAAgB,CAACgB,MAAM,CAAC,CAAC,CAAC,CAAC5E,MAAM,CAAC,UAACC,GAAG,EAAEwD,IAAI,EAAK;YAAA5E,cAAA,GAAAU,CAAA;YAAAV,cAAA,GAAAC,CAAA;YAAA,OAAAmB,GAAG,GAAGwD,IAAI;UAAD,CAAC,EAAE,CAAC,CAAC,GAAGG,gBAAgB,CAACzD,IAAI,MAAAtB,cAAA,GAAA8C,CAAA,WAAI,CAAC;QAC/H,CAAC;QACDkD,aAAa,EAAEhD,WAAW;QAC1BiD,KAAK,EAAE,IAAI,CAACC,yBAAyB,CAAC;MACxC,CAAC;IACH;EAAC;IAAA5F,GAAA;IAAAC,KAAA,EAKD,SAAQ2F,yBAAyBA,CAAA,EAAW;MAAAlG,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAC1C,IAAI,CAAC,IAAI,CAACF,OAAO,EAAE;QAAAC,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QAAA,OAAO,CAAC;MAAA,CAAC;QAAAD,cAAA,GAAA8C,CAAA;MAAA;MAE5B,IAAImD,KAAK,IAAAjG,cAAA,GAAAC,CAAA,QAAG,GAAG;MAACD,cAAA,GAAAC,CAAA;MAGhB,IAAI,IAAI,CAACF,OAAO,CAACmB,SAAS,GAAG,OAAO,EAAE;QAAAlB,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QAAAgG,KAAK,IAAI,EAAE;MAAA,CAAC,MAC7C;QAAAjG,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QAAA,IAAI,IAAI,CAACF,OAAO,CAACmB,SAAS,GAAG,MAAM,EAAE;UAAAlB,cAAA,GAAA8C,CAAA;UAAA9C,cAAA,GAAAC,CAAA;UAAAgG,KAAK,IAAI,EAAE;QAAA,CAAC;UAAAjG,cAAA,GAAA8C,CAAA;QAAA;MAAD;MAAC9C,cAAA,GAAAC,CAAA;MAGtD,IAAI,IAAI,CAACF,OAAO,CAAC2B,QAAQ,GAAG,IAAI,EAAE;QAAA1B,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QAAAgG,KAAK,IAAI,EAAE;MAAA,CAAC,MACzC;QAAAjG,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QAAA,IAAI,IAAI,CAACF,OAAO,CAAC2B,QAAQ,GAAG,IAAI,EAAE;UAAA1B,cAAA,GAAA8C,CAAA;UAAA9C,cAAA,GAAAC,CAAA;UAAAgG,KAAK,IAAI,EAAE;QAAA,CAAC;UAAAjG,cAAA,GAAA8C,CAAA;QAAA;MAAD;MAGlD,IAAMqD,aAAa,IAAAnG,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,OAAO,CAACe,MAAM,CAACoC,MAAM,CAAC,UAAAM,CAAC,EAAI;QAAAxD,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAA,QAACuD,CAAC,CAAClB,MAAM;MAAD,CAAC,CAAC,CAACa,MAAM,GAAG,IAAI,CAACpD,OAAO,CAACe,MAAM,CAACqC,MAAM;MAACnD,cAAA,GAAAC,CAAA;MACrGgG,KAAK,IAAIE,aAAa,GAAG,EAAE;MAG3B,IAAMV,gBAAgB,IAAAzF,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,OAAO,CAACwB,WAAW,GAAG,IAAI,CAACxB,OAAO,CAACmB,SAAS;MAAClB,cAAA,GAAAC,CAAA;MAC3E,IAAIwF,gBAAgB,GAAG,GAAG,EAAE;QAAAzF,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QAAAgG,KAAK,IAAI,EAAE;MAAA,CAAC;QAAAjG,cAAA,GAAA8C,CAAA;MAAA;MAAA9C,cAAA,GAAAC,CAAA;MAExC,OAAOuB,IAAI,CAAC4E,GAAG,CAAC,CAAC,EAAE5E,IAAI,CAACC,KAAK,CAACwE,KAAK,CAAC,CAAC;IACvC;EAAC;IAAA3F,GAAA;IAAAC,KAAA,EAKD,SAAQqB,iBAAiBA,CAACyD,UAAkB,EAAU;MAAArF,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAEpD,OAAOuB,IAAI,CAACC,KAAK,CAAC4D,UAAU,GAAG,IAAI,CAAC;IACtC;EAAC;IAAA/E,GAAA;IAAAC,KAAA,EAKD,SAAQgF,WAAWA,CAACc,KAAa,EAAU;MAAArG,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACzC,IAAIoG,KAAK,KAAK,CAAC,EAAE;QAAArG,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAC,CAAA;QAAA,OAAO,KAAK;MAAA,CAAC;QAAAD,cAAA,GAAA8C,CAAA;MAAA;MAE9B,IAAMwD,CAAC,IAAAtG,cAAA,GAAAC,CAAA,QAAG,IAAI;MACd,IAAMsG,KAAK,IAAAvG,cAAA,GAAAC,CAAA,QAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACrC,IAAMuG,CAAC,IAAAxG,cAAA,GAAAC,CAAA,QAAGuB,IAAI,CAACiF,KAAK,CAACjF,IAAI,CAACkF,GAAG,CAACL,KAAK,CAAC,GAAG7E,IAAI,CAACkF,GAAG,CAACJ,CAAC,CAAC,CAAC;MAACtG,cAAA,GAAAC,CAAA;MAEpD,OAAO0G,UAAU,CAAC,CAACN,KAAK,GAAG7E,IAAI,CAACoF,GAAG,CAACN,CAAC,EAAEE,CAAC,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGN,KAAK,CAACC,CAAC,CAAC;IACzE;EAAC;IAAAlG,GAAA;IAAAC,KAAA,EAKD,SAAAuG,UAAUA,CAAA,EAAS;MAAA9G,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACjB,IAAI,CAACF,OAAO,GAAG,IAAI;MAACC,cAAA,GAAAC,CAAA;MACpB,IAAI,CAACC,SAAS,CAAC6G,KAAK,CAAC,CAAC;MAAC/G,cAAA,GAAAC,CAAA;MACvB,IAAI,CAACG,UAAU,CAAC2G,KAAK,CAAC,CAAC;IACzB;EAAC;AAAA;AAIH,OAAO,IAAMC,qBAAqB,IAAAhH,cAAA,GAAAC,CAAA,QAAG,IAAIJ,qBAAqB,CAAC,CAAC;AAGhE,OAAO,IAAMoH,WAAW,IAAAjH,cAAA,GAAAC,CAAA,QAAG;EAIzBiH,aAAa,EAAE,SAAfA,aAAaA,CAAGC,aAAqB,EAAEzF,QAAgB,EAAK;IAAA1B,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAC,CAAA;IAC1D+G,qBAAqB,CAAC3C,cAAc,CAAC8C,aAAa,EAAEzF,QAAQ,CAAC;IAAC1B,cAAA,GAAAC,CAAA;IAC9DL,kBAAkB,CAACwH,kBAAkB,CAACD,aAAa,EAAEzF,QAAQ,CAAC;EAChE,CAAC;EAKD2F,8BAA8B;IAAA,IAAAC,+BAAA,GAAA7G,iBAAA,CAAE,aAAY;MAAAT,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAC1C,MAAM+G,qBAAqB,CAACjF,wBAAwB,CAAC,CAAC;MAAC/B,cAAA,GAAAC,CAAA;MACvD,OAAO+G,qBAAqB,CAACnE,+BAA+B,CAAC,CAAC;IAChE,CAAC;IAAA,SAHDwE,8BAA8BA,CAAA;MAAA,OAAAC,+BAAA,CAAAtF,KAAA,OAAAC,SAAA;IAAA;IAAA,OAA9BoF,8BAA8B;EAAA,GAG7B;EAKDE,uBAAuB;IAAA,IAAAC,wBAAA,GAAA/G,iBAAA,CAAE,aAAY;MAAAT,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MACnC,MAAM+G,qBAAqB,CAACjF,wBAAwB,CAAC,CAAC;MAAC/B,cAAA,GAAAC,CAAA;MACvD,OAAO+G,qBAAqB,CAACnC,oBAAoB,CAAC,CAAC;IACrD,CAAC;IAAA,SAHD0C,uBAAuBA,CAAA;MAAA,OAAAC,wBAAA,CAAAxF,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAvBsF,uBAAuB;EAAA;AAIzB,CAAC;AAED,eAAeP,qBAAqB", "ignoreList": []}