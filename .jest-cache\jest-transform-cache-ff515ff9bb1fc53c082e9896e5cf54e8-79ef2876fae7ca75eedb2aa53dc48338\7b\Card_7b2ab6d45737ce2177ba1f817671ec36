9877b3d7b0aef193dca6f7dce48bd490
function cov_xrq0811ty() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\ui\\Card.tsx";
  var hash = "75a4015d299aa049fea8b5bc22d5b5a182949e53";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\ui\\Card.tsx",
    statementMap: {
      "0": {
        start: {
          line: 4,
          column: 15
        },
        end: {
          line: 7,
          column: 1
        }
      },
      "1": {
        start: {
          line: 16,
          column: 2
        },
        end: {
          line: 20,
          column: 4
        }
      },
      "2": {
        start: {
          line: 23,
          column: 15
        },
        end: {
          line: 47,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "Card",
        decl: {
          start: {
            line: 15,
            column: 24
          },
          end: {
            line: 15,
            column: 28
          }
        },
        loc: {
          start: {
            line: 15,
            column: 82
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 15
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 15,
            column: 67
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 15,
            column: 58
          },
          end: {
            line: 15,
            column: 67
          }
        }],
        line: 15
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "75a4015d299aa049fea8b5bc22d5b5a182949e53"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_xrq0811ty = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_xrq0811ty();
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { jsx as _jsx } from "react/jsx-runtime";
var colors = (cov_xrq0811ty().s[0]++, {
  white: '#ffffff',
  lightGray: '#f9fafb'
});
export default function Card(_ref) {
  var children = _ref.children,
    style = _ref.style,
    _ref$variant = _ref.variant,
    variant = _ref$variant === void 0 ? (cov_xrq0811ty().b[0][0]++, 'default') : _ref$variant;
  cov_xrq0811ty().f[0]++;
  cov_xrq0811ty().s[1]++;
  return _jsx(View, {
    style: [styles.base, styles[variant], style],
    children: children
  });
}
var styles = (cov_xrq0811ty().s[2]++, StyleSheet.create({
  base: {
    borderRadius: 16,
    padding: 20
  },
  default: {
    backgroundColor: colors.white
  },
  elevated: {
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3
  },
  bordered: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.lightGray
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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