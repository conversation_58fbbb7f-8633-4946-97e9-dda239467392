{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_interopRequireWildcard", "default", "_interopRequireDefault", "exports", "__esModule", "_objectSpread2", "_invariant", "_unmountComponentAtNode", "_renderApplication", "emptyObject", "runnables", "componentProviderInstrumentationHook", "component", "wrapperComponentProvider", "AppRegistry", "key", "value", "getApp<PERSON><PERSON>s", "Object", "keys", "getApplication", "appKey", "appParameters", "registerComponent", "componentProvider", "initialProps", "run", "callback", "hydrate", "mode", "rootTag", "registerConfig", "config", "for<PERSON>ach", "_ref", "registerRunnable", "runApplication", "isDevelopment", "process", "env", "NODE_ENV", "params", "id", "console", "log", "setComponentProviderInstrumentationHook", "hook", "setWrapperComponentProvider", "provider", "unmountApplicationComponentAtRootTag", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nvar _unmountComponentAtNode = _interopRequireDefault(require(\"../unmountComponentAtNode\"));\nvar _renderApplication = _interopRequireWildcard(require(\"./renderApplication\"));\nvar emptyObject = {};\nvar runnables = {};\nvar componentProviderInstrumentationHook = component => component();\nvar wrapperComponentProvider;\n\n/**\n * `AppRegistry` is the JS entry point to running all React Native apps.\n */\nclass AppRegistry {\n  static getAppKeys() {\n    return Object.keys(runnables);\n  }\n  static getApplication(appKey, appParameters) {\n    (0, _invariant.default)(runnables[appKey] && runnables[appKey].getApplication, \"Application \" + appKey + \" has not been registered. \" + 'This is either due to an import error during initialization or failure to call AppRegistry.registerComponent.');\n    return runnables[appKey].getApplication(appParameters);\n  }\n  static registerComponent(appKey, componentProvider) {\n    runnables[appKey] = {\n      getApplication: appParameters => (0, _renderApplication.getApplication)(componentProviderInstrumentationHook(componentProvider), appParameters ? appParameters.initialProps : emptyObject, wrapperComponentProvider && wrapperComponentProvider(appParameters)),\n      run: appParameters => (0, _renderApplication.default)(componentProviderInstrumentationHook(componentProvider), wrapperComponentProvider && wrapperComponentProvider(appParameters), appParameters.callback, {\n        hydrate: appParameters.hydrate || false,\n        initialProps: appParameters.initialProps || emptyObject,\n        mode: appParameters.mode || 'concurrent',\n        rootTag: appParameters.rootTag\n      })\n    };\n    return appKey;\n  }\n  static registerConfig(config) {\n    config.forEach(_ref => {\n      var appKey = _ref.appKey,\n        component = _ref.component,\n        run = _ref.run;\n      if (run) {\n        AppRegistry.registerRunnable(appKey, run);\n      } else {\n        (0, _invariant.default)(component, 'No component provider passed in');\n        AppRegistry.registerComponent(appKey, component);\n      }\n    });\n  }\n\n  // TODO: fix style sheet creation when using this method\n  static registerRunnable(appKey, run) {\n    runnables[appKey] = {\n      run\n    };\n    return appKey;\n  }\n  static runApplication(appKey, appParameters) {\n    var isDevelopment = process.env.NODE_ENV !== 'production' && process.env.NODE_ENV !== 'test';\n    if (isDevelopment) {\n      var params = (0, _objectSpread2.default)({}, appParameters);\n      params.rootTag = \"#\" + params.rootTag.id;\n      console.log(\"Running application \\\"\" + appKey + \"\\\" with appParams:\\n\", params, \"\\nDevelopment-level warnings: \" + (isDevelopment ? 'ON' : 'OFF') + \".\" + (\"\\nPerformance optimizations: \" + (isDevelopment ? 'OFF' : 'ON') + \".\"));\n    }\n    (0, _invariant.default)(runnables[appKey] && runnables[appKey].run, \"Application \\\"\" + appKey + \"\\\" has not been registered. \" + 'This is either due to an import error during initialization or failure to call AppRegistry.registerComponent.');\n    return runnables[appKey].run(appParameters);\n  }\n  static setComponentProviderInstrumentationHook(hook) {\n    componentProviderInstrumentationHook = hook;\n  }\n  static setWrapperComponentProvider(provider) {\n    wrapperComponentProvider = provider;\n  }\n  static unmountApplicationComponentAtRootTag(rootTag) {\n    (0, _unmountComponentAtNode.default)(rootTag);\n  }\n}\nexports.default = AppRegistry;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAEb,IAAIG,uBAAuB,GAAGH,OAAO,CAAC,+CAA+C,CAAC,CAACI,OAAO;AAC9F,IAAIC,sBAAsB,GAAGL,OAAO,CAAC,8CAA8C,CAAC,CAACI,OAAO;AAC5FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,cAAc,GAAGH,sBAAsB,CAACL,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAIS,UAAU,GAAGJ,sBAAsB,CAACL,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAIU,uBAAuB,GAAGL,sBAAsB,CAACL,OAAO,4BAA4B,CAAC,CAAC;AAC1F,IAAIW,kBAAkB,GAAGR,uBAAuB,CAACH,OAAO,sBAAsB,CAAC,CAAC;AAChF,IAAIY,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,SAAS,GAAG,CAAC,CAAC;AAClB,IAAIC,oCAAoC,GAAG,SAAvCA,oCAAoCA,CAAGC,SAAS;EAAA,OAAIA,SAAS,CAAC,CAAC;AAAA;AACnE,IAAIC,wBAAwB;AAAC,IAKvBC,WAAW;EAAA,SAAAA,YAAA;IAAA,IAAAhB,gBAAA,CAAAG,OAAA,QAAAa,WAAA;EAAA;EAAA,WAAAf,aAAA,CAAAE,OAAA,EAAAa,WAAA;IAAAC,GAAA;IAAAC,KAAA,EACf,SAAOC,UAAUA,CAAA,EAAG;MAClB,OAAOC,MAAM,CAACC,IAAI,CAACT,SAAS,CAAC;IAC/B;EAAC;IAAAK,GAAA;IAAAC,KAAA,EACD,SAAOI,cAAcA,CAACC,MAAM,EAAEC,aAAa,EAAE;MAC3C,CAAC,CAAC,EAAEhB,UAAU,CAACL,OAAO,EAAES,SAAS,CAACW,MAAM,CAAC,IAAIX,SAAS,CAACW,MAAM,CAAC,CAACD,cAAc,EAAE,cAAc,GAAGC,MAAM,GAAG,4BAA4B,GAAG,+GAA+G,CAAC;MACxP,OAAOX,SAAS,CAACW,MAAM,CAAC,CAACD,cAAc,CAACE,aAAa,CAAC;IACxD;EAAC;IAAAP,GAAA;IAAAC,KAAA,EACD,SAAOO,iBAAiBA,CAACF,MAAM,EAAEG,iBAAiB,EAAE;MAClDd,SAAS,CAACW,MAAM,CAAC,GAAG;QAClBD,cAAc,EAAE,SAAhBA,cAAcA,CAAEE,aAAa;UAAA,OAAI,CAAC,CAAC,EAAEd,kBAAkB,CAACY,cAAc,EAAET,oCAAoC,CAACa,iBAAiB,CAAC,EAAEF,aAAa,GAAGA,aAAa,CAACG,YAAY,GAAGhB,WAAW,EAAEI,wBAAwB,IAAIA,wBAAwB,CAACS,aAAa,CAAC,CAAC;QAAA;QAC/PI,GAAG,EAAE,SAALA,GAAGA,CAAEJ,aAAa;UAAA,OAAI,CAAC,CAAC,EAAEd,kBAAkB,CAACP,OAAO,EAAEU,oCAAoC,CAACa,iBAAiB,CAAC,EAAEX,wBAAwB,IAAIA,wBAAwB,CAACS,aAAa,CAAC,EAAEA,aAAa,CAACK,QAAQ,EAAE;YAC1MC,OAAO,EAAEN,aAAa,CAACM,OAAO,IAAI,KAAK;YACvCH,YAAY,EAAEH,aAAa,CAACG,YAAY,IAAIhB,WAAW;YACvDoB,IAAI,EAAEP,aAAa,CAACO,IAAI,IAAI,YAAY;YACxCC,OAAO,EAAER,aAAa,CAACQ;UACzB,CAAC,CAAC;QAAA;MACJ,CAAC;MACD,OAAOT,MAAM;IACf;EAAC;IAAAN,GAAA;IAAAC,KAAA,EACD,SAAOe,cAAcA,CAACC,MAAM,EAAE;MAC5BA,MAAM,CAACC,OAAO,CAAC,UAAAC,IAAI,EAAI;QACrB,IAAIb,MAAM,GAAGa,IAAI,CAACb,MAAM;UACtBT,SAAS,GAAGsB,IAAI,CAACtB,SAAS;UAC1Bc,GAAG,GAAGQ,IAAI,CAACR,GAAG;QAChB,IAAIA,GAAG,EAAE;UACPZ,WAAW,CAACqB,gBAAgB,CAACd,MAAM,EAAEK,GAAG,CAAC;QAC3C,CAAC,MAAM;UACL,CAAC,CAAC,EAAEpB,UAAU,CAACL,OAAO,EAAEW,SAAS,EAAE,iCAAiC,CAAC;UACrEE,WAAW,CAACS,iBAAiB,CAACF,MAAM,EAAET,SAAS,CAAC;QAClD;MACF,CAAC,CAAC;IACJ;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAGD,SAAOmB,gBAAgBA,CAACd,MAAM,EAAEK,GAAG,EAAE;MACnChB,SAAS,CAACW,MAAM,CAAC,GAAG;QAClBK,GAAG,EAAHA;MACF,CAAC;MACD,OAAOL,MAAM;IACf;EAAC;IAAAN,GAAA;IAAAC,KAAA,EACD,SAAOoB,cAAcA,CAACf,MAAM,EAAEC,aAAa,EAAE;MAC3C,IAAIe,aAAa,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM;MAC5F,IAAIH,aAAa,EAAE;QACjB,IAAII,MAAM,GAAG,CAAC,CAAC,EAAEpC,cAAc,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEqB,aAAa,CAAC;QAC3DmB,MAAM,CAACX,OAAO,GAAG,GAAG,GAAGW,MAAM,CAACX,OAAO,CAACY,EAAE;QACxCC,OAAO,CAACC,GAAG,CAAC,wBAAwB,GAAGvB,MAAM,GAAG,sBAAsB,EAAEoB,MAAM,EAAE,gCAAgC,IAAIJ,aAAa,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,+BAA+B,IAAIA,aAAa,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;MACrO;MACA,CAAC,CAAC,EAAE/B,UAAU,CAACL,OAAO,EAAES,SAAS,CAACW,MAAM,CAAC,IAAIX,SAAS,CAACW,MAAM,CAAC,CAACK,GAAG,EAAE,gBAAgB,GAAGL,MAAM,GAAG,8BAA8B,GAAG,+GAA+G,CAAC;MACjP,OAAOX,SAAS,CAACW,MAAM,CAAC,CAACK,GAAG,CAACJ,aAAa,CAAC;IAC7C;EAAC;IAAAP,GAAA;IAAAC,KAAA,EACD,SAAO6B,uCAAuCA,CAACC,IAAI,EAAE;MACnDnC,oCAAoC,GAAGmC,IAAI;IAC7C;EAAC;IAAA/B,GAAA;IAAAC,KAAA,EACD,SAAO+B,2BAA2BA,CAACC,QAAQ,EAAE;MAC3CnC,wBAAwB,GAAGmC,QAAQ;IACrC;EAAC;IAAAjC,GAAA;IAAAC,KAAA,EACD,SAAOiC,oCAAoCA,CAACnB,OAAO,EAAE;MACnD,CAAC,CAAC,EAAEvB,uBAAuB,CAACN,OAAO,EAAE6B,OAAO,CAAC;IAC/C;EAAC;AAAA;AAEH3B,OAAO,CAACF,OAAO,GAAGa,WAAW;AAC7BoC,MAAM,CAAC/C,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}