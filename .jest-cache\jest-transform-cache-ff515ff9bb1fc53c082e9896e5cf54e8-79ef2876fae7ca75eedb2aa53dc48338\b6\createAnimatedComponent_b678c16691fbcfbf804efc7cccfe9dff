04efe9526f2c95c399129f9c16dab606
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = createAnimatedComponent;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _useAnimatedProps2 = _interopRequireDefault(require("./useAnimatedProps"));
var _useMergeRefs = _interopRequireDefault(require("../Utilities/useMergeRefs"));
var _StyleSheet = _interopRequireDefault(require("../../../exports/StyleSheet"));
var _View = _interopRequireDefault(require("../../../exports/View"));
var React = _interopRequireWildcard(require("react"));
var _excluded = ["style"];
function createAnimatedComponent(Component) {
  return React.forwardRef(function (props, forwardedRef) {
    var _useAnimatedProps = (0, _useAnimatedProps2.default)(props),
      reducedProps = _useAnimatedProps[0],
      callbackRef = _useAnimatedProps[1];
    var ref = (0, _useMergeRefs.default)(callbackRef, forwardedRef);
    var passthroughAnimatedPropExplicitValues = reducedProps.passthroughAnimatedPropExplicitValues,
      style = reducedProps.style;
    var _ref = passthroughAnimatedPropExplicitValues !== null && passthroughAnimatedPropExplicitValues !== void 0 ? passthroughAnimatedPropExplicitValues : {},
      passthroughStyle = _ref.style,
      passthroughProps = (0, _objectWithoutPropertiesLoose2.default)(_ref, _excluded);
    var mergedStyle = [style, passthroughStyle];
    return React.createElement(Component, (0, _extends2.default)({}, reducedProps, passthroughProps, {
      style: mergedStyle,
      ref: ref
    }));
  });
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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