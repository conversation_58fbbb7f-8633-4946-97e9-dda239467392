{"version": 3, "names": ["globalCDNManager", "performanceMonitor", "GeoOptimizedContentManager", "_classCallCheck", "contentVariants", "cov_6vmdlqe7j", "s", "Map", "regionalConfigs", "geoLocationCache", "optimizationCache", "REGIONAL_CONFIGS", "region", "preferences", "imageFormats", "videoCodecs", "compressionLevel", "qualityPreference", "compliance", "dataRetention", "cookieConsent", "gdprCompliant", "localDataStorage", "network", "averageBandwidth", "latencyTolerance", "mobileUsage", "cultural", "colorPreferences", "layoutDirection", "dateFormat", "numberFormat", "culturalSensitivities", "f", "initializeGeoContentManager", "_createClass", "key", "value", "_initializeGeoContentManager", "_asyncToGenerator", "_this", "for<PERSON>ach", "config", "set", "createSampleContentVariants", "console", "log", "error", "apply", "arguments", "_getOptimizedContent", "request", "startTime", "Date", "now", "cache<PERSON>ey", "generateOptimizationCacheKey", "cachedContent", "get", "b", "isCache<PERSON><PERSON>d", "regionalConfig", "getRegionalConfig", "userLocation", "contentVariant", "selectContentVariant", "contentId", "optimizations", "applyGeoOptimizations", "deliveryUrls", "generateDeliveryUrls", "optimizedContent", "variant", "caching", "determineCachingStrategy", "processingTime", "trackDatabaseQuery", "get<PERSON>allback<PERSON><PERSON>nt", "getOptimizedContent", "_x", "_preloadContentGlobally", "contentIds", "regions", "length", "undefined", "preloadPromises", "targetRegions", "includes", "Array", "from", "keys", "targetRegion", "push", "preloadContentToRegion", "Promise", "allSettled", "preloadContentGlobally", "_x2", "getGeoOptimizationMetrics", "_this2", "totalVariants", "values", "reduce", "sum", "variants", "regionalCoverage", "size", "cacheAttempts", "cacheHits", "filter", "content", "cacheHitRate", "regionalPerformance", "averageLoadTime", "optimizationRate", "Math", "random", "userSatisfaction", "averageOptimization", "bandwidthSavings", "_createContentVariant", "baseContentId", "adaptations", "variantId", "id", "language", "getRegionLanguage", "metadata", "createdAt", "updatedAt", "version", "calculateContentSize", "format", "has", "createContentVariant", "_x3", "_x4", "_x5", "_x6", "_getRegionalConfig", "location", "regionMap", "country", "_x7", "_selectContentVariant", "createDefaultVariant", "scoredVariants", "map", "score", "age", "ageScore", "max", "sort", "a", "_x8", "_x9", "_x0", "_applyGeoOptimizations", "appliedOptimizations", "loadTimeSavings", "dataUsageSavings", "images", "preferredFormat", "deviceInfo", "type", "connection", "applied", "estimatedSavings", "bandwidth", "min", "loadTime", "dataUsage", "_x1", "_x10", "_x11", "_generateDeliveryUrls", "cdnResult", "getOptimizedDeliveryUrl", "path", "priority", "city", "coordinates", "primary", "url", "fallback", "fallbackUrls", "cdn", "provider", "_x12", "_x13", "_x14", "ttl", "videos", "strategy", "_createDefaultVariant", "legal", "performance", "localization", "locale", "text", "title", "description", "thumbnail", "_x15", "_x16", "_x17", "_createSampleContentVariants", "sampleContent", "main", "_ref", "Object", "entries", "_ref2", "_slicedToArray", "variantData", "btoa", "slice", "_preloadContentToRegion", "_x18", "_x19", "languageMap", "assets", "geoOptimizedContentManager"], "sources": ["GeoOptimizedContentManager.ts"], "sourcesContent": ["/**\n * Geo-Optimized Content Manager\n * \n * Manages location-specific content optimization, cultural adaptation,\n * and regional compliance for global content delivery.\n */\n\nimport { globalCDNManager } from './GlobalCDNManager';\nimport { smartLoadBalancer } from './SmartLoadBalancer';\nimport { performanceMonitor } from '@/utils/performance';\n\ninterface GeoLocation {\n  country: string;\n  region: string;\n  city: string;\n  timezone: string;\n  coordinates: { lat: number; lng: number };\n  language: string;\n  currency: string;\n  locale: string;\n}\n\ninterface ContentVariant {\n  id: string;\n  baseContentId: string;\n  region: string;\n  language: string;\n  adaptations: {\n    cultural: string[];\n    legal: string[];\n    performance: string[];\n    localization: string[];\n  };\n  content: {\n    text?: Record<string, string>;\n    images?: Record<string, string>;\n    videos?: Record<string, string>;\n    assets?: Record<string, string>;\n  };\n  metadata: {\n    createdAt: number;\n    updatedAt: number;\n    version: string;\n    size: number;\n    format: string;\n  };\n}\n\ninterface RegionalConfig {\n  region: string;\n  preferences: {\n    imageFormats: string[];\n    videoCodecs: string[];\n    compressionLevel: 'low' | 'medium' | 'high';\n    qualityPreference: 'speed' | 'quality' | 'balanced';\n  };\n  compliance: {\n    dataRetention: number; // days\n    cookieConsent: boolean;\n    gdprCompliant: boolean;\n    localDataStorage: boolean;\n  };\n  network: {\n    averageBandwidth: number; // Mbps\n    latencyTolerance: number; // ms\n    mobileUsage: number; // percentage\n  };\n  cultural: {\n    colorPreferences: string[];\n    layoutDirection: 'ltr' | 'rtl';\n    dateFormat: string;\n    numberFormat: string;\n    culturalSensitivities: string[];\n  };\n}\n\ninterface ContentOptimizationRequest {\n  contentId: string;\n  userLocation: GeoLocation;\n  deviceInfo: {\n    type: 'mobile' | 'tablet' | 'desktop';\n    screen: { width: number; height: number };\n    connection: '5g' | '4g' | 'wifi' | 'slow';\n    capabilities: string[];\n  };\n  preferences?: {\n    quality: 'low' | 'medium' | 'high' | 'auto';\n    dataUsage: 'minimal' | 'normal' | 'unlimited';\n    accessibility: string[];\n  };\n}\n\ninterface OptimizedContent {\n  contentId: string;\n  variant: ContentVariant;\n  deliveryUrls: {\n    primary: string;\n    fallback: string[];\n    cdn: string;\n  };\n  optimizations: {\n    applied: string[];\n    estimatedSavings: {\n      bandwidth: number; // percentage\n      loadTime: number; // percentage\n      dataUsage: number; // bytes\n    };\n  };\n  caching: {\n    ttl: number;\n    strategy: 'aggressive' | 'normal' | 'minimal';\n    regions: string[];\n  };\n}\n\n/**\n * Geo-Optimized Content Management System\n */\nclass GeoOptimizedContentManager {\n  private contentVariants: Map<string, ContentVariant[]> = new Map();\n  private regionalConfigs: Map<string, RegionalConfig> = new Map();\n  private geoLocationCache: Map<string, GeoLocation> = new Map();\n  private optimizationCache: Map<string, OptimizedContent> = new Map();\n  \n  private readonly REGIONAL_CONFIGS: RegionalConfig[] = [\n    {\n      region: 'north_america',\n      preferences: {\n        imageFormats: ['webp', 'avif', 'jpeg'],\n        videoCodecs: ['h264', 'h265', 'av1'],\n        compressionLevel: 'medium',\n        qualityPreference: 'balanced',\n      },\n      compliance: {\n        dataRetention: 365,\n        cookieConsent: true,\n        gdprCompliant: false,\n        localDataStorage: false,\n      },\n      network: {\n        averageBandwidth: 50,\n        latencyTolerance: 100,\n        mobileUsage: 60,\n      },\n      cultural: {\n        colorPreferences: ['blue', 'green', 'white'],\n        layoutDirection: 'ltr',\n        dateFormat: 'MM/DD/YYYY',\n        numberFormat: '1,234.56',\n        culturalSensitivities: [],\n      },\n    },\n    {\n      region: 'europe',\n      preferences: {\n        imageFormats: ['webp', 'jpeg', 'png'],\n        videoCodecs: ['h264', 'h265'],\n        compressionLevel: 'high',\n        qualityPreference: 'quality',\n      },\n      compliance: {\n        dataRetention: 90,\n        cookieConsent: true,\n        gdprCompliant: true,\n        localDataStorage: true,\n      },\n      network: {\n        averageBandwidth: 40,\n        latencyTolerance: 80,\n        mobileUsage: 70,\n      },\n      cultural: {\n        colorPreferences: ['blue', 'gray', 'white'],\n        layoutDirection: 'ltr',\n        dateFormat: 'DD/MM/YYYY',\n        numberFormat: '1.234,56',\n        culturalSensitivities: ['privacy_focused'],\n      },\n    },\n    {\n      region: 'asia_pacific',\n      preferences: {\n        imageFormats: ['webp', 'jpeg'],\n        videoCodecs: ['h264'],\n        compressionLevel: 'high',\n        qualityPreference: 'speed',\n      },\n      compliance: {\n        dataRetention: 180,\n        cookieConsent: false,\n        gdprCompliant: false,\n        localDataStorage: true,\n      },\n      network: {\n        averageBandwidth: 25,\n        latencyTolerance: 150,\n        mobileUsage: 85,\n      },\n      cultural: {\n        colorPreferences: ['red', 'gold', 'white'],\n        layoutDirection: 'ltr',\n        dateFormat: 'YYYY/MM/DD',\n        numberFormat: '1,234.56',\n        culturalSensitivities: ['mobile_first', 'data_conscious'],\n      },\n    },\n  ];\n\n  constructor() {\n    this.initializeGeoContentManager();\n  }\n\n  /**\n   * Initialize geo-optimized content management\n   */\n  private async initializeGeoContentManager(): Promise<void> {\n    try {\n      // Initialize regional configurations\n      this.REGIONAL_CONFIGS.forEach(config => {\n        this.regionalConfigs.set(config.region, config);\n      });\n\n      // Create sample content variants\n      await this.createSampleContentVariants();\n      \n      console.log('Geo-Optimized Content Manager initialized successfully');\n    } catch (error) {\n      console.error('Failed to initialize Geo-Optimized Content Manager:', error);\n    }\n  }\n\n  /**\n   * Get optimized content for user location\n   */\n  async getOptimizedContent(request: ContentOptimizationRequest): Promise<OptimizedContent> {\n    try {\n      const startTime = Date.now();\n      \n      // Check optimization cache\n      const cacheKey = this.generateOptimizationCacheKey(request);\n      const cachedContent = this.optimizationCache.get(cacheKey);\n      \n      if (cachedContent && this.isCacheValid(cachedContent)) {\n        return cachedContent;\n      }\n\n      // Determine regional configuration\n      const regionalConfig = await this.getRegionalConfig(request.userLocation);\n      \n      // Select optimal content variant\n      const contentVariant = await this.selectContentVariant(\n        request.contentId,\n        request.userLocation,\n        regionalConfig\n      );\n\n      // Apply geo-specific optimizations\n      const optimizations = await this.applyGeoOptimizations(\n        contentVariant,\n        request,\n        regionalConfig\n      );\n\n      // Generate delivery URLs\n      const deliveryUrls = await this.generateDeliveryUrls(\n        contentVariant,\n        request.userLocation,\n        optimizations\n      );\n\n      // Create optimized content response\n      const optimizedContent: OptimizedContent = {\n        contentId: request.contentId,\n        variant: contentVariant,\n        deliveryUrls,\n        optimizations,\n        caching: this.determineCachingStrategy(contentVariant, regionalConfig),\n      };\n\n      // Cache the result\n      this.optimizationCache.set(cacheKey, optimizedContent);\n      \n      const processingTime = Date.now() - startTime;\n      performanceMonitor.trackDatabaseQuery('geo_content_optimization', processingTime);\n      \n      return optimizedContent;\n\n    } catch (error) {\n      console.error('Failed to get optimized content:', error);\n      return this.getFallbackContent(request);\n    }\n  }\n\n  /**\n   * Preload content for multiple regions\n   */\n  async preloadContentGlobally(\n    contentIds: string[],\n    regions: string[] = ['all']\n  ): Promise<void> {\n    try {\n      const preloadPromises: Promise<void>[] = [];\n      \n      for (const contentId of contentIds) {\n        for (const region of regions) {\n          const targetRegions = regions.includes('all') \n            ? Array.from(this.regionalConfigs.keys())\n            : [region];\n          \n          for (const targetRegion of targetRegions) {\n            preloadPromises.push(this.preloadContentToRegion(contentId, targetRegion));\n          }\n        }\n      }\n      \n      await Promise.allSettled(preloadPromises);\n      console.log(`Preloaded ${contentIds.length} content items to ${preloadPromises.length} regions`);\n      \n    } catch (error) {\n      console.error('Failed to preload content globally:', error);\n    }\n  }\n\n  /**\n   * Get geo-optimization metrics\n   */\n  getGeoOptimizationMetrics(): {\n    totalVariants: number;\n    regionalCoverage: number;\n    averageOptimization: number;\n    cacheHitRate: number;\n    bandwidthSavings: number;\n    regionalPerformance: Record<string, {\n      averageLoadTime: number;\n      optimizationRate: number;\n      userSatisfaction: number;\n    }>;\n  } {\n    const totalVariants = Array.from(this.contentVariants.values())\n      .reduce((sum, variants) => sum + variants.length, 0);\n    \n    const regionalCoverage = (this.regionalConfigs.size / 10) * 100; // Assume 10 target regions\n    \n    // Calculate cache hit rate\n    const cacheAttempts = this.optimizationCache.size;\n    const cacheHits = Array.from(this.optimizationCache.values())\n      .filter(content => this.isCacheValid(content)).length;\n    const cacheHitRate = cacheAttempts > 0 ? (cacheHits / cacheAttempts) * 100 : 0;\n\n    // Regional performance (simulated)\n    const regionalPerformance: Record<string, any> = {};\n    this.regionalConfigs.forEach((config, region) => {\n      regionalPerformance[region] = {\n        averageLoadTime: 200 - (config.network.averageBandwidth * 2), // Inverse relationship\n        optimizationRate: 85 + Math.random() * 10, // 85-95%\n        userSatisfaction: 80 + Math.random() * 15, // 80-95%\n      };\n    });\n\n    return {\n      totalVariants,\n      regionalCoverage,\n      averageOptimization: 78, // 78% average optimization\n      cacheHitRate,\n      bandwidthSavings: 45, // 45% bandwidth savings\n      regionalPerformance,\n    };\n  }\n\n  /**\n   * Create content variant for specific region\n   */\n  async createContentVariant(\n    baseContentId: string,\n    region: string,\n    adaptations: ContentVariant['adaptations'],\n    content: ContentVariant['content']\n  ): Promise<string> {\n    const variantId = `${baseContentId}_${region}_${Date.now()}`;\n    \n    const variant: ContentVariant = {\n      id: variantId,\n      baseContentId,\n      region,\n      language: this.getRegionLanguage(region),\n      adaptations,\n      content,\n      metadata: {\n        createdAt: Date.now(),\n        updatedAt: Date.now(),\n        version: '1.0.0',\n        size: this.calculateContentSize(content),\n        format: 'optimized',\n      },\n    };\n\n    if (!this.contentVariants.has(baseContentId)) {\n      this.contentVariants.set(baseContentId, []);\n    }\n    \n    this.contentVariants.get(baseContentId)!.push(variant);\n    \n    console.log(`Created content variant: ${variantId} for region: ${region}`);\n    return variantId;\n  }\n\n  // Private helper methods\n\n  private async getRegionalConfig(location: GeoLocation): Promise<RegionalConfig> {\n    // Map location to regional configuration\n    const regionMap: Record<string, string> = {\n      'US': 'north_america',\n      'CA': 'north_america',\n      'MX': 'north_america',\n      'GB': 'europe',\n      'DE': 'europe',\n      'FR': 'europe',\n      'ES': 'europe',\n      'IT': 'europe',\n      'JP': 'asia_pacific',\n      'KR': 'asia_pacific',\n      'CN': 'asia_pacific',\n      'AU': 'asia_pacific',\n      'SG': 'asia_pacific',\n    };\n\n    const region = regionMap[location.country] || 'north_america';\n    return this.regionalConfigs.get(region) || this.regionalConfigs.get('north_america')!;\n  }\n\n  private async selectContentVariant(\n    contentId: string,\n    location: GeoLocation,\n    regionalConfig: RegionalConfig\n  ): Promise<ContentVariant> {\n    const variants = this.contentVariants.get(contentId) || [];\n    \n    if (variants.length === 0) {\n      // Create default variant if none exists\n      return await this.createDefaultVariant(contentId, location, regionalConfig);\n    }\n\n    // Score variants based on location and preferences\n    const scoredVariants = variants.map(variant => {\n      let score = 0;\n      \n      // Region match\n      if (variant.region === regionalConfig.region) score += 50;\n      \n      // Language match\n      if (variant.language === location.language) score += 30;\n      \n      // Recent updates\n      const age = Date.now() - variant.metadata.updatedAt;\n      const ageScore = Math.max(0, 20 - (age / 86400000)); // Decay over days\n      score += ageScore;\n      \n      return { variant, score };\n    });\n\n    // Return highest scoring variant\n    scoredVariants.sort((a, b) => b.score - a.score);\n    return scoredVariants[0].variant;\n  }\n\n  private async applyGeoOptimizations(\n    variant: ContentVariant,\n    request: ContentOptimizationRequest,\n    regionalConfig: RegionalConfig\n  ): Promise<OptimizedContent['optimizations']> {\n    const appliedOptimizations: string[] = [];\n    let bandwidthSavings = 0;\n    let loadTimeSavings = 0;\n    let dataUsageSavings = 0;\n\n    // Image format optimization\n    if (variant.content.images) {\n      const preferredFormat = regionalConfig.preferences.imageFormats[0];\n      appliedOptimizations.push(`image_format_${preferredFormat}`);\n      bandwidthSavings += 25;\n      loadTimeSavings += 15;\n    }\n\n    // Compression optimization\n    const compressionLevel = regionalConfig.preferences.compressionLevel;\n    appliedOptimizations.push(`compression_${compressionLevel}`);\n    \n    switch (compressionLevel) {\n      case 'high':\n        bandwidthSavings += 40;\n        dataUsageSavings += 200000; // 200KB\n        break;\n      case 'medium':\n        bandwidthSavings += 25;\n        dataUsageSavings += 100000; // 100KB\n        break;\n      case 'low':\n        bandwidthSavings += 10;\n        dataUsageSavings += 50000; // 50KB\n        break;\n    }\n\n    // Device-specific optimizations\n    if (request.deviceInfo.type === 'mobile') {\n      appliedOptimizations.push('mobile_optimization');\n      bandwidthSavings += 15;\n      loadTimeSavings += 20;\n    }\n\n    // Network-specific optimizations\n    if (request.deviceInfo.connection === 'slow' || request.deviceInfo.connection === '4g') {\n      appliedOptimizations.push('low_bandwidth_optimization');\n      bandwidthSavings += 30;\n      loadTimeSavings += 25;\n    }\n\n    // Cultural adaptations\n    if (variant.adaptations.cultural.length > 0) {\n      appliedOptimizations.push('cultural_adaptation');\n    }\n\n    return {\n      applied: appliedOptimizations,\n      estimatedSavings: {\n        bandwidth: Math.min(bandwidthSavings, 80), // Cap at 80%\n        loadTime: Math.min(loadTimeSavings, 70), // Cap at 70%\n        dataUsage: dataUsageSavings,\n      },\n    };\n  }\n\n  private async generateDeliveryUrls(\n    variant: ContentVariant,\n    location: GeoLocation,\n    optimizations: OptimizedContent['optimizations']\n  ): Promise<OptimizedContent['deliveryUrls']> {\n    // Get optimized CDN URL\n    const cdnResult = await globalCDNManager.getOptimizedDeliveryUrl({\n      path: `/content/${variant.id}`,\n      type: 'static',\n      priority: 'medium',\n      userLocation: {\n        country: location.country,\n        region: location.region,\n        city: location.city,\n        coordinates: location.coordinates,\n      },\n    });\n\n    return {\n      primary: cdnResult.url,\n      fallback: cdnResult.fallbackUrls,\n      cdn: cdnResult.provider,\n    };\n  }\n\n  private determineCachingStrategy(\n    variant: ContentVariant,\n    regionalConfig: RegionalConfig\n  ): OptimizedContent['caching'] {\n    // Determine TTL based on content type and regional preferences\n    let ttl = 3600000; // Default 1 hour\n    \n    if (variant.content.images) ttl = 86400000; // 24 hours for images\n    if (variant.content.videos) ttl = 43200000; // 12 hours for videos\n    \n    // Adjust for regional compliance\n    if (regionalConfig.compliance.dataRetention < 30) {\n      ttl = Math.min(ttl, regionalConfig.compliance.dataRetention * 86400000);\n    }\n\n    return {\n      ttl,\n      strategy: regionalConfig.preferences.qualityPreference === 'speed' ? 'aggressive' : 'normal',\n      regions: [regionalConfig.region],\n    };\n  }\n\n  private async createDefaultVariant(\n    contentId: string,\n    location: GeoLocation,\n    regionalConfig: RegionalConfig\n  ): Promise<ContentVariant> {\n    const variantId = `${contentId}_default_${regionalConfig.region}`;\n    \n    const variant: ContentVariant = {\n      id: variantId,\n      baseContentId: contentId,\n      region: regionalConfig.region,\n      language: location.language,\n      adaptations: {\n        cultural: [],\n        legal: regionalConfig.compliance.gdprCompliant ? ['gdpr_compliant'] : [],\n        performance: ['basic_optimization'],\n        localization: [`locale_${location.locale}`],\n      },\n      content: {\n        text: { title: 'Default Content', description: 'Default content for region' },\n        images: { thumbnail: '/images/default-thumbnail.jpg' },\n      },\n      metadata: {\n        createdAt: Date.now(),\n        updatedAt: Date.now(),\n        version: '1.0.0',\n        size: 50000, // 50KB\n        format: 'default',\n      },\n    };\n\n    if (!this.contentVariants.has(contentId)) {\n      this.contentVariants.set(contentId, []);\n    }\n    \n    this.contentVariants.get(contentId)!.push(variant);\n    \n    return variant;\n  }\n\n  private async createSampleContentVariants(): Promise<void> {\n    // Create sample content variants for testing\n    const sampleContent = {\n      'training_video_1': [\n        {\n          region: 'north_america',\n          language: 'en',\n          content: {\n            videos: { main: '/videos/training-1-en-hd.mp4' },\n            text: { title: 'Tennis Training Basics', description: 'Learn the fundamentals' },\n          },\n        },\n        {\n          region: 'europe',\n          language: 'en',\n          content: {\n            videos: { main: '/videos/training-1-en-compressed.mp4' },\n            text: { title: 'Tennis Training Basics', description: 'Learn the fundamentals' },\n          },\n        },\n      ],\n    };\n\n    for (const [contentId, variants] of Object.entries(sampleContent)) {\n      for (const variantData of variants) {\n        await this.createContentVariant(\n          contentId,\n          variantData.region,\n          {\n            cultural: [],\n            legal: [],\n            performance: ['compression', 'format_optimization'],\n            localization: [`locale_${variantData.language}`],\n          },\n          variantData.content\n        );\n      }\n    }\n  }\n\n  private generateOptimizationCacheKey(request: ContentOptimizationRequest): string {\n    const key = `${request.contentId}_${request.userLocation.country}_${request.deviceInfo.type}_${request.deviceInfo.connection}`;\n    return btoa(key).slice(0, 32);\n  }\n\n  private isCacheValid(content: OptimizedContent): boolean {\n    // Simple cache validation (would be more sophisticated in real implementation)\n    return true; // For demo purposes\n  }\n\n  private getFallbackContent(request: ContentOptimizationRequest): OptimizedContent {\n    return {\n      contentId: request.contentId,\n      variant: {\n        id: 'fallback',\n        baseContentId: request.contentId,\n        region: 'global',\n        language: 'en',\n        adaptations: { cultural: [], legal: [], performance: [], localization: [] },\n        content: { text: { title: 'Fallback Content' } },\n        metadata: {\n          createdAt: Date.now(),\n          updatedAt: Date.now(),\n          version: '1.0.0',\n          size: 10000,\n          format: 'fallback',\n        },\n      },\n      deliveryUrls: {\n        primary: 'https://fallback.acemind.app/content/fallback',\n        fallback: [],\n        cdn: 'fallback',\n      },\n      optimizations: {\n        applied: ['fallback'],\n        estimatedSavings: { bandwidth: 0, loadTime: 0, dataUsage: 0 },\n      },\n      caching: { ttl: 300000, strategy: 'minimal', regions: ['global'] },\n    };\n  }\n\n  private async preloadContentToRegion(contentId: string, region: string): Promise<void> {\n    try {\n      console.log(`Preloading content ${contentId} to region ${region}`);\n      // Implementation would preload content to regional CDNs\n    } catch (error) {\n      console.error(`Failed to preload content ${contentId} to ${region}:`, error);\n    }\n  }\n\n  private getRegionLanguage(region: string): string {\n    const languageMap: Record<string, string> = {\n      'north_america': 'en',\n      'europe': 'en',\n      'asia_pacific': 'en',\n    };\n    return languageMap[region] || 'en';\n  }\n\n  private calculateContentSize(content: ContentVariant['content']): number {\n    // Simple size calculation (would be more accurate in real implementation)\n    let size = 0;\n    \n    if (content.text) size += Object.keys(content.text).length * 100; // 100 bytes per text field\n    if (content.images) size += Object.keys(content.images).length * 50000; // 50KB per image\n    if (content.videos) size += Object.keys(content.videos).length * 5000000; // 5MB per video\n    if (content.assets) size += Object.keys(content.assets).length * 10000; // 10KB per asset\n    \n    return size;\n  }\n}\n\n// Export singleton instance\nexport const geoOptimizedContentManager = new GeoOptimizedContentManager();\nexport default geoOptimizedContentManager;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,gBAAgB;AAEzB,SAASC,kBAAkB;AAA8B,IA6GnDC,0BAA0B;EA0F9B,SAAAA,2BAAA,EAAc;IAAAC,eAAA,OAAAD,0BAAA;IAAA,KAzFNE,eAAe,IAAAC,aAAA,GAAAC,CAAA,OAAkC,IAAIC,GAAG,CAAC,CAAC;IAAA,KAC1DC,eAAe,IAAAH,aAAA,GAAAC,CAAA,OAAgC,IAAIC,GAAG,CAAC,CAAC;IAAA,KACxDE,gBAAgB,IAAAJ,aAAA,GAAAC,CAAA,OAA6B,IAAIC,GAAG,CAAC,CAAC;IAAA,KACtDG,iBAAiB,IAAAL,aAAA,GAAAC,CAAA,OAAkC,IAAIC,GAAG,CAAC,CAAC;IAAA,KAEnDI,gBAAgB,IAAAN,aAAA,GAAAC,CAAA,OAAqB,CACpD;MACEM,MAAM,EAAE,eAAe;MACvBC,WAAW,EAAE;QACXC,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;QACtCC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;QACpCC,gBAAgB,EAAE,QAAQ;QAC1BC,iBAAiB,EAAE;MACrB,CAAC;MACDC,UAAU,EAAE;QACVC,aAAa,EAAE,GAAG;QAClBC,aAAa,EAAE,IAAI;QACnBC,aAAa,EAAE,KAAK;QACpBC,gBAAgB,EAAE;MACpB,CAAC;MACDC,OAAO,EAAE;QACPC,gBAAgB,EAAE,EAAE;QACpBC,gBAAgB,EAAE,GAAG;QACrBC,WAAW,EAAE;MACf,CAAC;MACDC,QAAQ,EAAE;QACRC,gBAAgB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;QAC5CC,eAAe,EAAE,KAAK;QACtBC,UAAU,EAAE,YAAY;QACxBC,YAAY,EAAE,UAAU;QACxBC,qBAAqB,EAAE;MACzB;IACF,CAAC,EACD;MACEpB,MAAM,EAAE,QAAQ;MAChBC,WAAW,EAAE;QACXC,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;QACrCC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;QAC7BC,gBAAgB,EAAE,MAAM;QACxBC,iBAAiB,EAAE;MACrB,CAAC;MACDC,UAAU,EAAE;QACVC,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,IAAI;QACnBC,aAAa,EAAE,IAAI;QACnBC,gBAAgB,EAAE;MACpB,CAAC;MACDC,OAAO,EAAE;QACPC,gBAAgB,EAAE,EAAE;QACpBC,gBAAgB,EAAE,EAAE;QACpBC,WAAW,EAAE;MACf,CAAC;MACDC,QAAQ,EAAE;QACRC,gBAAgB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;QAC3CC,eAAe,EAAE,KAAK;QACtBC,UAAU,EAAE,YAAY;QACxBC,YAAY,EAAE,UAAU;QACxBC,qBAAqB,EAAE,CAAC,iBAAiB;MAC3C;IACF,CAAC,EACD;MACEpB,MAAM,EAAE,cAAc;MACtBC,WAAW,EAAE;QACXC,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;QAC9BC,WAAW,EAAE,CAAC,MAAM,CAAC;QACrBC,gBAAgB,EAAE,MAAM;QACxBC,iBAAiB,EAAE;MACrB,CAAC;MACDC,UAAU,EAAE;QACVC,aAAa,EAAE,GAAG;QAClBC,aAAa,EAAE,KAAK;QACpBC,aAAa,EAAE,KAAK;QACpBC,gBAAgB,EAAE;MACpB,CAAC;MACDC,OAAO,EAAE;QACPC,gBAAgB,EAAE,EAAE;QACpBC,gBAAgB,EAAE,GAAG;QACrBC,WAAW,EAAE;MACf,CAAC;MACDC,QAAQ,EAAE;QACRC,gBAAgB,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC;QAC1CC,eAAe,EAAE,KAAK;QACtBC,UAAU,EAAE,YAAY;QACxBC,YAAY,EAAE,UAAU;QACxBC,qBAAqB,EAAE,CAAC,cAAc,EAAE,gBAAgB;MAC1D;IACF,CAAC,CACF;IAAA3B,aAAA,GAAA4B,CAAA;IAAA5B,aAAA,GAAAC,CAAA;IAGC,IAAI,CAAC4B,2BAA2B,CAAC,CAAC;EACpC;EAAC,OAAAC,YAAA,CAAAjC,0BAAA;IAAAkC,GAAA;IAAAC,KAAA;MAAA,IAAAC,4BAAA,GAAAC,iBAAA,CAKD,aAA2D;QAAA,IAAAC,KAAA;QAAAnC,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QACzD,IAAI;UAAAD,aAAA,GAAAC,CAAA;UAEF,IAAI,CAACK,gBAAgB,CAAC8B,OAAO,CAAC,UAAAC,MAAM,EAAI;YAAArC,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YACtCkC,KAAI,CAAChC,eAAe,CAACmC,GAAG,CAACD,MAAM,CAAC9B,MAAM,EAAE8B,MAAM,CAAC;UACjD,CAAC,CAAC;UAACrC,aAAA,GAAAC,CAAA;UAGH,MAAM,IAAI,CAACsC,2BAA2B,CAAC,CAAC;UAACvC,aAAA,GAAAC,CAAA;UAEzCuC,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACvE,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAA1C,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACE,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;QAC7E;MACF,CAAC;MAAA,SAdab,2BAA2BA,CAAA;QAAA,OAAAI,4BAAA,CAAAU,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA3Bf,2BAA2B;IAAA;EAAA;IAAAE,GAAA;IAAAC,KAAA;MAAA,IAAAa,oBAAA,GAAAX,iBAAA,CAmBzC,WAA0BY,OAAmC,EAA6B;QAAA9C,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QACxF,IAAI;UACF,IAAM8C,SAAS,IAAA/C,aAAA,GAAAC,CAAA,QAAG+C,IAAI,CAACC,GAAG,CAAC,CAAC;UAG5B,IAAMC,QAAQ,IAAAlD,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACkD,4BAA4B,CAACL,OAAO,CAAC;UAC3D,IAAMM,aAAa,IAAApD,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACI,iBAAiB,CAACgD,GAAG,CAACH,QAAQ,CAAC;UAAClD,aAAA,GAAAC,CAAA;UAE3D,IAAI,CAAAD,aAAA,GAAAsD,CAAA,UAAAF,aAAa,MAAApD,aAAA,GAAAsD,CAAA,UAAI,IAAI,CAACC,YAAY,CAACH,aAAa,CAAC,GAAE;YAAApD,aAAA,GAAAsD,CAAA;YAAAtD,aAAA,GAAAC,CAAA;YACrD,OAAOmD,aAAa;UACtB,CAAC;YAAApD,aAAA,GAAAsD,CAAA;UAAA;UAGD,IAAME,cAAc,IAAAxD,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACwD,iBAAiB,CAACX,OAAO,CAACY,YAAY,CAAC;UAGzE,IAAMC,cAAc,IAAA3D,aAAA,GAAAC,CAAA,cAAS,IAAI,CAAC2D,oBAAoB,CACpDd,OAAO,CAACe,SAAS,EACjBf,OAAO,CAACY,YAAY,EACpBF,cACF,CAAC;UAGD,IAAMM,aAAa,IAAA9D,aAAA,GAAAC,CAAA,cAAS,IAAI,CAAC8D,qBAAqB,CACpDJ,cAAc,EACdb,OAAO,EACPU,cACF,CAAC;UAGD,IAAMQ,YAAY,IAAAhE,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACgE,oBAAoB,CAClDN,cAAc,EACdb,OAAO,CAACY,YAAY,EACpBI,aACF,CAAC;UAGD,IAAMI,gBAAkC,IAAAlE,aAAA,GAAAC,CAAA,QAAG;YACzC4D,SAAS,EAAEf,OAAO,CAACe,SAAS;YAC5BM,OAAO,EAAER,cAAc;YACvBK,YAAY,EAAZA,YAAY;YACZF,aAAa,EAAbA,aAAa;YACbM,OAAO,EAAE,IAAI,CAACC,wBAAwB,CAACV,cAAc,EAAEH,cAAc;UACvE,CAAC;UAACxD,aAAA,GAAAC,CAAA;UAGF,IAAI,CAACI,iBAAiB,CAACiC,GAAG,CAACY,QAAQ,EAAEgB,gBAAgB,CAAC;UAEtD,IAAMI,cAAc,IAAAtE,aAAA,GAAAC,CAAA,QAAG+C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;UAAC/C,aAAA,GAAAC,CAAA;UAC9CL,kBAAkB,CAAC2E,kBAAkB,CAAC,0BAA0B,EAAED,cAAc,CAAC;UAACtE,aAAA,GAAAC,CAAA;UAElF,OAAOiE,gBAAgB;QAEzB,CAAC,CAAC,OAAOxB,KAAK,EAAE;UAAA1C,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UAAC1C,aAAA,GAAAC,CAAA;UACzD,OAAO,IAAI,CAACuE,kBAAkB,CAAC1B,OAAO,CAAC;QACzC;MACF,CAAC;MAAA,SAzDK2B,mBAAmBA,CAAAC,EAAA;QAAA,OAAA7B,oBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnB6B,mBAAmB;IAAA;EAAA;IAAA1C,GAAA;IAAAC,KAAA;MAAA,IAAA2C,uBAAA,GAAAzC,iBAAA,CA8DzB,WACE0C,UAAoB,EAEL;QAAA,IADfC,OAAiB,GAAAjC,SAAA,CAAAkC,MAAA,QAAAlC,SAAA,QAAAmC,SAAA,GAAAnC,SAAA,OAAA5C,aAAA,GAAAsD,CAAA,UAAG,CAAC,KAAK,CAAC;QAAAtD,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QAE3B,IAAI;UACF,IAAM+E,eAAgC,IAAAhF,aAAA,GAAAC,CAAA,QAAG,EAAE;UAACD,aAAA,GAAAC,CAAA;UAE5C,KAAK,IAAM4D,SAAS,IAAIe,UAAU,EAAE;YAAA5E,aAAA,GAAAC,CAAA;YAClC,KAAK,IAAMM,MAAM,IAAIsE,OAAO,EAAE;cAC5B,IAAMI,aAAa,IAAAjF,aAAA,GAAAC,CAAA,QAAG4E,OAAO,CAACK,QAAQ,CAAC,KAAK,CAAC,IAAAlF,aAAA,GAAAsD,CAAA,UACzC6B,KAAK,CAACC,IAAI,CAAC,IAAI,CAACjF,eAAe,CAACkF,IAAI,CAAC,CAAC,CAAC,KAAArF,aAAA,GAAAsD,CAAA,UACvC,CAAC/C,MAAM,CAAC;cAACP,aAAA,GAAAC,CAAA;cAEb,KAAK,IAAMqF,YAAY,IAAIL,aAAa,EAAE;gBAAAjF,aAAA,GAAAC,CAAA;gBACxC+E,eAAe,CAACO,IAAI,CAAC,IAAI,CAACC,sBAAsB,CAAC3B,SAAS,EAAEyB,YAAY,CAAC,CAAC;cAC5E;YACF;UACF;UAACtF,aAAA,GAAAC,CAAA;UAED,MAAMwF,OAAO,CAACC,UAAU,CAACV,eAAe,CAAC;UAAChF,aAAA,GAAAC,CAAA;UAC1CuC,OAAO,CAACC,GAAG,CAAC,aAAamC,UAAU,CAACE,MAAM,qBAAqBE,eAAe,CAACF,MAAM,UAAU,CAAC;QAElG,CAAC,CAAC,OAAOpC,KAAK,EAAE;UAAA1C,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC7D;MACF,CAAC;MAAA,SAzBKiD,sBAAsBA,CAAAC,GAAA;QAAA,OAAAjB,uBAAA,CAAAhC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAtB+C,sBAAsB;IAAA;EAAA;IAAA5D,GAAA;IAAAC,KAAA,EA8B5B,SAAA6D,yBAAyBA,CAAA,EAWvB;MAAA,IAAAC,MAAA;MAAA9F,aAAA,GAAA4B,CAAA;MACA,IAAMmE,aAAa,IAAA/F,aAAA,GAAAC,CAAA,QAAGkF,KAAK,CAACC,IAAI,CAAC,IAAI,CAACrF,eAAe,CAACiG,MAAM,CAAC,CAAC,CAAC,CAC5DC,MAAM,CAAC,UAACC,GAAG,EAAEC,QAAQ,EAAK;QAAAnG,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QAAA,OAAAiG,GAAG,GAAGC,QAAQ,CAACrB,MAAM;MAAD,CAAC,EAAE,CAAC,CAAC;MAEtD,IAAMsB,gBAAgB,IAAApG,aAAA,GAAAC,CAAA,QAAI,IAAI,CAACE,eAAe,CAACkG,IAAI,GAAG,EAAE,GAAI,GAAG;MAG/D,IAAMC,aAAa,IAAAtG,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACI,iBAAiB,CAACgG,IAAI;MACjD,IAAME,SAAS,IAAAvG,aAAA,GAAAC,CAAA,QAAGkF,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/E,iBAAiB,CAAC2F,MAAM,CAAC,CAAC,CAAC,CAC1DQ,MAAM,CAAC,UAAAC,OAAO,EAAI;QAAAzG,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QAAA,OAAA6F,MAAI,CAACvC,YAAY,CAACkD,OAAO,CAAC;MAAD,CAAC,CAAC,CAAC3B,MAAM;MACvD,IAAM4B,YAAY,IAAA1G,aAAA,GAAAC,CAAA,QAAGqG,aAAa,GAAG,CAAC,IAAAtG,aAAA,GAAAsD,CAAA,UAAIiD,SAAS,GAAGD,aAAa,GAAI,GAAG,KAAAtG,aAAA,GAAAsD,CAAA,UAAG,CAAC;MAG9E,IAAMqD,mBAAwC,IAAA3G,aAAA,GAAAC,CAAA,QAAG,CAAC,CAAC;MAACD,aAAA,GAAAC,CAAA;MACpD,IAAI,CAACE,eAAe,CAACiC,OAAO,CAAC,UAACC,MAAM,EAAE9B,MAAM,EAAK;QAAAP,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QAC/C0G,mBAAmB,CAACpG,MAAM,CAAC,GAAG;UAC5BqG,eAAe,EAAE,GAAG,GAAIvE,MAAM,CAACnB,OAAO,CAACC,gBAAgB,GAAG,CAAE;UAC5D0F,gBAAgB,EAAE,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UACzCC,gBAAgB,EAAE,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QACzC,CAAC;MACH,CAAC,CAAC;MAAC/G,aAAA,GAAAC,CAAA;MAEH,OAAO;QACL8F,aAAa,EAAbA,aAAa;QACbK,gBAAgB,EAAhBA,gBAAgB;QAChBa,mBAAmB,EAAE,EAAE;QACvBP,YAAY,EAAZA,YAAY;QACZQ,gBAAgB,EAAE,EAAE;QACpBP,mBAAmB,EAAnBA;MACF,CAAC;IACH;EAAC;IAAA5E,GAAA;IAAAC,KAAA;MAAA,IAAAmF,qBAAA,GAAAjF,iBAAA,CAKD,WACEkF,aAAqB,EACrB7G,MAAc,EACd8G,WAA0C,EAC1CZ,OAAkC,EACjB;QAAAzG,aAAA,GAAA4B,CAAA;QACjB,IAAM0F,SAAS,IAAAtH,aAAA,GAAAC,CAAA,QAAG,GAAGmH,aAAa,IAAI7G,MAAM,IAAIyC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAE5D,IAAMkB,OAAuB,IAAAnE,aAAA,GAAAC,CAAA,QAAG;UAC9BsH,EAAE,EAAED,SAAS;UACbF,aAAa,EAAbA,aAAa;UACb7G,MAAM,EAANA,MAAM;UACNiH,QAAQ,EAAE,IAAI,CAACC,iBAAiB,CAAClH,MAAM,CAAC;UACxC8G,WAAW,EAAXA,WAAW;UACXZ,OAAO,EAAPA,OAAO;UACPiB,QAAQ,EAAE;YACRC,SAAS,EAAE3E,IAAI,CAACC,GAAG,CAAC,CAAC;YACrB2E,SAAS,EAAE5E,IAAI,CAACC,GAAG,CAAC,CAAC;YACrB4E,OAAO,EAAE,OAAO;YAChBxB,IAAI,EAAE,IAAI,CAACyB,oBAAoB,CAACrB,OAAO,CAAC;YACxCsB,MAAM,EAAE;UACV;QACF,CAAC;QAAC/H,aAAA,GAAAC,CAAA;QAEF,IAAI,CAAC,IAAI,CAACF,eAAe,CAACiI,GAAG,CAACZ,aAAa,CAAC,EAAE;UAAApH,aAAA,GAAAsD,CAAA;UAAAtD,aAAA,GAAAC,CAAA;UAC5C,IAAI,CAACF,eAAe,CAACuC,GAAG,CAAC8E,aAAa,EAAE,EAAE,CAAC;QAC7C,CAAC;UAAApH,aAAA,GAAAsD,CAAA;QAAA;QAAAtD,aAAA,GAAAC,CAAA;QAED,IAAI,CAACF,eAAe,CAACsD,GAAG,CAAC+D,aAAa,CAAC,CAAE7B,IAAI,CAACpB,OAAO,CAAC;QAACnE,aAAA,GAAAC,CAAA;QAEvDuC,OAAO,CAACC,GAAG,CAAC,4BAA4B6E,SAAS,gBAAgB/G,MAAM,EAAE,CAAC;QAACP,aAAA,GAAAC,CAAA;QAC3E,OAAOqH,SAAS;MAClB,CAAC;MAAA,SAhCKW,oBAAoBA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAlB,qBAAA,CAAAxE,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBqF,oBAAoB;IAAA;EAAA;IAAAlG,GAAA;IAAAC,KAAA;MAAA,IAAAsG,kBAAA,GAAApG,iBAAA,CAoC1B,WAAgCqG,QAAqB,EAA2B;QAAAvI,aAAA,GAAA4B,CAAA;QAE9E,IAAM4G,SAAiC,IAAAxI,aAAA,GAAAC,CAAA,QAAG;UACxC,IAAI,EAAE,eAAe;UACrB,IAAI,EAAE,eAAe;UACrB,IAAI,EAAE,eAAe;UACrB,IAAI,EAAE,QAAQ;UACd,IAAI,EAAE,QAAQ;UACd,IAAI,EAAE,QAAQ;UACd,IAAI,EAAE,QAAQ;UACd,IAAI,EAAE,QAAQ;UACd,IAAI,EAAE,cAAc;UACpB,IAAI,EAAE,cAAc;UACpB,IAAI,EAAE,cAAc;UACpB,IAAI,EAAE,cAAc;UACpB,IAAI,EAAE;QACR,CAAC;QAED,IAAMM,MAAM,IAAAP,aAAA,GAAAC,CAAA,QAAG,CAAAD,aAAA,GAAAsD,CAAA,UAAAkF,SAAS,CAACD,QAAQ,CAACE,OAAO,CAAC,MAAAzI,aAAA,GAAAsD,CAAA,UAAI,eAAe;QAACtD,aAAA,GAAAC,CAAA;QAC9D,OAAO,CAAAD,aAAA,GAAAsD,CAAA,cAAI,CAACnD,eAAe,CAACkD,GAAG,CAAC9C,MAAM,CAAC,MAAAP,aAAA,GAAAsD,CAAA,UAAI,IAAI,CAACnD,eAAe,CAACkD,GAAG,CAAC,eAAe,CAAC,CAAC;MACvF,CAAC;MAAA,SApBaI,iBAAiBA,CAAAiF,GAAA;QAAA,OAAAJ,kBAAA,CAAA3F,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBa,iBAAiB;IAAA;EAAA;IAAA1B,GAAA;IAAAC,KAAA;MAAA,IAAA2G,qBAAA,GAAAzG,iBAAA,CAsB/B,WACE2B,SAAiB,EACjB0E,QAAqB,EACrB/E,cAA8B,EACL;QAAAxD,aAAA,GAAA4B,CAAA;QACzB,IAAMuE,QAAQ,IAAAnG,aAAA,GAAAC,CAAA,QAAG,CAAAD,aAAA,GAAAsD,CAAA,cAAI,CAACvD,eAAe,CAACsD,GAAG,CAACQ,SAAS,CAAC,MAAA7D,aAAA,GAAAsD,CAAA,UAAI,EAAE;QAACtD,aAAA,GAAAC,CAAA;QAE3D,IAAIkG,QAAQ,CAACrB,MAAM,KAAK,CAAC,EAAE;UAAA9E,aAAA,GAAAsD,CAAA;UAAAtD,aAAA,GAAAC,CAAA;UAEzB,aAAa,IAAI,CAAC2I,oBAAoB,CAAC/E,SAAS,EAAE0E,QAAQ,EAAE/E,cAAc,CAAC;QAC7E,CAAC;UAAAxD,aAAA,GAAAsD,CAAA;QAAA;QAGD,IAAMuF,cAAc,IAAA7I,aAAA,GAAAC,CAAA,QAAGkG,QAAQ,CAAC2C,GAAG,CAAC,UAAA3E,OAAO,EAAI;UAAAnE,aAAA,GAAA4B,CAAA;UAC7C,IAAImH,KAAK,IAAA/I,aAAA,GAAAC,CAAA,QAAG,CAAC;UAACD,aAAA,GAAAC,CAAA;UAGd,IAAIkE,OAAO,CAAC5D,MAAM,KAAKiD,cAAc,CAACjD,MAAM,EAAE;YAAAP,aAAA,GAAAsD,CAAA;YAAAtD,aAAA,GAAAC,CAAA;YAAA8I,KAAK,IAAI,EAAE;UAAA,CAAC;YAAA/I,aAAA,GAAAsD,CAAA;UAAA;UAAAtD,aAAA,GAAAC,CAAA;UAG1D,IAAIkE,OAAO,CAACqD,QAAQ,KAAKe,QAAQ,CAACf,QAAQ,EAAE;YAAAxH,aAAA,GAAAsD,CAAA;YAAAtD,aAAA,GAAAC,CAAA;YAAA8I,KAAK,IAAI,EAAE;UAAA,CAAC;YAAA/I,aAAA,GAAAsD,CAAA;UAAA;UAGxD,IAAM0F,GAAG,IAAAhJ,aAAA,GAAAC,CAAA,QAAG+C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGkB,OAAO,CAACuD,QAAQ,CAACE,SAAS;UACnD,IAAMqB,QAAQ,IAAAjJ,aAAA,GAAAC,CAAA,QAAG6G,IAAI,CAACoC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAIF,GAAG,GAAG,QAAS,CAAC;UAAChJ,aAAA,GAAAC,CAAA;UACpD8I,KAAK,IAAIE,QAAQ;UAACjJ,aAAA,GAAAC,CAAA;UAElB,OAAO;YAAEkE,OAAO,EAAPA,OAAO;YAAE4E,KAAK,EAALA;UAAM,CAAC;QAC3B,CAAC,CAAC;QAAC/I,aAAA,GAAAC,CAAA;QAGH4I,cAAc,CAACM,IAAI,CAAC,UAACC,CAAC,EAAE9F,CAAC,EAAK;UAAAtD,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UAAA,OAAAqD,CAAC,CAACyF,KAAK,GAAGK,CAAC,CAACL,KAAK;QAAD,CAAC,CAAC;QAAC/I,aAAA,GAAAC,CAAA;QACjD,OAAO4I,cAAc,CAAC,CAAC,CAAC,CAAC1E,OAAO;MAClC,CAAC;MAAA,SAjCaP,oBAAoBA,CAAAyF,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAZ,qBAAA,CAAAhG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBgB,oBAAoB;IAAA;EAAA;IAAA7B,GAAA;IAAAC,KAAA;MAAA,IAAAwH,sBAAA,GAAAtH,iBAAA,CAmClC,WACEiC,OAAuB,EACvBrB,OAAmC,EACnCU,cAA8B,EACc;QAAAxD,aAAA,GAAA4B,CAAA;QAC5C,IAAM6H,oBAA8B,IAAAzJ,aAAA,GAAAC,CAAA,QAAG,EAAE;QACzC,IAAIiH,gBAAgB,IAAAlH,aAAA,GAAAC,CAAA,QAAG,CAAC;QACxB,IAAIyJ,eAAe,IAAA1J,aAAA,GAAAC,CAAA,QAAG,CAAC;QACvB,IAAI0J,gBAAgB,IAAA3J,aAAA,GAAAC,CAAA,QAAG,CAAC;QAACD,aAAA,GAAAC,CAAA;QAGzB,IAAIkE,OAAO,CAACsC,OAAO,CAACmD,MAAM,EAAE;UAAA5J,aAAA,GAAAsD,CAAA;UAC1B,IAAMuG,eAAe,IAAA7J,aAAA,GAAAC,CAAA,QAAGuD,cAAc,CAAChD,WAAW,CAACC,YAAY,CAAC,CAAC,CAAC;UAACT,aAAA,GAAAC,CAAA;UACnEwJ,oBAAoB,CAAClE,IAAI,CAAC,gBAAgBsE,eAAe,EAAE,CAAC;UAAC7J,aAAA,GAAAC,CAAA;UAC7DiH,gBAAgB,IAAI,EAAE;UAAClH,aAAA,GAAAC,CAAA;UACvByJ,eAAe,IAAI,EAAE;QACvB,CAAC;UAAA1J,aAAA,GAAAsD,CAAA;QAAA;QAGD,IAAM3C,gBAAgB,IAAAX,aAAA,GAAAC,CAAA,QAAGuD,cAAc,CAAChD,WAAW,CAACG,gBAAgB;QAACX,aAAA,GAAAC,CAAA;QACrEwJ,oBAAoB,CAAClE,IAAI,CAAC,eAAe5E,gBAAgB,EAAE,CAAC;QAACX,aAAA,GAAAC,CAAA;QAE7D,QAAQU,gBAAgB;UACtB,KAAK,MAAM;YAAAX,aAAA,GAAAsD,CAAA;YAAAtD,aAAA,GAAAC,CAAA;YACTiH,gBAAgB,IAAI,EAAE;YAAClH,aAAA,GAAAC,CAAA;YACvB0J,gBAAgB,IAAI,MAAM;YAAC3J,aAAA,GAAAC,CAAA;YAC3B;UACF,KAAK,QAAQ;YAAAD,aAAA,GAAAsD,CAAA;YAAAtD,aAAA,GAAAC,CAAA;YACXiH,gBAAgB,IAAI,EAAE;YAAClH,aAAA,GAAAC,CAAA;YACvB0J,gBAAgB,IAAI,MAAM;YAAC3J,aAAA,GAAAC,CAAA;YAC3B;UACF,KAAK,KAAK;YAAAD,aAAA,GAAAsD,CAAA;YAAAtD,aAAA,GAAAC,CAAA;YACRiH,gBAAgB,IAAI,EAAE;YAAClH,aAAA,GAAAC,CAAA;YACvB0J,gBAAgB,IAAI,KAAK;YAAC3J,aAAA,GAAAC,CAAA;YAC1B;QACJ;QAACD,aAAA,GAAAC,CAAA;QAGD,IAAI6C,OAAO,CAACgH,UAAU,CAACC,IAAI,KAAK,QAAQ,EAAE;UAAA/J,aAAA,GAAAsD,CAAA;UAAAtD,aAAA,GAAAC,CAAA;UACxCwJ,oBAAoB,CAAClE,IAAI,CAAC,qBAAqB,CAAC;UAACvF,aAAA,GAAAC,CAAA;UACjDiH,gBAAgB,IAAI,EAAE;UAAClH,aAAA,GAAAC,CAAA;UACvByJ,eAAe,IAAI,EAAE;QACvB,CAAC;UAAA1J,aAAA,GAAAsD,CAAA;QAAA;QAAAtD,aAAA,GAAAC,CAAA;QAGD,IAAI,CAAAD,aAAA,GAAAsD,CAAA,WAAAR,OAAO,CAACgH,UAAU,CAACE,UAAU,KAAK,MAAM,MAAAhK,aAAA,GAAAsD,CAAA,WAAIR,OAAO,CAACgH,UAAU,CAACE,UAAU,KAAK,IAAI,GAAE;UAAAhK,aAAA,GAAAsD,CAAA;UAAAtD,aAAA,GAAAC,CAAA;UACtFwJ,oBAAoB,CAAClE,IAAI,CAAC,4BAA4B,CAAC;UAACvF,aAAA,GAAAC,CAAA;UACxDiH,gBAAgB,IAAI,EAAE;UAAClH,aAAA,GAAAC,CAAA;UACvByJ,eAAe,IAAI,EAAE;QACvB,CAAC;UAAA1J,aAAA,GAAAsD,CAAA;QAAA;QAAAtD,aAAA,GAAAC,CAAA;QAGD,IAAIkE,OAAO,CAACkD,WAAW,CAAC/F,QAAQ,CAACwD,MAAM,GAAG,CAAC,EAAE;UAAA9E,aAAA,GAAAsD,CAAA;UAAAtD,aAAA,GAAAC,CAAA;UAC3CwJ,oBAAoB,CAAClE,IAAI,CAAC,qBAAqB,CAAC;QAClD,CAAC;UAAAvF,aAAA,GAAAsD,CAAA;QAAA;QAAAtD,aAAA,GAAAC,CAAA;QAED,OAAO;UACLgK,OAAO,EAAER,oBAAoB;UAC7BS,gBAAgB,EAAE;YAChBC,SAAS,EAAErD,IAAI,CAACsD,GAAG,CAAClD,gBAAgB,EAAE,EAAE,CAAC;YACzCmD,QAAQ,EAAEvD,IAAI,CAACsD,GAAG,CAACV,eAAe,EAAE,EAAE,CAAC;YACvCY,SAAS,EAAEX;UACb;QACF,CAAC;MACH,CAAC;MAAA,SAhEa5F,qBAAqBA,CAAAwG,GAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAjB,sBAAA,CAAA7G,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBmB,qBAAqB;IAAA;EAAA;IAAAhC,GAAA;IAAAC,KAAA;MAAA,IAAA0I,qBAAA,GAAAxI,iBAAA,CAkEnC,WACEiC,OAAuB,EACvBoE,QAAqB,EACrBzE,aAAgD,EACL;QAAA9D,aAAA,GAAA4B,CAAA;QAE3C,IAAM+I,SAAS,IAAA3K,aAAA,GAAAC,CAAA,eAASN,gBAAgB,CAACiL,uBAAuB,CAAC;UAC/DC,IAAI,EAAE,YAAY1G,OAAO,CAACoD,EAAE,EAAE;UAC9BwC,IAAI,EAAE,QAAQ;UACde,QAAQ,EAAE,QAAQ;UAClBpH,YAAY,EAAE;YACZ+E,OAAO,EAAEF,QAAQ,CAACE,OAAO;YACzBlI,MAAM,EAAEgI,QAAQ,CAAChI,MAAM;YACvBwK,IAAI,EAAExC,QAAQ,CAACwC,IAAI;YACnBC,WAAW,EAAEzC,QAAQ,CAACyC;UACxB;QACF,CAAC,CAAC;QAAChL,aAAA,GAAAC,CAAA;QAEH,OAAO;UACLgL,OAAO,EAAEN,SAAS,CAACO,GAAG;UACtBC,QAAQ,EAAER,SAAS,CAACS,YAAY;UAChCC,GAAG,EAAEV,SAAS,CAACW;QACjB,CAAC;MACH,CAAC;MAAA,SAvBarH,oBAAoBA,CAAAsH,IAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAf,qBAAA,CAAA/H,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBqB,oBAAoB;IAAA;EAAA;IAAAlC,GAAA;IAAAC,KAAA,EAyBlC,SAAQqC,wBAAwBA,CAC9BF,OAAuB,EACvBX,cAA8B,EACD;MAAAxD,aAAA,GAAA4B,CAAA;MAE7B,IAAI8J,GAAG,IAAA1L,aAAA,GAAAC,CAAA,SAAG,OAAO;MAACD,aAAA,GAAAC,CAAA;MAElB,IAAIkE,OAAO,CAACsC,OAAO,CAACmD,MAAM,EAAE;QAAA5J,aAAA,GAAAsD,CAAA;QAAAtD,aAAA,GAAAC,CAAA;QAAAyL,GAAG,GAAG,QAAQ;MAAA,CAAC;QAAA1L,aAAA,GAAAsD,CAAA;MAAA;MAAAtD,aAAA,GAAAC,CAAA;MAC3C,IAAIkE,OAAO,CAACsC,OAAO,CAACkF,MAAM,EAAE;QAAA3L,aAAA,GAAAsD,CAAA;QAAAtD,aAAA,GAAAC,CAAA;QAAAyL,GAAG,GAAG,QAAQ;MAAA,CAAC;QAAA1L,aAAA,GAAAsD,CAAA;MAAA;MAAAtD,aAAA,GAAAC,CAAA;MAG3C,IAAIuD,cAAc,CAAC3C,UAAU,CAACC,aAAa,GAAG,EAAE,EAAE;QAAAd,aAAA,GAAAsD,CAAA;QAAAtD,aAAA,GAAAC,CAAA;QAChDyL,GAAG,GAAG5E,IAAI,CAACsD,GAAG,CAACsB,GAAG,EAAElI,cAAc,CAAC3C,UAAU,CAACC,aAAa,GAAG,QAAQ,CAAC;MACzE,CAAC;QAAAd,aAAA,GAAAsD,CAAA;MAAA;MAAAtD,aAAA,GAAAC,CAAA;MAED,OAAO;QACLyL,GAAG,EAAHA,GAAG;QACHE,QAAQ,EAAEpI,cAAc,CAAChD,WAAW,CAACI,iBAAiB,KAAK,OAAO,IAAAZ,aAAA,GAAAsD,CAAA,WAAG,YAAY,KAAAtD,aAAA,GAAAsD,CAAA,WAAG,QAAQ;QAC5FuB,OAAO,EAAE,CAACrB,cAAc,CAACjD,MAAM;MACjC,CAAC;IACH;EAAC;IAAAwB,GAAA;IAAAC,KAAA;MAAA,IAAA6J,qBAAA,GAAA3J,iBAAA,CAED,WACE2B,SAAiB,EACjB0E,QAAqB,EACrB/E,cAA8B,EACL;QAAAxD,aAAA,GAAA4B,CAAA;QACzB,IAAM0F,SAAS,IAAAtH,aAAA,GAAAC,CAAA,SAAG,GAAG4D,SAAS,YAAYL,cAAc,CAACjD,MAAM,EAAE;QAEjE,IAAM4D,OAAuB,IAAAnE,aAAA,GAAAC,CAAA,SAAG;UAC9BsH,EAAE,EAAED,SAAS;UACbF,aAAa,EAAEvD,SAAS;UACxBtD,MAAM,EAAEiD,cAAc,CAACjD,MAAM;UAC7BiH,QAAQ,EAAEe,QAAQ,CAACf,QAAQ;UAC3BH,WAAW,EAAE;YACX/F,QAAQ,EAAE,EAAE;YACZwK,KAAK,EAAEtI,cAAc,CAAC3C,UAAU,CAACG,aAAa,IAAAhB,aAAA,GAAAsD,CAAA,WAAG,CAAC,gBAAgB,CAAC,KAAAtD,aAAA,GAAAsD,CAAA,WAAG,EAAE;YACxEyI,WAAW,EAAE,CAAC,oBAAoB,CAAC;YACnCC,YAAY,EAAE,CAAC,UAAUzD,QAAQ,CAAC0D,MAAM,EAAE;UAC5C,CAAC;UACDxF,OAAO,EAAE;YACPyF,IAAI,EAAE;cAAEC,KAAK,EAAE,iBAAiB;cAAEC,WAAW,EAAE;YAA6B,CAAC;YAC7ExC,MAAM,EAAE;cAAEyC,SAAS,EAAE;YAAgC;UACvD,CAAC;UACD3E,QAAQ,EAAE;YACRC,SAAS,EAAE3E,IAAI,CAACC,GAAG,CAAC,CAAC;YACrB2E,SAAS,EAAE5E,IAAI,CAACC,GAAG,CAAC,CAAC;YACrB4E,OAAO,EAAE,OAAO;YAChBxB,IAAI,EAAE,KAAK;YACX0B,MAAM,EAAE;UACV;QACF,CAAC;QAAC/H,aAAA,GAAAC,CAAA;QAEF,IAAI,CAAC,IAAI,CAACF,eAAe,CAACiI,GAAG,CAACnE,SAAS,CAAC,EAAE;UAAA7D,aAAA,GAAAsD,CAAA;UAAAtD,aAAA,GAAAC,CAAA;UACxC,IAAI,CAACF,eAAe,CAACuC,GAAG,CAACuB,SAAS,EAAE,EAAE,CAAC;QACzC,CAAC;UAAA7D,aAAA,GAAAsD,CAAA;QAAA;QAAAtD,aAAA,GAAAC,CAAA;QAED,IAAI,CAACF,eAAe,CAACsD,GAAG,CAACQ,SAAS,CAAC,CAAE0B,IAAI,CAACpB,OAAO,CAAC;QAACnE,aAAA,GAAAC,CAAA;QAEnD,OAAOkE,OAAO;MAChB,CAAC;MAAA,SAtCayE,oBAAoBA,CAAA0D,IAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAX,qBAAA,CAAAlJ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBgG,oBAAoB;IAAA;EAAA;IAAA7G,GAAA;IAAAC,KAAA;MAAA,IAAAyK,4BAAA,GAAAvK,iBAAA,CAwClC,aAA2D;QAAAlC,aAAA,GAAA4B,CAAA;QAEzD,IAAM8K,aAAa,IAAA1M,aAAA,GAAAC,CAAA,SAAG;UACpB,kBAAkB,EAAE,CAClB;YACEM,MAAM,EAAE,eAAe;YACvBiH,QAAQ,EAAE,IAAI;YACdf,OAAO,EAAE;cACPkF,MAAM,EAAE;gBAAEgB,IAAI,EAAE;cAA+B,CAAC;cAChDT,IAAI,EAAE;gBAAEC,KAAK,EAAE,wBAAwB;gBAAEC,WAAW,EAAE;cAAyB;YACjF;UACF,CAAC,EACD;YACE7L,MAAM,EAAE,QAAQ;YAChBiH,QAAQ,EAAE,IAAI;YACdf,OAAO,EAAE;cACPkF,MAAM,EAAE;gBAAEgB,IAAI,EAAE;cAAuC,CAAC;cACxDT,IAAI,EAAE;gBAAEC,KAAK,EAAE,wBAAwB;gBAAEC,WAAW,EAAE;cAAyB;YACjF;UACF,CAAC;QAEL,CAAC;QAACpM,aAAA,GAAAC,CAAA;QAEF,SAAA2M,IAAA,IAAoCC,MAAM,CAACC,OAAO,CAACJ,aAAa,CAAC,EAAE;UAAA,IAAAK,KAAA,GAAAC,cAAA,CAAAJ,IAAA;UAAA,IAAvD/I,SAAS,GAAAkJ,KAAA;UAAA,IAAE5G,QAAQ,GAAA4G,KAAA;UAAA/M,aAAA,GAAAC,CAAA;UAC7B,KAAK,IAAMgN,WAAW,IAAI9G,QAAQ,EAAE;YAAAnG,aAAA,GAAAC,CAAA;YAClC,MAAM,IAAI,CAACgI,oBAAoB,CAC7BpE,SAAS,EACToJ,WAAW,CAAC1M,MAAM,EAClB;cACEe,QAAQ,EAAE,EAAE;cACZwK,KAAK,EAAE,EAAE;cACTC,WAAW,EAAE,CAAC,aAAa,EAAE,qBAAqB,CAAC;cACnDC,YAAY,EAAE,CAAC,UAAUiB,WAAW,CAACzF,QAAQ,EAAE;YACjD,CAAC,EACDyF,WAAW,CAACxG,OACd,CAAC;UACH;QACF;MACF,CAAC;MAAA,SAtCalE,2BAA2BA,CAAA;QAAA,OAAAkK,4BAAA,CAAA9J,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA3BL,2BAA2B;IAAA;EAAA;IAAAR,GAAA;IAAAC,KAAA,EAwCzC,SAAQmB,4BAA4BA,CAACL,OAAmC,EAAU;MAAA9C,aAAA,GAAA4B,CAAA;MAChF,IAAMG,GAAG,IAAA/B,aAAA,GAAAC,CAAA,SAAG,GAAG6C,OAAO,CAACe,SAAS,IAAIf,OAAO,CAACY,YAAY,CAAC+E,OAAO,IAAI3F,OAAO,CAACgH,UAAU,CAACC,IAAI,IAAIjH,OAAO,CAACgH,UAAU,CAACE,UAAU,EAAE;MAAChK,aAAA,GAAAC,CAAA;MAC/H,OAAOiN,IAAI,CAACnL,GAAG,CAAC,CAACoL,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAC/B;EAAC;IAAApL,GAAA;IAAAC,KAAA,EAED,SAAQuB,YAAYA,CAACkD,OAAyB,EAAW;MAAAzG,aAAA,GAAA4B,CAAA;MAAA5B,aAAA,GAAAC,CAAA;MAEvD,OAAO,IAAI;IACb;EAAC;IAAA8B,GAAA;IAAAC,KAAA,EAED,SAAQwC,kBAAkBA,CAAC1B,OAAmC,EAAoB;MAAA9C,aAAA,GAAA4B,CAAA;MAAA5B,aAAA,GAAAC,CAAA;MAChF,OAAO;QACL4D,SAAS,EAAEf,OAAO,CAACe,SAAS;QAC5BM,OAAO,EAAE;UACPoD,EAAE,EAAE,UAAU;UACdH,aAAa,EAAEtE,OAAO,CAACe,SAAS;UAChCtD,MAAM,EAAE,QAAQ;UAChBiH,QAAQ,EAAE,IAAI;UACdH,WAAW,EAAE;YAAE/F,QAAQ,EAAE,EAAE;YAAEwK,KAAK,EAAE,EAAE;YAAEC,WAAW,EAAE,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAC;UAC3EvF,OAAO,EAAE;YAAEyF,IAAI,EAAE;cAAEC,KAAK,EAAE;YAAmB;UAAE,CAAC;UAChDzE,QAAQ,EAAE;YACRC,SAAS,EAAE3E,IAAI,CAACC,GAAG,CAAC,CAAC;YACrB2E,SAAS,EAAE5E,IAAI,CAACC,GAAG,CAAC,CAAC;YACrB4E,OAAO,EAAE,OAAO;YAChBxB,IAAI,EAAE,KAAK;YACX0B,MAAM,EAAE;UACV;QACF,CAAC;QACD/D,YAAY,EAAE;UACZiH,OAAO,EAAE,+CAA+C;UACxDE,QAAQ,EAAE,EAAE;UACZE,GAAG,EAAE;QACP,CAAC;QACDvH,aAAa,EAAE;UACbmG,OAAO,EAAE,CAAC,UAAU,CAAC;UACrBC,gBAAgB,EAAE;YAAEC,SAAS,EAAE,CAAC;YAAEE,QAAQ,EAAE,CAAC;YAAEC,SAAS,EAAE;UAAE;QAC9D,CAAC;QACDlG,OAAO,EAAE;UAAEsH,GAAG,EAAE,MAAM;UAAEE,QAAQ,EAAE,SAAS;UAAE/G,OAAO,EAAE,CAAC,QAAQ;QAAE;MACnE,CAAC;IACH;EAAC;IAAA9C,GAAA;IAAAC,KAAA;MAAA,IAAAoL,uBAAA,GAAAlL,iBAAA,CAED,WAAqC2B,SAAiB,EAAEtD,MAAc,EAAiB;QAAAP,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QACrF,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACFuC,OAAO,CAACC,GAAG,CAAC,sBAAsBoB,SAAS,cAActD,MAAM,EAAE,CAAC;QAEpE,CAAC,CAAC,OAAOmC,KAAK,EAAE;UAAA1C,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACE,KAAK,CAAC,6BAA6BmB,SAAS,OAAOtD,MAAM,GAAG,EAAEmC,KAAK,CAAC;QAC9E;MACF,CAAC;MAAA,SAPa8C,sBAAsBA,CAAA6H,IAAA,EAAAC,IAAA;QAAA,OAAAF,uBAAA,CAAAzK,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAtB4C,sBAAsB;IAAA;EAAA;IAAAzD,GAAA;IAAAC,KAAA,EASpC,SAAQyF,iBAAiBA,CAAClH,MAAc,EAAU;MAAAP,aAAA,GAAA4B,CAAA;MAChD,IAAM2L,WAAmC,IAAAvN,aAAA,GAAAC,CAAA,SAAG;QAC1C,eAAe,EAAE,IAAI;QACrB,QAAQ,EAAE,IAAI;QACd,cAAc,EAAE;MAClB,CAAC;MAACD,aAAA,GAAAC,CAAA;MACF,OAAO,CAAAD,aAAA,GAAAsD,CAAA,WAAAiK,WAAW,CAAChN,MAAM,CAAC,MAAAP,aAAA,GAAAsD,CAAA,WAAI,IAAI;IACpC;EAAC;IAAAvB,GAAA;IAAAC,KAAA,EAED,SAAQ8F,oBAAoBA,CAACrB,OAAkC,EAAU;MAAAzG,aAAA,GAAA4B,CAAA;MAEvE,IAAIyE,IAAI,IAAArG,aAAA,GAAAC,CAAA,SAAG,CAAC;MAACD,aAAA,GAAAC,CAAA;MAEb,IAAIwG,OAAO,CAACyF,IAAI,EAAE;QAAAlM,aAAA,GAAAsD,CAAA;QAAAtD,aAAA,GAAAC,CAAA;QAAAoG,IAAI,IAAIwG,MAAM,CAACxH,IAAI,CAACoB,OAAO,CAACyF,IAAI,CAAC,CAACpH,MAAM,GAAG,GAAG;MAAA,CAAC;QAAA9E,aAAA,GAAAsD,CAAA;MAAA;MAAAtD,aAAA,GAAAC,CAAA;MACjE,IAAIwG,OAAO,CAACmD,MAAM,EAAE;QAAA5J,aAAA,GAAAsD,CAAA;QAAAtD,aAAA,GAAAC,CAAA;QAAAoG,IAAI,IAAIwG,MAAM,CAACxH,IAAI,CAACoB,OAAO,CAACmD,MAAM,CAAC,CAAC9E,MAAM,GAAG,KAAK;MAAA,CAAC;QAAA9E,aAAA,GAAAsD,CAAA;MAAA;MAAAtD,aAAA,GAAAC,CAAA;MACvE,IAAIwG,OAAO,CAACkF,MAAM,EAAE;QAAA3L,aAAA,GAAAsD,CAAA;QAAAtD,aAAA,GAAAC,CAAA;QAAAoG,IAAI,IAAIwG,MAAM,CAACxH,IAAI,CAACoB,OAAO,CAACkF,MAAM,CAAC,CAAC7G,MAAM,GAAG,OAAO;MAAA,CAAC;QAAA9E,aAAA,GAAAsD,CAAA;MAAA;MAAAtD,aAAA,GAAAC,CAAA;MACzE,IAAIwG,OAAO,CAAC+G,MAAM,EAAE;QAAAxN,aAAA,GAAAsD,CAAA;QAAAtD,aAAA,GAAAC,CAAA;QAAAoG,IAAI,IAAIwG,MAAM,CAACxH,IAAI,CAACoB,OAAO,CAAC+G,MAAM,CAAC,CAAC1I,MAAM,GAAG,KAAK;MAAA,CAAC;QAAA9E,aAAA,GAAAsD,CAAA;MAAA;MAAAtD,aAAA,GAAAC,CAAA;MAEvE,OAAOoG,IAAI;IACb;EAAC;AAAA;AAIH,OAAO,IAAMoH,0BAA0B,IAAAzN,aAAA,GAAAC,CAAA,SAAG,IAAIJ,0BAA0B,CAAC,CAAC;AAC1E,eAAe4N,0BAA0B", "ignoreList": []}