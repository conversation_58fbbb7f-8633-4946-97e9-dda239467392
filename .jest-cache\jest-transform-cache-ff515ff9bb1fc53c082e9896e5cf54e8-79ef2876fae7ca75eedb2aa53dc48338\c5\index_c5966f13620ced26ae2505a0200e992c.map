{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_isDisabled", "_propsToAccessibilityComponent", "_propsToAriaRole", "AccessibilityUtil", "isDisabled", "propsToAccessibilityComponent", "propsToAriaRole", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _isDisabled = _interopRequireDefault(require(\"./isDisabled\"));\nvar _propsToAccessibilityComponent = _interopRequireDefault(require(\"./propsToAccessibilityComponent\"));\nvar _propsToAriaRole = _interopRequireDefault(require(\"./propsToAriaRole\"));\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar AccessibilityUtil = {\n  isDisabled: _isDisabled.default,\n  propsToAccessibilityComponent: _propsToAccessibilityComponent.default,\n  propsToAriaRole: _propsToAriaRole.default\n};\nvar _default = exports.default = AccessibilityUtil;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,WAAW,GAAGL,sBAAsB,CAACC,OAAO,eAAe,CAAC,CAAC;AACjE,IAAIK,8BAA8B,GAAGN,sBAAsB,CAACC,OAAO,kCAAkC,CAAC,CAAC;AACvG,IAAIM,gBAAgB,GAAGP,sBAAsB,CAACC,OAAO,oBAAoB,CAAC,CAAC;AAU3E,IAAIO,iBAAiB,GAAG;EACtBC,UAAU,EAAEJ,WAAW,CAACH,OAAO;EAC/BQ,6BAA6B,EAAEJ,8BAA8B,CAACJ,OAAO;EACrES,eAAe,EAAEJ,gBAAgB,CAACL;AACpC,CAAC;AACD,IAAIU,QAAQ,GAAGT,OAAO,CAACD,OAAO,GAAGM,iBAAiB;AAClDK,MAAM,CAACV,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}