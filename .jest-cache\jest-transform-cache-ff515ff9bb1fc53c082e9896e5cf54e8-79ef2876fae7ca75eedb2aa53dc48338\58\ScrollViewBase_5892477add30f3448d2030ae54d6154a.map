{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "_extends2", "_objectWithoutPropertiesLoose2", "React", "_StyleSheet", "_View", "_useMergeRefs", "_excluded", "normalizeScrollEvent", "e", "nativeEvent", "contentOffset", "x", "target", "scrollLeft", "y", "scrollTop", "contentSize", "height", "scrollHeight", "width", "scrollWidth", "layoutMeasurement", "offsetHeight", "offsetWidth", "timeStamp", "Date", "now", "shouldEmitScrollEvent", "lastTick", "eventThrottle", "timeSinceLastTick", "ScrollViewBase", "forwardRef", "props", "forwardedRef", "onScroll", "onTouchMove", "onWheel", "_props$scrollEnabled", "scrollEnabled", "_props$scrollEventThr", "scrollEventThrottle", "showsHorizontalScrollIndicator", "showsVerticalScrollIndicator", "style", "rest", "scrollState", "useRef", "isScrolling", "scrollLastTick", "scrollTimeout", "scrollRef", "createPreventableScrollHandler", "handler", "handleScroll", "stopPropagation", "current", "persist", "clearTimeout", "setTimeout", "handleScrollEnd", "handleScrollTick", "handleScrollStart", "hideScrollbar", "createElement", "ref", "styles", "scrollDisabled", "create", "overflowX", "overflowY", "touchAction", "scrollbarWidth", "_default", "module"], "sources": ["ScrollViewBase.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _View = _interopRequireDefault(require(\"../View\"));\nvar _useMergeRefs = _interopRequireDefault(require(\"../../modules/useMergeRefs\"));\nvar _excluded = [\"onScroll\", \"onTouchMove\", \"onWheel\", \"scrollEnabled\", \"scrollEventThrottle\", \"showsHorizontalScrollIndicator\", \"showsVerticalScrollIndicator\", \"style\"];\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\nfunction normalizeScrollEvent(e) {\n  return {\n    nativeEvent: {\n      contentOffset: {\n        get x() {\n          return e.target.scrollLeft;\n        },\n        get y() {\n          return e.target.scrollTop;\n        }\n      },\n      contentSize: {\n        get height() {\n          return e.target.scrollHeight;\n        },\n        get width() {\n          return e.target.scrollWidth;\n        }\n      },\n      layoutMeasurement: {\n        get height() {\n          return e.target.offsetHeight;\n        },\n        get width() {\n          return e.target.offsetWidth;\n        }\n      }\n    },\n    timeStamp: Date.now()\n  };\n}\nfunction shouldEmitScrollEvent(lastTick, eventThrottle) {\n  var timeSinceLastTick = Date.now() - lastTick;\n  return eventThrottle > 0 && timeSinceLastTick >= eventThrottle;\n}\n\n/**\n * Encapsulates the Web-specific scroll throttling and disabling logic\n */\nvar ScrollViewBase = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var onScroll = props.onScroll,\n    onTouchMove = props.onTouchMove,\n    onWheel = props.onWheel,\n    _props$scrollEnabled = props.scrollEnabled,\n    scrollEnabled = _props$scrollEnabled === void 0 ? true : _props$scrollEnabled,\n    _props$scrollEventThr = props.scrollEventThrottle,\n    scrollEventThrottle = _props$scrollEventThr === void 0 ? 0 : _props$scrollEventThr,\n    showsHorizontalScrollIndicator = props.showsHorizontalScrollIndicator,\n    showsVerticalScrollIndicator = props.showsVerticalScrollIndicator,\n    style = props.style,\n    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  var scrollState = React.useRef({\n    isScrolling: false,\n    scrollLastTick: 0\n  });\n  var scrollTimeout = React.useRef(null);\n  var scrollRef = React.useRef(null);\n  function createPreventableScrollHandler(handler) {\n    return e => {\n      if (scrollEnabled) {\n        if (handler) {\n          handler(e);\n        }\n      }\n    };\n  }\n  function handleScroll(e) {\n    e.stopPropagation();\n    if (e.target === scrollRef.current) {\n      e.persist();\n      // A scroll happened, so the scroll resets the scrollend timeout.\n      if (scrollTimeout.current != null) {\n        clearTimeout(scrollTimeout.current);\n      }\n      scrollTimeout.current = setTimeout(() => {\n        handleScrollEnd(e);\n      }, 100);\n      if (scrollState.current.isScrolling) {\n        // Scroll last tick may have changed, check if we need to notify\n        if (shouldEmitScrollEvent(scrollState.current.scrollLastTick, scrollEventThrottle)) {\n          handleScrollTick(e);\n        }\n      } else {\n        // Weren't scrolling, so we must have just started\n        handleScrollStart(e);\n      }\n    }\n  }\n  function handleScrollStart(e) {\n    scrollState.current.isScrolling = true;\n    handleScrollTick(e);\n  }\n  function handleScrollTick(e) {\n    scrollState.current.scrollLastTick = Date.now();\n    if (onScroll) {\n      onScroll(normalizeScrollEvent(e));\n    }\n  }\n  function handleScrollEnd(e) {\n    scrollState.current.isScrolling = false;\n    if (onScroll) {\n      onScroll(normalizeScrollEvent(e));\n    }\n  }\n  var hideScrollbar = showsHorizontalScrollIndicator === false || showsVerticalScrollIndicator === false;\n  return /*#__PURE__*/React.createElement(_View.default, (0, _extends2.default)({}, rest, {\n    onScroll: handleScroll,\n    onTouchMove: createPreventableScrollHandler(onTouchMove),\n    onWheel: createPreventableScrollHandler(onWheel),\n    ref: (0, _useMergeRefs.default)(scrollRef, forwardedRef),\n    style: [style, !scrollEnabled && styles.scrollDisabled, hideScrollbar && styles.hideScrollbar]\n  }));\n});\n\n// Chrome doesn't support e.preventDefault in this case; touch-action must be\n// used to disable scrolling.\n// https://developers.google.com/web/updates/2017/01/scrolling-intervention\nvar styles = _StyleSheet.default.create({\n  scrollDisabled: {\n    overflowX: 'hidden',\n    overflowY: 'hidden',\n    touchAction: 'none'\n  },\n  hideScrollbar: {\n    scrollbarWidth: 'none'\n  }\n});\nvar _default = exports.default = ScrollViewBase;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIM,8BAA8B,GAAGP,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIO,KAAK,GAAGL,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIQ,WAAW,GAAGT,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIS,KAAK,GAAGV,sBAAsB,CAACC,OAAO,UAAU,CAAC,CAAC;AACtD,IAAIU,aAAa,GAAGX,sBAAsB,CAACC,OAAO,6BAA6B,CAAC,CAAC;AACjF,IAAIW,SAAS,GAAG,CAAC,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,eAAe,EAAE,qBAAqB,EAAE,gCAAgC,EAAE,8BAA8B,EAAE,OAAO,CAAC;AASzK,SAASC,oBAAoBA,CAACC,CAAC,EAAE;EAC/B,OAAO;IACLC,WAAW,EAAE;MACXC,aAAa,EAAE;QACb,IAAIC,CAACA,CAAA,EAAG;UACN,OAAOH,CAAC,CAACI,MAAM,CAACC,UAAU;QAC5B,CAAC;QACD,IAAIC,CAACA,CAAA,EAAG;UACN,OAAON,CAAC,CAACI,MAAM,CAACG,SAAS;QAC3B;MACF,CAAC;MACDC,WAAW,EAAE;QACX,IAAIC,MAAMA,CAAA,EAAG;UACX,OAAOT,CAAC,CAACI,MAAM,CAACM,YAAY;QAC9B,CAAC;QACD,IAAIC,KAAKA,CAAA,EAAG;UACV,OAAOX,CAAC,CAACI,MAAM,CAACQ,WAAW;QAC7B;MACF,CAAC;MACDC,iBAAiB,EAAE;QACjB,IAAIJ,MAAMA,CAAA,EAAG;UACX,OAAOT,CAAC,CAACI,MAAM,CAACU,YAAY;QAC9B,CAAC;QACD,IAAIH,KAAKA,CAAA,EAAG;UACV,OAAOX,CAAC,CAACI,MAAM,CAACW,WAAW;QAC7B;MACF;IACF,CAAC;IACDC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;EACtB,CAAC;AACH;AACA,SAASC,qBAAqBA,CAACC,QAAQ,EAAEC,aAAa,EAAE;EACtD,IAAIC,iBAAiB,GAAGL,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGE,QAAQ;EAC7C,OAAOC,aAAa,GAAG,CAAC,IAAIC,iBAAiB,IAAID,aAAa;AAChE;AAKA,IAAIE,cAAc,GAAgB7B,KAAK,CAAC8B,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EAC1E,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BC,OAAO,GAAGJ,KAAK,CAACI,OAAO;IACvBC,oBAAoB,GAAGL,KAAK,CAACM,aAAa;IAC1CA,aAAa,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,oBAAoB;IAC7EE,qBAAqB,GAAGP,KAAK,CAACQ,mBAAmB;IACjDA,mBAAmB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;IAClFE,8BAA8B,GAAGT,KAAK,CAACS,8BAA8B;IACrEC,4BAA4B,GAAGV,KAAK,CAACU,4BAA4B;IACjEC,KAAK,GAAGX,KAAK,CAACW,KAAK;IACnBC,IAAI,GAAG,CAAC,CAAC,EAAE5C,8BAA8B,CAACL,OAAO,EAAEqC,KAAK,EAAE3B,SAAS,CAAC;EACtE,IAAIwC,WAAW,GAAG5C,KAAK,CAAC6C,MAAM,CAAC;IAC7BC,WAAW,EAAE,KAAK;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,IAAIC,aAAa,GAAGhD,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EACtC,IAAII,SAAS,GAAGjD,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EAClC,SAASK,8BAA8BA,CAACC,OAAO,EAAE;IAC/C,OAAO,UAAA7C,CAAC,EAAI;MACV,IAAI+B,aAAa,EAAE;QACjB,IAAIc,OAAO,EAAE;UACXA,OAAO,CAAC7C,CAAC,CAAC;QACZ;MACF;IACF,CAAC;EACH;EACA,SAAS8C,YAAYA,CAAC9C,CAAC,EAAE;IACvBA,CAAC,CAAC+C,eAAe,CAAC,CAAC;IACnB,IAAI/C,CAAC,CAACI,MAAM,KAAKuC,SAAS,CAACK,OAAO,EAAE;MAClChD,CAAC,CAACiD,OAAO,CAAC,CAAC;MAEX,IAAIP,aAAa,CAACM,OAAO,IAAI,IAAI,EAAE;QACjCE,YAAY,CAACR,aAAa,CAACM,OAAO,CAAC;MACrC;MACAN,aAAa,CAACM,OAAO,GAAGG,UAAU,CAAC,YAAM;QACvCC,eAAe,CAACpD,CAAC,CAAC;MACpB,CAAC,EAAE,GAAG,CAAC;MACP,IAAIsC,WAAW,CAACU,OAAO,CAACR,WAAW,EAAE;QAEnC,IAAIrB,qBAAqB,CAACmB,WAAW,CAACU,OAAO,CAACP,cAAc,EAAER,mBAAmB,CAAC,EAAE;UAClFoB,gBAAgB,CAACrD,CAAC,CAAC;QACrB;MACF,CAAC,MAAM;QAELsD,iBAAiB,CAACtD,CAAC,CAAC;MACtB;IACF;EACF;EACA,SAASsD,iBAAiBA,CAACtD,CAAC,EAAE;IAC5BsC,WAAW,CAACU,OAAO,CAACR,WAAW,GAAG,IAAI;IACtCa,gBAAgB,CAACrD,CAAC,CAAC;EACrB;EACA,SAASqD,gBAAgBA,CAACrD,CAAC,EAAE;IAC3BsC,WAAW,CAACU,OAAO,CAACP,cAAc,GAAGxB,IAAI,CAACC,GAAG,CAAC,CAAC;IAC/C,IAAIS,QAAQ,EAAE;MACZA,QAAQ,CAAC5B,oBAAoB,CAACC,CAAC,CAAC,CAAC;IACnC;EACF;EACA,SAASoD,eAAeA,CAACpD,CAAC,EAAE;IAC1BsC,WAAW,CAACU,OAAO,CAACR,WAAW,GAAG,KAAK;IACvC,IAAIb,QAAQ,EAAE;MACZA,QAAQ,CAAC5B,oBAAoB,CAACC,CAAC,CAAC,CAAC;IACnC;EACF;EACA,IAAIuD,aAAa,GAAGrB,8BAA8B,KAAK,KAAK,IAAIC,4BAA4B,KAAK,KAAK;EACtG,OAAoBzC,KAAK,CAAC8D,aAAa,CAAC5D,KAAK,CAACR,OAAO,EAAE,CAAC,CAAC,EAAEI,SAAS,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEiD,IAAI,EAAE;IACtFV,QAAQ,EAAEmB,YAAY;IACtBlB,WAAW,EAAEgB,8BAA8B,CAAChB,WAAW,CAAC;IACxDC,OAAO,EAAEe,8BAA8B,CAACf,OAAO,CAAC;IAChD4B,GAAG,EAAE,CAAC,CAAC,EAAE5D,aAAa,CAACT,OAAO,EAAEuD,SAAS,EAAEjB,YAAY,CAAC;IACxDU,KAAK,EAAE,CAACA,KAAK,EAAE,CAACL,aAAa,IAAI2B,MAAM,CAACC,cAAc,EAAEJ,aAAa,IAAIG,MAAM,CAACH,aAAa;EAC/F,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAKF,IAAIG,MAAM,GAAG/D,WAAW,CAACP,OAAO,CAACwE,MAAM,CAAC;EACtCD,cAAc,EAAE;IACdE,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE,QAAQ;IACnBC,WAAW,EAAE;EACf,CAAC;EACDR,aAAa,EAAE;IACbS,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AACF,IAAIC,QAAQ,GAAG3E,OAAO,CAACF,OAAO,GAAGmC,cAAc;AAC/C2C,MAAM,CAAC5E,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}