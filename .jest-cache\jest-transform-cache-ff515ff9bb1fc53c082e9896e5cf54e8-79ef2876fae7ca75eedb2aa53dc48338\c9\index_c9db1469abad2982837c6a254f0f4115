91406e5e2d1f30ddeac37916d7f71a1f
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = useWindowDimensions;
var _Dimensions = _interopRequireDefault(require("../Dimensions"));
var _react = require("react");
function useWindowDimensions() {
  var _useState = (0, _react.useState)(function () {
      return _Dimensions.default.get('window');
    }),
    dims = _useState[0],
    setDims = _useState[1];
  (0, _react.useEffect)(function () {
    function handleChange(_ref) {
      var window = _ref.window;
      if (window != null) {
        setDims(window);
      }
    }
    _Dimensions.default.addEventListener('change', handleChange);
    setDims(_Dimensions.default.get('window'));
    return function () {
      _Dimensions.default.removeEventListener('change', handleChange);
    };
  }, []);
  return dims;
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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