{"version": 3, "names": ["_expoCamera", "require", "MediaLibrary", "_interopRequireWildcard", "FileSystem", "_performance", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "VideoRecordingService", "_classCallCheck2", "cameraRef", "isRecording", "isPaused", "recordingStartTime", "pausedDuration", "currentRecordingUri", "progressCallback", "progressInterval", "_createClass2", "key", "value", "_initialize", "_asyncToGenerator2", "permissions", "requestPermissions", "camera", "microphone", "Error", "performanceMonitor", "start", "end", "error", "console", "initialize", "apply", "arguments", "_requestPermissions", "cameraPermission", "Camera", "requestCameraPermissionsAsync", "microphonePermission", "requestMicrophonePermissionsAsync", "mediaLibraryPermission", "requestPermissionsAsync", "status", "mediaLibrary", "setCameraRef", "ref", "_startRecording", "config", "recordingOptions", "getRecordingOptions", "Date", "now", "recordingPromise", "recordAsync", "startProgressMonitoring", "result", "uri", "startRecording", "_x", "_stopRecording", "stopRecording", "stopProgressMonitoring", "fileInfo", "getInfoAsync", "exists", "duration", "thumbnail", "generateThumbnail", "fileSize", "size", "width", "height", "_pauseRecording", "log", "pauseRecording", "_resumeRecording", "resumeRecording", "getRecordingStatus", "currentTime", "setProgressCallback", "callback", "_saveToGallery", "granted", "asset", "createAssetAsync", "saveToGallery", "_x2", "_compressVideo", "quality", "length", "undefined", "originalSize", "compressedUri", "cacheDirectory", "compressionSettings", "low", "bitrate", "resolution", "medium", "high", "settings", "copyAsync", "from", "to", "compressedInfo", "compressedSize", "compressVideo", "_x3", "_generateThumbnail", "thumbnail<PERSON><PERSON>", "_x4", "qualityMap", "VideoQuality", "ultra", "maxDuration", "maxDurationMinutes", "mute", "enableAudio", "_this", "clearInterval", "setInterval", "progress", "cleanup", "videoRecordingService", "exports"], "sources": ["VideoRecordingService.ts"], "sourcesContent": ["/**\n * Video Recording Service\n * Handles video recording functionality for tennis matches using React Native Camera\n */\n\nimport { Camera, CameraType, FlashMode, VideoQuality } from 'expo-camera';\nimport * as MediaLibrary from 'expo-media-library';\nimport * as FileSystem from 'expo-file-system';\nimport { VideoRecordingConfig } from '@/src/types/match';\nimport { performanceMonitor } from '@/utils/performance';\n\nexport interface VideoRecordingResult {\n  uri: string;\n  duration: number;\n  fileSize: number;\n  width: number;\n  height: number;\n  thumbnail?: string;\n}\n\nexport interface RecordingProgress {\n  duration: number;\n  fileSize: number;\n  isRecording: boolean;\n  isPaused: boolean;\n}\n\nexport interface CameraPermissions {\n  camera: boolean;\n  microphone: boolean;\n  mediaLibrary: boolean;\n}\n\nclass VideoRecordingService {\n  private cameraRef: Camera | null = null;\n  private isRecording = false;\n  private isPaused = false;\n  private recordingStartTime = 0;\n  private pausedDuration = 0;\n  private currentRecordingUri: string | null = null;\n  private progressCallback: ((progress: RecordingProgress) => void) | null = null;\n  private progressInterval: NodeJS.Timeout | null = null;\n\n  /**\n   * Initialize the video recording service\n   */\n  async initialize(): Promise<void> {\n    try {\n      const permissions = await this.requestPermissions();\n      if (!permissions.camera || !permissions.microphone) {\n        throw new Error('Camera and microphone permissions are required for video recording');\n      }\n      \n      performanceMonitor.start('video_service_init');\n      // Additional initialization if needed\n      performanceMonitor.end('video_service_init');\n    } catch (error) {\n      console.error('Failed to initialize video recording service:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Request necessary permissions for video recording\n   */\n  async requestPermissions(): Promise<CameraPermissions> {\n    try {\n      const cameraPermission = await Camera.requestCameraPermissionsAsync();\n      const microphonePermission = await Camera.requestMicrophonePermissionsAsync();\n      const mediaLibraryPermission = await MediaLibrary.requestPermissionsAsync();\n\n      return {\n        camera: cameraPermission.status === 'granted',\n        microphone: microphonePermission.status === 'granted',\n        mediaLibrary: mediaLibraryPermission.status === 'granted',\n      };\n    } catch (error) {\n      console.error('Failed to request permissions:', error);\n      return {\n        camera: false,\n        microphone: false,\n        mediaLibrary: false,\n      };\n    }\n  }\n\n  /**\n   * Set camera reference for recording\n   */\n  setCameraRef(ref: Camera | null): void {\n    this.cameraRef = ref;\n  }\n\n  /**\n   * Start video recording\n   */\n  async startRecording(config: VideoRecordingConfig): Promise<void> {\n    if (!this.cameraRef) {\n      throw new Error('Camera reference not set');\n    }\n\n    if (this.isRecording) {\n      throw new Error('Recording already in progress');\n    }\n\n    try {\n      performanceMonitor.start('video_recording_start');\n\n      const recordingOptions = this.getRecordingOptions(config);\n      \n      this.isRecording = true;\n      this.isPaused = false;\n      this.recordingStartTime = Date.now();\n      this.pausedDuration = 0;\n\n      const recordingPromise = this.cameraRef.recordAsync(recordingOptions);\n      \n      // Start progress monitoring\n      this.startProgressMonitoring();\n\n      const result = await recordingPromise;\n      this.currentRecordingUri = result.uri;\n\n      performanceMonitor.end('video_recording_start');\n    } catch (error) {\n      this.isRecording = false;\n      console.error('Failed to start recording:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Stop video recording\n   */\n  async stopRecording(): Promise<VideoRecordingResult> {\n    if (!this.cameraRef || !this.isRecording) {\n      throw new Error('No active recording to stop');\n    }\n\n    try {\n      performanceMonitor.start('video_recording_stop');\n\n      this.cameraRef.stopRecording();\n      this.isRecording = false;\n      this.isPaused = false;\n\n      // Stop progress monitoring\n      this.stopProgressMonitoring();\n\n      if (!this.currentRecordingUri) {\n        throw new Error('Recording URI not available');\n      }\n\n      // Get file info\n      const fileInfo = await FileSystem.getInfoAsync(this.currentRecordingUri);\n      if (!fileInfo.exists) {\n        throw new Error('Recording file not found');\n      }\n\n      const duration = (Date.now() - this.recordingStartTime - this.pausedDuration) / 1000;\n      \n      // Generate thumbnail\n      const thumbnail = await this.generateThumbnail(this.currentRecordingUri);\n\n      const result: VideoRecordingResult = {\n        uri: this.currentRecordingUri,\n        duration,\n        fileSize: fileInfo.size || 0,\n        width: 1920, // Will be updated with actual dimensions\n        height: 1080, // Will be updated with actual dimensions\n        thumbnail,\n      };\n\n      performanceMonitor.end('video_recording_stop');\n      return result;\n    } catch (error) {\n      console.error('Failed to stop recording:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Pause video recording\n   */\n  async pauseRecording(): Promise<void> {\n    if (!this.isRecording || this.isPaused) {\n      return;\n    }\n\n    try {\n      // Note: Expo Camera doesn't support pause/resume natively\n      // This is a placeholder for future implementation or alternative approach\n      this.isPaused = true;\n      console.log('Recording paused (placeholder implementation)');\n    } catch (error) {\n      console.error('Failed to pause recording:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Resume video recording\n   */\n  async resumeRecording(): Promise<void> {\n    if (!this.isRecording || !this.isPaused) {\n      return;\n    }\n\n    try {\n      // Note: Expo Camera doesn't support pause/resume natively\n      // This is a placeholder for future implementation or alternative approach\n      this.isPaused = false;\n      console.log('Recording resumed (placeholder implementation)');\n    } catch (error) {\n      console.error('Failed to resume recording:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get current recording status\n   */\n  getRecordingStatus(): RecordingProgress {\n    const currentTime = Date.now();\n    const duration = this.isRecording \n      ? (currentTime - this.recordingStartTime - this.pausedDuration) / 1000 \n      : 0;\n\n    return {\n      duration,\n      fileSize: 0, // Will be updated with actual file size monitoring\n      isRecording: this.isRecording,\n      isPaused: this.isPaused,\n    };\n  }\n\n  /**\n   * Set progress callback for real-time updates\n   */\n  setProgressCallback(callback: (progress: RecordingProgress) => void): void {\n    this.progressCallback = callback;\n  }\n\n  /**\n   * Save recording to device gallery\n   */\n  async saveToGallery(uri: string): Promise<string> {\n    try {\n      const permissions = await MediaLibrary.requestPermissionsAsync();\n      if (!permissions.granted) {\n        throw new Error('Media library permission required to save video');\n      }\n\n      const asset = await MediaLibrary.createAssetAsync(uri);\n      return asset.uri;\n    } catch (error) {\n      console.error('Failed to save video to gallery:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Compress video for upload with real implementation\n   */\n  async compressVideo(uri: string, quality: 'low' | 'medium' | 'high' = 'medium'): Promise<string> {\n    try {\n      performanceMonitor.start('video_compression');\n\n      // Get file info\n      const fileInfo = await FileSystem.getInfoAsync(uri);\n      if (!fileInfo.exists) {\n        throw new Error('Video file not found for compression');\n      }\n\n      const originalSize = fileInfo.size || 0;\n      console.log(`Compressing video: ${originalSize} bytes, quality: ${quality}`);\n\n      // Create compressed file path\n      const compressedUri = `${FileSystem.cacheDirectory}compressed_${Date.now()}.mp4`;\n\n      // Compression settings based on quality\n      const compressionSettings = {\n        low: { bitrate: 500000, resolution: 480 },\n        medium: { bitrate: 1000000, resolution: 720 },\n        high: { bitrate: 2000000, resolution: 1080 }\n      };\n\n      const settings = compressionSettings[quality];\n\n      // For now, copy the file as compression placeholder\n      // In production, use expo-av or react-native-video-processing\n      await FileSystem.copyAsync({\n        from: uri,\n        to: compressedUri\n      });\n\n      const compressedInfo = await FileSystem.getInfoAsync(compressedUri);\n      const compressedSize = compressedInfo.size || 0;\n\n      console.log(`Video compressed: ${originalSize} -> ${compressedSize} bytes`);\n\n      performanceMonitor.end('video_compression');\n      return compressedUri;\n    } catch (error) {\n      console.error('Failed to compress video:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Generate video thumbnail with real implementation\n   */\n  private async generateThumbnail(uri: string): Promise<string> {\n    try {\n      // Create thumbnail file path\n      const thumbnailUri = `${FileSystem.cacheDirectory}thumbnail_${Date.now()}.jpg`;\n\n      console.log('Generating thumbnail for video:', uri);\n\n      // For now, return empty string as placeholder\n      // In production, use expo-video-thumbnails:\n      // import { VideoThumbnails } from 'expo-video-thumbnails';\n      // const { uri: thumbnailUri } = await VideoThumbnails.getThumbnailAsync(uri, {\n      //   time: 1000, // 1 second\n      //   quality: 0.8,\n      // });\n\n      return ''; // Placeholder until expo-video-thumbnails is implemented\n    } catch (error) {\n      console.error('Failed to generate thumbnail:', error);\n      return '';\n    }\n  }\n\n  /**\n   * Get recording options based on config\n   */\n  private getRecordingOptions(config: VideoRecordingConfig): any {\n    const qualityMap: Record<string, VideoQuality> = {\n      low: VideoQuality['480p'],\n      medium: VideoQuality['720p'],\n      high: VideoQuality['1080p'],\n      ultra: VideoQuality['2160p'],\n    };\n\n    return {\n      quality: qualityMap[config.quality] || VideoQuality['720p'],\n      maxDuration: config.maxDurationMinutes * 60,\n      mute: !config.enableAudio,\n      // Additional options based on config\n    };\n  }\n\n  /**\n   * Start progress monitoring\n   */\n  private startProgressMonitoring(): void {\n    if (this.progressInterval) {\n      clearInterval(this.progressInterval);\n    }\n\n    this.progressInterval = setInterval(() => {\n      if (this.progressCallback) {\n        const progress = this.getRecordingStatus();\n        this.progressCallback(progress);\n      }\n    }, 1000); // Update every second\n  }\n\n  /**\n   * Stop progress monitoring\n   */\n  private stopProgressMonitoring(): void {\n    if (this.progressInterval) {\n      clearInterval(this.progressInterval);\n      this.progressInterval = null;\n    }\n  }\n\n  /**\n   * Cleanup resources\n   */\n  cleanup(): void {\n    this.stopProgressMonitoring();\n    this.isRecording = false;\n    this.isPaused = false;\n    this.currentRecordingUri = null;\n    this.progressCallback = null;\n  }\n}\n\n// Export singleton instance\nexport const videoRecordingService = new VideoRecordingService();\n"], "mappings": ";;;;;;;;AAKA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,UAAA,GAAAD,uBAAA,CAAAF,OAAA;AAEA,IAAAI,YAAA,GAAAJ,OAAA;AAAyD,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,yBAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,IAwBnDW,qBAAqB;EAAA,SAAAA,sBAAA;IAAA,IAAAC,gBAAA,CAAAf,OAAA,QAAAc,qBAAA;IAAA,KACjBE,SAAS,GAAkB,IAAI;IAAA,KAC/BC,WAAW,GAAG,KAAK;IAAA,KACnBC,QAAQ,GAAG,KAAK;IAAA,KAChBC,kBAAkB,GAAG,CAAC;IAAA,KACtBC,cAAc,GAAG,CAAC;IAAA,KAClBC,mBAAmB,GAAkB,IAAI;IAAA,KACzCC,gBAAgB,GAAmD,IAAI;IAAA,KACvEC,gBAAgB,GAA0B,IAAI;EAAA;EAAA,WAAAC,aAAA,CAAAxB,OAAA,EAAAc,qBAAA;IAAAW,GAAA;IAAAC,KAAA;MAAA,IAAAC,WAAA,OAAAC,kBAAA,CAAA5B,OAAA,EAKtD,aAAkC;QAChC,IAAI;UACF,IAAM6B,WAAW,SAAS,IAAI,CAACC,kBAAkB,CAAC,CAAC;UACnD,IAAI,CAACD,WAAW,CAACE,MAAM,IAAI,CAACF,WAAW,CAACG,UAAU,EAAE;YAClD,MAAM,IAAIC,KAAK,CAAC,oEAAoE,CAAC;UACvF;UAEAC,+BAAkB,CAACC,KAAK,CAAC,oBAAoB,CAAC;UAE9CD,+BAAkB,CAACE,GAAG,CAAC,oBAAoB,CAAC;QAC9C,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;UACrE,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAdKE,UAAUA,CAAA;QAAA,OAAAZ,WAAA,CAAAa,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVF,UAAU;IAAA;EAAA;IAAAd,GAAA;IAAAC,KAAA;MAAA,IAAAgB,mBAAA,OAAAd,kBAAA,CAAA5B,OAAA,EAmBhB,aAAuD;QACrD,IAAI;UACF,IAAM2C,gBAAgB,SAASC,kBAAM,CAACC,6BAA6B,CAAC,CAAC;UACrE,IAAMC,oBAAoB,SAASF,kBAAM,CAACG,iCAAiC,CAAC,CAAC;UAC7E,IAAMC,sBAAsB,SAAS1D,YAAY,CAAC2D,uBAAuB,CAAC,CAAC;UAE3E,OAAO;YACLlB,MAAM,EAAEY,gBAAgB,CAACO,MAAM,KAAK,SAAS;YAC7ClB,UAAU,EAAEc,oBAAoB,CAACI,MAAM,KAAK,SAAS;YACrDC,YAAY,EAAEH,sBAAsB,CAACE,MAAM,KAAK;UAClD,CAAC;QACH,CAAC,CAAC,OAAOb,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACtD,OAAO;YACLN,MAAM,EAAE,KAAK;YACbC,UAAU,EAAE,KAAK;YACjBmB,YAAY,EAAE;UAChB,CAAC;QACH;MACF,CAAC;MAAA,SAnBKrB,kBAAkBA,CAAA;QAAA,OAAAY,mBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBX,kBAAkB;IAAA;EAAA;IAAAL,GAAA;IAAAC,KAAA,EAwBxB,SAAA0B,YAAYA,CAACC,GAAkB,EAAQ;MACrC,IAAI,CAACrC,SAAS,GAAGqC,GAAG;IACtB;EAAC;IAAA5B,GAAA;IAAAC,KAAA;MAAA,IAAA4B,eAAA,OAAA1B,kBAAA,CAAA5B,OAAA,EAKD,WAAqBuD,MAA4B,EAAiB;QAChE,IAAI,CAAC,IAAI,CAACvC,SAAS,EAAE;UACnB,MAAM,IAAIiB,KAAK,CAAC,0BAA0B,CAAC;QAC7C;QAEA,IAAI,IAAI,CAAChB,WAAW,EAAE;UACpB,MAAM,IAAIgB,KAAK,CAAC,+BAA+B,CAAC;QAClD;QAEA,IAAI;UACFC,+BAAkB,CAACC,KAAK,CAAC,uBAAuB,CAAC;UAEjD,IAAMqB,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAACF,MAAM,CAAC;UAEzD,IAAI,CAACtC,WAAW,GAAG,IAAI;UACvB,IAAI,CAACC,QAAQ,GAAG,KAAK;UACrB,IAAI,CAACC,kBAAkB,GAAGuC,IAAI,CAACC,GAAG,CAAC,CAAC;UACpC,IAAI,CAACvC,cAAc,GAAG,CAAC;UAEvB,IAAMwC,gBAAgB,GAAG,IAAI,CAAC5C,SAAS,CAAC6C,WAAW,CAACL,gBAAgB,CAAC;UAGrE,IAAI,CAACM,uBAAuB,CAAC,CAAC;UAE9B,IAAMC,MAAM,SAASH,gBAAgB;UACrC,IAAI,CAACvC,mBAAmB,GAAG0C,MAAM,CAACC,GAAG;UAErC9B,+BAAkB,CAACE,GAAG,CAAC,uBAAuB,CAAC;QACjD,CAAC,CAAC,OAAOC,KAAK,EAAE;UACd,IAAI,CAACpB,WAAW,GAAG,KAAK;UACxBqB,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAjCK4B,cAAcA,CAAAC,EAAA;QAAA,OAAAZ,eAAA,CAAAd,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdwB,cAAc;IAAA;EAAA;IAAAxC,GAAA;IAAAC,KAAA;MAAA,IAAAyC,cAAA,OAAAvC,kBAAA,CAAA5B,OAAA,EAsCpB,aAAqD;QACnD,IAAI,CAAC,IAAI,CAACgB,SAAS,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;UACxC,MAAM,IAAIgB,KAAK,CAAC,6BAA6B,CAAC;QAChD;QAEA,IAAI;UACFC,+BAAkB,CAACC,KAAK,CAAC,sBAAsB,CAAC;UAEhD,IAAI,CAACnB,SAAS,CAACoD,aAAa,CAAC,CAAC;UAC9B,IAAI,CAACnD,WAAW,GAAG,KAAK;UACxB,IAAI,CAACC,QAAQ,GAAG,KAAK;UAGrB,IAAI,CAACmD,sBAAsB,CAAC,CAAC;UAE7B,IAAI,CAAC,IAAI,CAAChD,mBAAmB,EAAE;YAC7B,MAAM,IAAIY,KAAK,CAAC,6BAA6B,CAAC;UAChD;UAGA,IAAMqC,QAAQ,SAAS9E,UAAU,CAAC+E,YAAY,CAAC,IAAI,CAAClD,mBAAmB,CAAC;UACxE,IAAI,CAACiD,QAAQ,CAACE,MAAM,EAAE;YACpB,MAAM,IAAIvC,KAAK,CAAC,0BAA0B,CAAC;UAC7C;UAEA,IAAMwC,QAAQ,GAAG,CAACf,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACxC,kBAAkB,GAAG,IAAI,CAACC,cAAc,IAAI,IAAI;UAGpF,IAAMsD,SAAS,SAAS,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACtD,mBAAmB,CAAC;UAExE,IAAM0C,MAA4B,GAAG;YACnCC,GAAG,EAAE,IAAI,CAAC3C,mBAAmB;YAC7BoD,QAAQ,EAARA,QAAQ;YACRG,QAAQ,EAAEN,QAAQ,CAACO,IAAI,IAAI,CAAC;YAC5BC,KAAK,EAAE,IAAI;YACXC,MAAM,EAAE,IAAI;YACZL,SAAS,EAATA;UACF,CAAC;UAEDxC,+BAAkB,CAACE,GAAG,CAAC,sBAAsB,CAAC;UAC9C,OAAO2B,MAAM;QACf,CAAC,CAAC,OAAO1B,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SA7CK+B,aAAaA,CAAA;QAAA,OAAAD,cAAA,CAAA3B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAb2B,aAAa;IAAA;EAAA;IAAA3C,GAAA;IAAAC,KAAA;MAAA,IAAAsD,eAAA,OAAApD,kBAAA,CAAA5B,OAAA,EAkDnB,aAAsC;QACpC,IAAI,CAAC,IAAI,CAACiB,WAAW,IAAI,IAAI,CAACC,QAAQ,EAAE;UACtC;QACF;QAEA,IAAI;UAGF,IAAI,CAACA,QAAQ,GAAG,IAAI;UACpBoB,OAAO,CAAC2C,GAAG,CAAC,+CAA+C,CAAC;QAC9D,CAAC,CAAC,OAAO5C,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAdK6C,cAAcA,CAAA;QAAA,OAAAF,eAAA,CAAAxC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdyC,cAAc;IAAA;EAAA;IAAAzD,GAAA;IAAAC,KAAA;MAAA,IAAAyD,gBAAA,OAAAvD,kBAAA,CAAA5B,OAAA,EAmBpB,aAAuC;QACrC,IAAI,CAAC,IAAI,CAACiB,WAAW,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;UACvC;QACF;QAEA,IAAI;UAGF,IAAI,CAACA,QAAQ,GAAG,KAAK;UACrBoB,OAAO,CAAC2C,GAAG,CAAC,gDAAgD,CAAC;QAC/D,CAAC,CAAC,OAAO5C,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAdK+C,eAAeA,CAAA;QAAA,OAAAD,gBAAA,CAAA3C,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAf2C,eAAe;IAAA;EAAA;IAAA3D,GAAA;IAAAC,KAAA,EAmBrB,SAAA2D,kBAAkBA,CAAA,EAAsB;MACtC,IAAMC,WAAW,GAAG5B,IAAI,CAACC,GAAG,CAAC,CAAC;MAC9B,IAAMc,QAAQ,GAAG,IAAI,CAACxD,WAAW,GAC7B,CAACqE,WAAW,GAAG,IAAI,CAACnE,kBAAkB,GAAG,IAAI,CAACC,cAAc,IAAI,IAAI,GACpE,CAAC;MAEL,OAAO;QACLqD,QAAQ,EAARA,QAAQ;QACRG,QAAQ,EAAE,CAAC;QACX3D,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BC,QAAQ,EAAE,IAAI,CAACA;MACjB,CAAC;IACH;EAAC;IAAAO,GAAA;IAAAC,KAAA,EAKD,SAAA6D,mBAAmBA,CAACC,QAA+C,EAAQ;MACzE,IAAI,CAAClE,gBAAgB,GAAGkE,QAAQ;IAClC;EAAC;IAAA/D,GAAA;IAAAC,KAAA;MAAA,IAAA+D,cAAA,OAAA7D,kBAAA,CAAA5B,OAAA,EAKD,WAAoBgE,GAAW,EAAmB;QAChD,IAAI;UACF,IAAMnC,WAAW,SAASvC,YAAY,CAAC2D,uBAAuB,CAAC,CAAC;UAChE,IAAI,CAACpB,WAAW,CAAC6D,OAAO,EAAE;YACxB,MAAM,IAAIzD,KAAK,CAAC,iDAAiD,CAAC;UACpE;UAEA,IAAM0D,KAAK,SAASrG,YAAY,CAACsG,gBAAgB,CAAC5B,GAAG,CAAC;UACtD,OAAO2B,KAAK,CAAC3B,GAAG;QAClB,CAAC,CAAC,OAAO3B,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UACxD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAbKwD,aAAaA,CAAAC,GAAA;QAAA,OAAAL,cAAA,CAAAjD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAboD,aAAa;IAAA;EAAA;IAAApE,GAAA;IAAAC,KAAA;MAAA,IAAAqE,cAAA,OAAAnE,kBAAA,CAAA5B,OAAA,EAkBnB,WAAoBgE,GAAW,EAAkE;QAAA,IAAhEgC,OAAkC,GAAAvD,SAAA,CAAAwD,MAAA,QAAAxD,SAAA,QAAAyD,SAAA,GAAAzD,SAAA,MAAG,QAAQ;QAC5E,IAAI;UACFP,+BAAkB,CAACC,KAAK,CAAC,mBAAmB,CAAC;UAG7C,IAAMmC,QAAQ,SAAS9E,UAAU,CAAC+E,YAAY,CAACP,GAAG,CAAC;UACnD,IAAI,CAACM,QAAQ,CAACE,MAAM,EAAE;YACpB,MAAM,IAAIvC,KAAK,CAAC,sCAAsC,CAAC;UACzD;UAEA,IAAMkE,YAAY,GAAG7B,QAAQ,CAACO,IAAI,IAAI,CAAC;UACvCvC,OAAO,CAAC2C,GAAG,CAAC,sBAAsBkB,YAAY,oBAAoBH,OAAO,EAAE,CAAC;UAG5E,IAAMI,aAAa,GAAG,GAAG5G,UAAU,CAAC6G,cAAc,cAAc3C,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;UAGhF,IAAM2C,mBAAmB,GAAG;YAC1BC,GAAG,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAI,CAAC;YACzCC,MAAM,EAAE;cAAEF,OAAO,EAAE,OAAO;cAAEC,UAAU,EAAE;YAAI,CAAC;YAC7CE,IAAI,EAAE;cAAEH,OAAO,EAAE,OAAO;cAAEC,UAAU,EAAE;YAAK;UAC7C,CAAC;UAED,IAAMG,QAAQ,GAAGN,mBAAmB,CAACN,OAAO,CAAC;UAI7C,MAAMxG,UAAU,CAACqH,SAAS,CAAC;YACzBC,IAAI,EAAE9C,GAAG;YACT+C,EAAE,EAAEX;UACN,CAAC,CAAC;UAEF,IAAMY,cAAc,SAASxH,UAAU,CAAC+E,YAAY,CAAC6B,aAAa,CAAC;UACnE,IAAMa,cAAc,GAAGD,cAAc,CAACnC,IAAI,IAAI,CAAC;UAE/CvC,OAAO,CAAC2C,GAAG,CAAC,qBAAqBkB,YAAY,OAAOc,cAAc,QAAQ,CAAC;UAE3E/E,+BAAkB,CAACE,GAAG,CAAC,mBAAmB,CAAC;UAC3C,OAAOgE,aAAa;QACtB,CAAC,CAAC,OAAO/D,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SA3CK6E,aAAaA,CAAAC,GAAA;QAAA,OAAApB,cAAA,CAAAvD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAbyE,aAAa;IAAA;EAAA;IAAAzF,GAAA;IAAAC,KAAA;MAAA,IAAA0F,kBAAA,OAAAxF,kBAAA,CAAA5B,OAAA,EAgDnB,WAAgCgE,GAAW,EAAmB;QAC5D,IAAI;UAEF,IAAMqD,YAAY,GAAG,GAAG7H,UAAU,CAAC6G,cAAc,aAAa3C,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;UAE9ErB,OAAO,CAAC2C,GAAG,CAAC,iCAAiC,EAAEjB,GAAG,CAAC;UAUnD,OAAO,EAAE;QACX,CAAC,CAAC,OAAO3B,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD,OAAO,EAAE;QACX;MACF,CAAC;MAAA,SApBasC,iBAAiBA,CAAA2C,GAAA;QAAA,OAAAF,kBAAA,CAAA5E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBkC,iBAAiB;IAAA;EAAA;IAAAlD,GAAA;IAAAC,KAAA,EAyB/B,SAAQ+B,mBAAmBA,CAACF,MAA4B,EAAO;MAC7D,IAAMgE,UAAwC,GAAG;QAC/ChB,GAAG,EAAEiB,wBAAY,CAAC,MAAM,CAAC;QACzBd,MAAM,EAAEc,wBAAY,CAAC,MAAM,CAAC;QAC5Bb,IAAI,EAAEa,wBAAY,CAAC,OAAO,CAAC;QAC3BC,KAAK,EAAED,wBAAY,CAAC,OAAO;MAC7B,CAAC;MAED,OAAO;QACLxB,OAAO,EAAEuB,UAAU,CAAChE,MAAM,CAACyC,OAAO,CAAC,IAAIwB,wBAAY,CAAC,MAAM,CAAC;QAC3DE,WAAW,EAAEnE,MAAM,CAACoE,kBAAkB,GAAG,EAAE;QAC3CC,IAAI,EAAE,CAACrE,MAAM,CAACsE;MAEhB,CAAC;IACH;EAAC;IAAApG,GAAA;IAAAC,KAAA,EAKD,SAAQoC,uBAAuBA,CAAA,EAAS;MAAA,IAAAgE,KAAA;MACtC,IAAI,IAAI,CAACvG,gBAAgB,EAAE;QACzBwG,aAAa,CAAC,IAAI,CAACxG,gBAAgB,CAAC;MACtC;MAEA,IAAI,CAACA,gBAAgB,GAAGyG,WAAW,CAAC,YAAM;QACxC,IAAIF,KAAI,CAACxG,gBAAgB,EAAE;UACzB,IAAM2G,QAAQ,GAAGH,KAAI,CAACzC,kBAAkB,CAAC,CAAC;UAC1CyC,KAAI,CAACxG,gBAAgB,CAAC2G,QAAQ,CAAC;QACjC;MACF,CAAC,EAAE,IAAI,CAAC;IACV;EAAC;IAAAxG,GAAA;IAAAC,KAAA,EAKD,SAAQ2C,sBAAsBA,CAAA,EAAS;MACrC,IAAI,IAAI,CAAC9C,gBAAgB,EAAE;QACzBwG,aAAa,CAAC,IAAI,CAACxG,gBAAgB,CAAC;QACpC,IAAI,CAACA,gBAAgB,GAAG,IAAI;MAC9B;IACF;EAAC;IAAAE,GAAA;IAAAC,KAAA,EAKD,SAAAwG,OAAOA,CAAA,EAAS;MACd,IAAI,CAAC7D,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACpD,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACG,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC9B;EAAC;AAAA;AAII,IAAM6G,qBAAqB,GAAAC,OAAA,CAAAD,qBAAA,GAAG,IAAIrH,qBAAqB,CAAC,CAAC", "ignoreList": []}