{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "createSheet", "_canUseDom", "_createCSSStyleSheet", "_createOrderedCSSStyleSheet", "defaultId", "roots", "WeakMap", "sheets", "initialRules", "root", "id", "sheet", "rootNode", "getRootNode", "document", "length", "for<PERSON>ach", "rule", "insert", "set", "push", "index", "get", "initialSheet", "textContent", "getTextContent", "cssText", "groupValue", "s"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.createSheet = createSheet;\nvar _canUseDom = _interopRequireDefault(require(\"../../../modules/canUseDom\"));\nvar _createCSSStyleSheet = _interopRequireDefault(require(\"./createCSSStyleSheet\"));\nvar _createOrderedCSSStyleSheet = _interopRequireDefault(require(\"./createOrderedCSSStyleSheet\"));\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar defaultId = 'react-native-stylesheet';\nvar roots = new WeakMap();\nvar sheets = [];\nvar initialRules = [\n// minimal top-level reset\n'html{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;-webkit-tap-highlight-color:rgba(0,0,0,0);}', 'body{margin:0;}',\n// minimal form pseudo-element reset\n'button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0;}', 'input::-webkit-search-cancel-button,input::-webkit-search-decoration,input::-webkit-search-results-button,input::-webkit-search-results-decoration{display:none;}'];\nfunction createSheet(root, id) {\n  if (id === void 0) {\n    id = defaultId;\n  }\n  var sheet;\n  if (_canUseDom.default) {\n    var rootNode = root != null ? root.getRootNode() : document;\n    // Create the initial style sheet\n    if (sheets.length === 0) {\n      sheet = (0, _createOrderedCSSStyleSheet.default)((0, _createCSSStyleSheet.default)(id));\n      initialRules.forEach(rule => {\n        sheet.insert(rule, 0);\n      });\n      roots.set(rootNode, sheets.length);\n      sheets.push(sheet);\n    } else {\n      var index = roots.get(rootNode);\n      if (index == null) {\n        var initialSheet = sheets[0];\n        // If we're creating a new sheet, populate it with existing styles\n        var textContent = initialSheet != null ? initialSheet.getTextContent() : '';\n        // Cast rootNode to 'any' because Flow types for getRootNode are wrong\n        sheet = (0, _createOrderedCSSStyleSheet.default)((0, _createCSSStyleSheet.default)(id, rootNode, textContent));\n        roots.set(rootNode, sheets.length);\n        sheets.push(sheet);\n      } else {\n        sheet = sheets[index];\n      }\n    }\n  } else {\n    // Create the initial style sheet\n    if (sheets.length === 0) {\n      sheet = (0, _createOrderedCSSStyleSheet.default)((0, _createCSSStyleSheet.default)(id));\n      initialRules.forEach(rule => {\n        sheet.insert(rule, 0);\n      });\n      sheets.push(sheet);\n    } else {\n      sheet = sheets[0];\n    }\n  }\n  return {\n    getTextContent() {\n      return sheet.getTextContent();\n    },\n    id,\n    insert(cssText, groupValue) {\n      sheets.forEach(s => {\n        s.insert(cssText, groupValue);\n      });\n    }\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,WAAW,GAAGA,WAAW;AACjC,IAAIC,UAAU,GAAGN,sBAAsB,CAACC,OAAO,6BAA6B,CAAC,CAAC;AAC9E,IAAIM,oBAAoB,GAAGP,sBAAsB,CAACC,OAAO,wBAAwB,CAAC,CAAC;AACnF,IAAIO,2BAA2B,GAAGR,sBAAsB,CAACC,OAAO,+BAA+B,CAAC,CAAC;AAUjG,IAAIQ,SAAS,GAAG,yBAAyB;AACzC,IAAIC,KAAK,GAAG,IAAIC,OAAO,CAAC,CAAC;AACzB,IAAIC,MAAM,GAAG,EAAE;AACf,IAAIC,YAAY,GAAG,CAEnB,0GAA0G,EAAE,iBAAiB,EAE7H,uEAAuE,EAAE,mKAAmK,CAAC;AAC7O,SAASR,WAAWA,CAACS,IAAI,EAAEC,EAAE,EAAE;EAC7B,IAAIA,EAAE,KAAK,KAAK,CAAC,EAAE;IACjBA,EAAE,GAAGN,SAAS;EAChB;EACA,IAAIO,KAAK;EACT,IAAIV,UAAU,CAACJ,OAAO,EAAE;IACtB,IAAIe,QAAQ,GAAGH,IAAI,IAAI,IAAI,GAAGA,IAAI,CAACI,WAAW,CAAC,CAAC,GAAGC,QAAQ;IAE3D,IAAIP,MAAM,CAACQ,MAAM,KAAK,CAAC,EAAE;MACvBJ,KAAK,GAAG,CAAC,CAAC,EAAER,2BAA2B,CAACN,OAAO,EAAE,CAAC,CAAC,EAAEK,oBAAoB,CAACL,OAAO,EAAEa,EAAE,CAAC,CAAC;MACvFF,YAAY,CAACQ,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3BN,KAAK,CAACO,MAAM,CAACD,IAAI,EAAE,CAAC,CAAC;MACvB,CAAC,CAAC;MACFZ,KAAK,CAACc,GAAG,CAACP,QAAQ,EAAEL,MAAM,CAACQ,MAAM,CAAC;MAClCR,MAAM,CAACa,IAAI,CAACT,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,IAAIU,KAAK,GAAGhB,KAAK,CAACiB,GAAG,CAACV,QAAQ,CAAC;MAC/B,IAAIS,KAAK,IAAI,IAAI,EAAE;QACjB,IAAIE,YAAY,GAAGhB,MAAM,CAAC,CAAC,CAAC;QAE5B,IAAIiB,WAAW,GAAGD,YAAY,IAAI,IAAI,GAAGA,YAAY,CAACE,cAAc,CAAC,CAAC,GAAG,EAAE;QAE3Ed,KAAK,GAAG,CAAC,CAAC,EAAER,2BAA2B,CAACN,OAAO,EAAE,CAAC,CAAC,EAAEK,oBAAoB,CAACL,OAAO,EAAEa,EAAE,EAAEE,QAAQ,EAAEY,WAAW,CAAC,CAAC;QAC9GnB,KAAK,CAACc,GAAG,CAACP,QAAQ,EAAEL,MAAM,CAACQ,MAAM,CAAC;QAClCR,MAAM,CAACa,IAAI,CAACT,KAAK,CAAC;MACpB,CAAC,MAAM;QACLA,KAAK,GAAGJ,MAAM,CAACc,KAAK,CAAC;MACvB;IACF;EACF,CAAC,MAAM;IAEL,IAAId,MAAM,CAACQ,MAAM,KAAK,CAAC,EAAE;MACvBJ,KAAK,GAAG,CAAC,CAAC,EAAER,2BAA2B,CAACN,OAAO,EAAE,CAAC,CAAC,EAAEK,oBAAoB,CAACL,OAAO,EAAEa,EAAE,CAAC,CAAC;MACvFF,YAAY,CAACQ,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3BN,KAAK,CAACO,MAAM,CAACD,IAAI,EAAE,CAAC,CAAC;MACvB,CAAC,CAAC;MACFV,MAAM,CAACa,IAAI,CAACT,KAAK,CAAC;IACpB,CAAC,MAAM;MACLA,KAAK,GAAGJ,MAAM,CAAC,CAAC,CAAC;IACnB;EACF;EACA,OAAO;IACLkB,cAAc,WAAdA,cAAcA,CAAA,EAAG;MACf,OAAOd,KAAK,CAACc,cAAc,CAAC,CAAC;IAC/B,CAAC;IACDf,EAAE,EAAFA,EAAE;IACFQ,MAAM,WAANA,MAAMA,CAACQ,OAAO,EAAEC,UAAU,EAAE;MAC1BpB,MAAM,CAACS,OAAO,CAAC,UAAAY,CAAC,EAAI;QAClBA,CAAC,CAACV,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC;MAC/B,CAAC,CAAC;IACJ;EACF,CAAC;AACH", "ignoreList": []}