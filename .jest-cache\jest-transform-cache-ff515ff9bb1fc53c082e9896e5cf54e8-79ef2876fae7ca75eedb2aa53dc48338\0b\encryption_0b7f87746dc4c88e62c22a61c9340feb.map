{"version": 3, "names": ["CryptoJS", "SecureStore", "Platform", "env", "EncryptionService", "_classCallCheck", "defaultOptions", "cov_2q529b4gnv", "s", "algorithm", "keySize", "mode", "padding", "iterations", "<PERSON><PERSON><PERSON>", "keyCache", "Map", "_createClass", "key", "value", "_initialize", "_asyncToGenerator", "f", "getMasterKey", "get", "b", "console", "log", "error", "Error", "initialize", "apply", "arguments", "encrypt", "data", "password", "options", "length", "undefined", "config", "Object", "assign", "salt", "lib", "WordArray", "random", "iv", "<PERSON><PERSON><PERSON>", "PBKDF2", "encrypted", "AES", "pad", "toString", "timestamp", "Date", "now", "decrypt", "encryptedData", "enc", "Hex", "parse", "decrypted", "CBC", "Pkcs7", "decryptedText", "Utf8", "hash", "SHA256", "SHA512", "MD5", "<PERSON><PERSON>ey", "_secureStore", "OS", "localStorage", "setItem", "JSON", "stringify", "storeOptions", "requireAuthentication", "keychainService", "accessGroup", "setItemAsync", "secureStore", "_x", "_x2", "_secureRetrieve", "stored", "getItem", "getItemAsync", "secureRetrieve", "_x3", "_secureDelete", "removeItem", "deleteItemAsync", "secureDelete", "_x4", "encryptUserProfile", "profile", "_this", "sensitiveFields", "profileCopy", "for<PERSON>ach", "field", "decryptUserProfile", "encryptedProfile", "_this2", "decry<PERSON><PERSON>son", "_getMaster<PERSON>ey", "_rotateKeys", "newMasterKey", "clear", "rotateKeys", "_clearAll", "keysToRemove", "i", "startsWith", "push", "clearAll", "validateIntegrity", "requiredFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "every", "hasOwnProperty", "maxAge", "isNotExpired", "encryptionService"], "sources": ["encryption.ts"], "sourcesContent": ["import CryptoJS from 'crypto-js';\nimport * as SecureStore from 'expo-secure-store';\nimport { Platform } from 'react-native';\nimport env from '@/config/environment';\n\n/**\n * Comprehensive Encryption Service\n * Handles all sensitive data encryption/decryption with multiple layers of security\n */\n\nexport interface EncryptionOptions {\n  algorithm?: 'AES' | 'DES' | 'TripleDES';\n  keySize?: 128 | 192 | 256;\n  mode?: 'CBC' | 'CFB' | 'CTR' | 'ECB' | 'OFB';\n  padding?: 'Pkcs7' | 'AnsiX923' | 'Iso10126' | 'NoPadding';\n  iterations?: number;\n}\n\nexport interface EncryptedData {\n  data: string;\n  iv: string;\n  salt: string;\n  timestamp: number;\n  algorithm: string;\n  keySize: number;\n}\n\nexport interface SecureStorageOptions {\n  requireAuthentication?: boolean;\n  accessGroup?: string;\n  keychainService?: string;\n}\n\nclass EncryptionService {\n  private readonly defaultOptions: Required<EncryptionOptions> = {\n    algorithm: 'AES',\n    keySize: 256,\n    mode: 'CBC',\n    padding: 'Pkcs7',\n    iterations: 10000,\n  };\n\n  private masterKey: string | null = null;\n  private keyCache: Map<string, string> = new Map();\n\n  /**\n   * Initialize encryption service with master key\n   */\n  async initialize(): Promise<void> {\n    try {\n      // Generate or retrieve master key\n      this.masterKey = await this.getMasterKey();\n      \n      if (env.get('DEBUG_MODE')) {\n        console.log('🔐 Encryption service initialized');\n      }\n    } catch (error) {\n      console.error('Failed to initialize encryption service:', error);\n      throw new Error('Encryption service initialization failed');\n    }\n  }\n\n  /**\n   * Encrypt sensitive data\n   */\n  encrypt(\n    data: string, \n    password?: string, \n    options: EncryptionOptions = {}\n  ): EncryptedData {\n    try {\n      const config = { ...this.defaultOptions, ...options };\n      const key = password || this.masterKey;\n      \n      if (!key) {\n        throw new Error('No encryption key available');\n      }\n\n      // Generate random salt and IV\n      const salt = CryptoJS.lib.WordArray.random(256/8);\n      const iv = CryptoJS.lib.WordArray.random(128/8);\n\n      // Derive key using PBKDF2\n      const derivedKey = CryptoJS.PBKDF2(key, salt, {\n        keySize: config.keySize / 32,\n        iterations: config.iterations,\n      });\n\n      // Encrypt data\n      const encrypted = CryptoJS.AES.encrypt(data, derivedKey, {\n        iv: iv,\n        mode: CryptoJS.mode[config.mode],\n        padding: CryptoJS.pad[config.padding],\n      });\n\n      return {\n        data: encrypted.toString(),\n        iv: iv.toString(),\n        salt: salt.toString(),\n        timestamp: Date.now(),\n        algorithm: config.algorithm,\n        keySize: config.keySize,\n      };\n    } catch (error) {\n      console.error('Encryption failed:', error);\n      throw new Error('Data encryption failed');\n    }\n  }\n\n  /**\n   * Decrypt sensitive data\n   */\n  decrypt(\n    encryptedData: EncryptedData, \n    password?: string\n  ): string {\n    try {\n      const key = password || this.masterKey;\n      \n      if (!key) {\n        throw new Error('No decryption key available');\n      }\n\n      // Recreate salt and IV\n      const salt = CryptoJS.enc.Hex.parse(encryptedData.salt);\n      const iv = CryptoJS.enc.Hex.parse(encryptedData.iv);\n\n      // Derive key using same parameters\n      const derivedKey = CryptoJS.PBKDF2(key, salt, {\n        keySize: encryptedData.keySize / 32,\n        iterations: this.defaultOptions.iterations,\n      });\n\n      // Decrypt data\n      const decrypted = CryptoJS.AES.decrypt(encryptedData.data, derivedKey, {\n        iv: iv,\n        mode: CryptoJS.mode.CBC,\n        padding: CryptoJS.pad.Pkcs7,\n      });\n\n      const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);\n      \n      if (!decryptedText) {\n        throw new Error('Decryption resulted in empty data');\n      }\n\n      return decryptedText;\n    } catch (error) {\n      console.error('Decryption failed:', error);\n      throw new Error('Data decryption failed');\n    }\n  }\n\n  /**\n   * Hash sensitive data (one-way)\n   */\n  hash(data: string, algorithm: 'SHA256' | 'SHA512' | 'MD5' = 'SHA256'): string {\n    try {\n      switch (algorithm) {\n        case 'SHA256':\n          return CryptoJS.SHA256(data).toString();\n        case 'SHA512':\n          return CryptoJS.SHA512(data).toString();\n        case 'MD5':\n          return CryptoJS.MD5(data).toString();\n        default:\n          throw new Error(`Unsupported hash algorithm: ${algorithm}`);\n      }\n    } catch (error) {\n      console.error('Hashing failed:', error);\n      throw new Error('Data hashing failed');\n    }\n  }\n\n  /**\n   * Generate secure random key\n   */\n  generateKey(length: number = 32): string {\n    return CryptoJS.lib.WordArray.random(length).toString();\n  }\n\n  /**\n   * Secure storage operations using device keychain/keystore\n   */\n  async secureStore(\n    key: string, \n    value: string, \n    options: SecureStorageOptions = {}\n  ): Promise<void> {\n    try {\n      if (Platform.OS === 'web') {\n        // Fallback to encrypted localStorage for web\n        const encrypted = this.encrypt(value);\n        localStorage.setItem(key, JSON.stringify(encrypted));\n        return;\n      }\n\n      const storeOptions: SecureStore.SecureStoreOptions = {\n        requireAuthentication: options.requireAuthentication || false,\n        keychainService: options.keychainService || 'AceMindSecureStore',\n      };\n\n      if (Platform.OS === 'ios' && options.accessGroup) {\n        (storeOptions as any).accessGroup = options.accessGroup;\n      }\n\n      await SecureStore.setItemAsync(key, value, storeOptions);\n    } catch (error) {\n      console.error('Secure store failed:', error);\n      throw new Error('Failed to store data securely');\n    }\n  }\n\n  /**\n   * Retrieve from secure storage\n   */\n  async secureRetrieve(\n    key: string, \n    options: SecureStorageOptions = {}\n  ): Promise<string | null> {\n    try {\n      if (Platform.OS === 'web') {\n        // Fallback to encrypted localStorage for web\n        const stored = localStorage.getItem(key);\n        if (!stored) return null;\n        \n        const encrypted = JSON.parse(stored) as EncryptedData;\n        return this.decrypt(encrypted);\n      }\n\n      const storeOptions: SecureStore.SecureStoreOptions = {\n        requireAuthentication: options.requireAuthentication || false,\n        keychainService: options.keychainService || 'AceMindSecureStore',\n      };\n\n      if (Platform.OS === 'ios' && options.accessGroup) {\n        (storeOptions as any).accessGroup = options.accessGroup;\n      }\n\n      return await SecureStore.getItemAsync(key, storeOptions);\n    } catch (error) {\n      console.error('Secure retrieve failed:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Delete from secure storage\n   */\n  async secureDelete(\n    key: string, \n    options: SecureStorageOptions = {}\n  ): Promise<void> {\n    try {\n      if (Platform.OS === 'web') {\n        localStorage.removeItem(key);\n        return;\n      }\n\n      const storeOptions: SecureStore.SecureStoreOptions = {\n        requireAuthentication: options.requireAuthentication || false,\n        keychainService: options.keychainService || 'AceMindSecureStore',\n      };\n\n      await SecureStore.deleteItemAsync(key, storeOptions);\n    } catch (error) {\n      console.error('Secure delete failed:', error);\n      throw new Error('Failed to delete secure data');\n    }\n  }\n\n  /**\n   * Encrypt user profile data\n   */\n  encryptUserProfile(profile: any): EncryptedData {\n    const sensitiveFields = [\n      'email',\n      'phone',\n      'address',\n      'birthDate',\n      'emergencyContact',\n    ];\n\n    const profileCopy = { ...profile };\n    \n    // Encrypt sensitive fields\n    sensitiveFields.forEach(field => {\n      if (profileCopy[field]) {\n        profileCopy[field] = this.encrypt(profileCopy[field].toString());\n      }\n    });\n\n    return this.encrypt(JSON.stringify(profileCopy));\n  }\n\n  /**\n   * Decrypt user profile data\n   */\n  decryptUserProfile(encryptedProfile: EncryptedData): any {\n    try {\n      const decryptedJson = this.decrypt(encryptedProfile);\n      const profile = JSON.parse(decryptedJson);\n\n      const sensitiveFields = [\n        'email',\n        'phone',\n        'address',\n        'birthDate',\n        'emergencyContact',\n      ];\n\n      // Decrypt sensitive fields\n      sensitiveFields.forEach(field => {\n        if (profile[field] && typeof profile[field] === 'object') {\n          profile[field] = this.decrypt(profile[field]);\n        }\n      });\n\n      return profile;\n    } catch (error) {\n      console.error('Profile decryption failed:', error);\n      throw new Error('Failed to decrypt user profile');\n    }\n  }\n\n  /**\n   * Get or generate master key\n   */\n  private async getMasterKey(): Promise<string> {\n    try {\n      // Try to retrieve existing master key\n      let masterKey = await this.secureRetrieve('master_key');\n      \n      if (!masterKey) {\n        // Generate new master key\n        masterKey = this.generateKey(64);\n        await this.secureStore('master_key', masterKey, {\n          requireAuthentication: true,\n        });\n      }\n\n      return masterKey;\n    } catch (error) {\n      console.error('Master key generation failed:', error);\n      throw new Error('Failed to initialize master key');\n    }\n  }\n\n  /**\n   * Rotate encryption keys\n   */\n  async rotateKeys(): Promise<void> {\n    try {\n      // Generate new master key\n      const newMasterKey = this.generateKey(64);\n      \n      // Store new master key\n      await this.secureStore('master_key', newMasterKey, {\n        requireAuthentication: true,\n      });\n\n      // Update instance\n      this.masterKey = newMasterKey;\n      this.keyCache.clear();\n\n      if (env.get('DEBUG_MODE')) {\n        console.log('🔄 Encryption keys rotated successfully');\n      }\n    } catch (error) {\n      console.error('Key rotation failed:', error);\n      throw new Error('Failed to rotate encryption keys');\n    }\n  }\n\n  /**\n   * Clear all encryption data\n   */\n  async clearAll(): Promise<void> {\n    try {\n      await this.secureDelete('master_key');\n      this.masterKey = null;\n      this.keyCache.clear();\n      \n      if (Platform.OS === 'web') {\n        // Clear encrypted localStorage items\n        const keysToRemove = [];\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          if (key && key.startsWith('encrypted_')) {\n            keysToRemove.push(key);\n          }\n        }\n        keysToRemove.forEach(key => localStorage.removeItem(key));\n      }\n\n      if (env.get('DEBUG_MODE')) {\n        console.log('🗑️ All encryption data cleared');\n      }\n    } catch (error) {\n      console.error('Clear encryption data failed:', error);\n      throw new Error('Failed to clear encryption data');\n    }\n  }\n\n  /**\n   * Validate encryption integrity\n   */\n  validateIntegrity(encryptedData: EncryptedData): boolean {\n    try {\n      // Check required fields\n      const requiredFields = ['data', 'iv', 'salt', 'timestamp', 'algorithm', 'keySize'];\n      const hasAllFields = requiredFields.every(field => \n        encryptedData.hasOwnProperty(field) && (encryptedData as any)[field] !== null\n      );\n\n      if (!hasAllFields) {\n        return false;\n      }\n\n      // Check timestamp (reject data older than 30 days)\n      const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds\n      const isNotExpired = (Date.now() - encryptedData.timestamp) < maxAge;\n\n      return isNotExpired;\n    } catch (error) {\n      console.error('Integrity validation failed:', error);\n      return false;\n    }\n  }\n}\n\n// Create singleton instance\nexport const encryptionService = new EncryptionService();\n\n// Convenience functions\nexport const encrypt = (data: string, password?: string, options?: EncryptionOptions) => \n  encryptionService.encrypt(data, password, options);\n\nexport const decrypt = (encryptedData: EncryptedData, password?: string) => \n  encryptionService.decrypt(encryptedData, password);\n\nexport const hash = (data: string, algorithm?: 'SHA256' | 'SHA512' | 'MD5') => \n  encryptionService.hash(data, algorithm);\n\nexport const secureStore = (key: string, value: string, options?: SecureStorageOptions) => \n  encryptionService.secureStore(key, value, options);\n\nexport const secureRetrieve = (key: string, options?: SecureStorageOptions) => \n  encryptionService.secureRetrieve(key, options);\n\nexport const secureDelete = (key: string, options?: SecureStorageOptions) => \n  encryptionService.secureDelete(key, options);\n\nexport default encryptionService;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,QAAQ,MAAM,WAAW;AAChC,OAAO,KAAKC,WAAW,MAAM,mBAAmB;AAChD,SAASC,QAAQ,QAAQ,cAAc;AACvC,OAAOC,GAAG;AAA6B,IA8BjCC,iBAAiB;EAAA,SAAAA,kBAAA;IAAAC,eAAA,OAAAD,iBAAA;IAAA,KACJE,cAAc,IAAAC,cAAA,GAAAC,CAAA,OAAgC;MAC7DC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE,KAAK;MACXC,OAAO,EAAE,OAAO;MAChBC,UAAU,EAAE;IACd,CAAC;IAAA,KAEOC,SAAS,IAAAP,cAAA,GAAAC,CAAA,OAAkB,IAAI;IAAA,KAC/BO,QAAQ,IAAAR,cAAA,GAAAC,CAAA,OAAwB,IAAIQ,GAAG,CAAC,CAAC;EAAA;EAAA,OAAAC,YAAA,CAAAb,iBAAA;IAAAc,GAAA;IAAAC,KAAA;MAAA,IAAAC,WAAA,GAAAC,iBAAA,CAKjD,aAAkC;QAAAd,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAC,CAAA;QAChC,IAAI;UAAAD,cAAA,GAAAC,CAAA;UAEF,IAAI,CAACM,SAAS,SAAS,IAAI,CAACS,YAAY,CAAC,CAAC;UAAChB,cAAA,GAAAC,CAAA;UAE3C,IAAIL,GAAG,CAACqB,GAAG,CAAC,YAAY,CAAC,EAAE;YAAAjB,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAC,CAAA;YACzBkB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;UAClD,CAAC;YAAApB,cAAA,GAAAkB,CAAA;UAAA;QACH,CAAC,CAAC,OAAOG,KAAK,EAAE;UAAArB,cAAA,GAAAC,CAAA;UACdkB,OAAO,CAACE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;UAACrB,cAAA,GAAAC,CAAA;UACjE,MAAM,IAAIqB,KAAK,CAAC,0CAA0C,CAAC;QAC7D;MACF,CAAC;MAAA,SAZKC,UAAUA,CAAA;QAAA,OAAAV,WAAA,CAAAW,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVF,UAAU;IAAA;EAAA;IAAAZ,GAAA;IAAAC,KAAA,EAiBhB,SAAAc,OAAOA,CACLC,IAAY,EACZC,QAAiB,EAEF;MAAA,IADfC,OAA0B,GAAAJ,SAAA,CAAAK,MAAA,QAAAL,SAAA,QAAAM,SAAA,GAAAN,SAAA,OAAAzB,cAAA,GAAAkB,CAAA,UAAG,CAAC,CAAC;MAAAlB,cAAA,GAAAe,CAAA;MAAAf,cAAA,GAAAC,CAAA;MAE/B,IAAI;QACF,IAAM+B,MAAM,IAAAhC,cAAA,GAAAC,CAAA,QAAAgC,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAACnC,cAAc,EAAK8B,OAAO,EAAE;QACrD,IAAMlB,GAAG,IAAAX,cAAA,GAAAC,CAAA,QAAG,CAAAD,cAAA,GAAAkB,CAAA,UAAAU,QAAQ,MAAA5B,cAAA,GAAAkB,CAAA,UAAI,IAAI,CAACX,SAAS;QAACP,cAAA,GAAAC,CAAA;QAEvC,IAAI,CAACU,GAAG,EAAE;UAAAX,cAAA,GAAAkB,CAAA;UAAAlB,cAAA,GAAAC,CAAA;UACR,MAAM,IAAIqB,KAAK,CAAC,6BAA6B,CAAC;QAChD,CAAC;UAAAtB,cAAA,GAAAkB,CAAA;QAAA;QAGD,IAAMiB,IAAI,IAAAnC,cAAA,GAAAC,CAAA,QAAGR,QAAQ,CAAC2C,GAAG,CAACC,SAAS,CAACC,MAAM,CAAC,GAAG,GAAC,CAAC,CAAC;QACjD,IAAMC,EAAE,IAAAvC,cAAA,GAAAC,CAAA,QAAGR,QAAQ,CAAC2C,GAAG,CAACC,SAAS,CAACC,MAAM,CAAC,GAAG,GAAC,CAAC,CAAC;QAG/C,IAAME,UAAU,IAAAxC,cAAA,GAAAC,CAAA,QAAGR,QAAQ,CAACgD,MAAM,CAAC9B,GAAG,EAAEwB,IAAI,EAAE;UAC5ChC,OAAO,EAAE6B,MAAM,CAAC7B,OAAO,GAAG,EAAE;UAC5BG,UAAU,EAAE0B,MAAM,CAAC1B;QACrB,CAAC,CAAC;QAGF,IAAMoC,SAAS,IAAA1C,cAAA,GAAAC,CAAA,QAAGR,QAAQ,CAACkD,GAAG,CAACjB,OAAO,CAACC,IAAI,EAAEa,UAAU,EAAE;UACvDD,EAAE,EAAEA,EAAE;UACNnC,IAAI,EAAEX,QAAQ,CAACW,IAAI,CAAC4B,MAAM,CAAC5B,IAAI,CAAC;UAChCC,OAAO,EAAEZ,QAAQ,CAACmD,GAAG,CAACZ,MAAM,CAAC3B,OAAO;QACtC,CAAC,CAAC;QAACL,cAAA,GAAAC,CAAA;QAEH,OAAO;UACL0B,IAAI,EAAEe,SAAS,CAACG,QAAQ,CAAC,CAAC;UAC1BN,EAAE,EAAEA,EAAE,CAACM,QAAQ,CAAC,CAAC;UACjBV,IAAI,EAAEA,IAAI,CAACU,QAAQ,CAAC,CAAC;UACrBC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACrB9C,SAAS,EAAE8B,MAAM,CAAC9B,SAAS;UAC3BC,OAAO,EAAE6B,MAAM,CAAC7B;QAClB,CAAC;MACH,CAAC,CAAC,OAAOkB,KAAK,EAAE;QAAArB,cAAA,GAAAC,CAAA;QACdkB,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAACrB,cAAA,GAAAC,CAAA;QAC3C,MAAM,IAAIqB,KAAK,CAAC,wBAAwB,CAAC;MAC3C;IACF;EAAC;IAAAX,GAAA;IAAAC,KAAA,EAKD,SAAAqC,OAAOA,CACLC,aAA4B,EAC5BtB,QAAiB,EACT;MAAA5B,cAAA,GAAAe,CAAA;MAAAf,cAAA,GAAAC,CAAA;MACR,IAAI;QACF,IAAMU,GAAG,IAAAX,cAAA,GAAAC,CAAA,QAAG,CAAAD,cAAA,GAAAkB,CAAA,UAAAU,QAAQ,MAAA5B,cAAA,GAAAkB,CAAA,UAAI,IAAI,CAACX,SAAS;QAACP,cAAA,GAAAC,CAAA;QAEvC,IAAI,CAACU,GAAG,EAAE;UAAAX,cAAA,GAAAkB,CAAA;UAAAlB,cAAA,GAAAC,CAAA;UACR,MAAM,IAAIqB,KAAK,CAAC,6BAA6B,CAAC;QAChD,CAAC;UAAAtB,cAAA,GAAAkB,CAAA;QAAA;QAGD,IAAMiB,IAAI,IAAAnC,cAAA,GAAAC,CAAA,QAAGR,QAAQ,CAAC0D,GAAG,CAACC,GAAG,CAACC,KAAK,CAACH,aAAa,CAACf,IAAI,CAAC;QACvD,IAAMI,EAAE,IAAAvC,cAAA,GAAAC,CAAA,QAAGR,QAAQ,CAAC0D,GAAG,CAACC,GAAG,CAACC,KAAK,CAACH,aAAa,CAACX,EAAE,CAAC;QAGnD,IAAMC,UAAU,IAAAxC,cAAA,GAAAC,CAAA,QAAGR,QAAQ,CAACgD,MAAM,CAAC9B,GAAG,EAAEwB,IAAI,EAAE;UAC5ChC,OAAO,EAAE+C,aAAa,CAAC/C,OAAO,GAAG,EAAE;UACnCG,UAAU,EAAE,IAAI,CAACP,cAAc,CAACO;QAClC,CAAC,CAAC;QAGF,IAAMgD,SAAS,IAAAtD,cAAA,GAAAC,CAAA,QAAGR,QAAQ,CAACkD,GAAG,CAACM,OAAO,CAACC,aAAa,CAACvB,IAAI,EAAEa,UAAU,EAAE;UACrED,EAAE,EAAEA,EAAE;UACNnC,IAAI,EAAEX,QAAQ,CAACW,IAAI,CAACmD,GAAG;UACvBlD,OAAO,EAAEZ,QAAQ,CAACmD,GAAG,CAACY;QACxB,CAAC,CAAC;QAEF,IAAMC,aAAa,IAAAzD,cAAA,GAAAC,CAAA,QAAGqD,SAAS,CAACT,QAAQ,CAACpD,QAAQ,CAAC0D,GAAG,CAACO,IAAI,CAAC;QAAC1D,cAAA,GAAAC,CAAA;QAE5D,IAAI,CAACwD,aAAa,EAAE;UAAAzD,cAAA,GAAAkB,CAAA;UAAAlB,cAAA,GAAAC,CAAA;UAClB,MAAM,IAAIqB,KAAK,CAAC,mCAAmC,CAAC;QACtD,CAAC;UAAAtB,cAAA,GAAAkB,CAAA;QAAA;QAAAlB,cAAA,GAAAC,CAAA;QAED,OAAOwD,aAAa;MACtB,CAAC,CAAC,OAAOpC,KAAK,EAAE;QAAArB,cAAA,GAAAC,CAAA;QACdkB,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAACrB,cAAA,GAAAC,CAAA;QAC3C,MAAM,IAAIqB,KAAK,CAAC,wBAAwB,CAAC;MAC3C;IACF;EAAC;IAAAX,GAAA;IAAAC,KAAA,EAKD,SAAA+C,IAAIA,CAAChC,IAAY,EAA6D;MAAA,IAA3DzB,SAAsC,GAAAuB,SAAA,CAAAK,MAAA,QAAAL,SAAA,QAAAM,SAAA,GAAAN,SAAA,OAAAzB,cAAA,GAAAkB,CAAA,UAAG,QAAQ;MAAAlB,cAAA,GAAAe,CAAA;MAAAf,cAAA,GAAAC,CAAA;MAClE,IAAI;QAAAD,cAAA,GAAAC,CAAA;QACF,QAAQC,SAAS;UACf,KAAK,QAAQ;YAAAF,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAC,CAAA;YACX,OAAOR,QAAQ,CAACmE,MAAM,CAACjC,IAAI,CAAC,CAACkB,QAAQ,CAAC,CAAC;UACzC,KAAK,QAAQ;YAAA7C,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAC,CAAA;YACX,OAAOR,QAAQ,CAACoE,MAAM,CAAClC,IAAI,CAAC,CAACkB,QAAQ,CAAC,CAAC;UACzC,KAAK,KAAK;YAAA7C,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAC,CAAA;YACR,OAAOR,QAAQ,CAACqE,GAAG,CAACnC,IAAI,CAAC,CAACkB,QAAQ,CAAC,CAAC;UACtC;YAAA7C,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAC,CAAA;YACE,MAAM,IAAIqB,KAAK,CAAC,+BAA+BpB,SAAS,EAAE,CAAC;QAC/D;MACF,CAAC,CAAC,OAAOmB,KAAK,EAAE;QAAArB,cAAA,GAAAC,CAAA;QACdkB,OAAO,CAACE,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;QAACrB,cAAA,GAAAC,CAAA;QACxC,MAAM,IAAIqB,KAAK,CAAC,qBAAqB,CAAC;MACxC;IACF;EAAC;IAAAX,GAAA;IAAAC,KAAA,EAKD,SAAAmD,WAAWA,CAAA,EAA8B;MAAA,IAA7BjC,MAAc,GAAAL,SAAA,CAAAK,MAAA,QAAAL,SAAA,QAAAM,SAAA,GAAAN,SAAA,OAAAzB,cAAA,GAAAkB,CAAA,UAAG,EAAE;MAAAlB,cAAA,GAAAe,CAAA;MAAAf,cAAA,GAAAC,CAAA;MAC7B,OAAOR,QAAQ,CAAC2C,GAAG,CAACC,SAAS,CAACC,MAAM,CAACR,MAAM,CAAC,CAACe,QAAQ,CAAC,CAAC;IACzD;EAAC;IAAAlC,GAAA;IAAAC,KAAA;MAAA,IAAAoD,YAAA,GAAAlD,iBAAA,CAKD,WACEH,GAAW,EACXC,KAAa,EAEE;QAAA,IADfiB,OAA6B,GAAAJ,SAAA,CAAAK,MAAA,QAAAL,SAAA,QAAAM,SAAA,GAAAN,SAAA,OAAAzB,cAAA,GAAAkB,CAAA,WAAG,CAAC,CAAC;QAAAlB,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAC,CAAA;QAElC,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACF,IAAIN,QAAQ,CAACsE,EAAE,KAAK,KAAK,EAAE;YAAAjE,cAAA,GAAAkB,CAAA;YAEzB,IAAMwB,SAAS,IAAA1C,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACyB,OAAO,CAACd,KAAK,CAAC;YAACZ,cAAA,GAAAC,CAAA;YACtCiE,YAAY,CAACC,OAAO,CAACxD,GAAG,EAAEyD,IAAI,CAACC,SAAS,CAAC3B,SAAS,CAAC,CAAC;YAAC1C,cAAA,GAAAC,CAAA;YACrD;UACF,CAAC;YAAAD,cAAA,GAAAkB,CAAA;UAAA;UAED,IAAMoD,YAA4C,IAAAtE,cAAA,GAAAC,CAAA,QAAG;YACnDsE,qBAAqB,EAAE,CAAAvE,cAAA,GAAAkB,CAAA,WAAAW,OAAO,CAAC0C,qBAAqB,MAAAvE,cAAA,GAAAkB,CAAA,WAAI,KAAK;YAC7DsD,eAAe,EAAE,CAAAxE,cAAA,GAAAkB,CAAA,WAAAW,OAAO,CAAC2C,eAAe,MAAAxE,cAAA,GAAAkB,CAAA,WAAI,oBAAoB;UAClE,CAAC;UAAClB,cAAA,GAAAC,CAAA;UAEF,IAAI,CAAAD,cAAA,GAAAkB,CAAA,WAAAvB,QAAQ,CAACsE,EAAE,KAAK,KAAK,MAAAjE,cAAA,GAAAkB,CAAA,WAAIW,OAAO,CAAC4C,WAAW,GAAE;YAAAzE,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAC,CAAA;YAC/CqE,YAAY,CAASG,WAAW,GAAG5C,OAAO,CAAC4C,WAAW;UACzD,CAAC;YAAAzE,cAAA,GAAAkB,CAAA;UAAA;UAAAlB,cAAA,GAAAC,CAAA;UAED,MAAMP,WAAW,CAACgF,YAAY,CAAC/D,GAAG,EAAEC,KAAK,EAAE0D,YAAY,CAAC;QAC1D,CAAC,CAAC,OAAOjD,KAAK,EAAE;UAAArB,cAAA,GAAAC,CAAA;UACdkB,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAACrB,cAAA,GAAAC,CAAA;UAC7C,MAAM,IAAIqB,KAAK,CAAC,+BAA+B,CAAC;QAClD;MACF,CAAC;MAAA,SA3BKqD,WAAWA,CAAAC,EAAA,EAAAC,GAAA;QAAA,OAAAb,YAAA,CAAAxC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAXkD,WAAW;IAAA;EAAA;IAAAhE,GAAA;IAAAC,KAAA;MAAA,IAAAkE,eAAA,GAAAhE,iBAAA,CAgCjB,WACEH,GAAW,EAEa;QAAA,IADxBkB,OAA6B,GAAAJ,SAAA,CAAAK,MAAA,QAAAL,SAAA,QAAAM,SAAA,GAAAN,SAAA,OAAAzB,cAAA,GAAAkB,CAAA,WAAG,CAAC,CAAC;QAAAlB,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAC,CAAA;QAElC,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACF,IAAIN,QAAQ,CAACsE,EAAE,KAAK,KAAK,EAAE;YAAAjE,cAAA,GAAAkB,CAAA;YAEzB,IAAM6D,MAAM,IAAA/E,cAAA,GAAAC,CAAA,QAAGiE,YAAY,CAACc,OAAO,CAACrE,GAAG,CAAC;YAACX,cAAA,GAAAC,CAAA;YACzC,IAAI,CAAC8E,MAAM,EAAE;cAAA/E,cAAA,GAAAkB,CAAA;cAAAlB,cAAA,GAAAC,CAAA;cAAA,OAAO,IAAI;YAAA,CAAC;cAAAD,cAAA,GAAAkB,CAAA;YAAA;YAEzB,IAAMwB,SAAS,IAAA1C,cAAA,GAAAC,CAAA,QAAGmE,IAAI,CAACf,KAAK,CAAC0B,MAAM,CAAC,CAAiB;YAAC/E,cAAA,GAAAC,CAAA;YACtD,OAAO,IAAI,CAACgD,OAAO,CAACP,SAAS,CAAC;UAChC,CAAC;YAAA1C,cAAA,GAAAkB,CAAA;UAAA;UAED,IAAMoD,YAA4C,IAAAtE,cAAA,GAAAC,CAAA,QAAG;YACnDsE,qBAAqB,EAAE,CAAAvE,cAAA,GAAAkB,CAAA,WAAAW,OAAO,CAAC0C,qBAAqB,MAAAvE,cAAA,GAAAkB,CAAA,WAAI,KAAK;YAC7DsD,eAAe,EAAE,CAAAxE,cAAA,GAAAkB,CAAA,WAAAW,OAAO,CAAC2C,eAAe,MAAAxE,cAAA,GAAAkB,CAAA,WAAI,oBAAoB;UAClE,CAAC;UAAClB,cAAA,GAAAC,CAAA;UAEF,IAAI,CAAAD,cAAA,GAAAkB,CAAA,WAAAvB,QAAQ,CAACsE,EAAE,KAAK,KAAK,MAAAjE,cAAA,GAAAkB,CAAA,WAAIW,OAAO,CAAC4C,WAAW,GAAE;YAAAzE,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAC,CAAA;YAC/CqE,YAAY,CAASG,WAAW,GAAG5C,OAAO,CAAC4C,WAAW;UACzD,CAAC;YAAAzE,cAAA,GAAAkB,CAAA;UAAA;UAAAlB,cAAA,GAAAC,CAAA;UAED,aAAaP,WAAW,CAACuF,YAAY,CAACtE,GAAG,EAAE2D,YAAY,CAAC;QAC1D,CAAC,CAAC,OAAOjD,KAAK,EAAE;UAAArB,cAAA,GAAAC,CAAA;UACdkB,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAACrB,cAAA,GAAAC,CAAA;UAChD,OAAO,IAAI;QACb;MACF,CAAC;MAAA,SA5BKiF,cAAcA,CAAAC,GAAA;QAAA,OAAAL,eAAA,CAAAtD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdyD,cAAc;IAAA;EAAA;IAAAvE,GAAA;IAAAC,KAAA;MAAA,IAAAwE,aAAA,GAAAtE,iBAAA,CAiCpB,WACEH,GAAW,EAEI;QAAA,IADfkB,OAA6B,GAAAJ,SAAA,CAAAK,MAAA,QAAAL,SAAA,QAAAM,SAAA,GAAAN,SAAA,OAAAzB,cAAA,GAAAkB,CAAA,WAAG,CAAC,CAAC;QAAAlB,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAC,CAAA;QAElC,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACF,IAAIN,QAAQ,CAACsE,EAAE,KAAK,KAAK,EAAE;YAAAjE,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAC,CAAA;YACzBiE,YAAY,CAACmB,UAAU,CAAC1E,GAAG,CAAC;YAACX,cAAA,GAAAC,CAAA;YAC7B;UACF,CAAC;YAAAD,cAAA,GAAAkB,CAAA;UAAA;UAED,IAAMoD,YAA4C,IAAAtE,cAAA,GAAAC,CAAA,QAAG;YACnDsE,qBAAqB,EAAE,CAAAvE,cAAA,GAAAkB,CAAA,WAAAW,OAAO,CAAC0C,qBAAqB,MAAAvE,cAAA,GAAAkB,CAAA,WAAI,KAAK;YAC7DsD,eAAe,EAAE,CAAAxE,cAAA,GAAAkB,CAAA,WAAAW,OAAO,CAAC2C,eAAe,MAAAxE,cAAA,GAAAkB,CAAA,WAAI,oBAAoB;UAClE,CAAC;UAAClB,cAAA,GAAAC,CAAA;UAEF,MAAMP,WAAW,CAAC4F,eAAe,CAAC3E,GAAG,EAAE2D,YAAY,CAAC;QACtD,CAAC,CAAC,OAAOjD,KAAK,EAAE;UAAArB,cAAA,GAAAC,CAAA;UACdkB,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAACrB,cAAA,GAAAC,CAAA;UAC9C,MAAM,IAAIqB,KAAK,CAAC,8BAA8B,CAAC;QACjD;MACF,CAAC;MAAA,SApBKiE,YAAYA,CAAAC,GAAA;QAAA,OAAAJ,aAAA,CAAA5D,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAZ8D,YAAY;IAAA;EAAA;IAAA5E,GAAA;IAAAC,KAAA,EAyBlB,SAAA6E,kBAAkBA,CAACC,OAAY,EAAiB;MAAA,IAAAC,KAAA;MAAA3F,cAAA,GAAAe,CAAA;MAC9C,IAAM6E,eAAe,IAAA5F,cAAA,GAAAC,CAAA,QAAG,CACtB,OAAO,EACP,OAAO,EACP,SAAS,EACT,WAAW,EACX,kBAAkB,CACnB;MAED,IAAM4F,WAAW,IAAA7F,cAAA,GAAAC,CAAA,QAAAgC,MAAA,CAAAC,MAAA,KAAQwD,OAAO,EAAE;MAAC1F,cAAA,GAAAC,CAAA;MAGnC2F,eAAe,CAACE,OAAO,CAAC,UAAAC,KAAK,EAAI;QAAA/F,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAC,CAAA;QAC/B,IAAI4F,WAAW,CAACE,KAAK,CAAC,EAAE;UAAA/F,cAAA,GAAAkB,CAAA;UAAAlB,cAAA,GAAAC,CAAA;UACtB4F,WAAW,CAACE,KAAK,CAAC,GAAGJ,KAAI,CAACjE,OAAO,CAACmE,WAAW,CAACE,KAAK,CAAC,CAAClD,QAAQ,CAAC,CAAC,CAAC;QAClE,CAAC;UAAA7C,cAAA,GAAAkB,CAAA;QAAA;MACH,CAAC,CAAC;MAAClB,cAAA,GAAAC,CAAA;MAEH,OAAO,IAAI,CAACyB,OAAO,CAAC0C,IAAI,CAACC,SAAS,CAACwB,WAAW,CAAC,CAAC;IAClD;EAAC;IAAAlF,GAAA;IAAAC,KAAA,EAKD,SAAAoF,kBAAkBA,CAACC,gBAA+B,EAAO;MAAA,IAAAC,MAAA;MAAAlG,cAAA,GAAAe,CAAA;MAAAf,cAAA,GAAAC,CAAA;MACvD,IAAI;QACF,IAAMkG,aAAa,IAAAnG,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACgD,OAAO,CAACgD,gBAAgB,CAAC;QACpD,IAAMP,OAAO,IAAA1F,cAAA,GAAAC,CAAA,QAAGmE,IAAI,CAACf,KAAK,CAAC8C,aAAa,CAAC;QAEzC,IAAMP,eAAe,IAAA5F,cAAA,GAAAC,CAAA,QAAG,CACtB,OAAO,EACP,OAAO,EACP,SAAS,EACT,WAAW,EACX,kBAAkB,CACnB;QAACD,cAAA,GAAAC,CAAA;QAGF2F,eAAe,CAACE,OAAO,CAAC,UAAAC,KAAK,EAAI;UAAA/F,cAAA,GAAAe,CAAA;UAAAf,cAAA,GAAAC,CAAA;UAC/B,IAAI,CAAAD,cAAA,GAAAkB,CAAA,WAAAwE,OAAO,CAACK,KAAK,CAAC,MAAA/F,cAAA,GAAAkB,CAAA,WAAI,OAAOwE,OAAO,CAACK,KAAK,CAAC,KAAK,QAAQ,GAAE;YAAA/F,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAC,CAAA;YACxDyF,OAAO,CAACK,KAAK,CAAC,GAAGG,MAAI,CAACjD,OAAO,CAACyC,OAAO,CAACK,KAAK,CAAC,CAAC;UAC/C,CAAC;YAAA/F,cAAA,GAAAkB,CAAA;UAAA;QACH,CAAC,CAAC;QAAClB,cAAA,GAAAC,CAAA;QAEH,OAAOyF,OAAO;MAChB,CAAC,CAAC,OAAOrE,KAAK,EAAE;QAAArB,cAAA,GAAAC,CAAA;QACdkB,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAACrB,cAAA,GAAAC,CAAA;QACnD,MAAM,IAAIqB,KAAK,CAAC,gCAAgC,CAAC;MACnD;IACF;EAAC;IAAAX,GAAA;IAAAC,KAAA;MAAA,IAAAwF,aAAA,GAAAtF,iBAAA,CAKD,aAA8C;QAAAd,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAC,CAAA;QAC5C,IAAI;UAEF,IAAIM,SAAS,IAAAP,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACiF,cAAc,CAAC,YAAY,CAAC;UAAClF,cAAA,GAAAC,CAAA;UAExD,IAAI,CAACM,SAAS,EAAE;YAAAP,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAC,CAAA;YAEdM,SAAS,GAAG,IAAI,CAACwD,WAAW,CAAC,EAAE,CAAC;YAAC/D,cAAA,GAAAC,CAAA;YACjC,MAAM,IAAI,CAAC0E,WAAW,CAAC,YAAY,EAAEpE,SAAS,EAAE;cAC9CgE,qBAAqB,EAAE;YACzB,CAAC,CAAC;UACJ,CAAC;YAAAvE,cAAA,GAAAkB,CAAA;UAAA;UAAAlB,cAAA,GAAAC,CAAA;UAED,OAAOM,SAAS;QAClB,CAAC,CAAC,OAAOc,KAAK,EAAE;UAAArB,cAAA,GAAAC,CAAA;UACdkB,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UAACrB,cAAA,GAAAC,CAAA;UACtD,MAAM,IAAIqB,KAAK,CAAC,iCAAiC,CAAC;QACpD;MACF,CAAC;MAAA,SAlBaN,YAAYA,CAAA;QAAA,OAAAoF,aAAA,CAAA5E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAZT,YAAY;IAAA;EAAA;IAAAL,GAAA;IAAAC,KAAA;MAAA,IAAAyF,WAAA,GAAAvF,iBAAA,CAuB1B,aAAkC;QAAAd,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAC,CAAA;QAChC,IAAI;UAEF,IAAMqG,YAAY,IAAAtG,cAAA,GAAAC,CAAA,SAAG,IAAI,CAAC8D,WAAW,CAAC,EAAE,CAAC;UAAC/D,cAAA,GAAAC,CAAA;UAG1C,MAAM,IAAI,CAAC0E,WAAW,CAAC,YAAY,EAAE2B,YAAY,EAAE;YACjD/B,qBAAqB,EAAE;UACzB,CAAC,CAAC;UAACvE,cAAA,GAAAC,CAAA;UAGH,IAAI,CAACM,SAAS,GAAG+F,YAAY;UAACtG,cAAA,GAAAC,CAAA;UAC9B,IAAI,CAACO,QAAQ,CAAC+F,KAAK,CAAC,CAAC;UAACvG,cAAA,GAAAC,CAAA;UAEtB,IAAIL,GAAG,CAACqB,GAAG,CAAC,YAAY,CAAC,EAAE;YAAAjB,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAC,CAAA;YACzBkB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;UACxD,CAAC;YAAApB,cAAA,GAAAkB,CAAA;UAAA;QACH,CAAC,CAAC,OAAOG,KAAK,EAAE;UAAArB,cAAA,GAAAC,CAAA;UACdkB,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAACrB,cAAA,GAAAC,CAAA;UAC7C,MAAM,IAAIqB,KAAK,CAAC,kCAAkC,CAAC;QACrD;MACF,CAAC;MAAA,SArBKkF,UAAUA,CAAA;QAAA,OAAAH,WAAA,CAAA7E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAV+E,UAAU;IAAA;EAAA;IAAA7F,GAAA;IAAAC,KAAA;MAAA,IAAA6F,SAAA,GAAA3F,iBAAA,CA0BhB,aAAgC;QAAAd,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAC,CAAA;QAC9B,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACF,MAAM,IAAI,CAACsF,YAAY,CAAC,YAAY,CAAC;UAACvF,cAAA,GAAAC,CAAA;UACtC,IAAI,CAACM,SAAS,GAAG,IAAI;UAACP,cAAA,GAAAC,CAAA;UACtB,IAAI,CAACO,QAAQ,CAAC+F,KAAK,CAAC,CAAC;UAACvG,cAAA,GAAAC,CAAA;UAEtB,IAAIN,QAAQ,CAACsE,EAAE,KAAK,KAAK,EAAE;YAAAjE,cAAA,GAAAkB,CAAA;YAEzB,IAAMwF,YAAY,IAAA1G,cAAA,GAAAC,CAAA,SAAG,EAAE;YAACD,cAAA,GAAAC,CAAA;YACxB,KAAK,IAAI0G,CAAC,IAAA3G,cAAA,GAAAC,CAAA,SAAG,CAAC,GAAE0G,CAAC,GAAGzC,YAAY,CAACpC,MAAM,EAAE6E,CAAC,EAAE,EAAE;cAC5C,IAAMhG,GAAG,IAAAX,cAAA,GAAAC,CAAA,SAAGiE,YAAY,CAACvD,GAAG,CAACgG,CAAC,CAAC;cAAC3G,cAAA,GAAAC,CAAA;cAChC,IAAI,CAAAD,cAAA,GAAAkB,CAAA,WAAAP,GAAG,MAAAX,cAAA,GAAAkB,CAAA,WAAIP,GAAG,CAACiG,UAAU,CAAC,YAAY,CAAC,GAAE;gBAAA5G,cAAA,GAAAkB,CAAA;gBAAAlB,cAAA,GAAAC,CAAA;gBACvCyG,YAAY,CAACG,IAAI,CAAClG,GAAG,CAAC;cACxB,CAAC;gBAAAX,cAAA,GAAAkB,CAAA;cAAA;YACH;YAAClB,cAAA,GAAAC,CAAA;YACDyG,YAAY,CAACZ,OAAO,CAAC,UAAAnF,GAAG,EAAI;cAAAX,cAAA,GAAAe,CAAA;cAAAf,cAAA,GAAAC,CAAA;cAAA,OAAAiE,YAAY,CAACmB,UAAU,CAAC1E,GAAG,CAAC;YAAD,CAAC,CAAC;UAC3D,CAAC;YAAAX,cAAA,GAAAkB,CAAA;UAAA;UAAAlB,cAAA,GAAAC,CAAA;UAED,IAAIL,GAAG,CAACqB,GAAG,CAAC,YAAY,CAAC,EAAE;YAAAjB,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAC,CAAA;YACzBkB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAChD,CAAC;YAAApB,cAAA,GAAAkB,CAAA;UAAA;QACH,CAAC,CAAC,OAAOG,KAAK,EAAE;UAAArB,cAAA,GAAAC,CAAA;UACdkB,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UAACrB,cAAA,GAAAC,CAAA;UACtD,MAAM,IAAIqB,KAAK,CAAC,iCAAiC,CAAC;QACpD;MACF,CAAC;MAAA,SAzBKwF,QAAQA,CAAA;QAAA,OAAAL,SAAA,CAAAjF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAARqF,QAAQ;IAAA;EAAA;IAAAnG,GAAA;IAAAC,KAAA,EA8Bd,SAAAmG,iBAAiBA,CAAC7D,aAA4B,EAAW;MAAAlD,cAAA,GAAAe,CAAA;MAAAf,cAAA,GAAAC,CAAA;MACvD,IAAI;QAEF,IAAM+G,cAAc,IAAAhH,cAAA,GAAAC,CAAA,SAAG,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,CAAC;QAClF,IAAMgH,YAAY,IAAAjH,cAAA,GAAAC,CAAA,SAAG+G,cAAc,CAACE,KAAK,CAAC,UAAAnB,KAAK,EAC7C;UAAA/F,cAAA,GAAAe,CAAA;UAAAf,cAAA,GAAAC,CAAA;UAAA,QAAAD,cAAA,GAAAkB,CAAA,WAAAgC,aAAa,CAACiE,cAAc,CAACpB,KAAK,CAAC,MAAA/F,cAAA,GAAAkB,CAAA,WAAKgC,aAAa,CAAS6C,KAAK,CAAC,KAAK,IAAI;QAAD,CAC9E,CAAC;QAAC/F,cAAA,GAAAC,CAAA;QAEF,IAAI,CAACgH,YAAY,EAAE;UAAAjH,cAAA,GAAAkB,CAAA;UAAAlB,cAAA,GAAAC,CAAA;UACjB,OAAO,KAAK;QACd,CAAC;UAAAD,cAAA,GAAAkB,CAAA;QAAA;QAGD,IAAMkG,MAAM,IAAApH,cAAA,GAAAC,CAAA,SAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QACvC,IAAMoH,YAAY,IAAArH,cAAA,GAAAC,CAAA,SAAI8C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGE,aAAa,CAACJ,SAAS,GAAIsE,MAAM;QAACpH,cAAA,GAAAC,CAAA;QAErE,OAAOoH,YAAY;MACrB,CAAC,CAAC,OAAOhG,KAAK,EAAE;QAAArB,cAAA,GAAAC,CAAA;QACdkB,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QAACrB,cAAA,GAAAC,CAAA;QACrD,OAAO,KAAK;MACd;IACF;EAAC;AAAA;AAIH,OAAO,IAAMqH,iBAAiB,IAAAtH,cAAA,GAAAC,CAAA,SAAG,IAAIJ,iBAAiB,CAAC,CAAC;AAACG,cAAA,GAAAC,CAAA;AAGzD,OAAO,IAAMyB,OAAO,GAAG,SAAVA,OAAOA,CAAIC,IAAY,EAAEC,QAAiB,EAAEC,OAA2B,EAClF;EAAA7B,cAAA,GAAAe,CAAA;EAAAf,cAAA,GAAAC,CAAA;EAAA,OAAAqH,iBAAiB,CAAC5F,OAAO,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;AAAD,CAAC;AAAC7B,cAAA,GAAAC,CAAA;AAErD,OAAO,IAAMgD,OAAO,GAAG,SAAVA,OAAOA,CAAIC,aAA4B,EAAEtB,QAAiB,EACrE;EAAA5B,cAAA,GAAAe,CAAA;EAAAf,cAAA,GAAAC,CAAA;EAAA,OAAAqH,iBAAiB,CAACrE,OAAO,CAACC,aAAa,EAAEtB,QAAQ,CAAC;AAAD,CAAC;AAAC5B,cAAA,GAAAC,CAAA;AAErD,OAAO,IAAM0D,IAAI,GAAG,SAAPA,IAAIA,CAAIhC,IAAY,EAAEzB,SAAuC,EACxE;EAAAF,cAAA,GAAAe,CAAA;EAAAf,cAAA,GAAAC,CAAA;EAAA,OAAAqH,iBAAiB,CAAC3D,IAAI,CAAChC,IAAI,EAAEzB,SAAS,CAAC;AAAD,CAAC;AAACF,cAAA,GAAAC,CAAA;AAE1C,OAAO,IAAM0E,WAAW,GAAG,SAAdA,WAAWA,CAAIhE,GAAW,EAAEC,KAAa,EAAEiB,OAA8B,EACpF;EAAA7B,cAAA,GAAAe,CAAA;EAAAf,cAAA,GAAAC,CAAA;EAAA,OAAAqH,iBAAiB,CAAC3C,WAAW,CAAChE,GAAG,EAAEC,KAAK,EAAEiB,OAAO,CAAC;AAAD,CAAC;AAAC7B,cAAA,GAAAC,CAAA;AAErD,OAAO,IAAMiF,cAAc,GAAG,SAAjBA,cAAcA,CAAIvE,GAAW,EAAEkB,OAA8B,EACxE;EAAA7B,cAAA,GAAAe,CAAA;EAAAf,cAAA,GAAAC,CAAA;EAAA,OAAAqH,iBAAiB,CAACpC,cAAc,CAACvE,GAAG,EAAEkB,OAAO,CAAC;AAAD,CAAC;AAAC7B,cAAA,GAAAC,CAAA;AAEjD,OAAO,IAAMsF,YAAY,GAAG,SAAfA,YAAYA,CAAI5E,GAAW,EAAEkB,OAA8B,EACtE;EAAA7B,cAAA,GAAAe,CAAA;EAAAf,cAAA,GAAAC,CAAA;EAAA,OAAAqH,iBAAiB,CAAC/B,YAAY,CAAC5E,GAAG,EAAEkB,OAAO,CAAC;AAAD,CAAC;AAE9C,eAAeyF,iBAAiB", "ignoreList": []}