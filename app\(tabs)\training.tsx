import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  SafeAreaView, 
  TouchableOpacity, 
  Alert,
  Image,
  Platform
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LoadingState from '@/components/ui/LoadingState';
import ErrorState from '@/components/ui/ErrorState';
import EmptyState from '@/components/ui/EmptyState';
import { useVideoAnalysis } from '@/hooks/useVideoAnalysis';
import { Upload, Video, Play, Target, Camera, FileText, TrendingUp, Clock, CircleCheck as CheckCircle, Star, Zap, RotateCcw, Share2, Eye, Award, ChevronRight, Settings } from 'lucide-react-native';
import PremiumGate from '@/components/subscription/PremiumGate';

const colors = {
  primary: '#23ba16',
  yellow: '#ffe600',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  red: '#ef4444',
  blue: '#3b82f6',
};

export default function TrainingScreen() {
  const { 
    uploadProgress, 
    isAnalyzing, 
    analysisComplete, 
    analysisResults,
    error,
    uploadVideo,
    resetAnalysis,
    saveSession
  } = useVideoAnalysis();

  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);

  const handleVideoUpload = () => {
    if (Platform.OS === 'web') {
      // Web file picker simulation
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'video/*';
      input.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          uploadVideo(file, file.name, 'camera_roll');
        }
      };
      input.click();
    } else {
      // Mobile options
      Alert.alert(
        'Upload Video',
        'Choose video source:',
        [
          { text: 'Camera Roll', onPress: () => uploadVideo(new Blob(), 'practice_video.mp4', 'camera_roll') },
          { text: 'Record New', onPress: () => uploadVideo(new Blob(), 'new_recording.mp4', 'camera') },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    }
  };

  const handleSaveSession = async () => {
    if (!analysisResults) return;
    
    try {
      await saveSession();
      Alert.alert(
        'Session Saved',
        'Your training session has been saved to your history.',
        [
          { text: 'View Progress', onPress: () => router.push('/(tabs)/progress') },
          { text: 'OK' },
        ]
      );
    } catch (err) {
      Alert.alert('Error', 'Failed to save session. Please try again.');
    }
  };

  const handleStartNewAnalysis = () => {
    resetAnalysis();
  };

  const handleViewDrill = (drillId: string) => {
    Alert.alert('Drill Details', `Opening drill instructions for: ${drillId}`);
  };

  const handleShareResults = () => {
    if (!analysisResults) return;
    Alert.alert('Share Results', 'Analysis results shared with your coach!');
  };

  if (error) {
    return (
      <ErrorState 
        message={error}
        onRetry={resetAnalysis}
        retryText="Try Again"
      />
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Video Analysis</Text>
          <Text style={styles.subtitle}>AI-powered technique feedback</Text>
        </View>

        {/* Video Upload Section */}
        {!analysisComplete && (
          <PremiumGate
            featureId="video_analysis"
            customMessage="Unlock AI-powered video analysis to get instant feedback on your technique"
          >
            <Card variant="elevated" style={styles.uploadCard}>
              <View style={styles.uploadContent}>
                <View style={styles.uploadIcon}>
                  <Upload size={32} color={colors.primary} />
                </View>
                <Text style={styles.uploadTitle}>Upload Training Video</Text>
                <Text style={styles.uploadDescription}>
                  Get instant AI feedback on your technique, footwork, and strategy
                </Text>
              
              {uploadProgress > 0 && uploadProgress < 100 && (
                <View style={styles.progressContainer}>
                  <View style={styles.progressBar}>
                    <View style={[styles.progressFill, { width: `${uploadProgress}%` }]} />
                  </View>
                  <Text style={styles.progressText}>{uploadProgress}% uploaded</Text>
                </View>
              )}
              
              {isAnalyzing && (
                <View style={styles.analyzingContainer}>
                  <View style={styles.analyzingIcon}>
                    <TrendingUp size={24} color={colors.primary} />
                  </View>
                  <Text style={styles.analyzingText}>AI is analyzing your video...</Text>
                  <Text style={styles.analyzingSubtext}>
                    Examining technique, footwork, and shot placement
                  </Text>
                </View>
              )}
              
              {!isAnalyzing && uploadProgress === 0 && (
                <Button
                  title="Choose Video"
                  onPress={handleVideoUpload}
                  style={styles.uploadButton}
                />
              )}
            </View>
          </Card>
          </PremiumGate>
        )}

        {/* Analysis Results */}
        {analysisComplete && analysisResults && (
          <>
            {/* Overall Score */}
            <Card variant="elevated" style={styles.resultsCard}>
              <View style={styles.resultsHeader}>
                <CheckCircle size={24} color={colors.primary} />
                <Text style={styles.resultsTitle}>Analysis Complete</Text>
                <TouchableOpacity onPress={handleShareResults} style={styles.shareButton}>
                  <Share2 size={20} color={colors.primary} />
                </TouchableOpacity>
              </View>
              
              <View style={styles.overallScore}>
                <Text style={styles.scoreLabel}>Overall Performance</Text>
                <Text style={styles.scoreValue}>{analysisResults.overallScore}/100</Text>
                <View style={styles.scoreRing}>
                  <View 
                    style={[
                      styles.scoreProgress, 
                      { 
                        transform: [{ 
                          rotate: `${(analysisResults.overallScore / 100) * 360}deg` 
                        }] 
                      }
                    ]} 
                  />
                </View>
              </View>
            </Card>

            {/* Video Highlights */}
            <Card variant="elevated" style={styles.highlightsCard}>
              <Text style={styles.sectionTitle}>Video Highlights</Text>
              <Text style={styles.sectionSubtitle}>
                Key moments identified by AI analysis
              </Text>
              
              {analysisResults.videoHighlights.map((highlight) => (
                <TouchableOpacity 
                  key={highlight.id} 
                  style={styles.highlightItem}
                  onPress={() => setSelectedVideo(highlight.id)}
                >
                  <Image 
                    source={{ uri: highlight.thumbnail }} 
                    style={styles.highlightThumbnail}
                  />
                  <View style={styles.highlightContent}>
                    <Text style={styles.highlightTitle}>{highlight.title}</Text>
                    <Text style={styles.highlightDescription}>{highlight.description}</Text>
                    <View style={styles.highlightMeta}>
                      <Clock size={12} color={colors.gray} />
                      <Text style={styles.highlightTime}>{highlight.timestamp}</Text>
                      <View style={[
                        styles.highlightType,
                        { backgroundColor: highlight.type === 'positive' ? colors.primary : colors.yellow }
                      ]}>
                        <Text style={styles.highlightTypeText}>
                          {highlight.type === 'positive' ? 'Good' : 'Improve'}
                        </Text>
                      </View>
                    </View>
                  </View>
                  <Eye size={20} color={colors.gray} />
                </TouchableOpacity>
              ))}
            </Card>

            {/* Technique Ratings */}
            <Card variant="elevated" style={styles.ratingsCard}>
              <Text style={styles.sectionTitle}>Technique Ratings</Text>
              <Text style={styles.sectionSubtitle}>
                Detailed breakdown of your performance
              </Text>
              
              {analysisResults.techniqueRatings.map((rating) => (
                <View key={rating.skill} style={styles.ratingItem}>
                  <View style={styles.ratingHeader}>
                    <Text style={styles.ratingSkill}>{rating.skill}</Text>
                    <Text style={styles.ratingScore}>{rating.score}/100</Text>
                  </View>
                  <View style={styles.ratingBar}>
                    <View 
                      style={[
                        styles.ratingFill, 
                        { 
                          width: `${rating.score}%`,
                          backgroundColor: rating.score >= 80 ? colors.primary : 
                                         rating.score >= 60 ? colors.yellow : colors.red
                        }
                      ]} 
                    />
                  </View>
                  <Text style={styles.ratingFeedback}>{rating.feedback}</Text>
                </View>
              ))}
            </Card>

            {/* AI Feedback */}
            <PremiumGate
              featureId="ai_coaching"
              customMessage="Get personalized AI coaching feedback on your performance"
            >
              <Card variant="elevated" style={styles.feedbackCard}>
                <Text style={styles.sectionTitle}>AI Coach Feedback</Text>
              
              {analysisResults.aiFeedback.map((feedback, index) => (
                <View key={index} style={styles.feedbackItem}>
                  <View style={styles.feedbackHeader}>
                    <View style={[
                      styles.feedbackIcon,
                      { backgroundColor: feedback.type === 'positive' ? colors.primary : colors.yellow }
                    ]}>
                      {feedback.type === 'positive' ? (
                        <CheckCircle size={16} color={colors.white} />
                      ) : (
                        <Target size={16} color={colors.white} />
                      )}
                    </View>
                    <Text style={styles.feedbackArea}>{feedback.area}</Text>
                  </View>
                  <Text style={styles.feedbackText}>{feedback.feedback}</Text>
                  {feedback.improvement && (
                    <Text style={styles.feedbackImprovement}>
                      Improvement: {feedback.improvement}
                    </Text>
                  )}
                </View>
              ))}
            </Card>
            </PremiumGate>

            {/* Recommended Drills */}
            <Card variant="elevated" style={styles.drillsCard}>
              <Text style={styles.sectionTitle}>Recommended Drills</Text>
              <Text style={styles.sectionSubtitle}>
                Practice these drills to improve your technique
              </Text>
              
              {analysisResults.recommendedDrills.map((drill) => (
                <TouchableOpacity 
                  key={drill.id} 
                  style={styles.drillItem}
                  onPress={() => handleViewDrill(drill.id)}
                >
                  <View style={styles.drillIcon}>
                    <Target size={20} color={colors.primary} />
                  </View>
                  
                  <View style={styles.drillDetails}>
                    <Text style={styles.drillTitle}>{drill.title}</Text>
                    <Text style={styles.drillFocus}>{drill.focus}</Text>
                    <View style={styles.drillMeta}>
                      <View style={styles.drillMetaItem}>
                        <Clock size={12} color={colors.gray} />
                        <Text style={styles.drillMetaText}>{drill.duration}</Text>
                      </View>
                      <View style={styles.drillMetaItem}>
                        <Star size={12} color={colors.yellow} />
                        <Text style={styles.drillMetaText}>{drill.difficulty}</Text>
                      </View>
                    </View>
                  </View>
                  
                  <ChevronRight size={16} color={colors.gray} />
                </TouchableOpacity>
              ))}
            </Card>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <Button
                title="Save Session"
                onPress={handleSaveSession}
                style={styles.primaryButton}
              />
              
              <View style={styles.secondaryButtons}>
                <TouchableOpacity 
                  style={styles.secondaryButton} 
                  onPress={handleStartNewAnalysis}
                >
                  <RotateCcw size={20} color={colors.primary} />
                  <Text style={styles.secondaryButtonText}>New Analysis</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.secondaryButton}
                  onPress={() => router.push('/(tabs)/progress')}
                >
                  <TrendingUp size={20} color={colors.primary} />
                  <Text style={styles.secondaryButtonText}>View Progress</Text>
                </TouchableOpacity>
              </View>
            </View>
          </>
        )}

        {/* Match Recording CTA */}
        <Card variant="elevated" style={styles.matchRecordingCard}>
          <LinearGradient
            colors={[colors.blue, '#60a5fa']}
            style={styles.matchRecordingGradient}
          >
            <View style={styles.matchRecordingContent}>
              <View style={styles.matchRecordingText}>
                <Text style={styles.matchRecordingTitle}>Record Live Match</Text>
                <Text style={styles.matchRecordingSubtitle}>
                  Track scores, stats, and performance in real-time
                </Text>
              </View>
              <Target size={32} color={colors.white} />
            </View>
            <Button
              title="Start Recording"
              onPress={() => router.push('/match-recording')}
              variant="outline"
              style={styles.matchRecordingButton}
            />
          </LinearGradient>
        </Card>

        {/* MediaPipe Demo CTA */}
        <Card variant="elevated" style={styles.mediaPipeCard}>
          <LinearGradient
            colors={['#6366f1', '#8b5cf6']}
            style={styles.mediaPipeGradient}
          >
            <View style={styles.mediaPipeContent}>
              <View style={styles.mediaPipeText}>
                <Text style={styles.mediaPipeTitle}>MediaPipe Demo</Text>
                <Text style={styles.mediaPipeSubtitle}>
                  Try our new local pose detection technology
                </Text>
              </View>
              <Zap size={32} color={colors.white} />
            </View>
            <Button
              title="Try MediaPipe"
              onPress={() => router.push('/mediapipe-demo')}
              variant="outline"
              style={styles.mediaPipeButton}
            />
          </LinearGradient>
        </Card>

        {/* Integration Demo CTA */}
        <Card variant="elevated" style={styles.integrationCard}>
          <LinearGradient
            colors={['#f59e0b', '#d97706']}
            style={styles.integrationGradient}
          >
            <View style={styles.integrationContent}>
              <View style={styles.integrationText}>
                <Text style={styles.integrationTitle}>Service Integrations</Text>
                <Text style={styles.integrationSubtitle}>
                  Explore Firebase, PostHog, Sentry, Magic.link & Stripe
                </Text>
              </View>
              <Settings size={32} color={colors.white} />
            </View>
            <Button
              title="View Integrations"
              onPress={() => router.push('/integration-demo')}
              variant="outline"
              style={styles.integrationButton}
            />
          </LinearGradient>
        </Card>

        {/* Match Simulation CTA */}
        <Card variant="elevated" style={styles.simulationCard}>
          <LinearGradient
            colors={[colors.yellow, '#ffd700']}
            style={styles.simulationGradient}
          >
            <View style={styles.simulationContent}>
              <View style={styles.simulationText}>
                <Text style={styles.simulationTitle}>Match Simulation</Text>
                <Text style={styles.simulationSubtitle}>
                  Practice against AI opponents with different playing styles
                </Text>
              </View>
              <Video size={32} color={colors.dark} />
            </View>
            <Button
              title="Start Simulation"
              onPress={() => router.push('/(tabs)/simulation' as any)}
              variant="outline"
              style={styles.simulationButton}
            />
          </LinearGradient>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGray,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 24,
    paddingBottom: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: colors.dark,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginTop: 4,
  },
  uploadCard: {
    marginHorizontal: 24,
    marginBottom: 20,
  },
  uploadContent: {
    alignItems: 'center',
  },
  uploadIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  uploadTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 8,
  },
  uploadDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  uploadButton: {
    width: '100%',
  },
  progressContainer: {
    width: '100%',
    marginBottom: 16,
  },
  progressBar: {
    height: 4,
    backgroundColor: colors.lightGray,
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: colors.gray,
    textAlign: 'center',
  },
  analyzingContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  analyzingIcon: {
    marginBottom: 8,
  },
  analyzingText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: colors.primary,
    marginBottom: 4,
  },
  analyzingSubtext: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    textAlign: 'center',
  },
  resultsCard: {
    marginHorizontal: 24,
    marginBottom: 20,
  },
  resultsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  resultsTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginLeft: 8,
    flex: 1,
  },
  shareButton: {
    padding: 8,
  },
  overallScore: {
    alignItems: 'center',
    paddingVertical: 20,
    position: 'relative',
  },
  scoreLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginBottom: 8,
  },
  scoreValue: {
    fontSize: 36,
    fontFamily: 'Inter-Bold',
    color: colors.primary,
  },
  scoreRing: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 8,
    borderColor: colors.lightGray,
    top: 10,
  },
  scoreProgress: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 8,
    borderColor: colors.primary,
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  highlightsCard: {
    marginHorizontal: 24,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginBottom: 20,
  },
  highlightItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray,
  },
  highlightThumbnail: {
    width: 60,
    height: 40,
    borderRadius: 8,
    marginRight: 12,
    backgroundColor: colors.lightGray,
  },
  highlightContent: {
    flex: 1,
  },
  highlightTitle: {
    fontSize: 15,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 4,
  },
  highlightDescription: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginBottom: 6,
  },
  highlightMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  highlightTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginLeft: 4,
  },
  highlightType: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  highlightTypeText: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
    color: colors.white,
  },
  ratingsCard: {
    marginHorizontal: 24,
    marginBottom: 20,
  },
  ratingItem: {
    marginBottom: 20,
  },
  ratingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  ratingSkill: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
  },
  ratingScore: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: colors.primary,
  },
  ratingBar: {
    height: 6,
    backgroundColor: colors.lightGray,
    borderRadius: 3,
    marginBottom: 8,
  },
  ratingFill: {
    height: '100%',
    borderRadius: 3,
  },
  ratingFeedback: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    lineHeight: 18,
  },
  feedbackCard: {
    marginHorizontal: 24,
    marginBottom: 20,
  },
  feedbackItem: {
    marginBottom: 16,
  },
  feedbackHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  feedbackIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  feedbackArea: {
    fontSize: 15,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
  },
  feedbackText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    lineHeight: 20,
    marginBottom: 4,
  },
  feedbackImprovement: {
    fontSize: 13,
    fontFamily: 'Inter-Medium',
    color: colors.primary,
  },
  drillsCard: {
    marginHorizontal: 24,
    marginBottom: 20,
  },
  drillItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray,
  },
  drillIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  drillDetails: {
    flex: 1,
  },
  drillTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 4,
  },
  drillFocus: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginBottom: 8,
  },
  drillMeta: {
    flexDirection: 'row',
    gap: 16,
  },
  drillMetaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  drillMetaText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginLeft: 4,
  },
  actionButtons: {
    padding: 24,
    paddingTop: 8,
  },
  primaryButton: {
    marginBottom: 16,
  },
  secondaryButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  secondaryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.primary,
    backgroundColor: colors.white,
  },
  secondaryButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: colors.primary,
    marginLeft: 8,
  },
  matchRecordingCard: {
    marginHorizontal: 24,
    marginBottom: 20,
    padding: 0,
    overflow: 'hidden',
  },
  matchRecordingGradient: {
    padding: 24,
    borderRadius: 16,
  },
  matchRecordingContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  matchRecordingText: {
    flex: 1,
  },
  matchRecordingTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: colors.white,
    marginBottom: 8,
  },
  matchRecordingSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.white,
    opacity: 0.9,
  },
  matchRecordingButton: {
    borderColor: colors.white,
  },
  simulationCard: {
    marginHorizontal: 24,
    marginBottom: 20,
    padding: 0,
    overflow: 'hidden',
  },
  simulationGradient: {
    padding: 24,
    borderRadius: 16,
  },
  simulationContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  simulationText: {
    flex: 1,
  },
  simulationTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: colors.dark,
    marginBottom: 8,
  },
  simulationSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.dark,
    opacity: 0.8,
  },
  simulationButton: {
    borderColor: colors.dark,
  },
  mediaPipeCard: {
    marginHorizontal: 24,
    marginBottom: 20,
    padding: 0,
    overflow: 'hidden',
  },
  mediaPipeGradient: {
    padding: 24,
    borderRadius: 16,
  },
  mediaPipeContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  mediaPipeText: {
    flex: 1,
  },
  mediaPipeTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: colors.white,
    marginBottom: 8,
  },
  mediaPipeSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.white,
    opacity: 0.9,
  },
  mediaPipeButton: {
    borderColor: colors.white,
  },
  integrationCard: {
    marginHorizontal: 24,
    marginBottom: 20,
    padding: 0,
    overflow: 'hidden',
  },
  integrationGradient: {
    padding: 24,
    borderRadius: 16,
  },
  integrationContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  integrationText: {
    flex: 1,
  },
  integrationTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: colors.white,
    marginBottom: 8,
  },
  integrationSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.white,
    opacity: 0.9,
  },
  integrationButton: {
    borderColor: colors.white,
  },
});