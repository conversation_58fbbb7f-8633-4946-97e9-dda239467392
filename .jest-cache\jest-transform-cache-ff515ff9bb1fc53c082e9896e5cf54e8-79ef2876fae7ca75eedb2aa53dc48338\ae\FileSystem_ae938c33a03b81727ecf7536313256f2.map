{"version": 3, "names": ["_expoModulesCore", "require", "_reactNative", "_ExponentFileSystem", "_interopRequireDefault", "_FileSystem", "_callSuper", "t", "o", "e", "_getPrototypeOf2", "default", "_possibleConstructorReturn2", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "ExponentFileSystem", "console", "warn", "normalizeEndingSlash", "p", "replace", "documentDirectory", "exports", "cacheDirectory", "bundleDirectory", "getInfoAsync", "_x", "_getInfoAsync", "arguments", "_asyncToGenerator2", "fileUri", "options", "length", "undefined", "UnavailabilityError", "readAsStringAsync", "_x2", "_readAsStringAsync", "getContentUriAsync", "_x3", "_getContentUriAsync", "Platform", "OS", "writeAsStringAsync", "_x4", "_x5", "_writeAsStringAsync", "contents", "deleteAsync", "_x6", "_deleteAsync", "deleteLegacyDocumentDirectoryAndroid", "_deleteLegacyDocumentDirectoryAndroid", "legacyDocumentDirectory", "idempotent", "moveAsync", "_x7", "_moveAsync", "copyAsync", "_x8", "_copyAsync", "makeDirectoryAsync", "_x9", "_makeDirectoryAsync", "readDirectoryAsync", "_x0", "_readDirectoryAsync", "getFreeDiskStorageAsync", "_getFreeDiskStorageAsync", "getTotalDiskCapacityAsync", "_getTotalDiskCapacityAsync", "downloadAsync", "_x1", "_x10", "_downloadAsync", "uri", "Object", "assign", "sessionType", "FileSystemSessionType", "BACKGROUND", "uploadAsync", "_x11", "_x12", "_uploadAsync", "url", "uploadType", "FileSystemUploadType", "BINARY_CONTENT", "httpMethod", "toUpperCase", "createDownloadResumable", "callback", "resumeData", "DownloadResumable", "createUploadTask", "UploadTask", "FileSystemCancellableNetworkTask", "_classCallCheck2", "_uuid", "uuid", "v4", "taskWasCanceled", "_createClass2", "key", "value", "_cancelAsync", "networkTaskCancelAsync", "removeSubscription", "cancelAsync", "isTaskCancelled", "get", "addSubscription", "_this", "subscription", "addListener", "getEventName", "event", "get<PERSON>allback", "data", "remove", "_FileSystemCancellabl", "_options$httpMethod", "_this2", "_inherits2", "_uploadAsync2", "uploadTaskStartAsync", "result", "_FileSystemCancellabl2", "_fileUri", "_this3", "_downloadAsync2", "downloadResumableStartAsync", "_pauseAsync", "downloadResumablePauseAsync", "pauseResult", "savable", "Error", "pauseAsync", "_resumeAsync", "resumeAsync", "baseReadAsStringAsync", "baseWriteAsStringAsync", "baseDeleteAsync", "baseMoveAsync", "baseCopyAsync", "StorageAccessFramework", "_StorageAccessFramework", "getUriForDirectoryInRoot", "folderName", "requestDirectoryPermissionsAsync", "_requestDirectoryPermissionsAsync", "initialFileUrl", "_x13", "_readDirectoryAsync2", "<PERSON><PERSON><PERSON>", "readSAFDirectoryAsync", "_x14", "_x15", "_makeDirectoryAsync2", "parentUri", "<PERSON><PERSON><PERSON>", "makeSAFDirectoryAsync", "createFileAsync", "_x16", "_x17", "_x18", "_createFileAsync", "fileName", "mimeType", "createSAFFileAsync"], "sources": ["FileSystem.ts"], "sourcesContent": ["import { type EventSubscription, UnavailabilityError, uuid } from 'expo-modules-core';\nimport { Platform } from 'react-native';\n\nimport ExponentFileSystem from './ExponentFileSystem';\nimport {\n  DownloadOptions,\n  DownloadPauseState,\n  FileSystemNetworkTaskProgressCallback,\n  DownloadProgressData,\n  UploadProgressData,\n  FileInfo,\n  FileSystemAcceptedUploadHttpMethod,\n  FileSystemDownloadResult,\n  FileSystemRequestDirectoryPermissionsResult,\n  FileSystemSessionType,\n  FileSystemUploadOptions,\n  FileSystemUploadResult,\n  FileSystemUploadType,\n  ProgressEvent,\n  ReadingOptions,\n  WritingOptions,\n  DeletingOptions,\n  InfoOptions,\n  RelocatingOptions,\n  MakeDirectoryOptions,\n} from './FileSystem.types';\n\nif (!ExponentFileSystem) {\n  console.warn(\n    \"No native ExponentFileSystem module found, are you sure the expo-file-system's module is linked properly?\"\n  );\n}\n\nfunction normalizeEndingSlash(p: string | null): string | null {\n  if (p != null) {\n    return p.replace(/\\/*$/, '') + '/';\n  }\n  return null;\n}\n\n/**\n * `file://` URI pointing to the directory where user documents for this app will be stored.\n * Files stored here will remain until explicitly deleted by the app. Ends with a trailing `/`.\n * Example uses are for files the user saves that they expect to see again.\n */\nexport const documentDirectory = normalizeEndingSlash(ExponentFileSystem.documentDirectory);\n\n/**\n * `file://` URI pointing to the directory where temporary files used by this app will be stored.\n * Files stored here may be automatically deleted by the system when low on storage.\n * Example uses are for downloaded or generated files that the app just needs for one-time usage.\n */\nexport const cacheDirectory = normalizeEndingSlash(ExponentFileSystem.cacheDirectory);\n\n/**\n * URI to the directory where assets bundled with the application are stored.\n */\nexport const bundleDirectory = normalizeEndingSlash(ExponentFileSystem.bundleDirectory);\n\n/**\n * Get metadata information about a file, directory or external content/asset.\n * @param fileUri URI to the file or directory. See [supported URI schemes](#supported-uri-schemes).\n * @param options A map of options represented by [`InfoOptions`](#infooptions) type.\n * @return A Promise that resolves to a `FileInfo` object. If no item exists at this URI,\n * the returned Promise resolves to `FileInfo` object in form of `{ exists: false, isDirectory: false }`.\n */\nexport async function getInfoAsync(fileUri: string, options: InfoOptions = {}): Promise<FileInfo> {\n  if (!ExponentFileSystem.getInfoAsync) {\n    throw new UnavailabilityError('expo-file-system', 'getInfoAsync');\n  }\n  return await ExponentFileSystem.getInfoAsync(fileUri, options);\n}\n\n/**\n * Read the entire contents of a file as a string. Binary will be returned in raw format, you will need to append `data:image/png;base64,` to use it as Base64.\n * @param fileUri `file://` or [SAF](#saf-uri) URI to the file or directory.\n * @param options A map of read options represented by [`ReadingOptions`](#readingoptions) type.\n * @return A Promise that resolves to a string containing the entire contents of the file.\n */\nexport async function readAsStringAsync(\n  fileUri: string,\n  options: ReadingOptions = {}\n): Promise<string> {\n  if (!ExponentFileSystem.readAsStringAsync) {\n    throw new UnavailabilityError('expo-file-system', 'readAsStringAsync');\n  }\n  return await ExponentFileSystem.readAsStringAsync(fileUri, options);\n}\n\n/**\n * Takes a `file://` URI and converts it into content URI (`content://`) so that it can be accessed by other applications outside of Expo.\n * @param fileUri The local URI of the file. If there is no file at this URI, an exception will be thrown.\n * @example\n * ```js\n * FileSystem.getContentUriAsync(uri).then(cUri => {\n *   console.log(cUri);\n *   IntentLauncher.startActivityAsync('android.intent.action.VIEW', {\n *     data: cUri,\n *     flags: 1,\n *   });\n * });\n * ```\n * @return Returns a Promise that resolves to a `string` containing a `content://` URI pointing to the file.\n * The URI is the same as the `fileUri` input parameter but in a different format.\n * @platform android\n */\nexport async function getContentUriAsync(fileUri: string): Promise<string> {\n  if (Platform.OS === 'android') {\n    if (!ExponentFileSystem.getContentUriAsync) {\n      throw new UnavailabilityError('expo-file-system', 'getContentUriAsync');\n    }\n    return await ExponentFileSystem.getContentUriAsync(fileUri);\n  } else {\n    return fileUri;\n  }\n}\n\n/**\n * Write the entire contents of a file as a string.\n * @param fileUri `file://` or [SAF](#saf-uri) URI to the file or directory.\n * > Note: when you're using SAF URI the file needs to exist. You can't create a new file.\n * @param contents The string to replace the contents of the file with.\n * @param options A map of write options represented by [`WritingOptions`](#writingoptions) type.\n */\nexport async function writeAsStringAsync(\n  fileUri: string,\n  contents: string,\n  options: WritingOptions = {}\n): Promise<void> {\n  if (!ExponentFileSystem.writeAsStringAsync) {\n    throw new UnavailabilityError('expo-file-system', 'writeAsStringAsync');\n  }\n  return await ExponentFileSystem.writeAsStringAsync(fileUri, contents, options);\n}\n\n/**\n * Delete a file or directory. If the URI points to a directory, the directory and all its contents are recursively deleted.\n * @param fileUri `file://` or [SAF](#saf-uri) URI to the file or directory.\n * @param options A map of write options represented by [`DeletingOptions`](#deletingoptions) type.\n */\nexport async function deleteAsync(fileUri: string, options: DeletingOptions = {}): Promise<void> {\n  if (!ExponentFileSystem.deleteAsync) {\n    throw new UnavailabilityError('expo-file-system', 'deleteAsync');\n  }\n  return await ExponentFileSystem.deleteAsync(fileUri, options);\n}\n\nexport async function deleteLegacyDocumentDirectoryAndroid(): Promise<void> {\n  if (Platform.OS !== 'android' || documentDirectory == null) {\n    return;\n  }\n  const legacyDocumentDirectory = `${documentDirectory}ExperienceData/`;\n  return await deleteAsync(legacyDocumentDirectory, { idempotent: true });\n}\n\n/**\n * Move a file or directory to a new location.\n * @param options A map of move options represented by [`RelocatingOptions`](#relocatingoptions) type.\n */\nexport async function moveAsync(options: RelocatingOptions): Promise<void> {\n  if (!ExponentFileSystem.moveAsync) {\n    throw new UnavailabilityError('expo-file-system', 'moveAsync');\n  }\n  return await ExponentFileSystem.moveAsync(options);\n}\n\n/**\n * Create a copy of a file or directory. Directories are recursively copied with all of their contents.\n * It can be also used to copy content shared by other apps to local filesystem.\n * @param options A map of move options represented by [`RelocatingOptions`](#relocatingoptions) type.\n */\nexport async function copyAsync(options: RelocatingOptions): Promise<void> {\n  if (!ExponentFileSystem.copyAsync) {\n    throw new UnavailabilityError('expo-file-system', 'copyAsync');\n  }\n  return await ExponentFileSystem.copyAsync(options);\n}\n\n/**\n * Create a new empty directory.\n * @param fileUri `file://` URI to the new directory to create.\n * @param options A map of create directory options represented by [`MakeDirectoryOptions`](#makedirectoryoptions) type.\n */\nexport async function makeDirectoryAsync(\n  fileUri: string,\n  options: MakeDirectoryOptions = {}\n): Promise<void> {\n  if (!ExponentFileSystem.makeDirectoryAsync) {\n    throw new UnavailabilityError('expo-file-system', 'makeDirectoryAsync');\n  }\n  return await ExponentFileSystem.makeDirectoryAsync(fileUri, options);\n}\n\n/**\n * Enumerate the contents of a directory.\n * @param fileUri `file://` URI to the directory.\n * @return A Promise that resolves to an array of strings, each containing the name of a file or directory contained in the directory at `fileUri`.\n */\nexport async function readDirectoryAsync(fileUri: string): Promise<string[]> {\n  if (!ExponentFileSystem.readDirectoryAsync) {\n    throw new UnavailabilityError('expo-file-system', 'readDirectoryAsync');\n  }\n  return await ExponentFileSystem.readDirectoryAsync(fileUri);\n}\n\n/**\n * Gets the available internal disk storage size, in bytes. This returns the free space on the data partition that hosts all of the internal storage for all apps on the device.\n * @return Returns a Promise that resolves to the number of bytes available on the internal disk.\n */\nexport async function getFreeDiskStorageAsync(): Promise<number> {\n  if (!ExponentFileSystem.getFreeDiskStorageAsync) {\n    throw new UnavailabilityError('expo-file-system', 'getFreeDiskStorageAsync');\n  }\n  return await ExponentFileSystem.getFreeDiskStorageAsync();\n}\n\n/**\n * Gets total internal disk storage size, in bytes. This is the total capacity of the data partition that hosts all the internal storage for all apps on the device.\n * @return Returns a Promise that resolves to a number that specifies the total internal disk storage capacity in bytes.\n */\nexport async function getTotalDiskCapacityAsync(): Promise<number> {\n  if (!ExponentFileSystem.getTotalDiskCapacityAsync) {\n    throw new UnavailabilityError('expo-file-system', 'getTotalDiskCapacityAsync');\n  }\n  return await ExponentFileSystem.getTotalDiskCapacityAsync();\n}\n\n/**\n * Download the contents at a remote URI to a file in the app's file system. The directory for a local file uri must exist prior to calling this function.\n * @param uri The remote URI to download from.\n * @param fileUri The local URI of the file to download to. If there is no file at this URI, a new one is created.\n * If there is a file at this URI, its contents are replaced. The directory for the file must exist.\n * @param options A map of download options represented by [`DownloadOptions`](#downloadoptions) type.\n * @example\n * ```js\n * FileSystem.downloadAsync(\n *   'http://techslides.com/demos/sample-videos/small.mp4',\n *   FileSystem.documentDirectory + 'small.mp4'\n * )\n *   .then(({ uri }) => {\n *     console.log('Finished downloading to ', uri);\n *   })\n *   .catch(error => {\n *     console.error(error);\n *   });\n * ```\n * @return Returns a Promise that resolves to a `FileSystemDownloadResult` object.\n */\nexport async function downloadAsync(\n  uri: string,\n  fileUri: string,\n  options: DownloadOptions = {}\n): Promise<FileSystemDownloadResult> {\n  if (!ExponentFileSystem.downloadAsync) {\n    throw new UnavailabilityError('expo-file-system', 'downloadAsync');\n  }\n\n  return await ExponentFileSystem.downloadAsync(uri, fileUri, {\n    sessionType: FileSystemSessionType.BACKGROUND,\n    ...options,\n  });\n}\n\n/**\n * Upload the contents of the file pointed by `fileUri` to the remote url.\n * @param url The remote URL, where the file will be sent.\n * @param fileUri The local URI of the file to send. The file must exist.\n * @param options A map of download options represented by [`FileSystemUploadOptions`](#filesystemuploadoptions) type.\n * @example\n * **Client**\n *\n * ```js\n * import * as FileSystem from 'expo-file-system';\n *\n * try {\n *   const response = await FileSystem.uploadAsync(`http://***********:1234/binary-upload`, fileUri, {\n *     fieldName: 'file',\n *     httpMethod: 'PATCH',\n *     uploadType: FileSystem.FileSystemUploadType.BINARY_CONTENT,\n *   });\n *   console.log(JSON.stringify(response, null, 4));\n * } catch (error) {\n *   console.log(error);\n * }\n * ```\n *\n * **Server**\n *\n * Please refer to the \"[Server: Handling multipart requests](#server-handling-multipart-requests)\" example - there is code for a simple Node.js server.\n * @return Returns a Promise that resolves to `FileSystemUploadResult` object.\n */\nexport async function uploadAsync(\n  url: string,\n  fileUri: string,\n  options: FileSystemUploadOptions = {}\n): Promise<FileSystemUploadResult> {\n  if (!ExponentFileSystem.uploadAsync) {\n    throw new UnavailabilityError('expo-file-system', 'uploadAsync');\n  }\n\n  return await ExponentFileSystem.uploadAsync(url, fileUri, {\n    sessionType: FileSystemSessionType.BACKGROUND,\n    uploadType: FileSystemUploadType.BINARY_CONTENT,\n    ...options,\n    httpMethod: (options.httpMethod || 'POST').toUpperCase(),\n  });\n}\n\n/**\n * Create a `DownloadResumable` object which can start, pause, and resume a download of contents at a remote URI to a file in the app's file system.\n * > Note: You need to call `downloadAsync()`, on a `DownloadResumable` instance to initiate the download.\n * The `DownloadResumable` object has a callback that provides download progress updates.\n * Downloads can be resumed across app restarts by using `AsyncStorage` to store the `DownloadResumable.savable()` object for later retrieval.\n * The `savable` object contains the arguments required to initialize a new `DownloadResumable` object to resume the download after an app restart.\n * The directory for a local file uri must exist prior to calling this function.\n * @param uri The remote URI to download from.\n * @param fileUri The local URI of the file to download to. If there is no file at this URI, a new one is created.\n * If there is a file at this URI, its contents are replaced. The directory for the file must exist.\n * @param options A map of download options represented by [`DownloadOptions`](#downloadoptions) type.\n * @param callback This function is called on each data write to update the download progress.\n * > **Note**: When the app has been moved to the background, this callback won't be fired until it's moved to the foreground.\n * @param resumeData The string which allows the api to resume a paused download. This is set on the `DownloadResumable` object automatically when a download is paused.\n * When initializing a new `DownloadResumable` this should be `null`.\n */\nexport function createDownloadResumable(\n  uri: string,\n  fileUri: string,\n  options?: DownloadOptions,\n  callback?: FileSystemNetworkTaskProgressCallback<DownloadProgressData>,\n  resumeData?: string\n): DownloadResumable {\n  return new DownloadResumable(uri, fileUri, options, callback, resumeData);\n}\n\nexport function createUploadTask(\n  url: string,\n  fileUri: string,\n  options?: FileSystemUploadOptions,\n  callback?: FileSystemNetworkTaskProgressCallback<UploadProgressData>\n): UploadTask {\n  return new UploadTask(url, fileUri, options, callback);\n}\n\nexport abstract class FileSystemCancellableNetworkTask<\n  T extends DownloadProgressData | UploadProgressData,\n> {\n  private _uuid = uuid.v4();\n  protected taskWasCanceled = false;\n  private subscription?: EventSubscription | null;\n\n  // @docsMissing\n  public async cancelAsync(): Promise<void> {\n    if (!ExponentFileSystem.networkTaskCancelAsync) {\n      throw new UnavailabilityError('expo-file-system', 'networkTaskCancelAsync');\n    }\n\n    this.removeSubscription();\n    this.taskWasCanceled = true;\n    return await ExponentFileSystem.networkTaskCancelAsync(this.uuid);\n  }\n\n  protected isTaskCancelled(): boolean {\n    if (this.taskWasCanceled) {\n      console.warn('This task was already canceled.');\n      return true;\n    }\n\n    return false;\n  }\n\n  protected get uuid(): string {\n    return this._uuid;\n  }\n\n  protected abstract getEventName(): string;\n\n  protected abstract getCallback(): FileSystemNetworkTaskProgressCallback<T> | undefined;\n\n  protected addSubscription() {\n    if (this.subscription) {\n      return;\n    }\n\n    this.subscription = ExponentFileSystem.addListener(\n      this.getEventName(),\n      (event: ProgressEvent<T>) => {\n        if (event.uuid === this.uuid) {\n          const callback = this.getCallback();\n          if (callback) {\n            callback(event.data as T);\n          }\n        }\n      }\n    );\n  }\n\n  protected removeSubscription() {\n    if (!this.subscription) {\n      return;\n    }\n    this.subscription.remove();\n    this.subscription = null;\n  }\n}\n\nexport class UploadTask extends FileSystemCancellableNetworkTask<UploadProgressData> {\n  private options: FileSystemUploadOptions;\n\n  constructor(\n    private url: string,\n    private fileUri: string,\n    options?: FileSystemUploadOptions,\n    private callback?: FileSystemNetworkTaskProgressCallback<UploadProgressData>\n  ) {\n    super();\n\n    const httpMethod = (options?.httpMethod?.toUpperCase() ||\n      'POST') as FileSystemAcceptedUploadHttpMethod;\n\n    this.options = {\n      sessionType: FileSystemSessionType.BACKGROUND,\n      uploadType: FileSystemUploadType.BINARY_CONTENT,\n      ...options,\n      httpMethod,\n    };\n  }\n\n  protected getEventName(): string {\n    return 'expo-file-system.uploadProgress';\n  }\n  protected getCallback(): FileSystemNetworkTaskProgressCallback<UploadProgressData> | undefined {\n    return this.callback;\n  }\n\n  // @docsMissing\n  public async uploadAsync(): Promise<FileSystemUploadResult | undefined> {\n    if (!ExponentFileSystem.uploadTaskStartAsync) {\n      throw new UnavailabilityError('expo-file-system', 'uploadTaskStartAsync');\n    }\n\n    if (this.isTaskCancelled()) {\n      return;\n    }\n\n    this.addSubscription();\n    const result = await ExponentFileSystem.uploadTaskStartAsync(\n      this.url,\n      this.fileUri,\n      this.uuid,\n      this.options\n    );\n    this.removeSubscription();\n\n    return result;\n  }\n}\n\nexport class DownloadResumable extends FileSystemCancellableNetworkTask<DownloadProgressData> {\n  constructor(\n    private url: string,\n    private _fileUri: string,\n    private options: DownloadOptions = {},\n    private callback?: FileSystemNetworkTaskProgressCallback<DownloadProgressData>,\n    private resumeData?: string\n  ) {\n    super();\n  }\n\n  public get fileUri(): string {\n    return this._fileUri;\n  }\n\n  protected getEventName(): string {\n    return 'expo-file-system.downloadProgress';\n  }\n\n  protected getCallback(): FileSystemNetworkTaskProgressCallback<DownloadProgressData> | undefined {\n    return this.callback;\n  }\n\n  /**\n   * Download the contents at a remote URI to a file in the app's file system.\n   * @return Returns a Promise that resolves to `FileSystemDownloadResult` object, or to `undefined` when task was cancelled.\n   */\n  async downloadAsync(): Promise<FileSystemDownloadResult | undefined> {\n    if (!ExponentFileSystem.downloadResumableStartAsync) {\n      throw new UnavailabilityError('expo-file-system', 'downloadResumableStartAsync');\n    }\n\n    if (this.isTaskCancelled()) {\n      return;\n    }\n\n    this.addSubscription();\n    return await ExponentFileSystem.downloadResumableStartAsync(\n      this.url,\n      this._fileUri,\n      this.uuid,\n      this.options,\n      this.resumeData\n    );\n  }\n\n  /**\n   * Pause the current download operation. `resumeData` is added to the `DownloadResumable` object after a successful pause operation.\n   * Returns an object that can be saved with `AsyncStorage` for future retrieval (the same object that is returned from calling `FileSystem.DownloadResumable.savable()`).\n   * @return Returns a Promise that resolves to `DownloadPauseState` object.\n   */\n  async pauseAsync(): Promise<DownloadPauseState> {\n    if (!ExponentFileSystem.downloadResumablePauseAsync) {\n      throw new UnavailabilityError('expo-file-system', 'downloadResumablePauseAsync');\n    }\n\n    if (this.isTaskCancelled()) {\n      return {\n        fileUri: this._fileUri,\n        options: this.options,\n        url: this.url,\n      };\n    }\n\n    const pauseResult = await ExponentFileSystem.downloadResumablePauseAsync(this.uuid);\n    this.removeSubscription();\n    if (pauseResult) {\n      this.resumeData = pauseResult.resumeData;\n      return this.savable();\n    } else {\n      throw new Error('Unable to generate a savable pause state');\n    }\n  }\n\n  /**\n   * Resume a paused download operation.\n   * @return Returns a Promise that resolves to `FileSystemDownloadResult` object, or to `undefined` when task was cancelled.\n   */\n  async resumeAsync(): Promise<FileSystemDownloadResult | undefined> {\n    if (!ExponentFileSystem.downloadResumableStartAsync) {\n      throw new UnavailabilityError('expo-file-system', 'downloadResumableStartAsync');\n    }\n\n    if (this.isTaskCancelled()) {\n      return;\n    }\n\n    this.addSubscription();\n    return await ExponentFileSystem.downloadResumableStartAsync(\n      this.url,\n      this.fileUri,\n      this.uuid,\n      this.options,\n      this.resumeData\n    );\n  }\n\n  /**\n   * Method to get the object which can be saved with `AsyncStorage` for future retrieval.\n   * @returns Returns object in shape of `DownloadPauseState` type.\n   */\n  savable(): DownloadPauseState {\n    return {\n      url: this.url,\n      fileUri: this.fileUri,\n      options: this.options,\n      resumeData: this.resumeData,\n    };\n  }\n}\n\nconst baseReadAsStringAsync = readAsStringAsync;\nconst baseWriteAsStringAsync = writeAsStringAsync;\nconst baseDeleteAsync = deleteAsync;\nconst baseMoveAsync = moveAsync;\nconst baseCopyAsync = copyAsync;\n\n/**\n * The `StorageAccessFramework` is a namespace inside of the `expo-file-system` module, which encapsulates all functions which can be used with [SAF URIs](#saf-uri).\n * You can read more about SAF in the [Android documentation](https://developer.android.com/guide/topics/providers/document-provider).\n *\n * @example\n * # Basic Usage\n *\n * ```ts\n * import { StorageAccessFramework } from 'expo-file-system';\n *\n * // Requests permissions for external directory\n * const permissions = await StorageAccessFramework.requestDirectoryPermissionsAsync();\n *\n * if (permissions.granted) {\n *   // Gets SAF URI from response\n *   const uri = permissions.directoryUri;\n *\n *   // Gets all files inside of selected directory\n *   const files = await StorageAccessFramework.readDirectoryAsync(uri);\n *   alert(`Files inside ${uri}:\\n\\n${JSON.stringify(files)}`);\n * }\n * ```\n *\n * # Migrating an album\n *\n * ```ts\n * import * as MediaLibrary from 'expo-media-library';\n * import * as FileSystem from 'expo-file-system';\n * const { StorageAccessFramework } = FileSystem;\n *\n * async function migrateAlbum(albumName: string) {\n *   // Gets SAF URI to the album\n *   const albumUri = StorageAccessFramework.getUriForDirectoryInRoot(albumName);\n *\n *   // Requests permissions\n *   const permissions = await StorageAccessFramework.requestDirectoryPermissionsAsync(albumUri);\n *   if (!permissions.granted) {\n *     return;\n *   }\n *\n *   const permittedUri = permissions.directoryUri;\n *   // Checks if users selected the correct folder\n *   if (!permittedUri.includes(albumName)) {\n *     return;\n *   }\n *\n *   const mediaLibraryPermissions = await MediaLibrary.requestPermissionsAsync();\n *   if (!mediaLibraryPermissions.granted) {\n *     return;\n *   }\n *\n *   // Moves files from external storage to internal storage\n *   await StorageAccessFramework.moveAsync({\n *     from: permittedUri,\n *     to: FileSystem.documentDirectory!,\n *   });\n *\n *   const outputDir = FileSystem.documentDirectory! + albumName;\n *   const migratedFiles = await FileSystem.readDirectoryAsync(outputDir);\n *\n *   // Creates assets from local files\n *   const [newAlbumCreator, ...assets] = await Promise.all(\n *     migratedFiles.map<Promise<MediaLibrary.Asset>>(\n *       async fileName => await MediaLibrary.createAssetAsync(outputDir + '/' + fileName)\n *     )\n *   );\n *\n *   // Album was empty\n *   if (!newAlbumCreator) {\n *     return;\n *   }\n *\n *   // Creates a new album in the scoped directory\n *   const newAlbum = await MediaLibrary.createAlbumAsync(albumName, newAlbumCreator, false);\n *   if (assets.length) {\n *     await MediaLibrary.addAssetsToAlbumAsync(assets, newAlbum, false);\n *   }\n * }\n * ```\n * @platform Android\n */\nexport namespace StorageAccessFramework {\n  /**\n   * Gets a [SAF URI](#saf-uri) pointing to a folder in the Android root directory. You can use this function to get URI for\n   * `StorageAccessFramework.requestDirectoryPermissionsAsync()` when you trying to migrate an album. In that case, the name of the album is the folder name.\n   * @param folderName The name of the folder which is located in the Android root directory.\n   * @return Returns a [SAF URI](#saf-uri) to a folder.\n   * @platform Android\n   */\n  export function getUriForDirectoryInRoot(folderName: string) {\n    return `content://com.android.externalstorage.documents/tree/primary:${folderName}/document/primary:${folderName}`;\n  }\n\n  /**\n   * Allows users to select a specific directory, granting your app access to all of the files and sub-directories within that directory.\n   * @param initialFileUrl The [SAF URI](#saf-uri) of the directory that the file picker should display when it first loads.\n   * If URI is incorrect or points to a non-existing folder, it's ignored.\n   * @platform android 11+\n   * @return Returns a Promise that resolves to `FileSystemRequestDirectoryPermissionsResult` object.\n   */\n  export async function requestDirectoryPermissionsAsync(\n    initialFileUrl: string | null = null\n  ): Promise<FileSystemRequestDirectoryPermissionsResult> {\n    if (!ExponentFileSystem.requestDirectoryPermissionsAsync) {\n      throw new UnavailabilityError(\n        'expo-file-system',\n        'StorageAccessFramework.requestDirectoryPermissionsAsync'\n      );\n    }\n\n    return await ExponentFileSystem.requestDirectoryPermissionsAsync(initialFileUrl);\n  }\n\n  /**\n   * Enumerate the contents of a directory.\n   * @param dirUri [SAF](#saf-uri) URI to the directory.\n   * @return A Promise that resolves to an array of strings, each containing the full [SAF URI](#saf-uri) of a file or directory contained in the directory at `fileUri`.\n   * @platform Android\n   */\n  export async function readDirectoryAsync(dirUri: string): Promise<string[]> {\n    if (!ExponentFileSystem.readSAFDirectoryAsync) {\n      throw new UnavailabilityError(\n        'expo-file-system',\n        'StorageAccessFramework.readDirectoryAsync'\n      );\n    }\n    return await ExponentFileSystem.readSAFDirectoryAsync(dirUri);\n  }\n\n  /**\n   * Creates a new empty directory.\n   * @param parentUri The [SAF](#saf-uri) URI to the parent directory.\n   * @param dirName The name of new directory.\n   * @return A Promise that resolves to a [SAF URI](#saf-uri) to the created directory.\n   * @platform Android\n   */\n  export async function makeDirectoryAsync(parentUri: string, dirName: string): Promise<string> {\n    if (!ExponentFileSystem.makeSAFDirectoryAsync) {\n      throw new UnavailabilityError(\n        'expo-file-system',\n        'StorageAccessFramework.makeDirectoryAsync'\n      );\n    }\n    return await ExponentFileSystem.makeSAFDirectoryAsync(parentUri, dirName);\n  }\n\n  /**\n   * Creates a new empty file.\n   * @param parentUri The [SAF](#saf-uri) URI to the parent directory.\n   * @param fileName The name of new file **without the extension**.\n   * @param mimeType The MIME type of new file.\n   * @return A Promise that resolves to a [SAF URI](#saf-uri) to the created file.\n   * @platform Android\n   */\n  export async function createFileAsync(\n    parentUri: string,\n    fileName: string,\n    mimeType: string\n  ): Promise<string> {\n    if (!ExponentFileSystem.createSAFFileAsync) {\n      throw new UnavailabilityError('expo-file-system', 'StorageAccessFramework.createFileAsync');\n    }\n    return await ExponentFileSystem.createSAFFileAsync(parentUri, fileName, mimeType);\n  }\n\n  /**\n   * Alias for [`writeAsStringAsync`](#filesystemwriteasstringasyncfileuri-contents-options) method.\n   */\n  export const writeAsStringAsync = baseWriteAsStringAsync;\n  /**\n   * Alias for [`readAsStringAsync`](#filesystemreadasstringasyncfileuri-options) method.\n   */\n  export const readAsStringAsync = baseReadAsStringAsync;\n  /**\n   * Alias for [`deleteAsync`](#filesystemdeleteasyncfileuri-options) method.\n   */\n  export const deleteAsync = baseDeleteAsync;\n  /**\n   * Alias for [`moveAsync`](#filesystemmoveasyncoptions) method.\n   */\n  export const moveAsync = baseMoveAsync;\n  /**\n   * Alias for [`copyAsync`](#filesystemcopyasyncoptions) method.\n   */\n  export const copyAsync = baseCopyAsync;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,gBAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,mBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;AAqB4B,SAAAK,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAE,gBAAA,CAAAC,OAAA,EAAAH,CAAA,OAAAI,2BAAA,CAAAD,OAAA,EAAAJ,CAAA,EAAAM,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAP,CAAA,EAAAC,CAAA,YAAAC,gBAAA,CAAAC,OAAA,EAAAJ,CAAA,EAAAS,WAAA,IAAAR,CAAA,CAAAS,KAAA,CAAAV,CAAA,EAAAE,CAAA;AAAA,SAAAI,0BAAA,cAAAN,CAAA,IAAAW,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAX,CAAA,aAAAM,yBAAA,YAAAA,0BAAA,aAAAN,CAAA;AAE5B,IAAI,CAACe,2BAAkB,EAAE;EACvBC,OAAO,CAACC,IAAI,CACV,2GACF,CAAC;AACH;AAEA,SAASC,oBAAoBA,CAACC,CAAgB,EAAiB;EAC7D,IAAIA,CAAC,IAAI,IAAI,EAAE;IACb,OAAOA,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG;EACpC;EACA,OAAO,IAAI;AACb;AAOO,IAAMC,iBAAiB,GAAAC,OAAA,CAAAD,iBAAA,GAAGH,oBAAoB,CAACH,2BAAkB,CAACM,iBAAiB,CAAC;AAOpF,IAAME,cAAc,GAAAD,OAAA,CAAAC,cAAA,GAAGL,oBAAoB,CAACH,2BAAkB,CAACQ,cAAc,CAAC;AAK9E,IAAMC,eAAe,GAAAF,OAAA,CAAAE,eAAA,GAAGN,oBAAoB,CAACH,2BAAkB,CAACS,eAAe,CAAC;AAAC,SASlEC,YAAYA,CAAAC,EAAA;EAAA,OAAAC,aAAA,CAAAjB,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAAAD,cAAA;EAAAA,aAAA,OAAAE,kBAAA,CAAAzB,OAAA,EAA3B,WAA4B0B,OAAe,EAAgD;IAAA,IAA9CC,OAAoB,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAG,CAAC,CAAC;IAC3E,IAAI,CAACb,2BAAkB,CAACU,YAAY,EAAE;MACpC,MAAM,IAAIS,oCAAmB,CAAC,kBAAkB,EAAE,cAAc,CAAC;IACnE;IACA,aAAanB,2BAAkB,CAACU,YAAY,CAACK,OAAO,EAAEC,OAAO,CAAC;EAChE,CAAC;EAAA,OAAAJ,aAAA,CAAAjB,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAQqBO,iBAAiBA,CAAAC,GAAA;EAAA,OAAAC,kBAAA,CAAA3B,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAAAS,mBAAA;EAAAA,kBAAA,OAAAR,kBAAA,CAAAzB,OAAA,EAAhC,WACL0B,OAAe,EAEE;IAAA,IADjBC,OAAuB,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAG,CAAC,CAAC;IAE5B,IAAI,CAACb,2BAAkB,CAACoB,iBAAiB,EAAE;MACzC,MAAM,IAAID,oCAAmB,CAAC,kBAAkB,EAAE,mBAAmB,CAAC;IACxE;IACA,aAAanB,2BAAkB,CAACoB,iBAAiB,CAACL,OAAO,EAAEC,OAAO,CAAC;EACrE,CAAC;EAAA,OAAAM,kBAAA,CAAA3B,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAmBqBU,kBAAkBA,CAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAA9B,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAAAY,oBAAA;EAAAA,mBAAA,OAAAX,kBAAA,CAAAzB,OAAA,EAAjC,WAAkC0B,OAAe,EAAmB;IACzE,IAAIW,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;MAC7B,IAAI,CAAC3B,2BAAkB,CAACuB,kBAAkB,EAAE;QAC1C,MAAM,IAAIJ,oCAAmB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC;MACzE;MACA,aAAanB,2BAAkB,CAACuB,kBAAkB,CAACR,OAAO,CAAC;IAC7D,CAAC,MAAM;MACL,OAAOA,OAAO;IAChB;EACF,CAAC;EAAA,OAAAU,mBAAA,CAAA9B,KAAA,OAAAkB,SAAA;AAAA;AAAA,SASqBe,kBAAkBA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAApC,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAAAkB,oBAAA;EAAAA,mBAAA,OAAAjB,kBAAA,CAAAzB,OAAA,EAAjC,WACL0B,OAAe,EACfiB,QAAgB,EAED;IAAA,IADfhB,OAAuB,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAG,CAAC,CAAC;IAE5B,IAAI,CAACb,2BAAkB,CAAC4B,kBAAkB,EAAE;MAC1C,MAAM,IAAIT,oCAAmB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC;IACzE;IACA,aAAanB,2BAAkB,CAAC4B,kBAAkB,CAACb,OAAO,EAAEiB,QAAQ,EAAEhB,OAAO,CAAC;EAChF,CAAC;EAAA,OAAAe,mBAAA,CAAApC,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAOqBoB,WAAWA,CAAAC,GAAA;EAAA,OAAAC,YAAA,CAAAxC,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAAAsB,aAAA;EAAAA,YAAA,OAAArB,kBAAA,CAAAzB,OAAA,EAA1B,WAA2B0B,OAAe,EAAgD;IAAA,IAA9CC,OAAwB,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAG,CAAC,CAAC;IAC9E,IAAI,CAACb,2BAAkB,CAACiC,WAAW,EAAE;MACnC,MAAM,IAAId,oCAAmB,CAAC,kBAAkB,EAAE,aAAa,CAAC;IAClE;IACA,aAAanB,2BAAkB,CAACiC,WAAW,CAAClB,OAAO,EAAEC,OAAO,CAAC;EAC/D,CAAC;EAAA,OAAAmB,YAAA,CAAAxC,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAEqBuB,oCAAoCA,CAAA;EAAA,OAAAC,qCAAA,CAAA1C,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAAAwB,sCAAA;EAAAA,qCAAA,OAAAvB,kBAAA,CAAAzB,OAAA,EAAnD,aAAqE;IAC1E,IAAIqC,qBAAQ,CAACC,EAAE,KAAK,SAAS,IAAIrB,iBAAiB,IAAI,IAAI,EAAE;MAC1D;IACF;IACA,IAAMgC,uBAAuB,GAAG,GAAGhC,iBAAiB,iBAAiB;IACrE,aAAa2B,WAAW,CAACK,uBAAuB,EAAE;MAAEC,UAAU,EAAE;IAAK,CAAC,CAAC;EACzE,CAAC;EAAA,OAAAF,qCAAA,CAAA1C,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAMqB2B,SAASA,CAAAC,GAAA;EAAA,OAAAC,UAAA,CAAA/C,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAAA6B,WAAA;EAAAA,UAAA,OAAA5B,kBAAA,CAAAzB,OAAA,EAAxB,WAAyB2B,OAA0B,EAAiB;IACzE,IAAI,CAAChB,2BAAkB,CAACwC,SAAS,EAAE;MACjC,MAAM,IAAIrB,oCAAmB,CAAC,kBAAkB,EAAE,WAAW,CAAC;IAChE;IACA,aAAanB,2BAAkB,CAACwC,SAAS,CAACxB,OAAO,CAAC;EACpD,CAAC;EAAA,OAAA0B,UAAA,CAAA/C,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAOqB8B,SAASA,CAAAC,GAAA;EAAA,OAAAC,UAAA,CAAAlD,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAAAgC,WAAA;EAAAA,UAAA,OAAA/B,kBAAA,CAAAzB,OAAA,EAAxB,WAAyB2B,OAA0B,EAAiB;IACzE,IAAI,CAAChB,2BAAkB,CAAC2C,SAAS,EAAE;MACjC,MAAM,IAAIxB,oCAAmB,CAAC,kBAAkB,EAAE,WAAW,CAAC;IAChE;IACA,aAAanB,2BAAkB,CAAC2C,SAAS,CAAC3B,OAAO,CAAC;EACpD,CAAC;EAAA,OAAA6B,UAAA,CAAAlD,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAOqBiC,kBAAkBA,CAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAArD,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAAAmC,oBAAA;EAAAA,mBAAA,OAAAlC,kBAAA,CAAAzB,OAAA,EAAjC,WACL0B,OAAe,EAEA;IAAA,IADfC,OAA6B,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAG,CAAC,CAAC;IAElC,IAAI,CAACb,2BAAkB,CAAC8C,kBAAkB,EAAE;MAC1C,MAAM,IAAI3B,oCAAmB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC;IACzE;IACA,aAAanB,2BAAkB,CAAC8C,kBAAkB,CAAC/B,OAAO,EAAEC,OAAO,CAAC;EACtE,CAAC;EAAA,OAAAgC,mBAAA,CAAArD,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAOqBoC,kBAAkBA,CAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAAxD,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAAAsC,oBAAA;EAAAA,mBAAA,OAAArC,kBAAA,CAAAzB,OAAA,EAAjC,WAAkC0B,OAAe,EAAqB;IAC3E,IAAI,CAACf,2BAAkB,CAACiD,kBAAkB,EAAE;MAC1C,MAAM,IAAI9B,oCAAmB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC;IACzE;IACA,aAAanB,2BAAkB,CAACiD,kBAAkB,CAAClC,OAAO,CAAC;EAC7D,CAAC;EAAA,OAAAoC,mBAAA,CAAAxD,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAMqBuC,uBAAuBA,CAAA;EAAA,OAAAC,wBAAA,CAAA1D,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAAAwC,yBAAA;EAAAA,wBAAA,OAAAvC,kBAAA,CAAAzB,OAAA,EAAtC,aAA0D;IAC/D,IAAI,CAACW,2BAAkB,CAACoD,uBAAuB,EAAE;MAC/C,MAAM,IAAIjC,oCAAmB,CAAC,kBAAkB,EAAE,yBAAyB,CAAC;IAC9E;IACA,aAAanB,2BAAkB,CAACoD,uBAAuB,CAAC,CAAC;EAC3D,CAAC;EAAA,OAAAC,wBAAA,CAAA1D,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAMqByC,yBAAyBA,CAAA;EAAA,OAAAC,0BAAA,CAAA5D,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAAA0C,2BAAA;EAAAA,0BAAA,OAAAzC,kBAAA,CAAAzB,OAAA,EAAxC,aAA4D;IACjE,IAAI,CAACW,2BAAkB,CAACsD,yBAAyB,EAAE;MACjD,MAAM,IAAInC,oCAAmB,CAAC,kBAAkB,EAAE,2BAA2B,CAAC;IAChF;IACA,aAAanB,2BAAkB,CAACsD,yBAAyB,CAAC,CAAC;EAC7D,CAAC;EAAA,OAAAC,0BAAA,CAAA5D,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAuBqB2C,aAAaA,CAAAC,GAAA,EAAAC,IAAA;EAAA,OAAAC,cAAA,CAAAhE,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAAA8C,eAAA;EAAAA,cAAA,OAAA7C,kBAAA,CAAAzB,OAAA,EAA5B,WACLuE,GAAW,EACX7C,OAAe,EAEoB;IAAA,IADnCC,OAAwB,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAG,CAAC,CAAC;IAE7B,IAAI,CAACb,2BAAkB,CAACwD,aAAa,EAAE;MACrC,MAAM,IAAIrC,oCAAmB,CAAC,kBAAkB,EAAE,eAAe,CAAC;IACpE;IAEA,aAAanB,2BAAkB,CAACwD,aAAa,CAACI,GAAG,EAAE7C,OAAO,EAAA8C,MAAA,CAAAC,MAAA;MACxDC,WAAW,EAAEC,iCAAqB,CAACC;IAAU,GAC1CjD,OAAO,CACX,CAAC;EACJ,CAAC;EAAA,OAAA2C,cAAA,CAAAhE,KAAA,OAAAkB,SAAA;AAAA;AAAA,SA8BqBqD,WAAWA,CAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,YAAA,CAAA1E,KAAA,OAAAkB,SAAA;AAAA;AAAA,SAAAwD,aAAA;EAAAA,YAAA,OAAAvD,kBAAA,CAAAzB,OAAA,EAA1B,WACLiF,GAAW,EACXvD,OAAe,EAEkB;IAAA,IADjCC,OAAgC,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAG,CAAC,CAAC;IAErC,IAAI,CAACb,2BAAkB,CAACkE,WAAW,EAAE;MACnC,MAAM,IAAI/C,oCAAmB,CAAC,kBAAkB,EAAE,aAAa,CAAC;IAClE;IAEA,aAAanB,2BAAkB,CAACkE,WAAW,CAACI,GAAG,EAAEvD,OAAO,EAAA8C,MAAA,CAAAC,MAAA;MACtDC,WAAW,EAAEC,iCAAqB,CAACC,UAAU;MAC7CM,UAAU,EAAEC,gCAAoB,CAACC;IAAc,GAC5CzD,OAAO;MACV0D,UAAU,EAAE,CAAC1D,OAAO,CAAC0D,UAAU,IAAI,MAAM,EAAEC,WAAW,CAAC;IAAC,EACzD,CAAC;EACJ,CAAC;EAAA,OAAAN,YAAA,CAAA1E,KAAA,OAAAkB,SAAA;AAAA;AAkBM,SAAS+D,uBAAuBA,CACrChB,GAAW,EACX7C,OAAe,EACfC,OAAyB,EACzB6D,QAAsE,EACtEC,UAAmB,EACA;EACnB,OAAO,IAAIC,iBAAiB,CAACnB,GAAG,EAAE7C,OAAO,EAAEC,OAAO,EAAE6D,QAAQ,EAAEC,UAAU,CAAC;AAC3E;AAEO,SAASE,gBAAgBA,CAC9BV,GAAW,EACXvD,OAAe,EACfC,OAAiC,EACjC6D,QAAoE,EACxD;EACZ,OAAO,IAAII,UAAU,CAACX,GAAG,EAAEvD,OAAO,EAAEC,OAAO,EAAE6D,QAAQ,CAAC;AACxD;AAAC,IAEqBK,gCAAgC,GAAA3E,OAAA,CAAA2E,gCAAA;EAAA,SAAAA,iCAAA;IAAA,IAAAC,gBAAA,CAAA9F,OAAA,QAAA6F,gCAAA;IAAA,KAG5CE,KAAK,GAAGC,qBAAI,CAACC,EAAE,CAAC,CAAC;IAAA,KACfC,eAAe,GAAG,KAAK;EAAA;EAAA,WAAAC,aAAA,CAAAnG,OAAA,EAAA6F,gCAAA;IAAAO,GAAA;IAAAC,KAAA;MAAA,IAAAC,YAAA,OAAA7E,kBAAA,CAAAzB,OAAA,EAIjC,aAA0C;QACxC,IAAI,CAACW,2BAAkB,CAAC4F,sBAAsB,EAAE;UAC9C,MAAM,IAAIzE,oCAAmB,CAAC,kBAAkB,EAAE,wBAAwB,CAAC;QAC7E;QAEA,IAAI,CAAC0E,kBAAkB,CAAC,CAAC;QACzB,IAAI,CAACN,eAAe,GAAG,IAAI;QAC3B,aAAavF,2BAAkB,CAAC4F,sBAAsB,CAAC,IAAI,CAACP,IAAI,CAAC;MACnE,CAAC;MAAA,SARYS,WAAWA,CAAA;QAAA,OAAAH,YAAA,CAAAhG,KAAA,OAAAkB,SAAA;MAAA;MAAA,OAAXiF,WAAW;IAAA;EAAA;IAAAL,GAAA;IAAAC,KAAA,EAUxB,SAAUK,eAAeA,CAAA,EAAY;MACnC,IAAI,IAAI,CAACR,eAAe,EAAE;QACxBtF,OAAO,CAACC,IAAI,CAAC,iCAAiC,CAAC;QAC/C,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd;EAAC;IAAAuF,GAAA;IAAAO,GAAA,EAED,SAAAA,IAAA,EAA6B;MAC3B,OAAO,IAAI,CAACZ,KAAK;IACnB;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAMD,SAAUO,eAAeA,CAAA,EAAG;MAAA,IAAAC,KAAA;MAC1B,IAAI,IAAI,CAACC,YAAY,EAAE;QACrB;MACF;MAEA,IAAI,CAACA,YAAY,GAAGnG,2BAAkB,CAACoG,WAAW,CAChD,IAAI,CAACC,YAAY,CAAC,CAAC,EACnB,UAACC,KAAuB,EAAK;QAC3B,IAAIA,KAAK,CAACjB,IAAI,KAAKa,KAAI,CAACb,IAAI,EAAE;UAC5B,IAAMR,SAAQ,GAAGqB,KAAI,CAACK,WAAW,CAAC,CAAC;UACnC,IAAI1B,SAAQ,EAAE;YACZA,SAAQ,CAACyB,KAAK,CAACE,IAAS,CAAC;UAC3B;QACF;MACF,CACF,CAAC;IACH;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAED,SAAUG,kBAAkBA,CAAA,EAAG;MAC7B,IAAI,CAAC,IAAI,CAACM,YAAY,EAAE;QACtB;MACF;MACA,IAAI,CAACA,YAAY,CAACM,MAAM,CAAC,CAAC;MAC1B,IAAI,CAACN,YAAY,GAAG,IAAI;IAC1B;EAAC;AAAA;AAAA,IAGUlB,UAAU,GAAA1E,OAAA,CAAA0E,UAAA,aAAAyB,qBAAA;EAGrB,SAAAzB,WACUX,GAAW,EACXvD,OAAe,EACvBC,OAAiC,EACzB6D,QAAoE,EAC5E;IAAA,IAAA8B,mBAAA;IAAA,IAAAC,MAAA;IAAA,IAAAzB,gBAAA,CAAA9F,OAAA,QAAA4F,UAAA;IACA2B,MAAA,GAAA5H,UAAA,OAAAiG,UAAA;IAAQ2B,MAAA,CALAtC,GAAW,GAAXA,GAAW;IAAAsC,MAAA,CACX7F,OAAe,GAAfA,OAAe;IAAA6F,MAAA,CAEf/B,QAAoE,GAApEA,QAAoE;IAI5E,IAAMH,UAAU,GAAI,CAAA1D,OAAO,aAAA2F,mBAAA,GAAP3F,OAAO,CAAE0D,UAAU,qBAAnBiC,mBAAA,CAAqBhC,WAAW,CAAC,CAAC,KACpD,MAA6C;IAE/CiC,MAAA,CAAK5F,OAAO,GAAA6C,MAAA,CAAAC,MAAA;MACVC,WAAW,EAAEC,iCAAqB,CAACC,UAAU;MAC7CM,UAAU,EAAEC,gCAAoB,CAACC;IAAc,GAC5CzD,OAAO;MACV0D,UAAU,EAAVA;IAAU,EACX;IAAC,OAAAkC,MAAA;EACJ;EAAC,IAAAC,UAAA,CAAAxH,OAAA,EAAA4F,UAAA,EAAAyB,qBAAA;EAAA,WAAAlB,aAAA,CAAAnG,OAAA,EAAA4F,UAAA;IAAAQ,GAAA;IAAAC,KAAA,EAED,SAAUW,YAAYA,CAAA,EAAW;MAC/B,OAAO,iCAAiC;IAC1C;EAAC;IAAAZ,GAAA;IAAAC,KAAA,EACD,SAAUa,WAAWA,CAAA,EAA0E;MAC7F,OAAO,IAAI,CAAC1B,QAAQ;IACtB;EAAC;IAAAY,GAAA;IAAAC,KAAA;MAAA,IAAAoB,aAAA,OAAAhG,kBAAA,CAAAzB,OAAA,EAGD,aAAwE;QACtE,IAAI,CAACW,2BAAkB,CAAC+G,oBAAoB,EAAE;UAC5C,MAAM,IAAI5F,oCAAmB,CAAC,kBAAkB,EAAE,sBAAsB,CAAC;QAC3E;QAEA,IAAI,IAAI,CAAC4E,eAAe,CAAC,CAAC,EAAE;UAC1B;QACF;QAEA,IAAI,CAACE,eAAe,CAAC,CAAC;QACtB,IAAMe,MAAM,SAAShH,2BAAkB,CAAC+G,oBAAoB,CAC1D,IAAI,CAACzC,GAAG,EACR,IAAI,CAACvD,OAAO,EACZ,IAAI,CAACsE,IAAI,EACT,IAAI,CAACrE,OACP,CAAC;QACD,IAAI,CAAC6E,kBAAkB,CAAC,CAAC;QAEzB,OAAOmB,MAAM;MACf,CAAC;MAAA,SAnBY9C,WAAWA,CAAA;QAAA,OAAA4C,aAAA,CAAAnH,KAAA,OAAAkB,SAAA;MAAA;MAAA,OAAXqD,WAAW;IAAA;EAAA;AAAA,EA9BMgB,gCAAgC;AAAA,IAoDnDH,iBAAiB,GAAAxE,OAAA,CAAAwE,iBAAA,aAAAkC,sBAAA;EAC5B,SAAAlC,kBACUT,GAAW,EACX4C,QAAgB,EAIxB;IAAA,IAAAC,MAAA;IAAA,IAHQnG,OAAwB,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAG,CAAC,CAAC;IAAA,IAC7BgE,QAAsE,GAAAhE,SAAA,CAAAI,MAAA,OAAAJ,SAAA,MAAAK,SAAA;IAAA,IACtE4D,UAAmB,GAAAjE,SAAA,CAAAI,MAAA,OAAAJ,SAAA,MAAAK,SAAA;IAAA,IAAAiE,gBAAA,CAAA9F,OAAA,QAAA0F,iBAAA;IAE3BoC,MAAA,GAAAnI,UAAA,OAAA+F,iBAAA;IAAQoC,MAAA,CANA7C,GAAW,GAAXA,GAAW;IAAA6C,MAAA,CACXD,QAAgB,GAAhBA,QAAgB;IAAAC,MAAA,CAChBnG,OAAwB,GAAxBA,OAAwB;IAAAmG,MAAA,CACxBtC,QAAsE,GAAtEA,QAAsE;IAAAsC,MAAA,CACtErC,UAAmB,GAAnBA,UAAmB;IAAA,OAAAqC,MAAA;EAG7B;EAAC,IAAAN,UAAA,CAAAxH,OAAA,EAAA0F,iBAAA,EAAAkC,sBAAA;EAAA,WAAAzB,aAAA,CAAAnG,OAAA,EAAA0F,iBAAA;IAAAU,GAAA;IAAAO,GAAA,EAED,SAAAA,IAAA,EAA6B;MAC3B,OAAO,IAAI,CAACkB,QAAQ;IACtB;EAAC;IAAAzB,GAAA;IAAAC,KAAA,EAED,SAAUW,YAAYA,CAAA,EAAW;MAC/B,OAAO,mCAAmC;IAC5C;EAAC;IAAAZ,GAAA;IAAAC,KAAA,EAED,SAAUa,WAAWA,CAAA,EAA4E;MAC/F,OAAO,IAAI,CAAC1B,QAAQ;IACtB;EAAC;IAAAY,GAAA;IAAAC,KAAA;MAAA,IAAA0B,eAAA,OAAAtG,kBAAA,CAAAzB,OAAA,EAMD,aAAqE;QACnE,IAAI,CAACW,2BAAkB,CAACqH,2BAA2B,EAAE;UACnD,MAAM,IAAIlG,oCAAmB,CAAC,kBAAkB,EAAE,6BAA6B,CAAC;QAClF;QAEA,IAAI,IAAI,CAAC4E,eAAe,CAAC,CAAC,EAAE;UAC1B;QACF;QAEA,IAAI,CAACE,eAAe,CAAC,CAAC;QACtB,aAAajG,2BAAkB,CAACqH,2BAA2B,CACzD,IAAI,CAAC/C,GAAG,EACR,IAAI,CAAC4C,QAAQ,EACb,IAAI,CAAC7B,IAAI,EACT,IAAI,CAACrE,OAAO,EACZ,IAAI,CAAC8D,UACP,CAAC;MACH,CAAC;MAAA,SAjBKtB,aAAaA,CAAA;QAAA,OAAA4D,eAAA,CAAAzH,KAAA,OAAAkB,SAAA;MAAA;MAAA,OAAb2C,aAAa;IAAA;EAAA;IAAAiC,GAAA;IAAAC,KAAA;MAAA,IAAA4B,WAAA,OAAAxG,kBAAA,CAAAzB,OAAA,EAwBnB,aAAgD;QAC9C,IAAI,CAACW,2BAAkB,CAACuH,2BAA2B,EAAE;UACnD,MAAM,IAAIpG,oCAAmB,CAAC,kBAAkB,EAAE,6BAA6B,CAAC;QAClF;QAEA,IAAI,IAAI,CAAC4E,eAAe,CAAC,CAAC,EAAE;UAC1B,OAAO;YACLhF,OAAO,EAAE,IAAI,CAACmG,QAAQ;YACtBlG,OAAO,EAAE,IAAI,CAACA,OAAO;YACrBsD,GAAG,EAAE,IAAI,CAACA;UACZ,CAAC;QACH;QAEA,IAAMkD,WAAW,SAASxH,2BAAkB,CAACuH,2BAA2B,CAAC,IAAI,CAAClC,IAAI,CAAC;QACnF,IAAI,CAACQ,kBAAkB,CAAC,CAAC;QACzB,IAAI2B,WAAW,EAAE;UACf,IAAI,CAAC1C,UAAU,GAAG0C,WAAW,CAAC1C,UAAU;UACxC,OAAO,IAAI,CAAC2C,OAAO,CAAC,CAAC;QACvB,CAAC,MAAM;UACL,MAAM,IAAIC,KAAK,CAAC,0CAA0C,CAAC;QAC7D;MACF,CAAC;MAAA,SArBKC,UAAUA,CAAA;QAAA,OAAAL,WAAA,CAAA3H,KAAA,OAAAkB,SAAA;MAAA;MAAA,OAAV8G,UAAU;IAAA;EAAA;IAAAlC,GAAA;IAAAC,KAAA;MAAA,IAAAkC,YAAA,OAAA9G,kBAAA,CAAAzB,OAAA,EA2BhB,aAAmE;QACjE,IAAI,CAACW,2BAAkB,CAACqH,2BAA2B,EAAE;UACnD,MAAM,IAAIlG,oCAAmB,CAAC,kBAAkB,EAAE,6BAA6B,CAAC;QAClF;QAEA,IAAI,IAAI,CAAC4E,eAAe,CAAC,CAAC,EAAE;UAC1B;QACF;QAEA,IAAI,CAACE,eAAe,CAAC,CAAC;QACtB,aAAajG,2BAAkB,CAACqH,2BAA2B,CACzD,IAAI,CAAC/C,GAAG,EACR,IAAI,CAACvD,OAAO,EACZ,IAAI,CAACsE,IAAI,EACT,IAAI,CAACrE,OAAO,EACZ,IAAI,CAAC8D,UACP,CAAC;MACH,CAAC;MAAA,SAjBK+C,WAAWA,CAAA;QAAA,OAAAD,YAAA,CAAAjI,KAAA,OAAAkB,SAAA;MAAA;MAAA,OAAXgH,WAAW;IAAA;EAAA;IAAApC,GAAA;IAAAC,KAAA,EAuBjB,SAAA+B,OAAOA,CAAA,EAAuB;MAC5B,OAAO;QACLnD,GAAG,EAAE,IAAI,CAACA,GAAG;QACbvD,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBC,OAAO,EAAE,IAAI,CAACA,OAAO;QACrB8D,UAAU,EAAE,IAAI,CAACA;MACnB,CAAC;IACH;EAAC;AAAA,EA5GoCI,gCAAgC;AA+GvE,IAAM4C,qBAAqB,GAAG1G,iBAAiB;AAC/C,IAAM2G,sBAAsB,GAAGnG,kBAAkB;AACjD,IAAMoG,eAAe,GAAG/F,WAAW;AACnC,IAAMgG,aAAa,GAAGzF,SAAS;AAC/B,IAAM0F,aAAa,GAAGvF,SAAS;AAAC,IAmFfwF,sBAAsB;AAAA,WAAAC,uBAAA;EAQ9B,SAASC,wBAAwBA,CAACC,UAAkB,EAAE;IAC3D,OAAO,gEAAgEA,UAAU,qBAAqBA,UAAU,EAAE;EACpH;EAACF,uBAAA,CAAAC,wBAAA,GAAAA,wBAAA;EAAA,SASqBE,gCAAgCA,CAAA;IAAA,OAAAC,iCAAA,CAAA7I,KAAA,OAAAkB,SAAA;EAAA;EAAA,SAAA2H,kCAAA;IAAAA,iCAAA,OAAA1H,kBAAA,CAAAzB,OAAA,EAA/C,aAEiD;MAAA,IADtDoJ,cAA6B,GAAA5H,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAG,IAAI;MAEpC,IAAI,CAACb,2BAAkB,CAACuI,gCAAgC,EAAE;QACxD,MAAM,IAAIpH,oCAAmB,CAC3B,kBAAkB,EAClB,yDACF,CAAC;MACH;MAEA,aAAanB,2BAAkB,CAACuI,gCAAgC,CAACE,cAAc,CAAC;IAClF,CAAC;IAAA,OAAAD,iCAAA,CAAA7I,KAAA,OAAAkB,SAAA;EAAA;EAAAuH,uBAAA,CAAAG,gCAAA,GAAAA,gCAAA;EAAA,SAQqBtF,kBAAkBA,CAAAyF,IAAA;IAAA,OAAAC,oBAAA,CAAAhJ,KAAA,OAAAkB,SAAA;EAAA;EAAA,SAAA8H,qBAAA;IAAAA,oBAAA,OAAA7H,kBAAA,CAAAzB,OAAA,EAAjC,WAAkCuJ,MAAc,EAAqB;MAC1E,IAAI,CAAC5I,2BAAkB,CAAC6I,qBAAqB,EAAE;QAC7C,MAAM,IAAI1H,oCAAmB,CAC3B,kBAAkB,EAClB,2CACF,CAAC;MACH;MACA,aAAanB,2BAAkB,CAAC6I,qBAAqB,CAACD,MAAM,CAAC;IAC/D,CAAC;IAAA,OAAAD,oBAAA,CAAAhJ,KAAA,OAAAkB,SAAA;EAAA;EAAAuH,uBAAA,CAAAnF,kBAAA,GAAAA,kBAAA;EAAA,SASqBH,kBAAkBA,CAAAgG,IAAA,EAAAC,IAAA;IAAA,OAAAC,oBAAA,CAAArJ,KAAA,OAAAkB,SAAA;EAAA;EAAA,SAAAmI,qBAAA;IAAAA,oBAAA,OAAAlI,kBAAA,CAAAzB,OAAA,EAAjC,WAAkC4J,SAAiB,EAAEC,OAAe,EAAmB;MAC5F,IAAI,CAAClJ,2BAAkB,CAACmJ,qBAAqB,EAAE;QAC7C,MAAM,IAAIhI,oCAAmB,CAC3B,kBAAkB,EAClB,2CACF,CAAC;MACH;MACA,aAAanB,2BAAkB,CAACmJ,qBAAqB,CAACF,SAAS,EAAEC,OAAO,CAAC;IAC3E,CAAC;IAAA,OAAAF,oBAAA,CAAArJ,KAAA,OAAAkB,SAAA;EAAA;EAAAuH,uBAAA,CAAAtF,kBAAA,GAAAA,kBAAA;EAAA,SAUqBsG,eAAeA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;IAAA,OAAAC,gBAAA,CAAA7J,KAAA,OAAAkB,SAAA;EAAA;EAAA,SAAA2I,iBAAA;IAAAA,gBAAA,OAAA1I,kBAAA,CAAAzB,OAAA,EAA9B,WACL4J,SAAiB,EACjBQ,QAAgB,EAChBC,QAAgB,EACC;MACjB,IAAI,CAAC1J,2BAAkB,CAAC2J,kBAAkB,EAAE;QAC1C,MAAM,IAAIxI,oCAAmB,CAAC,kBAAkB,EAAE,wCAAwC,CAAC;MAC7F;MACA,aAAanB,2BAAkB,CAAC2J,kBAAkB,CAACV,SAAS,EAAEQ,QAAQ,EAAEC,QAAQ,CAAC;IACnF,CAAC;IAAA,OAAAF,gBAAA,CAAA7J,KAAA,OAAAkB,SAAA;EAAA;EAAAuH,uBAAA,CAAAgB,eAAA,GAAAA,eAAA;EAKM,IAAMxH,kBAAkB,GAAAwG,uBAAA,CAAAxG,kBAAA,GAAGmG,sBAAsB;EAIjD,IAAM3G,iBAAiB,GAAAgH,uBAAA,CAAAhH,iBAAA,GAAG0G,qBAAqB;EAI/C,IAAM7F,WAAW,GAAAmG,uBAAA,CAAAnG,WAAA,GAAG+F,eAAe;EAInC,IAAMxF,SAAS,GAAA4F,uBAAA,CAAA5F,SAAA,GAAGyF,aAAa;EAI/B,IAAMtF,SAAS,GAAAyF,uBAAA,CAAAzF,SAAA,GAAGuF,aAAa;AAAC,GAvGxBC,sBAAsB,KAAA5H,OAAA,CAAA4H,sBAAA,GAAtBA,sBAAsB", "ignoreList": []}