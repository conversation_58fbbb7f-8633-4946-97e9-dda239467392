7f5bd6a5a752d70692dcde0e554caf16
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _AnimatedEvent = require("./AnimatedEvent");
var _AnimatedAddition = _interopRequireDefault(require("./nodes/AnimatedAddition"));
var _AnimatedDiffClamp = _interopRequireDefault(require("./nodes/AnimatedDiffClamp"));
var _AnimatedDivision = _interopRequireDefault(require("./nodes/AnimatedDivision"));
var _AnimatedInterpolation = _interopRequireDefault(require("./nodes/AnimatedInterpolation"));
var _AnimatedModulo = _interopRequireDefault(require("./nodes/AnimatedModulo"));
var _AnimatedMultiplication = _interopRequireDefault(require("./nodes/AnimatedMultiplication"));
var _AnimatedNode = _interopRequireDefault(require("./nodes/AnimatedNode"));
var _AnimatedProps = _interopRequireDefault(require("./nodes/AnimatedProps"));
var _AnimatedSubtraction = _interopRequireDefault(require("./nodes/AnimatedSubtraction"));
var _AnimatedTracking = _interopRequireDefault(require("./nodes/AnimatedTracking"));
var _AnimatedValue = _interopRequireDefault(require("./nodes/AnimatedValue"));
var _AnimatedValueXY = _interopRequireDefault(require("./nodes/AnimatedValueXY"));
var _DecayAnimation = _interopRequireDefault(require("./animations/DecayAnimation"));
var _SpringAnimation = _interopRequireDefault(require("./animations/SpringAnimation"));
var _TimingAnimation = _interopRequireDefault(require("./animations/TimingAnimation"));
var _createAnimatedComponent = _interopRequireDefault(require("./createAnimatedComponent"));
var _AnimatedColor = _interopRequireDefault(require("./nodes/AnimatedColor"));
var add = function add(a, b) {
  return new _AnimatedAddition.default(a, b);
};
var subtract = function subtract(a, b) {
  return new _AnimatedSubtraction.default(a, b);
};
var divide = function divide(a, b) {
  return new _AnimatedDivision.default(a, b);
};
var multiply = function multiply(a, b) {
  return new _AnimatedMultiplication.default(a, b);
};
var modulo = function modulo(a, modulus) {
  return new _AnimatedModulo.default(a, modulus);
};
var diffClamp = function diffClamp(a, min, max) {
  return new _AnimatedDiffClamp.default(a, min, max);
};
var _combineCallbacks = function _combineCallbacks(callback, config) {
  if (callback && config.onComplete) {
    return function () {
      config.onComplete && config.onComplete.apply(config, arguments);
      callback && callback.apply(void 0, arguments);
    };
  } else {
    return callback || config.onComplete;
  }
};
var maybeVectorAnim = function maybeVectorAnim(value, config, anim) {
  if (value instanceof _AnimatedValueXY.default) {
    var configX = (0, _objectSpread2.default)({}, config);
    var configY = (0, _objectSpread2.default)({}, config);
    for (var key in config) {
      var _config$key = config[key],
        x = _config$key.x,
        y = _config$key.y;
      if (x !== undefined && y !== undefined) {
        configX[key] = x;
        configY[key] = y;
      }
    }
    var aX = anim(value.x, configX);
    var aY = anim(value.y, configY);
    return parallel([aX, aY], {
      stopTogether: false
    });
  } else if (value instanceof _AnimatedColor.default) {
    var configR = (0, _objectSpread2.default)({}, config);
    var configG = (0, _objectSpread2.default)({}, config);
    var configB = (0, _objectSpread2.default)({}, config);
    var configA = (0, _objectSpread2.default)({}, config);
    for (var _key in config) {
      var _config$_key = config[_key],
        r = _config$_key.r,
        g = _config$_key.g,
        b = _config$_key.b,
        a = _config$_key.a;
      if (r !== undefined && g !== undefined && b !== undefined && a !== undefined) {
        configR[_key] = r;
        configG[_key] = g;
        configB[_key] = b;
        configA[_key] = a;
      }
    }
    var aR = anim(value.r, configR);
    var aG = anim(value.g, configG);
    var aB = anim(value.b, configB);
    var aA = anim(value.a, configA);
    return parallel([aR, aG, aB, aA], {
      stopTogether: false
    });
  }
  return null;
};
var spring = function spring(value, config) {
  var _start = function start(animatedValue, configuration, callback) {
    callback = _combineCallbacks(callback, configuration);
    var singleValue = animatedValue;
    var singleConfig = configuration;
    singleValue.stopTracking();
    if (configuration.toValue instanceof _AnimatedNode.default) {
      singleValue.track(new _AnimatedTracking.default(singleValue, configuration.toValue, _SpringAnimation.default, singleConfig, callback));
    } else {
      singleValue.animate(new _SpringAnimation.default(singleConfig), callback);
    }
  };
  return maybeVectorAnim(value, config, spring) || {
    start: function start(callback) {
      _start(value, config, callback);
    },
    stop: function stop() {
      value.stopAnimation();
    },
    reset: function reset() {
      value.resetAnimation();
    },
    _startNativeLoop: function _startNativeLoop(iterations) {
      var singleConfig = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, config), {}, {
        iterations: iterations
      });
      _start(value, singleConfig);
    },
    _isUsingNativeDriver: function _isUsingNativeDriver() {
      return config.useNativeDriver || false;
    }
  };
};
var timing = function timing(value, config) {
  var _start2 = function start(animatedValue, configuration, callback) {
    callback = _combineCallbacks(callback, configuration);
    var singleValue = animatedValue;
    var singleConfig = configuration;
    singleValue.stopTracking();
    if (configuration.toValue instanceof _AnimatedNode.default) {
      singleValue.track(new _AnimatedTracking.default(singleValue, configuration.toValue, _TimingAnimation.default, singleConfig, callback));
    } else {
      singleValue.animate(new _TimingAnimation.default(singleConfig), callback);
    }
  };
  return maybeVectorAnim(value, config, timing) || {
    start: function start(callback) {
      _start2(value, config, callback);
    },
    stop: function stop() {
      value.stopAnimation();
    },
    reset: function reset() {
      value.resetAnimation();
    },
    _startNativeLoop: function _startNativeLoop(iterations) {
      var singleConfig = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, config), {}, {
        iterations: iterations
      });
      _start2(value, singleConfig);
    },
    _isUsingNativeDriver: function _isUsingNativeDriver() {
      return config.useNativeDriver || false;
    }
  };
};
var decay = function decay(value, config) {
  var _start3 = function start(animatedValue, configuration, callback) {
    callback = _combineCallbacks(callback, configuration);
    var singleValue = animatedValue;
    var singleConfig = configuration;
    singleValue.stopTracking();
    singleValue.animate(new _DecayAnimation.default(singleConfig), callback);
  };
  return maybeVectorAnim(value, config, decay) || {
    start: function start(callback) {
      _start3(value, config, callback);
    },
    stop: function stop() {
      value.stopAnimation();
    },
    reset: function reset() {
      value.resetAnimation();
    },
    _startNativeLoop: function _startNativeLoop(iterations) {
      var singleConfig = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, config), {}, {
        iterations: iterations
      });
      _start3(value, singleConfig);
    },
    _isUsingNativeDriver: function _isUsingNativeDriver() {
      return config.useNativeDriver || false;
    }
  };
};
var sequence = function sequence(animations) {
  var current = 0;
  return {
    start: function start(callback) {
      var onComplete = function onComplete(result) {
        if (!result.finished) {
          callback && callback(result);
          return;
        }
        current++;
        if (current === animations.length) {
          callback && callback(result);
          return;
        }
        animations[current].start(onComplete);
      };
      if (animations.length === 0) {
        callback && callback({
          finished: true
        });
      } else {
        animations[current].start(onComplete);
      }
    },
    stop: function stop() {
      if (current < animations.length) {
        animations[current].stop();
      }
    },
    reset: function reset() {
      animations.forEach(function (animation, idx) {
        if (idx <= current) {
          animation.reset();
        }
      });
      current = 0;
    },
    _startNativeLoop: function _startNativeLoop() {
      throw new Error('Loops run using the native driver cannot contain Animated.sequence animations');
    },
    _isUsingNativeDriver: function _isUsingNativeDriver() {
      return false;
    }
  };
};
var parallel = function parallel(animations, config) {
  var doneCount = 0;
  var hasEnded = {};
  var stopTogether = !(config && config.stopTogether === false);
  var result = {
    start: function start(callback) {
      if (doneCount === animations.length) {
        callback && callback({
          finished: true
        });
        return;
      }
      animations.forEach(function (animation, idx) {
        var cb = function cb(endResult) {
          hasEnded[idx] = true;
          doneCount++;
          if (doneCount === animations.length) {
            doneCount = 0;
            callback && callback(endResult);
            return;
          }
          if (!endResult.finished && stopTogether) {
            result.stop();
          }
        };
        if (!animation) {
          cb({
            finished: true
          });
        } else {
          animation.start(cb);
        }
      });
    },
    stop: function stop() {
      animations.forEach(function (animation, idx) {
        !hasEnded[idx] && animation.stop();
        hasEnded[idx] = true;
      });
    },
    reset: function reset() {
      animations.forEach(function (animation, idx) {
        animation.reset();
        hasEnded[idx] = false;
        doneCount = 0;
      });
    },
    _startNativeLoop: function _startNativeLoop() {
      throw new Error('Loops run using the native driver cannot contain Animated.parallel animations');
    },
    _isUsingNativeDriver: function _isUsingNativeDriver() {
      return false;
    }
  };
  return result;
};
var delay = function delay(time) {
  return timing(new _AnimatedValue.default(0), {
    toValue: 0,
    delay: time,
    duration: 0,
    useNativeDriver: false
  });
};
var stagger = function stagger(time, animations) {
  return parallel(animations.map(function (animation, i) {
    return sequence([delay(time * i), animation]);
  }));
};
var loop = function loop(animation, _temp) {
  var _ref = _temp === void 0 ? {} : _temp,
    _ref$iterations = _ref.iterations,
    iterations = _ref$iterations === void 0 ? -1 : _ref$iterations,
    _ref$resetBeforeItera = _ref.resetBeforeIteration,
    resetBeforeIteration = _ref$resetBeforeItera === void 0 ? true : _ref$resetBeforeItera;
  var isFinished = false;
  var iterationsSoFar = 0;
  return {
    start: function start(callback) {
      var restart = function restart(result) {
        if (result === void 0) {
          result = {
            finished: true
          };
        }
        if (isFinished || iterationsSoFar === iterations || result.finished === false) {
          callback && callback(result);
        } else {
          iterationsSoFar++;
          resetBeforeIteration && animation.reset();
          animation.start(restart);
        }
      };
      if (!animation || iterations === 0) {
        callback && callback({
          finished: true
        });
      } else {
        if (animation._isUsingNativeDriver()) {
          animation._startNativeLoop(iterations);
        } else {
          restart();
        }
      }
    },
    stop: function stop() {
      isFinished = true;
      animation.stop();
    },
    reset: function reset() {
      iterationsSoFar = 0;
      isFinished = false;
      animation.reset();
    },
    _startNativeLoop: function _startNativeLoop() {
      throw new Error('Loops run using the native driver cannot contain Animated.loop animations');
    },
    _isUsingNativeDriver: function _isUsingNativeDriver() {
      return animation._isUsingNativeDriver();
    }
  };
};
function forkEvent(event, listener) {
  if (!event) {
    return listener;
  } else if (event instanceof _AnimatedEvent.AnimatedEvent) {
    event.__addListener(listener);
    return event;
  } else {
    return function () {
      typeof event === 'function' && event.apply(void 0, arguments);
      listener.apply(void 0, arguments);
    };
  }
}
function unforkEvent(event, listener) {
  if (event && event instanceof _AnimatedEvent.AnimatedEvent) {
    event.__removeListener(listener);
  }
}
var event = function event(argMapping, config) {
  var animatedEvent = new _AnimatedEvent.AnimatedEvent(argMapping, config);
  if (animatedEvent.__isNative) {
    return animatedEvent;
  } else {
    return animatedEvent.__getHandler();
  }
};
var _default = exports.default = {
  Value: _AnimatedValue.default,
  ValueXY: _AnimatedValueXY.default,
  Color: _AnimatedColor.default,
  Interpolation: _AnimatedInterpolation.default,
  Node: _AnimatedNode.default,
  decay: decay,
  timing: timing,
  spring: spring,
  add: add,
  subtract: subtract,
  divide: divide,
  multiply: multiply,
  modulo: modulo,
  diffClamp: diffClamp,
  delay: delay,
  sequence: sequence,
  parallel: parallel,
  stagger: stagger,
  loop: loop,
  event: event,
  createAnimatedComponent: _createAnimatedComponent.default,
  attachNativeEvent: _AnimatedEvent.attachNativeEvent,
  forkEvent: forkEvent,
  unforkEvent: unforkEvent,
  Event: _AnimatedEvent.AnimatedEvent
};
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsImRlZmF1bHQiLCJleHBvcnRzIiwiX19lc01vZHVsZSIsIl9vYmplY3RTcHJlYWQyIiwiX0FuaW1hdGVkRXZlbnQiLCJfQW5pbWF0ZWRBZGRpdGlvbiIsIl9BbmltYXRlZERpZmZDbGFtcCIsIl9BbmltYXRlZERpdmlzaW9uIiwiX0FuaW1hdGVkSW50ZXJwb2xhdGlvbiIsIl9BbmltYXRlZE1vZHVsbyIsIl9BbmltYXRlZE11bHRpcGxpY2F0aW9uIiwiX0FuaW1hdGVkTm9kZSIsIl9BbmltYXRlZFByb3BzIiwiX0FuaW1hdGVkU3VidHJhY3Rpb24iLCJfQW5pbWF0ZWRUcmFja2luZyIsIl9BbmltYXRlZFZhbHVlIiwiX0FuaW1hdGVkVmFsdWVYWSIsIl9EZWNheUFuaW1hdGlvbiIsIl9TcHJpbmdBbmltYXRpb24iLCJfVGltaW5nQW5pbWF0aW9uIiwiX2NyZWF0ZUFuaW1hdGVkQ29tcG9uZW50IiwiX0FuaW1hdGVkQ29sb3IiLCJhZGQiLCJhIiwiYiIsInN1YnRyYWN0IiwiZGl2aWRlIiwibXVsdGlwbHkiLCJtb2R1bG8iLCJtb2R1bHVzIiwiZGlmZkNsYW1wIiwibWluIiwibWF4IiwiX2NvbWJpbmVDYWxsYmFja3MiLCJjYWxsYmFjayIsImNvbmZpZyIsIm9uQ29tcGxldGUiLCJhcHBseSIsImFyZ3VtZW50cyIsIm1heWJlVmVjdG9yQW5pbSIsInZhbHVlIiwiYW5pbSIsImNvbmZpZ1giLCJjb25maWdZIiwia2V5IiwiX2NvbmZpZyRrZXkiLCJ4IiwieSIsInVuZGVmaW5lZCIsImFYIiwiYVkiLCJwYXJhbGxlbCIsInN0b3BUb2dldGhlciIsImNvbmZpZ1IiLCJjb25maWdHIiwiY29uZmlnQiIsImNvbmZpZ0EiLCJfa2V5IiwiX2NvbmZpZyRfa2V5IiwiciIsImciLCJhUiIsImFHIiwiYUIiLCJhQSIsInNwcmluZyIsIl9zdGFydCIsInN0YXJ0IiwiYW5pbWF0ZWRWYWx1ZSIsImNvbmZpZ3VyYXRpb24iLCJzaW5nbGVWYWx1ZSIsInNpbmdsZUNvbmZpZyIsInN0b3BUcmFja2luZyIsInRvVmFsdWUiLCJ0cmFjayIsImFuaW1hdGUiLCJzdG9wIiwic3RvcEFuaW1hdGlvbiIsInJlc2V0IiwicmVzZXRBbmltYXRpb24iLCJfc3RhcnROYXRpdmVMb29wIiwiaXRlcmF0aW9ucyIsIl9pc1VzaW5nTmF0aXZlRHJpdmVyIiwidXNlTmF0aXZlRHJpdmVyIiwidGltaW5nIiwiX3N0YXJ0MiIsImRlY2F5IiwiX3N0YXJ0MyIsInNlcXVlbmNlIiwiYW5pbWF0aW9ucyIsImN1cnJlbnQiLCJyZXN1bHQiLCJmaW5pc2hlZCIsImxlbmd0aCIsImZvckVhY2giLCJhbmltYXRpb24iLCJpZHgiLCJFcnJvciIsImRvbmVDb3VudCIsImhhc0VuZGVkIiwiY2IiLCJlbmRSZXN1bHQiLCJkZWxheSIsInRpbWUiLCJkdXJhdGlvbiIsInN0YWdnZXIiLCJtYXAiLCJpIiwibG9vcCIsIl90ZW1wIiwiX3JlZiIsIl9yZWYkaXRlcmF0aW9ucyIsIl9yZWYkcmVzZXRCZWZvcmVJdGVyYSIsInJlc2V0QmVmb3JlSXRlcmF0aW9uIiwiaXNGaW5pc2hlZCIsIml0ZXJhdGlvbnNTb0ZhciIsInJlc3RhcnQiLCJmb3JrRXZlbnQiLCJldmVudCIsImxpc3RlbmVyIiwiQW5pbWF0ZWRFdmVudCIsIl9fYWRkTGlzdGVuZXIiLCJ1bmZvcmtFdmVudCIsIl9fcmVtb3ZlTGlzdGVuZXIiLCJhcmdNYXBwaW5nIiwiYW5pbWF0ZWRFdmVudCIsIl9faXNOYXRpdmUiLCJfX2dldEhhbmRsZXIiLCJfZGVmYXVsdCIsIlZhbHVlIiwiVmFsdWVYWSIsIkNvbG9yIiwiSW50ZXJwb2xhdGlvbiIsIk5vZGUiLCJjcmVhdGVBbmltYXRlZENvbXBvbmVudCIsImF0dGFjaE5hdGl2ZUV2ZW50IiwiRXZlbnQiLCJtb2R1bGUiXSwic291cmNlcyI6WyJBbmltYXRlZEltcGxlbWVudGF0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBcbiAqIEBmb3JtYXRcbiAqL1xuXG4ndXNlIHN0cmljdCc7XG5cbnZhciBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0ID0gcmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0XCIpLmRlZmF1bHQ7XG5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlO1xuZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwO1xudmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyXCIpKTtcbnZhciBfQW5pbWF0ZWRFdmVudCA9IHJlcXVpcmUoXCIuL0FuaW1hdGVkRXZlbnRcIik7XG52YXIgX0FuaW1hdGVkQWRkaXRpb24gPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuL25vZGVzL0FuaW1hdGVkQWRkaXRpb25cIikpO1xudmFyIF9BbmltYXRlZERpZmZDbGFtcCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4vbm9kZXMvQW5pbWF0ZWREaWZmQ2xhbXBcIikpO1xudmFyIF9BbmltYXRlZERpdmlzaW9uID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi9ub2Rlcy9BbmltYXRlZERpdmlzaW9uXCIpKTtcbnZhciBfQW5pbWF0ZWRJbnRlcnBvbGF0aW9uID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi9ub2Rlcy9BbmltYXRlZEludGVycG9sYXRpb25cIikpO1xudmFyIF9BbmltYXRlZE1vZHVsbyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4vbm9kZXMvQW5pbWF0ZWRNb2R1bG9cIikpO1xudmFyIF9BbmltYXRlZE11bHRpcGxpY2F0aW9uID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi9ub2Rlcy9BbmltYXRlZE11bHRpcGxpY2F0aW9uXCIpKTtcbnZhciBfQW5pbWF0ZWROb2RlID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi9ub2Rlcy9BbmltYXRlZE5vZGVcIikpO1xudmFyIF9BbmltYXRlZFByb3BzID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi9ub2Rlcy9BbmltYXRlZFByb3BzXCIpKTtcbnZhciBfQW5pbWF0ZWRTdWJ0cmFjdGlvbiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4vbm9kZXMvQW5pbWF0ZWRTdWJ0cmFjdGlvblwiKSk7XG52YXIgX0FuaW1hdGVkVHJhY2tpbmcgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuL25vZGVzL0FuaW1hdGVkVHJhY2tpbmdcIikpO1xudmFyIF9BbmltYXRlZFZhbHVlID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi9ub2Rlcy9BbmltYXRlZFZhbHVlXCIpKTtcbnZhciBfQW5pbWF0ZWRWYWx1ZVhZID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi9ub2Rlcy9BbmltYXRlZFZhbHVlWFlcIikpO1xudmFyIF9EZWNheUFuaW1hdGlvbiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4vYW5pbWF0aW9ucy9EZWNheUFuaW1hdGlvblwiKSk7XG52YXIgX1NwcmluZ0FuaW1hdGlvbiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4vYW5pbWF0aW9ucy9TcHJpbmdBbmltYXRpb25cIikpO1xudmFyIF9UaW1pbmdBbmltYXRpb24gPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuL2FuaW1hdGlvbnMvVGltaW5nQW5pbWF0aW9uXCIpKTtcbnZhciBfY3JlYXRlQW5pbWF0ZWRDb21wb25lbnQgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuL2NyZWF0ZUFuaW1hdGVkQ29tcG9uZW50XCIpKTtcbnZhciBfQW5pbWF0ZWRDb2xvciA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4vbm9kZXMvQW5pbWF0ZWRDb2xvclwiKSk7XG52YXIgYWRkID0gZnVuY3Rpb24gYWRkKGEsIGIpIHtcbiAgcmV0dXJuIG5ldyBfQW5pbWF0ZWRBZGRpdGlvbi5kZWZhdWx0KGEsIGIpO1xufTtcbnZhciBzdWJ0cmFjdCA9IGZ1bmN0aW9uIHN1YnRyYWN0KGEsIGIpIHtcbiAgcmV0dXJuIG5ldyBfQW5pbWF0ZWRTdWJ0cmFjdGlvbi5kZWZhdWx0KGEsIGIpO1xufTtcbnZhciBkaXZpZGUgPSBmdW5jdGlvbiBkaXZpZGUoYSwgYikge1xuICByZXR1cm4gbmV3IF9BbmltYXRlZERpdmlzaW9uLmRlZmF1bHQoYSwgYik7XG59O1xudmFyIG11bHRpcGx5ID0gZnVuY3Rpb24gbXVsdGlwbHkoYSwgYikge1xuICByZXR1cm4gbmV3IF9BbmltYXRlZE11bHRpcGxpY2F0aW9uLmRlZmF1bHQoYSwgYik7XG59O1xudmFyIG1vZHVsbyA9IGZ1bmN0aW9uIG1vZHVsbyhhLCBtb2R1bHVzKSB7XG4gIHJldHVybiBuZXcgX0FuaW1hdGVkTW9kdWxvLmRlZmF1bHQoYSwgbW9kdWx1cyk7XG59O1xudmFyIGRpZmZDbGFtcCA9IGZ1bmN0aW9uIGRpZmZDbGFtcChhLCBtaW4sIG1heCkge1xuICByZXR1cm4gbmV3IF9BbmltYXRlZERpZmZDbGFtcC5kZWZhdWx0KGEsIG1pbiwgbWF4KTtcbn07XG52YXIgX2NvbWJpbmVDYWxsYmFja3MgPSBmdW5jdGlvbiBfY29tYmluZUNhbGxiYWNrcyhjYWxsYmFjaywgY29uZmlnKSB7XG4gIGlmIChjYWxsYmFjayAmJiBjb25maWcub25Db21wbGV0ZSkge1xuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICBjb25maWcub25Db21wbGV0ZSAmJiBjb25maWcub25Db21wbGV0ZSguLi5hcmd1bWVudHMpO1xuICAgICAgY2FsbGJhY2sgJiYgY2FsbGJhY2soLi4uYXJndW1lbnRzKTtcbiAgICB9O1xuICB9IGVsc2Uge1xuICAgIHJldHVybiBjYWxsYmFjayB8fCBjb25maWcub25Db21wbGV0ZTtcbiAgfVxufTtcbnZhciBtYXliZVZlY3RvckFuaW0gPSBmdW5jdGlvbiBtYXliZVZlY3RvckFuaW0odmFsdWUsIGNvbmZpZywgYW5pbSkge1xuICBpZiAodmFsdWUgaW5zdGFuY2VvZiBfQW5pbWF0ZWRWYWx1ZVhZLmRlZmF1bHQpIHtcbiAgICB2YXIgY29uZmlnWCA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgY29uZmlnKTtcbiAgICB2YXIgY29uZmlnWSA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgY29uZmlnKTtcbiAgICBmb3IgKHZhciBrZXkgaW4gY29uZmlnKSB7XG4gICAgICB2YXIgX2NvbmZpZyRrZXkgPSBjb25maWdba2V5XSxcbiAgICAgICAgeCA9IF9jb25maWcka2V5LngsXG4gICAgICAgIHkgPSBfY29uZmlnJGtleS55O1xuICAgICAgaWYgKHggIT09IHVuZGVmaW5lZCAmJiB5ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgY29uZmlnWFtrZXldID0geDtcbiAgICAgICAgY29uZmlnWVtrZXldID0geTtcbiAgICAgIH1cbiAgICB9XG4gICAgdmFyIGFYID0gYW5pbSh2YWx1ZS54LCBjb25maWdYKTtcbiAgICB2YXIgYVkgPSBhbmltKHZhbHVlLnksIGNvbmZpZ1kpO1xuICAgIC8vIFdlIHVzZSBgc3RvcFRvZ2V0aGVyOiBmYWxzZWAgaGVyZSBiZWNhdXNlIG90aGVyd2lzZSB0cmFja2luZyB3aWxsIGJyZWFrXG4gICAgLy8gYmVjYXVzZSB0aGUgc2Vjb25kIGFuaW1hdGlvbiB3aWxsIGdldCBzdG9wcGVkIGJlZm9yZSBpdCBjYW4gdXBkYXRlLlxuICAgIHJldHVybiBwYXJhbGxlbChbYVgsIGFZXSwge1xuICAgICAgc3RvcFRvZ2V0aGVyOiBmYWxzZVxuICAgIH0pO1xuICB9IGVsc2UgaWYgKHZhbHVlIGluc3RhbmNlb2YgX0FuaW1hdGVkQ29sb3IuZGVmYXVsdCkge1xuICAgIHZhciBjb25maWdSID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBjb25maWcpO1xuICAgIHZhciBjb25maWdHID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBjb25maWcpO1xuICAgIHZhciBjb25maWdCID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBjb25maWcpO1xuICAgIHZhciBjb25maWdBID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBjb25maWcpO1xuICAgIGZvciAodmFyIF9rZXkgaW4gY29uZmlnKSB7XG4gICAgICB2YXIgX2NvbmZpZyRfa2V5ID0gY29uZmlnW19rZXldLFxuICAgICAgICByID0gX2NvbmZpZyRfa2V5LnIsXG4gICAgICAgIGcgPSBfY29uZmlnJF9rZXkuZyxcbiAgICAgICAgYiA9IF9jb25maWckX2tleS5iLFxuICAgICAgICBhID0gX2NvbmZpZyRfa2V5LmE7XG4gICAgICBpZiAociAhPT0gdW5kZWZpbmVkICYmIGcgIT09IHVuZGVmaW5lZCAmJiBiICE9PSB1bmRlZmluZWQgJiYgYSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGNvbmZpZ1JbX2tleV0gPSByO1xuICAgICAgICBjb25maWdHW19rZXldID0gZztcbiAgICAgICAgY29uZmlnQltfa2V5XSA9IGI7XG4gICAgICAgIGNvbmZpZ0FbX2tleV0gPSBhO1xuICAgICAgfVxuICAgIH1cbiAgICB2YXIgYVIgPSBhbmltKHZhbHVlLnIsIGNvbmZpZ1IpO1xuICAgIHZhciBhRyA9IGFuaW0odmFsdWUuZywgY29uZmlnRyk7XG4gICAgdmFyIGFCID0gYW5pbSh2YWx1ZS5iLCBjb25maWdCKTtcbiAgICB2YXIgYUEgPSBhbmltKHZhbHVlLmEsIGNvbmZpZ0EpO1xuICAgIC8vIFdlIHVzZSBgc3RvcFRvZ2V0aGVyOiBmYWxzZWAgaGVyZSBiZWNhdXNlIG90aGVyd2lzZSB0cmFja2luZyB3aWxsIGJyZWFrXG4gICAgLy8gYmVjYXVzZSB0aGUgc2Vjb25kIGFuaW1hdGlvbiB3aWxsIGdldCBzdG9wcGVkIGJlZm9yZSBpdCBjYW4gdXBkYXRlLlxuICAgIHJldHVybiBwYXJhbGxlbChbYVIsIGFHLCBhQiwgYUFdLCB7XG4gICAgICBzdG9wVG9nZXRoZXI6IGZhbHNlXG4gICAgfSk7XG4gIH1cbiAgcmV0dXJuIG51bGw7XG59O1xudmFyIHNwcmluZyA9IGZ1bmN0aW9uIHNwcmluZyh2YWx1ZSwgY29uZmlnKSB7XG4gIHZhciBfc3RhcnQgPSBmdW5jdGlvbiBzdGFydChhbmltYXRlZFZhbHVlLCBjb25maWd1cmF0aW9uLCBjYWxsYmFjaykge1xuICAgIGNhbGxiYWNrID0gX2NvbWJpbmVDYWxsYmFja3MoY2FsbGJhY2ssIGNvbmZpZ3VyYXRpb24pO1xuICAgIHZhciBzaW5nbGVWYWx1ZSA9IGFuaW1hdGVkVmFsdWU7XG4gICAgdmFyIHNpbmdsZUNvbmZpZyA9IGNvbmZpZ3VyYXRpb247XG4gICAgc2luZ2xlVmFsdWUuc3RvcFRyYWNraW5nKCk7XG4gICAgaWYgKGNvbmZpZ3VyYXRpb24udG9WYWx1ZSBpbnN0YW5jZW9mIF9BbmltYXRlZE5vZGUuZGVmYXVsdCkge1xuICAgICAgc2luZ2xlVmFsdWUudHJhY2sobmV3IF9BbmltYXRlZFRyYWNraW5nLmRlZmF1bHQoc2luZ2xlVmFsdWUsIGNvbmZpZ3VyYXRpb24udG9WYWx1ZSwgX1NwcmluZ0FuaW1hdGlvbi5kZWZhdWx0LCBzaW5nbGVDb25maWcsIGNhbGxiYWNrKSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNpbmdsZVZhbHVlLmFuaW1hdGUobmV3IF9TcHJpbmdBbmltYXRpb24uZGVmYXVsdChzaW5nbGVDb25maWcpLCBjYWxsYmFjayk7XG4gICAgfVxuICB9O1xuICByZXR1cm4gbWF5YmVWZWN0b3JBbmltKHZhbHVlLCBjb25maWcsIHNwcmluZykgfHwge1xuICAgIHN0YXJ0OiBmdW5jdGlvbiBzdGFydChjYWxsYmFjaykge1xuICAgICAgX3N0YXJ0KHZhbHVlLCBjb25maWcsIGNhbGxiYWNrKTtcbiAgICB9LFxuICAgIHN0b3A6IGZ1bmN0aW9uIHN0b3AoKSB7XG4gICAgICB2YWx1ZS5zdG9wQW5pbWF0aW9uKCk7XG4gICAgfSxcbiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7XG4gICAgICB2YWx1ZS5yZXNldEFuaW1hdGlvbigpO1xuICAgIH0sXG4gICAgX3N0YXJ0TmF0aXZlTG9vcDogZnVuY3Rpb24gX3N0YXJ0TmF0aXZlTG9vcChpdGVyYXRpb25zKSB7XG4gICAgICB2YXIgc2luZ2xlQ29uZmlnID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgY29uZmlnKSwge30sIHtcbiAgICAgICAgaXRlcmF0aW9uc1xuICAgICAgfSk7XG4gICAgICBfc3RhcnQodmFsdWUsIHNpbmdsZUNvbmZpZyk7XG4gICAgfSxcbiAgICBfaXNVc2luZ05hdGl2ZURyaXZlcjogZnVuY3Rpb24gX2lzVXNpbmdOYXRpdmVEcml2ZXIoKSB7XG4gICAgICByZXR1cm4gY29uZmlnLnVzZU5hdGl2ZURyaXZlciB8fCBmYWxzZTtcbiAgICB9XG4gIH07XG59O1xudmFyIHRpbWluZyA9IGZ1bmN0aW9uIHRpbWluZyh2YWx1ZSwgY29uZmlnKSB7XG4gIHZhciBfc3RhcnQyID0gZnVuY3Rpb24gc3RhcnQoYW5pbWF0ZWRWYWx1ZSwgY29uZmlndXJhdGlvbiwgY2FsbGJhY2spIHtcbiAgICBjYWxsYmFjayA9IF9jb21iaW5lQ2FsbGJhY2tzKGNhbGxiYWNrLCBjb25maWd1cmF0aW9uKTtcbiAgICB2YXIgc2luZ2xlVmFsdWUgPSBhbmltYXRlZFZhbHVlO1xuICAgIHZhciBzaW5nbGVDb25maWcgPSBjb25maWd1cmF0aW9uO1xuICAgIHNpbmdsZVZhbHVlLnN0b3BUcmFja2luZygpO1xuICAgIGlmIChjb25maWd1cmF0aW9uLnRvVmFsdWUgaW5zdGFuY2VvZiBfQW5pbWF0ZWROb2RlLmRlZmF1bHQpIHtcbiAgICAgIHNpbmdsZVZhbHVlLnRyYWNrKG5ldyBfQW5pbWF0ZWRUcmFja2luZy5kZWZhdWx0KHNpbmdsZVZhbHVlLCBjb25maWd1cmF0aW9uLnRvVmFsdWUsIF9UaW1pbmdBbmltYXRpb24uZGVmYXVsdCwgc2luZ2xlQ29uZmlnLCBjYWxsYmFjaykpO1xuICAgIH0gZWxzZSB7XG4gICAgICBzaW5nbGVWYWx1ZS5hbmltYXRlKG5ldyBfVGltaW5nQW5pbWF0aW9uLmRlZmF1bHQoc2luZ2xlQ29uZmlnKSwgY2FsbGJhY2spO1xuICAgIH1cbiAgfTtcbiAgcmV0dXJuIG1heWJlVmVjdG9yQW5pbSh2YWx1ZSwgY29uZmlnLCB0aW1pbmcpIHx8IHtcbiAgICBzdGFydDogZnVuY3Rpb24gc3RhcnQoY2FsbGJhY2spIHtcbiAgICAgIF9zdGFydDIodmFsdWUsIGNvbmZpZywgY2FsbGJhY2spO1xuICAgIH0sXG4gICAgc3RvcDogZnVuY3Rpb24gc3RvcCgpIHtcbiAgICAgIHZhbHVlLnN0b3BBbmltYXRpb24oKTtcbiAgICB9LFxuICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHtcbiAgICAgIHZhbHVlLnJlc2V0QW5pbWF0aW9uKCk7XG4gICAgfSxcbiAgICBfc3RhcnROYXRpdmVMb29wOiBmdW5jdGlvbiBfc3RhcnROYXRpdmVMb29wKGl0ZXJhdGlvbnMpIHtcbiAgICAgIHZhciBzaW5nbGVDb25maWcgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBjb25maWcpLCB7fSwge1xuICAgICAgICBpdGVyYXRpb25zXG4gICAgICB9KTtcbiAgICAgIF9zdGFydDIodmFsdWUsIHNpbmdsZUNvbmZpZyk7XG4gICAgfSxcbiAgICBfaXNVc2luZ05hdGl2ZURyaXZlcjogZnVuY3Rpb24gX2lzVXNpbmdOYXRpdmVEcml2ZXIoKSB7XG4gICAgICByZXR1cm4gY29uZmlnLnVzZU5hdGl2ZURyaXZlciB8fCBmYWxzZTtcbiAgICB9XG4gIH07XG59O1xudmFyIGRlY2F5ID0gZnVuY3Rpb24gZGVjYXkodmFsdWUsIGNvbmZpZykge1xuICB2YXIgX3N0YXJ0MyA9IGZ1bmN0aW9uIHN0YXJ0KGFuaW1hdGVkVmFsdWUsIGNvbmZpZ3VyYXRpb24sIGNhbGxiYWNrKSB7XG4gICAgY2FsbGJhY2sgPSBfY29tYmluZUNhbGxiYWNrcyhjYWxsYmFjaywgY29uZmlndXJhdGlvbik7XG4gICAgdmFyIHNpbmdsZVZhbHVlID0gYW5pbWF0ZWRWYWx1ZTtcbiAgICB2YXIgc2luZ2xlQ29uZmlnID0gY29uZmlndXJhdGlvbjtcbiAgICBzaW5nbGVWYWx1ZS5zdG9wVHJhY2tpbmcoKTtcbiAgICBzaW5nbGVWYWx1ZS5hbmltYXRlKG5ldyBfRGVjYXlBbmltYXRpb24uZGVmYXVsdChzaW5nbGVDb25maWcpLCBjYWxsYmFjayk7XG4gIH07XG4gIHJldHVybiBtYXliZVZlY3RvckFuaW0odmFsdWUsIGNvbmZpZywgZGVjYXkpIHx8IHtcbiAgICBzdGFydDogZnVuY3Rpb24gc3RhcnQoY2FsbGJhY2spIHtcbiAgICAgIF9zdGFydDModmFsdWUsIGNvbmZpZywgY2FsbGJhY2spO1xuICAgIH0sXG4gICAgc3RvcDogZnVuY3Rpb24gc3RvcCgpIHtcbiAgICAgIHZhbHVlLnN0b3BBbmltYXRpb24oKTtcbiAgICB9LFxuICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHtcbiAgICAgIHZhbHVlLnJlc2V0QW5pbWF0aW9uKCk7XG4gICAgfSxcbiAgICBfc3RhcnROYXRpdmVMb29wOiBmdW5jdGlvbiBfc3RhcnROYXRpdmVMb29wKGl0ZXJhdGlvbnMpIHtcbiAgICAgIHZhciBzaW5nbGVDb25maWcgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBjb25maWcpLCB7fSwge1xuICAgICAgICBpdGVyYXRpb25zXG4gICAgICB9KTtcbiAgICAgIF9zdGFydDModmFsdWUsIHNpbmdsZUNvbmZpZyk7XG4gICAgfSxcbiAgICBfaXNVc2luZ05hdGl2ZURyaXZlcjogZnVuY3Rpb24gX2lzVXNpbmdOYXRpdmVEcml2ZXIoKSB7XG4gICAgICByZXR1cm4gY29uZmlnLnVzZU5hdGl2ZURyaXZlciB8fCBmYWxzZTtcbiAgICB9XG4gIH07XG59O1xudmFyIHNlcXVlbmNlID0gZnVuY3Rpb24gc2VxdWVuY2UoYW5pbWF0aW9ucykge1xuICB2YXIgY3VycmVudCA9IDA7XG4gIHJldHVybiB7XG4gICAgc3RhcnQ6IGZ1bmN0aW9uIHN0YXJ0KGNhbGxiYWNrKSB7XG4gICAgICB2YXIgb25Db21wbGV0ZSA9IGZ1bmN0aW9uIG9uQ29tcGxldGUocmVzdWx0KSB7XG4gICAgICAgIGlmICghcmVzdWx0LmZpbmlzaGVkKSB7XG4gICAgICAgICAgY2FsbGJhY2sgJiYgY2FsbGJhY2socmVzdWx0KTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgY3VycmVudCsrO1xuICAgICAgICBpZiAoY3VycmVudCA9PT0gYW5pbWF0aW9ucy5sZW5ndGgpIHtcbiAgICAgICAgICBjYWxsYmFjayAmJiBjYWxsYmFjayhyZXN1bHQpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBhbmltYXRpb25zW2N1cnJlbnRdLnN0YXJ0KG9uQ29tcGxldGUpO1xuICAgICAgfTtcbiAgICAgIGlmIChhbmltYXRpb25zLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICBjYWxsYmFjayAmJiBjYWxsYmFjayh7XG4gICAgICAgICAgZmluaXNoZWQ6IHRydWVcbiAgICAgICAgfSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBhbmltYXRpb25zW2N1cnJlbnRdLnN0YXJ0KG9uQ29tcGxldGUpO1xuICAgICAgfVxuICAgIH0sXG4gICAgc3RvcDogZnVuY3Rpb24gc3RvcCgpIHtcbiAgICAgIGlmIChjdXJyZW50IDwgYW5pbWF0aW9ucy5sZW5ndGgpIHtcbiAgICAgICAgYW5pbWF0aW9uc1tjdXJyZW50XS5zdG9wKCk7XG4gICAgICB9XG4gICAgfSxcbiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7XG4gICAgICBhbmltYXRpb25zLmZvckVhY2goKGFuaW1hdGlvbiwgaWR4KSA9PiB7XG4gICAgICAgIGlmIChpZHggPD0gY3VycmVudCkge1xuICAgICAgICAgIGFuaW1hdGlvbi5yZXNldCgpO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICAgIGN1cnJlbnQgPSAwO1xuICAgIH0sXG4gICAgX3N0YXJ0TmF0aXZlTG9vcDogZnVuY3Rpb24gX3N0YXJ0TmF0aXZlTG9vcCgpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTG9vcHMgcnVuIHVzaW5nIHRoZSBuYXRpdmUgZHJpdmVyIGNhbm5vdCBjb250YWluIEFuaW1hdGVkLnNlcXVlbmNlIGFuaW1hdGlvbnMnKTtcbiAgICB9LFxuICAgIF9pc1VzaW5nTmF0aXZlRHJpdmVyOiBmdW5jdGlvbiBfaXNVc2luZ05hdGl2ZURyaXZlcigpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH07XG59O1xudmFyIHBhcmFsbGVsID0gZnVuY3Rpb24gcGFyYWxsZWwoYW5pbWF0aW9ucywgY29uZmlnKSB7XG4gIHZhciBkb25lQ291bnQgPSAwO1xuICAvLyBNYWtlIHN1cmUgd2Ugb25seSBjYWxsIHN0b3AoKSBhdCBtb3N0IG9uY2UgZm9yIGVhY2ggYW5pbWF0aW9uXG4gIHZhciBoYXNFbmRlZCA9IHt9O1xuICB2YXIgc3RvcFRvZ2V0aGVyID0gIShjb25maWcgJiYgY29uZmlnLnN0b3BUb2dldGhlciA9PT0gZmFsc2UpO1xuICB2YXIgcmVzdWx0ID0ge1xuICAgIHN0YXJ0OiBmdW5jdGlvbiBzdGFydChjYWxsYmFjaykge1xuICAgICAgaWYgKGRvbmVDb3VudCA9PT0gYW5pbWF0aW9ucy5sZW5ndGgpIHtcbiAgICAgICAgY2FsbGJhY2sgJiYgY2FsbGJhY2soe1xuICAgICAgICAgIGZpbmlzaGVkOiB0cnVlXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBhbmltYXRpb25zLmZvckVhY2goKGFuaW1hdGlvbiwgaWR4KSA9PiB7XG4gICAgICAgIHZhciBjYiA9IGZ1bmN0aW9uIGNiKGVuZFJlc3VsdCkge1xuICAgICAgICAgIGhhc0VuZGVkW2lkeF0gPSB0cnVlO1xuICAgICAgICAgIGRvbmVDb3VudCsrO1xuICAgICAgICAgIGlmIChkb25lQ291bnQgPT09IGFuaW1hdGlvbnMubGVuZ3RoKSB7XG4gICAgICAgICAgICBkb25lQ291bnQgPSAwO1xuICAgICAgICAgICAgY2FsbGJhY2sgJiYgY2FsbGJhY2soZW5kUmVzdWx0KTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICB9XG4gICAgICAgICAgaWYgKCFlbmRSZXN1bHQuZmluaXNoZWQgJiYgc3RvcFRvZ2V0aGVyKSB7XG4gICAgICAgICAgICByZXN1bHQuc3RvcCgpO1xuICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgaWYgKCFhbmltYXRpb24pIHtcbiAgICAgICAgICBjYih7XG4gICAgICAgICAgICBmaW5pc2hlZDogdHJ1ZVxuICAgICAgICAgIH0pO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGFuaW1hdGlvbi5zdGFydChjYik7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH0sXG4gICAgc3RvcDogZnVuY3Rpb24gc3RvcCgpIHtcbiAgICAgIGFuaW1hdGlvbnMuZm9yRWFjaCgoYW5pbWF0aW9uLCBpZHgpID0+IHtcbiAgICAgICAgIWhhc0VuZGVkW2lkeF0gJiYgYW5pbWF0aW9uLnN0b3AoKTtcbiAgICAgICAgaGFzRW5kZWRbaWR4XSA9IHRydWU7XG4gICAgICB9KTtcbiAgICB9LFxuICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHtcbiAgICAgIGFuaW1hdGlvbnMuZm9yRWFjaCgoYW5pbWF0aW9uLCBpZHgpID0+IHtcbiAgICAgICAgYW5pbWF0aW9uLnJlc2V0KCk7XG4gICAgICAgIGhhc0VuZGVkW2lkeF0gPSBmYWxzZTtcbiAgICAgICAgZG9uZUNvdW50ID0gMDtcbiAgICAgIH0pO1xuICAgIH0sXG4gICAgX3N0YXJ0TmF0aXZlTG9vcDogZnVuY3Rpb24gX3N0YXJ0TmF0aXZlTG9vcCgpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTG9vcHMgcnVuIHVzaW5nIHRoZSBuYXRpdmUgZHJpdmVyIGNhbm5vdCBjb250YWluIEFuaW1hdGVkLnBhcmFsbGVsIGFuaW1hdGlvbnMnKTtcbiAgICB9LFxuICAgIF9pc1VzaW5nTmF0aXZlRHJpdmVyOiBmdW5jdGlvbiBfaXNVc2luZ05hdGl2ZURyaXZlcigpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH07XG4gIHJldHVybiByZXN1bHQ7XG59O1xudmFyIGRlbGF5ID0gZnVuY3Rpb24gZGVsYXkodGltZSkge1xuICAvLyBXb3VsZCBiZSBuaWNlIHRvIG1ha2UgYSBzcGVjaWFsaXplZCBpbXBsZW1lbnRhdGlvblxuICByZXR1cm4gdGltaW5nKG5ldyBfQW5pbWF0ZWRWYWx1ZS5kZWZhdWx0KDApLCB7XG4gICAgdG9WYWx1ZTogMCxcbiAgICBkZWxheTogdGltZSxcbiAgICBkdXJhdGlvbjogMCxcbiAgICB1c2VOYXRpdmVEcml2ZXI6IGZhbHNlXG4gIH0pO1xufTtcbnZhciBzdGFnZ2VyID0gZnVuY3Rpb24gc3RhZ2dlcih0aW1lLCBhbmltYXRpb25zKSB7XG4gIHJldHVybiBwYXJhbGxlbChhbmltYXRpb25zLm1hcCgoYW5pbWF0aW9uLCBpKSA9PiB7XG4gICAgcmV0dXJuIHNlcXVlbmNlKFtkZWxheSh0aW1lICogaSksIGFuaW1hdGlvbl0pO1xuICB9KSk7XG59O1xudmFyIGxvb3AgPSBmdW5jdGlvbiBsb29wKGFuaW1hdGlvbiwgLy8gJEZsb3dGaXhNZVtwcm9wLW1pc3NpbmddXG5fdGVtcCkge1xuICB2YXIgX3JlZiA9IF90ZW1wID09PSB2b2lkIDAgPyB7fSA6IF90ZW1wLFxuICAgIF9yZWYkaXRlcmF0aW9ucyA9IF9yZWYuaXRlcmF0aW9ucyxcbiAgICBpdGVyYXRpb25zID0gX3JlZiRpdGVyYXRpb25zID09PSB2b2lkIDAgPyAtMSA6IF9yZWYkaXRlcmF0aW9ucyxcbiAgICBfcmVmJHJlc2V0QmVmb3JlSXRlcmEgPSBfcmVmLnJlc2V0QmVmb3JlSXRlcmF0aW9uLFxuICAgIHJlc2V0QmVmb3JlSXRlcmF0aW9uID0gX3JlZiRyZXNldEJlZm9yZUl0ZXJhID09PSB2b2lkIDAgPyB0cnVlIDogX3JlZiRyZXNldEJlZm9yZUl0ZXJhO1xuICB2YXIgaXNGaW5pc2hlZCA9IGZhbHNlO1xuICB2YXIgaXRlcmF0aW9uc1NvRmFyID0gMDtcbiAgcmV0dXJuIHtcbiAgICBzdGFydDogZnVuY3Rpb24gc3RhcnQoY2FsbGJhY2spIHtcbiAgICAgIHZhciByZXN0YXJ0ID0gZnVuY3Rpb24gcmVzdGFydChyZXN1bHQpIHtcbiAgICAgICAgaWYgKHJlc3VsdCA9PT0gdm9pZCAwKSB7XG4gICAgICAgICAgcmVzdWx0ID0ge1xuICAgICAgICAgICAgZmluaXNoZWQ6IHRydWVcbiAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIGlmIChpc0ZpbmlzaGVkIHx8IGl0ZXJhdGlvbnNTb0ZhciA9PT0gaXRlcmF0aW9ucyB8fCByZXN1bHQuZmluaXNoZWQgPT09IGZhbHNlKSB7XG4gICAgICAgICAgY2FsbGJhY2sgJiYgY2FsbGJhY2socmVzdWx0KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBpdGVyYXRpb25zU29GYXIrKztcbiAgICAgICAgICByZXNldEJlZm9yZUl0ZXJhdGlvbiAmJiBhbmltYXRpb24ucmVzZXQoKTtcbiAgICAgICAgICBhbmltYXRpb24uc3RhcnQocmVzdGFydCk7XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgICBpZiAoIWFuaW1hdGlvbiB8fCBpdGVyYXRpb25zID09PSAwKSB7XG4gICAgICAgIGNhbGxiYWNrICYmIGNhbGxiYWNrKHtcbiAgICAgICAgICBmaW5pc2hlZDogdHJ1ZVxuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGlmIChhbmltYXRpb24uX2lzVXNpbmdOYXRpdmVEcml2ZXIoKSkge1xuICAgICAgICAgIGFuaW1hdGlvbi5fc3RhcnROYXRpdmVMb29wKGl0ZXJhdGlvbnMpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJlc3RhcnQoKTsgLy8gU3RhcnQgbG9vcGluZyByZWN1cnNpdmVseSBvbiB0aGUganMgdGhyZWFkXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9LFxuICAgIHN0b3A6IGZ1bmN0aW9uIHN0b3AoKSB7XG4gICAgICBpc0ZpbmlzaGVkID0gdHJ1ZTtcbiAgICAgIGFuaW1hdGlvbi5zdG9wKCk7XG4gICAgfSxcbiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7XG4gICAgICBpdGVyYXRpb25zU29GYXIgPSAwO1xuICAgICAgaXNGaW5pc2hlZCA9IGZhbHNlO1xuICAgICAgYW5pbWF0aW9uLnJlc2V0KCk7XG4gICAgfSxcbiAgICBfc3RhcnROYXRpdmVMb29wOiBmdW5jdGlvbiBfc3RhcnROYXRpdmVMb29wKCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdMb29wcyBydW4gdXNpbmcgdGhlIG5hdGl2ZSBkcml2ZXIgY2Fubm90IGNvbnRhaW4gQW5pbWF0ZWQubG9vcCBhbmltYXRpb25zJyk7XG4gICAgfSxcbiAgICBfaXNVc2luZ05hdGl2ZURyaXZlcjogZnVuY3Rpb24gX2lzVXNpbmdOYXRpdmVEcml2ZXIoKSB7XG4gICAgICByZXR1cm4gYW5pbWF0aW9uLl9pc1VzaW5nTmF0aXZlRHJpdmVyKCk7XG4gICAgfVxuICB9O1xufTtcbmZ1bmN0aW9uIGZvcmtFdmVudChldmVudCwgbGlzdGVuZXIpIHtcbiAgaWYgKCFldmVudCkge1xuICAgIHJldHVybiBsaXN0ZW5lcjtcbiAgfSBlbHNlIGlmIChldmVudCBpbnN0YW5jZW9mIF9BbmltYXRlZEV2ZW50LkFuaW1hdGVkRXZlbnQpIHtcbiAgICBldmVudC5fX2FkZExpc3RlbmVyKGxpc3RlbmVyKTtcbiAgICByZXR1cm4gZXZlbnQ7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgIHR5cGVvZiBldmVudCA9PT0gJ2Z1bmN0aW9uJyAmJiBldmVudCguLi5hcmd1bWVudHMpO1xuICAgICAgbGlzdGVuZXIoLi4uYXJndW1lbnRzKTtcbiAgICB9O1xuICB9XG59XG5mdW5jdGlvbiB1bmZvcmtFdmVudChldmVudCwgbGlzdGVuZXIpIHtcbiAgaWYgKGV2ZW50ICYmIGV2ZW50IGluc3RhbmNlb2YgX0FuaW1hdGVkRXZlbnQuQW5pbWF0ZWRFdmVudCkge1xuICAgIGV2ZW50Ll9fcmVtb3ZlTGlzdGVuZXIobGlzdGVuZXIpO1xuICB9XG59XG52YXIgZXZlbnQgPSBmdW5jdGlvbiBldmVudChhcmdNYXBwaW5nLCBjb25maWcpIHtcbiAgdmFyIGFuaW1hdGVkRXZlbnQgPSBuZXcgX0FuaW1hdGVkRXZlbnQuQW5pbWF0ZWRFdmVudChhcmdNYXBwaW5nLCBjb25maWcpO1xuICBpZiAoYW5pbWF0ZWRFdmVudC5fX2lzTmF0aXZlKSB7XG4gICAgcmV0dXJuIGFuaW1hdGVkRXZlbnQ7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIGFuaW1hdGVkRXZlbnQuX19nZXRIYW5kbGVyKCk7XG4gIH1cbn07XG5cbi8vIEFsbCB0eXBlcyBvZiBhbmltYXRlZCBub2RlcyB0aGF0IHJlcHJlc2VudCBzY2FsYXIgbnVtYmVycyBhbmQgY2FuIGJlIGludGVycG9sYXRlZCAoZXRjKVxuLyoqXG4gKiBUaGUgYEFuaW1hdGVkYCBsaWJyYXJ5IGlzIGRlc2lnbmVkIHRvIG1ha2UgYW5pbWF0aW9ucyBmbHVpZCwgcG93ZXJmdWwsIGFuZFxuICogZWFzeSB0byBidWlsZCBhbmQgbWFpbnRhaW4uIGBBbmltYXRlZGAgZm9jdXNlcyBvbiBkZWNsYXJhdGl2ZSByZWxhdGlvbnNoaXBzXG4gKiBiZXR3ZWVuIGlucHV0cyBhbmQgb3V0cHV0cywgd2l0aCBjb25maWd1cmFibGUgdHJhbnNmb3JtcyBpbiBiZXR3ZWVuLCBhbmRcbiAqIHNpbXBsZSBgc3RhcnRgL2BzdG9wYCBtZXRob2RzIHRvIGNvbnRyb2wgdGltZS1iYXNlZCBhbmltYXRpb24gZXhlY3V0aW9uLlxuICogSWYgYWRkaXRpb25hbCB0cmFuc2Zvcm1zIGFyZSBhZGRlZCwgYmUgc3VyZSB0byBpbmNsdWRlIHRoZW0gaW5cbiAqIEFuaW1hdGVkTW9jay5qcyBhcyB3ZWxsLlxuICpcbiAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkXG4gKi9cbnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHtcbiAgLyoqXG4gICAqIFN0YW5kYXJkIHZhbHVlIGNsYXNzIGZvciBkcml2aW5nIGFuaW1hdGlvbnMuICBUeXBpY2FsbHkgaW5pdGlhbGl6ZWQgd2l0aFxuICAgKiBgbmV3IEFuaW1hdGVkLlZhbHVlKDApO2BcbiAgICpcbiAgICogU2VlIGh0dHBzOi8vcmVhY3RuYXRpdmUuZGV2L2RvY3MvYW5pbWF0ZWQjdmFsdWVcbiAgICovXG4gIFZhbHVlOiBfQW5pbWF0ZWRWYWx1ZS5kZWZhdWx0LFxuICAvKipcbiAgICogMkQgdmFsdWUgY2xhc3MgZm9yIGRyaXZpbmcgMkQgYW5pbWF0aW9ucywgc3VjaCBhcyBwYW4gZ2VzdHVyZXMuXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkdmFsdWV4eVxuICAgKi9cbiAgVmFsdWVYWTogX0FuaW1hdGVkVmFsdWVYWS5kZWZhdWx0LFxuICAvKipcbiAgICogVmFsdWUgY2xhc3MgZm9yIGRyaXZpbmcgY29sb3IgYW5pbWF0aW9ucy5cbiAgICovXG4gIENvbG9yOiBfQW5pbWF0ZWRDb2xvci5kZWZhdWx0LFxuICAvKipcbiAgICogRXhwb3J0ZWQgdG8gdXNlIHRoZSBJbnRlcnBvbGF0aW9uIHR5cGUgaW4gZmxvdy5cbiAgICpcbiAgICogU2VlIGh0dHBzOi8vcmVhY3RuYXRpdmUuZGV2L2RvY3MvYW5pbWF0ZWQjaW50ZXJwb2xhdGlvblxuICAgKi9cbiAgSW50ZXJwb2xhdGlvbjogX0FuaW1hdGVkSW50ZXJwb2xhdGlvbi5kZWZhdWx0LFxuICAvKipcbiAgICogRXhwb3J0ZWQgZm9yIGVhc2Ugb2YgdHlwZSBjaGVja2luZy4gQWxsIGFuaW1hdGVkIHZhbHVlcyBkZXJpdmUgZnJvbSB0aGlzXG4gICAqIGNsYXNzLlxuICAgKlxuICAgKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9hbmltYXRlZCNub2RlXG4gICAqL1xuICBOb2RlOiBfQW5pbWF0ZWROb2RlLmRlZmF1bHQsXG4gIC8qKlxuICAgKiBBbmltYXRlcyBhIHZhbHVlIGZyb20gYW4gaW5pdGlhbCB2ZWxvY2l0eSB0byB6ZXJvIGJhc2VkIG9uIGEgZGVjYXlcbiAgICogY29lZmZpY2llbnQuXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkI2RlY2F5XG4gICAqL1xuICBkZWNheSxcbiAgLyoqXG4gICAqIEFuaW1hdGVzIGEgdmFsdWUgYWxvbmcgYSB0aW1lZCBlYXNpbmcgY3VydmUuIFRoZSBFYXNpbmcgbW9kdWxlIGhhcyB0b25zIG9mXG4gICAqIHByZWRlZmluZWQgY3VydmVzLCBvciB5b3UgY2FuIHVzZSB5b3VyIG93biBmdW5jdGlvbi5cbiAgICpcbiAgICogU2VlIGh0dHBzOi8vcmVhY3RuYXRpdmUuZGV2L2RvY3MvYW5pbWF0ZWQjdGltaW5nXG4gICAqL1xuICB0aW1pbmcsXG4gIC8qKlxuICAgKiBBbmltYXRlcyBhIHZhbHVlIGFjY29yZGluZyB0byBhbiBhbmFseXRpY2FsIHNwcmluZyBtb2RlbCBiYXNlZCBvblxuICAgKiBkYW1wZWQgaGFybW9uaWMgb3NjaWxsYXRpb24uXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkI3NwcmluZ1xuICAgKi9cbiAgc3ByaW5nLFxuICAvKipcbiAgICogQ3JlYXRlcyBhIG5ldyBBbmltYXRlZCB2YWx1ZSBjb21wb3NlZCBmcm9tIHR3byBBbmltYXRlZCB2YWx1ZXMgYWRkZWRcbiAgICogdG9nZXRoZXIuXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkI2FkZFxuICAgKi9cbiAgYWRkLFxuICAvKipcbiAgICogQ3JlYXRlcyBhIG5ldyBBbmltYXRlZCB2YWx1ZSBjb21wb3NlZCBieSBzdWJ0cmFjdGluZyB0aGUgc2Vjb25kIEFuaW1hdGVkXG4gICAqIHZhbHVlIGZyb20gdGhlIGZpcnN0IEFuaW1hdGVkIHZhbHVlLlxuICAgKlxuICAgKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9hbmltYXRlZCNzdWJ0cmFjdFxuICAgKi9cbiAgc3VidHJhY3QsXG4gIC8qKlxuICAgKiBDcmVhdGVzIGEgbmV3IEFuaW1hdGVkIHZhbHVlIGNvbXBvc2VkIGJ5IGRpdmlkaW5nIHRoZSBmaXJzdCBBbmltYXRlZCB2YWx1ZVxuICAgKiBieSB0aGUgc2Vjb25kIEFuaW1hdGVkIHZhbHVlLlxuICAgKlxuICAgKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9hbmltYXRlZCNkaXZpZGVcbiAgICovXG4gIGRpdmlkZSxcbiAgLyoqXG4gICAqIENyZWF0ZXMgYSBuZXcgQW5pbWF0ZWQgdmFsdWUgY29tcG9zZWQgZnJvbSB0d28gQW5pbWF0ZWQgdmFsdWVzIG11bHRpcGxpZWRcbiAgICogdG9nZXRoZXIuXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkI211bHRpcGx5XG4gICAqL1xuICBtdWx0aXBseSxcbiAgLyoqXG4gICAqIENyZWF0ZXMgYSBuZXcgQW5pbWF0ZWQgdmFsdWUgdGhhdCBpcyB0aGUgKG5vbi1uZWdhdGl2ZSkgbW9kdWxvIG9mIHRoZVxuICAgKiBwcm92aWRlZCBBbmltYXRlZCB2YWx1ZS5cbiAgICpcbiAgICogU2VlIGh0dHBzOi8vcmVhY3RuYXRpdmUuZGV2L2RvY3MvYW5pbWF0ZWQjbW9kdWxvXG4gICAqL1xuICBtb2R1bG8sXG4gIC8qKlxuICAgKiBDcmVhdGUgYSBuZXcgQW5pbWF0ZWQgdmFsdWUgdGhhdCBpcyBsaW1pdGVkIGJldHdlZW4gMiB2YWx1ZXMuIEl0IHVzZXMgdGhlXG4gICAqIGRpZmZlcmVuY2UgYmV0d2VlbiB0aGUgbGFzdCB2YWx1ZSBzbyBldmVuIGlmIHRoZSB2YWx1ZSBpcyBmYXIgZnJvbSB0aGVcbiAgICogYm91bmRzIGl0IHdpbGwgc3RhcnQgY2hhbmdpbmcgd2hlbiB0aGUgdmFsdWUgc3RhcnRzIGdldHRpbmcgY2xvc2VyIGFnYWluLlxuICAgKlxuICAgKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9hbmltYXRlZCNkaWZmY2xhbXBcbiAgICovXG4gIGRpZmZDbGFtcCxcbiAgLyoqXG4gICAqIFN0YXJ0cyBhbiBhbmltYXRpb24gYWZ0ZXIgdGhlIGdpdmVuIGRlbGF5LlxuICAgKlxuICAgKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9hbmltYXRlZCNkZWxheVxuICAgKi9cbiAgZGVsYXksXG4gIC8qKlxuICAgKiBTdGFydHMgYW4gYXJyYXkgb2YgYW5pbWF0aW9ucyBpbiBvcmRlciwgd2FpdGluZyBmb3IgZWFjaCB0byBjb21wbGV0ZVxuICAgKiBiZWZvcmUgc3RhcnRpbmcgdGhlIG5leHQuIElmIHRoZSBjdXJyZW50IHJ1bm5pbmcgYW5pbWF0aW9uIGlzIHN0b3BwZWQsIG5vXG4gICAqIGZvbGxvd2luZyBhbmltYXRpb25zIHdpbGwgYmUgc3RhcnRlZC5cbiAgICpcbiAgICogU2VlIGh0dHBzOi8vcmVhY3RuYXRpdmUuZGV2L2RvY3MvYW5pbWF0ZWQjc2VxdWVuY2VcbiAgICovXG4gIHNlcXVlbmNlLFxuICAvKipcbiAgICogU3RhcnRzIGFuIGFycmF5IG9mIGFuaW1hdGlvbnMgYWxsIGF0IHRoZSBzYW1lIHRpbWUuIEJ5IGRlZmF1bHQsIGlmIG9uZVxuICAgKiBvZiB0aGUgYW5pbWF0aW9ucyBpcyBzdG9wcGVkLCB0aGV5IHdpbGwgYWxsIGJlIHN0b3BwZWQuIFlvdSBjYW4gb3ZlcnJpZGVcbiAgICogdGhpcyB3aXRoIHRoZSBgc3RvcFRvZ2V0aGVyYCBmbGFnLlxuICAgKlxuICAgKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9hbmltYXRlZCNwYXJhbGxlbFxuICAgKi9cbiAgcGFyYWxsZWwsXG4gIC8qKlxuICAgKiBBcnJheSBvZiBhbmltYXRpb25zIG1heSBydW4gaW4gcGFyYWxsZWwgKG92ZXJsYXApLCBidXQgYXJlIHN0YXJ0ZWQgaW5cbiAgICogc2VxdWVuY2Ugd2l0aCBzdWNjZXNzaXZlIGRlbGF5cy4gIE5pY2UgZm9yIGRvaW5nIHRyYWlsaW5nIGVmZmVjdHMuXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkI3N0YWdnZXJcbiAgICovXG4gIHN0YWdnZXIsXG4gIC8qKlxuICAgKiBMb29wcyBhIGdpdmVuIGFuaW1hdGlvbiBjb250aW51b3VzbHksIHNvIHRoYXQgZWFjaCB0aW1lIGl0IHJlYWNoZXMgdGhlXG4gICAqIGVuZCwgaXQgcmVzZXRzIGFuZCBiZWdpbnMgYWdhaW4gZnJvbSB0aGUgc3RhcnQuXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkI2xvb3BcbiAgICovXG4gIGxvb3AsXG4gIC8qKlxuICAgKiBUYWtlcyBhbiBhcnJheSBvZiBtYXBwaW5ncyBhbmQgZXh0cmFjdHMgdmFsdWVzIGZyb20gZWFjaCBhcmcgYWNjb3JkaW5nbHksXG4gICAqIHRoZW4gY2FsbHMgYHNldFZhbHVlYCBvbiB0aGUgbWFwcGVkIG91dHB1dHMuXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkI2V2ZW50XG4gICAqL1xuICBldmVudCxcbiAgLyoqXG4gICAqIE1ha2UgYW55IFJlYWN0IGNvbXBvbmVudCBBbmltYXRhYmxlLiAgVXNlZCB0byBjcmVhdGUgYEFuaW1hdGVkLlZpZXdgLCBldGMuXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkI2NyZWF0ZWFuaW1hdGVkY29tcG9uZW50XG4gICAqL1xuICBjcmVhdGVBbmltYXRlZENvbXBvbmVudDogX2NyZWF0ZUFuaW1hdGVkQ29tcG9uZW50LmRlZmF1bHQsXG4gIC8qKlxuICAgKiBJbXBlcmF0aXZlIEFQSSB0byBhdHRhY2ggYW4gYW5pbWF0ZWQgdmFsdWUgdG8gYW4gZXZlbnQgb24gYSB2aWV3LiBQcmVmZXJcbiAgICogdXNpbmcgYEFuaW1hdGVkLmV2ZW50YCB3aXRoIGB1c2VOYXRpdmVEcml2ZTogdHJ1ZWAgaWYgcG9zc2libGUuXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkI2F0dGFjaG5hdGl2ZWV2ZW50XG4gICAqL1xuICBhdHRhY2hOYXRpdmVFdmVudDogX0FuaW1hdGVkRXZlbnQuYXR0YWNoTmF0aXZlRXZlbnQsXG4gIC8qKlxuICAgKiBBZHZhbmNlZCBpbXBlcmF0aXZlIEFQSSBmb3Igc25vb3Bpbmcgb24gYW5pbWF0ZWQgZXZlbnRzIHRoYXQgYXJlIHBhc3NlZCBpblxuICAgKiB0aHJvdWdoIHByb3BzLiBVc2UgdmFsdWVzIGRpcmVjdGx5IHdoZXJlIHBvc3NpYmxlLlxuICAgKlxuICAgKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9hbmltYXRlZCNmb3JrZXZlbnRcbiAgICovXG4gIGZvcmtFdmVudCxcbiAgdW5mb3JrRXZlbnQsXG4gIC8qKlxuICAgKiBFeHBvc2UgRXZlbnQgY2xhc3MsIHNvIGl0IGNhbiBiZSB1c2VkIGFzIGEgdHlwZSBmb3IgdHlwZSBjaGVja2Vycy5cbiAgICovXG4gIEV2ZW50OiBfQW5pbWF0ZWRFdmVudC5BbmltYXRlZEV2ZW50XG59O1xubW9kdWxlLmV4cG9ydHMgPSBleHBvcnRzLmRlZmF1bHQ7Il0sIm1hcHBpbmdzIjoiQUFVQSxZQUFZOztBQUVaLElBQUlBLHNCQUFzQixHQUFHQyxPQUFPLENBQUMsOENBQThDLENBQUMsQ0FBQ0MsT0FBTztBQUM1RkMsT0FBTyxDQUFDQyxVQUFVLEdBQUcsSUFBSTtBQUN6QkQsT0FBTyxDQUFDRCxPQUFPLEdBQUcsS0FBSyxDQUFDO0FBQ3hCLElBQUlHLGNBQWMsR0FBR0wsc0JBQXNCLENBQUNDLE9BQU8sQ0FBQyxzQ0FBc0MsQ0FBQyxDQUFDO0FBQzVGLElBQUlLLGNBQWMsR0FBR0wsT0FBTyxrQkFBa0IsQ0FBQztBQUMvQyxJQUFJTSxpQkFBaUIsR0FBR1Asc0JBQXNCLENBQUNDLE9BQU8sMkJBQTJCLENBQUMsQ0FBQztBQUNuRixJQUFJTyxrQkFBa0IsR0FBR1Isc0JBQXNCLENBQUNDLE9BQU8sNEJBQTRCLENBQUMsQ0FBQztBQUNyRixJQUFJUSxpQkFBaUIsR0FBR1Qsc0JBQXNCLENBQUNDLE9BQU8sMkJBQTJCLENBQUMsQ0FBQztBQUNuRixJQUFJUyxzQkFBc0IsR0FBR1Ysc0JBQXNCLENBQUNDLE9BQU8sZ0NBQWdDLENBQUMsQ0FBQztBQUM3RixJQUFJVSxlQUFlLEdBQUdYLHNCQUFzQixDQUFDQyxPQUFPLHlCQUF5QixDQUFDLENBQUM7QUFDL0UsSUFBSVcsdUJBQXVCLEdBQUdaLHNCQUFzQixDQUFDQyxPQUFPLGlDQUFpQyxDQUFDLENBQUM7QUFDL0YsSUFBSVksYUFBYSxHQUFHYixzQkFBc0IsQ0FBQ0MsT0FBTyx1QkFBdUIsQ0FBQyxDQUFDO0FBQzNFLElBQUlhLGNBQWMsR0FBR2Qsc0JBQXNCLENBQUNDLE9BQU8sd0JBQXdCLENBQUMsQ0FBQztBQUM3RSxJQUFJYyxvQkFBb0IsR0FBR2Ysc0JBQXNCLENBQUNDLE9BQU8sOEJBQThCLENBQUMsQ0FBQztBQUN6RixJQUFJZSxpQkFBaUIsR0FBR2hCLHNCQUFzQixDQUFDQyxPQUFPLDJCQUEyQixDQUFDLENBQUM7QUFDbkYsSUFBSWdCLGNBQWMsR0FBR2pCLHNCQUFzQixDQUFDQyxPQUFPLHdCQUF3QixDQUFDLENBQUM7QUFDN0UsSUFBSWlCLGdCQUFnQixHQUFHbEIsc0JBQXNCLENBQUNDLE9BQU8sMEJBQTBCLENBQUMsQ0FBQztBQUNqRixJQUFJa0IsZUFBZSxHQUFHbkIsc0JBQXNCLENBQUNDLE9BQU8sOEJBQThCLENBQUMsQ0FBQztBQUNwRixJQUFJbUIsZ0JBQWdCLEdBQUdwQixzQkFBc0IsQ0FBQ0MsT0FBTywrQkFBK0IsQ0FBQyxDQUFDO0FBQ3RGLElBQUlvQixnQkFBZ0IsR0FBR3JCLHNCQUFzQixDQUFDQyxPQUFPLCtCQUErQixDQUFDLENBQUM7QUFDdEYsSUFBSXFCLHdCQUF3QixHQUFHdEIsc0JBQXNCLENBQUNDLE9BQU8sNEJBQTRCLENBQUMsQ0FBQztBQUMzRixJQUFJc0IsY0FBYyxHQUFHdkIsc0JBQXNCLENBQUNDLE9BQU8sd0JBQXdCLENBQUMsQ0FBQztBQUM3RSxJQUFJdUIsR0FBRyxHQUFHLFNBQVNBLEdBQUdBLENBQUNDLENBQUMsRUFBRUMsQ0FBQyxFQUFFO0VBQzNCLE9BQU8sSUFBSW5CLGlCQUFpQixDQUFDTCxPQUFPLENBQUN1QixDQUFDLEVBQUVDLENBQUMsQ0FBQztBQUM1QyxDQUFDO0FBQ0QsSUFBSUMsUUFBUSxHQUFHLFNBQVNBLFFBQVFBLENBQUNGLENBQUMsRUFBRUMsQ0FBQyxFQUFFO0VBQ3JDLE9BQU8sSUFBSVgsb0JBQW9CLENBQUNiLE9BQU8sQ0FBQ3VCLENBQUMsRUFBRUMsQ0FBQyxDQUFDO0FBQy9DLENBQUM7QUFDRCxJQUFJRSxNQUFNLEdBQUcsU0FBU0EsTUFBTUEsQ0FBQ0gsQ0FBQyxFQUFFQyxDQUFDLEVBQUU7RUFDakMsT0FBTyxJQUFJakIsaUJBQWlCLENBQUNQLE9BQU8sQ0FBQ3VCLENBQUMsRUFBRUMsQ0FBQyxDQUFDO0FBQzVDLENBQUM7QUFDRCxJQUFJRyxRQUFRLEdBQUcsU0FBU0EsUUFBUUEsQ0FBQ0osQ0FBQyxFQUFFQyxDQUFDLEVBQUU7RUFDckMsT0FBTyxJQUFJZCx1QkFBdUIsQ0FBQ1YsT0FBTyxDQUFDdUIsQ0FBQyxFQUFFQyxDQUFDLENBQUM7QUFDbEQsQ0FBQztBQUNELElBQUlJLE1BQU0sR0FBRyxTQUFTQSxNQUFNQSxDQUFDTCxDQUFDLEVBQUVNLE9BQU8sRUFBRTtFQUN2QyxPQUFPLElBQUlwQixlQUFlLENBQUNULE9BQU8sQ0FBQ3VCLENBQUMsRUFBRU0sT0FBTyxDQUFDO0FBQ2hELENBQUM7QUFDRCxJQUFJQyxTQUFTLEdBQUcsU0FBU0EsU0FBU0EsQ0FBQ1AsQ0FBQyxFQUFFUSxHQUFHLEVBQUVDLEdBQUcsRUFBRTtFQUM5QyxPQUFPLElBQUkxQixrQkFBa0IsQ0FBQ04sT0FBTyxDQUFDdUIsQ0FBQyxFQUFFUSxHQUFHLEVBQUVDLEdBQUcsQ0FBQztBQUNwRCxDQUFDO0FBQ0QsSUFBSUMsaUJBQWlCLEdBQUcsU0FBU0EsaUJBQWlCQSxDQUFDQyxRQUFRLEVBQUVDLE1BQU0sRUFBRTtFQUNuRSxJQUFJRCxRQUFRLElBQUlDLE1BQU0sQ0FBQ0MsVUFBVSxFQUFFO0lBQ2pDLE9BQU8sWUFBWTtNQUNqQkQsTUFBTSxDQUFDQyxVQUFVLElBQUlELE1BQU0sQ0FBQ0MsVUFBVSxDQUFBQyxLQUFBLENBQWpCRixNQUFNLEVBQWVHLFNBQVMsQ0FBQztNQUNwREosUUFBUSxJQUFJQSxRQUFRLENBQUFHLEtBQUEsU0FBSUMsU0FBUyxDQUFDO0lBQ3BDLENBQUM7RUFDSCxDQUFDLE1BQU07SUFDTCxPQUFPSixRQUFRLElBQUlDLE1BQU0sQ0FBQ0MsVUFBVTtFQUN0QztBQUNGLENBQUM7QUFDRCxJQUFJRyxlQUFlLEdBQUcsU0FBU0EsZUFBZUEsQ0FBQ0MsS0FBSyxFQUFFTCxNQUFNLEVBQUVNLElBQUksRUFBRTtFQUNsRSxJQUFJRCxLQUFLLFlBQVl4QixnQkFBZ0IsQ0FBQ2hCLE9BQU8sRUFBRTtJQUM3QyxJQUFJMEMsT0FBTyxHQUFHLENBQUMsQ0FBQyxFQUFFdkMsY0FBYyxDQUFDSCxPQUFPLEVBQUUsQ0FBQyxDQUFDLEVBQUVtQyxNQUFNLENBQUM7SUFDckQsSUFBSVEsT0FBTyxHQUFHLENBQUMsQ0FBQyxFQUFFeEMsY0FBYyxDQUFDSCxPQUFPLEVBQUUsQ0FBQyxDQUFDLEVBQUVtQyxNQUFNLENBQUM7SUFDckQsS0FBSyxJQUFJUyxHQUFHLElBQUlULE1BQU0sRUFBRTtNQUN0QixJQUFJVSxXQUFXLEdBQUdWLE1BQU0sQ0FBQ1MsR0FBRyxDQUFDO1FBQzNCRSxDQUFDLEdBQUdELFdBQVcsQ0FBQ0MsQ0FBQztRQUNqQkMsQ0FBQyxHQUFHRixXQUFXLENBQUNFLENBQUM7TUFDbkIsSUFBSUQsQ0FBQyxLQUFLRSxTQUFTLElBQUlELENBQUMsS0FBS0MsU0FBUyxFQUFFO1FBQ3RDTixPQUFPLENBQUNFLEdBQUcsQ0FBQyxHQUFHRSxDQUFDO1FBQ2hCSCxPQUFPLENBQUNDLEdBQUcsQ0FBQyxHQUFHRyxDQUFDO01BQ2xCO0lBQ0Y7SUFDQSxJQUFJRSxFQUFFLEdBQUdSLElBQUksQ0FBQ0QsS0FBSyxDQUFDTSxDQUFDLEVBQUVKLE9BQU8sQ0FBQztJQUMvQixJQUFJUSxFQUFFLEdBQUdULElBQUksQ0FBQ0QsS0FBSyxDQUFDTyxDQUFDLEVBQUVKLE9BQU8sQ0FBQztJQUcvQixPQUFPUSxRQUFRLENBQUMsQ0FBQ0YsRUFBRSxFQUFFQyxFQUFFLENBQUMsRUFBRTtNQUN4QkUsWUFBWSxFQUFFO0lBQ2hCLENBQUMsQ0FBQztFQUNKLENBQUMsTUFBTSxJQUFJWixLQUFLLFlBQVluQixjQUFjLENBQUNyQixPQUFPLEVBQUU7SUFDbEQsSUFBSXFELE9BQU8sR0FBRyxDQUFDLENBQUMsRUFBRWxELGNBQWMsQ0FBQ0gsT0FBTyxFQUFFLENBQUMsQ0FBQyxFQUFFbUMsTUFBTSxDQUFDO0lBQ3JELElBQUltQixPQUFPLEdBQUcsQ0FBQyxDQUFDLEVBQUVuRCxjQUFjLENBQUNILE9BQU8sRUFBRSxDQUFDLENBQUMsRUFBRW1DLE1BQU0sQ0FBQztJQUNyRCxJQUFJb0IsT0FBTyxHQUFHLENBQUMsQ0FBQyxFQUFFcEQsY0FBYyxDQUFDSCxPQUFPLEVBQUUsQ0FBQyxDQUFDLEVBQUVtQyxNQUFNLENBQUM7SUFDckQsSUFBSXFCLE9BQU8sR0FBRyxDQUFDLENBQUMsRUFBRXJELGNBQWMsQ0FBQ0gsT0FBTyxFQUFFLENBQUMsQ0FBQyxFQUFFbUMsTUFBTSxDQUFDO0lBQ3JELEtBQUssSUFBSXNCLElBQUksSUFBSXRCLE1BQU0sRUFBRTtNQUN2QixJQUFJdUIsWUFBWSxHQUFHdkIsTUFBTSxDQUFDc0IsSUFBSSxDQUFDO1FBQzdCRSxDQUFDLEdBQUdELFlBQVksQ0FBQ0MsQ0FBQztRQUNsQkMsQ0FBQyxHQUFHRixZQUFZLENBQUNFLENBQUM7UUFDbEJwQyxDQUFDLEdBQUdrQyxZQUFZLENBQUNsQyxDQUFDO1FBQ2xCRCxDQUFDLEdBQUdtQyxZQUFZLENBQUNuQyxDQUFDO01BQ3BCLElBQUlvQyxDQUFDLEtBQUtYLFNBQVMsSUFBSVksQ0FBQyxLQUFLWixTQUFTLElBQUl4QixDQUFDLEtBQUt3QixTQUFTLElBQUl6QixDQUFDLEtBQUt5QixTQUFTLEVBQUU7UUFDNUVLLE9BQU8sQ0FBQ0ksSUFBSSxDQUFDLEdBQUdFLENBQUM7UUFDakJMLE9BQU8sQ0FBQ0csSUFBSSxDQUFDLEdBQUdHLENBQUM7UUFDakJMLE9BQU8sQ0FBQ0UsSUFBSSxDQUFDLEdBQUdqQyxDQUFDO1FBQ2pCZ0MsT0FBTyxDQUFDQyxJQUFJLENBQUMsR0FBR2xDLENBQUM7TUFDbkI7SUFDRjtJQUNBLElBQUlzQyxFQUFFLEdBQUdwQixJQUFJLENBQUNELEtBQUssQ0FBQ21CLENBQUMsRUFBRU4sT0FBTyxDQUFDO0lBQy9CLElBQUlTLEVBQUUsR0FBR3JCLElBQUksQ0FBQ0QsS0FBSyxDQUFDb0IsQ0FBQyxFQUFFTixPQUFPLENBQUM7SUFDL0IsSUFBSVMsRUFBRSxHQUFHdEIsSUFBSSxDQUFDRCxLQUFLLENBQUNoQixDQUFDLEVBQUUrQixPQUFPLENBQUM7SUFDL0IsSUFBSVMsRUFBRSxHQUFHdkIsSUFBSSxDQUFDRCxLQUFLLENBQUNqQixDQUFDLEVBQUVpQyxPQUFPLENBQUM7SUFHL0IsT0FBT0wsUUFBUSxDQUFDLENBQUNVLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxFQUFFLEVBQUVDLEVBQUUsQ0FBQyxFQUFFO01BQ2hDWixZQUFZLEVBQUU7SUFDaEIsQ0FBQyxDQUFDO0VBQ0o7RUFDQSxPQUFPLElBQUk7QUFDYixDQUFDO0FBQ0QsSUFBSWEsTUFBTSxHQUFHLFNBQVNBLE1BQU1BLENBQUN6QixLQUFLLEVBQUVMLE1BQU0sRUFBRTtFQUMxQyxJQUFJK0IsTUFBTSxHQUFHLFNBQVNDLEtBQUtBLENBQUNDLGFBQWEsRUFBRUMsYUFBYSxFQUFFbkMsUUFBUSxFQUFFO0lBQ2xFQSxRQUFRLEdBQUdELGlCQUFpQixDQUFDQyxRQUFRLEVBQUVtQyxhQUFhLENBQUM7SUFDckQsSUFBSUMsV0FBVyxHQUFHRixhQUFhO0lBQy9CLElBQUlHLFlBQVksR0FBR0YsYUFBYTtJQUNoQ0MsV0FBVyxDQUFDRSxZQUFZLENBQUMsQ0FBQztJQUMxQixJQUFJSCxhQUFhLENBQUNJLE9BQU8sWUFBWTlELGFBQWEsQ0FBQ1gsT0FBTyxFQUFFO01BQzFEc0UsV0FBVyxDQUFDSSxLQUFLLENBQUMsSUFBSTVELGlCQUFpQixDQUFDZCxPQUFPLENBQUNzRSxXQUFXLEVBQUVELGFBQWEsQ0FBQ0ksT0FBTyxFQUFFdkQsZ0JBQWdCLENBQUNsQixPQUFPLEVBQUV1RSxZQUFZLEVBQUVyQyxRQUFRLENBQUMsQ0FBQztJQUN4SSxDQUFDLE1BQU07TUFDTG9DLFdBQVcsQ0FBQ0ssT0FBTyxDQUFDLElBQUl6RCxnQkFBZ0IsQ0FBQ2xCLE9BQU8sQ0FBQ3VFLFlBQVksQ0FBQyxFQUFFckMsUUFBUSxDQUFDO0lBQzNFO0VBQ0YsQ0FBQztFQUNELE9BQU9LLGVBQWUsQ0FBQ0MsS0FBSyxFQUFFTCxNQUFNLEVBQUU4QixNQUFNLENBQUMsSUFBSTtJQUMvQ0UsS0FBSyxFQUFFLFNBQVNBLEtBQUtBLENBQUNqQyxRQUFRLEVBQUU7TUFDOUJnQyxNQUFNLENBQUMxQixLQUFLLEVBQUVMLE1BQU0sRUFBRUQsUUFBUSxDQUFDO0lBQ2pDLENBQUM7SUFDRDBDLElBQUksRUFBRSxTQUFTQSxJQUFJQSxDQUFBLEVBQUc7TUFDcEJwQyxLQUFLLENBQUNxQyxhQUFhLENBQUMsQ0FBQztJQUN2QixDQUFDO0lBQ0RDLEtBQUssRUFBRSxTQUFTQSxLQUFLQSxDQUFBLEVBQUc7TUFDdEJ0QyxLQUFLLENBQUN1QyxjQUFjLENBQUMsQ0FBQztJQUN4QixDQUFDO0lBQ0RDLGdCQUFnQixFQUFFLFNBQVNBLGdCQUFnQkEsQ0FBQ0MsVUFBVSxFQUFFO01BQ3RELElBQUlWLFlBQVksR0FBRyxDQUFDLENBQUMsRUFBRXBFLGNBQWMsQ0FBQ0gsT0FBTyxFQUFFLENBQUMsQ0FBQyxFQUFFRyxjQUFjLENBQUNILE9BQU8sRUFBRSxDQUFDLENBQUMsRUFBRW1DLE1BQU0sQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFO1FBQzFGOEMsVUFBVSxFQUFWQTtNQUNGLENBQUMsQ0FBQztNQUNGZixNQUFNLENBQUMxQixLQUFLLEVBQUUrQixZQUFZLENBQUM7SUFDN0IsQ0FBQztJQUNEVyxvQkFBb0IsRUFBRSxTQUFTQSxvQkFBb0JBLENBQUEsRUFBRztNQUNwRCxPQUFPL0MsTUFBTSxDQUFDZ0QsZUFBZSxJQUFJLEtBQUs7SUFDeEM7RUFDRixDQUFDO0FBQ0gsQ0FBQztBQUNELElBQUlDLE1BQU0sR0FBRyxTQUFTQSxNQUFNQSxDQUFDNUMsS0FBSyxFQUFFTCxNQUFNLEVBQUU7RUFDMUMsSUFBSWtELE9BQU8sR0FBRyxTQUFTbEIsS0FBS0EsQ0FBQ0MsYUFBYSxFQUFFQyxhQUFhLEVBQUVuQyxRQUFRLEVBQUU7SUFDbkVBLFFBQVEsR0FBR0QsaUJBQWlCLENBQUNDLFFBQVEsRUFBRW1DLGFBQWEsQ0FBQztJQUNyRCxJQUFJQyxXQUFXLEdBQUdGLGFBQWE7SUFDL0IsSUFBSUcsWUFBWSxHQUFHRixhQUFhO0lBQ2hDQyxXQUFXLENBQUNFLFlBQVksQ0FBQyxDQUFDO0lBQzFCLElBQUlILGFBQWEsQ0FBQ0ksT0FBTyxZQUFZOUQsYUFBYSxDQUFDWCxPQUFPLEVBQUU7TUFDMURzRSxXQUFXLENBQUNJLEtBQUssQ0FBQyxJQUFJNUQsaUJBQWlCLENBQUNkLE9BQU8sQ0FBQ3NFLFdBQVcsRUFBRUQsYUFBYSxDQUFDSSxPQUFPLEVBQUV0RCxnQkFBZ0IsQ0FBQ25CLE9BQU8sRUFBRXVFLFlBQVksRUFBRXJDLFFBQVEsQ0FBQyxDQUFDO0lBQ3hJLENBQUMsTUFBTTtNQUNMb0MsV0FBVyxDQUFDSyxPQUFPLENBQUMsSUFBSXhELGdCQUFnQixDQUFDbkIsT0FBTyxDQUFDdUUsWUFBWSxDQUFDLEVBQUVyQyxRQUFRLENBQUM7SUFDM0U7RUFDRixDQUFDO0VBQ0QsT0FBT0ssZUFBZSxDQUFDQyxLQUFLLEVBQUVMLE1BQU0sRUFBRWlELE1BQU0sQ0FBQyxJQUFJO0lBQy9DakIsS0FBSyxFQUFFLFNBQVNBLEtBQUtBLENBQUNqQyxRQUFRLEVBQUU7TUFDOUJtRCxPQUFPLENBQUM3QyxLQUFLLEVBQUVMLE1BQU0sRUFBRUQsUUFBUSxDQUFDO0lBQ2xDLENBQUM7SUFDRDBDLElBQUksRUFBRSxTQUFTQSxJQUFJQSxDQUFBLEVBQUc7TUFDcEJwQyxLQUFLLENBQUNxQyxhQUFhLENBQUMsQ0FBQztJQUN2QixDQUFDO0lBQ0RDLEtBQUssRUFBRSxTQUFTQSxLQUFLQSxDQUFBLEVBQUc7TUFDdEJ0QyxLQUFLLENBQUN1QyxjQUFjLENBQUMsQ0FBQztJQUN4QixDQUFDO0lBQ0RDLGdCQUFnQixFQUFFLFNBQVNBLGdCQUFnQkEsQ0FBQ0MsVUFBVSxFQUFFO01BQ3RELElBQUlWLFlBQVksR0FBRyxDQUFDLENBQUMsRUFBRXBFLGNBQWMsQ0FBQ0gsT0FBTyxFQUFFLENBQUMsQ0FBQyxFQUFFRyxjQUFjLENBQUNILE9BQU8sRUFBRSxDQUFDLENBQUMsRUFBRW1DLE1BQU0sQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFO1FBQzFGOEMsVUFBVSxFQUFWQTtNQUNGLENBQUMsQ0FBQztNQUNGSSxPQUFPLENBQUM3QyxLQUFLLEVBQUUrQixZQUFZLENBQUM7SUFDOUIsQ0FBQztJQUNEVyxvQkFBb0IsRUFBRSxTQUFTQSxvQkFBb0JBLENBQUEsRUFBRztNQUNwRCxPQUFPL0MsTUFBTSxDQUFDZ0QsZUFBZSxJQUFJLEtBQUs7SUFDeEM7RUFDRixDQUFDO0FBQ0gsQ0FBQztBQUNELElBQUlHLEtBQUssR0FBRyxTQUFTQSxLQUFLQSxDQUFDOUMsS0FBSyxFQUFFTCxNQUFNLEVBQUU7RUFDeEMsSUFBSW9ELE9BQU8sR0FBRyxTQUFTcEIsS0FBS0EsQ0FBQ0MsYUFBYSxFQUFFQyxhQUFhLEVBQUVuQyxRQUFRLEVBQUU7SUFDbkVBLFFBQVEsR0FBR0QsaUJBQWlCLENBQUNDLFFBQVEsRUFBRW1DLGFBQWEsQ0FBQztJQUNyRCxJQUFJQyxXQUFXLEdBQUdGLGFBQWE7SUFDL0IsSUFBSUcsWUFBWSxHQUFHRixhQUFhO0lBQ2hDQyxXQUFXLENBQUNFLFlBQVksQ0FBQyxDQUFDO0lBQzFCRixXQUFXLENBQUNLLE9BQU8sQ0FBQyxJQUFJMUQsZUFBZSxDQUFDakIsT0FBTyxDQUFDdUUsWUFBWSxDQUFDLEVBQUVyQyxRQUFRLENBQUM7RUFDMUUsQ0FBQztFQUNELE9BQU9LLGVBQWUsQ0FBQ0MsS0FBSyxFQUFFTCxNQUFNLEVBQUVtRCxLQUFLLENBQUMsSUFBSTtJQUM5Q25CLEtBQUssRUFBRSxTQUFTQSxLQUFLQSxDQUFDakMsUUFBUSxFQUFFO01BQzlCcUQsT0FBTyxDQUFDL0MsS0FBSyxFQUFFTCxNQUFNLEVBQUVELFFBQVEsQ0FBQztJQUNsQyxDQUFDO0lBQ0QwQyxJQUFJLEVBQUUsU0FBU0EsSUFBSUEsQ0FBQSxFQUFHO01BQ3BCcEMsS0FBSyxDQUFDcUMsYUFBYSxDQUFDLENBQUM7SUFDdkIsQ0FBQztJQUNEQyxLQUFLLEVBQUUsU0FBU0EsS0FBS0EsQ0FBQSxFQUFHO01BQ3RCdEMsS0FBSyxDQUFDdUMsY0FBYyxDQUFDLENBQUM7SUFDeEIsQ0FBQztJQUNEQyxnQkFBZ0IsRUFBRSxTQUFTQSxnQkFBZ0JBLENBQUNDLFVBQVUsRUFBRTtNQUN0RCxJQUFJVixZQUFZLEdBQUcsQ0FBQyxDQUFDLEVBQUVwRSxjQUFjLENBQUNILE9BQU8sRUFBRSxDQUFDLENBQUMsRUFBRUcsY0FBYyxDQUFDSCxPQUFPLEVBQUUsQ0FBQyxDQUFDLEVBQUVtQyxNQUFNLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRTtRQUMxRjhDLFVBQVUsRUFBVkE7TUFDRixDQUFDLENBQUM7TUFDRk0sT0FBTyxDQUFDL0MsS0FBSyxFQUFFK0IsWUFBWSxDQUFDO0lBQzlCLENBQUM7SUFDRFcsb0JBQW9CLEVBQUUsU0FBU0Esb0JBQW9CQSxDQUFBLEVBQUc7TUFDcEQsT0FBTy9DLE1BQU0sQ0FBQ2dELGVBQWUsSUFBSSxLQUFLO0lBQ3hDO0VBQ0YsQ0FBQztBQUNILENBQUM7QUFDRCxJQUFJSyxRQUFRLEdBQUcsU0FBU0EsUUFBUUEsQ0FBQ0MsVUFBVSxFQUFFO0VBQzNDLElBQUlDLE9BQU8sR0FBRyxDQUFDO0VBQ2YsT0FBTztJQUNMdkIsS0FBSyxFQUFFLFNBQVNBLEtBQUtBLENBQUNqQyxRQUFRLEVBQUU7TUFDOUIsSUFBSUUsVUFBVSxHQUFHLFNBQVNBLFVBQVVBLENBQUN1RCxNQUFNLEVBQUU7UUFDM0MsSUFBSSxDQUFDQSxNQUFNLENBQUNDLFFBQVEsRUFBRTtVQUNwQjFELFFBQVEsSUFBSUEsUUFBUSxDQUFDeUQsTUFBTSxDQUFDO1VBQzVCO1FBQ0Y7UUFDQUQsT0FBTyxFQUFFO1FBQ1QsSUFBSUEsT0FBTyxLQUFLRCxVQUFVLENBQUNJLE1BQU0sRUFBRTtVQUNqQzNELFFBQVEsSUFBSUEsUUFBUSxDQUFDeUQsTUFBTSxDQUFDO1VBQzVCO1FBQ0Y7UUFDQUYsVUFBVSxDQUFDQyxPQUFPLENBQUMsQ0FBQ3ZCLEtBQUssQ0FBQy9CLFVBQVUsQ0FBQztNQUN2QyxDQUFDO01BQ0QsSUFBSXFELFVBQVUsQ0FBQ0ksTUFBTSxLQUFLLENBQUMsRUFBRTtRQUMzQjNELFFBQVEsSUFBSUEsUUFBUSxDQUFDO1VBQ25CMEQsUUFBUSxFQUFFO1FBQ1osQ0FBQyxDQUFDO01BQ0osQ0FBQyxNQUFNO1FBQ0xILFVBQVUsQ0FBQ0MsT0FBTyxDQUFDLENBQUN2QixLQUFLLENBQUMvQixVQUFVLENBQUM7TUFDdkM7SUFDRixDQUFDO0lBQ0R3QyxJQUFJLEVBQUUsU0FBU0EsSUFBSUEsQ0FBQSxFQUFHO01BQ3BCLElBQUljLE9BQU8sR0FBR0QsVUFBVSxDQUFDSSxNQUFNLEVBQUU7UUFDL0JKLFVBQVUsQ0FBQ0MsT0FBTyxDQUFDLENBQUNkLElBQUksQ0FBQyxDQUFDO01BQzVCO0lBQ0YsQ0FBQztJQUNERSxLQUFLLEVBQUUsU0FBU0EsS0FBS0EsQ0FBQSxFQUFHO01BQ3RCVyxVQUFVLENBQUNLLE9BQU8sQ0FBQyxVQUFDQyxTQUFTLEVBQUVDLEdBQUcsRUFBSztRQUNyQyxJQUFJQSxHQUFHLElBQUlOLE9BQU8sRUFBRTtVQUNsQkssU0FBUyxDQUFDakIsS0FBSyxDQUFDLENBQUM7UUFDbkI7TUFDRixDQUFDLENBQUM7TUFDRlksT0FBTyxHQUFHLENBQUM7SUFDYixDQUFDO0lBQ0RWLGdCQUFnQixFQUFFLFNBQVNBLGdCQUFnQkEsQ0FBQSxFQUFHO01BQzVDLE1BQU0sSUFBSWlCLEtBQUssQ0FBQywrRUFBK0UsQ0FBQztJQUNsRyxDQUFDO0lBQ0RmLG9CQUFvQixFQUFFLFNBQVNBLG9CQUFvQkEsQ0FBQSxFQUFHO01BQ3BELE9BQU8sS0FBSztJQUNkO0VBQ0YsQ0FBQztBQUNILENBQUM7QUFDRCxJQUFJL0IsUUFBUSxHQUFHLFNBQVNBLFFBQVFBLENBQUNzQyxVQUFVLEVBQUV0RCxNQUFNLEVBQUU7RUFDbkQsSUFBSStELFNBQVMsR0FBRyxDQUFDO0VBRWpCLElBQUlDLFFBQVEsR0FBRyxDQUFDLENBQUM7RUFDakIsSUFBSS9DLFlBQVksR0FBRyxFQUFFakIsTUFBTSxJQUFJQSxNQUFNLENBQUNpQixZQUFZLEtBQUssS0FBSyxDQUFDO0VBQzdELElBQUl1QyxNQUFNLEdBQUc7SUFDWHhCLEtBQUssRUFBRSxTQUFTQSxLQUFLQSxDQUFDakMsUUFBUSxFQUFFO01BQzlCLElBQUlnRSxTQUFTLEtBQUtULFVBQVUsQ0FBQ0ksTUFBTSxFQUFFO1FBQ25DM0QsUUFBUSxJQUFJQSxRQUFRLENBQUM7VUFDbkIwRCxRQUFRLEVBQUU7UUFDWixDQUFDLENBQUM7UUFDRjtNQUNGO01BQ0FILFVBQVUsQ0FBQ0ssT0FBTyxDQUFDLFVBQUNDLFNBQVMsRUFBRUMsR0FBRyxFQUFLO1FBQ3JDLElBQUlJLEVBQUUsR0FBRyxTQUFTQSxFQUFFQSxDQUFDQyxTQUFTLEVBQUU7VUFDOUJGLFFBQVEsQ0FBQ0gsR0FBRyxDQUFDLEdBQUcsSUFBSTtVQUNwQkUsU0FBUyxFQUFFO1VBQ1gsSUFBSUEsU0FBUyxLQUFLVCxVQUFVLENBQUNJLE1BQU0sRUFBRTtZQUNuQ0ssU0FBUyxHQUFHLENBQUM7WUFDYmhFLFFBQVEsSUFBSUEsUUFBUSxDQUFDbUUsU0FBUyxDQUFDO1lBQy9CO1VBQ0Y7VUFDQSxJQUFJLENBQUNBLFNBQVMsQ0FBQ1QsUUFBUSxJQUFJeEMsWUFBWSxFQUFFO1lBQ3ZDdUMsTUFBTSxDQUFDZixJQUFJLENBQUMsQ0FBQztVQUNmO1FBQ0YsQ0FBQztRQUNELElBQUksQ0FBQ21CLFNBQVMsRUFBRTtVQUNkSyxFQUFFLENBQUM7WUFDRFIsUUFBUSxFQUFFO1VBQ1osQ0FBQyxDQUFDO1FBQ0osQ0FBQyxNQUFNO1VBQ0xHLFNBQVMsQ0FBQzVCLEtBQUssQ0FBQ2lDLEVBQUUsQ0FBQztRQUNyQjtNQUNGLENBQUMsQ0FBQztJQUNKLENBQUM7SUFDRHhCLElBQUksRUFBRSxTQUFTQSxJQUFJQSxDQUFBLEVBQUc7TUFDcEJhLFVBQVUsQ0FBQ0ssT0FBTyxDQUFDLFVBQUNDLFNBQVMsRUFBRUMsR0FBRyxFQUFLO1FBQ3JDLENBQUNHLFFBQVEsQ0FBQ0gsR0FBRyxDQUFDLElBQUlELFNBQVMsQ0FBQ25CLElBQUksQ0FBQyxDQUFDO1FBQ2xDdUIsUUFBUSxDQUFDSCxHQUFHLENBQUMsR0FBRyxJQUFJO01BQ3RCLENBQUMsQ0FBQztJQUNKLENBQUM7SUFDRGxCLEtBQUssRUFBRSxTQUFTQSxLQUFLQSxDQUFBLEVBQUc7TUFDdEJXLFVBQVUsQ0FBQ0ssT0FBTyxDQUFDLFVBQUNDLFNBQVMsRUFBRUMsR0FBRyxFQUFLO1FBQ3JDRCxTQUFTLENBQUNqQixLQUFLLENBQUMsQ0FBQztRQUNqQnFCLFFBQVEsQ0FBQ0gsR0FBRyxDQUFDLEdBQUcsS0FBSztRQUNyQkUsU0FBUyxHQUFHLENBQUM7TUFDZixDQUFDLENBQUM7SUFDSixDQUFDO0lBQ0RsQixnQkFBZ0IsRUFBRSxTQUFTQSxnQkFBZ0JBLENBQUEsRUFBRztNQUM1QyxNQUFNLElBQUlpQixLQUFLLENBQUMsK0VBQStFLENBQUM7SUFDbEcsQ0FBQztJQUNEZixvQkFBb0IsRUFBRSxTQUFTQSxvQkFBb0JBLENBQUEsRUFBRztNQUNwRCxPQUFPLEtBQUs7SUFDZDtFQUNGLENBQUM7RUFDRCxPQUFPUyxNQUFNO0FBQ2YsQ0FBQztBQUNELElBQUlXLEtBQUssR0FBRyxTQUFTQSxLQUFLQSxDQUFDQyxJQUFJLEVBQUU7RUFFL0IsT0FBT25CLE1BQU0sQ0FBQyxJQUFJckUsY0FBYyxDQUFDZixPQUFPLENBQUMsQ0FBQyxDQUFDLEVBQUU7SUFDM0N5RSxPQUFPLEVBQUUsQ0FBQztJQUNWNkIsS0FBSyxFQUFFQyxJQUFJO0lBQ1hDLFFBQVEsRUFBRSxDQUFDO0lBQ1hyQixlQUFlLEVBQUU7RUFDbkIsQ0FBQyxDQUFDO0FBQ0osQ0FBQztBQUNELElBQUlzQixPQUFPLEdBQUcsU0FBU0EsT0FBT0EsQ0FBQ0YsSUFBSSxFQUFFZCxVQUFVLEVBQUU7RUFDL0MsT0FBT3RDLFFBQVEsQ0FBQ3NDLFVBQVUsQ0FBQ2lCLEdBQUcsQ0FBQyxVQUFDWCxTQUFTLEVBQUVZLENBQUMsRUFBSztJQUMvQyxPQUFPbkIsUUFBUSxDQUFDLENBQUNjLEtBQUssQ0FBQ0MsSUFBSSxHQUFHSSxDQUFDLENBQUMsRUFBRVosU0FBUyxDQUFDLENBQUM7RUFDL0MsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDO0FBQ0QsSUFBSWEsSUFBSSxHQUFHLFNBQVNBLElBQUlBLENBQUNiLFNBQVMsRUFDbENjLEtBQUssRUFBRTtFQUNMLElBQUlDLElBQUksR0FBR0QsS0FBSyxLQUFLLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHQSxLQUFLO0lBQ3RDRSxlQUFlLEdBQUdELElBQUksQ0FBQzdCLFVBQVU7SUFDakNBLFVBQVUsR0FBRzhCLGVBQWUsS0FBSyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBR0EsZUFBZTtJQUM5REMscUJBQXFCLEdBQUdGLElBQUksQ0FBQ0csb0JBQW9CO0lBQ2pEQSxvQkFBb0IsR0FBR0QscUJBQXFCLEtBQUssS0FBSyxDQUFDLEdBQUcsSUFBSSxHQUFHQSxxQkFBcUI7RUFDeEYsSUFBSUUsVUFBVSxHQUFHLEtBQUs7RUFDdEIsSUFBSUMsZUFBZSxHQUFHLENBQUM7RUFDdkIsT0FBTztJQUNMaEQsS0FBSyxFQUFFLFNBQVNBLEtBQUtBLENBQUNqQyxRQUFRLEVBQUU7TUFDOUIsSUFBSWtGLE9BQU8sR0FBRyxTQUFTQSxPQUFPQSxDQUFDekIsTUFBTSxFQUFFO1FBQ3JDLElBQUlBLE1BQU0sS0FBSyxLQUFLLENBQUMsRUFBRTtVQUNyQkEsTUFBTSxHQUFHO1lBQ1BDLFFBQVEsRUFBRTtVQUNaLENBQUM7UUFDSDtRQUNBLElBQUlzQixVQUFVLElBQUlDLGVBQWUsS0FBS2xDLFVBQVUsSUFBSVUsTUFBTSxDQUFDQyxRQUFRLEtBQUssS0FBSyxFQUFFO1VBQzdFMUQsUUFBUSxJQUFJQSxRQUFRLENBQUN5RCxNQUFNLENBQUM7UUFDOUIsQ0FBQyxNQUFNO1VBQ0x3QixlQUFlLEVBQUU7VUFDakJGLG9CQUFvQixJQUFJbEIsU0FBUyxDQUFDakIsS0FBSyxDQUFDLENBQUM7VUFDekNpQixTQUFTLENBQUM1QixLQUFLLENBQUNpRCxPQUFPLENBQUM7UUFDMUI7TUFDRixDQUFDO01BQ0QsSUFBSSxDQUFDckIsU0FBUyxJQUFJZCxVQUFVLEtBQUssQ0FBQyxFQUFFO1FBQ2xDL0MsUUFBUSxJQUFJQSxRQUFRLENBQUM7VUFDbkIwRCxRQUFRLEVBQUU7UUFDWixDQUFDLENBQUM7TUFDSixDQUFDLE1BQU07UUFDTCxJQUFJRyxTQUFTLENBQUNiLG9CQUFvQixDQUFDLENBQUMsRUFBRTtVQUNwQ2EsU0FBUyxDQUFDZixnQkFBZ0IsQ0FBQ0MsVUFBVSxDQUFDO1FBQ3hDLENBQUMsTUFBTTtVQUNMbUMsT0FBTyxDQUFDLENBQUM7UUFDWDtNQUNGO0lBQ0YsQ0FBQztJQUNEeEMsSUFBSSxFQUFFLFNBQVNBLElBQUlBLENBQUEsRUFBRztNQUNwQnNDLFVBQVUsR0FBRyxJQUFJO01BQ2pCbkIsU0FBUyxDQUFDbkIsSUFBSSxDQUFDLENBQUM7SUFDbEIsQ0FBQztJQUNERSxLQUFLLEVBQUUsU0FBU0EsS0FBS0EsQ0FBQSxFQUFHO01BQ3RCcUMsZUFBZSxHQUFHLENBQUM7TUFDbkJELFVBQVUsR0FBRyxLQUFLO01BQ2xCbkIsU0FBUyxDQUFDakIsS0FBSyxDQUFDLENBQUM7SUFDbkIsQ0FBQztJQUNERSxnQkFBZ0IsRUFBRSxTQUFTQSxnQkFBZ0JBLENBQUEsRUFBRztNQUM1QyxNQUFNLElBQUlpQixLQUFLLENBQUMsMkVBQTJFLENBQUM7SUFDOUYsQ0FBQztJQUNEZixvQkFBb0IsRUFBRSxTQUFTQSxvQkFBb0JBLENBQUEsRUFBRztNQUNwRCxPQUFPYSxTQUFTLENBQUNiLG9CQUFvQixDQUFDLENBQUM7SUFDekM7RUFDRixDQUFDO0FBQ0gsQ0FBQztBQUNELFNBQVNtQyxTQUFTQSxDQUFDQyxLQUFLLEVBQUVDLFFBQVEsRUFBRTtFQUNsQyxJQUFJLENBQUNELEtBQUssRUFBRTtJQUNWLE9BQU9DLFFBQVE7RUFDakIsQ0FBQyxNQUFNLElBQUlELEtBQUssWUFBWWxILGNBQWMsQ0FBQ29ILGFBQWEsRUFBRTtJQUN4REYsS0FBSyxDQUFDRyxhQUFhLENBQUNGLFFBQVEsQ0FBQztJQUM3QixPQUFPRCxLQUFLO0VBQ2QsQ0FBQyxNQUFNO0lBQ0wsT0FBTyxZQUFZO01BQ2pCLE9BQU9BLEtBQUssS0FBSyxVQUFVLElBQUlBLEtBQUssQ0FBQWpGLEtBQUEsU0FBSUMsU0FBUyxDQUFDO01BQ2xEaUYsUUFBUSxDQUFBbEYsS0FBQSxTQUFJQyxTQUFTLENBQUM7SUFDeEIsQ0FBQztFQUNIO0FBQ0Y7QUFDQSxTQUFTb0YsV0FBV0EsQ0FBQ0osS0FBSyxFQUFFQyxRQUFRLEVBQUU7RUFDcEMsSUFBSUQsS0FBSyxJQUFJQSxLQUFLLFlBQVlsSCxjQUFjLENBQUNvSCxhQUFhLEVBQUU7SUFDMURGLEtBQUssQ0FBQ0ssZ0JBQWdCLENBQUNKLFFBQVEsQ0FBQztFQUNsQztBQUNGO0FBQ0EsSUFBSUQsS0FBSyxHQUFHLFNBQVNBLEtBQUtBLENBQUNNLFVBQVUsRUFBRXpGLE1BQU0sRUFBRTtFQUM3QyxJQUFJMEYsYUFBYSxHQUFHLElBQUl6SCxjQUFjLENBQUNvSCxhQUFhLENBQUNJLFVBQVUsRUFBRXpGLE1BQU0sQ0FBQztFQUN4RSxJQUFJMEYsYUFBYSxDQUFDQyxVQUFVLEVBQUU7SUFDNUIsT0FBT0QsYUFBYTtFQUN0QixDQUFDLE1BQU07SUFDTCxPQUFPQSxhQUFhLENBQUNFLFlBQVksQ0FBQyxDQUFDO0VBQ3JDO0FBQ0YsQ0FBQztBQWFELElBQUlDLFFBQVEsR0FBRy9ILE9BQU8sQ0FBQ0QsT0FBTyxHQUFHO0VBTy9CaUksS0FBSyxFQUFFbEgsY0FBYyxDQUFDZixPQUFPO0VBTTdCa0ksT0FBTyxFQUFFbEgsZ0JBQWdCLENBQUNoQixPQUFPO0VBSWpDbUksS0FBSyxFQUFFOUcsY0FBYyxDQUFDckIsT0FBTztFQU03Qm9JLGFBQWEsRUFBRTVILHNCQUFzQixDQUFDUixPQUFPO0VBTzdDcUksSUFBSSxFQUFFMUgsYUFBYSxDQUFDWCxPQUFPO0VBTzNCc0YsS0FBSyxFQUFMQSxLQUFLO0VBT0xGLE1BQU0sRUFBTkEsTUFBTTtFQU9ObkIsTUFBTSxFQUFOQSxNQUFNO0VBT04zQyxHQUFHLEVBQUhBLEdBQUc7RUFPSEcsUUFBUSxFQUFSQSxRQUFRO0VBT1JDLE1BQU0sRUFBTkEsTUFBTTtFQU9OQyxRQUFRLEVBQVJBLFFBQVE7RUFPUkMsTUFBTSxFQUFOQSxNQUFNO0VBUU5FLFNBQVMsRUFBVEEsU0FBUztFQU1Ud0UsS0FBSyxFQUFMQSxLQUFLO0VBUUxkLFFBQVEsRUFBUkEsUUFBUTtFQVFSckMsUUFBUSxFQUFSQSxRQUFRO0VBT1JzRCxPQUFPLEVBQVBBLE9BQU87RUFPUEcsSUFBSSxFQUFKQSxJQUFJO0VBT0pVLEtBQUssRUFBTEEsS0FBSztFQU1MZ0IsdUJBQXVCLEVBQUVsSCx3QkFBd0IsQ0FBQ3BCLE9BQU87RUFPekR1SSxpQkFBaUIsRUFBRW5JLGNBQWMsQ0FBQ21JLGlCQUFpQjtFQU9uRGxCLFNBQVMsRUFBVEEsU0FBUztFQUNUSyxXQUFXLEVBQVhBLFdBQVc7RUFJWGMsS0FBSyxFQUFFcEksY0FBYyxDQUFDb0g7QUFDeEIsQ0FBQztBQUNEaUIsTUFBTSxDQUFDeEksT0FBTyxHQUFHQSxPQUFPLENBQUNELE9BQU8iLCJpZ25vcmVMaXN0IjpbXX0=