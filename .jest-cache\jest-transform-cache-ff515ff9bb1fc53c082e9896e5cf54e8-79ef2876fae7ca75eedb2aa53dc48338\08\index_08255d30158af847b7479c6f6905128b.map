{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "_objectSpread2", "_objectWithoutPropertiesLoose2", "React", "_createElement", "_useMergeRefs", "_usePlatformMethods", "_PickerItem", "_StyleSheet", "_excluded", "Picker", "forwardRef", "props", "forwardedRef", "children", "enabled", "onValueChange", "selected<PERSON><PERSON><PERSON>", "style", "testID", "itemStyle", "mode", "prompt", "other", "hostRef", "useRef", "handleChange", "e", "_e$target", "target", "selectedIndex", "value", "supportedProps", "disabled", "undefined", "onChange", "styles", "initial", "platformMethodsRef", "setRef", "ref", "<PERSON><PERSON>", "create", "fontFamily", "fontSize", "margin", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _createElement = _interopRequireDefault(require(\"../createElement\"));\nvar _useMergeRefs = _interopRequireDefault(require(\"../../modules/useMergeRefs\"));\nvar _usePlatformMethods = _interopRequireDefault(require(\"../../modules/usePlatformMethods\"));\nvar _PickerItem = _interopRequireDefault(require(\"./PickerItem\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _excluded = [\"children\", \"enabled\", \"onValueChange\", \"selectedValue\", \"style\", \"testID\", \"itemStyle\", \"mode\", \"prompt\"];\nvar Picker = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var children = props.children,\n    enabled = props.enabled,\n    onValueChange = props.onValueChange,\n    selectedValue = props.selectedValue,\n    style = props.style,\n    testID = props.testID,\n    itemStyle = props.itemStyle,\n    mode = props.mode,\n    prompt = props.prompt,\n    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  var hostRef = React.useRef(null);\n  function handleChange(e) {\n    var _e$target = e.target,\n      selectedIndex = _e$target.selectedIndex,\n      value = _e$target.value;\n    if (onValueChange) {\n      onValueChange(value, selectedIndex);\n    }\n  }\n\n  // $FlowFixMe\n  var supportedProps = (0, _objectSpread2.default)({\n    children,\n    disabled: enabled === false ? true : undefined,\n    onChange: handleChange,\n    style: [styles.initial, style],\n    testID,\n    value: selectedValue\n  }, other);\n  var platformMethodsRef = (0, _usePlatformMethods.default)(supportedProps);\n  var setRef = (0, _useMergeRefs.default)(hostRef, platformMethodsRef, forwardedRef);\n  supportedProps.ref = setRef;\n  return (0, _createElement.default)('select', supportedProps);\n});\n\n// $FlowFixMe\nPicker.Item = _PickerItem.default;\nvar styles = _StyleSheet.default.create({\n  initial: {\n    fontFamily: 'System',\n    fontSize: 'inherit',\n    margin: 0\n  }\n});\nvar _default = exports.default = Picker;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,cAAc,GAAGN,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAIM,8BAA8B,GAAGP,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIO,KAAK,GAAGL,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIQ,cAAc,GAAGT,sBAAsB,CAACC,OAAO,mBAAmB,CAAC,CAAC;AACxE,IAAIS,aAAa,GAAGV,sBAAsB,CAACC,OAAO,6BAA6B,CAAC,CAAC;AACjF,IAAIU,mBAAmB,GAAGX,sBAAsB,CAACC,OAAO,mCAAmC,CAAC,CAAC;AAC7F,IAAIW,WAAW,GAAGZ,sBAAsB,CAACC,OAAO,eAAe,CAAC,CAAC;AACjE,IAAIY,WAAW,GAAGb,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIa,SAAS,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,CAAC;AAC3H,IAAIC,MAAM,GAAgBP,KAAK,CAACQ,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EAClE,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,OAAO,GAAGH,KAAK,CAACG,OAAO;IACvBC,aAAa,GAAGJ,KAAK,CAACI,aAAa;IACnCC,aAAa,GAAGL,KAAK,CAACK,aAAa;IACnCC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,MAAM,GAAGV,KAAK,CAACU,MAAM;IACrBC,KAAK,GAAG,CAAC,CAAC,EAAErB,8BAA8B,CAACL,OAAO,EAAEe,KAAK,EAAEH,SAAS,CAAC;EACvE,IAAIe,OAAO,GAAGrB,KAAK,CAACsB,MAAM,CAAC,IAAI,CAAC;EAChC,SAASC,YAAYA,CAACC,CAAC,EAAE;IACvB,IAAIC,SAAS,GAAGD,CAAC,CAACE,MAAM;MACtBC,aAAa,GAAGF,SAAS,CAACE,aAAa;MACvCC,KAAK,GAAGH,SAAS,CAACG,KAAK;IACzB,IAAIf,aAAa,EAAE;MACjBA,aAAa,CAACe,KAAK,EAAED,aAAa,CAAC;IACrC;EACF;EAGA,IAAIE,cAAc,GAAG,CAAC,CAAC,EAAE/B,cAAc,CAACJ,OAAO,EAAE;IAC/CiB,QAAQ,EAARA,QAAQ;IACRmB,QAAQ,EAAElB,OAAO,KAAK,KAAK,GAAG,IAAI,GAAGmB,SAAS;IAC9CC,QAAQ,EAAET,YAAY;IACtBR,KAAK,EAAE,CAACkB,MAAM,CAACC,OAAO,EAAEnB,KAAK,CAAC;IAC9BC,MAAM,EAANA,MAAM;IACNY,KAAK,EAAEd;EACT,CAAC,EAAEM,KAAK,CAAC;EACT,IAAIe,kBAAkB,GAAG,CAAC,CAAC,EAAEhC,mBAAmB,CAACT,OAAO,EAAEmC,cAAc,CAAC;EACzE,IAAIO,MAAM,GAAG,CAAC,CAAC,EAAElC,aAAa,CAACR,OAAO,EAAE2B,OAAO,EAAEc,kBAAkB,EAAEzB,YAAY,CAAC;EAClFmB,cAAc,CAACQ,GAAG,GAAGD,MAAM;EAC3B,OAAO,CAAC,CAAC,EAAEnC,cAAc,CAACP,OAAO,EAAE,QAAQ,EAAEmC,cAAc,CAAC;AAC9D,CAAC,CAAC;AAGFtB,MAAM,CAAC+B,IAAI,GAAGlC,WAAW,CAACV,OAAO;AACjC,IAAIuC,MAAM,GAAG5B,WAAW,CAACX,OAAO,CAAC6C,MAAM,CAAC;EACtCL,OAAO,EAAE;IACPM,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AACF,IAAIC,QAAQ,GAAG/C,OAAO,CAACF,OAAO,GAAGa,MAAM;AACvCqC,MAAM,CAAChD,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}