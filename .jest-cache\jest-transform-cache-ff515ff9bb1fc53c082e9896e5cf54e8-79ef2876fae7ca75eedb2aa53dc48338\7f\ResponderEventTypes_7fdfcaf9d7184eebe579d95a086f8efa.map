{"version": 3, "names": ["exports", "__esModule", "TOUCH_START", "TOUCH_MOVE", "TOUCH_END", "TOUCH_CANCEL", "SELECTION_CHANGE", "SELECT", "SCROLL", "MOUSE_UP", "MOUSE_MOVE", "MOUSE_DOWN", "MOUSE_CANCEL", "FOCUS_OUT", "CONTEXT_MENU", "BLUR", "isCancelish", "<PERSON><PERSON><PERSON><PERSON>", "isMoveish", "isScroll", "isSelectionChange", "isStartish", "eventType"], "sources": ["ResponderEventTypes.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.TOUCH_START = exports.TOUCH_MOVE = exports.TOUCH_END = exports.TOUCH_CANCEL = exports.SELECTION_CHANGE = exports.SELECT = exports.SCROLL = exports.MOUSE_UP = exports.MOUSE_MOVE = exports.MOUSE_DOWN = exports.MOUSE_CANCEL = exports.FOCUS_OUT = exports.CONTEXT_MENU = exports.BLUR = void 0;\nexports.isCancelish = isCancelish;\nexports.isEndish = isEndish;\nexports.isMoveish = isMoveish;\nexports.isScroll = isScroll;\nexports.isSelectionChange = isSelectionChange;\nexports.isStartish = isStartish;\n/**\n * Copyright (c) Nicolas Gallagher\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar BLUR = exports.BLUR = 'blur';\nvar CONTEXT_MENU = exports.CONTEXT_MENU = 'contextmenu';\nvar FOCUS_OUT = exports.FOCUS_OUT = 'focusout';\nvar MOUSE_DOWN = exports.MOUSE_DOWN = 'mousedown';\nvar MOUSE_MOVE = exports.MOUSE_MOVE = 'mousemove';\nvar MOUSE_UP = exports.MOUSE_UP = 'mouseup';\nvar MOUSE_CANCEL = exports.MOUSE_CANCEL = 'dragstart';\nvar TOUCH_START = exports.TOUCH_START = 'touchstart';\nvar TOUCH_MOVE = exports.TOUCH_MOVE = 'touchmove';\nvar TOUCH_END = exports.TOUCH_END = 'touchend';\nvar TOUCH_CANCEL = exports.TOUCH_CANCEL = 'touchcancel';\nvar SCROLL = exports.SCROLL = 'scroll';\nvar SELECT = exports.SELECT = 'select';\nvar SELECTION_CHANGE = exports.SELECTION_CHANGE = 'selectionchange';\nfunction isStartish(eventType) {\n  return eventType === TOUCH_START || eventType === MOUSE_DOWN;\n}\nfunction isMoveish(eventType) {\n  return eventType === TOUCH_MOVE || eventType === MOUSE_MOVE;\n}\nfunction isEndish(eventType) {\n  return eventType === TOUCH_END || eventType === MOUSE_UP || isCancelish(eventType);\n}\nfunction isCancelish(eventType) {\n  return eventType === TOUCH_CANCEL || eventType === MOUSE_CANCEL;\n}\nfunction isScroll(eventType) {\n  return eventType === SCROLL;\n}\nfunction isSelectionChange(eventType) {\n  return eventType === SELECT || eventType === SELECTION_CHANGE;\n}"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,WAAW,GAAGF,OAAO,CAACG,UAAU,GAAGH,OAAO,CAACI,SAAS,GAAGJ,OAAO,CAACK,YAAY,GAAGL,OAAO,CAACM,gBAAgB,GAAGN,OAAO,CAACO,MAAM,GAAGP,OAAO,CAACQ,MAAM,GAAGR,OAAO,CAACS,QAAQ,GAAGT,OAAO,CAACU,UAAU,GAAGV,OAAO,CAACW,UAAU,GAAGX,OAAO,CAACY,YAAY,GAAGZ,OAAO,CAACa,SAAS,GAAGb,OAAO,CAACc,YAAY,GAAGd,OAAO,CAACe,IAAI,GAAG,KAAK,CAAC;AACvSf,OAAO,CAACgB,WAAW,GAAGA,WAAW;AACjChB,OAAO,CAACiB,QAAQ,GAAGA,QAAQ;AAC3BjB,OAAO,CAACkB,SAAS,GAAGA,SAAS;AAC7BlB,OAAO,CAACmB,QAAQ,GAAGA,QAAQ;AAC3BnB,OAAO,CAACoB,iBAAiB,GAAGA,iBAAiB;AAC7CpB,OAAO,CAACqB,UAAU,GAAGA,UAAU;AAU/B,IAAIN,IAAI,GAAGf,OAAO,CAACe,IAAI,GAAG,MAAM;AAChC,IAAID,YAAY,GAAGd,OAAO,CAACc,YAAY,GAAG,aAAa;AACvD,IAAID,SAAS,GAAGb,OAAO,CAACa,SAAS,GAAG,UAAU;AAC9C,IAAIF,UAAU,GAAGX,OAAO,CAACW,UAAU,GAAG,WAAW;AACjD,IAAID,UAAU,GAAGV,OAAO,CAACU,UAAU,GAAG,WAAW;AACjD,IAAID,QAAQ,GAAGT,OAAO,CAACS,QAAQ,GAAG,SAAS;AAC3C,IAAIG,YAAY,GAAGZ,OAAO,CAACY,YAAY,GAAG,WAAW;AACrD,IAAIV,WAAW,GAAGF,OAAO,CAACE,WAAW,GAAG,YAAY;AACpD,IAAIC,UAAU,GAAGH,OAAO,CAACG,UAAU,GAAG,WAAW;AACjD,IAAIC,SAAS,GAAGJ,OAAO,CAACI,SAAS,GAAG,UAAU;AAC9C,IAAIC,YAAY,GAAGL,OAAO,CAACK,YAAY,GAAG,aAAa;AACvD,IAAIG,MAAM,GAAGR,OAAO,CAACQ,MAAM,GAAG,QAAQ;AACtC,IAAID,MAAM,GAAGP,OAAO,CAACO,MAAM,GAAG,QAAQ;AACtC,IAAID,gBAAgB,GAAGN,OAAO,CAACM,gBAAgB,GAAG,iBAAiB;AACnE,SAASe,UAAUA,CAACC,SAAS,EAAE;EAC7B,OAAOA,SAAS,KAAKpB,WAAW,IAAIoB,SAAS,KAAKX,UAAU;AAC9D;AACA,SAASO,SAASA,CAACI,SAAS,EAAE;EAC5B,OAAOA,SAAS,KAAKnB,UAAU,IAAImB,SAAS,KAAKZ,UAAU;AAC7D;AACA,SAASO,QAAQA,CAACK,SAAS,EAAE;EAC3B,OAAOA,SAAS,KAAKlB,SAAS,IAAIkB,SAAS,KAAKb,QAAQ,IAAIO,WAAW,CAACM,SAAS,CAAC;AACpF;AACA,SAASN,WAAWA,CAACM,SAAS,EAAE;EAC9B,OAAOA,SAAS,KAAKjB,YAAY,IAAIiB,SAAS,KAAKV,YAAY;AACjE;AACA,SAASO,QAAQA,CAACG,SAAS,EAAE;EAC3B,OAAOA,SAAS,KAAKd,MAAM;AAC7B;AACA,SAASY,iBAAiBA,CAACE,SAAS,EAAE;EACpC,OAAOA,SAAS,KAAKf,MAAM,IAAIe,SAAS,KAAKhB,gBAAgB;AAC/D", "ignoreList": []}