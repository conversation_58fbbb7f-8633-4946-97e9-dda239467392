{"version": 3, "names": ["useState", "useEffect", "useCallback", "useMemo", "Platform", "gpuAccelerationManager", "nativeModuleManager", "advancedMemoryManager", "backgroundProcessingManager", "useEdgeOptimization", "useNativeOptimization", "initialConfig", "arguments", "length", "undefined", "cov_61901ie07", "b", "f", "_ref", "s", "edgeState", "state", "_ref2", "Object", "assign", "enableGPUAcceleration", "enableNativeModules", "enableAdvancedMemory", "enableBackgroundProcessing", "autoOptimization", "optimizationInterval", "aggressiveOptimization", "_ref3", "_slicedToArray", "config", "setConfig", "_ref4", "isInitialized", "isOptimizing", "gpuAcceleration", "available", "utilization", "averageExecutionTime", "tasksPerSecond", "efficiency", "nativeModules", "totalModules", "availableModules", "successRate", "bridgeEfficiency", "memoryManagement", "totalAllocated", "totalAvailable", "memoryPressure", "leakCount", "gcEfficiency", "fragmentationRatio", "backgroundProcessing", "activeTaskCount", "queuedTaskCount", "totalTasksExecuted", "currentStrategy", "platformOptimization", "hardwareAcceleration", "platformIntegration", "performanceGain", "_ref5", "setState", "initialize", "_asyncToGenerator", "prev", "initPromises", "push", "Promise", "resolve", "all", "updateNativeState", "console", "log", "error", "executeGPUTask", "_ref7", "task", "warn", "result", "executeComputeTask", "success", "executionTime", "processingUnit", "performance", "_x", "apply", "executeNativeOperation", "_ref8", "operation", "nativeModule", "method", "_x2", "optimizeMemory", "aggressive", "<PERSON><PERSON><PERSON><PERSON>", "optimizedPools", "leaksResolved", "scheduleBackgroundTask", "taskId", "registerTask", "scheduleTask", "analyzeVideo", "_ref0", "videoData", "analysisType", "frameRate", "resolution", "width", "height", "outputFormat", "_x3", "_x4", "runMLInference", "_ref1", "modelId", "inputData", "inputTensor", "inputShape", "outputShape", "precision", "accuracy", "_x5", "_x6", "getNativeMetrics", "metrics", "gpu", "getGPUPerformanceMetrics", "getNativeModuleMetrics", "memory", "getMemoryMetrics", "getProcessingMetrics", "platform", "os", "OS", "version", "Version", "constants", "updateConfig", "newConfig", "newState", "gpuMetrics", "capabilities", "nativeMetrics", "memoryMetrics", "level", "bgMetrics", "calculateHardwareAcceleration", "calculatePlatformIntegration", "calculatePerformanceGain", "score", "Math", "min", "moduleRatio", "memoryScore", "gain", "memoryGain", "interval", "setInterval", "optimizeGPUPerformance", "clearInterval", "actions"], "sources": ["useNativeOptimization.ts"], "sourcesContent": ["/**\n * Native Optimization Hook\n * \n * Unified hook that integrates all native optimization and hardware acceleration\n * systems for ultimate performance and platform-specific enhancements.\n */\n\nimport { useState, useEffect, useCallback, useMemo } from 'react';\nimport { Platform } from 'react-native';\nimport { gpuAccelerationManager } from '@/services/native/GPUAccelerationManager';\nimport { nativeModuleManager } from '@/services/native/NativeModuleManager';\nimport { advancedMemoryManager } from '@/services/native/AdvancedMemoryManager';\nimport { backgroundProcessingManager } from '@/services/native/BackgroundProcessingManager';\nimport { useEdgeOptimization } from './useEdgeOptimization';\n\ninterface NativeOptimizationState {\n  isInitialized: boolean;\n  isOptimizing: boolean;\n  gpuAcceleration: {\n    available: boolean;\n    utilization: number;\n    averageExecutionTime: number;\n    tasksPerSecond: number;\n    efficiency: number;\n  };\n  nativeModules: {\n    totalModules: number;\n    availableModules: number;\n    averageExecutionTime: number;\n    successRate: number;\n    bridgeEfficiency: number;\n  };\n  memoryManagement: {\n    totalAllocated: number;\n    totalAvailable: number;\n    memoryPressure: string;\n    leakCount: number;\n    gcEfficiency: number;\n    fragmentationRatio: number;\n  };\n  backgroundProcessing: {\n    activeTaskCount: number;\n    queuedTaskCount: number;\n    totalTasksExecuted: number;\n    successRate: number;\n    currentStrategy: string;\n  };\n  platformOptimization: {\n    hardwareAcceleration: number;\n    platformIntegration: number;\n    performanceGain: number;\n  };\n}\n\ninterface NativeOptimizationConfig {\n  enableGPUAcceleration: boolean;\n  enableNativeModules: boolean;\n  enableAdvancedMemory: boolean;\n  enableBackgroundProcessing: boolean;\n  autoOptimization: boolean;\n  optimizationInterval: number;\n  aggressiveOptimization: boolean;\n}\n\ninterface UseNativeOptimizationReturn {\n  state: NativeOptimizationState;\n  actions: {\n    initialize: () => Promise<void>;\n    executeGPUTask: (task: any) => Promise<any>;\n    executeNativeOperation: (operation: any) => Promise<any>;\n    optimizeMemory: (aggressive?: boolean) => Promise<any>;\n    scheduleBackgroundTask: (task: any) => string;\n    analyzeVideo: (videoData: ArrayBuffer, analysisType: string) => Promise<any>;\n    runMLInference: (modelId: string, inputData: Float32Array) => Promise<any>;\n    getNativeMetrics: () => Promise<any>;\n    updateConfig: (config: Partial<NativeOptimizationConfig>) => void;\n  };\n  config: NativeOptimizationConfig;\n}\n\n/**\n * Native Optimization & Hardware Acceleration Hook\n */\nexport function useNativeOptimization(\n  initialConfig: Partial<NativeOptimizationConfig> = {}\n): UseNativeOptimizationReturn {\n  const { state: edgeState } = useEdgeOptimization();\n  \n  const [config, setConfig] = useState<NativeOptimizationConfig>({\n    enableGPUAcceleration: true,\n    enableNativeModules: true,\n    enableAdvancedMemory: true,\n    enableBackgroundProcessing: true,\n    autoOptimization: true,\n    optimizationInterval: 120000, // 2 minutes\n    aggressiveOptimization: false,\n    ...initialConfig,\n  });\n\n  const [state, setState] = useState<NativeOptimizationState>({\n    isInitialized: false,\n    isOptimizing: false,\n    gpuAcceleration: {\n      available: false,\n      utilization: 0,\n      averageExecutionTime: 0,\n      tasksPerSecond: 0,\n      efficiency: 0,\n    },\n    nativeModules: {\n      totalModules: 0,\n      availableModules: 0,\n      averageExecutionTime: 0,\n      successRate: 0,\n      bridgeEfficiency: 0,\n    },\n    memoryManagement: {\n      totalAllocated: 0,\n      totalAvailable: 0,\n      memoryPressure: 'normal',\n      leakCount: 0,\n      gcEfficiency: 0,\n      fragmentationRatio: 0,\n    },\n    backgroundProcessing: {\n      activeTaskCount: 0,\n      queuedTaskCount: 0,\n      totalTasksExecuted: 0,\n      successRate: 0,\n      currentStrategy: 'balanced',\n    },\n    platformOptimization: {\n      hardwareAcceleration: 0,\n      platformIntegration: 0,\n      performanceGain: 0,\n    },\n  });\n\n  /**\n   * Initialize all native optimization systems\n   */\n  const initialize = useCallback(async () => {\n    if (state.isInitialized) return;\n\n    try {\n      setState(prev => ({ ...prev, isOptimizing: true }));\n\n      // Initialize systems based on configuration\n      const initPromises: Promise<any>[] = [];\n\n      if (config.enableGPUAcceleration) {\n        // GPU acceleration manager is auto-initialized\n        initPromises.push(Promise.resolve());\n      }\n\n      if (config.enableNativeModules) {\n        // Native module manager is auto-initialized\n        initPromises.push(Promise.resolve());\n      }\n\n      if (config.enableAdvancedMemory) {\n        // Advanced memory manager is auto-initialized\n        initPromises.push(Promise.resolve());\n      }\n\n      if (config.enableBackgroundProcessing) {\n        // Background processing manager is auto-initialized\n        initPromises.push(Promise.resolve());\n      }\n\n      await Promise.all(initPromises);\n\n      // Get initial metrics\n      await updateNativeState();\n\n      setState(prev => ({\n        ...prev,\n        isInitialized: true,\n        isOptimizing: false,\n      }));\n\n      console.log('Native Optimization systems initialized successfully');\n\n    } catch (error) {\n      console.error('Failed to initialize native optimization:', error);\n      setState(prev => ({ ...prev, isOptimizing: false }));\n    }\n  }, [state.isInitialized, config]);\n\n  /**\n   * Execute GPU-accelerated task\n   */\n  const executeGPUTask = useCallback(async (task: any) => {\n    if (!state.isInitialized || !config.enableGPUAcceleration) {\n      console.warn('GPU acceleration not available');\n      return null;\n    }\n\n    try {\n      const result = await gpuAccelerationManager.executeComputeTask(task);\n      \n      console.log(`GPU task executed:`, {\n        success: result.success,\n        executionTime: result.executionTime,\n        processingUnit: result.processingUnit,\n        efficiency: result.performance.efficiency,\n      });\n\n      return result;\n\n    } catch (error) {\n      console.error('Failed to execute GPU task:', error);\n      return null;\n    }\n  }, [state.isInitialized, config.enableGPUAcceleration]);\n\n  /**\n   * Execute native module operation\n   */\n  const executeNativeOperation = useCallback(async (operation: any) => {\n    if (!state.isInitialized || !config.enableNativeModules) {\n      console.warn('Native modules not available');\n      return null;\n    }\n\n    try {\n      const result = await nativeModuleManager.executeNativeOperation(operation);\n      \n      console.log(`Native operation executed:`, {\n        success: result.success,\n        executionTime: result.executionTime,\n        nativeModule: result.nativeModule,\n        method: result.method,\n      });\n\n      return result;\n\n    } catch (error) {\n      console.error('Failed to execute native operation:', error);\n      return null;\n    }\n  }, [state.isInitialized, config.enableNativeModules]);\n\n  /**\n   * Optimize memory usage\n   */\n  const optimizeMemory = useCallback(async (aggressive: boolean = config.aggressiveOptimization) => {\n    if (!state.isInitialized || !config.enableAdvancedMemory) {\n      console.warn('Advanced memory management not available');\n      return null;\n    }\n\n    try {\n      const result = await advancedMemoryManager.optimizeMemory(aggressive);\n      \n      console.log(`Memory optimization completed:`, {\n        freedMemory: result.freedMemory,\n        optimizedPools: result.optimizedPools,\n        leaksResolved: result.leaksResolved,\n      });\n\n      return result;\n\n    } catch (error) {\n      console.error('Failed to optimize memory:', error);\n      return null;\n    }\n  }, [state.isInitialized, config.enableAdvancedMemory, config.aggressiveOptimization]);\n\n  /**\n   * Schedule background task\n   */\n  const scheduleBackgroundTask = useCallback((task: any) => {\n    if (!state.isInitialized || !config.enableBackgroundProcessing) {\n      console.warn('Background processing not available');\n      return '';\n    }\n\n    try {\n      const taskId = backgroundProcessingManager.registerTask(task);\n      backgroundProcessingManager.scheduleTask(taskId);\n      \n      console.log(`Background task scheduled: ${taskId}`);\n      return taskId;\n\n    } catch (error) {\n      console.error('Failed to schedule background task:', error);\n      return '';\n    }\n  }, [state.isInitialized, config.enableBackgroundProcessing]);\n\n  /**\n   * Analyze video with GPU acceleration\n   */\n  const analyzeVideo = useCallback(async (\n    videoData: ArrayBuffer,\n    analysisType: string\n  ) => {\n    if (!state.isInitialized || !config.enableGPUAcceleration) {\n      console.warn('GPU acceleration not available for video analysis');\n      return null;\n    }\n\n    try {\n      const result = await gpuAccelerationManager.analyzeVideo({\n        videoData,\n        analysisType: analysisType as any,\n        frameRate: 30,\n        resolution: { width: 1920, height: 1080 },\n        outputFormat: 'keypoints',\n      });\n\n      console.log(`Video analysis completed:`, {\n        success: result.success,\n        executionTime: result.executionTime,\n        processingUnit: result.processingUnit,\n      });\n\n      return result;\n\n    } catch (error) {\n      console.error('Failed to analyze video:', error);\n      return null;\n    }\n  }, [state.isInitialized, config.enableGPUAcceleration]);\n\n  /**\n   * Run ML inference with hardware acceleration\n   */\n  const runMLInference = useCallback(async (\n    modelId: string,\n    inputData: Float32Array\n  ) => {\n    if (!state.isInitialized || !config.enableGPUAcceleration) {\n      console.warn('GPU acceleration not available for ML inference');\n      return null;\n    }\n\n    try {\n      const result = await gpuAccelerationManager.runMLInference({\n        modelId,\n        inputTensor: inputData,\n        inputShape: [1, inputData.length],\n        outputShape: [1, 10], // Example output shape\n        precision: 'fp32',\n      });\n\n      console.log(`ML inference completed:`, {\n        success: result.success,\n        executionTime: result.executionTime,\n        accuracy: result.performance.accuracy,\n      });\n\n      return result;\n\n    } catch (error) {\n      console.error('Failed to run ML inference:', error);\n      return null;\n    }\n  }, [state.isInitialized, config.enableGPUAcceleration]);\n\n  /**\n   * Get comprehensive native metrics\n   */\n  const getNativeMetrics = useCallback(async () => {\n    if (!state.isInitialized) return null;\n\n    try {\n      const metrics = {\n        gpu: config.enableGPUAcceleration ? gpuAccelerationManager.getGPUPerformanceMetrics() : null,\n        nativeModules: config.enableNativeModules ? nativeModuleManager.getNativeModuleMetrics() : null,\n        memory: config.enableAdvancedMemory ? advancedMemoryManager.getMemoryMetrics() : null,\n        backgroundProcessing: config.enableBackgroundProcessing ? backgroundProcessingManager.getProcessingMetrics() : null,\n        platform: {\n          os: Platform.OS,\n          version: Platform.Version,\n          constants: Platform.constants,\n        },\n      };\n\n      return metrics;\n    } catch (error) {\n      console.error('Failed to get native metrics:', error);\n      return null;\n    }\n  }, [state.isInitialized, config]);\n\n  /**\n   * Update configuration\n   */\n  const updateConfig = useCallback((newConfig: Partial<NativeOptimizationConfig>) => {\n    setConfig(prev => ({ ...prev, ...newConfig }));\n  }, []);\n\n  /**\n   * Update native state with current metrics\n   */\n  const updateNativeState = useCallback(async () => {\n    try {\n      const newState: Partial<NativeOptimizationState> = {};\n\n      // GPU acceleration metrics\n      if (config.enableGPUAcceleration) {\n        const gpuMetrics = gpuAccelerationManager.getGPUPerformanceMetrics();\n        newState.gpuAcceleration = {\n          available: gpuMetrics.capabilities !== null,\n          utilization: gpuMetrics.utilization,\n          averageExecutionTime: gpuMetrics.averageExecutionTime,\n          tasksPerSecond: gpuMetrics.tasksPerSecond,\n          efficiency: gpuMetrics.efficiency,\n        };\n      }\n\n      // Native modules metrics\n      if (config.enableNativeModules) {\n        const nativeMetrics = nativeModuleManager.getNativeModuleMetrics();\n        newState.nativeModules = {\n          totalModules: nativeMetrics.totalModules,\n          availableModules: nativeMetrics.availableModules,\n          averageExecutionTime: nativeMetrics.averageExecutionTime,\n          successRate: nativeMetrics.successRate,\n          bridgeEfficiency: nativeMetrics.bridgeEfficiency,\n        };\n      }\n\n      // Memory management metrics\n      if (config.enableAdvancedMemory) {\n        const memoryMetrics = advancedMemoryManager.getMemoryMetrics();\n        newState.memoryManagement = {\n          totalAllocated: memoryMetrics.totalAllocated,\n          totalAvailable: memoryMetrics.totalAvailable,\n          memoryPressure: memoryMetrics.memoryPressure.level,\n          leakCount: memoryMetrics.leakCount,\n          gcEfficiency: memoryMetrics.gcEfficiency,\n          fragmentationRatio: memoryMetrics.fragmentationRatio,\n        };\n      }\n\n      // Background processing metrics\n      if (config.enableBackgroundProcessing) {\n        const bgMetrics = backgroundProcessingManager.getProcessingMetrics();\n        newState.backgroundProcessing = {\n          activeTaskCount: bgMetrics.activeTaskCount,\n          queuedTaskCount: bgMetrics.queuedTaskCount,\n          totalTasksExecuted: bgMetrics.totalTasksExecuted,\n          successRate: bgMetrics.successRate,\n          currentStrategy: bgMetrics.currentStrategy,\n        };\n      }\n\n      // Platform optimization metrics\n      newState.platformOptimization = {\n        hardwareAcceleration: calculateHardwareAcceleration(),\n        platformIntegration: calculatePlatformIntegration(),\n        performanceGain: calculatePerformanceGain(),\n      };\n\n      setState(prev => ({ ...prev, ...newState }));\n\n    } catch (error) {\n      console.error('Failed to update native state:', error);\n    }\n  }, [config]);\n\n  // Helper functions for platform optimization metrics\n  const calculateHardwareAcceleration = (): number => {\n    let score = 0;\n    \n    if (config.enableGPUAcceleration && state.gpuAcceleration.available) {\n      score += state.gpuAcceleration.efficiency * 40; // 40% weight\n    }\n    \n    if (config.enableNativeModules) {\n      score += state.nativeModules.bridgeEfficiency * 30; // 30% weight\n    }\n    \n    if (config.enableAdvancedMemory) {\n      score += (100 - state.memoryManagement.fragmentationRatio) * 30; // 30% weight\n    }\n    \n    return Math.min(score, 100);\n  };\n\n  const calculatePlatformIntegration = (): number => {\n    let score = 0;\n    \n    // Native modules integration\n    if (config.enableNativeModules) {\n      const moduleRatio = state.nativeModules.totalModules > 0 \n        ? state.nativeModules.availableModules / state.nativeModules.totalModules \n        : 0;\n      score += moduleRatio * 50; // 50% weight\n    }\n    \n    // Background processing integration\n    if (config.enableBackgroundProcessing) {\n      score += (state.backgroundProcessing.successRate / 100) * 30; // 30% weight\n    }\n    \n    // Memory management integration\n    if (config.enableAdvancedMemory) {\n      const memoryScore = state.memoryManagement.memoryPressure === 'normal' ? 100 : \n                         state.memoryManagement.memoryPressure === 'warning' ? 70 : 30;\n      score += (memoryScore / 100) * 20; // 20% weight\n    }\n    \n    return Math.min(score, 100);\n  };\n\n  const calculatePerformanceGain = (): number => {\n    // Calculate overall performance gain from native optimizations\n    let gain = 0;\n    \n    // GPU acceleration gain\n    if (config.enableGPUAcceleration && state.gpuAcceleration.available) {\n      gain += state.gpuAcceleration.efficiency * 50; // Up to 50% gain\n    }\n    \n    // Native modules gain\n    if (config.enableNativeModules) {\n      gain += (state.nativeModules.successRate / 100) * 30; // Up to 30% gain\n    }\n    \n    // Memory optimization gain\n    if (config.enableAdvancedMemory) {\n      const memoryGain = state.memoryManagement.memoryPressure === 'normal' ? 20 : 10;\n      gain += memoryGain;\n    }\n    \n    return Math.min(gain, 100);\n  };\n\n  // Auto-optimization effect\n  useEffect(() => {\n    if (!config.autoOptimization || !state.isInitialized) return;\n\n    const interval = setInterval(async () => {\n      // Perform automatic optimizations\n      if (config.enableAdvancedMemory && state.memoryManagement.memoryPressure !== 'normal') {\n        await optimizeMemory(false);\n      }\n      \n      if (config.enableGPUAcceleration) {\n        await gpuAccelerationManager.optimizeGPUPerformance();\n      }\n    }, config.optimizationInterval);\n\n    return () => clearInterval(interval);\n  }, [config.autoOptimization, config.optimizationInterval, state.isInitialized, optimizeMemory]);\n\n  // Periodic state updates\n  useEffect(() => {\n    if (!state.isInitialized) return;\n\n    const interval = setInterval(() => {\n      updateNativeState();\n    }, 30000); // Update every 30 seconds\n\n    return () => clearInterval(interval);\n  }, [state.isInitialized, updateNativeState]);\n\n  // Initialize on mount\n  useEffect(() => {\n    if (!state.isInitialized) {\n      initialize();\n    }\n  }, [state.isInitialized, initialize]);\n\n  // Memoized return value\n  return useMemo(() => ({\n    state,\n    actions: {\n      initialize,\n      executeGPUTask,\n      executeNativeOperation,\n      optimizeMemory,\n      scheduleBackgroundTask,\n      analyzeVideo,\n      runMLInference,\n      getNativeMetrics,\n      updateConfig,\n    },\n    config,\n  }), [\n    state,\n    initialize,\n    executeGPUTask,\n    executeNativeOperation,\n    optimizeMemory,\n    scheduleBackgroundTask,\n    analyzeVideo,\n    runMLInference,\n    getNativeMetrics,\n    updateConfig,\n    config,\n  ]);\n}\n\nexport default useNativeOptimization;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACjE,SAASC,QAAQ,QAAQ,cAAc;AACvC,SAASC,sBAAsB;AAC/B,SAASC,mBAAmB;AAC5B,SAASC,qBAAqB;AAC9B,SAASC,2BAA2B;AACpC,SAASC,mBAAmB;AAsE5B,OAAO,SAASC,qBAAqBA,CAAA,EAEN;EAAA,IAD7BC,aAAgD,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,aAAA,GAAAC,CAAA,UAAG,CAAC,CAAC;EAAAD,aAAA,GAAAE,CAAA;EAErD,IAAAC,IAAA,IAAAH,aAAA,GAAAI,CAAA,OAA6BV,mBAAmB,CAAC,CAAC;IAAnCW,SAAS,GAAAF,IAAA,CAAhBG,KAAK;EAEb,IAAAC,KAAA,IAAAP,aAAA,GAAAI,CAAA,OAA4BnB,QAAQ,CAAAuB,MAAA,CAAAC,MAAA;MAClCC,qBAAqB,EAAE,IAAI;MAC3BC,mBAAmB,EAAE,IAAI;MACzBC,oBAAoB,EAAE,IAAI;MAC1BC,0BAA0B,EAAE,IAAI;MAChCC,gBAAgB,EAAE,IAAI;MACtBC,oBAAoB,EAAE,MAAM;MAC5BC,sBAAsB,EAAE;IAAK,GAC1BpB,aAAa,CACjB,CAAC;IAAAqB,KAAA,GAAAC,cAAA,CAAAX,KAAA;IATKY,MAAM,GAAAF,KAAA;IAAEG,SAAS,GAAAH,KAAA;EAWxB,IAAAI,KAAA,IAAArB,aAAA,GAAAI,CAAA,OAA0BnB,QAAQ,CAA0B;MAC1DqC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,KAAK;MACnBC,eAAe,EAAE;QACfC,SAAS,EAAE,KAAK;QAChBC,WAAW,EAAE,CAAC;QACdC,oBAAoB,EAAE,CAAC;QACvBC,cAAc,EAAE,CAAC;QACjBC,UAAU,EAAE;MACd,CAAC;MACDC,aAAa,EAAE;QACbC,YAAY,EAAE,CAAC;QACfC,gBAAgB,EAAE,CAAC;QACnBL,oBAAoB,EAAE,CAAC;QACvBM,WAAW,EAAE,CAAC;QACdC,gBAAgB,EAAE;MACpB,CAAC;MACDC,gBAAgB,EAAE;QAChBC,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,CAAC;QACZC,YAAY,EAAE,CAAC;QACfC,kBAAkB,EAAE;MACtB,CAAC;MACDC,oBAAoB,EAAE;QACpBC,eAAe,EAAE,CAAC;QAClBC,eAAe,EAAE,CAAC;QAClBC,kBAAkB,EAAE,CAAC;QACrBZ,WAAW,EAAE,CAAC;QACda,eAAe,EAAE;MACnB,CAAC;MACDC,oBAAoB,EAAE;QACpBC,oBAAoB,EAAE,CAAC;QACvBC,mBAAmB,EAAE,CAAC;QACtBC,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;IAAAC,KAAA,GAAAjC,cAAA,CAAAG,KAAA;IArCKf,KAAK,GAAA6C,KAAA;IAAEC,QAAQ,GAAAD,KAAA;EA0CtB,IAAME,UAAU,IAAArD,aAAA,GAAAI,CAAA,OAAGjB,WAAW,CAAAmE,iBAAA,CAAC,aAAY;IAAAtD,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IACzC,IAAIE,KAAK,CAACgB,aAAa,EAAE;MAAAtB,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAEhC,IAAI;MAAAJ,aAAA,GAAAI,CAAA;MACFgD,QAAQ,CAAC,UAAAG,IAAI,EAAK;QAAAvD,aAAA,GAAAE,CAAA;QAAAF,aAAA,GAAAI,CAAA;QAAA,OAAAI,MAAA,CAAAC,MAAA,KAAK8C,IAAI;UAAEhC,YAAY,EAAE;QAAI;MAAC,CAAE,CAAC;MAGnD,IAAMiC,YAA4B,IAAAxD,aAAA,GAAAI,CAAA,OAAG,EAAE;MAACJ,aAAA,GAAAI,CAAA;MAExC,IAAIe,MAAM,CAACT,qBAAqB,EAAE;QAAAV,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QAEhCoD,YAAY,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;MACtC,CAAC;QAAA3D,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,IAAIe,MAAM,CAACR,mBAAmB,EAAE;QAAAX,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QAE9BoD,YAAY,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;MACtC,CAAC;QAAA3D,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,IAAIe,MAAM,CAACP,oBAAoB,EAAE;QAAAZ,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QAE/BoD,YAAY,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;MACtC,CAAC;QAAA3D,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,IAAIe,MAAM,CAACN,0BAA0B,EAAE;QAAAb,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QAErCoD,YAAY,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;MACtC,CAAC;QAAA3D,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,MAAMsD,OAAO,CAACE,GAAG,CAACJ,YAAY,CAAC;MAACxD,aAAA,GAAAI,CAAA;MAGhC,MAAMyD,iBAAiB,CAAC,CAAC;MAAC7D,aAAA,GAAAI,CAAA;MAE1BgD,QAAQ,CAAC,UAAAG,IAAI,EAAK;QAAAvD,aAAA,GAAAE,CAAA;QAAAF,aAAA,GAAAI,CAAA;QAAA,OAAAI,MAAA,CAAAC,MAAA,KACb8C,IAAI;UACPjC,aAAa,EAAE,IAAI;UACnBC,YAAY,EAAE;QAAK;MACrB,CAAE,CAAC;MAACvB,aAAA,GAAAI,CAAA;MAEJ0D,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IAErE,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAAhE,aAAA,GAAAI,CAAA;MACd0D,OAAO,CAACE,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MAAChE,aAAA,GAAAI,CAAA;MAClEgD,QAAQ,CAAC,UAAAG,IAAI,EAAK;QAAAvD,aAAA,GAAAE,CAAA;QAAAF,aAAA,GAAAI,CAAA;QAAA,OAAAI,MAAA,CAAAC,MAAA,KAAK8C,IAAI;UAAEhC,YAAY,EAAE;QAAK;MAAC,CAAE,CAAC;IACtD;EACF,CAAC,GAAE,CAACjB,KAAK,CAACgB,aAAa,EAAEH,MAAM,CAAC,CAAC;EAKjC,IAAM8C,cAAc,IAAAjE,aAAA,GAAAI,CAAA,QAAGjB,WAAW;IAAA,IAAA+E,KAAA,GAAAZ,iBAAA,CAAC,WAAOa,IAAS,EAAK;MAAAnE,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MACtD,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAACK,KAAK,CAACgB,aAAa,MAAAtB,aAAA,GAAAC,CAAA,UAAI,CAACkB,MAAM,CAACT,qBAAqB,GAAE;QAAAV,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACzD0D,OAAO,CAACM,IAAI,CAAC,gCAAgC,CAAC;QAACpE,aAAA,GAAAI,CAAA;QAC/C,OAAO,IAAI;MACb,CAAC;QAAAJ,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,IAAI;QACF,IAAMiE,MAAM,IAAArE,aAAA,GAAAI,CAAA,cAASd,sBAAsB,CAACgF,kBAAkB,CAACH,IAAI,CAAC;QAACnE,aAAA,GAAAI,CAAA;QAErE0D,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;UAChCQ,OAAO,EAAEF,MAAM,CAACE,OAAO;UACvBC,aAAa,EAAEH,MAAM,CAACG,aAAa;UACnCC,cAAc,EAAEJ,MAAM,CAACI,cAAc;UACrC5C,UAAU,EAAEwC,MAAM,CAACK,WAAW,CAAC7C;QACjC,CAAC,CAAC;QAAC7B,aAAA,GAAAI,CAAA;QAEH,OAAOiE,MAAM;MAEf,CAAC,CAAC,OAAOL,KAAK,EAAE;QAAAhE,aAAA,GAAAI,CAAA;QACd0D,OAAO,CAACE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QAAChE,aAAA,GAAAI,CAAA;QACpD,OAAO,IAAI;MACb;IACF,CAAC;IAAA,iBAAAuE,EAAA;MAAA,OAAAT,KAAA,CAAAU,KAAA,OAAA/E,SAAA;IAAA;EAAA,KAAE,CAACS,KAAK,CAACgB,aAAa,EAAEH,MAAM,CAACT,qBAAqB,CAAC,CAAC;EAKvD,IAAMmE,sBAAsB,IAAA7E,aAAA,GAAAI,CAAA,QAAGjB,WAAW;IAAA,IAAA2F,KAAA,GAAAxB,iBAAA,CAAC,WAAOyB,SAAc,EAAK;MAAA/E,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MACnE,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAACK,KAAK,CAACgB,aAAa,MAAAtB,aAAA,GAAAC,CAAA,UAAI,CAACkB,MAAM,CAACR,mBAAmB,GAAE;QAAAX,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACvD0D,OAAO,CAACM,IAAI,CAAC,8BAA8B,CAAC;QAACpE,aAAA,GAAAI,CAAA;QAC7C,OAAO,IAAI;MACb,CAAC;QAAAJ,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,IAAI;QACF,IAAMiE,MAAM,IAAArE,aAAA,GAAAI,CAAA,cAASb,mBAAmB,CAACsF,sBAAsB,CAACE,SAAS,CAAC;QAAC/E,aAAA,GAAAI,CAAA;QAE3E0D,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;UACxCQ,OAAO,EAAEF,MAAM,CAACE,OAAO;UACvBC,aAAa,EAAEH,MAAM,CAACG,aAAa;UACnCQ,YAAY,EAAEX,MAAM,CAACW,YAAY;UACjCC,MAAM,EAAEZ,MAAM,CAACY;QACjB,CAAC,CAAC;QAACjF,aAAA,GAAAI,CAAA;QAEH,OAAOiE,MAAM;MAEf,CAAC,CAAC,OAAOL,KAAK,EAAE;QAAAhE,aAAA,GAAAI,CAAA;QACd0D,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAAChE,aAAA,GAAAI,CAAA;QAC5D,OAAO,IAAI;MACb;IACF,CAAC;IAAA,iBAAA8E,GAAA;MAAA,OAAAJ,KAAA,CAAAF,KAAA,OAAA/E,SAAA;IAAA;EAAA,KAAE,CAACS,KAAK,CAACgB,aAAa,EAAEH,MAAM,CAACR,mBAAmB,CAAC,CAAC;EAKrD,IAAMwE,cAAc,IAAAnF,aAAA,GAAAI,CAAA,QAAGjB,WAAW,CAAAmE,iBAAA,CAAC,aAA+D;IAAA,IAAxD8B,UAAmB,GAAAvF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,aAAA,GAAAC,CAAA,WAAGkB,MAAM,CAACH,sBAAsB;IAAAhB,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IAC3F,IAAI,CAAAJ,aAAA,GAAAC,CAAA,YAACK,KAAK,CAACgB,aAAa,MAAAtB,aAAA,GAAAC,CAAA,WAAI,CAACkB,MAAM,CAACP,oBAAoB,GAAE;MAAAZ,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MACxD0D,OAAO,CAACM,IAAI,CAAC,0CAA0C,CAAC;MAACpE,aAAA,GAAAI,CAAA;MACzD,OAAO,IAAI;IACb,CAAC;MAAAJ,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAED,IAAI;MACF,IAAMiE,MAAM,IAAArE,aAAA,GAAAI,CAAA,cAASZ,qBAAqB,CAAC2F,cAAc,CAACC,UAAU,CAAC;MAACpF,aAAA,GAAAI,CAAA;MAEtE0D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAC5CsB,WAAW,EAAEhB,MAAM,CAACgB,WAAW;QAC/BC,cAAc,EAAEjB,MAAM,CAACiB,cAAc;QACrCC,aAAa,EAAElB,MAAM,CAACkB;MACxB,CAAC,CAAC;MAACvF,aAAA,GAAAI,CAAA;MAEH,OAAOiE,MAAM;IAEf,CAAC,CAAC,OAAOL,KAAK,EAAE;MAAAhE,aAAA,GAAAI,CAAA;MACd0D,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAAChE,aAAA,GAAAI,CAAA;MACnD,OAAO,IAAI;IACb;EACF,CAAC,GAAE,CAACE,KAAK,CAACgB,aAAa,EAAEH,MAAM,CAACP,oBAAoB,EAAEO,MAAM,CAACH,sBAAsB,CAAC,CAAC;EAKrF,IAAMwE,sBAAsB,IAAAxF,aAAA,GAAAI,CAAA,QAAGjB,WAAW,CAAC,UAACgF,IAAS,EAAK;IAAAnE,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IACxD,IAAI,CAAAJ,aAAA,GAAAC,CAAA,YAACK,KAAK,CAACgB,aAAa,MAAAtB,aAAA,GAAAC,CAAA,WAAI,CAACkB,MAAM,CAACN,0BAA0B,GAAE;MAAAb,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAC9D0D,OAAO,CAACM,IAAI,CAAC,qCAAqC,CAAC;MAACpE,aAAA,GAAAI,CAAA;MACpD,OAAO,EAAE;IACX,CAAC;MAAAJ,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAED,IAAI;MACF,IAAMqF,MAAM,IAAAzF,aAAA,GAAAI,CAAA,QAAGX,2BAA2B,CAACiG,YAAY,CAACvB,IAAI,CAAC;MAACnE,aAAA,GAAAI,CAAA;MAC9DX,2BAA2B,CAACkG,YAAY,CAACF,MAAM,CAAC;MAACzF,aAAA,GAAAI,CAAA;MAEjD0D,OAAO,CAACC,GAAG,CAAC,8BAA8B0B,MAAM,EAAE,CAAC;MAACzF,aAAA,GAAAI,CAAA;MACpD,OAAOqF,MAAM;IAEf,CAAC,CAAC,OAAOzB,KAAK,EAAE;MAAAhE,aAAA,GAAAI,CAAA;MACd0D,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAAChE,aAAA,GAAAI,CAAA;MAC5D,OAAO,EAAE;IACX;EACF,CAAC,EAAE,CAACE,KAAK,CAACgB,aAAa,EAAEH,MAAM,CAACN,0BAA0B,CAAC,CAAC;EAK5D,IAAM+E,YAAY,IAAA5F,aAAA,GAAAI,CAAA,QAAGjB,WAAW;IAAA,IAAA0G,KAAA,GAAAvC,iBAAA,CAAC,WAC/BwC,SAAsB,EACtBC,YAAoB,EACjB;MAAA/F,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MACH,IAAI,CAAAJ,aAAA,GAAAC,CAAA,YAACK,KAAK,CAACgB,aAAa,MAAAtB,aAAA,GAAAC,CAAA,WAAI,CAACkB,MAAM,CAACT,qBAAqB,GAAE;QAAAV,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACzD0D,OAAO,CAACM,IAAI,CAAC,mDAAmD,CAAC;QAACpE,aAAA,GAAAI,CAAA;QAClE,OAAO,IAAI;MACb,CAAC;QAAAJ,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,IAAI;QACF,IAAMiE,MAAM,IAAArE,aAAA,GAAAI,CAAA,cAASd,sBAAsB,CAACsG,YAAY,CAAC;UACvDE,SAAS,EAATA,SAAS;UACTC,YAAY,EAAEA,YAAmB;UACjCC,SAAS,EAAE,EAAE;UACbC,UAAU,EAAE;YAAEC,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE;UAAK,CAAC;UACzCC,YAAY,EAAE;QAChB,CAAC,CAAC;QAACpG,aAAA,GAAAI,CAAA;QAEH0D,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;UACvCQ,OAAO,EAAEF,MAAM,CAACE,OAAO;UACvBC,aAAa,EAAEH,MAAM,CAACG,aAAa;UACnCC,cAAc,EAAEJ,MAAM,CAACI;QACzB,CAAC,CAAC;QAACzE,aAAA,GAAAI,CAAA;QAEH,OAAOiE,MAAM;MAEf,CAAC,CAAC,OAAOL,KAAK,EAAE;QAAAhE,aAAA,GAAAI,CAAA;QACd0D,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAAChE,aAAA,GAAAI,CAAA;QACjD,OAAO,IAAI;MACb;IACF,CAAC;IAAA,iBAAAiG,GAAA,EAAAC,GAAA;MAAA,OAAAT,KAAA,CAAAjB,KAAA,OAAA/E,SAAA;IAAA;EAAA,KAAE,CAACS,KAAK,CAACgB,aAAa,EAAEH,MAAM,CAACT,qBAAqB,CAAC,CAAC;EAKvD,IAAM6F,cAAc,IAAAvG,aAAA,GAAAI,CAAA,QAAGjB,WAAW;IAAA,IAAAqH,KAAA,GAAAlD,iBAAA,CAAC,WACjCmD,OAAe,EACfC,SAAuB,EACpB;MAAA1G,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MACH,IAAI,CAAAJ,aAAA,GAAAC,CAAA,YAACK,KAAK,CAACgB,aAAa,MAAAtB,aAAA,GAAAC,CAAA,WAAI,CAACkB,MAAM,CAACT,qBAAqB,GAAE;QAAAV,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACzD0D,OAAO,CAACM,IAAI,CAAC,iDAAiD,CAAC;QAACpE,aAAA,GAAAI,CAAA;QAChE,OAAO,IAAI;MACb,CAAC;QAAAJ,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,IAAI;QACF,IAAMiE,MAAM,IAAArE,aAAA,GAAAI,CAAA,cAASd,sBAAsB,CAACiH,cAAc,CAAC;UACzDE,OAAO,EAAPA,OAAO;UACPE,WAAW,EAAED,SAAS;UACtBE,UAAU,EAAE,CAAC,CAAC,EAAEF,SAAS,CAAC5G,MAAM,CAAC;UACjC+G,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;UACpBC,SAAS,EAAE;QACb,CAAC,CAAC;QAAC9G,aAAA,GAAAI,CAAA;QAEH0D,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE;UACrCQ,OAAO,EAAEF,MAAM,CAACE,OAAO;UACvBC,aAAa,EAAEH,MAAM,CAACG,aAAa;UACnCuC,QAAQ,EAAE1C,MAAM,CAACK,WAAW,CAACqC;QAC/B,CAAC,CAAC;QAAC/G,aAAA,GAAAI,CAAA;QAEH,OAAOiE,MAAM;MAEf,CAAC,CAAC,OAAOL,KAAK,EAAE;QAAAhE,aAAA,GAAAI,CAAA;QACd0D,OAAO,CAACE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QAAChE,aAAA,GAAAI,CAAA;QACpD,OAAO,IAAI;MACb;IACF,CAAC;IAAA,iBAAA4G,GAAA,EAAAC,GAAA;MAAA,OAAAT,KAAA,CAAA5B,KAAA,OAAA/E,SAAA;IAAA;EAAA,KAAE,CAACS,KAAK,CAACgB,aAAa,EAAEH,MAAM,CAACT,qBAAqB,CAAC,CAAC;EAKvD,IAAMwG,gBAAgB,IAAAlH,aAAA,GAAAI,CAAA,QAAGjB,WAAW,CAAAmE,iBAAA,CAAC,aAAY;IAAAtD,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IAC/C,IAAI,CAACE,KAAK,CAACgB,aAAa,EAAE;MAAAtB,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;MAAAJ,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAEtC,IAAI;MACF,IAAM+G,OAAO,IAAAnH,aAAA,GAAAI,CAAA,QAAG;QACdgH,GAAG,EAAEjG,MAAM,CAACT,qBAAqB,IAAAV,aAAA,GAAAC,CAAA,WAAGX,sBAAsB,CAAC+H,wBAAwB,CAAC,CAAC,KAAArH,aAAA,GAAAC,CAAA,WAAG,IAAI;QAC5F6B,aAAa,EAAEX,MAAM,CAACR,mBAAmB,IAAAX,aAAA,GAAAC,CAAA,WAAGV,mBAAmB,CAAC+H,sBAAsB,CAAC,CAAC,KAAAtH,aAAA,GAAAC,CAAA,WAAG,IAAI;QAC/FsH,MAAM,EAAEpG,MAAM,CAACP,oBAAoB,IAAAZ,aAAA,GAAAC,CAAA,WAAGT,qBAAqB,CAACgI,gBAAgB,CAAC,CAAC,KAAAxH,aAAA,GAAAC,CAAA,WAAG,IAAI;QACrFyC,oBAAoB,EAAEvB,MAAM,CAACN,0BAA0B,IAAAb,aAAA,GAAAC,CAAA,WAAGR,2BAA2B,CAACgI,oBAAoB,CAAC,CAAC,KAAAzH,aAAA,GAAAC,CAAA,WAAG,IAAI;QACnHyH,QAAQ,EAAE;UACRC,EAAE,EAAEtI,QAAQ,CAACuI,EAAE;UACfC,OAAO,EAAExI,QAAQ,CAACyI,OAAO;UACzBC,SAAS,EAAE1I,QAAQ,CAAC0I;QACtB;MACF,CAAC;MAAC/H,aAAA,GAAAI,CAAA;MAEF,OAAO+G,OAAO;IAChB,CAAC,CAAC,OAAOnD,KAAK,EAAE;MAAAhE,aAAA,GAAAI,CAAA;MACd0D,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MAAChE,aAAA,GAAAI,CAAA;MACtD,OAAO,IAAI;IACb;EACF,CAAC,GAAE,CAACE,KAAK,CAACgB,aAAa,EAAEH,MAAM,CAAC,CAAC;EAKjC,IAAM6G,YAAY,IAAAhI,aAAA,GAAAI,CAAA,QAAGjB,WAAW,CAAC,UAAC8I,SAA4C,EAAK;IAAAjI,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IACjFgB,SAAS,CAAC,UAAAmC,IAAI,EAAK;MAAAvD,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MAAA,OAAAI,MAAA,CAAAC,MAAA,KAAK8C,IAAI,EAAK0E,SAAS;IAAC,CAAE,CAAC;EAChD,CAAC,EAAE,EAAE,CAAC;EAKN,IAAMpE,iBAAiB,IAAA7D,aAAA,GAAAI,CAAA,QAAGjB,WAAW,CAAAmE,iBAAA,CAAC,aAAY;IAAAtD,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IAChD,IAAI;MACF,IAAM8H,QAA0C,IAAAlI,aAAA,GAAAI,CAAA,SAAG,CAAC,CAAC;MAACJ,aAAA,GAAAI,CAAA;MAGtD,IAAIe,MAAM,CAACT,qBAAqB,EAAE;QAAAV,aAAA,GAAAC,CAAA;QAChC,IAAMkI,UAAU,IAAAnI,aAAA,GAAAI,CAAA,SAAGd,sBAAsB,CAAC+H,wBAAwB,CAAC,CAAC;QAACrH,aAAA,GAAAI,CAAA;QACrE8H,QAAQ,CAAC1G,eAAe,GAAG;UACzBC,SAAS,EAAE0G,UAAU,CAACC,YAAY,KAAK,IAAI;UAC3C1G,WAAW,EAAEyG,UAAU,CAACzG,WAAW;UACnCC,oBAAoB,EAAEwG,UAAU,CAACxG,oBAAoB;UACrDC,cAAc,EAAEuG,UAAU,CAACvG,cAAc;UACzCC,UAAU,EAAEsG,UAAU,CAACtG;QACzB,CAAC;MACH,CAAC;QAAA7B,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAGD,IAAIe,MAAM,CAACR,mBAAmB,EAAE;QAAAX,aAAA,GAAAC,CAAA;QAC9B,IAAMoI,aAAa,IAAArI,aAAA,GAAAI,CAAA,SAAGb,mBAAmB,CAAC+H,sBAAsB,CAAC,CAAC;QAACtH,aAAA,GAAAI,CAAA;QACnE8H,QAAQ,CAACpG,aAAa,GAAG;UACvBC,YAAY,EAAEsG,aAAa,CAACtG,YAAY;UACxCC,gBAAgB,EAAEqG,aAAa,CAACrG,gBAAgB;UAChDL,oBAAoB,EAAE0G,aAAa,CAAC1G,oBAAoB;UACxDM,WAAW,EAAEoG,aAAa,CAACpG,WAAW;UACtCC,gBAAgB,EAAEmG,aAAa,CAACnG;QAClC,CAAC;MACH,CAAC;QAAAlC,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAGD,IAAIe,MAAM,CAACP,oBAAoB,EAAE;QAAAZ,aAAA,GAAAC,CAAA;QAC/B,IAAMqI,aAAa,IAAAtI,aAAA,GAAAI,CAAA,SAAGZ,qBAAqB,CAACgI,gBAAgB,CAAC,CAAC;QAACxH,aAAA,GAAAI,CAAA;QAC/D8H,QAAQ,CAAC/F,gBAAgB,GAAG;UAC1BC,cAAc,EAAEkG,aAAa,CAAClG,cAAc;UAC5CC,cAAc,EAAEiG,aAAa,CAACjG,cAAc;UAC5CC,cAAc,EAAEgG,aAAa,CAAChG,cAAc,CAACiG,KAAK;UAClDhG,SAAS,EAAE+F,aAAa,CAAC/F,SAAS;UAClCC,YAAY,EAAE8F,aAAa,CAAC9F,YAAY;UACxCC,kBAAkB,EAAE6F,aAAa,CAAC7F;QACpC,CAAC;MACH,CAAC;QAAAzC,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAGD,IAAIe,MAAM,CAACN,0BAA0B,EAAE;QAAAb,aAAA,GAAAC,CAAA;QACrC,IAAMuI,SAAS,IAAAxI,aAAA,GAAAI,CAAA,SAAGX,2BAA2B,CAACgI,oBAAoB,CAAC,CAAC;QAACzH,aAAA,GAAAI,CAAA;QACrE8H,QAAQ,CAACxF,oBAAoB,GAAG;UAC9BC,eAAe,EAAE6F,SAAS,CAAC7F,eAAe;UAC1CC,eAAe,EAAE4F,SAAS,CAAC5F,eAAe;UAC1CC,kBAAkB,EAAE2F,SAAS,CAAC3F,kBAAkB;UAChDZ,WAAW,EAAEuG,SAAS,CAACvG,WAAW;UAClCa,eAAe,EAAE0F,SAAS,CAAC1F;QAC7B,CAAC;MACH,CAAC;QAAA9C,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAGD8H,QAAQ,CAACnF,oBAAoB,GAAG;QAC9BC,oBAAoB,EAAEyF,6BAA6B,CAAC,CAAC;QACrDxF,mBAAmB,EAAEyF,4BAA4B,CAAC,CAAC;QACnDxF,eAAe,EAAEyF,wBAAwB,CAAC;MAC5C,CAAC;MAAC3I,aAAA,GAAAI,CAAA;MAEFgD,QAAQ,CAAC,UAAAG,IAAI,EAAK;QAAAvD,aAAA,GAAAE,CAAA;QAAAF,aAAA,GAAAI,CAAA;QAAA,OAAAI,MAAA,CAAAC,MAAA,KAAK8C,IAAI,EAAK2E,QAAQ;MAAC,CAAE,CAAC;IAE9C,CAAC,CAAC,OAAOlE,KAAK,EAAE;MAAAhE,aAAA,GAAAI,CAAA;MACd0D,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC,GAAE,CAAC7C,MAAM,CAAC,CAAC;EAACnB,aAAA,GAAAI,CAAA;EAGb,IAAMqI,6BAA6B,GAAG,SAAhCA,6BAA6BA,CAAA,EAAiB;IAAAzI,aAAA,GAAAE,CAAA;IAClD,IAAI0I,KAAK,IAAA5I,aAAA,GAAAI,CAAA,SAAG,CAAC;IAACJ,aAAA,GAAAI,CAAA;IAEd,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAAAkB,MAAM,CAACT,qBAAqB,MAAAV,aAAA,GAAAC,CAAA,WAAIK,KAAK,CAACkB,eAAe,CAACC,SAAS,GAAE;MAAAzB,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MACnEwI,KAAK,IAAItI,KAAK,CAACkB,eAAe,CAACK,UAAU,GAAG,EAAE;IAChD,CAAC;MAAA7B,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAED,IAAIe,MAAM,CAACR,mBAAmB,EAAE;MAAAX,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAC9BwI,KAAK,IAAItI,KAAK,CAACwB,aAAa,CAACI,gBAAgB,GAAG,EAAE;IACpD,CAAC;MAAAlC,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAED,IAAIe,MAAM,CAACP,oBAAoB,EAAE;MAAAZ,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAC/BwI,KAAK,IAAI,CAAC,GAAG,GAAGtI,KAAK,CAAC6B,gBAAgB,CAACM,kBAAkB,IAAI,EAAE;IACjE,CAAC;MAAAzC,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAED,OAAOyI,IAAI,CAACC,GAAG,CAACF,KAAK,EAAE,GAAG,CAAC;EAC7B,CAAC;EAAC5I,aAAA,GAAAI,CAAA;EAEF,IAAMsI,4BAA4B,GAAG,SAA/BA,4BAA4BA,CAAA,EAAiB;IAAA1I,aAAA,GAAAE,CAAA;IACjD,IAAI0I,KAAK,IAAA5I,aAAA,GAAAI,CAAA,SAAG,CAAC;IAACJ,aAAA,GAAAI,CAAA;IAGd,IAAIe,MAAM,CAACR,mBAAmB,EAAE;MAAAX,aAAA,GAAAC,CAAA;MAC9B,IAAM8I,WAAW,IAAA/I,aAAA,GAAAI,CAAA,SAAGE,KAAK,CAACwB,aAAa,CAACC,YAAY,GAAG,CAAC,IAAA/B,aAAA,GAAAC,CAAA,WACpDK,KAAK,CAACwB,aAAa,CAACE,gBAAgB,GAAG1B,KAAK,CAACwB,aAAa,CAACC,YAAY,KAAA/B,aAAA,GAAAC,CAAA,WACvE,CAAC;MAACD,aAAA,GAAAI,CAAA;MACNwI,KAAK,IAAIG,WAAW,GAAG,EAAE;IAC3B,CAAC;MAAA/I,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAGD,IAAIe,MAAM,CAACN,0BAA0B,EAAE;MAAAb,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MACrCwI,KAAK,IAAKtI,KAAK,CAACoC,oBAAoB,CAACT,WAAW,GAAG,GAAG,GAAI,EAAE;IAC9D,CAAC;MAAAjC,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAGD,IAAIe,MAAM,CAACP,oBAAoB,EAAE;MAAAZ,aAAA,GAAAC,CAAA;MAC/B,IAAM+I,WAAW,IAAAhJ,aAAA,GAAAI,CAAA,SAAGE,KAAK,CAAC6B,gBAAgB,CAACG,cAAc,KAAK,QAAQ,IAAAtC,aAAA,GAAAC,CAAA,WAAG,GAAG,KAAAD,aAAA,GAAAC,CAAA,WACzDK,KAAK,CAAC6B,gBAAgB,CAACG,cAAc,KAAK,SAAS,IAAAtC,aAAA,GAAAC,CAAA,WAAG,EAAE,KAAAD,aAAA,GAAAC,CAAA,WAAG,EAAE;MAACD,aAAA,GAAAI,CAAA;MACjFwI,KAAK,IAAKI,WAAW,GAAG,GAAG,GAAI,EAAE;IACnC,CAAC;MAAAhJ,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAED,OAAOyI,IAAI,CAACC,GAAG,CAACF,KAAK,EAAE,GAAG,CAAC;EAC7B,CAAC;EAAC5I,aAAA,GAAAI,CAAA;EAEF,IAAMuI,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA,EAAiB;IAAA3I,aAAA,GAAAE,CAAA;IAE7C,IAAI+I,IAAI,IAAAjJ,aAAA,GAAAI,CAAA,SAAG,CAAC;IAACJ,aAAA,GAAAI,CAAA;IAGb,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAAAkB,MAAM,CAACT,qBAAqB,MAAAV,aAAA,GAAAC,CAAA,WAAIK,KAAK,CAACkB,eAAe,CAACC,SAAS,GAAE;MAAAzB,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MACnE6I,IAAI,IAAI3I,KAAK,CAACkB,eAAe,CAACK,UAAU,GAAG,EAAE;IAC/C,CAAC;MAAA7B,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAGD,IAAIe,MAAM,CAACR,mBAAmB,EAAE;MAAAX,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAC9B6I,IAAI,IAAK3I,KAAK,CAACwB,aAAa,CAACG,WAAW,GAAG,GAAG,GAAI,EAAE;IACtD,CAAC;MAAAjC,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAGD,IAAIe,MAAM,CAACP,oBAAoB,EAAE;MAAAZ,aAAA,GAAAC,CAAA;MAC/B,IAAMiJ,UAAU,IAAAlJ,aAAA,GAAAI,CAAA,SAAGE,KAAK,CAAC6B,gBAAgB,CAACG,cAAc,KAAK,QAAQ,IAAAtC,aAAA,GAAAC,CAAA,WAAG,EAAE,KAAAD,aAAA,GAAAC,CAAA,WAAG,EAAE;MAACD,aAAA,GAAAI,CAAA;MAChF6I,IAAI,IAAIC,UAAU;IACpB,CAAC;MAAAlJ,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAED,OAAOyI,IAAI,CAACC,GAAG,CAACG,IAAI,EAAE,GAAG,CAAC;EAC5B,CAAC;EAACjJ,aAAA,GAAAI,CAAA;EAGFlB,SAAS,CAAC,YAAM;IAAAc,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IACd,IAAI,CAAAJ,aAAA,GAAAC,CAAA,YAACkB,MAAM,CAACL,gBAAgB,MAAAd,aAAA,GAAAC,CAAA,WAAI,CAACK,KAAK,CAACgB,aAAa,GAAE;MAAAtB,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,aAAA,GAAAC,CAAA;IAAA;IAE7D,IAAMkJ,QAAQ,IAAAnJ,aAAA,GAAAI,CAAA,SAAGgJ,WAAW,CAAA9F,iBAAA,CAAC,aAAY;MAAAtD,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MAEvC,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAAAkB,MAAM,CAACP,oBAAoB,MAAAZ,aAAA,GAAAC,CAAA,WAAIK,KAAK,CAAC6B,gBAAgB,CAACG,cAAc,KAAK,QAAQ,GAAE;QAAAtC,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACrF,MAAM+E,cAAc,CAAC,KAAK,CAAC;MAC7B,CAAC;QAAAnF,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,IAAIe,MAAM,CAACT,qBAAqB,EAAE;QAAAV,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QAChC,MAAMd,sBAAsB,CAAC+J,sBAAsB,CAAC,CAAC;MACvD,CAAC;QAAArJ,aAAA,GAAAC,CAAA;MAAA;IACH,CAAC,GAAEkB,MAAM,CAACJ,oBAAoB,CAAC;IAACf,aAAA,GAAAI,CAAA;IAEhC,OAAO,YAAM;MAAAJ,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MAAA,OAAAkJ,aAAa,CAACH,QAAQ,CAAC;IAAD,CAAC;EACtC,CAAC,EAAE,CAAChI,MAAM,CAACL,gBAAgB,EAAEK,MAAM,CAACJ,oBAAoB,EAAET,KAAK,CAACgB,aAAa,EAAE6D,cAAc,CAAC,CAAC;EAACnF,aAAA,GAAAI,CAAA;EAGhGlB,SAAS,CAAC,YAAM;IAAAc,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IACd,IAAI,CAACE,KAAK,CAACgB,aAAa,EAAE;MAAAtB,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,aAAA,GAAAC,CAAA;IAAA;IAEjC,IAAMkJ,QAAQ,IAAAnJ,aAAA,GAAAI,CAAA,SAAGgJ,WAAW,CAAC,YAAM;MAAApJ,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MACjCyD,iBAAiB,CAAC,CAAC;IACrB,CAAC,EAAE,KAAK,CAAC;IAAC7D,aAAA,GAAAI,CAAA;IAEV,OAAO,YAAM;MAAAJ,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MAAA,OAAAkJ,aAAa,CAACH,QAAQ,CAAC;IAAD,CAAC;EACtC,CAAC,EAAE,CAAC7I,KAAK,CAACgB,aAAa,EAAEuC,iBAAiB,CAAC,CAAC;EAAC7D,aAAA,GAAAI,CAAA;EAG7ClB,SAAS,CAAC,YAAM;IAAAc,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IACd,IAAI,CAACE,KAAK,CAACgB,aAAa,EAAE;MAAAtB,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MACxBiD,UAAU,CAAC,CAAC;IACd,CAAC;MAAArD,aAAA,GAAAC,CAAA;IAAA;EACH,CAAC,EAAE,CAACK,KAAK,CAACgB,aAAa,EAAE+B,UAAU,CAAC,CAAC;EAACrD,aAAA,GAAAI,CAAA;EAGtC,OAAOhB,OAAO,CAAC,YAAO;IAAAY,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IAAA;MACpBE,KAAK,EAALA,KAAK;MACLiJ,OAAO,EAAE;QACPlG,UAAU,EAAVA,UAAU;QACVY,cAAc,EAAdA,cAAc;QACdY,sBAAsB,EAAtBA,sBAAsB;QACtBM,cAAc,EAAdA,cAAc;QACdK,sBAAsB,EAAtBA,sBAAsB;QACtBI,YAAY,EAAZA,YAAY;QACZW,cAAc,EAAdA,cAAc;QACdW,gBAAgB,EAAhBA,gBAAgB;QAChBc,YAAY,EAAZA;MACF,CAAC;MACD7G,MAAM,EAANA;IACF,CAAC;EAAD,CAAE,EAAE,CACFb,KAAK,EACL+C,UAAU,EACVY,cAAc,EACdY,sBAAsB,EACtBM,cAAc,EACdK,sBAAsB,EACtBI,YAAY,EACZW,cAAc,EACdW,gBAAgB,EAChBc,YAAY,EACZ7G,MAAM,CACP,CAAC;AACJ;AAEA,eAAexB,qBAAqB", "ignoreList": []}