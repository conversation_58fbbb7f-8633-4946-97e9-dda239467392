1da4dfecf28f4d1fb8747be478b3dd51
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_2nmm6qpohh() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\matches\\[id].tsx";
  var hash = "f6c38b19aedecd23737d1a2cbfde609e89a4dcfe";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\matches\\[id].tsx",
    statementMap: {
      "0": {
        start: {
          line: 35,
          column: 15
        },
        end: {
          line: 45,
          column: 1
        }
      },
      "1": {
        start: {
          line: 48,
          column: 18
        },
        end: {
          line: 121,
          column: 1
        }
      },
      "2": {
        start: {
          line: 124,
          column: 17
        },
        end: {
          line: 124,
          column: 39
        }
      },
      "3": {
        start: {
          line: 125,
          column: 40
        },
        end: {
          line: 125,
          column: 60
        }
      },
      "4": {
        start: {
          line: 127,
          column: 16
        },
        end: {
          line: 127,
          column: 55
        }
      },
      "5": {
        start: {
          line: 129,
          column: 2
        },
        end: {
          line: 135,
          column: 3
        }
      },
      "6": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 134,
          column: 6
        }
      },
      "7": {
        start: {
          line: 137,
          column: 25
        },
        end: {
          line: 139,
          column: 3
        }
      },
      "8": {
        start: {
          line: 138,
          column: 4
        },
        end: {
          line: 138,
          column: 56
        }
      },
      "9": {
        start: {
          line: 141,
          column: 22
        },
        end: {
          line: 143,
          column: 3
        }
      },
      "10": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 142,
          column: 57
        }
      },
      "11": {
        start: {
          line: 145,
          column: 23
        },
        end: {
          line: 147,
          column: 3
        }
      },
      "12": {
        start: {
          line: 146,
          column: 4
        },
        end: {
          line: 146,
          column: 63
        }
      },
      "13": {
        start: {
          line: 149,
          column: 15
        },
        end: {
          line: 153,
          column: 3
        }
      },
      "14": {
        start: {
          line: 155,
          column: 25
        },
        end: {
          line: 253,
          column: 3
        }
      },
      "15": {
        start: {
          line: 156,
          column: 4
        },
        end: {
          line: 252,
          column: 11
        }
      },
      "16": {
        start: {
          line: 174,
          column: 12
        },
        end: {
          line: 191,
          column: 19
        }
      },
      "17": {
        start: {
          line: 255,
          column: 30
        },
        end: {
          line: 347,
          column: 3
        }
      },
      "18": {
        start: {
          line: 256,
          column: 4
        },
        end: {
          line: 346,
          column: 11
        }
      },
      "19": {
        start: {
          line: 323,
          column: 10
        },
        end: {
          line: 343,
          column: 17
        }
      },
      "20": {
        start: {
          line: 349,
          column: 25
        },
        end: {
          line: 426,
          column: 3
        }
      },
      "21": {
        start: {
          line: 350,
          column: 4
        },
        end: {
          line: 425,
          column: 11
        }
      },
      "22": {
        start: {
          line: 359,
          column: 12
        },
        end: {
          line: 362,
          column: 19
        }
      },
      "23": {
        start: {
          line: 375,
          column: 12
        },
        end: {
          line: 378,
          column: 19
        }
      },
      "24": {
        start: {
          line: 428,
          column: 2
        },
        end: {
          line: 521,
          column: 4
        }
      },
      "25": {
        start: {
          line: 436,
          column: 43
        },
        end: {
          line: 436,
          column: 56
        }
      },
      "26": {
        start: {
          line: 489,
          column: 16
        },
        end: {
          line: 507,
          column: 35
        }
      },
      "27": {
        start: {
          line: 495,
          column: 33
        },
        end: {
          line: 495,
          column: 55
        }
      },
      "28": {
        start: {
          line: 524,
          column: 15
        },
        end: {
          line: 886,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "MatchDetailScreen",
        decl: {
          start: {
            line: 123,
            column: 24
          },
          end: {
            line: 123,
            column: 41
          }
        },
        loc: {
          start: {
            line: 123,
            column: 44
          },
          end: {
            line: 522,
            column: 1
          }
        },
        line: 123
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 137,
            column: 25
          },
          end: {
            line: 137,
            column: 26
          }
        },
        loc: {
          start: {
            line: 137,
            column: 45
          },
          end: {
            line: 139,
            column: 3
          }
        },
        line: 137
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 141,
            column: 22
          },
          end: {
            line: 141,
            column: 23
          }
        },
        loc: {
          start: {
            line: 141,
            column: 28
          },
          end: {
            line: 143,
            column: 3
          }
        },
        line: 141
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 145,
            column: 23
          },
          end: {
            line: 145,
            column: 24
          }
        },
        loc: {
          start: {
            line: 145,
            column: 29
          },
          end: {
            line: 147,
            column: 3
          }
        },
        line: 145
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 155,
            column: 25
          },
          end: {
            line: 155,
            column: 26
          }
        },
        loc: {
          start: {
            line: 156,
            column: 4
          },
          end: {
            line: 252,
            column: 11
          }
        },
        line: 156
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 173,
            column: 26
          },
          end: {
            line: 173,
            column: 27
          }
        },
        loc: {
          start: {
            line: 174,
            column: 12
          },
          end: {
            line: 191,
            column: 19
          }
        },
        line: 174
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 255,
            column: 30
          },
          end: {
            line: 255,
            column: 31
          }
        },
        loc: {
          start: {
            line: 256,
            column: 4
          },
          end: {
            line: 346,
            column: 11
          }
        },
        line: 256
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 322,
            column: 33
          },
          end: {
            line: 322,
            column: 34
          }
        },
        loc: {
          start: {
            line: 323,
            column: 10
          },
          end: {
            line: 343,
            column: 17
          }
        },
        line: 323
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 349,
            column: 25
          },
          end: {
            line: 349,
            column: 26
          }
        },
        loc: {
          start: {
            line: 350,
            column: 4
          },
          end: {
            line: 425,
            column: 11
          }
        },
        line: 350
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 358,
            column: 32
          },
          end: {
            line: 358,
            column: 33
          }
        },
        loc: {
          start: {
            line: 359,
            column: 12
          },
          end: {
            line: 362,
            column: 19
          }
        },
        line: 359
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 374,
            column: 34
          },
          end: {
            line: 374,
            column: 35
          }
        },
        loc: {
          start: {
            line: 375,
            column: 12
          },
          end: {
            line: 378,
            column: 19
          }
        },
        line: 375
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 436,
            column: 37
          },
          end: {
            line: 436,
            column: 38
          }
        },
        loc: {
          start: {
            line: 436,
            column: 43
          },
          end: {
            line: 436,
            column: 56
          }
        },
        line: 436
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 488,
            column: 24
          },
          end: {
            line: 488,
            column: 25
          }
        },
        loc: {
          start: {
            line: 489,
            column: 16
          },
          end: {
            line: 507,
            column: 35
          }
        },
        line: 489
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 495,
            column: 27
          },
          end: {
            line: 495,
            column: 28
          }
        },
        loc: {
          start: {
            line: 495,
            column: 33
          },
          end: {
            line: 495,
            column: 55
          }
        },
        line: 495
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 129,
            column: 2
          },
          end: {
            line: 135,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 129,
            column: 2
          },
          end: {
            line: 135,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 129
      },
      "1": {
        loc: {
          start: {
            line: 138,
            column: 11
          },
          end: {
            line: 138,
            column: 55
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 138,
            column: 30
          },
          end: {
            line: 138,
            column: 42
          }
        }, {
          start: {
            line: 138,
            column: 45
          },
          end: {
            line: 138,
            column: 55
          }
        }],
        line: 138
      },
      "2": {
        loc: {
          start: {
            line: 179,
            column: 18
          },
          end: {
            line: 179,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 179,
            column: 18
          },
          end: {
            line: 179,
            column: 43
          }
        }, {
          start: {
            line: 179,
            column: 47
          },
          end: {
            line: 179,
            column: 66
          }
        }],
        line: 179
      },
      "3": {
        loc: {
          start: {
            line: 186,
            column: 18
          },
          end: {
            line: 186,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 186,
            column: 18
          },
          end: {
            line: 186,
            column: 43
          }
        }, {
          start: {
            line: 186,
            column: 47
          },
          end: {
            line: 186,
            column: 66
          }
        }],
        line: 186
      },
      "4": {
        loc: {
          start: {
            line: 475,
            column: 13
          },
          end: {
            line: 480,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 475,
            column: 13
          },
          end: {
            line: 475,
            column: 29
          }
        }, {
          start: {
            line: 476,
            column: 14
          },
          end: {
            line: 479,
            column: 21
          }
        }],
        line: 475
      },
      "5": {
        loc: {
          start: {
            line: 493,
            column: 20
          },
          end: {
            line: 493,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 493,
            column: 20
          },
          end: {
            line: 493,
            column: 42
          }
        }, {
          start: {
            line: 493,
            column: 46
          },
          end: {
            line: 493,
            column: 62
          }
        }],
        line: 493
      },
      "6": {
        loc: {
          start: {
            line: 499,
            column: 27
          },
          end: {
            line: 499,
            column: 76
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 499,
            column: 52
          },
          end: {
            line: 499,
            column: 66
          }
        }, {
          start: {
            line: 499,
            column: 69
          },
          end: {
            line: 499,
            column: 76
          }
        }],
        line: 499
      },
      "7": {
        loc: {
          start: {
            line: 503,
            column: 20
          },
          end: {
            line: 503,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 503,
            column: 20
          },
          end: {
            line: 503,
            column: 42
          }
        }, {
          start: {
            line: 503,
            column: 46
          },
          end: {
            line: 503,
            column: 66
          }
        }],
        line: 503
      },
      "8": {
        loc: {
          start: {
            line: 515,
            column: 11
          },
          end: {
            line: 515,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 515,
            column: 11
          },
          end: {
            line: 515,
            column: 37
          }
        }, {
          start: {
            line: 515,
            column: 41
          },
          end: {
            line: 515,
            column: 57
          }
        }],
        line: 515
      },
      "9": {
        loc: {
          start: {
            line: 516,
            column: 11
          },
          end: {
            line: 516,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 516,
            column: 11
          },
          end: {
            line: 516,
            column: 34
          }
        }, {
          start: {
            line: 516,
            column: 38
          },
          end: {
            line: 516,
            column: 59
          }
        }],
        line: 516
      },
      "10": {
        loc: {
          start: {
            line: 517,
            column: 11
          },
          end: {
            line: 517,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 517,
            column: 11
          },
          end: {
            line: 517,
            column: 37
          }
        }, {
          start: {
            line: 517,
            column: 41
          },
          end: {
            line: 517,
            column: 57
          }
        }],
        line: 517
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f6c38b19aedecd23737d1a2cbfde609e89a4dcfe"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2nmm6qpohh = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2nmm6qpohh();
import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, SafeAreaView, TouchableOpacity, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useLocalSearchParams } from 'expo-router';
import Card from "../../components/ui/Card";
import ProgressRing from "../../components/ui/ProgressRing";
import { ArrowLeft, Calendar, Clock, MapPin, Target, Trophy, TrendingUp, TrendingDown, Share, Download, BarChart3, PieChart, Activity, Zap, Shield } from 'lucide-react-native';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_2nmm6qpohh().s[0]++, {
  primary: '#23ba16',
  yellow: '#ffe600',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  red: '#ef4444',
  green: '#10b981',
  blue: '#3b82f6'
});
var matchData = (cov_2nmm6qpohh().s[1]++, {
  '1': {
    id: '1',
    date: '2024-01-15',
    opponent: 'Sarah Chen',
    opponentRating: 4.2,
    result: 'win',
    score: '6-4, 3-6, 6-2',
    sets: [{
      player: 6,
      opponent: 4
    }, {
      player: 3,
      opponent: 6
    }, {
      player: 6,
      opponent: 2
    }],
    duration: '2h 15m',
    surface: 'hard',
    location: 'Central Tennis Club',
    tournament: 'Club Championship',
    stats: {
      aces: 8,
      doubleFaults: 3,
      firstServePercentage: 68,
      secondServePercentage: 45,
      winnersCount: 24,
      unforcedErrors: 18,
      breakPointsConverted: '4/7',
      breakPointsSaved: '3/5',
      totalPoints: 89,
      serviceGames: 12,
      returnGames: 11
    },
    detailedStats: {
      forehandWinners: 12,
      backhandWinners: 8,
      volleyWinners: 4,
      forehandErrors: 8,
      backhandErrors: 7,
      volleyErrors: 3,
      netApproaches: 15,
      netSuccess: 11,
      distanceCovered: '2.8 km',
      averageRallyLength: 4.2,
      longestRally: 18
    },
    setBySetStats: [{
      set: 1,
      aces: 3,
      winners: 9,
      errors: 5,
      firstServe: 72
    }, {
      set: 2,
      aces: 2,
      winners: 6,
      errors: 8,
      firstServe: 58
    }, {
      set: 3,
      aces: 3,
      winners: 9,
      errors: 5,
      firstServe: 75
    }],
    highlights: ['Strong serve performance with 8 aces', 'Excellent third set comeback', 'Good net play in crucial points', 'Effective break point conversion'],
    improvements: ['Reduce unforced errors on backhand', 'Improve second serve placement', 'Work on consistency in second set', 'Better movement on defensive shots'],
    heatmap: {
      serves: [{
        zone: 'wide',
        percentage: 35
      }, {
        zone: 'body',
        percentage: 40
      }, {
        zone: 'T',
        percentage: 25
      }],
      shots: [{
        zone: 'crosscourt',
        percentage: 45
      }, {
        zone: 'down-the-line',
        percentage: 30
      }, {
        zone: 'short-angle',
        percentage: 25
      }]
    }
  }
});
export default function MatchDetailScreen() {
  cov_2nmm6qpohh().f[0]++;
  var _ref = (cov_2nmm6qpohh().s[2]++, useLocalSearchParams()),
    id = _ref.id;
  var _ref2 = (cov_2nmm6qpohh().s[3]++, useState('overview')),
    _ref3 = _slicedToArray(_ref2, 2),
    selectedTab = _ref3[0],
    setSelectedTab = _ref3[1];
  var match = (cov_2nmm6qpohh().s[4]++, matchData[id]);
  cov_2nmm6qpohh().s[5]++;
  if (!match) {
    cov_2nmm6qpohh().b[0][0]++;
    cov_2nmm6qpohh().s[6]++;
    return _jsx(SafeAreaView, {
      style: styles.container,
      children: _jsx(Text, {
        children: "Match not found"
      })
    });
  } else {
    cov_2nmm6qpohh().b[0][1]++;
  }
  cov_2nmm6qpohh().s[7]++;
  var getResultColor = function getResultColor(result) {
    cov_2nmm6qpohh().f[1]++;
    cov_2nmm6qpohh().s[8]++;
    return result === 'win' ? (cov_2nmm6qpohh().b[1][0]++, colors.green) : (cov_2nmm6qpohh().b[1][1]++, colors.red);
  };
  cov_2nmm6qpohh().s[9]++;
  var handleShare = function handleShare() {
    cov_2nmm6qpohh().f[2]++;
    cov_2nmm6qpohh().s[10]++;
    Alert.alert('Share Match', 'Match analysis shared!');
  };
  cov_2nmm6qpohh().s[11]++;
  var handleExport = function handleExport() {
    cov_2nmm6qpohh().f[3]++;
    cov_2nmm6qpohh().s[12]++;
    Alert.alert('Export Match', 'Match data exported to PDF!');
  };
  var tabs = (cov_2nmm6qpohh().s[13]++, [{
    id: 'overview',
    label: 'Overview',
    icon: BarChart3
  }, {
    id: 'stats',
    label: 'Detailed Stats',
    icon: PieChart
  }, {
    id: 'analysis',
    label: 'Analysis',
    icon: Activity
  }]);
  cov_2nmm6qpohh().s[14]++;
  var renderOverview = function renderOverview() {
    cov_2nmm6qpohh().f[4]++;
    cov_2nmm6qpohh().s[15]++;
    return _jsxs(View, {
      style: styles.tabContent,
      children: [_jsxs(Card, {
        style: styles.resultCard,
        children: [_jsxs(View, {
          style: styles.resultHeader,
          children: [_jsx(Text, {
            style: styles.resultTitle,
            children: "Match Result"
          }), _jsx(View, {
            style: [styles.resultBadge, {
              backgroundColor: getResultColor(match.result)
            }],
            children: _jsx(Text, {
              style: styles.resultText,
              children: match.result.toUpperCase()
            })
          })]
        }), _jsx(Text, {
          style: styles.scoreText,
          children: match.score
        }), _jsxs(Text, {
          style: styles.opponentText,
          children: ["vs ", match.opponent]
        })]
      }), _jsxs(Card, {
        style: styles.sectionCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "Set Breakdown"
        }), _jsx(View, {
          style: styles.setsContainer,
          children: match.sets.map(function (set, index) {
            cov_2nmm6qpohh().f[5]++;
            cov_2nmm6qpohh().s[16]++;
            return _jsxs(View, {
              style: styles.setItem,
              children: [_jsxs(Text, {
                style: styles.setLabel,
                children: ["Set ", index + 1]
              }), _jsxs(View, {
                style: styles.setScore,
                children: [_jsx(Text, {
                  style: [styles.setPlayerScore, (cov_2nmm6qpohh().b[2][0]++, set.player > set.opponent) && (cov_2nmm6qpohh().b[2][1]++, styles.winningScore)],
                  children: set.player
                }), _jsx(Text, {
                  style: styles.setDivider,
                  children: "-"
                }), _jsx(Text, {
                  style: [styles.setOpponentScore, (cov_2nmm6qpohh().b[3][0]++, set.opponent > set.player) && (cov_2nmm6qpohh().b[3][1]++, styles.winningScore)],
                  children: set.opponent
                })]
              })]
            }, index);
          })
        })]
      }), _jsxs(Card, {
        style: styles.sectionCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "Key Statistics"
        }), _jsxs(View, {
          style: styles.statsGrid,
          children: [_jsxs(View, {
            style: styles.statBox,
            children: [_jsx(Text, {
              style: styles.statValue,
              children: match.stats.aces
            }), _jsx(Text, {
              style: styles.statLabel,
              children: "Aces"
            })]
          }), _jsxs(View, {
            style: styles.statBox,
            children: [_jsx(Text, {
              style: styles.statValue,
              children: match.stats.winnersCount
            }), _jsx(Text, {
              style: styles.statLabel,
              children: "Winners"
            })]
          }), _jsxs(View, {
            style: styles.statBox,
            children: [_jsx(Text, {
              style: styles.statValue,
              children: match.stats.unforcedErrors
            }), _jsx(Text, {
              style: styles.statLabel,
              children: "Unforced Errors"
            })]
          }), _jsxs(View, {
            style: styles.statBox,
            children: [_jsxs(Text, {
              style: styles.statValue,
              children: [match.stats.firstServePercentage, "%"]
            }), _jsx(Text, {
              style: styles.statLabel,
              children: "1st Serve"
            })]
          })]
        })]
      }), _jsxs(Card, {
        style: styles.sectionCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "Performance Metrics"
        }), _jsxs(View, {
          style: styles.ringsContainer,
          children: [_jsxs(View, {
            style: styles.ringItem,
            children: [_jsx(ProgressRing, {
              progress: match.stats.firstServePercentage,
              size: 80,
              strokeWidth: 8,
              color: colors.primary
            }), _jsx(Text, {
              style: styles.ringLabel,
              children: "First Serve"
            })]
          }), _jsxs(View, {
            style: styles.ringItem,
            children: [_jsx(ProgressRing, {
              progress: Math.round(match.stats.winnersCount / (match.stats.winnersCount + match.stats.unforcedErrors) * 100),
              size: 80,
              strokeWidth: 8,
              color: colors.blue
            }), _jsx(Text, {
              style: styles.ringLabel,
              children: "Winner/Error Ratio"
            })]
          }), _jsxs(View, {
            style: styles.ringItem,
            children: [_jsx(ProgressRing, {
              progress: Math.round(parseInt(match.stats.breakPointsConverted.split('/')[0]) / parseInt(match.stats.breakPointsConverted.split('/')[1]) * 100),
              size: 80,
              strokeWidth: 8,
              color: colors.yellow
            }), _jsx(Text, {
              style: styles.ringLabel,
              children: "Break Points"
            })]
          })]
        })]
      })]
    });
  };
  cov_2nmm6qpohh().s[17]++;
  var renderDetailedStats = function renderDetailedStats() {
    cov_2nmm6qpohh().f[6]++;
    cov_2nmm6qpohh().s[18]++;
    return _jsxs(View, {
      style: styles.tabContent,
      children: [_jsxs(Card, {
        style: styles.sectionCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "Serving Statistics"
        }), _jsxs(View, {
          style: styles.statRow,
          children: [_jsx(Text, {
            style: styles.statRowLabel,
            children: "Aces"
          }), _jsx(Text, {
            style: styles.statRowValue,
            children: match.stats.aces
          })]
        }), _jsxs(View, {
          style: styles.statRow,
          children: [_jsx(Text, {
            style: styles.statRowLabel,
            children: "Double Faults"
          }), _jsx(Text, {
            style: styles.statRowValue,
            children: match.stats.doubleFaults
          })]
        }), _jsxs(View, {
          style: styles.statRow,
          children: [_jsx(Text, {
            style: styles.statRowLabel,
            children: "First Serve %"
          }), _jsxs(Text, {
            style: styles.statRowValue,
            children: [match.stats.firstServePercentage, "%"]
          })]
        }), _jsxs(View, {
          style: styles.statRow,
          children: [_jsx(Text, {
            style: styles.statRowLabel,
            children: "Second Serve %"
          }), _jsxs(Text, {
            style: styles.statRowValue,
            children: [match.stats.secondServePercentage, "%"]
          })]
        }), _jsxs(View, {
          style: styles.statRow,
          children: [_jsx(Text, {
            style: styles.statRowLabel,
            children: "Break Points Converted"
          }), _jsx(Text, {
            style: styles.statRowValue,
            children: match.stats.breakPointsConverted
          })]
        })]
      }), _jsxs(Card, {
        style: styles.sectionCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "Shot Analysis"
        }), _jsxs(View, {
          style: styles.shotGrid,
          children: [_jsxs(View, {
            style: styles.shotCategory,
            children: [_jsx(Text, {
              style: styles.shotCategoryTitle,
              children: "Winners"
            }), _jsxs(View, {
              style: styles.shotItem,
              children: [_jsx(Text, {
                style: styles.shotLabel,
                children: "Forehand"
              }), _jsx(Text, {
                style: styles.shotValue,
                children: match.detailedStats.forehandWinners
              })]
            }), _jsxs(View, {
              style: styles.shotItem,
              children: [_jsx(Text, {
                style: styles.shotLabel,
                children: "Backhand"
              }), _jsx(Text, {
                style: styles.shotValue,
                children: match.detailedStats.backhandWinners
              })]
            }), _jsxs(View, {
              style: styles.shotItem,
              children: [_jsx(Text, {
                style: styles.shotLabel,
                children: "Volley"
              }), _jsx(Text, {
                style: styles.shotValue,
                children: match.detailedStats.volleyWinners
              })]
            })]
          }), _jsxs(View, {
            style: styles.shotCategory,
            children: [_jsx(Text, {
              style: styles.shotCategoryTitle,
              children: "Errors"
            }), _jsxs(View, {
              style: styles.shotItem,
              children: [_jsx(Text, {
                style: styles.shotLabel,
                children: "Forehand"
              }), _jsx(Text, {
                style: styles.shotValue,
                children: match.detailedStats.forehandErrors
              })]
            }), _jsxs(View, {
              style: styles.shotItem,
              children: [_jsx(Text, {
                style: styles.shotLabel,
                children: "Backhand"
              }), _jsx(Text, {
                style: styles.shotValue,
                children: match.detailedStats.backhandErrors
              })]
            }), _jsxs(View, {
              style: styles.shotItem,
              children: [_jsx(Text, {
                style: styles.shotLabel,
                children: "Volley"
              }), _jsx(Text, {
                style: styles.shotValue,
                children: match.detailedStats.volleyErrors
              })]
            })]
          })]
        })]
      }), _jsxs(Card, {
        style: styles.sectionCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "Set by Set Performance"
        }), match.setBySetStats.map(function (setStats, index) {
          cov_2nmm6qpohh().f[7]++;
          cov_2nmm6qpohh().s[19]++;
          return _jsxs(View, {
            style: styles.setStatsContainer,
            children: [_jsxs(Text, {
              style: styles.setStatsTitle,
              children: ["Set ", setStats.set]
            }), _jsxs(View, {
              style: styles.setStatsGrid,
              children: [_jsxs(View, {
                style: styles.setStatItem,
                children: [_jsx(Text, {
                  style: styles.setStatValue,
                  children: setStats.aces
                }), _jsx(Text, {
                  style: styles.setStatLabel,
                  children: "Aces"
                })]
              }), _jsxs(View, {
                style: styles.setStatItem,
                children: [_jsx(Text, {
                  style: styles.setStatValue,
                  children: setStats.winners
                }), _jsx(Text, {
                  style: styles.setStatLabel,
                  children: "Winners"
                })]
              }), _jsxs(View, {
                style: styles.setStatItem,
                children: [_jsx(Text, {
                  style: styles.setStatValue,
                  children: setStats.errors
                }), _jsx(Text, {
                  style: styles.setStatLabel,
                  children: "Errors"
                })]
              }), _jsxs(View, {
                style: styles.setStatItem,
                children: [_jsxs(Text, {
                  style: styles.setStatValue,
                  children: [setStats.firstServe, "%"]
                }), _jsx(Text, {
                  style: styles.setStatLabel,
                  children: "1st Serve"
                })]
              })]
            })]
          }, index);
        })]
      })]
    });
  };
  cov_2nmm6qpohh().s[20]++;
  var renderAnalysis = function renderAnalysis() {
    cov_2nmm6qpohh().f[8]++;
    cov_2nmm6qpohh().s[21]++;
    return _jsxs(View, {
      style: styles.tabContent,
      children: [_jsxs(Card, {
        style: styles.sectionCard,
        children: [_jsxs(View, {
          style: styles.sectionHeader,
          children: [_jsx(TrendingUp, {
            size: 20,
            color: colors.green
          }), _jsx(Text, {
            style: styles.sectionTitle,
            children: "Match Highlights"
          })]
        }), _jsx(View, {
          style: styles.highlightsList,
          children: match.highlights.map(function (highlight, index) {
            cov_2nmm6qpohh().f[9]++;
            cov_2nmm6qpohh().s[22]++;
            return _jsxs(View, {
              style: styles.highlightItem,
              children: [_jsx(Text, {
                style: styles.highlightBullet,
                children: "\u2713"
              }), _jsx(Text, {
                style: styles.highlightText,
                children: highlight
              })]
            }, index);
          })
        })]
      }), _jsxs(Card, {
        style: styles.sectionCard,
        children: [_jsxs(View, {
          style: styles.sectionHeader,
          children: [_jsx(TrendingDown, {
            size: 20,
            color: colors.red
          }), _jsx(Text, {
            style: styles.sectionTitle,
            children: "Areas for Improvement"
          })]
        }), _jsx(View, {
          style: styles.improvementsList,
          children: match.improvements.map(function (improvement, index) {
            cov_2nmm6qpohh().f[10]++;
            cov_2nmm6qpohh().s[23]++;
            return _jsxs(View, {
              style: styles.improvementItem,
              children: [_jsx(Text, {
                style: styles.improvementBullet,
                children: "\u2022"
              }), _jsx(Text, {
                style: styles.improvementText,
                children: improvement
              })]
            }, index);
          })
        })]
      }), _jsxs(Card, {
        style: styles.sectionCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "Physical Performance"
        }), _jsxs(View, {
          style: styles.physicalStats,
          children: [_jsxs(View, {
            style: styles.physicalStatItem,
            children: [_jsx(Activity, {
              size: 20,
              color: colors.primary
            }), _jsx(Text, {
              style: styles.physicalStatLabel,
              children: "Distance Covered"
            }), _jsx(Text, {
              style: styles.physicalStatValue,
              children: match.detailedStats.distanceCovered
            })]
          }), _jsxs(View, {
            style: styles.physicalStatItem,
            children: [_jsx(Zap, {
              size: 20,
              color: colors.yellow
            }), _jsx(Text, {
              style: styles.physicalStatLabel,
              children: "Avg Rally Length"
            }), _jsxs(Text, {
              style: styles.physicalStatValue,
              children: [match.detailedStats.averageRallyLength, " shots"]
            })]
          }), _jsxs(View, {
            style: styles.physicalStatItem,
            children: [_jsx(Shield, {
              size: 20,
              color: colors.blue
            }), _jsx(Text, {
              style: styles.physicalStatLabel,
              children: "Longest Rally"
            }), _jsxs(Text, {
              style: styles.physicalStatValue,
              children: [match.detailedStats.longestRally, " shots"]
            })]
          })]
        })]
      }), _jsxs(Card, {
        style: styles.sectionCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "Net Play Analysis"
        }), _jsxs(View, {
          style: styles.netPlayStats,
          children: [_jsxs(View, {
            style: styles.netPlayItem,
            children: [_jsx(Text, {
              style: styles.netPlayLabel,
              children: "Net Approaches"
            }), _jsx(Text, {
              style: styles.netPlayValue,
              children: match.detailedStats.netApproaches
            })]
          }), _jsxs(View, {
            style: styles.netPlayItem,
            children: [_jsx(Text, {
              style: styles.netPlayLabel,
              children: "Successful"
            }), _jsx(Text, {
              style: styles.netPlayValue,
              children: match.detailedStats.netSuccess
            })]
          }), _jsxs(View, {
            style: styles.netPlayItem,
            children: [_jsx(Text, {
              style: styles.netPlayLabel,
              children: "Success Rate"
            }), _jsxs(Text, {
              style: styles.netPlayValue,
              children: [Math.round(match.detailedStats.netSuccess / match.detailedStats.netApproaches * 100), "%"]
            })]
          })]
        })]
      })]
    });
  };
  cov_2nmm6qpohh().s[24]++;
  return _jsx(SafeAreaView, {
    style: styles.container,
    children: _jsxs(LinearGradient, {
      colors: ['#1e3a8a', '#3b82f6', '#60a5fa'],
      style: styles.gradient,
      children: [_jsxs(View, {
        style: styles.header,
        children: [_jsx(TouchableOpacity, {
          onPress: function onPress() {
            cov_2nmm6qpohh().f[11]++;
            cov_2nmm6qpohh().s[25]++;
            return router.back();
          },
          style: styles.backButton,
          children: _jsx(ArrowLeft, {
            size: 24,
            color: "white"
          })
        }), _jsx(Text, {
          style: styles.title,
          children: "Match Analysis"
        }), _jsxs(View, {
          style: styles.headerActions,
          children: [_jsx(TouchableOpacity, {
            onPress: handleShare,
            style: styles.headerAction,
            children: _jsx(Share, {
              size: 24,
              color: "white"
            })
          }), _jsx(TouchableOpacity, {
            onPress: handleExport,
            style: styles.headerAction,
            children: _jsx(Download, {
              size: 24,
              color: "white"
            })
          })]
        })]
      }), _jsx(View, {
        style: styles.matchInfo,
        children: _jsxs(Card, {
          style: styles.matchInfoCard,
          children: [_jsxs(View, {
            style: styles.matchInfoRow,
            children: [_jsxs(View, {
              style: styles.matchInfoItem,
              children: [_jsx(Calendar, {
                size: 16,
                color: colors.gray
              }), _jsx(Text, {
                style: styles.matchInfoText,
                children: new Date(match.date).toLocaleDateString()
              })]
            }), _jsxs(View, {
              style: styles.matchInfoItem,
              children: [_jsx(Clock, {
                size: 16,
                color: colors.gray
              }), _jsx(Text, {
                style: styles.matchInfoText,
                children: match.duration
              })]
            }), _jsxs(View, {
              style: styles.matchInfoItem,
              children: [_jsx(MapPin, {
                size: 16,
                color: colors.gray
              }), _jsx(Text, {
                style: styles.matchInfoText,
                children: match.location
              })]
            }), _jsxs(View, {
              style: styles.matchInfoItem,
              children: [_jsx(Target, {
                size: 16,
                color: colors.gray
              }), _jsx(Text, {
                style: styles.matchInfoText,
                children: match.surface.charAt(0).toUpperCase() + match.surface.slice(1)
              })]
            })]
          }), (cov_2nmm6qpohh().b[4][0]++, match.tournament) && (cov_2nmm6qpohh().b[4][1]++, _jsxs(View, {
            style: styles.tournamentInfo,
            children: [_jsx(Trophy, {
              size: 16,
              color: colors.yellow
            }), _jsx(Text, {
              style: styles.tournamentText,
              children: match.tournament
            })]
          }))]
        })
      }), _jsx(View, {
        style: styles.tabsContainer,
        children: _jsx(ScrollView, {
          horizontal: true,
          showsHorizontalScrollIndicator: false,
          children: _jsx(View, {
            style: styles.tabs,
            children: tabs.map(function (tab) {
              cov_2nmm6qpohh().f[12]++;
              cov_2nmm6qpohh().s[26]++;
              return _jsxs(TouchableOpacity, {
                style: [styles.tab, (cov_2nmm6qpohh().b[5][0]++, selectedTab === tab.id) && (cov_2nmm6qpohh().b[5][1]++, styles.activeTab)],
                onPress: function onPress() {
                  cov_2nmm6qpohh().f[13]++;
                  cov_2nmm6qpohh().s[27]++;
                  return setSelectedTab(tab.id);
                },
                children: [_jsx(tab.icon, {
                  size: 16,
                  color: selectedTab === tab.id ? (cov_2nmm6qpohh().b[6][0]++, colors.primary) : (cov_2nmm6qpohh().b[6][1]++, 'white')
                }), _jsx(Text, {
                  style: [styles.tabText, (cov_2nmm6qpohh().b[7][0]++, selectedTab === tab.id) && (cov_2nmm6qpohh().b[7][1]++, styles.activeTabText)],
                  children: tab.label
                })]
              }, tab.id);
            })
          })
        })
      }), _jsxs(ScrollView, {
        style: styles.content,
        showsVerticalScrollIndicator: false,
        children: [(cov_2nmm6qpohh().b[8][0]++, selectedTab === 'overview') && (cov_2nmm6qpohh().b[8][1]++, renderOverview()), (cov_2nmm6qpohh().b[9][0]++, selectedTab === 'stats') && (cov_2nmm6qpohh().b[9][1]++, renderDetailedStats()), (cov_2nmm6qpohh().b[10][0]++, selectedTab === 'analysis') && (cov_2nmm6qpohh().b[10][1]++, renderAnalysis())]
      })]
    })
  });
}
var styles = (cov_2nmm6qpohh().s[28]++, StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10
  },
  backButton: {
    padding: 8
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white'
  },
  headerActions: {
    flexDirection: 'row',
    gap: 10
  },
  headerAction: {
    padding: 8
  },
  matchInfo: {
    paddingHorizontal: 20,
    paddingBottom: 10
  },
  matchInfoCard: {
    padding: 16
  },
  matchInfoRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 15,
    marginBottom: 10
  },
  matchInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6
  },
  matchInfoText: {
    fontSize: 12,
    color: colors.gray
  },
  tournamentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    borderTopWidth: 1,
    borderTopColor: colors.lightGray,
    paddingTop: 10
  },
  tournamentText: {
    fontSize: 12,
    color: colors.yellow,
    fontWeight: '600'
  },
  tabsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 10
  },
  tabs: {
    flexDirection: 'row',
    gap: 10
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20
  },
  activeTab: {
    backgroundColor: 'white'
  },
  tabText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500'
  },
  activeTabText: {
    color: colors.primary
  },
  content: {
    flex: 1,
    paddingHorizontal: 20
  },
  tabContent: {
    paddingBottom: 30
  },
  resultCard: {
    padding: 20,
    marginBottom: 15,
    alignItems: 'center'
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
    marginBottom: 10
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.dark
  },
  resultBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16
  },
  resultText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600'
  },
  scoreText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.dark,
    marginBottom: 5
  },
  opponentText: {
    fontSize: 16,
    color: colors.gray
  },
  sectionCard: {
    padding: 20,
    marginBottom: 15
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.dark,
    marginBottom: 15
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 15
  },
  setsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around'
  },
  setItem: {
    alignItems: 'center'
  },
  setLabel: {
    fontSize: 12,
    color: colors.gray,
    marginBottom: 8
  },
  setScore: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  setPlayerScore: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.dark
  },
  setOpponentScore: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.dark
  },
  setDivider: {
    fontSize: 16,
    color: colors.gray
  },
  winningScore: {
    color: colors.green
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around'
  },
  statBox: {
    alignItems: 'center'
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.dark
  },
  statLabel: {
    fontSize: 12,
    color: colors.gray,
    marginTop: 4
  },
  ringsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around'
  },
  ringItem: {
    alignItems: 'center'
  },
  ringLabel: {
    fontSize: 12,
    color: colors.gray,
    marginTop: 8,
    textAlign: 'center'
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray
  },
  statRowLabel: {
    fontSize: 14,
    color: colors.dark
  },
  statRowValue: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.dark
  },
  shotGrid: {
    flexDirection: 'row',
    gap: 20
  },
  shotCategory: {
    flex: 1
  },
  shotCategoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.dark,
    marginBottom: 10
  },
  shotItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 6
  },
  shotLabel: {
    fontSize: 14,
    color: colors.gray
  },
  shotValue: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.dark
  },
  setStatsContainer: {
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray
  },
  setStatsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.dark,
    marginBottom: 10
  },
  setStatsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around'
  },
  setStatItem: {
    alignItems: 'center'
  },
  setStatValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.dark
  },
  setStatLabel: {
    fontSize: 10,
    color: colors.gray,
    marginTop: 2
  },
  highlightsList: {
    gap: 10
  },
  highlightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10
  },
  highlightBullet: {
    fontSize: 16,
    color: colors.green,
    fontWeight: 'bold'
  },
  highlightText: {
    flex: 1,
    fontSize: 14,
    color: colors.dark,
    lineHeight: 20
  },
  improvementsList: {
    gap: 10
  },
  improvementItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10
  },
  improvementBullet: {
    fontSize: 16,
    color: colors.red,
    fontWeight: 'bold'
  },
  improvementText: {
    flex: 1,
    fontSize: 14,
    color: colors.dark,
    lineHeight: 20
  },
  physicalStats: {
    gap: 15
  },
  physicalStatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12
  },
  physicalStatLabel: {
    flex: 1,
    fontSize: 14,
    color: colors.dark
  },
  physicalStatValue: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.dark
  },
  netPlayStats: {
    flexDirection: 'row',
    justifyContent: 'space-around'
  },
  netPlayItem: {
    alignItems: 'center'
  },
  netPlayLabel: {
    fontSize: 12,
    color: colors.gray,
    marginBottom: 4
  },
  netPlayValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.dark
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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