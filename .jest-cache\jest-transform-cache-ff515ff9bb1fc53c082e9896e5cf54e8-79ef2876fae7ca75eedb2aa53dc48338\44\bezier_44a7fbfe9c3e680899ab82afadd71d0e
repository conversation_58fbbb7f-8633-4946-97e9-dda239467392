c241ef2d0e5e8230dd960c293692a902
'use strict';
exports.__esModule = true;
exports.default = bezier;
var NEWTON_ITERATIONS = 4;
var NEWTON_MIN_SLOPE = 0.001;
var SUBDIVISION_PRECISION = 0.0000001;
var SUBDIVISION_MAX_ITERATIONS = 10;
var kSplineTableSize = 11;
var kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);
var float32ArraySupported = typeof Float32Array === 'function';
function A(aA1, aA2) {
  return 1.0 - 3.0 * aA2 + 3.0 * aA1;
}
function B(aA1, aA2) {
  return 3.0 * aA2 - 6.0 * aA1;
}
function C(aA1) {
  return 3.0 * aA1;
}
function calcBezier(aT, aA1, aA2) {
  return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT;
}
function getSlope(aT, aA1, aA2) {
  return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1);
}
function binarySubdivide(aX, _aA, _aB, mX1, mX2) {
  var currentX,
    currentT,
    i = 0,
    aA = _aA,
    aB = _aB;
  do {
    currentT = aA + (aB - aA) / 2.0;
    currentX = calcBezier(currentT, mX1, mX2) - aX;
    if (currentX > 0.0) {
      aB = currentT;
    } else {
      aA = currentT;
    }
  } while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i < SUBDIVISION_MAX_ITERATIONS);
  return currentT;
}
function newtonRaphsonIterate(aX, _aGuessT, mX1, mX2) {
  var aGuessT = _aGuessT;
  for (var i = 0; i < NEWTON_ITERATIONS; ++i) {
    var currentSlope = getSlope(aGuessT, mX1, mX2);
    if (currentSlope === 0.0) {
      return aGuessT;
    }
    var currentX = calcBezier(aGuessT, mX1, mX2) - aX;
    aGuessT -= currentX / currentSlope;
  }
  return aGuessT;
}
function bezier(mX1, mY1, mX2, mY2) {
  if (!(mX1 >= 0 && mX1 <= 1 && mX2 >= 0 && mX2 <= 1)) {
    throw new Error('bezier x values must be in [0, 1] range');
  }
  var sampleValues = float32ArraySupported ? new Float32Array(kSplineTableSize) : new Array(kSplineTableSize);
  if (mX1 !== mY1 || mX2 !== mY2) {
    for (var i = 0; i < kSplineTableSize; ++i) {
      sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);
    }
  }
  function getTForX(aX) {
    var intervalStart = 0.0;
    var currentSample = 1;
    var lastSample = kSplineTableSize - 1;
    for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {
      intervalStart += kSampleStepSize;
    }
    --currentSample;
    var dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);
    var guessForT = intervalStart + dist * kSampleStepSize;
    var initialSlope = getSlope(guessForT, mX1, mX2);
    if (initialSlope >= NEWTON_MIN_SLOPE) {
      return newtonRaphsonIterate(aX, guessForT, mX1, mX2);
    } else if (initialSlope === 0.0) {
      return guessForT;
    } else {
      return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);
    }
  }
  return function BezierEasing(x) {
    if (mX1 === mY1 && mX2 === mY2) {
      return x;
    }
    if (x === 0) {
      return 0;
    }
    if (x === 1) {
      return 1;
    }
    return calcBezier(getTForX(x), mY1, mY2);
  };
}
;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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