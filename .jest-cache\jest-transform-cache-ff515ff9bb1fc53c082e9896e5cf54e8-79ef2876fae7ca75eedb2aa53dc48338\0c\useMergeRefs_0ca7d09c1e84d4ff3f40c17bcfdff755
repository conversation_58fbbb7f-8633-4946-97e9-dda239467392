b39dd64dd2498a61e82fd0fdf67202d4
"use strict";

exports.__esModule = true;
exports.default = useMergeRefs;
var _react = require("react");
function useMergeRefs() {
  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {
    refs[_key] = arguments[_key];
  }
  return (0, _react.useCallback)(function (current) {
    for (var _i = 0, _refs = refs; _i < _refs.length; _i++) {
      var ref = _refs[_i];
      if (ref != null) {
        if (typeof ref === 'function') {
          ref(current);
        } else {
          ref.current = current;
        }
      }
    }
  }, [].concat(refs));
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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