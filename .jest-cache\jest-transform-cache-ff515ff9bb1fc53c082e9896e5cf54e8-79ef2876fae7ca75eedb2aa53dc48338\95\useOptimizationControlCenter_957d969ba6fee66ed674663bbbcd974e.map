{"version": 3, "names": ["useState", "useEffect", "useCallback", "useMemo", "usePerformanceDashboard", "useAIOptimization", "useEdgeOptimization", "useNativeOptimization", "advancedCustomizationManager", "futureEnhancementsManager", "useOptimizationControlCenter", "initialConfig", "arguments", "length", "undefined", "cov_2blu1uqp5u", "b", "f", "dashboard", "s", "ai", "edge", "native", "_ref", "Object", "assign", "enableRealTimeMonitoring", "enableAdvancedCustomization", "enableFuturePlanning", "autoOptimization", "alertThreshold", "updateInterval", "_ref2", "_slicedToArray", "config", "setConfig", "_ref3", "isInitialized", "currentMode", "systemHealth", "overall", "phases", "alerts", "criticalIssues", "optimization", "totalImprovement", "activeOptimizations", "performanceScore", "efficiency", "customization", "activeProfile", "availableProfiles", "abTestsRunning", "customizationScore", "planning", "roadmapPhases", "pipelineEnhancements", "investmentRequired", "expectedROI", "insights", "_ref4", "state", "setState", "switchMode", "mode", "prev", "applyCustomProfile", "_ref5", "_asyncToGenerator", "profileId", "success", "applyProfile", "error", "console", "_x", "apply", "startABTest", "_ref6", "testConfig", "id", "variants", "duration", "_x2", "exportSystemReport", "format", "report", "timestamp", "Date", "now", "dashboardData", "actions", "exportData", "customizationAnalytics", "getCustomizationAnalytics", "roadmap", "getStrategicRoadmap", "technologyTrends", "getTechnologyTrendAnalysis", "JSON", "stringify", "optimizeAllSystems", "log", "optimizePerformance", "preloadGlobally", "optimizeMemory", "refreshMetrics", "getSystemRecommendations", "recommendations", "customizationRecs", "getOptimizationRecommendations", "push", "_toConsumableArray", "dashboardRecs", "trendAnalysis", "adoptionRecs", "adoptionRecommendations", "filter", "rec", "recommendation", "map", "category", "toUpperCase", "technology", "impact", "effort", "expectedImprovement", "scheduleEnhancement", "_ref0", "enhancementId", "_x3", "getInvestmentAnalysis", "acknowledgeInsight", "insightId", "insight", "updateControlCenterState", "summary", "healthScore", "phase1", "phaseStatus", "health", "phase2", "phase3a", "phase3b", "phase3c", "active<PERSON>lerts", "alert", "severity", "overallScore", "Math", "round", "keys", "profilePerformance", "values", "abTestResults", "test", "status", "investmentAnalysis", "getInnovationPipeline", "pipeline", "totalInvestment", "roi", "mediumTerm", "generateSystemInsights", "_ref10", "message", "priority", "actionable", "_x4", "_x5", "_x6", "_x7", "interval", "setInterval", "clearInterval", "initialize", "_ref11", "_state$insights$find", "_state$insights$find2", "overallHealth", "performanceGrade", "optimizationLevel", "futureReadiness", "topPriority", "find"], "sources": ["useOptimizationControlCenter.ts"], "sourcesContent": ["/**\n * Optimization Control Center Hook\n * \n * Master control center that unifies monitoring, customization, and future planning\n * for comprehensive optimization management across all phases.\n */\n\nimport { useState, useEffect, useCallback, useMemo } from 'react';\nimport { usePerformanceDashboard } from './usePerformanceDashboard';\nimport { useAIOptimization } from './useAIOptimization';\nimport { useEdgeOptimization } from './useEdgeOptimization';\nimport { useNativeOptimization } from './useNativeOptimization';\nimport { advancedCustomizationManager } from '@/services/customization/AdvancedCustomizationManager';\nimport { futureEnhancementsManager } from '@/services/planning/FutureEnhancementsManager';\n\ninterface ControlCenterState {\n  isInitialized: boolean;\n  currentMode: 'monitoring' | 'customization' | 'planning' | 'overview';\n  systemHealth: {\n    overall: number;\n    phases: Record<string, number>;\n    alerts: number;\n    criticalIssues: number;\n  };\n  optimization: {\n    totalImprovement: number;\n    activeOptimizations: number;\n    performanceScore: number;\n    efficiency: number;\n  };\n  customization: {\n    activeProfile: string | null;\n    availableProfiles: number;\n    abTestsRunning: number;\n    customizationScore: number;\n  };\n  planning: {\n    roadmapPhases: number;\n    pipelineEnhancements: number;\n    investmentRequired: number;\n    expectedROI: number;\n  };\n  insights: Array<{\n    category: string;\n    message: string;\n    priority: 'low' | 'medium' | 'high' | 'critical';\n    actionable: boolean;\n  }>;\n}\n\ninterface ControlCenterConfig {\n  enableRealTimeMonitoring: boolean;\n  enableAdvancedCustomization: boolean;\n  enableFuturePlanning: boolean;\n  autoOptimization: boolean;\n  alertThreshold: number;\n  updateInterval: number;\n}\n\ninterface UseOptimizationControlCenterReturn {\n  state: ControlCenterState;\n  dashboard: ReturnType<typeof usePerformanceDashboard>;\n  ai: ReturnType<typeof useAIOptimization>;\n  edge: ReturnType<typeof useEdgeOptimization>;\n  native: ReturnType<typeof useNativeOptimization>;\n  actions: {\n    switchMode: (mode: ControlCenterState['currentMode']) => void;\n    applyCustomProfile: (profileId: string) => Promise<boolean>;\n    startABTest: (testConfig: any) => Promise<boolean>;\n    exportSystemReport: (format: 'json' | 'pdf' | 'excel') => Promise<string>;\n    optimizeAllSystems: () => Promise<void>;\n    getSystemRecommendations: () => Promise<any[]>;\n    scheduleEnhancement: (enhancementId: string) => Promise<boolean>;\n    getInvestmentAnalysis: () => any;\n    acknowledgeInsight: (insightId: string) => void;\n  };\n  config: ControlCenterConfig;\n  summary: {\n    overallHealth: string;\n    performanceGrade: string;\n    optimizationLevel: string;\n    futureReadiness: string;\n    topPriority: string;\n  };\n}\n\n/**\n * Optimization Control Center Hook\n */\nexport function useOptimizationControlCenter(\n  initialConfig: Partial<ControlCenterConfig> = {}\n): UseOptimizationControlCenterReturn {\n  const dashboard = usePerformanceDashboard();\n  const ai = useAIOptimization();\n  const edge = useEdgeOptimization();\n  const native = useNativeOptimization();\n\n  const [config, setConfig] = useState<ControlCenterConfig>({\n    enableRealTimeMonitoring: true,\n    enableAdvancedCustomization: true,\n    enableFuturePlanning: true,\n    autoOptimization: true,\n    alertThreshold: 80,\n    updateInterval: 30000, // 30 seconds\n    ...initialConfig,\n  });\n\n  const [state, setState] = useState<ControlCenterState>({\n    isInitialized: false,\n    currentMode: 'overview',\n    systemHealth: {\n      overall: 0,\n      phases: {},\n      alerts: 0,\n      criticalIssues: 0,\n    },\n    optimization: {\n      totalImprovement: 0,\n      activeOptimizations: 0,\n      performanceScore: 0,\n      efficiency: 0,\n    },\n    customization: {\n      activeProfile: null,\n      availableProfiles: 0,\n      abTestsRunning: 0,\n      customizationScore: 0,\n    },\n    planning: {\n      roadmapPhases: 0,\n      pipelineEnhancements: 0,\n      investmentRequired: 0,\n      expectedROI: 0,\n    },\n    insights: [],\n  });\n\n  /**\n   * Switch control center mode\n   */\n  const switchMode = useCallback((mode: ControlCenterState['currentMode']) => {\n    setState(prev => ({ ...prev, currentMode: mode }));\n  }, []);\n\n  /**\n   * Apply custom optimization profile\n   */\n  const applyCustomProfile = useCallback(async (profileId: string) => {\n    try {\n      const success = await advancedCustomizationManager.applyProfile(profileId);\n      if (success) {\n        setState(prev => ({\n          ...prev,\n          customization: {\n            ...prev.customization,\n            activeProfile: profileId,\n          },\n        }));\n      }\n      return success;\n    } catch (error) {\n      console.error('Failed to apply custom profile:', error);\n      return false;\n    }\n  }, []);\n\n  /**\n   * Start A/B test\n   */\n  const startABTest = useCallback(async (testConfig: any) => {\n    try {\n      const success = await advancedCustomizationManager.startABTest(\n        testConfig.id,\n        testConfig.variants,\n        testConfig.duration\n      );\n      return success;\n    } catch (error) {\n      console.error('Failed to start A/B test:', error);\n      return false;\n    }\n  }, []);\n\n  /**\n   * Export comprehensive system report\n   */\n  const exportSystemReport = useCallback(async (format: 'json' | 'pdf' | 'excel' = 'json') => {\n    try {\n      const report = {\n        timestamp: Date.now(),\n        systemHealth: state.systemHealth,\n        optimization: state.optimization,\n        customization: state.customization,\n        planning: state.planning,\n        insights: state.insights,\n        dashboardData: await dashboard.actions.exportData(format),\n        customizationAnalytics: advancedCustomizationManager.getCustomizationAnalytics(),\n        roadmap: futureEnhancementsManager.getStrategicRoadmap(),\n        technologyTrends: futureEnhancementsManager.getTechnologyTrendAnalysis(),\n      };\n\n      switch (format) {\n        case 'json':\n          return JSON.stringify(report, null, 2);\n        case 'pdf':\n          return 'PDF export not implemented yet';\n        case 'excel':\n          return 'Excel export not implemented yet';\n        default:\n          return JSON.stringify(report, null, 2);\n      }\n    } catch (error) {\n      console.error('Failed to export system report:', error);\n      return '';\n    }\n  }, [state, dashboard.actions]);\n\n  /**\n   * Optimize all systems\n   */\n  const optimizeAllSystems = useCallback(async () => {\n    try {\n      console.log('Starting comprehensive system optimization...');\n      \n      // Optimize AI systems\n      if (config.enableRealTimeMonitoring && ai.state.isInitialized) {\n        await ai.actions.optimizePerformance();\n      }\n      \n      // Optimize Edge systems\n      if (edge.state.isInitialized) {\n        await edge.actions.preloadGlobally(['critical_content']);\n      }\n      \n      // Optimize Native systems\n      if (native.state.isInitialized) {\n        await native.actions.optimizeMemory(false);\n      }\n      \n      // Refresh dashboard metrics\n      await dashboard.actions.refreshMetrics();\n      \n      console.log('Comprehensive system optimization completed');\n    } catch (error) {\n      console.error('Failed to optimize all systems:', error);\n    }\n  }, [config, ai, edge, native, dashboard]);\n\n  /**\n   * Get system recommendations\n   */\n  const getSystemRecommendations = useCallback(async () => {\n    try {\n      const recommendations = [];\n      \n      // Get customization recommendations\n      const customizationRecs = await advancedCustomizationManager.getOptimizationRecommendations();\n      recommendations.push(...customizationRecs);\n      \n      // Get dashboard recommendations\n      const dashboardRecs = await dashboard.actions.getOptimizationRecommendations();\n      recommendations.push(...dashboardRecs);\n      \n      // Get future enhancement recommendations\n      const trendAnalysis = futureEnhancementsManager.getTechnologyTrendAnalysis();\n      const adoptionRecs = trendAnalysis.adoptionRecommendations\n        .filter(rec => rec.recommendation === 'adopt' || rec.recommendation === 'trial')\n        .map(rec => ({\n          category: 'Technology Adoption',\n          recommendation: `${rec.recommendation.toUpperCase()}: ${rec.technology}`,\n          impact: 'high',\n          effort: 'medium',\n          expectedImprovement: 20,\n        }));\n      recommendations.push(...adoptionRecs);\n      \n      return recommendations;\n    } catch (error) {\n      console.error('Failed to get system recommendations:', error);\n      return [];\n    }\n  }, [dashboard.actions]);\n\n  /**\n   * Schedule future enhancement\n   */\n  const scheduleEnhancement = useCallback(async (enhancementId: string) => {\n    try {\n      // This would integrate with project management systems\n      console.log(`Scheduling enhancement: ${enhancementId}`);\n      return true;\n    } catch (error) {\n      console.error('Failed to schedule enhancement:', error);\n      return false;\n    }\n  }, []);\n\n  /**\n   * Get investment analysis\n   */\n  const getInvestmentAnalysis = useCallback(() => {\n    return futureEnhancementsManager.getInvestmentAnalysis();\n  }, []);\n\n  /**\n   * Acknowledge insight\n   */\n  const acknowledgeInsight = useCallback((insightId: string) => {\n    setState(prev => ({\n      ...prev,\n      insights: prev.insights.filter(insight => insight.category !== insightId),\n    }));\n  }, []);\n\n  /**\n   * Update control center state\n   */\n  const updateControlCenterState = useCallback(async () => {\n    try {\n      // Get system health from dashboard\n      const systemHealth = {\n        overall: dashboard.summary.healthScore,\n        phases: {\n          phase1: dashboard.state.phaseStatus.phase1.health,\n          phase2: dashboard.state.phaseStatus.phase2.health,\n          phase3a: dashboard.state.phaseStatus.phase3a.health,\n          phase3b: dashboard.state.phaseStatus.phase3b.health,\n          phase3c: dashboard.state.phaseStatus.phase3c.health,\n        },\n        alerts: dashboard.summary.activeAlerts,\n        criticalIssues: dashboard.state.alerts.filter(alert => alert.severity === 'critical').length,\n      };\n\n      // Get optimization metrics\n      const optimization = {\n        totalImprovement: dashboard.summary.totalImprovement,\n        activeOptimizations: 5, // All phases active\n        performanceScore: dashboard.summary.overallScore,\n        efficiency: Math.round((dashboard.summary.overallScore / 500) * 100), // Scale to percentage\n      };\n\n      // Get customization metrics\n      const customizationAnalytics = advancedCustomizationManager.getCustomizationAnalytics();\n      const customization = {\n        activeProfile: customizationAnalytics.activeProfile,\n        availableProfiles: Object.keys(customizationAnalytics.profilePerformance).length,\n        abTestsRunning: Object.values(customizationAnalytics.abTestResults)\n          .filter((test: any) => test.status === 'running').length,\n        customizationScore: 85, // Would calculate from actual metrics\n      };\n\n      // Get planning metrics\n      const roadmap = futureEnhancementsManager.getStrategicRoadmap();\n      const investmentAnalysis = futureEnhancementsManager.getInvestmentAnalysis();\n      const planning = {\n        roadmapPhases: roadmap.phases.length,\n        pipelineEnhancements: futureEnhancementsManager.getInnovationPipeline().pipeline.length,\n        investmentRequired: investmentAnalysis.totalInvestment,\n        expectedROI: investmentAnalysis.roi.mediumTerm,\n      };\n\n      // Generate insights\n      const insights = await generateSystemInsights(systemHealth, optimization, customization, planning);\n\n      setState(prev => ({\n        ...prev,\n        systemHealth,\n        optimization,\n        customization,\n        planning,\n        insights,\n      }));\n\n    } catch (error) {\n      console.error('Failed to update control center state:', error);\n    }\n  }, [dashboard]);\n\n  // Generate system insights\n  const generateSystemInsights = async (\n    systemHealth: any,\n    optimization: any,\n    customization: any,\n    planning: any\n  ) => {\n    const insights = [];\n\n    if (systemHealth.overall > 95) {\n      insights.push({\n        category: 'System Health',\n        message: 'All systems operating at peak performance',\n        priority: 'low' as const,\n        actionable: false,\n      });\n    }\n\n    if (optimization.totalImprovement > 300) {\n      insights.push({\n        category: 'Performance',\n        message: 'Exceptional performance gains achieved across all phases',\n        priority: 'medium' as const,\n        actionable: false,\n      });\n    }\n\n    if (customization.abTestsRunning > 0) {\n      insights.push({\n        category: 'Customization',\n        message: `${customization.abTestsRunning} A/B tests currently running`,\n        priority: 'medium' as const,\n        actionable: true,\n      });\n    }\n\n    if (planning.expectedROI > 200) {\n      insights.push({\n        category: 'Planning',\n        message: 'High ROI potential identified in future enhancements',\n        priority: 'high' as const,\n        actionable: true,\n      });\n    }\n\n    return insights;\n  };\n\n  // Auto-update effect\n  useEffect(() => {\n    if (!config.enableRealTimeMonitoring) return;\n\n    const interval = setInterval(() => {\n      updateControlCenterState();\n    }, config.updateInterval);\n\n    return () => clearInterval(interval);\n  }, [config.enableRealTimeMonitoring, config.updateInterval, updateControlCenterState]);\n\n  // Initialize effect\n  useEffect(() => {\n    const initialize = async () => {\n      await updateControlCenterState();\n      setState(prev => ({ ...prev, isInitialized: true }));\n    };\n\n    initialize();\n  }, []);\n\n  // Calculate summary\n  const summary = useMemo(() => {\n    const overallHealth = state.systemHealth.overall > 95 ? 'Excellent' :\n                         state.systemHealth.overall > 85 ? 'Good' :\n                         state.systemHealth.overall > 70 ? 'Fair' : 'Poor';\n\n    const performanceGrade = state.optimization.performanceScore > 450 ? 'A+' :\n                            state.optimization.performanceScore > 400 ? 'A' :\n                            state.optimization.performanceScore > 350 ? 'B+' :\n                            state.optimization.performanceScore > 300 ? 'B' : 'C';\n\n    const optimizationLevel = state.optimization.efficiency > 90 ? 'Maximum' :\n                             state.optimization.efficiency > 75 ? 'High' :\n                             state.optimization.efficiency > 60 ? 'Medium' : 'Low';\n\n    const futureReadiness = state.planning.expectedROI > 200 ? 'Excellent' :\n                           state.planning.expectedROI > 150 ? 'Good' :\n                           state.planning.expectedROI > 100 ? 'Fair' : 'Poor';\n\n    const topPriority = state.insights.find(insight => insight.priority === 'critical')?.message ||\n                       state.insights.find(insight => insight.priority === 'high')?.message ||\n                       'No critical issues identified';\n\n    return {\n      overallHealth,\n      performanceGrade,\n      optimizationLevel,\n      futureReadiness,\n      topPriority,\n    };\n  }, [state]);\n\n  return useMemo(() => ({\n    state,\n    dashboard,\n    ai,\n    edge,\n    native,\n    actions: {\n      switchMode,\n      applyCustomProfile,\n      startABTest,\n      exportSystemReport,\n      optimizeAllSystems,\n      getSystemRecommendations,\n      scheduleEnhancement,\n      getInvestmentAnalysis,\n      acknowledgeInsight,\n    },\n    config,\n    summary,\n  }), [\n    state,\n    dashboard,\n    ai,\n    edge,\n    native,\n    switchMode,\n    applyCustomProfile,\n    startABTest,\n    exportSystemReport,\n    optimizeAllSystems,\n    getSystemRecommendations,\n    scheduleEnhancement,\n    getInvestmentAnalysis,\n    acknowledgeInsight,\n    config,\n    summary,\n  ]);\n}\n\nexport default useOptimizationControlCenter;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACjE,SAASC,uBAAuB;AAChC,SAASC,iBAAiB;AAC1B,SAASC,mBAAmB;AAC5B,SAASC,qBAAqB;AAC9B,SAASC,4BAA4B;AACrC,SAASC,yBAAyB;AA4ElC,OAAO,SAASC,4BAA4BA,CAAA,EAEN;EAAA,IADpCC,aAA2C,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,cAAA,GAAAC,CAAA,UAAG,CAAC,CAAC;EAAAD,cAAA,GAAAE,CAAA;EAEhD,IAAMC,SAAS,IAAAH,cAAA,GAAAI,CAAA,OAAGf,uBAAuB,CAAC,CAAC;EAC3C,IAAMgB,EAAE,IAAAL,cAAA,GAAAI,CAAA,OAAGd,iBAAiB,CAAC,CAAC;EAC9B,IAAMgB,IAAI,IAAAN,cAAA,GAAAI,CAAA,OAAGb,mBAAmB,CAAC,CAAC;EAClC,IAAMgB,MAAM,IAAAP,cAAA,GAAAI,CAAA,OAAGZ,qBAAqB,CAAC,CAAC;EAEtC,IAAAgB,IAAA,IAAAR,cAAA,GAAAI,CAAA,OAA4BnB,QAAQ,CAAAwB,MAAA,CAAAC,MAAA;MAClCC,wBAAwB,EAAE,IAAI;MAC9BC,2BAA2B,EAAE,IAAI;MACjCC,oBAAoB,EAAE,IAAI;MAC1BC,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;IAAK,GAClBpB,aAAa,CACjB,CAAC;IAAAqB,KAAA,GAAAC,cAAA,CAAAV,IAAA;IARKW,MAAM,GAAAF,KAAA;IAAEG,SAAS,GAAAH,KAAA;EAUxB,IAAAI,KAAA,IAAArB,cAAA,GAAAI,CAAA,OAA0BnB,QAAQ,CAAqB;MACrDqC,aAAa,EAAE,KAAK;MACpBC,WAAW,EAAE,UAAU;MACvBC,YAAY,EAAE;QACZC,OAAO,EAAE,CAAC;QACVC,MAAM,EAAE,CAAC,CAAC;QACVC,MAAM,EAAE,CAAC;QACTC,cAAc,EAAE;MAClB,CAAC;MACDC,YAAY,EAAE;QACZC,gBAAgB,EAAE,CAAC;QACnBC,mBAAmB,EAAE,CAAC;QACtBC,gBAAgB,EAAE,CAAC;QACnBC,UAAU,EAAE;MACd,CAAC;MACDC,aAAa,EAAE;QACbC,aAAa,EAAE,IAAI;QACnBC,iBAAiB,EAAE,CAAC;QACpBC,cAAc,EAAE,CAAC;QACjBC,kBAAkB,EAAE;MACtB,CAAC;MACDC,QAAQ,EAAE;QACRC,aAAa,EAAE,CAAC;QAChBC,oBAAoB,EAAE,CAAC;QACvBC,kBAAkB,EAAE,CAAC;QACrBC,WAAW,EAAE;MACf,CAAC;MACDC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAAAC,KAAA,GAAA3B,cAAA,CAAAG,KAAA;IA5BKyB,KAAK,GAAAD,KAAA;IAAEE,QAAQ,GAAAF,KAAA;EAiCtB,IAAMG,UAAU,IAAAhD,cAAA,GAAAI,CAAA,OAAGjB,WAAW,CAAC,UAAC8D,IAAuC,EAAK;IAAAjD,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC1E2C,QAAQ,CAAC,UAAAG,IAAI,EAAK;MAAAlD,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAAK,MAAA,CAAAC,MAAA,KAAKwC,IAAI;QAAE3B,WAAW,EAAE0B;MAAI;IAAC,CAAE,CAAC;EACpD,CAAC,EAAE,EAAE,CAAC;EAKN,IAAME,kBAAkB,IAAAnD,cAAA,GAAAI,CAAA,OAAGjB,WAAW;IAAA,IAAAiE,KAAA,GAAAC,iBAAA,CAAC,WAAOC,SAAiB,EAAK;MAAAtD,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAClE,IAAI;QACF,IAAMmD,OAAO,IAAAvD,cAAA,GAAAI,CAAA,cAASX,4BAA4B,CAAC+D,YAAY,CAACF,SAAS,CAAC;QAACtD,cAAA,GAAAI,CAAA;QAC3E,IAAImD,OAAO,EAAE;UAAAvD,cAAA,GAAAC,CAAA;UAAAD,cAAA,GAAAI,CAAA;UACX2C,QAAQ,CAAC,UAAAG,IAAI,EAAK;YAAAlD,cAAA,GAAAE,CAAA;YAAAF,cAAA,GAAAI,CAAA;YAAA,OAAAK,MAAA,CAAAC,MAAA,KACbwC,IAAI;cACPhB,aAAa,EAAAzB,MAAA,CAAAC,MAAA,KACRwC,IAAI,CAAChB,aAAa;gBACrBC,aAAa,EAAEmB;cAAS;YACzB;UACH,CAAE,CAAC;QACL,CAAC;UAAAtD,cAAA,GAAAC,CAAA;QAAA;QAAAD,cAAA,GAAAI,CAAA;QACD,OAAOmD,OAAO;MAChB,CAAC,CAAC,OAAOE,KAAK,EAAE;QAAAzD,cAAA,GAAAI,CAAA;QACdsD,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QAACzD,cAAA,GAAAI,CAAA;QACxD,OAAO,KAAK;MACd;IACF,CAAC;IAAA,iBAAAuD,EAAA;MAAA,OAAAP,KAAA,CAAAQ,KAAA,OAAA/D,SAAA;IAAA;EAAA,KAAE,EAAE,CAAC;EAKN,IAAMgE,WAAW,IAAA7D,cAAA,GAAAI,CAAA,QAAGjB,WAAW;IAAA,IAAA2E,KAAA,GAAAT,iBAAA,CAAC,WAAOU,UAAe,EAAK;MAAA/D,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MACzD,IAAI;QACF,IAAMmD,OAAO,IAAAvD,cAAA,GAAAI,CAAA,cAASX,4BAA4B,CAACoE,WAAW,CAC5DE,UAAU,CAACC,EAAE,EACbD,UAAU,CAACE,QAAQ,EACnBF,UAAU,CAACG,QACb,CAAC;QAAClE,cAAA,GAAAI,CAAA;QACF,OAAOmD,OAAO;MAChB,CAAC,CAAC,OAAOE,KAAK,EAAE;QAAAzD,cAAA,GAAAI,CAAA;QACdsD,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QAACzD,cAAA,GAAAI,CAAA;QAClD,OAAO,KAAK;MACd;IACF,CAAC;IAAA,iBAAA+D,GAAA;MAAA,OAAAL,KAAA,CAAAF,KAAA,OAAA/D,SAAA;IAAA;EAAA,KAAE,EAAE,CAAC;EAKN,IAAMuE,kBAAkB,IAAApE,cAAA,GAAAI,CAAA,QAAGjB,WAAW,CAAAkE,iBAAA,CAAC,aAAqD;IAAA,IAA9CgB,MAAgC,GAAAxE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,cAAA,GAAAC,CAAA,UAAG,MAAM;IAAAD,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACrF,IAAI;MACF,IAAMkE,MAAM,IAAAtE,cAAA,GAAAI,CAAA,QAAG;QACbmE,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBjD,YAAY,EAAEsB,KAAK,CAACtB,YAAY;QAChCK,YAAY,EAAEiB,KAAK,CAACjB,YAAY;QAChCK,aAAa,EAAEY,KAAK,CAACZ,aAAa;QAClCK,QAAQ,EAAEO,KAAK,CAACP,QAAQ;QACxBK,QAAQ,EAAEE,KAAK,CAACF,QAAQ;QACxB8B,aAAa,QAAQvE,SAAS,CAACwE,OAAO,CAACC,UAAU,CAACP,MAAM,CAAC;QACzDQ,sBAAsB,EAAEpF,4BAA4B,CAACqF,yBAAyB,CAAC,CAAC;QAChFC,OAAO,EAAErF,yBAAyB,CAACsF,mBAAmB,CAAC,CAAC;QACxDC,gBAAgB,EAAEvF,yBAAyB,CAACwF,0BAA0B,CAAC;MACzE,CAAC;MAAClF,cAAA,GAAAI,CAAA;MAEF,QAAQiE,MAAM;QACZ,KAAK,MAAM;UAAArE,cAAA,GAAAC,CAAA;UAAAD,cAAA,GAAAI,CAAA;UACT,OAAO+E,IAAI,CAACC,SAAS,CAACd,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QACxC,KAAK,KAAK;UAAAtE,cAAA,GAAAC,CAAA;UAAAD,cAAA,GAAAI,CAAA;UACR,OAAO,gCAAgC;QACzC,KAAK,OAAO;UAAAJ,cAAA,GAAAC,CAAA;UAAAD,cAAA,GAAAI,CAAA;UACV,OAAO,kCAAkC;QAC3C;UAAAJ,cAAA,GAAAC,CAAA;UAAAD,cAAA,GAAAI,CAAA;UACE,OAAO+E,IAAI,CAACC,SAAS,CAACd,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOb,KAAK,EAAE;MAAAzD,cAAA,GAAAI,CAAA;MACdsD,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MAACzD,cAAA,GAAAI,CAAA;MACxD,OAAO,EAAE;IACX;EACF,CAAC,GAAE,CAAC0C,KAAK,EAAE3C,SAAS,CAACwE,OAAO,CAAC,CAAC;EAK9B,IAAMU,kBAAkB,IAAArF,cAAA,GAAAI,CAAA,QAAGjB,WAAW,CAAAkE,iBAAA,CAAC,aAAY;IAAArD,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACjD,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACFsD,OAAO,CAAC4B,GAAG,CAAC,+CAA+C,CAAC;MAACtF,cAAA,GAAAI,CAAA;MAG7D,IAAI,CAAAJ,cAAA,GAAAC,CAAA,UAAAkB,MAAM,CAACR,wBAAwB,MAAAX,cAAA,GAAAC,CAAA,UAAII,EAAE,CAACyC,KAAK,CAACxB,aAAa,GAAE;QAAAtB,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAC7D,MAAMC,EAAE,CAACsE,OAAO,CAACY,mBAAmB,CAAC,CAAC;MACxC,CAAC;QAAAvF,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAGD,IAAIE,IAAI,CAACwC,KAAK,CAACxB,aAAa,EAAE;QAAAtB,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAC5B,MAAME,IAAI,CAACqE,OAAO,CAACa,eAAe,CAAC,CAAC,kBAAkB,CAAC,CAAC;MAC1D,CAAC;QAAAxF,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAGD,IAAIG,MAAM,CAACuC,KAAK,CAACxB,aAAa,EAAE;QAAAtB,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAC9B,MAAMG,MAAM,CAACoE,OAAO,CAACc,cAAc,CAAC,KAAK,CAAC;MAC5C,CAAC;QAAAzF,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAGD,MAAMD,SAAS,CAACwE,OAAO,CAACe,cAAc,CAAC,CAAC;MAAC1F,cAAA,GAAAI,CAAA;MAEzCsD,OAAO,CAAC4B,GAAG,CAAC,6CAA6C,CAAC;IAC5D,CAAC,CAAC,OAAO7B,KAAK,EAAE;MAAAzD,cAAA,GAAAI,CAAA;MACdsD,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC,GAAE,CAACtC,MAAM,EAAEd,EAAE,EAAEC,IAAI,EAAEC,MAAM,EAAEJ,SAAS,CAAC,CAAC;EAKzC,IAAMwF,wBAAwB,IAAA3F,cAAA,GAAAI,CAAA,QAAGjB,WAAW,CAAAkE,iBAAA,CAAC,aAAY;IAAArD,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACvD,IAAI;MACF,IAAMwF,eAAe,IAAA5F,cAAA,GAAAI,CAAA,QAAG,EAAE;MAG1B,IAAMyF,iBAAiB,IAAA7F,cAAA,GAAAI,CAAA,cAASX,4BAA4B,CAACqG,8BAA8B,CAAC,CAAC;MAAC9F,cAAA,GAAAI,CAAA;MAC9FwF,eAAe,CAACG,IAAI,CAAAnC,KAAA,CAApBgC,eAAe,EAAAI,kBAAA,CAASH,iBAAiB,EAAC;MAG1C,IAAMI,aAAa,IAAAjG,cAAA,GAAAI,CAAA,cAASD,SAAS,CAACwE,OAAO,CAACmB,8BAA8B,CAAC,CAAC;MAAC9F,cAAA,GAAAI,CAAA;MAC/EwF,eAAe,CAACG,IAAI,CAAAnC,KAAA,CAApBgC,eAAe,EAAAI,kBAAA,CAASC,aAAa,EAAC;MAGtC,IAAMC,aAAa,IAAAlG,cAAA,GAAAI,CAAA,QAAGV,yBAAyB,CAACwF,0BAA0B,CAAC,CAAC;MAC5E,IAAMiB,YAAY,IAAAnG,cAAA,GAAAI,CAAA,QAAG8F,aAAa,CAACE,uBAAuB,CACvDC,MAAM,CAAC,UAAAC,GAAG,EAAI;QAAAtG,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAAA,QAAAJ,cAAA,GAAAC,CAAA,UAAAqG,GAAG,CAACC,cAAc,KAAK,OAAO,MAAAvG,cAAA,GAAAC,CAAA,UAAIqG,GAAG,CAACC,cAAc,KAAK,OAAO;MAAD,CAAC,CAAC,CAC/EC,GAAG,CAAC,UAAAF,GAAG,EAAK;QAAAtG,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAAA;UACXqG,QAAQ,EAAE,qBAAqB;UAC/BF,cAAc,EAAE,GAAGD,GAAG,CAACC,cAAc,CAACG,WAAW,CAAC,CAAC,KAAKJ,GAAG,CAACK,UAAU,EAAE;UACxEC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,QAAQ;UAChBC,mBAAmB,EAAE;QACvB,CAAC;MAAD,CAAE,CAAC;MAAC9G,cAAA,GAAAI,CAAA;MACNwF,eAAe,CAACG,IAAI,CAAAnC,KAAA,CAApBgC,eAAe,EAAAI,kBAAA,CAASG,YAAY,EAAC;MAACnG,cAAA,GAAAI,CAAA;MAEtC,OAAOwF,eAAe;IACxB,CAAC,CAAC,OAAOnC,KAAK,EAAE;MAAAzD,cAAA,GAAAI,CAAA;MACdsD,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAACzD,cAAA,GAAAI,CAAA;MAC9D,OAAO,EAAE;IACX;EACF,CAAC,GAAE,CAACD,SAAS,CAACwE,OAAO,CAAC,CAAC;EAKvB,IAAMoC,mBAAmB,IAAA/G,cAAA,GAAAI,CAAA,QAAGjB,WAAW;IAAA,IAAA6H,KAAA,GAAA3D,iBAAA,CAAC,WAAO4D,aAAqB,EAAK;MAAAjH,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MACvE,IAAI;QAAAJ,cAAA,GAAAI,CAAA;QAEFsD,OAAO,CAAC4B,GAAG,CAAC,2BAA2B2B,aAAa,EAAE,CAAC;QAACjH,cAAA,GAAAI,CAAA;QACxD,OAAO,IAAI;MACb,CAAC,CAAC,OAAOqD,KAAK,EAAE;QAAAzD,cAAA,GAAAI,CAAA;QACdsD,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QAACzD,cAAA,GAAAI,CAAA;QACxD,OAAO,KAAK;MACd;IACF,CAAC;IAAA,iBAAA8G,GAAA;MAAA,OAAAF,KAAA,CAAApD,KAAA,OAAA/D,SAAA;IAAA;EAAA,KAAE,EAAE,CAAC;EAKN,IAAMsH,qBAAqB,IAAAnH,cAAA,GAAAI,CAAA,QAAGjB,WAAW,CAAC,YAAM;IAAAa,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC9C,OAAOV,yBAAyB,CAACyH,qBAAqB,CAAC,CAAC;EAC1D,CAAC,EAAE,EAAE,CAAC;EAKN,IAAMC,kBAAkB,IAAApH,cAAA,GAAAI,CAAA,QAAGjB,WAAW,CAAC,UAACkI,SAAiB,EAAK;IAAArH,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC5D2C,QAAQ,CAAC,UAAAG,IAAI,EAAK;MAAAlD,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAAK,MAAA,CAAAC,MAAA,KACbwC,IAAI;QACPN,QAAQ,EAAEM,IAAI,CAACN,QAAQ,CAACyD,MAAM,CAAC,UAAAiB,OAAO,EAAI;UAAAtH,cAAA,GAAAE,CAAA;UAAAF,cAAA,GAAAI,CAAA;UAAA,OAAAkH,OAAO,CAACb,QAAQ,KAAKY,SAAS;QAAD,CAAC;MAAC;IAC3E,CAAE,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAKN,IAAME,wBAAwB,IAAAvH,cAAA,GAAAI,CAAA,QAAGjB,WAAW,CAAAkE,iBAAA,CAAC,aAAY;IAAArD,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACvD,IAAI;MAEF,IAAMoB,YAAY,IAAAxB,cAAA,GAAAI,CAAA,QAAG;QACnBqB,OAAO,EAAEtB,SAAS,CAACqH,OAAO,CAACC,WAAW;QACtC/F,MAAM,EAAE;UACNgG,MAAM,EAAEvH,SAAS,CAAC2C,KAAK,CAAC6E,WAAW,CAACD,MAAM,CAACE,MAAM;UACjDC,MAAM,EAAE1H,SAAS,CAAC2C,KAAK,CAAC6E,WAAW,CAACE,MAAM,CAACD,MAAM;UACjDE,OAAO,EAAE3H,SAAS,CAAC2C,KAAK,CAAC6E,WAAW,CAACG,OAAO,CAACF,MAAM;UACnDG,OAAO,EAAE5H,SAAS,CAAC2C,KAAK,CAAC6E,WAAW,CAACI,OAAO,CAACH,MAAM;UACnDI,OAAO,EAAE7H,SAAS,CAAC2C,KAAK,CAAC6E,WAAW,CAACK,OAAO,CAACJ;QAC/C,CAAC;QACDjG,MAAM,EAAExB,SAAS,CAACqH,OAAO,CAACS,YAAY;QACtCrG,cAAc,EAAEzB,SAAS,CAAC2C,KAAK,CAACnB,MAAM,CAAC0E,MAAM,CAAC,UAAA6B,KAAK,EAAI;UAAAlI,cAAA,GAAAE,CAAA;UAAAF,cAAA,GAAAI,CAAA;UAAA,OAAA8H,KAAK,CAACC,QAAQ,KAAK,UAAU;QAAD,CAAC,CAAC,CAACrI;MACxF,CAAC;MAGD,IAAM+B,YAAY,IAAA7B,cAAA,GAAAI,CAAA,QAAG;QACnB0B,gBAAgB,EAAE3B,SAAS,CAACqH,OAAO,CAAC1F,gBAAgB;QACpDC,mBAAmB,EAAE,CAAC;QACtBC,gBAAgB,EAAE7B,SAAS,CAACqH,OAAO,CAACY,YAAY;QAChDnG,UAAU,EAAEoG,IAAI,CAACC,KAAK,CAAEnI,SAAS,CAACqH,OAAO,CAACY,YAAY,GAAG,GAAG,GAAI,GAAG;MACrE,CAAC;MAGD,IAAMvD,sBAAsB,IAAA7E,cAAA,GAAAI,CAAA,QAAGX,4BAA4B,CAACqF,yBAAyB,CAAC,CAAC;MACvF,IAAM5C,aAAa,IAAAlC,cAAA,GAAAI,CAAA,QAAG;QACpB+B,aAAa,EAAE0C,sBAAsB,CAAC1C,aAAa;QACnDC,iBAAiB,EAAE3B,MAAM,CAAC8H,IAAI,CAAC1D,sBAAsB,CAAC2D,kBAAkB,CAAC,CAAC1I,MAAM;QAChFuC,cAAc,EAAE5B,MAAM,CAACgI,MAAM,CAAC5D,sBAAsB,CAAC6D,aAAa,CAAC,CAChErC,MAAM,CAAC,UAACsC,IAAS,EAAK;UAAA3I,cAAA,GAAAE,CAAA;UAAAF,cAAA,GAAAI,CAAA;UAAA,OAAAuI,IAAI,CAACC,MAAM,KAAK,SAAS;QAAD,CAAC,CAAC,CAAC9I,MAAM;QAC1DwC,kBAAkB,EAAE;MACtB,CAAC;MAGD,IAAMyC,OAAO,IAAA/E,cAAA,GAAAI,CAAA,QAAGV,yBAAyB,CAACsF,mBAAmB,CAAC,CAAC;MAC/D,IAAM6D,kBAAkB,IAAA7I,cAAA,GAAAI,CAAA,QAAGV,yBAAyB,CAACyH,qBAAqB,CAAC,CAAC;MAC5E,IAAM5E,QAAQ,IAAAvC,cAAA,GAAAI,CAAA,QAAG;QACfoC,aAAa,EAAEuC,OAAO,CAACrD,MAAM,CAAC5B,MAAM;QACpC2C,oBAAoB,EAAE/C,yBAAyB,CAACoJ,qBAAqB,CAAC,CAAC,CAACC,QAAQ,CAACjJ,MAAM;QACvF4C,kBAAkB,EAAEmG,kBAAkB,CAACG,eAAe;QACtDrG,WAAW,EAAEkG,kBAAkB,CAACI,GAAG,CAACC;MACtC,CAAC;MAGD,IAAMtG,QAAQ,IAAA5C,cAAA,GAAAI,CAAA,cAAS+I,sBAAsB,CAAC3H,YAAY,EAAEK,YAAY,EAAEK,aAAa,EAAEK,QAAQ,CAAC;MAACvC,cAAA,GAAAI,CAAA;MAEnG2C,QAAQ,CAAC,UAAAG,IAAI,EAAK;QAAAlD,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAAA,OAAAK,MAAA,CAAAC,MAAA,KACbwC,IAAI;UACP1B,YAAY,EAAZA,YAAY;UACZK,YAAY,EAAZA,YAAY;UACZK,aAAa,EAAbA,aAAa;UACbK,QAAQ,EAARA,QAAQ;UACRK,QAAQ,EAARA;QAAQ;MACV,CAAE,CAAC;IAEL,CAAC,CAAC,OAAOa,KAAK,EAAE;MAAAzD,cAAA,GAAAI,CAAA;MACdsD,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF,CAAC,GAAE,CAACtD,SAAS,CAAC,CAAC;EAACH,cAAA,GAAAI,CAAA;EAGhB,IAAM+I,sBAAsB;IAAA,IAAAC,MAAA,GAAA/F,iBAAA,CAAG,WAC7B7B,YAAiB,EACjBK,YAAiB,EACjBK,aAAkB,EAClBK,QAAa,EACV;MAAAvC,cAAA,GAAAE,CAAA;MACH,IAAM0C,QAAQ,IAAA5C,cAAA,GAAAI,CAAA,QAAG,EAAE;MAACJ,cAAA,GAAAI,CAAA;MAEpB,IAAIoB,YAAY,CAACC,OAAO,GAAG,EAAE,EAAE;QAAAzB,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAC7BwC,QAAQ,CAACmD,IAAI,CAAC;UACZU,QAAQ,EAAE,eAAe;UACzB4C,OAAO,EAAE,2CAA2C;UACpDC,QAAQ,EAAE,KAAc;UACxBC,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC;QAAAvJ,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAED,IAAIyB,YAAY,CAACC,gBAAgB,GAAG,GAAG,EAAE;QAAA9B,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QACvCwC,QAAQ,CAACmD,IAAI,CAAC;UACZU,QAAQ,EAAE,aAAa;UACvB4C,OAAO,EAAE,0DAA0D;UACnEC,QAAQ,EAAE,QAAiB;UAC3BC,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC;QAAAvJ,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAED,IAAI8B,aAAa,CAACG,cAAc,GAAG,CAAC,EAAE;QAAArC,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QACpCwC,QAAQ,CAACmD,IAAI,CAAC;UACZU,QAAQ,EAAE,eAAe;UACzB4C,OAAO,EAAE,GAAGnH,aAAa,CAACG,cAAc,8BAA8B;UACtEiH,QAAQ,EAAE,QAAiB;UAC3BC,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC;QAAAvJ,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAED,IAAImC,QAAQ,CAACI,WAAW,GAAG,GAAG,EAAE;QAAA3C,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAC9BwC,QAAQ,CAACmD,IAAI,CAAC;UACZU,QAAQ,EAAE,UAAU;UACpB4C,OAAO,EAAE,sDAAsD;UAC/DC,QAAQ,EAAE,MAAe;UACzBC,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC;QAAAvJ,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAED,OAAOwC,QAAQ;IACjB,CAAC;IAAA,gBA7CKuG,sBAAsBA,CAAAK,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;MAAA,OAAAP,MAAA,CAAAxF,KAAA,OAAA/D,SAAA;IAAA;EAAA,GA6C3B;EAACG,cAAA,GAAAI,CAAA;EAGFlB,SAAS,CAAC,YAAM;IAAAc,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACd,IAAI,CAACe,MAAM,CAACR,wBAAwB,EAAE;MAAAX,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAE7C,IAAM2J,QAAQ,IAAA5J,cAAA,GAAAI,CAAA,SAAGyJ,WAAW,CAAC,YAAM;MAAA7J,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MACjCmH,wBAAwB,CAAC,CAAC;IAC5B,CAAC,EAAEpG,MAAM,CAACH,cAAc,CAAC;IAAChB,cAAA,GAAAI,CAAA;IAE1B,OAAO,YAAM;MAAAJ,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAA0J,aAAa,CAACF,QAAQ,CAAC;IAAD,CAAC;EACtC,CAAC,EAAE,CAACzI,MAAM,CAACR,wBAAwB,EAAEQ,MAAM,CAACH,cAAc,EAAEuG,wBAAwB,CAAC,CAAC;EAACvH,cAAA,GAAAI,CAAA;EAGvFlB,SAAS,CAAC,YAAM;IAAAc,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACd,IAAM2J,UAAU;MAAA,IAAAC,MAAA,GAAA3G,iBAAA,CAAG,aAAY;QAAArD,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAC7B,MAAMmH,wBAAwB,CAAC,CAAC;QAACvH,cAAA,GAAAI,CAAA;QACjC2C,QAAQ,CAAC,UAAAG,IAAI,EAAK;UAAAlD,cAAA,GAAAE,CAAA;UAAAF,cAAA,GAAAI,CAAA;UAAA,OAAAK,MAAA,CAAAC,MAAA,KAAKwC,IAAI;YAAE5B,aAAa,EAAE;UAAI;QAAC,CAAE,CAAC;MACtD,CAAC;MAAA,gBAHKyI,UAAUA,CAAA;QAAA,OAAAC,MAAA,CAAApG,KAAA,OAAA/D,SAAA;MAAA;IAAA,GAGf;IAACG,cAAA,GAAAI,CAAA;IAEF2J,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAGN,IAAMvC,OAAO,IAAAxH,cAAA,GAAAI,CAAA,SAAGhB,OAAO,CAAC,YAAM;IAAA,IAAA6K,oBAAA,EAAAC,qBAAA;IAAAlK,cAAA,GAAAE,CAAA;IAC5B,IAAMiK,aAAa,IAAAnK,cAAA,GAAAI,CAAA,SAAG0C,KAAK,CAACtB,YAAY,CAACC,OAAO,GAAG,EAAE,IAAAzB,cAAA,GAAAC,CAAA,WAAG,WAAW,KAAAD,cAAA,GAAAC,CAAA,WAC9C6C,KAAK,CAACtB,YAAY,CAACC,OAAO,GAAG,EAAE,IAAAzB,cAAA,GAAAC,CAAA,WAAG,MAAM,KAAAD,cAAA,GAAAC,CAAA,WACxC6C,KAAK,CAACtB,YAAY,CAACC,OAAO,GAAG,EAAE,IAAAzB,cAAA,GAAAC,CAAA,WAAG,MAAM,KAAAD,cAAA,GAAAC,CAAA,WAAG,MAAM;IAEtE,IAAMmK,gBAAgB,IAAApK,cAAA,GAAAI,CAAA,SAAG0C,KAAK,CAACjB,YAAY,CAACG,gBAAgB,GAAG,GAAG,IAAAhC,cAAA,GAAAC,CAAA,WAAG,IAAI,KAAAD,cAAA,GAAAC,CAAA,WACjD6C,KAAK,CAACjB,YAAY,CAACG,gBAAgB,GAAG,GAAG,IAAAhC,cAAA,GAAAC,CAAA,WAAG,GAAG,KAAAD,cAAA,GAAAC,CAAA,WAC/C6C,KAAK,CAACjB,YAAY,CAACG,gBAAgB,GAAG,GAAG,IAAAhC,cAAA,GAAAC,CAAA,WAAG,IAAI,KAAAD,cAAA,GAAAC,CAAA,WAChD6C,KAAK,CAACjB,YAAY,CAACG,gBAAgB,GAAG,GAAG,IAAAhC,cAAA,GAAAC,CAAA,WAAG,GAAG,KAAAD,cAAA,GAAAC,CAAA,WAAG,GAAG;IAE7E,IAAMoK,iBAAiB,IAAArK,cAAA,GAAAI,CAAA,SAAG0C,KAAK,CAACjB,YAAY,CAACI,UAAU,GAAG,EAAE,IAAAjC,cAAA,GAAAC,CAAA,WAAG,SAAS,KAAAD,cAAA,GAAAC,CAAA,WAC/C6C,KAAK,CAACjB,YAAY,CAACI,UAAU,GAAG,EAAE,IAAAjC,cAAA,GAAAC,CAAA,WAAG,MAAM,KAAAD,cAAA,GAAAC,CAAA,WAC3C6C,KAAK,CAACjB,YAAY,CAACI,UAAU,GAAG,EAAE,IAAAjC,cAAA,GAAAC,CAAA,WAAG,QAAQ,KAAAD,cAAA,GAAAC,CAAA,WAAG,KAAK;IAE9E,IAAMqK,eAAe,IAAAtK,cAAA,GAAAI,CAAA,SAAG0C,KAAK,CAACP,QAAQ,CAACI,WAAW,GAAG,GAAG,IAAA3C,cAAA,GAAAC,CAAA,WAAG,WAAW,KAAAD,cAAA,GAAAC,CAAA,WAC/C6C,KAAK,CAACP,QAAQ,CAACI,WAAW,GAAG,GAAG,IAAA3C,cAAA,GAAAC,CAAA,WAAG,MAAM,KAAAD,cAAA,GAAAC,CAAA,WACzC6C,KAAK,CAACP,QAAQ,CAACI,WAAW,GAAG,GAAG,IAAA3C,cAAA,GAAAC,CAAA,WAAG,MAAM,KAAAD,cAAA,GAAAC,CAAA,WAAG,MAAM;IAEzE,IAAMsK,WAAW,IAAAvK,cAAA,GAAAI,CAAA,SAAG,CAAAJ,cAAA,GAAAC,CAAA,YAAAgK,oBAAA,GAAAnH,KAAK,CAACF,QAAQ,CAAC4H,IAAI,CAAC,UAAAlD,OAAO,EAAI;MAAAtH,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAAkH,OAAO,CAACgC,QAAQ,KAAK,UAAU;IAAD,CAAC,CAAC,qBAA/DW,oBAAA,CAAiEZ,OAAO,MAAArJ,cAAA,GAAAC,CAAA,YAAAiK,qBAAA,GACzEpH,KAAK,CAACF,QAAQ,CAAC4H,IAAI,CAAC,UAAAlD,OAAO,EAAI;MAAAtH,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAAkH,OAAO,CAACgC,QAAQ,KAAK,MAAM;IAAD,CAAC,CAAC,qBAA3DY,qBAAA,CAA6Db,OAAO,MAAArJ,cAAA,GAAAC,CAAA,WACpE,+BAA+B;IAACD,cAAA,GAAAI,CAAA;IAEnD,OAAO;MACL+J,aAAa,EAAbA,aAAa;MACbC,gBAAgB,EAAhBA,gBAAgB;MAChBC,iBAAiB,EAAjBA,iBAAiB;MACjBC,eAAe,EAAfA,eAAe;MACfC,WAAW,EAAXA;IACF,CAAC;EACH,CAAC,EAAE,CAACzH,KAAK,CAAC,CAAC;EAAC9C,cAAA,GAAAI,CAAA;EAEZ,OAAOhB,OAAO,CAAC,YAAO;IAAAY,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAAA;MACpB0C,KAAK,EAALA,KAAK;MACL3C,SAAS,EAATA,SAAS;MACTE,EAAE,EAAFA,EAAE;MACFC,IAAI,EAAJA,IAAI;MACJC,MAAM,EAANA,MAAM;MACNoE,OAAO,EAAE;QACP3B,UAAU,EAAVA,UAAU;QACVG,kBAAkB,EAAlBA,kBAAkB;QAClBU,WAAW,EAAXA,WAAW;QACXO,kBAAkB,EAAlBA,kBAAkB;QAClBiB,kBAAkB,EAAlBA,kBAAkB;QAClBM,wBAAwB,EAAxBA,wBAAwB;QACxBoB,mBAAmB,EAAnBA,mBAAmB;QACnBI,qBAAqB,EAArBA,qBAAqB;QACrBC,kBAAkB,EAAlBA;MACF,CAAC;MACDjG,MAAM,EAANA,MAAM;MACNqG,OAAO,EAAPA;IACF,CAAC;EAAD,CAAE,EAAE,CACF1E,KAAK,EACL3C,SAAS,EACTE,EAAE,EACFC,IAAI,EACJC,MAAM,EACNyC,UAAU,EACVG,kBAAkB,EAClBU,WAAW,EACXO,kBAAkB,EAClBiB,kBAAkB,EAClBM,wBAAwB,EACxBoB,mBAAmB,EACnBI,qBAAqB,EACrBC,kBAAkB,EAClBjG,MAAM,EACNqG,OAAO,CACR,CAAC;AACJ;AAEA,eAAe7H,4BAA4B", "ignoreList": []}