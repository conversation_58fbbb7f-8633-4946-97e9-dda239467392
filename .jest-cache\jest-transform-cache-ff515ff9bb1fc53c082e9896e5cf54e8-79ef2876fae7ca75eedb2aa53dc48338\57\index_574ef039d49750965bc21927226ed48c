23183b920fc492bfbcaa8f88d407e563
"use strict";

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var Share = function () {
  function Share() {
    (0, _classCallCheck2.default)(this, Share);
  }
  return (0, _createClass2.default)(Share, null, [{
    key: "share",
    value: function share(content, options) {
      if (options === void 0) {
        options = {};
      }
      (0, _invariant.default)(typeof content === 'object' && content !== null, 'Content to share must be a valid object');
      (0, _invariant.default)(typeof content.url === 'string' || typeof content.message === 'string', 'At least one of URL and message is required');
      (0, _invariant.default)(typeof options === 'object' && options !== null, 'Options must be a valid object');
      (0, _invariant.default)(!content.title || typeof content.title === 'string', 'Invalid title: title should be a string.');
      if (window.navigator.share !== undefined) {
        return window.navigator.share({
          title: content.title,
          text: content.message,
          url: content.url
        });
      } else {
        return Promise.reject(new Error('Share is not supported in this browser'));
      }
    }
  }, {
    key: "sharedAction",
    get: function get() {
      return 'sharedAction';
    }
  }, {
    key: "dismissedAction",
    get: function get() {
      return 'dismissedAction';
    }
  }]);
}();
var _default = exports.default = Share;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0MiIsInJlcXVpcmUiLCJfY2xhc3NDYWxsQ2hlY2syIiwiX2NyZWF0ZUNsYXNzMiIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJkZWZhdWx0IiwiZXhwb3J0cyIsIl9fZXNNb2R1bGUiLCJfaW52YXJpYW50IiwiU2hhcmUiLCJrZXkiLCJ2YWx1ZSIsInNoYXJlIiwiY29udGVudCIsIm9wdGlvbnMiLCJ1cmwiLCJtZXNzYWdlIiwidGl0bGUiLCJ3aW5kb3ciLCJuYXZpZ2F0b3IiLCJ1bmRlZmluZWQiLCJ0ZXh0IiwiUHJvbWlzZSIsInJlamVjdCIsIkVycm9yIiwiZ2V0IiwiX2RlZmF1bHQiLCJtb2R1bGUiXSwic291cmNlcyI6WyJpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHRcIikuZGVmYXVsdDtcbmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWU7XG5leHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7XG52YXIgX2ludmFyaWFudCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcImZianMvbGliL2ludmFyaWFudFwiKSk7XG4vKipcbiAqIENvcHlyaWdodCAoYykgTmljb2xhcyBHYWxsYWdoZXIuXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqIFxuICovXG5cbmNsYXNzIFNoYXJlIHtcbiAgc3RhdGljIHNoYXJlKGNvbnRlbnQsIG9wdGlvbnMpIHtcbiAgICBpZiAob3B0aW9ucyA9PT0gdm9pZCAwKSB7XG4gICAgICBvcHRpb25zID0ge307XG4gICAgfVxuICAgICgwLCBfaW52YXJpYW50LmRlZmF1bHQpKHR5cGVvZiBjb250ZW50ID09PSAnb2JqZWN0JyAmJiBjb250ZW50ICE9PSBudWxsLCAnQ29udGVudCB0byBzaGFyZSBtdXN0IGJlIGEgdmFsaWQgb2JqZWN0Jyk7XG4gICAgKDAsIF9pbnZhcmlhbnQuZGVmYXVsdCkodHlwZW9mIGNvbnRlbnQudXJsID09PSAnc3RyaW5nJyB8fCB0eXBlb2YgY29udGVudC5tZXNzYWdlID09PSAnc3RyaW5nJywgJ0F0IGxlYXN0IG9uZSBvZiBVUkwgYW5kIG1lc3NhZ2UgaXMgcmVxdWlyZWQnKTtcbiAgICAoMCwgX2ludmFyaWFudC5kZWZhdWx0KSh0eXBlb2Ygb3B0aW9ucyA9PT0gJ29iamVjdCcgJiYgb3B0aW9ucyAhPT0gbnVsbCwgJ09wdGlvbnMgbXVzdCBiZSBhIHZhbGlkIG9iamVjdCcpO1xuICAgICgwLCBfaW52YXJpYW50LmRlZmF1bHQpKCFjb250ZW50LnRpdGxlIHx8IHR5cGVvZiBjb250ZW50LnRpdGxlID09PSAnc3RyaW5nJywgJ0ludmFsaWQgdGl0bGU6IHRpdGxlIHNob3VsZCBiZSBhIHN0cmluZy4nKTtcbiAgICBpZiAod2luZG93Lm5hdmlnYXRvci5zaGFyZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICByZXR1cm4gd2luZG93Lm5hdmlnYXRvci5zaGFyZSh7XG4gICAgICAgIHRpdGxlOiBjb250ZW50LnRpdGxlLFxuICAgICAgICB0ZXh0OiBjb250ZW50Lm1lc3NhZ2UsXG4gICAgICAgIHVybDogY29udGVudC51cmxcbiAgICAgIH0pO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QobmV3IEVycm9yKCdTaGFyZSBpcyBub3Qgc3VwcG9ydGVkIGluIHRoaXMgYnJvd3NlcicpKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogVGhlIGNvbnRlbnQgd2FzIHN1Y2Nlc3NmdWxseSBzaGFyZWQuXG4gICAqL1xuICBzdGF0aWMgZ2V0IHNoYXJlZEFjdGlvbigpIHtcbiAgICByZXR1cm4gJ3NoYXJlZEFjdGlvbic7XG4gIH1cblxuICAvKipcbiAgICogVGhlIGRpYWxvZyBoYXMgYmVlbiBkaXNtaXNzZWQuXG4gICAqIEBwbGF0Zm9ybSBpb3NcbiAgICovXG4gIHN0YXRpYyBnZXQgZGlzbWlzc2VkQWN0aW9uKCkge1xuICAgIHJldHVybiAnZGlzbWlzc2VkQWN0aW9uJztcbiAgfVxufVxudmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gU2hhcmU7XG5tb2R1bGUuZXhwb3J0cyA9IGV4cG9ydHMuZGVmYXVsdDsiXSwibWFwcGluZ3MiOiJBQUFBLFlBQVk7O0FBQUMsSUFBQUEsdUJBQUEsR0FBQUMsT0FBQTtBQUFBLElBQUFDLGdCQUFBLEdBQUFGLHVCQUFBLENBQUFDLE9BQUE7QUFBQSxJQUFBRSxhQUFBLEdBQUFILHVCQUFBLENBQUFDLE9BQUE7QUFFYixJQUFJRyxzQkFBc0IsR0FBR0gsT0FBTyxDQUFDLDhDQUE4QyxDQUFDLENBQUNJLE9BQU87QUFDNUZDLE9BQU8sQ0FBQ0MsVUFBVSxHQUFHLElBQUk7QUFDekJELE9BQU8sQ0FBQ0QsT0FBTyxHQUFHLEtBQUssQ0FBQztBQUN4QixJQUFJRyxVQUFVLEdBQUdKLHNCQUFzQixDQUFDSCxPQUFPLENBQUMsb0JBQW9CLENBQUMsQ0FBQztBQUFDLElBV2pFUSxLQUFLO0VBQUEsU0FBQUEsTUFBQTtJQUFBLElBQUFQLGdCQUFBLENBQUFHLE9BQUEsUUFBQUksS0FBQTtFQUFBO0VBQUEsV0FBQU4sYUFBQSxDQUFBRSxPQUFBLEVBQUFJLEtBQUE7SUFBQUMsR0FBQTtJQUFBQyxLQUFBLEVBQ1QsU0FBT0MsS0FBS0EsQ0FBQ0MsT0FBTyxFQUFFQyxPQUFPLEVBQUU7TUFDN0IsSUFBSUEsT0FBTyxLQUFLLEtBQUssQ0FBQyxFQUFFO1FBQ3RCQSxPQUFPLEdBQUcsQ0FBQyxDQUFDO01BQ2Q7TUFDQSxDQUFDLENBQUMsRUFBRU4sVUFBVSxDQUFDSCxPQUFPLEVBQUUsT0FBT1EsT0FBTyxLQUFLLFFBQVEsSUFBSUEsT0FBTyxLQUFLLElBQUksRUFBRSx5Q0FBeUMsQ0FBQztNQUNuSCxDQUFDLENBQUMsRUFBRUwsVUFBVSxDQUFDSCxPQUFPLEVBQUUsT0FBT1EsT0FBTyxDQUFDRSxHQUFHLEtBQUssUUFBUSxJQUFJLE9BQU9GLE9BQU8sQ0FBQ0csT0FBTyxLQUFLLFFBQVEsRUFBRSw2Q0FBNkMsQ0FBQztNQUM5SSxDQUFDLENBQUMsRUFBRVIsVUFBVSxDQUFDSCxPQUFPLEVBQUUsT0FBT1MsT0FBTyxLQUFLLFFBQVEsSUFBSUEsT0FBTyxLQUFLLElBQUksRUFBRSxnQ0FBZ0MsQ0FBQztNQUMxRyxDQUFDLENBQUMsRUFBRU4sVUFBVSxDQUFDSCxPQUFPLEVBQUUsQ0FBQ1EsT0FBTyxDQUFDSSxLQUFLLElBQUksT0FBT0osT0FBTyxDQUFDSSxLQUFLLEtBQUssUUFBUSxFQUFFLDBDQUEwQyxDQUFDO01BQ3hILElBQUlDLE1BQU0sQ0FBQ0MsU0FBUyxDQUFDUCxLQUFLLEtBQUtRLFNBQVMsRUFBRTtRQUN4QyxPQUFPRixNQUFNLENBQUNDLFNBQVMsQ0FBQ1AsS0FBSyxDQUFDO1VBQzVCSyxLQUFLLEVBQUVKLE9BQU8sQ0FBQ0ksS0FBSztVQUNwQkksSUFBSSxFQUFFUixPQUFPLENBQUNHLE9BQU87VUFDckJELEdBQUcsRUFBRUYsT0FBTyxDQUFDRTtRQUNmLENBQUMsQ0FBQztNQUNKLENBQUMsTUFBTTtRQUNMLE9BQU9PLE9BQU8sQ0FBQ0MsTUFBTSxDQUFDLElBQUlDLEtBQUssQ0FBQyx3Q0FBd0MsQ0FBQyxDQUFDO01BQzVFO0lBQ0Y7RUFBQztJQUFBZCxHQUFBO0lBQUFlLEdBQUEsRUFLRCxTQUFBQSxJQUFBLEVBQTBCO01BQ3hCLE9BQU8sY0FBYztJQUN2QjtFQUFDO0lBQUFmLEdBQUE7SUFBQWUsR0FBQSxFQU1ELFNBQUFBLElBQUEsRUFBNkI7TUFDM0IsT0FBTyxpQkFBaUI7SUFDMUI7RUFBQztBQUFBO0FBRUgsSUFBSUMsUUFBUSxHQUFHcEIsT0FBTyxDQUFDRCxPQUFPLEdBQUdJLEtBQUs7QUFDdENrQixNQUFNLENBQUNyQixPQUFPLEdBQUdBLE9BQU8sQ0FBQ0QsT0FBTyIsImlnbm9yZUxpc3QiOltdfQ==