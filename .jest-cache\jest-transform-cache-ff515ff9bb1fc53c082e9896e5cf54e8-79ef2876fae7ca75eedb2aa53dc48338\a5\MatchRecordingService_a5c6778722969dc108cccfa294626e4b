e4fd1f0a42e7d5cdddfd17f0262acc6c
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.matchRecordingService = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _VideoRecordingService = require("../video/VideoRecordingService");
var _MatchRepository = require("../database/MatchRepository");
var _FileUploadService = require("../storage/FileUploadService");
var _performance = require("../../../utils/performance");
var MatchRecordingService = function () {
  function MatchRecordingService() {
    (0, _classCallCheck2.default)(this, MatchRecordingService);
    this.currentSession = null;
    this.sessionListeners = [];
    this.scoreListeners = [];
    this.offlineSyncQueue = null;
    this.syncInterval = null;
    this.autoSaveInterval = null;
  }
  return (0, _createClass2.default)(MatchRecordingService, [{
    key: "startMatch",
    value: (function () {
      var _startMatch = (0, _asyncToGenerator2.default)(function* (metadata, options) {
        try {
          _performance.performanceMonitor.start('match_recording_start');
          this.validateMatchMetadata(metadata);
          if (this.currentSession) {
            throw new Error('Another match recording is already in progress');
          }
          var matchRecording = {
            id: `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            metadata: Object.assign({}, metadata, {
              startTime: new Date().toISOString()
            }),
            score: this.initializeScore(metadata.matchFormat),
            statistics: this.initializeStatistics(metadata.userId),
            status: 'recording',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          var session = {
            id: this.generateSessionId(),
            match: matchRecording,
            currentSet: 1,
            currentGame: 1,
            isRecording: true,
            isPaused: false,
            startTime: Date.now(),
            pausedTime: 0,
            totalPausedDuration: 0,
            videoRecordingActive: options.enableVideoRecording,
            autoScoreDetection: options.enableAutoScoreDetection
          };
          if (options.enableVideoRecording) {
            yield _VideoRecordingService.videoRecordingService.startRecording(options.videoConfig);
          }
          var savedMatch = yield this.saveMatchToDatabase(matchRecording);
          if (!savedMatch.success) {
            throw new Error(savedMatch.error || 'Failed to save match to database');
          }
          session.match.id = savedMatch.data.id;
          session.match.databaseId = savedMatch.data.databaseId;
          this.setupOfflineSync(session.match.id);
          this.currentSession = session;
          this.notifySessionListeners();
          this.startAutoSave();
          _performance.performanceMonitor.end('match_recording_start');
          return session;
        } catch (error) {
          console.error('Failed to start match recording:', error);
          if (this.currentSession) {
            yield this.cleanupFailedSession();
          }
          throw error;
        }
      });
      function startMatch(_x, _x2) {
        return _startMatch.apply(this, arguments);
      }
      return startMatch;
    }())
  }, {
    key: "addPoint",
    value: (function () {
      var _addPoint = (0, _asyncToGenerator2.default)(function* (winner) {
        var eventType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'normal';
        var shotType = arguments.length > 2 ? arguments[2] : undefined;
        var courtPosition = arguments.length > 3 ? arguments[3] : undefined;
        if (!this.currentSession) {
          throw new Error('No active match session');
        }
        try {
          var session = this.currentSession;
          var currentSet = session.currentSet;
          var currentGame = session.currentGame;
          var gameEvent = {
            id: this.generateEventId(),
            timestamp: Date.now(),
            eventType: eventType === 'normal' ? 'point_won' : eventType,
            player: winner,
            shotType: shotType,
            courtPosition: courtPosition,
            description: `Point won by ${winner}`
          };
          var updatedScore = this.updateScore(session.match.score, currentSet, currentGame, winner, gameEvent);
          this.updateStatistics(session.match.statistics, gameEvent);
          session.match.score = updatedScore;
          session.match.updatedAt = new Date().toISOString();
          var setComplete = this.isSetComplete(updatedScore.sets[currentSet - 1]);
          var matchComplete = this.isMatchComplete(updatedScore, session.match.metadata.matchFormat);
          if (setComplete && !matchComplete) {
            session.currentSet++;
            session.currentGame = 1;
          } else if (!setComplete) {
            var gameComplete = this.isGameComplete(updatedScore.sets[currentSet - 1], currentGame);
            if (gameComplete) {
              session.currentGame++;
            }
          }
          if (matchComplete) {
            yield this.endMatch();
          } else {
            yield this.updateMatchInDatabase(session.match);
          }
          this.notifyScoreListeners();
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to add point:', error);
          throw error;
        }
      });
      function addPoint(_x3) {
        return _addPoint.apply(this, arguments);
      }
      return addPoint;
    }())
  }, {
    key: "pauseMatch",
    value: (function () {
      var _pauseMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession || this.currentSession.isPaused) {
          return;
        }
        try {
          this.currentSession.isPaused = true;
          this.currentSession.pausedTime = Date.now();
          this.currentSession.match.status = 'paused';
          if (this.currentSession.videoRecordingActive) {
            yield _VideoRecordingService.videoRecordingService.pauseRecording();
          }
          yield this.updateMatchInDatabase(this.currentSession.match);
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to pause match:', error);
          throw error;
        }
      });
      function pauseMatch() {
        return _pauseMatch.apply(this, arguments);
      }
      return pauseMatch;
    }())
  }, {
    key: "resumeMatch",
    value: (function () {
      var _resumeMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession || !this.currentSession.isPaused) {
          return;
        }
        try {
          var pauseDuration = Date.now() - this.currentSession.pausedTime;
          this.currentSession.totalPausedDuration += pauseDuration;
          this.currentSession.isPaused = false;
          this.currentSession.pausedTime = 0;
          this.currentSession.match.status = 'recording';
          if (this.currentSession.videoRecordingActive) {
            yield _VideoRecordingService.videoRecordingService.resumeRecording();
          }
          yield this.updateMatchInDatabase(this.currentSession.match);
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to resume match:', error);
          throw error;
        }
      });
      function resumeMatch() {
        return _resumeMatch.apply(this, arguments);
      }
      return resumeMatch;
    }())
  }, {
    key: "endMatch",
    value: (function () {
      var _endMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession) {
          throw new Error('No active match session');
        }
        try {
          _performance.performanceMonitor.start('match_recording_end');
          var session = this.currentSession;
          var endTime = Date.now();
          var totalDuration = (endTime - session.startTime - session.totalPausedDuration) / 1000 / 60;
          session.match.metadata.endTime = new Date().toISOString();
          session.match.metadata.durationMinutes = Math.round(totalDuration);
          session.match.status = 'completed';
          if (session.videoRecordingActive) {
            var videoResult = yield _VideoRecordingService.videoRecordingService.stopRecording();
            var uploadResult = yield _FileUploadService.fileUploadService.uploadVideo(videoResult.uri, {
              folder: `matches/${session.match.id || 'temp'}`
            });
            if (uploadResult.data) {
              session.match.videoUrl = uploadResult.data.url;
              session.match.videoDurationSeconds = videoResult.duration;
              session.match.videoFileSizeBytes = uploadResult.data.size;
              if (videoResult.thumbnail) {
                var thumbnailResult = yield _FileUploadService.fileUploadService.uploadThumbnail(videoResult.uri, videoResult.thumbnail, {
                  folder: `matches/${session.match.id || 'temp'}/thumbnails`
                });
                if (thumbnailResult.data) {
                  session.match.videoThumbnailUrl = thumbnailResult.data.url;
                }
              }
            }
          }
          this.calculateFinalStatistics(session.match.statistics, session.match.score);
          yield this.updateMatchInDatabase(session.match);
          var finalMatch = Object.assign({}, session.match);
          this.currentSession = null;
          this.notifySessionListeners();
          _performance.performanceMonitor.end('match_recording_end');
          return finalMatch;
        } catch (error) {
          console.error('Failed to end match:', error);
          throw error;
        }
      });
      function endMatch() {
        return _endMatch.apply(this, arguments);
      }
      return endMatch;
    }())
  }, {
    key: "cancelMatch",
    value: (function () {
      var _cancelMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession) {
          return;
        }
        try {
          if (this.currentSession.videoRecordingActive) {
            yield _VideoRecordingService.videoRecordingService.stopRecording();
          }
          this.currentSession.match.status = 'cancelled';
          var updateData = {
            current_score: JSON.stringify(this.currentSession.match.score),
            statistics: JSON.stringify(this.currentSession.match.statistics),
            status: 'cancelled',
            updated_at: new Date().toISOString()
          };
          var result = yield _MatchRepository.matchRepository.updateMatch(this.currentSession.match.id, updateData);
          if (result.error) {
            throw new Error(result.error);
          }
          this.currentSession = null;
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to cancel match:', error);
          throw error;
        }
      });
      function cancelMatch() {
        return _cancelMatch.apply(this, arguments);
      }
      return cancelMatch;
    }())
  }, {
    key: "getCurrentSession",
    value: function getCurrentSession() {
      return this.currentSession;
    }
  }, {
    key: "addSessionListener",
    value: function addSessionListener(listener) {
      this.sessionListeners.push(listener);
    }
  }, {
    key: "removeSessionListener",
    value: function removeSessionListener(listener) {
      this.sessionListeners = this.sessionListeners.filter(function (l) {
        return l !== listener;
      });
    }
  }, {
    key: "addScoreListener",
    value: function addScoreListener(listener) {
      this.scoreListeners.push(listener);
    }
  }, {
    key: "removeScoreListener",
    value: function removeScoreListener(listener) {
      this.scoreListeners = this.scoreListeners.filter(function (l) {
        return l !== listener;
      });
    }
  }, {
    key: "validateMatchMetadata",
    value: function validateMatchMetadata(metadata) {
      var _metadata$opponentNam;
      if (!((_metadata$opponentNam = metadata.opponentName) != null && _metadata$opponentNam.trim())) {
        throw new Error('Opponent name is required');
      }
      if (!metadata.userId) {
        throw new Error('User ID is required');
      }
      if (!metadata.matchType) {
        throw new Error('Match type is required');
      }
      if (!metadata.matchFormat) {
        throw new Error('Match format is required');
      }
      if (!metadata.surface) {
        throw new Error('Court surface is required');
      }
    }
  }, {
    key: "initializeScore",
    value: function initializeScore(format) {
      var maxSets = format === 'best_of_5' ? 5 : 3;
      return {
        sets: [],
        finalScore: '',
        result: 'win',
        setsWon: 0,
        setsLost: 0
      };
    }
  }, {
    key: "initializeStatistics",
    value: function initializeStatistics(userId) {
      return {
        matchId: '',
        userId: userId,
        aces: 0,
        doubleFaults: 0,
        firstServesIn: 0,
        firstServesAttempted: 0,
        firstServePointsWon: 0,
        secondServePointsWon: 0,
        firstServeReturnPointsWon: 0,
        secondServeReturnPointsWon: 0,
        breakPointsConverted: 0,
        breakPointsFaced: 0,
        winners: 0,
        unforcedErrors: 0,
        forcedErrors: 0,
        totalPointsWon: 0,
        totalPointsPlayed: 0,
        netPointsAttempted: 0,
        netPointsWon: 0,
        forehandWinners: 0,
        backhandWinners: 0,
        forehandErrors: 0,
        backhandErrors: 0
      };
    }
  }, {
    key: "updateScore",
    value: function updateScore(currentScore, setNumber, gameNumber, winner, event) {
      var updatedScore = Object.assign({}, currentScore);
      while (updatedScore.sets.length < setNumber) {
        updatedScore.sets.push({
          setNumber: updatedScore.sets.length + 1,
          userGames: 0,
          opponentGames: 0,
          isTiebreak: false,
          isCompleted: false
        });
      }
      var currentSet = updatedScore.sets[setNumber - 1];
      if (winner === 'user') {} else {}
      return updatedScore;
    }
  }, {
    key: "updateStatistics",
    value: function updateStatistics(statistics, event) {
      statistics.totalPointsPlayed++;
      if (event.player === 'user') {
        statistics.totalPointsWon++;
      }
      switch (event.eventType) {
        case 'ace':
          statistics.aces++;
          break;
        case 'double_fault':
          statistics.doubleFaults++;
          break;
        case 'winner':
          statistics.winners++;
          break;
        case 'unforced_error':
          statistics.unforcedErrors++;
          break;
        case 'forced_error':
          statistics.forcedErrors++;
          break;
      }
    }
  }, {
    key: "calculateFinalStatistics",
    value: function calculateFinalStatistics(statistics, score) {
      if (statistics.firstServesAttempted > 0) {
        statistics.firstServePercentage = statistics.firstServesIn / statistics.firstServesAttempted * 100;
      }
      if (statistics.breakPointsFaced > 0) {
        statistics.breakPointConversionRate = statistics.breakPointsConverted / statistics.breakPointsFaced * 100;
      }
      if (statistics.netPointsAttempted > 0) {
        statistics.netSuccessRate = statistics.netPointsWon / statistics.netPointsAttempted * 100;
      }
    }
  }, {
    key: "isSetComplete",
    value: function isSetComplete(set) {
      return set.userGames >= 6 && set.userGames - set.opponentGames >= 2 || set.opponentGames >= 6 && set.opponentGames - set.userGames >= 2 || set.isTiebreak;
    }
  }, {
    key: "isGameComplete",
    value: function isGameComplete(set, gameNumber) {
      return true;
    }
  }, {
    key: "isMatchComplete",
    value: function isMatchComplete(score, format) {
      var setsToWin = format === 'best_of_5' ? 3 : 2;
      return score.setsWon >= setsToWin || score.setsLost >= setsToWin;
    }
  }, {
    key: "saveMatchToDatabase",
    value: function () {
      var _saveMatchToDatabase = (0, _asyncToGenerator2.default)(function* (match) {
        try {
          var matchData = {
            id: match.id,
            user_id: match.metadata.userId,
            opponent_name: match.metadata.opponentName,
            match_type: match.metadata.matchType || 'friendly',
            match_format: match.metadata.matchFormat,
            surface: match.metadata.surface,
            location: match.metadata.location,
            court_name: match.metadata.courtName,
            weather_conditions: match.metadata.weather,
            temperature: match.metadata.temperature,
            match_date: new Date(match.metadata.startTime).toISOString().split('T')[0],
            start_time: new Date(match.metadata.startTime).toTimeString().split(' ')[0],
            status: match.status,
            current_score: JSON.stringify(match.score),
            statistics: JSON.stringify(match.statistics),
            created_at: match.createdAt,
            updated_at: match.updatedAt
          };
          var attempts = 0;
          var maxAttempts = 3;
          while (attempts < maxAttempts) {
            try {
              var _result$data;
              var result = yield _MatchRepository.matchRepository.createMatch(matchData);
              if (result.error) {
                if (attempts === maxAttempts - 1) {
                  return {
                    success: false,
                    error: result.error
                  };
                }
                attempts++;
                yield new Promise(function (resolve) {
                  return setTimeout(resolve, 1000 * attempts);
                });
                continue;
              }
              return {
                success: true,
                data: {
                  id: match.id,
                  databaseId: (_result$data = result.data) == null ? void 0 : _result$data.id
                }
              };
            } catch (error) {
              attempts++;
              if (attempts === maxAttempts) {
                throw error;
              }
              yield new Promise(function (resolve) {
                return setTimeout(resolve, 1000 * attempts);
              });
            }
          }
          return {
            success: false,
            error: 'Failed to save after multiple attempts'
          };
        } catch (error) {
          console.error('Error saving match to database:', error);
          return {
            success: false,
            error: 'Database connection failed'
          };
        }
      });
      function saveMatchToDatabase(_x4) {
        return _saveMatchToDatabase.apply(this, arguments);
      }
      return saveMatchToDatabase;
    }()
  }, {
    key: "updateMatchInDatabase",
    value: function () {
      var _updateMatchInDatabase = (0, _asyncToGenerator2.default)(function* (match) {
        try {
          if (!match.id) {
            return {
              success: false,
              error: 'Match ID is required for update'
            };
          }
          var updateData = {
            current_score: JSON.stringify(match.score),
            statistics: JSON.stringify(match.statistics),
            status: match.status,
            updated_at: new Date().toISOString()
          };
          if (match.status === 'completed' && match.metadata.endTime) {
            updateData.end_time = new Date(match.metadata.endTime).toTimeString().split(' ')[0];
            updateData.duration_minutes = Math.round((new Date(match.metadata.endTime).getTime() - new Date(match.metadata.startTime).getTime()) / (1000 * 60));
            updateData.final_score = this.generateFinalScoreString(match.score);
            updateData.result = this.determineMatchResult(match.score, match.metadata.userId);
            updateData.sets_won = match.score.setsWon;
            updateData.sets_lost = match.score.setsLost;
          }
          var result = yield _MatchRepository.matchRepository.updateMatch(match.id, updateData);
          if (result.error) {
            return {
              success: false,
              error: result.error
            };
          }
          return {
            success: true
          };
        } catch (error) {
          console.error('Error updating match in database:', error);
          return {
            success: false,
            error: 'Database connection failed'
          };
        }
      });
      function updateMatchInDatabase(_x5) {
        return _updateMatchInDatabase.apply(this, arguments);
      }
      return updateMatchInDatabase;
    }()
  }, {
    key: "generateFinalScoreString",
    value: function generateFinalScoreString(score) {
      if (!score.sets || score.sets.length === 0) {
        return '0-0';
      }
      return score.sets.map(function (set) {
        return `${set.userGames}-${set.opponentGames}`;
      }).join(', ');
    }
  }, {
    key: "determineMatchResult",
    value: function determineMatchResult(score, userId) {
      if (score.setsWon > score.setsLost) {
        return 'win';
      } else if (score.setsLost > score.setsWon) {
        return 'loss';
      }
      return 'draw';
    }
  }, {
    key: "generateSessionId",
    value: function generateSessionId() {
      return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "generateEventId",
    value: function generateEventId() {
      return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "notifySessionListeners",
    value: function notifySessionListeners() {
      var _this = this;
      this.sessionListeners.forEach(function (listener) {
        return listener(_this.currentSession);
      });
    }
  }, {
    key: "notifyScoreListeners",
    value: function notifyScoreListeners() {
      var _this2 = this;
      if (this.currentSession) {
        this.scoreListeners.forEach(function (listener) {
          return listener(_this2.currentSession.match.score);
        });
      }
    }
  }, {
    key: "setupOfflineSync",
    value: function setupOfflineSync(matchId) {
      try {
        if (!this.offlineSyncQueue) {
          this.offlineSyncQueue = new Map();
        }
        this.offlineSyncQueue.set(matchId, []);
        this.startOfflineSync(matchId);
      } catch (error) {
        console.error('Failed to setup offline sync:', error);
      }
    }
  }, {
    key: "startOfflineSync",
    value: function startOfflineSync(matchId) {
      var _this3 = this;
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
      }
      this.syncInterval = setInterval((0, _asyncToGenerator2.default)(function* () {
        yield _this3.syncOfflineData(matchId);
      }), 30000);
    }
  }, {
    key: "syncOfflineData",
    value: (function () {
      var _syncOfflineData = (0, _asyncToGenerator2.default)(function* (matchId) {
        try {
          var _this$offlineSyncQueu, _this$offlineSyncQueu2;
          var queue = (_this$offlineSyncQueu = this.offlineSyncQueue) == null ? void 0 : _this$offlineSyncQueu.get(matchId);
          if (!queue || queue.length === 0) {
            return;
          }
          var updates = (0, _toConsumableArray2.default)(queue);
          (_this$offlineSyncQueu2 = this.offlineSyncQueue) == null || _this$offlineSyncQueu2.set(matchId, []);
          for (var update of updates) {
            try {
              yield this.processOfflineUpdate(update);
            } catch (error) {
              var _this$offlineSyncQueu3;
              console.error('Failed to sync update:', error);
              (_this$offlineSyncQueu3 = this.offlineSyncQueue) == null || (_this$offlineSyncQueu3 = _this$offlineSyncQueu3.get(matchId)) == null || _this$offlineSyncQueu3.push(update);
            }
          }
        } catch (error) {
          console.error('Failed to sync offline data:', error);
        }
      });
      function syncOfflineData(_x6) {
        return _syncOfflineData.apply(this, arguments);
      }
      return syncOfflineData;
    }())
  }, {
    key: "processOfflineUpdate",
    value: (function () {
      var _processOfflineUpdate = (0, _asyncToGenerator2.default)(function* (update) {
        switch (update.type) {
          case 'match_update':
            yield this.updateMatchInDatabase(update.data);
            break;
          case 'score_update':
            break;
          case 'statistics_update':
            break;
          default:
            console.warn('Unknown update type:', update.type);
        }
      });
      function processOfflineUpdate(_x7) {
        return _processOfflineUpdate.apply(this, arguments);
      }
      return processOfflineUpdate;
    }())
  }, {
    key: "startAutoSave",
    value: function startAutoSave() {
      var _this4 = this;
      if (this.autoSaveInterval) {
        clearInterval(this.autoSaveInterval);
      }
      this.autoSaveInterval = setInterval((0, _asyncToGenerator2.default)(function* () {
        if (_this4.currentSession) {
          try {
            yield _this4.updateMatchInDatabase(_this4.currentSession.match);
          } catch (error) {
            console.error('Auto-save failed:', error);
          }
        }
      }), 120000);
    }
  }, {
    key: "cleanupFailedSession",
    value: (function () {
      var _cleanupFailedSession = (0, _asyncToGenerator2.default)(function* () {
        try {
          if (this.currentSession) {
            if (this.currentSession.videoRecordingActive) {
              yield _VideoRecordingService.videoRecordingService.stopRecording();
            }
            if (this.autoSaveInterval) {
              clearInterval(this.autoSaveInterval);
              this.autoSaveInterval = null;
            }
            if (this.syncInterval) {
              clearInterval(this.syncInterval);
              this.syncInterval = null;
            }
            this.currentSession = null;
          }
        } catch (error) {
          console.error('Failed to cleanup session:', error);
        }
      });
      function cleanupFailedSession() {
        return _cleanupFailedSession.apply(this, arguments);
      }
      return cleanupFailedSession;
    }())
  }]);
}();
var matchRecordingService = exports.matchRecordingService = new MatchRecordingService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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