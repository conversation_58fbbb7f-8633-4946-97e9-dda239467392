ba835906aa5dc572003df297b4bf7e33
_getJestObj().mock("../../services/ai/VideoAnalysisService", function () {
  return {
    videoAnalysisService: mockVideoAnalysisService
  };
});
_getJestObj().mock("../../services/ai/OpenAIService");
_getJestObj().mock("../../config/environment");
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockVideoAnalysisService = {
  initialize: jest.fn().mockResolvedValue({
    success: true
  }),
  analyzeVideo: jest.fn(),
  getAnalysisProgress: jest.fn()
};
describe('VideoAnalysisService', function () {
  beforeEach(function () {
    jest.clearAllMocks();
    mockVideoAnalysisService.initialize.mockResolvedValue({
      success: true
    });
    mockVideoAnalysisService.getAnalysisProgress.mockReturnValue(null);
    process.env.EXPO_PUBLIC_MEDIAPIPE_MODEL_URL = 'https://mock-model-url.com/model.task';
    process.env.EXPO_PUBLIC_ENABLE_AI_FEATURES = 'true';
  });
  describe('initialize', function () {
    it('should initialize successfully', (0, _asyncToGenerator2.default)(function* () {
      var result = yield mockVideoAnalysisService.initialize();
      expect(result.success).toBe(true);
      expect(result.error).toBeUndefined();
    }));
    it('should handle initialization only once', (0, _asyncToGenerator2.default)(function* () {
      yield mockVideoAnalysisService.initialize();
      var result = yield mockVideoAnalysisService.initialize();
      expect(result.success).toBe(true);
    }));
    it('should handle initialization failure', (0, _asyncToGenerator2.default)(function* () {
      mockVideoAnalysisService.initialize.mockResolvedValueOnce({
        success: false,
        error: 'MediaPipe load failed'
      });
      var result = yield mockVideoAnalysisService.initialize();
      expect(result.success).toBe(false);
      expect(result.error).toBe('MediaPipe load failed');
    }));
  });
  describe('analyzeVideo', function () {
    var mockVideoUrl = 'https://example.com/tennis-video.mp4';
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      yield mockVideoAnalysisService.initialize();
      mockVideoAnalysisService.analyzeVideo.mockResolvedValue({
        result: {
          videoId: 'video-123',
          duration: 120,
          poses: Array.from({
            length: 100
          }, function (_, i) {
            return {
              landmarks: Array.from({
                length: 33
              }, function (_, j) {
                return {
                  x: Math.random(),
                  y: Math.random(),
                  visibility: 0.8 + Math.random() * 0.2
                };
              }),
              confidence: 0.8 + Math.random() * 0.2,
              timestamp: i * 1000
            };
          }),
          strokes: [{
            type: 'forehand',
            startTime: 1000,
            endTime: 3000,
            confidence: 0.85,
            biomechanics: {
              preparation: 75,
              execution: 80,
              followThrough: 70,
              timing: 85,
              balance: 78
            },
            technicalAnalysis: {
              racketPath: [{
                x: 0.3,
                y: 0.5
              }, {
                x: 0.7,
                y: 0.3
              }],
              bodyRotation: 85,
              weightTransfer: 80,
              footwork: 75,
              contactPoint: {
                x: 0.5,
                y: 0.4,
                optimal: true
              }
            },
            keyFrames: [1000, 2000, 3000]
          }],
          overallScore: 78,
          improvements: ['Better balance', 'Improve timing'],
          strengths: ['Good preparation', 'Excellent follow-through'],
          recommendations: ['Focus on weight transfer', 'Work on footwork']
        },
        error: undefined
      });
    }));
    it('should analyze video successfully', (0, _asyncToGenerator2.default)(function* () {
      var result = yield mockVideoAnalysisService.analyzeVideo(mockVideoUrl, {
        strokeType: 'forehand',
        compareWithProfessional: true,
        generateCoaching: true
      });
      expect(result.result).toBeDefined();
      expect(result.error).toBeUndefined();
      var analysis = result.result;
      expect(analysis.videoId).toBeDefined();
      expect(analysis.duration).toBeGreaterThan(0);
      expect(analysis.poses).toHaveLength(100);
      expect(analysis.strokes).toHaveLength(1);
      expect(analysis.overallScore).toBeGreaterThan(0);
      expect(analysis.improvements).toBeInstanceOf(Array);
      expect(analysis.strengths).toBeInstanceOf(Array);
      expect(analysis.recommendations).toBeInstanceOf(Array);
    }));
    it('should handle video analysis with specific stroke type', (0, _asyncToGenerator2.default)(function* () {
      mockVideoAnalysisService.analyzeVideo.mockResolvedValueOnce({
        result: {
          videoId: 'video-123',
          strokes: [{
            type: 'serve',
            confidence: 0.9
          }],
          overallScore: 80,
          improvements: [],
          strengths: [],
          recommendations: []
        }
      });
      var result = yield mockVideoAnalysisService.analyzeVideo(mockVideoUrl, {
        strokeType: 'serve'
      });
      expect(result.result).toBeDefined();
      expect(result.result.strokes[0].type).toBe('serve');
    }));
    it('should include professional comparison when requested', (0, _asyncToGenerator2.default)(function* () {
      mockVideoAnalysisService.analyzeVideo.mockResolvedValueOnce({
        result: {
          videoId: 'video-123',
          strokes: [],
          overallScore: 75,
          improvements: [],
          strengths: [],
          recommendations: [],
          comparisonData: {
            professionalStroke: {
              type: 'forehand',
              technique: 'professional'
            },
            similarities: 85,
            differences: ['Timing could be improved', 'Follow-through needs work']
          }
        }
      });
      var result = yield mockVideoAnalysisService.analyzeVideo(mockVideoUrl, {
        compareWithProfessional: true
      });
      expect(result.result).toBeDefined();
      expect(result.result.comparisonData).toBeDefined();
      expect(result.result.comparisonData.professionalStroke).toBeDefined();
      expect(result.result.comparisonData.similarities).toBeGreaterThanOrEqual(0);
      expect(result.result.comparisonData.differences).toBeInstanceOf(Array);
    }));
    it('should handle analysis without professional comparison', (0, _asyncToGenerator2.default)(function* () {
      mockVideoAnalysisService.analyzeVideo.mockResolvedValueOnce({
        result: {
          videoId: 'video-123',
          strokes: [],
          overallScore: 75,
          improvements: [],
          strengths: [],
          recommendations: []
        }
      });
      var result = yield mockVideoAnalysisService.analyzeVideo(mockVideoUrl, {
        compareWithProfessional: false
      });
      expect(result.result).toBeDefined();
      expect(result.result.comparisonData).toBeUndefined();
    }));
    it('should handle OpenAI service errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockVideoAnalysisService.analyzeVideo.mockResolvedValueOnce({
        result: {
          videoId: 'video-123',
          strokes: [],
          overallScore: 70,
          improvements: ['Basic improvement'],
          strengths: ['Basic strength'],
          recommendations: ['Basic recommendation']
        },
        error: undefined
      });
      var result = yield mockVideoAnalysisService.analyzeVideo(mockVideoUrl);
      expect(result.result).toBeDefined();
      expect(result.error).toBeUndefined();
    }));
    it('should handle video analysis errors', (0, _asyncToGenerator2.default)(function* () {
      mockVideoAnalysisService.analyzeVideo.mockResolvedValueOnce({
        result: null,
        error: 'Invalid video URL'
      });
      var invalidVideoUrl = 'invalid-url';
      var result = yield mockVideoAnalysisService.analyzeVideo(invalidVideoUrl);
      expect(result.result).toBeNull();
      expect(result.error).toBeDefined();
    }));
  });
  describe('getAnalysisProgress', function () {
    it('should return null for non-existent video', function () {
      mockVideoAnalysisService.getAnalysisProgress.mockReturnValue(null);
      var progress = mockVideoAnalysisService.getAnalysisProgress('non-existent-id');
      expect(progress).toBeNull();
    });
    it('should track analysis progress', (0, _asyncToGenerator2.default)(function* () {
      mockVideoAnalysisService.getAnalysisProgress.mockReturnValue({
        stage: 'processing',
        progress: 50,
        message: 'Analyzing video frames...'
      });
      var progress = mockVideoAnalysisService.getAnalysisProgress('video-123');
      expect(progress).toBeDefined();
      expect(progress.stage).toBe('processing');
      expect(progress.progress).toBe(50);
      expect(progress.message).toBe('Analyzing video frames...');
    }));
    it('should handle completed analysis progress', function () {
      mockVideoAnalysisService.getAnalysisProgress.mockReturnValue({
        stage: 'complete',
        progress: 100,
        message: 'Analysis complete'
      });
      var progress = mockVideoAnalysisService.getAnalysisProgress('video-123');
      expect(progress.stage).toBe('complete');
      expect(progress.progress).toBe(100);
    });
  });
  describe('pose detection', function () {
    it('should generate realistic pose landmarks', (0, _asyncToGenerator2.default)(function* () {
      mockVideoAnalysisService.analyzeVideo.mockResolvedValueOnce({
        result: {
          videoId: 'video-123',
          poses: [{
            landmarks: Array.from({
              length: 33
            }, function () {
              return {
                x: Math.random(),
                y: Math.random(),
                visibility: 0.8 + Math.random() * 0.2
              };
            }),
            confidence: 0.85,
            timestamp: 1000
          }],
          strokes: [],
          overallScore: 75,
          improvements: [],
          strengths: [],
          recommendations: []
        }
      });
      yield mockVideoAnalysisService.initialize();
      var result = yield mockVideoAnalysisService.analyzeVideo('test-video.mp4');
      expect(result.result).toBeDefined();
      var poses = result.result.poses;
      expect(poses.length).toBeGreaterThan(0);
      var firstPose = poses[0];
      expect(firstPose.landmarks).toHaveLength(33);
      expect(firstPose.confidence).toBeGreaterThan(0);
      expect(firstPose.timestamp).toBeGreaterThanOrEqual(0);
      var landmark = firstPose.landmarks[0];
      expect(landmark.x).toBeGreaterThanOrEqual(0);
      expect(landmark.x).toBeLessThanOrEqual(1);
      expect(landmark.y).toBeGreaterThanOrEqual(0);
      expect(landmark.y).toBeLessThanOrEqual(1);
      expect(landmark.visibility).toBeGreaterThan(0);
    }));
  });
  describe('stroke analysis', function () {
    beforeEach(function () {
      mockVideoAnalysisService.analyzeVideo.mockResolvedValue({
        result: {
          videoId: 'video-123',
          strokes: [{
            type: 'forehand',
            biomechanics: {
              preparation: 75,
              execution: 80,
              followThrough: 70,
              timing: 85,
              balance: 78
            },
            technicalAnalysis: {
              racketPath: [{
                x: 0.3,
                y: 0.5
              }, {
                x: 0.7,
                y: 0.3
              }],
              bodyRotation: 85,
              weightTransfer: 80,
              footwork: 75,
              contactPoint: {
                x: 0.5,
                y: 0.4,
                optimal: true
              }
            }
          }],
          overallScore: 78,
          improvements: [],
          strengths: [],
          recommendations: []
        }
      });
    });
    it('should analyze biomechanics correctly', (0, _asyncToGenerator2.default)(function* () {
      var result = yield mockVideoAnalysisService.analyzeVideo('test-video.mp4');
      expect(result.result).toBeDefined();
      var strokes = result.result.strokes;
      expect(strokes.length).toBeGreaterThan(0);
      var stroke = strokes[0];
      expect(stroke.biomechanics.preparation).toBeGreaterThanOrEqual(0);
      expect(stroke.biomechanics.preparation).toBeLessThanOrEqual(100);
      expect(stroke.biomechanics.execution).toBeGreaterThanOrEqual(0);
      expect(stroke.biomechanics.execution).toBeLessThanOrEqual(100);
      expect(stroke.biomechanics.followThrough).toBeGreaterThanOrEqual(0);
      expect(stroke.biomechanics.followThrough).toBeLessThanOrEqual(100);
      expect(stroke.biomechanics.timing).toBeGreaterThanOrEqual(0);
      expect(stroke.biomechanics.timing).toBeLessThanOrEqual(100);
      expect(stroke.biomechanics.balance).toBeGreaterThanOrEqual(0);
      expect(stroke.biomechanics.balance).toBeLessThanOrEqual(100);
    }));
    it('should provide technical analysis', (0, _asyncToGenerator2.default)(function* () {
      var result = yield mockVideoAnalysisService.analyzeVideo('test-video.mp4');
      var stroke = result.result.strokes[0];
      expect(stroke.technicalAnalysis.racketPath).toBeInstanceOf(Array);
      expect(stroke.technicalAnalysis.bodyRotation).toBeGreaterThanOrEqual(0);
      expect(stroke.technicalAnalysis.weightTransfer).toBeGreaterThanOrEqual(0);
      expect(stroke.technicalAnalysis.footwork).toBeGreaterThanOrEqual(0);
      expect(stroke.technicalAnalysis.contactPoint).toBeDefined();
      expect(typeof stroke.technicalAnalysis.contactPoint.optimal).toBe('boolean');
    }));
    it('should calculate overall score', (0, _asyncToGenerator2.default)(function* () {
      var result = yield mockVideoAnalysisService.analyzeVideo('test-video.mp4');
      expect(result.result.overallScore).toBeGreaterThanOrEqual(0);
      expect(result.result.overallScore).toBeLessThanOrEqual(100);
    }));
  });
  describe('insights generation', function () {
    beforeEach(function () {
      mockVideoAnalysisService.analyzeVideo.mockResolvedValue({
        result: {
          videoId: 'video-123',
          strokes: [],
          overallScore: 75,
          improvements: ['Better balance', 'Improve timing'],
          strengths: ['Good preparation', 'Excellent follow-through'],
          recommendations: ['Focus on weight transfer', 'Work on footwork']
        }
      });
    });
    it('should generate appropriate insights based on scores', (0, _asyncToGenerator2.default)(function* () {
      var result = yield mockVideoAnalysisService.analyzeVideo('test-video.mp4');
      expect(result.result.improvements).toBeInstanceOf(Array);
      expect(result.result.strengths).toBeInstanceOf(Array);
      expect(result.result.recommendations).toBeInstanceOf(Array);
      var totalInsights = result.result.improvements.length + result.result.strengths.length + result.result.recommendations.length;
      expect(totalInsights).toBeGreaterThan(0);
    }));
    it('should provide contextual recommendations', (0, _asyncToGenerator2.default)(function* () {
      var result = yield mockVideoAnalysisService.analyzeVideo('test-video.mp4', {
        strokeType: 'serve'
      });
      var recommendations = result.result.recommendations;
      expect(recommendations.length).toBeGreaterThan(0);
      expect(recommendations.every(function (rec) {
        return typeof rec === 'string';
      })).toBe(true);
    }));
  });
  describe('professional comparison', function () {
    beforeEach(function () {
      mockVideoAnalysisService.analyzeVideo.mockResolvedValue({
        result: {
          videoId: 'video-123',
          strokes: [],
          overallScore: 75,
          improvements: [],
          strengths: [],
          recommendations: [],
          comparisonData: {
            professionalStroke: {
              type: 'forehand',
              technique: 'professional'
            },
            similarities: 85,
            differences: ['Timing could be improved', 'Follow-through needs work']
          }
        }
      });
    });
    it('should calculate similarity scores', (0, _asyncToGenerator2.default)(function* () {
      var result = yield mockVideoAnalysisService.analyzeVideo('test-video.mp4', {
        compareWithProfessional: true
      });
      var comparison = result.result.comparisonData;
      expect(comparison.similarities).toBeGreaterThanOrEqual(0);
      expect(comparison.similarities).toBeLessThanOrEqual(100);
    }));
    it('should identify meaningful differences', (0, _asyncToGenerator2.default)(function* () {
      var result = yield mockVideoAnalysisService.analyzeVideo('test-video.mp4', {
        compareWithProfessional: true
      });
      var differences = result.result.comparisonData.differences;
      expect(differences).toBeInstanceOf(Array);
      expect(differences.every(function (diff) {
        return typeof diff === 'string';
      })).toBe(true);
    }));
  });
  describe('error handling', function () {
    it('should handle initialization errors', (0, _asyncToGenerator2.default)(function* () {
      mockVideoAnalysisService.initialize.mockResolvedValueOnce({
        success: false,
        error: 'MediaPipe model failed to load'
      });
      var result = yield mockVideoAnalysisService.initialize();
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    }));
    it('should handle video processing errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockVideoAnalysisService.analyzeVideo.mockResolvedValueOnce({
        result: null,
        error: 'Invalid video format'
      });
      var result = yield mockVideoAnalysisService.analyzeVideo('');
      expect(result.result).toBeNull();
      expect(result.error).toBeDefined();
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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