f448ef59f47075ada157fa4c47ed7eb1
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_6ubufw8oh() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\usePerformanceAnalytics.ts";
  var hash = "2e7d3860aa22cc44bc6eda0ab2090325990599d0";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\usePerformanceAnalytics.ts",
    statementMap: {
      "0": {
        start: {
          line: 44,
          column: 44
        },
        end: {
          line: 44,
          column: 86
        }
      },
      "1": {
        start: {
          line: 45,
          column: 32
        },
        end: {
          line: 45,
          column: 47
        }
      },
      "2": {
        start: {
          line: 46,
          column: 28
        },
        end: {
          line: 46,
          column: 57
        }
      },
      "3": {
        start: {
          line: 47,
          column: 19
        },
        end: {
          line: 47,
          column: 28
        }
      },
      "4": {
        start: {
          line: 56,
          column: 6
        },
        end: {
          line: 61,
          column: 4
        }
      },
      "5": {
        start: {
          line: 64,
          column: 29
        },
        end: {
          line: 64,
          column: 70
        }
      },
      "6": {
        start: {
          line: 67,
          column: 25
        },
        end: {
          line: 72,
          column: 4
        }
      },
      "7": {
        start: {
          line: 75,
          column: 32
        },
        end: {
          line: 75,
          column: 50
        }
      },
      "8": {
        start: {
          line: 78,
          column: 26
        },
        end: {
          line: 91,
          column: 42
        }
      },
      "9": {
        start: {
          line: 79,
          column: 4
        },
        end: {
          line: 79,
          column: 60
        }
      },
      "10": {
        start: {
          line: 79,
          column: 48
        },
        end: {
          line: 79,
          column: 60
        }
      },
      "11": {
        start: {
          line: 81,
          column: 4
        },
        end: {
          line: 90,
          column: 6
        }
      },
      "12": {
        start: {
          line: 96,
          column: 23
        },
        end: {
          line: 154,
          column: 35
        }
      },
      "13": {
        start: {
          line: 97,
          column: 4
        },
        end: {
          line: 97,
          column: 22
        }
      },
      "14": {
        start: {
          line: 97,
          column: 15
        },
        end: {
          line: 97,
          column: 22
        }
      },
      "15": {
        start: {
          line: 99,
          column: 4
        },
        end: {
          line: 153,
          column: 5
        }
      },
      "16": {
        start: {
          line: 100,
          column: 6
        },
        end: {
          line: 100,
          column: 23
        }
      },
      "17": {
        start: {
          line: 101,
          column: 6
        },
        end: {
          line: 101,
          column: 21
        }
      },
      "18": {
        start: {
          line: 104,
          column: 29
        },
        end: {
          line: 104,
          column: 82
        }
      },
      "19": {
        start: {
          line: 107,
          column: 40
        },
        end: {
          line: 107,
          column: 42
        }
      },
      "20": {
        start: {
          line: 109,
          column: 6
        },
        end: {
          line: 111,
          column: 7
        }
      },
      "21": {
        start: {
          line: 110,
          column: 8
        },
        end: {
          line: 110,
          column: 75
        }
      },
      "22": {
        start: {
          line: 113,
          column: 6
        },
        end: {
          line: 115,
          column: 7
        }
      },
      "23": {
        start: {
          line: 114,
          column: 8
        },
        end: {
          line: 114,
          column: 65
        }
      },
      "24": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 119,
          column: 7
        }
      },
      "25": {
        start: {
          line: 118,
          column: 8
        },
        end: {
          line: 118,
          column: 64
        }
      },
      "26": {
        start: {
          line: 121,
          column: 42
        },
        end: {
          line: 127,
          column: 7
        }
      },
      "27": {
        start: {
          line: 129,
          column: 6
        },
        end: {
          line: 129,
          column: 31
        }
      },
      "28": {
        start: {
          line: 132,
          column: 6
        },
        end: {
          line: 146,
          column: 10
        }
      },
      "29": {
        start: {
          line: 149,
          column: 6
        },
        end: {
          line: 149,
          column: 79
        }
      },
      "30": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 50
        }
      },
      "31": {
        start: {
          line: 152,
          column: 6
        },
        end: {
          line: 152,
          column: 24
        }
      },
      "32": {
        start: {
          line: 159,
          column: 36
        },
        end: {
          line: 175,
          column: 23
        }
      },
      "33": {
        start: {
          line: 160,
          column: 4
        },
        end: {
          line: 160,
          column: 22
        }
      },
      "34": {
        start: {
          line: 160,
          column: 15
        },
        end: {
          line: 160,
          column: 22
        }
      },
      "35": {
        start: {
          line: 162,
          column: 4
        },
        end: {
          line: 174,
          column: 5
        }
      },
      "36": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 163,
          column: 23
        }
      },
      "37": {
        start: {
          line: 164,
          column: 6
        },
        end: {
          line: 164,
          column: 21
        }
      },
      "38": {
        start: {
          line: 167,
          column: 6
        },
        end: {
          line: 167,
          column: 28
        }
      },
      "39": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 170,
          column: 93
        }
      },
      "40": {
        start: {
          line: 171,
          column: 6
        },
        end: {
          line: 171,
          column: 54
        }
      },
      "41": {
        start: {
          line: 173,
          column: 6
        },
        end: {
          line: 173,
          column: 24
        }
      },
      "42": {
        start: {
          line: 180,
          column: 30
        },
        end: {
          line: 198,
          column: 12
        }
      },
      "43": {
        start: {
          line: 181,
          column: 4
        },
        end: {
          line: 181,
          column: 27
        }
      },
      "44": {
        start: {
          line: 181,
          column: 15
        },
        end: {
          line: 181,
          column: 27
        }
      },
      "45": {
        start: {
          line: 183,
          column: 4
        },
        end: {
          line: 197,
          column: 5
        }
      },
      "46": {
        start: {
          line: 185,
          column: 30
        },
        end: {
          line: 185,
          column: 99
        }
      },
      "47": {
        start: {
          line: 187,
          column: 6
        },
        end: {
          line: 189,
          column: 7
        }
      },
      "48": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 188,
          column: 20
        }
      },
      "49": {
        start: {
          line: 192,
          column: 6
        },
        end: {
          line: 192,
          column: 76
        }
      },
      "50": {
        start: {
          line: 195,
          column: 6
        },
        end: {
          line: 195,
          column: 64
        }
      },
      "51": {
        start: {
          line: 196,
          column: 6
        },
        end: {
          line: 196,
          column: 18
        }
      },
      "52": {
        start: {
          line: 203,
          column: 27
        },
        end: {
          line: 241,
          column: 12
        }
      },
      "53": {
        start: {
          line: 204,
          column: 4
        },
        end: {
          line: 204,
          column: 27
        }
      },
      "54": {
        start: {
          line: 204,
          column: 15
        },
        end: {
          line: 204,
          column: 27
        }
      },
      "55": {
        start: {
          line: 206,
          column: 4
        },
        end: {
          line: 240,
          column: 5
        }
      },
      "56": {
        start: {
          line: 208,
          column: 34
        },
        end: {
          line: 212,
          column: 17
        }
      },
      "57": {
        start: {
          line: 214,
          column: 6
        },
        end: {
          line: 214,
          column: 34
        }
      },
      "58": {
        start: {
          line: 214,
          column: 22
        },
        end: {
          line: 214,
          column: 34
        }
      },
      "59": {
        start: {
          line: 217,
          column: 34
        },
        end: {
          line: 225,
          column: 18
        }
      },
      "60": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 229,
          column: 7
        }
      },
      "61": {
        start: {
          line: 228,
          column: 8
        },
        end: {
          line: 228,
          column: 20
        }
      },
      "62": {
        start: {
          line: 232,
          column: 27
        },
        end: {
          line: 232,
          column: 59
        }
      },
      "63": {
        start: {
          line: 235,
          column: 6
        },
        end: {
          line: 235,
          column: 61
        }
      },
      "64": {
        start: {
          line: 238,
          column: 6
        },
        end: {
          line: 238,
          column: 58
        }
      },
      "65": {
        start: {
          line: 239,
          column: 6
        },
        end: {
          line: 239,
          column: 18
        }
      },
      "66": {
        start: {
          line: 246,
          column: 27
        },
        end: {
          line: 248,
          column: 19
        }
      },
      "67": {
        start: {
          line: 247,
          column: 4
        },
        end: {
          line: 247,
          column: 24
        }
      },
      "68": {
        start: {
          line: 251,
          column: 2
        },
        end: {
          line: 273,
          column: 5
        }
      },
      "69": {
        start: {
          line: 251,
          column: 24
        },
        end: {
          line: 261,
          column: 3
        }
      },
      "70": {
        start: {
          line: 277,
          column: 37
        },
        end: {
          line: 304,
          column: 8
        }
      },
      "71": {
        start: {
          line: 278,
          column: 30
        },
        end: {
          line: 283,
          column: 16
        }
      },
      "72": {
        start: {
          line: 285,
          column: 31
        },
        end: {
          line: 290,
          column: 16
        }
      },
      "73": {
        start: {
          line: 292,
          column: 33
        },
        end: {
          line: 297,
          column: 15
        }
      },
      "74": {
        start: {
          line: 299,
          column: 4
        },
        end: {
          line: 303,
          column: 6
        }
      },
      "75": {
        start: {
          line: 306,
          column: 24
        },
        end: {
          line: 333,
          column: 8
        }
      },
      "76": {
        start: {
          line: 308,
          column: 29
        },
        end: {
          line: 310,
          column: 10
        }
      },
      "77": {
        start: {
          line: 313,
          column: 29
        },
        end: {
          line: 323,
          column: 7
        }
      },
      "78": {
        start: {
          line: 313,
          column: 63
        },
        end: {
          line: 323,
          column: 5
        }
      },
      "79": {
        start: {
          line: 326,
          column: 27
        },
        end: {
          line: 326,
          column: 65
        }
      },
      "80": {
        start: {
          line: 328,
          column: 4
        },
        end: {
          line: 332,
          column: 6
        }
      },
      "81": {
        start: {
          line: 335,
          column: 27
        },
        end: {
          line: 365,
          column: 8
        }
      },
      "82": {
        start: {
          line: 336,
          column: 32
        },
        end: {
          line: 336,
          column: 34
        }
      },
      "83": {
        start: {
          line: 337,
          column: 33
        },
        end: {
          line: 337,
          column: 35
        }
      },
      "84": {
        start: {
          line: 338,
          column: 38
        },
        end: {
          line: 338,
          column: 40
        }
      },
      "85": {
        start: {
          line: 341,
          column: 23
        },
        end: {
          line: 341,
          column: 81
        }
      },
      "86": {
        start: {
          line: 341,
          column: 55
        },
        end: {
          line: 341,
          column: 73
        }
      },
      "87": {
        start: {
          line: 342,
          column: 20
        },
        end: {
          line: 342,
          column: 90
        }
      },
      "88": {
        start: {
          line: 344,
          column: 4
        },
        end: {
          line: 349,
          column: 5
        }
      },
      "89": {
        start: {
          line: 345,
          column: 6
        },
        end: {
          line: 345,
          column: 56
        }
      },
      "90": {
        start: {
          line: 346,
          column: 11
        },
        end: {
          line: 349,
          column: 5
        }
      },
      "91": {
        start: {
          line: 347,
          column: 6
        },
        end: {
          line: 347,
          column: 54
        }
      },
      "92": {
        start: {
          line: 348,
          column: 6
        },
        end: {
          line: 348,
          column: 63
        }
      },
      "93": {
        start: {
          line: 352,
          column: 28
        },
        end: {
          line: 352,
          column: 88
        }
      },
      "94": {
        start: {
          line: 352,
          column: 64
        },
        end: {
          line: 352,
          column: 87
        }
      },
      "95": {
        start: {
          line: 353,
          column: 28
        },
        end: {
          line: 353,
          column: 88
        }
      },
      "96": {
        start: {
          line: 353,
          column: 64
        },
        end: {
          line: 353,
          column: 87
        }
      },
      "97": {
        start: {
          line: 355,
          column: 4
        },
        end: {
          line: 357,
          column: 5
        }
      },
      "98": {
        start: {
          line: 356,
          column: 6
        },
        end: {
          line: 356,
          column: 85
        }
      },
      "99": {
        start: {
          line: 356,
          column: 62
        },
        end: {
          line: 356,
          column: 69
        }
      },
      "100": {
        start: {
          line: 359,
          column: 4
        },
        end: {
          line: 362,
          column: 5
        }
      },
      "101": {
        start: {
          line: 360,
          column: 6
        },
        end: {
          line: 360,
          column: 86
        }
      },
      "102": {
        start: {
          line: 360,
          column: 63
        },
        end: {
          line: 360,
          column: 70
        }
      },
      "103": {
        start: {
          line: 361,
          column: 6
        },
        end: {
          line: 361,
          column: 76
        }
      },
      "104": {
        start: {
          line: 364,
          column: 4
        },
        end: {
          line: 364,
          column: 54
        }
      },
      "105": {
        start: {
          line: 367,
          column: 30
        },
        end: {
          line: 384,
          column: 8
        }
      },
      "106": {
        start: {
          line: 368,
          column: 29
        },
        end: {
          line: 375,
          column: 9
        }
      },
      "107": {
        start: {
          line: 369,
          column: 19
        },
        end: {
          line: 369,
          column: 42
        }
      },
      "108": {
        start: {
          line: 370,
          column: 17
        },
        end: {
          line: 375,
          column: 7
        }
      },
      "109": {
        start: {
          line: 377,
          column: 27
        },
        end: {
          line: 381,
          column: 5
        }
      },
      "110": {
        start: {
          line: 383,
          column: 4
        },
        end: {
          line: 383,
          column: 48
        }
      },
      "111": {
        start: {
          line: 386,
          column: 33
        },
        end: {
          line: 395,
          column: 8
        }
      },
      "112": {
        start: {
          line: 388,
          column: 17
        },
        end: {
          line: 388,
          column: 18
        }
      },
      "113": {
        start: {
          line: 389,
          column: 4
        },
        end: {
          line: 394,
          column: 8
        }
      },
      "114": {
        start: {
          line: 389,
          column: 51
        },
        end: {
          line: 394,
          column: 5
        }
      },
      "115": {
        start: {
          line: 397,
          column: 39
        },
        end: {
          line: 417,
          column: 8
        }
      },
      "116": {
        start: {
          line: 402,
          column: 38
        },
        end: {
          line: 402,
          column: 40
        }
      },
      "117": {
        start: {
          line: 404,
          column: 4
        },
        end: {
          line: 406,
          column: 5
        }
      },
      "118": {
        start: {
          line: 405,
          column: 6
        },
        end: {
          line: 405,
          column: 73
        }
      },
      "119": {
        start: {
          line: 408,
          column: 4
        },
        end: {
          line: 410,
          column: 5
        }
      },
      "120": {
        start: {
          line: 409,
          column: 6
        },
        end: {
          line: 409,
          column: 76
        }
      },
      "121": {
        start: {
          line: 412,
          column: 4
        },
        end: {
          line: 414,
          column: 5
        }
      },
      "122": {
        start: {
          line: 413,
          column: 6
        },
        end: {
          line: 413,
          column: 80
        }
      },
      "123": {
        start: {
          line: 416,
          column: 4
        },
        end: {
          line: 416,
          column: 27
        }
      },
      "124": {
        start: {
          line: 419,
          column: 28
        },
        end: {
          line: 441,
          column: 12
        }
      },
      "125": {
        start: {
          line: 420,
          column: 4
        },
        end: {
          line: 440,
          column: 5
        }
      },
      "126": {
        start: {
          line: 421,
          column: 6
        },
        end: {
          line: 437,
          column: 11
        }
      },
      "127": {
        start: {
          line: 439,
          column: 6
        },
        end: {
          line: 439,
          column: 59
        }
      },
      "128": {
        start: {
          line: 443,
          column: 31
        },
        end: {
          line: 477,
          column: 8
        }
      },
      "129": {
        start: {
          line: 451,
          column: 19
        },
        end: {
          line: 451,
          column: 101
        }
      },
      "130": {
        start: {
          line: 453,
          column: 4
        },
        end: {
          line: 476,
          column: 7
        }
      },
      "131": {
        start: {
          line: 454,
          column: 21
        },
        end: {
          line: 454,
          column: 73
        }
      },
      "132": {
        start: {
          line: 454,
          column: 41
        },
        end: {
          line: 454,
          column: 49
        }
      },
      "133": {
        start: {
          line: 454,
          column: 63
        },
        end: {
          line: 454,
          column: 72
        }
      },
      "134": {
        start: {
          line: 455,
          column: 6
        },
        end: {
          line: 463,
          column: 7
        }
      },
      "135": {
        start: {
          line: 456,
          column: 8
        },
        end: {
          line: 462,
          column: 10
        }
      },
      "136": {
        start: {
          line: 465,
          column: 29
        },
        end: {
          line: 465,
          column: 68
        }
      },
      "137": {
        start: {
          line: 466,
          column: 28
        },
        end: {
          line: 466,
          column: 67
        }
      },
      "138": {
        start: {
          line: 467,
          column: 25
        },
        end: {
          line: 467,
          column: 55
        }
      },
      "139": {
        start: {
          line: 469,
          column: 6
        },
        end: {
          line: 475,
          column: 8
        }
      },
      "140": {
        start: {
          line: 479,
          column: 34
        },
        end: {
          line: 508,
          column: 8
        }
      },
      "141": {
        start: {
          line: 481,
          column: 47
        },
        end: {
          line: 481,
          column: 49
        }
      },
      "142": {
        start: {
          line: 483,
          column: 4
        },
        end: {
          line: 501,
          column: 7
        }
      },
      "143": {
        start: {
          line: 484,
          column: 19
        },
        end: {
          line: 484,
          column: 74
        }
      },
      "144": {
        start: {
          line: 485,
          column: 6
        },
        end: {
          line: 492,
          column: 7
        }
      },
      "145": {
        start: {
          line: 486,
          column: 8
        },
        end: {
          line: 491,
          column: 10
        }
      },
      "146": {
        start: {
          line: 494,
          column: 6
        },
        end: {
          line: 494,
          column: 43
        }
      },
      "147": {
        start: {
          line: 495,
          column: 6
        },
        end: {
          line: 495,
          column: 65
        }
      },
      "148": {
        start: {
          line: 496,
          column: 6
        },
        end: {
          line: 500,
          column: 7
        }
      },
      "149": {
        start: {
          line: 497,
          column: 8
        },
        end: {
          line: 499,
          column: 10
        }
      },
      "150": {
        start: {
          line: 498,
          column: 10
        },
        end: {
          line: 498,
          column: 53
        }
      },
      "151": {
        start: {
          line: 503,
          column: 4
        },
        end: {
          line: 507,
          column: 8
        }
      },
      "152": {
        start: {
          line: 503,
          column: 57
        },
        end: {
          line: 507,
          column: 5
        }
      },
      "153": {
        start: {
          line: 510,
          column: 34
        },
        end: {
          line: 539,
          column: 8
        }
      },
      "154": {
        start: {
          line: 512,
          column: 16
        },
        end: {
          line: 512,
          column: 26
        }
      },
      "155": {
        start: {
          line: 513,
          column: 23
        },
        end: {
          line: 513,
          column: 33
        }
      },
      "156": {
        start: {
          line: 515,
          column: 4
        },
        end: {
          line: 525,
          column: 5
        }
      },
      "157": {
        start: {
          line: 517,
          column: 8
        },
        end: {
          line: 517,
          column: 46
        }
      },
      "158": {
        start: {
          line: 518,
          column: 8
        },
        end: {
          line: 518,
          column: 14
        }
      },
      "159": {
        start: {
          line: 520,
          column: 8
        },
        end: {
          line: 520,
          column: 48
        }
      },
      "160": {
        start: {
          line: 521,
          column: 8
        },
        end: {
          line: 521,
          column: 14
        }
      },
      "161": {
        start: {
          line: 523,
          column: 8
        },
        end: {
          line: 523,
          column: 54
        }
      },
      "162": {
        start: {
          line: 524,
          column: 8
        },
        end: {
          line: 524,
          column: 14
        }
      },
      "163": {
        start: {
          line: 527,
          column: 4
        },
        end: {
          line: 538,
          column: 10
        }
      },
      "164": {
        start: {
          line: 528,
          column: 22
        },
        end: {
          line: 528,
          column: 61
        }
      },
      "165": {
        start: {
          line: 529,
          column: 20
        },
        end: {
          line: 538,
          column: 7
        }
      },
      "166": {
        start: {
          line: 541,
          column: 32
        },
        end: {
          line: 564,
          column: 8
        }
      },
      "167": {
        start: {
          line: 542,
          column: 19
        },
        end: {
          line: 545,
          column: 5
        }
      },
      "168": {
        start: {
          line: 547,
          column: 16
        },
        end: {
          line: 547,
          column: 17
        }
      },
      "169": {
        start: {
          line: 548,
          column: 4
        },
        end: {
          line: 555,
          column: 7
        }
      },
      "170": {
        start: {
          line: 549,
          column: 6
        },
        end: {
          line: 554,
          column: 7
        }
      },
      "171": {
        start: {
          line: 550,
          column: 8
        },
        end: {
          line: 552,
          column: 11
        }
      },
      "172": {
        start: {
          line: 551,
          column: 10
        },
        end: {
          line: 551,
          column: 79
        }
      },
      "173": {
        start: {
          line: 553,
          column: 8
        },
        end: {
          line: 553,
          column: 16
        }
      },
      "174": {
        start: {
          line: 557,
          column: 4
        },
        end: {
          line: 557,
          column: 35
        }
      },
      "175": {
        start: {
          line: 557,
          column: 21
        },
        end: {
          line: 557,
          column: 35
        }
      },
      "176": {
        start: {
          line: 559,
          column: 4
        },
        end: {
          line: 561,
          column: 7
        }
      },
      "177": {
        start: {
          line: 560,
          column: 6
        },
        end: {
          line: 560,
          column: 102
        }
      },
      "178": {
        start: {
          line: 563,
          column: 4
        },
        end: {
          line: 563,
          column: 18
        }
      },
      "179": {
        start: {
          line: 566,
          column: 33
        },
        end: {
          line: 583,
          column: 8
        }
      },
      "180": {
        start: {
          line: 567,
          column: 47
        },
        end: {
          line: 567,
          column: 49
        }
      },
      "181": {
        start: {
          line: 569,
          column: 4
        },
        end: {
          line: 580,
          column: 7
        }
      },
      "182": {
        start: {
          line: 570,
          column: 24
        },
        end: {
          line: 570,
          column: 45
        }
      },
      "183": {
        start: {
          line: 571,
          column: 24
        },
        end: {
          line: 571,
          column: 48
        }
      },
      "184": {
        start: {
          line: 572,
          column: 25
        },
        end: {
          line: 572,
          column: 46
        }
      },
      "185": {
        start: {
          line: 574,
          column: 6
        },
        end: {
          line: 579,
          column: 8
        }
      },
      "186": {
        start: {
          line: 582,
          column: 4
        },
        end: {
          line: 582,
          column: 22
        }
      },
      "187": {
        start: {
          line: 585,
          column: 30
        },
        end: {
          line: 595,
          column: 8
        }
      },
      "188": {
        start: {
          line: 587,
          column: 4
        },
        end: {
          line: 587,
          column: 35
        }
      },
      "189": {
        start: {
          line: 587,
          column: 25
        },
        end: {
          line: 587,
          column: 35
        }
      },
      "190": {
        start: {
          line: 588,
          column: 4
        },
        end: {
          line: 588,
          column: 35
        }
      },
      "191": {
        start: {
          line: 588,
          column: 25
        },
        end: {
          line: 588,
          column: 35
        }
      },
      "192": {
        start: {
          line: 589,
          column: 4
        },
        end: {
          line: 589,
          column: 34
        }
      },
      "193": {
        start: {
          line: 589,
          column: 24
        },
        end: {
          line: 589,
          column: 34
        }
      },
      "194": {
        start: {
          line: 590,
          column: 4
        },
        end: {
          line: 590,
          column: 34
        }
      },
      "195": {
        start: {
          line: 590,
          column: 24
        },
        end: {
          line: 590,
          column: 34
        }
      },
      "196": {
        start: {
          line: 591,
          column: 4
        },
        end: {
          line: 591,
          column: 35
        }
      },
      "197": {
        start: {
          line: 591,
          column: 25
        },
        end: {
          line: 591,
          column: 35
        }
      },
      "198": {
        start: {
          line: 592,
          column: 4
        },
        end: {
          line: 592,
          column: 36
        }
      },
      "199": {
        start: {
          line: 592,
          column: 26
        },
        end: {
          line: 592,
          column: 36
        }
      },
      "200": {
        start: {
          line: 593,
          column: 4
        },
        end: {
          line: 593,
          column: 36
        }
      },
      "201": {
        start: {
          line: 593,
          column: 26
        },
        end: {
          line: 593,
          column: 36
        }
      },
      "202": {
        start: {
          line: 594,
          column: 4
        },
        end: {
          line: 594,
          column: 14
        }
      },
      "203": {
        start: {
          line: 597,
          column: 2
        },
        end: {
          line: 607,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "usePerformanceAnalytics",
        decl: {
          start: {
            line: 43,
            column: 16
          },
          end: {
            line: 43,
            column: 39
          }
        },
        loc: {
          start: {
            line: 43,
            column: 73
          },
          end: {
            line: 608,
            column: 1
          }
        },
        line: 43
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 78,
            column: 34
          },
          end: {
            line: 78,
            column: 35
          }
        },
        loc: {
          start: {
            line: 78,
            column: 64
          },
          end: {
            line: 91,
            column: 3
          }
        },
        line: 78
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 96,
            column: 35
          },
          end: {
            line: 96,
            column: 36
          }
        },
        loc: {
          start: {
            line: 96,
            column: 94
          },
          end: {
            line: 154,
            column: 3
          }
        },
        line: 96
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 159,
            column: 48
          },
          end: {
            line: 159,
            column: 49
          }
        },
        loc: {
          start: {
            line: 159,
            column: 60
          },
          end: {
            line: 175,
            column: 3
          }
        },
        line: 159
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 180,
            column: 42
          },
          end: {
            line: 180,
            column: 43
          }
        },
        loc: {
          start: {
            line: 180,
            column: 90
          },
          end: {
            line: 198,
            column: 3
          }
        },
        line: 180
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 203,
            column: 39
          },
          end: {
            line: 203,
            column: 40
          }
        },
        loc: {
          start: {
            line: 203,
            column: 69
          },
          end: {
            line: 241,
            column: 3
          }
        },
        line: 203
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 246,
            column: 39
          },
          end: {
            line: 246,
            column: 40
          }
        },
        loc: {
          start: {
            line: 246,
            column: 51
          },
          end: {
            line: 248,
            column: 3
          }
        },
        line: 246
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 251,
            column: 17
          },
          end: {
            line: 251,
            column: 18
          }
        },
        loc: {
          start: {
            line: 251,
            column: 24
          },
          end: {
            line: 261,
            column: 3
          }
        },
        line: 251
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 277,
            column: 49
          },
          end: {
            line: 277,
            column: 50
          }
        },
        loc: {
          start: {
            line: 277,
            column: 75
          },
          end: {
            line: 304,
            column: 3
          }
        },
        line: 277
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 306,
            column: 36
          },
          end: {
            line: 306,
            column: 37
          }
        },
        loc: {
          start: {
            line: 306,
            column: 70
          },
          end: {
            line: 333,
            column: 3
          }
        },
        line: 306
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 313,
            column: 46
          },
          end: {
            line: 313,
            column: 47
          }
        },
        loc: {
          start: {
            line: 313,
            column: 63
          },
          end: {
            line: 323,
            column: 5
          }
        },
        line: 313
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 335,
            column: 39
          },
          end: {
            line: 335,
            column: 40
          }
        },
        loc: {
          start: {
            line: 335,
            column: 81
          },
          end: {
            line: 365,
            column: 3
          }
        },
        line: 335
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 341,
            column: 43
          },
          end: {
            line: 341,
            column: 44
          }
        },
        loc: {
          start: {
            line: 341,
            column: 55
          },
          end: {
            line: 341,
            column: 73
          }
        },
        line: 341
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 352,
            column: 59
          },
          end: {
            line: 352,
            column: 60
          }
        },
        loc: {
          start: {
            line: 352,
            column: 64
          },
          end: {
            line: 352,
            column: 87
          }
        },
        line: 352
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 353,
            column: 59
          },
          end: {
            line: 353,
            column: 60
          }
        },
        loc: {
          start: {
            line: 353,
            column: 64
          },
          end: {
            line: 353,
            column: 87
          }
        },
        line: 353
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 356,
            column: 57
          },
          end: {
            line: 356,
            column: 58
          }
        },
        loc: {
          start: {
            line: 356,
            column: 62
          },
          end: {
            line: 356,
            column: 69
          }
        },
        line: 356
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 360,
            column: 58
          },
          end: {
            line: 360,
            column: 59
          }
        },
        loc: {
          start: {
            line: 360,
            column: 63
          },
          end: {
            line: 360,
            column: 70
          }
        },
        line: 360
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 367,
            column: 42
          },
          end: {
            line: 367,
            column: 43
          }
        },
        loc: {
          start: {
            line: 367,
            column: 73
          },
          end: {
            line: 384,
            column: 3
          }
        },
        line: 367
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 369,
            column: 14
          },
          end: {
            line: 369,
            column: 15
          }
        },
        loc: {
          start: {
            line: 369,
            column: 19
          },
          end: {
            line: 369,
            column: 42
          }
        },
        line: 369
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 370,
            column: 11
          },
          end: {
            line: 370,
            column: 12
          }
        },
        loc: {
          start: {
            line: 370,
            column: 17
          },
          end: {
            line: 375,
            column: 7
          }
        },
        line: 370
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 386,
            column: 45
          },
          end: {
            line: 386,
            column: 46
          }
        },
        loc: {
          start: {
            line: 386,
            column: 97
          },
          end: {
            line: 395,
            column: 3
          }
        },
        line: 386
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 389,
            column: 40
          },
          end: {
            line: 389,
            column: 41
          }
        },
        loc: {
          start: {
            line: 389,
            column: 51
          },
          end: {
            line: 394,
            column: 5
          }
        },
        line: 389
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 397,
            column: 51
          },
          end: {
            line: 397,
            column: 52
          }
        },
        loc: {
          start: {
            line: 401,
            column: 17
          },
          end: {
            line: 417,
            column: 3
          }
        },
        line: 401
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 419,
            column: 40
          },
          end: {
            line: 419,
            column: 41
          }
        },
        loc: {
          start: {
            line: 419,
            column: 110
          },
          end: {
            line: 441,
            column: 3
          }
        },
        line: 419
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 443,
            column: 43
          },
          end: {
            line: 443,
            column: 44
          }
        },
        loc: {
          start: {
            line: 449,
            column: 8
          },
          end: {
            line: 477,
            column: 3
          }
        },
        line: 449
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 453,
            column: 22
          },
          end: {
            line: 453,
            column: 23
          }
        },
        loc: {
          start: {
            line: 453,
            column: 31
          },
          end: {
            line: 476,
            column: 5
          }
        },
        line: 453
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 454,
            column: 36
          },
          end: {
            line: 454,
            column: 37
          }
        },
        loc: {
          start: {
            line: 454,
            column: 41
          },
          end: {
            line: 454,
            column: 49
          }
        },
        line: 454
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 454,
            column: 58
          },
          end: {
            line: 454,
            column: 59
          }
        },
        loc: {
          start: {
            line: 454,
            column: 63
          },
          end: {
            line: 454,
            column: 72
          }
        },
        line: 454
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 479,
            column: 46
          },
          end: {
            line: 479,
            column: 47
          }
        },
        loc: {
          start: {
            line: 479,
            column: 67
          },
          end: {
            line: 508,
            column: 3
          }
        },
        line: 479
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 483,
            column: 21
          },
          end: {
            line: 483,
            column: 22
          }
        },
        loc: {
          start: {
            line: 483,
            column: 32
          },
          end: {
            line: 501,
            column: 5
          }
        },
        line: 483
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 497,
            column: 42
          },
          end: {
            line: 497,
            column: 43
          }
        },
        loc: {
          start: {
            line: 498,
            column: 10
          },
          end: {
            line: 498,
            column: 53
          }
        },
        line: 498
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 503,
            column: 41
          },
          end: {
            line: 503,
            column: 42
          }
        },
        loc: {
          start: {
            line: 503,
            column: 57
          },
          end: {
            line: 507,
            column: 5
          }
        },
        line: 503
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 510,
            column: 46
          },
          end: {
            line: 510,
            column: 47
          }
        },
        loc: {
          start: {
            line: 510,
            column: 90
          },
          end: {
            line: 539,
            column: 3
          }
        },
        line: 510
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 528,
            column: 14
          },
          end: {
            line: 528,
            column: 15
          }
        },
        loc: {
          start: {
            line: 528,
            column: 22
          },
          end: {
            line: 528,
            column: 61
          }
        },
        line: 528
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 529,
            column: 11
          },
          end: {
            line: 529,
            column: 12
          }
        },
        loc: {
          start: {
            line: 529,
            column: 20
          },
          end: {
            line: 538,
            column: 7
          }
        },
        line: 529
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 541,
            column: 44
          },
          end: {
            line: 541,
            column: 45
          }
        },
        loc: {
          start: {
            line: 541,
            column: 66
          },
          end: {
            line: 564,
            column: 3
          }
        },
        line: 541
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 548,
            column: 22
          },
          end: {
            line: 548,
            column: 23
          }
        },
        loc: {
          start: {
            line: 548,
            column: 30
          },
          end: {
            line: 555,
            column: 5
          }
        },
        line: 548
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 550,
            column: 36
          },
          end: {
            line: 550,
            column: 37
          }
        },
        loc: {
          start: {
            line: 550,
            column: 45
          },
          end: {
            line: 552,
            column: 9
          }
        },
        line: 550
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 559,
            column: 32
          },
          end: {
            line: 559,
            column: 33
          }
        },
        loc: {
          start: {
            line: 559,
            column: 41
          },
          end: {
            line: 561,
            column: 5
          }
        },
        line: 559
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 566,
            column: 45
          },
          end: {
            line: 566,
            column: 46
          }
        },
        loc: {
          start: {
            line: 566,
            column: 84
          },
          end: {
            line: 583,
            column: 3
          }
        },
        line: 566
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 569,
            column: 38
          },
          end: {
            line: 569,
            column: 39
          }
        },
        loc: {
          start: {
            line: 569,
            column: 47
          },
          end: {
            line: 580,
            column: 5
          }
        },
        line: 569
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 585,
            column: 42
          },
          end: {
            line: 585,
            column: 43
          }
        },
        loc: {
          start: {
            line: 585,
            column: 74
          },
          end: {
            line: 595,
            column: 3
          }
        },
        line: 585
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 79,
            column: 4
          },
          end: {
            line: 79,
            column: 60
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 79,
            column: 4
          },
          end: {
            line: 79,
            column: 60
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 79
      },
      "1": {
        loc: {
          start: {
            line: 79,
            column: 8
          },
          end: {
            line: 79,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 79,
            column: 8
          },
          end: {
            line: 79,
            column: 23
          }
        }, {
          start: {
            line: 79,
            column: 27
          },
          end: {
            line: 79,
            column: 46
          }
        }],
        line: 79
      },
      "2": {
        loc: {
          start: {
            line: 82,
            column: 21
          },
          end: {
            line: 82,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 82,
            column: 21
          },
          end: {
            line: 82,
            column: 56
          }
        }, {
          start: {
            line: 82,
            column: 60
          },
          end: {
            line: 82,
            column: 64
          }
        }],
        line: 82
      },
      "3": {
        loc: {
          start: {
            line: 97,
            column: 4
          },
          end: {
            line: 97,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 97,
            column: 4
          },
          end: {
            line: 97,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 97
      },
      "4": {
        loc: {
          start: {
            line: 109,
            column: 6
          },
          end: {
            line: 111,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 109,
            column: 6
          },
          end: {
            line: 111,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 109
      },
      "5": {
        loc: {
          start: {
            line: 113,
            column: 6
          },
          end: {
            line: 115,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 6
          },
          end: {
            line: 115,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "6": {
        loc: {
          start: {
            line: 117,
            column: 6
          },
          end: {
            line: 119,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 6
          },
          end: {
            line: 119,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "7": {
        loc: {
          start: {
            line: 134,
            column: 23
          },
          end: {
            line: 134,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 134,
            column: 23
          },
          end: {
            line: 134,
            column: 41
          }
        }, {
          start: {
            line: 134,
            column: 45
          },
          end: {
            line: 134,
            column: 63
          }
        }],
        line: 134
      },
      "8": {
        loc: {
          start: {
            line: 139,
            column: 17
          },
          end: {
            line: 139,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 139,
            column: 17
          },
          end: {
            line: 139,
            column: 38
          }
        }, {
          start: {
            line: 139,
            column: 42
          },
          end: {
            line: 139,
            column: 48
          }
        }],
        line: 139
      },
      "9": {
        loc: {
          start: {
            line: 141,
            column: 16
          },
          end: {
            line: 141,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 141,
            column: 70
          },
          end: {
            line: 141,
            column: 75
          }
        }, {
          start: {
            line: 141,
            column: 78
          },
          end: {
            line: 141,
            column: 84
          }
        }],
        line: 141
      },
      "10": {
        loc: {
          start: {
            line: 149,
            column: 15
          },
          end: {
            line: 149,
            column: 77
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 149,
            column: 38
          },
          end: {
            line: 149,
            column: 49
          }
        }, {
          start: {
            line: 149,
            column: 52
          },
          end: {
            line: 149,
            column: 77
          }
        }],
        line: 149
      },
      "11": {
        loc: {
          start: {
            line: 160,
            column: 4
          },
          end: {
            line: 160,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 160,
            column: 4
          },
          end: {
            line: 160,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 160
      },
      "12": {
        loc: {
          start: {
            line: 170,
            column: 15
          },
          end: {
            line: 170,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 170,
            column: 38
          },
          end: {
            line: 170,
            column: 49
          }
        }, {
          start: {
            line: 170,
            column: 52
          },
          end: {
            line: 170,
            column: 91
          }
        }],
        line: 170
      },
      "13": {
        loc: {
          start: {
            line: 181,
            column: 4
          },
          end: {
            line: 181,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 181,
            column: 4
          },
          end: {
            line: 181,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 181
      },
      "14": {
        loc: {
          start: {
            line: 187,
            column: 6
          },
          end: {
            line: 189,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 187,
            column: 6
          },
          end: {
            line: 189,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 187
      },
      "15": {
        loc: {
          start: {
            line: 187,
            column: 10
          },
          end: {
            line: 187,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 187,
            column: 10
          },
          end: {
            line: 187,
            column: 37
          }
        }, {
          start: {
            line: 187,
            column: 41
          },
          end: {
            line: 187,
            column: 80
          }
        }],
        line: 187
      },
      "16": {
        loc: {
          start: {
            line: 204,
            column: 4
          },
          end: {
            line: 204,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 4
          },
          end: {
            line: 204,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 204
      },
      "17": {
        loc: {
          start: {
            line: 214,
            column: 6
          },
          end: {
            line: 214,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 214,
            column: 6
          },
          end: {
            line: 214,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 214
      },
      "18": {
        loc: {
          start: {
            line: 227,
            column: 6
          },
          end: {
            line: 229,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 227,
            column: 6
          },
          end: {
            line: 229,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 227
      },
      "19": {
        loc: {
          start: {
            line: 227,
            column: 10
          },
          end: {
            line: 227,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 227,
            column: 10
          },
          end: {
            line: 227,
            column: 20
          }
        }, {
          start: {
            line: 227,
            column: 24
          },
          end: {
            line: 227,
            column: 46
          }
        }],
        line: 227
      },
      "20": {
        loc: {
          start: {
            line: 254,
            column: 13
          },
          end: {
            line: 254,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 254,
            column: 13
          },
          end: {
            line: 254,
            column: 20
          }
        }, {
          start: {
            line: 254,
            column: 24
          },
          end: {
            line: 254,
            column: 35
          }
        }],
        line: 254
      },
      "21": {
        loc: {
          start: {
            line: 255,
            column: 11
          },
          end: {
            line: 255,
            column: 29
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 255,
            column: 11
          },
          end: {
            line: 255,
            column: 16
          }
        }, {
          start: {
            line: 255,
            column: 20
          },
          end: {
            line: 255,
            column: 29
          }
        }],
        line: 255
      },
      "22": {
        loc: {
          start: {
            line: 300,
            column: 15
          },
          end: {
            line: 300,
            column: 28
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 300,
            column: 15
          },
          end: {
            line: 300,
            column: 22
          }
        }, {
          start: {
            line: 300,
            column: 26
          },
          end: {
            line: 300,
            column: 28
          }
        }],
        line: 300
      },
      "23": {
        loc: {
          start: {
            line: 301,
            column: 16
          },
          end: {
            line: 301,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 301,
            column: 16
          },
          end: {
            line: 301,
            column: 24
          }
        }, {
          start: {
            line: 301,
            column: 28
          },
          end: {
            line: 301,
            column: 30
          }
        }],
        line: 301
      },
      "24": {
        loc: {
          start: {
            line: 302,
            column: 18
          },
          end: {
            line: 302,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 302,
            column: 18
          },
          end: {
            line: 302,
            column: 28
          }
        }, {
          start: {
            line: 302,
            column: 32
          },
          end: {
            line: 302,
            column: 34
          }
        }],
        line: 302
      },
      "25": {
        loc: {
          start: {
            line: 308,
            column: 29
          },
          end: {
            line: 310,
            column: 10
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 309,
            column: 8
          },
          end: {
            line: 309,
            column: 45
          }
        }, {
          start: {
            line: 310,
            column: 8
          },
          end: {
            line: 310,
            column: 10
          }
        }],
        line: 308
      },
      "26": {
        loc: {
          start: {
            line: 319,
            column: 17
          },
          end: {
            line: 319,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 319,
            column: 17
          },
          end: {
            line: 319,
            column: 43
          }
        }, {
          start: {
            line: 319,
            column: 47
          },
          end: {
            line: 319,
            column: 48
          }
        }],
        line: 319
      },
      "27": {
        loc: {
          start: {
            line: 320,
            column: 16
          },
          end: {
            line: 320,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 320,
            column: 16
          },
          end: {
            line: 320,
            column: 41
          }
        }, {
          start: {
            line: 320,
            column: 45
          },
          end: {
            line: 320,
            column: 46
          }
        }],
        line: 320
      },
      "28": {
        loc: {
          start: {
            line: 321,
            column: 30
          },
          end: {
            line: 321,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 321,
            column: 30
          },
          end: {
            line: 321,
            column: 69
          }
        }, {
          start: {
            line: 321,
            column: 73
          },
          end: {
            line: 321,
            column: 74
          }
        }],
        line: 321
      },
      "29": {
        loc: {
          start: {
            line: 342,
            column: 20
          },
          end: {
            line: 342,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 342,
            column: 46
          },
          end: {
            line: 342,
            column: 86
          }
        }, {
          start: {
            line: 342,
            column: 89
          },
          end: {
            line: 342,
            column: 90
          }
        }],
        line: 342
      },
      "30": {
        loc: {
          start: {
            line: 344,
            column: 4
          },
          end: {
            line: 349,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 344,
            column: 4
          },
          end: {
            line: 349,
            column: 5
          }
        }, {
          start: {
            line: 346,
            column: 11
          },
          end: {
            line: 349,
            column: 5
          }
        }],
        line: 344
      },
      "31": {
        loc: {
          start: {
            line: 346,
            column: 11
          },
          end: {
            line: 349,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 346,
            column: 11
          },
          end: {
            line: 349,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 346
      },
      "32": {
        loc: {
          start: {
            line: 355,
            column: 4
          },
          end: {
            line: 357,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 355,
            column: 4
          },
          end: {
            line: 357,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 355
      },
      "33": {
        loc: {
          start: {
            line: 359,
            column: 4
          },
          end: {
            line: 362,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 359,
            column: 4
          },
          end: {
            line: 362,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 359
      },
      "34": {
        loc: {
          start: {
            line: 404,
            column: 4
          },
          end: {
            line: 406,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 404,
            column: 4
          },
          end: {
            line: 406,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 404
      },
      "35": {
        loc: {
          start: {
            line: 408,
            column: 4
          },
          end: {
            line: 410,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 408,
            column: 4
          },
          end: {
            line: 410,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 408
      },
      "36": {
        loc: {
          start: {
            line: 412,
            column: 4
          },
          end: {
            line: 414,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 412,
            column: 4
          },
          end: {
            line: 414,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 412
      },
      "37": {
        loc: {
          start: {
            line: 432,
            column: 18
          },
          end: {
            line: 432,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 432,
            column: 72
          },
          end: {
            line: 432,
            column: 77
          }
        }, {
          start: {
            line: 432,
            column: 80
          },
          end: {
            line: 432,
            column: 86
          }
        }],
        line: 432
      },
      "38": {
        loc: {
          start: {
            line: 455,
            column: 6
          },
          end: {
            line: 463,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 455,
            column: 6
          },
          end: {
            line: 463,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 455
      },
      "39": {
        loc: {
          start: {
            line: 458,
            column: 26
          },
          end: {
            line: 458,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 458,
            column: 26
          },
          end: {
            line: 458,
            column: 43
          }
        }, {
          start: {
            line: 458,
            column: 47
          },
          end: {
            line: 458,
            column: 49
          }
        }],
        line: 458
      },
      "40": {
        loc: {
          start: {
            line: 459,
            column: 25
          },
          end: {
            line: 459,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 459,
            column: 25
          },
          end: {
            line: 459,
            column: 42
          }
        }, {
          start: {
            line: 459,
            column: 46
          },
          end: {
            line: 459,
            column: 48
          }
        }],
        line: 459
      },
      "41": {
        loc: {
          start: {
            line: 465,
            column: 29
          },
          end: {
            line: 465,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 465,
            column: 29
          },
          end: {
            line: 465,
            column: 62
          }
        }, {
          start: {
            line: 465,
            column: 66
          },
          end: {
            line: 465,
            column: 68
          }
        }],
        line: 465
      },
      "42": {
        loc: {
          start: {
            line: 466,
            column: 28
          },
          end: {
            line: 466,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 466,
            column: 28
          },
          end: {
            line: 466,
            column: 61
          }
        }, {
          start: {
            line: 466,
            column: 65
          },
          end: {
            line: 466,
            column: 67
          }
        }],
        line: 466
      },
      "43": {
        loc: {
          start: {
            line: 473,
            column: 15
          },
          end: {
            line: 473,
            column: 119
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 473,
            column: 33
          },
          end: {
            line: 473,
            column: 53
          }
        }, {
          start: {
            line: 473,
            column: 57
          },
          end: {
            line: 473,
            column: 119
          }
        }],
        line: 473
      },
      "44": {
        loc: {
          start: {
            line: 473,
            column: 57
          },
          end: {
            line: 473,
            column: 119
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 473,
            column: 76
          },
          end: {
            line: 473,
            column: 96
          }
        }, {
          start: {
            line: 473,
            column: 101
          },
          end: {
            line: 473,
            column: 118
          }
        }],
        line: 473
      },
      "45": {
        loc: {
          start: {
            line: 485,
            column: 6
          },
          end: {
            line: 492,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 485,
            column: 6
          },
          end: {
            line: 492,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 485
      },
      "46": {
        loc: {
          start: {
            line: 495,
            column: 37
          },
          end: {
            line: 495,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 495,
            column: 37
          },
          end: {
            line: 495,
            column: 58
          }
        }, {
          start: {
            line: 495,
            column: 62
          },
          end: {
            line: 495,
            column: 64
          }
        }],
        line: 495
      },
      "47": {
        loc: {
          start: {
            line: 496,
            column: 6
          },
          end: {
            line: 500,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 496,
            column: 6
          },
          end: {
            line: 500,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 496
      },
      "48": {
        loc: {
          start: {
            line: 515,
            column: 4
          },
          end: {
            line: 525,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 516,
            column: 6
          },
          end: {
            line: 518,
            column: 14
          }
        }, {
          start: {
            line: 519,
            column: 6
          },
          end: {
            line: 521,
            column: 14
          }
        }, {
          start: {
            line: 522,
            column: 6
          },
          end: {
            line: 524,
            column: 14
          }
        }],
        line: 515
      },
      "49": {
        loc: {
          start: {
            line: 549,
            column: 6
          },
          end: {
            line: 554,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 549,
            column: 6
          },
          end: {
            line: 554,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 549
      },
      "50": {
        loc: {
          start: {
            line: 551,
            column: 50
          },
          end: {
            line: 551,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 551,
            column: 50
          },
          end: {
            line: 551,
            column: 73
          }
        }, {
          start: {
            line: 551,
            column: 77
          },
          end: {
            line: 551,
            column: 78
          }
        }],
        line: 551
      },
      "51": {
        loc: {
          start: {
            line: 557,
            column: 4
          },
          end: {
            line: 557,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 557,
            column: 4
          },
          end: {
            line: 557,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 557
      },
      "52": {
        loc: {
          start: {
            line: 570,
            column: 24
          },
          end: {
            line: 570,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 570,
            column: 24
          },
          end: {
            line: 570,
            column: 40
          }
        }, {
          start: {
            line: 570,
            column: 44
          },
          end: {
            line: 570,
            column: 45
          }
        }],
        line: 570
      },
      "53": {
        loc: {
          start: {
            line: 571,
            column: 24
          },
          end: {
            line: 571,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 571,
            column: 24
          },
          end: {
            line: 571,
            column: 43
          }
        }, {
          start: {
            line: 571,
            column: 47
          },
          end: {
            line: 571,
            column: 48
          }
        }],
        line: 571
      },
      "54": {
        loc: {
          start: {
            line: 587,
            column: 4
          },
          end: {
            line: 587,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 587,
            column: 4
          },
          end: {
            line: 587,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 587
      },
      "55": {
        loc: {
          start: {
            line: 588,
            column: 4
          },
          end: {
            line: 588,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 588,
            column: 4
          },
          end: {
            line: 588,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 588
      },
      "56": {
        loc: {
          start: {
            line: 589,
            column: 4
          },
          end: {
            line: 589,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 589,
            column: 4
          },
          end: {
            line: 589,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 589
      },
      "57": {
        loc: {
          start: {
            line: 590,
            column: 4
          },
          end: {
            line: 590,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 590,
            column: 4
          },
          end: {
            line: 590,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 590
      },
      "58": {
        loc: {
          start: {
            line: 591,
            column: 4
          },
          end: {
            line: 591,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 591,
            column: 4
          },
          end: {
            line: 591,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 591
      },
      "59": {
        loc: {
          start: {
            line: 592,
            column: 4
          },
          end: {
            line: 592,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 592,
            column: 4
          },
          end: {
            line: 592,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 592
      },
      "60": {
        loc: {
          start: {
            line: 593,
            column: 4
          },
          end: {
            line: 593,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 593,
            column: 4
          },
          end: {
            line: 593,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 593
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "2e7d3860aa22cc44bc6eda0ab2090325990599d0"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_6ubufw8oh = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_6ubufw8oh();
import { useState, useCallback, useMemo } from 'react';
import { useAuth } from "../contexts/AuthContext";
import { supabase } from "../lib/supabase";
import { usePerformanceData, usePerformanceMetrics } from "./optimized/usePerformanceData";
import { usePerformanceAnalysis, useMatchAnalysis } from "./optimized/usePerformanceAnalysis";
import { optimizedDatabaseService } from "../services/optimized/databaseService";
export function usePerformanceAnalytics() {
  cov_6ubufw8oh().f[0]++;
  var _ref = (cov_6ubufw8oh().s[0]++, useState(null)),
    _ref2 = _slicedToArray(_ref, 2),
    matchAnalysis = _ref2[0],
    setMatchAnalysis = _ref2[1];
  var _ref3 = (cov_6ubufw8oh().s[1]++, useState(false)),
    _ref4 = _slicedToArray(_ref3, 2),
    loading = _ref4[0],
    setLoading = _ref4[1];
  var _ref5 = (cov_6ubufw8oh().s[2]++, useState(null)),
    _ref6 = _slicedToArray(_ref5, 2),
    error = _ref6[0],
    setError = _ref6[1];
  var _ref7 = (cov_6ubufw8oh().s[3]++, useAuth()),
    user = _ref7.user;
  var _ref8 = (cov_6ubufw8oh().s[4]++, usePerformanceData({
      enableCache: true,
      cacheTimeout: 300000,
      autoRefresh: true,
      refreshInterval: 600000
    })),
    rawPerformanceData = _ref8.data,
    dataLoading = _ref8.loading,
    dataError = _ref8.error,
    fetchData = _ref8.fetchData,
    refreshData = _ref8.refreshData;
  var performanceMetrics = (cov_6ubufw8oh().s[5]++, usePerformanceMetrics(rawPerformanceData));
  var analysisResult = (cov_6ubufw8oh().s[6]++, usePerformanceAnalysis(rawPerformanceData, {
    enableTrends: true,
    enableProjections: true,
    enableRecommendations: true,
    analysisDepth: 'detailed'
  }));
  var analyzeMatchOptimized = (cov_6ubufw8oh().s[7]++, useMatchAnalysis());
  var performanceData = (cov_6ubufw8oh().s[8]++, useMemo(function () {
    cov_6ubufw8oh().f[1]++;
    cov_6ubufw8oh().s[9]++;
    if ((cov_6ubufw8oh().b[1][0]++, !analysisResult) || (cov_6ubufw8oh().b[1][1]++, !performanceMetrics)) {
      cov_6ubufw8oh().b[0][0]++;
      cov_6ubufw8oh().s[10]++;
      return null;
    } else {
      cov_6ubufw8oh().b[0][1]++;
    }
    cov_6ubufw8oh().s[11]++;
    return {
      matchAnalysis: (cov_6ubufw8oh().b[2][0]++, performanceMetrics.recentMatches[0]) || (cov_6ubufw8oh().b[2][1]++, null),
      trends: analysisResult.trends,
      insights: {
        strengths: analysisResult.strengths,
        weaknesses: analysisResult.weaknesses,
        recommendations: analysisResult.recommendations
      },
      projections: analysisResult.projections
    };
  }, [analysisResult, performanceMetrics]));
  var analyzeMatch = (cov_6ubufw8oh().s[12]++, useCallback(function () {
    var _ref9 = _asyncToGenerator(function* (matchStats, opponentInfo) {
      cov_6ubufw8oh().f[2]++;
      cov_6ubufw8oh().s[13]++;
      if (!user) {
        cov_6ubufw8oh().b[3][0]++;
        cov_6ubufw8oh().s[14]++;
        return;
      } else {
        cov_6ubufw8oh().b[3][1]++;
      }
      cov_6ubufw8oh().s[15]++;
      try {
        cov_6ubufw8oh().s[16]++;
        setLoading(true);
        cov_6ubufw8oh().s[17]++;
        setError(null);
        var _analysisResult = (cov_6ubufw8oh().s[18]++, yield analyzeMatchOptimized(matchStats, opponentInfo));
        var recommendations = (cov_6ubufw8oh().s[19]++, []);
        cov_6ubufw8oh().s[20]++;
        if (_analysisResult.overallRating < 60) {
          cov_6ubufw8oh().b[4][0]++;
          cov_6ubufw8oh().s[21]++;
          recommendations.push('Focus on fundamental technique improvement');
        } else {
          cov_6ubufw8oh().b[4][1]++;
        }
        cov_6ubufw8oh().s[22]++;
        if (_analysisResult.detailedMetrics.errorRate > 30) {
          cov_6ubufw8oh().b[5][0]++;
          cov_6ubufw8oh().s[23]++;
          recommendations.push('Work on reducing unforced errors');
        } else {
          cov_6ubufw8oh().b[5][1]++;
        }
        cov_6ubufw8oh().s[24]++;
        if (_analysisResult.detailedMetrics.winnerRate < 15) {
          cov_6ubufw8oh().b[6][0]++;
          cov_6ubufw8oh().s[25]++;
          recommendations.push('Practice aggressive shot-making');
        } else {
          cov_6ubufw8oh().b[6][1]++;
        }
        var result = (cov_6ubufw8oh().s[26]++, {
          overallRating: _analysisResult.overallRating,
          detailedMetrics: _analysisResult.detailedMetrics,
          tacticalInsights: _analysisResult.tacticalInsights,
          fitnessAnalysis: _analysisResult.fitnessAnalysis,
          recommendations: recommendations
        });
        cov_6ubufw8oh().s[27]++;
        setMatchAnalysis(result);
        cov_6ubufw8oh().s[28]++;
        yield optimizedDatabaseService.batchInsert('match_results', [{
          user_id: user.id,
          opponent_name: (cov_6ubufw8oh().b[7][0]++, opponentInfo == null ? void 0 : opponentInfo.name) || (cov_6ubufw8oh().b[7][1]++, 'Analysis Session'),
          opponent_type: 'ai',
          match_score: `${matchStats.pointsWon}-${matchStats.totalPoints - matchStats.pointsWon}`,
          sets: [matchStats.pointsWon],
          opponent_sets: [matchStats.totalPoints - matchStats.pointsWon],
          surface: (cov_6ubufw8oh().b[8][0]++, opponentInfo == null ? void 0 : opponentInfo.surface) || (cov_6ubufw8oh().b[8][1]++, 'hard'),
          duration_minutes: matchStats.totalGameTime,
          result: matchStats.pointsWon > matchStats.totalPoints / 2 ? (cov_6ubufw8oh().b[9][0]++, 'win') : (cov_6ubufw8oh().b[9][1]++, 'loss'),
          match_stats: Object.assign({}, matchStats, {
            analysis: result
          })
        }]);
      } catch (err) {
        cov_6ubufw8oh().s[29]++;
        setError(err instanceof Error ? (cov_6ubufw8oh().b[10][0]++, err.message) : (cov_6ubufw8oh().b[10][1]++, 'Failed to analyze match'));
        cov_6ubufw8oh().s[30]++;
        console.error('Match analysis error:', err);
      } finally {
        cov_6ubufw8oh().s[31]++;
        setLoading(false);
      }
    });
    return function (_x, _x2) {
      return _ref9.apply(this, arguments);
    };
  }(), [user, analyzeMatchOptimized]));
  var generatePerformanceReport = (cov_6ubufw8oh().s[32]++, useCallback(_asyncToGenerator(function* () {
    cov_6ubufw8oh().f[3]++;
    cov_6ubufw8oh().s[33]++;
    if (!user) {
      cov_6ubufw8oh().b[11][0]++;
      cov_6ubufw8oh().s[34]++;
      return;
    } else {
      cov_6ubufw8oh().b[11][1]++;
    }
    cov_6ubufw8oh().s[35]++;
    try {
      cov_6ubufw8oh().s[36]++;
      setLoading(true);
      cov_6ubufw8oh().s[37]++;
      setError(null);
      cov_6ubufw8oh().s[38]++;
      yield fetchData(true);
    } catch (err) {
      cov_6ubufw8oh().s[39]++;
      setError(err instanceof Error ? (cov_6ubufw8oh().b[12][0]++, err.message) : (cov_6ubufw8oh().b[12][1]++, 'Failed to generate performance report'));
      cov_6ubufw8oh().s[40]++;
      console.error('Performance report error:', err);
    } finally {
      cov_6ubufw8oh().s[41]++;
      setLoading(false);
    }
  }), [user, fetchData]));
  var getSkillProgression = (cov_6ubufw8oh().s[42]++, useCallback(function () {
    var _ref1 = _asyncToGenerator(function* (timeframe) {
      cov_6ubufw8oh().f[4]++;
      cov_6ubufw8oh().s[43]++;
      if (!user) {
        cov_6ubufw8oh().b[13][0]++;
        cov_6ubufw8oh().s[44]++;
        return null;
      } else {
        cov_6ubufw8oh().b[13][1]++;
      }
      cov_6ubufw8oh().s[45]++;
      try {
        var _performanceData = (cov_6ubufw8oh().s[46]++, yield optimizedDatabaseService.getPerformanceData(user.id, timeframe));
        cov_6ubufw8oh().s[47]++;
        if ((cov_6ubufw8oh().b[15][0]++, !_performanceData.skillStats) || (cov_6ubufw8oh().b[15][1]++, _performanceData.skillStats.length === 0)) {
          cov_6ubufw8oh().b[14][0]++;
          cov_6ubufw8oh().s[48]++;
          return null;
        } else {
          cov_6ubufw8oh().b[14][1]++;
        }
        cov_6ubufw8oh().s[49]++;
        return processSkillProgression(_performanceData.skillStats, timeframe);
      } catch (error) {
        cov_6ubufw8oh().s[50]++;
        console.error('Error fetching skill progression:', error);
        cov_6ubufw8oh().s[51]++;
        return null;
      }
    });
    return function (_x3) {
      return _ref1.apply(this, arguments);
    };
  }(), [user]));
  var compareWithPeers = (cov_6ubufw8oh().s[52]++, useCallback(function () {
    var _ref10 = _asyncToGenerator(function* (skillLevel) {
      cov_6ubufw8oh().f[5]++;
      cov_6ubufw8oh().s[53]++;
      if (!user) {
        cov_6ubufw8oh().b[16][0]++;
        cov_6ubufw8oh().s[54]++;
        return null;
      } else {
        cov_6ubufw8oh().b[16][1]++;
      }
      cov_6ubufw8oh().s[55]++;
      try {
        var _ref11 = (cov_6ubufw8oh().s[56]++, yield supabase.from('skill_stats').select('*').eq('user_id', user.id).single()),
          userStats = _ref11.data;
        cov_6ubufw8oh().s[57]++;
        if (!userStats) {
          cov_6ubufw8oh().b[17][0]++;
          cov_6ubufw8oh().s[58]++;
          return null;
        } else {
          cov_6ubufw8oh().b[17][1]++;
        }
        var _ref12 = (cov_6ubufw8oh().s[59]++, yield supabase.from('users').select(`
          skill_stats (
            forehand, backhand, serve, volley, footwork, strategy, mental_game
          )
        `).eq('skill_level', skillLevel).limit(50)),
          peerStats = _ref12.data;
        cov_6ubufw8oh().s[60]++;
        if ((cov_6ubufw8oh().b[19][0]++, !peerStats) || (cov_6ubufw8oh().b[19][1]++, peerStats.length === 0)) {
          cov_6ubufw8oh().b[18][0]++;
          cov_6ubufw8oh().s[61]++;
          return null;
        } else {
          cov_6ubufw8oh().b[18][1]++;
        }
        var peerAverages = (cov_6ubufw8oh().s[62]++, calculatePeerAverages(peerStats));
        cov_6ubufw8oh().s[63]++;
        return generatePeerComparison(userStats, peerAverages);
      } catch (error) {
        cov_6ubufw8oh().s[64]++;
        console.error('Error comparing with peers:', error);
        cov_6ubufw8oh().s[65]++;
        return null;
      }
    });
    return function (_x4) {
      return _ref10.apply(this, arguments);
    };
  }(), [user]));
  var refreshAnalytics = (cov_6ubufw8oh().s[66]++, useCallback(_asyncToGenerator(function* () {
    cov_6ubufw8oh().f[6]++;
    cov_6ubufw8oh().s[67]++;
    yield refreshData();
  }), [refreshData]));
  cov_6ubufw8oh().s[68]++;
  return useMemo(function () {
    cov_6ubufw8oh().f[7]++;
    cov_6ubufw8oh().s[69]++;
    return {
      performanceData: performanceData,
      matchAnalysis: matchAnalysis,
      loading: (cov_6ubufw8oh().b[20][0]++, loading) || (cov_6ubufw8oh().b[20][1]++, dataLoading),
      error: (cov_6ubufw8oh().b[21][0]++, error) || (cov_6ubufw8oh().b[21][1]++, dataError),
      analyzeMatch: analyzeMatch,
      generatePerformanceReport: generatePerformanceReport,
      getSkillProgression: getSkillProgression,
      compareWithPeers: compareWithPeers,
      refreshAnalytics: refreshAnalytics
    };
  }, [performanceData, matchAnalysis, loading, dataLoading, error, dataError, analyzeMatch, generatePerformanceReport, getSkillProgression, compareWithPeers, refreshAnalytics]);
  var fetchRecentPerformanceData = (cov_6ubufw8oh().s[70]++, useCallback(function () {
    var _ref14 = _asyncToGenerator(function* (userId) {
      cov_6ubufw8oh().f[8]++;
      var _ref15 = (cov_6ubufw8oh().s[71]++, yield supabase.from('match_results').select('*').eq('user_id', userId).order('created_at', {
          ascending: false
        }).limit(10)),
        matches = _ref15.data;
      var _ref16 = (cov_6ubufw8oh().s[72]++, yield supabase.from('training_sessions').select('*').eq('user_id', userId).order('created_at', {
          ascending: false
        }).limit(20)),
        sessions = _ref16.data;
      var _ref17 = (cov_6ubufw8oh().s[73]++, yield supabase.from('skill_stats').select('*').eq('user_id', userId).order('updated_at', {
          ascending: false
        }).limit(5)),
        skillStats = _ref17.data;
      cov_6ubufw8oh().s[74]++;
      return {
        matches: (cov_6ubufw8oh().b[22][0]++, matches) || (cov_6ubufw8oh().b[22][1]++, []),
        sessions: (cov_6ubufw8oh().b[23][0]++, sessions) || (cov_6ubufw8oh().b[23][1]++, []),
        skillStats: (cov_6ubufw8oh().b[24][0]++, skillStats) || (cov_6ubufw8oh().b[24][1]++, [])
      };
    });
    return function (_x5) {
      return _ref14.apply(this, arguments);
    };
  }(), []));
  var analyzeTrends = (cov_6ubufw8oh().s[75]++, useCallback(function (data) {
    cov_6ubufw8oh().f[9]++;
    var skillProgression = (cov_6ubufw8oh().s[76]++, data.skillStats.length > 1 ? (cov_6ubufw8oh().b[25][0]++, calculateSkillTrends(data.skillStats)) : (cov_6ubufw8oh().b[25][1]++, []));
    var matchPerformance = (cov_6ubufw8oh().s[77]++, data.matches.map(function (match) {
      var _match$match_stats, _match$match_stats2, _match$match_stats3;
      cov_6ubufw8oh().f[10]++;
      cov_6ubufw8oh().s[78]++;
      return {
        date: match.created_at,
        opponent: match.opponent_name,
        result: match.result,
        score: match.match_score,
        keyMetrics: {
          winners: (cov_6ubufw8oh().b[26][0]++, (_match$match_stats = match.match_stats) == null ? void 0 : _match$match_stats.winners) || (cov_6ubufw8oh().b[26][1]++, 0),
          errors: (cov_6ubufw8oh().b[27][0]++, (_match$match_stats2 = match.match_stats) == null ? void 0 : _match$match_stats2.errors) || (cov_6ubufw8oh().b[27][1]++, 0),
          firstServePercentage: (cov_6ubufw8oh().b[28][0]++, (_match$match_stats3 = match.match_stats) == null ? void 0 : _match$match_stats3.firstServePercentage) || (cov_6ubufw8oh().b[28][1]++, 0)
        }
      };
    }));
    var weeklyProgress = (cov_6ubufw8oh().s[79]++, calculateWeeklyProgress(data.sessions));
    cov_6ubufw8oh().s[80]++;
    return {
      skillProgression: skillProgression,
      matchPerformance: matchPerformance,
      weeklyProgress: weeklyProgress
    };
  }, []));
  var generateInsights = (cov_6ubufw8oh().s[81]++, useCallback(function (data, trends) {
    cov_6ubufw8oh().f[11]++;
    var strengths = (cov_6ubufw8oh().s[82]++, []);
    var weaknesses = (cov_6ubufw8oh().s[83]++, []);
    var recommendations = (cov_6ubufw8oh().s[84]++, []);
    var recentWins = (cov_6ubufw8oh().s[85]++, data.matches.filter(function (m) {
      cov_6ubufw8oh().f[12]++;
      cov_6ubufw8oh().s[86]++;
      return m.result === 'win';
    }).length);
    var winRate = (cov_6ubufw8oh().s[87]++, data.matches.length > 0 ? (cov_6ubufw8oh().b[29][0]++, recentWins / data.matches.length * 100) : (cov_6ubufw8oh().b[29][1]++, 0));
    cov_6ubufw8oh().s[88]++;
    if (winRate > 60) {
      cov_6ubufw8oh().b[30][0]++;
      cov_6ubufw8oh().s[89]++;
      strengths.push('Strong recent match performance');
    } else {
      cov_6ubufw8oh().b[30][1]++;
      cov_6ubufw8oh().s[90]++;
      if (winRate < 40) {
        cov_6ubufw8oh().b[31][0]++;
        cov_6ubufw8oh().s[91]++;
        weaknesses.push('Struggling in recent matches');
        cov_6ubufw8oh().s[92]++;
        recommendations.push('Focus on match-specific training');
      } else {
        cov_6ubufw8oh().b[31][1]++;
      }
    }
    var improvingSkills = (cov_6ubufw8oh().s[93]++, trends.skillProgression.filter(function (s) {
      cov_6ubufw8oh().f[13]++;
      cov_6ubufw8oh().s[94]++;
      return s.trend === 'improving';
    }));
    var decliningSkills = (cov_6ubufw8oh().s[95]++, trends.skillProgression.filter(function (s) {
      cov_6ubufw8oh().f[14]++;
      cov_6ubufw8oh().s[96]++;
      return s.trend === 'declining';
    }));
    cov_6ubufw8oh().s[97]++;
    if (improvingSkills.length > 0) {
      cov_6ubufw8oh().b[32][0]++;
      cov_6ubufw8oh().s[98]++;
      strengths.push(`Improving in ${improvingSkills.map(function (s) {
        cov_6ubufw8oh().f[15]++;
        cov_6ubufw8oh().s[99]++;
        return s.skill;
      }).join(', ')}`);
    } else {
      cov_6ubufw8oh().b[32][1]++;
    }
    cov_6ubufw8oh().s[100]++;
    if (decliningSkills.length > 0) {
      cov_6ubufw8oh().b[33][0]++;
      cov_6ubufw8oh().s[101]++;
      weaknesses.push(`Declining in ${decliningSkills.map(function (s) {
        cov_6ubufw8oh().f[16]++;
        cov_6ubufw8oh().s[102]++;
        return s.skill;
      }).join(', ')}`);
      cov_6ubufw8oh().s[103]++;
      recommendations.push(`Focus practice on ${decliningSkills[0].skill}`);
    } else {
      cov_6ubufw8oh().b[33][1]++;
    }
    cov_6ubufw8oh().s[104]++;
    return {
      strengths: strengths,
      weaknesses: weaknesses,
      recommendations: recommendations
    };
  }, []));
  var generateProjections = (cov_6ubufw8oh().s[105]++, useCallback(function (trends) {
    cov_6ubufw8oh().f[17]++;
    var skillProgression = (cov_6ubufw8oh().s[106]++, trends.skillProgression.filter(function (s) {
      cov_6ubufw8oh().f[18]++;
      cov_6ubufw8oh().s[107]++;
      return s.trend === 'improving';
    }).map(function (s) {
      cov_6ubufw8oh().f[19]++;
      cov_6ubufw8oh().s[108]++;
      return {
        skill: s.skill,
        currentRating: s.currentRating,
        projectedRating: Math.min(100, s.currentRating + s.changeRate * 4),
        timeframe: '4 weeks'
      };
    }));
    var nextMilestones = (cov_6ubufw8oh().s[109]++, ['Reach 80% consistency in forehand', 'Improve serve percentage to 70%', 'Win next tournament match']);
    cov_6ubufw8oh().s[110]++;
    return {
      skillProgression: skillProgression,
      nextMilestones: nextMilestones
    };
  }, []));
  var generateSetPerformance = (cov_6ubufw8oh().s[111]++, useCallback(function (matchStats) {
    cov_6ubufw8oh().f[20]++;
    var sets = (cov_6ubufw8oh().s[112]++, 3);
    cov_6ubufw8oh().s[113]++;
    return Array.from({
      length: sets
    }, function (_, i) {
      cov_6ubufw8oh().f[21]++;
      cov_6ubufw8oh().s[114]++;
      return Object.assign({}, matchStats, {
        totalPoints: Math.floor(matchStats.totalPoints / sets),
        pointsWon: Math.floor(matchStats.pointsWon / sets) - i * 2,
        unforcedErrors: Math.floor(matchStats.unforcedErrors / sets) + i
      });
    });
  }, []));
  var generateMatchRecommendations = (cov_6ubufw8oh().s[115]++, useCallback(function (basicAnalysis, tacticalAnalysis, fitnessAnalysis) {
    cov_6ubufw8oh().f[22]++;
    var recommendations = (cov_6ubufw8oh().s[116]++, []);
    cov_6ubufw8oh().s[117]++;
    if (basicAnalysis.overallRating < 60) {
      cov_6ubufw8oh().b[34][0]++;
      cov_6ubufw8oh().s[118]++;
      recommendations.push('Focus on fundamental technique improvement');
    } else {
      cov_6ubufw8oh().b[34][1]++;
    }
    cov_6ubufw8oh().s[119]++;
    if ((tacticalAnalysis == null ? void 0 : tacticalAnalysis.tacticalEffectiveness) < 70) {
      cov_6ubufw8oh().b[35][0]++;
      cov_6ubufw8oh().s[120]++;
      recommendations.push('Work on match strategy and tactical awareness');
    } else {
      cov_6ubufw8oh().b[35][1]++;
    }
    cov_6ubufw8oh().s[121]++;
    if (fitnessAnalysis.enduranceRating < 60) {
      cov_6ubufw8oh().b[36][0]++;
      cov_6ubufw8oh().s[122]++;
      recommendations.push('Improve cardiovascular fitness for longer matches');
    } else {
      cov_6ubufw8oh().b[36][1]++;
    }
    cov_6ubufw8oh().s[123]++;
    return recommendations;
  }, []));
  var saveMatchAnalysis = (cov_6ubufw8oh().s[124]++, useCallback(function () {
    var _ref18 = _asyncToGenerator(function* (matchStats, analysis) {
      cov_6ubufw8oh().f[23]++;
      cov_6ubufw8oh().s[125]++;
      try {
        cov_6ubufw8oh().s[126]++;
        yield supabase.from('match_results').insert({
          user_id: user == null ? void 0 : user.id,
          opponent_name: 'Analysis Session',
          opponent_type: 'ai',
          match_score: `${matchStats.pointsWon}-${matchStats.totalPoints - matchStats.pointsWon}`,
          sets: [matchStats.pointsWon],
          opponent_sets: [matchStats.totalPoints - matchStats.pointsWon],
          surface: 'hard',
          duration_minutes: matchStats.totalGameTime,
          result: matchStats.pointsWon > matchStats.totalPoints / 2 ? (cov_6ubufw8oh().b[37][0]++, 'win') : (cov_6ubufw8oh().b[37][1]++, 'loss'),
          match_stats: Object.assign({}, matchStats, {
            analysis: analysis
          })
        });
      } catch (error) {
        cov_6ubufw8oh().s[127]++;
        console.error('Error saving match analysis:', error);
      }
    });
    return function (_x6, _x7) {
      return _ref18.apply(this, arguments);
    };
  }(), [user]));
  var calculateSkillTrends = (cov_6ubufw8oh().s[128]++, useCallback(function (skillStats) {
    cov_6ubufw8oh().f[24]++;
    var skills = (cov_6ubufw8oh().s[129]++, ['forehand', 'backhand', 'serve', 'volley', 'footwork', 'strategy', 'mental_game']);
    cov_6ubufw8oh().s[130]++;
    return skills.map(function (skill) {
      cov_6ubufw8oh().f[25]++;
      var values = (cov_6ubufw8oh().s[131]++, skillStats.map(function (s) {
        cov_6ubufw8oh().f[26]++;
        cov_6ubufw8oh().s[132]++;
        return s[skill];
      }).filter(function (v) {
        cov_6ubufw8oh().f[27]++;
        cov_6ubufw8oh().s[133]++;
        return v != null;
      }));
      cov_6ubufw8oh().s[134]++;
      if (values.length < 2) {
        cov_6ubufw8oh().b[38][0]++;
        cov_6ubufw8oh().s[135]++;
        return {
          skill: skill,
          previousRating: (cov_6ubufw8oh().b[39][0]++, Number(values[0])) || (cov_6ubufw8oh().b[39][1]++, 50),
          currentRating: (cov_6ubufw8oh().b[40][0]++, Number(values[0])) || (cov_6ubufw8oh().b[40][1]++, 50),
          trend: 'stable',
          changeRate: 0
        };
      } else {
        cov_6ubufw8oh().b[38][1]++;
      }
      var previousRating = (cov_6ubufw8oh().s[136]++, (cov_6ubufw8oh().b[41][0]++, Number(values[values.length - 2])) || (cov_6ubufw8oh().b[41][1]++, 50));
      var currentRating = (cov_6ubufw8oh().s[137]++, (cov_6ubufw8oh().b[42][0]++, Number(values[values.length - 1])) || (cov_6ubufw8oh().b[42][1]++, 50));
      var changeRate = (cov_6ubufw8oh().s[138]++, currentRating - previousRating);
      cov_6ubufw8oh().s[139]++;
      return {
        skill: skill,
        previousRating: previousRating,
        currentRating: currentRating,
        trend: changeRate > 2 ? (cov_6ubufw8oh().b[43][0]++, 'improving') : (cov_6ubufw8oh().b[43][1]++, changeRate < -2 ? (cov_6ubufw8oh().b[44][0]++, 'declining') : (cov_6ubufw8oh().b[44][1]++, 'stable')),
        changeRate: changeRate
      };
    });
  }, []));
  var calculateWeeklyProgress = (cov_6ubufw8oh().s[140]++, useCallback(function (sessions) {
    cov_6ubufw8oh().f[28]++;
    var weeklyData = (cov_6ubufw8oh().s[141]++, {});
    cov_6ubufw8oh().s[142]++;
    sessions.forEach(function (session) {
      cov_6ubufw8oh().f[29]++;
      var week = (cov_6ubufw8oh().s[143]++, new Date(session.created_at).toISOString().slice(0, 10));
      cov_6ubufw8oh().s[144]++;
      if (!weeklyData[week]) {
        cov_6ubufw8oh().b[45][0]++;
        cov_6ubufw8oh().s[145]++;
        weeklyData[week] = {
          week: week,
          sessionsCompleted: 0,
          totalScore: 0,
          improvementAreas: new Set()
        };
      } else {
        cov_6ubufw8oh().b[45][1]++;
      }
      cov_6ubufw8oh().s[146]++;
      weeklyData[week].sessionsCompleted++;
      cov_6ubufw8oh().s[147]++;
      weeklyData[week].totalScore += (cov_6ubufw8oh().b[46][0]++, session.overall_score) || (cov_6ubufw8oh().b[46][1]++, 75);
      cov_6ubufw8oh().s[148]++;
      if (session.improvement_areas) {
        cov_6ubufw8oh().b[47][0]++;
        cov_6ubufw8oh().s[149]++;
        session.improvement_areas.forEach(function (area) {
          cov_6ubufw8oh().f[30]++;
          cov_6ubufw8oh().s[150]++;
          return weeklyData[week].improvementAreas.add(area);
        });
      } else {
        cov_6ubufw8oh().b[47][1]++;
      }
    });
    cov_6ubufw8oh().s[151]++;
    return Object.values(weeklyData).map(function (week) {
      cov_6ubufw8oh().f[31]++;
      cov_6ubufw8oh().s[152]++;
      return Object.assign({}, week, {
        averageScore: Math.round(week.totalScore / week.sessionsCompleted),
        improvementAreas: Array.from(week.improvementAreas)
      });
    });
  }, []));
  var processSkillProgression = (cov_6ubufw8oh().s[153]++, useCallback(function (skillHistory, timeframe) {
    cov_6ubufw8oh().f[32]++;
    var now = (cov_6ubufw8oh().s[154]++, new Date());
    var cutoffDate = (cov_6ubufw8oh().s[155]++, new Date());
    cov_6ubufw8oh().s[156]++;
    switch (timeframe) {
      case 'week':
        cov_6ubufw8oh().b[48][0]++;
        cov_6ubufw8oh().s[157]++;
        cutoffDate.setDate(now.getDate() - 7);
        cov_6ubufw8oh().s[158]++;
        break;
      case 'month':
        cov_6ubufw8oh().b[48][1]++;
        cov_6ubufw8oh().s[159]++;
        cutoffDate.setMonth(now.getMonth() - 1);
        cov_6ubufw8oh().s[160]++;
        break;
      case 'year':
        cov_6ubufw8oh().b[48][2]++;
        cov_6ubufw8oh().s[161]++;
        cutoffDate.setFullYear(now.getFullYear() - 1);
        cov_6ubufw8oh().s[162]++;
        break;
    }
    cov_6ubufw8oh().s[163]++;
    return skillHistory.filter(function (stat) {
      cov_6ubufw8oh().f[33]++;
      cov_6ubufw8oh().s[164]++;
      return new Date(stat.updated_at) >= cutoffDate;
    }).map(function (stat) {
      cov_6ubufw8oh().f[34]++;
      cov_6ubufw8oh().s[165]++;
      return {
        date: stat.updated_at,
        forehand: stat.forehand,
        backhand: stat.backhand,
        serve: stat.serve,
        volley: stat.volley,
        footwork: stat.footwork,
        strategy: stat.strategy,
        mental_game: stat.mental_game
      };
    });
  }, []));
  var calculatePeerAverages = (cov_6ubufw8oh().s[166]++, useCallback(function (peerStats) {
    cov_6ubufw8oh().f[35]++;
    var totals = (cov_6ubufw8oh().s[167]++, {
      forehand: 0,
      backhand: 0,
      serve: 0,
      volley: 0,
      footwork: 0,
      strategy: 0,
      mental_game: 0
    });
    var count = (cov_6ubufw8oh().s[168]++, 0);
    cov_6ubufw8oh().s[169]++;
    peerStats.forEach(function (peer) {
      cov_6ubufw8oh().f[36]++;
      cov_6ubufw8oh().s[170]++;
      if (peer.skill_stats) {
        cov_6ubufw8oh().b[49][0]++;
        cov_6ubufw8oh().s[171]++;
        Object.keys(totals).forEach(function (skill) {
          cov_6ubufw8oh().f[37]++;
          cov_6ubufw8oh().s[172]++;
          totals[skill] += (cov_6ubufw8oh().b[50][0]++, peer.skill_stats[skill]) || (cov_6ubufw8oh().b[50][1]++, 0);
        });
        cov_6ubufw8oh().s[173]++;
        count++;
      } else {
        cov_6ubufw8oh().b[49][1]++;
      }
    });
    cov_6ubufw8oh().s[174]++;
    if (count === 0) {
      cov_6ubufw8oh().b[51][0]++;
      cov_6ubufw8oh().s[175]++;
      return totals;
    } else {
      cov_6ubufw8oh().b[51][1]++;
    }
    cov_6ubufw8oh().s[176]++;
    Object.keys(totals).forEach(function (skill) {
      cov_6ubufw8oh().f[38]++;
      cov_6ubufw8oh().s[177]++;
      totals[skill] = Math.round(totals[skill] / count);
    });
    cov_6ubufw8oh().s[178]++;
    return totals;
  }, []));
  var generatePeerComparison = (cov_6ubufw8oh().s[179]++, useCallback(function (userStats, peerAverages) {
    cov_6ubufw8oh().f[39]++;
    var comparison = (cov_6ubufw8oh().s[180]++, {});
    cov_6ubufw8oh().s[181]++;
    Object.keys(peerAverages).forEach(function (skill) {
      cov_6ubufw8oh().f[40]++;
      var userValue = (cov_6ubufw8oh().s[182]++, (cov_6ubufw8oh().b[52][0]++, userStats[skill]) || (cov_6ubufw8oh().b[52][1]++, 0));
      var peerValue = (cov_6ubufw8oh().s[183]++, (cov_6ubufw8oh().b[53][0]++, peerAverages[skill]) || (cov_6ubufw8oh().b[53][1]++, 0));
      var difference = (cov_6ubufw8oh().s[184]++, userValue - peerValue);
      cov_6ubufw8oh().s[185]++;
      comparison[skill] = {
        user: userValue,
        peer: peerValue,
        difference: difference,
        percentile: calculatePercentile(difference)
      };
    });
    cov_6ubufw8oh().s[186]++;
    return comparison;
  }, []));
  var calculatePercentile = (cov_6ubufw8oh().s[187]++, useCallback(function (difference) {
    cov_6ubufw8oh().f[41]++;
    cov_6ubufw8oh().s[188]++;
    if (difference > 15) {
      cov_6ubufw8oh().b[54][0]++;
      cov_6ubufw8oh().s[189]++;
      return 90;
    } else {
      cov_6ubufw8oh().b[54][1]++;
    }
    cov_6ubufw8oh().s[190]++;
    if (difference > 10) {
      cov_6ubufw8oh().b[55][0]++;
      cov_6ubufw8oh().s[191]++;
      return 80;
    } else {
      cov_6ubufw8oh().b[55][1]++;
    }
    cov_6ubufw8oh().s[192]++;
    if (difference > 5) {
      cov_6ubufw8oh().b[56][0]++;
      cov_6ubufw8oh().s[193]++;
      return 70;
    } else {
      cov_6ubufw8oh().b[56][1]++;
    }
    cov_6ubufw8oh().s[194]++;
    if (difference > 0) {
      cov_6ubufw8oh().b[57][0]++;
      cov_6ubufw8oh().s[195]++;
      return 60;
    } else {
      cov_6ubufw8oh().b[57][1]++;
    }
    cov_6ubufw8oh().s[196]++;
    if (difference > -5) {
      cov_6ubufw8oh().b[58][0]++;
      cov_6ubufw8oh().s[197]++;
      return 50;
    } else {
      cov_6ubufw8oh().b[58][1]++;
    }
    cov_6ubufw8oh().s[198]++;
    if (difference > -10) {
      cov_6ubufw8oh().b[59][0]++;
      cov_6ubufw8oh().s[199]++;
      return 40;
    } else {
      cov_6ubufw8oh().b[59][1]++;
    }
    cov_6ubufw8oh().s[200]++;
    if (difference > -15) {
      cov_6ubufw8oh().b[60][0]++;
      cov_6ubufw8oh().s[201]++;
      return 30;
    } else {
      cov_6ubufw8oh().b[60][1]++;
    }
    cov_6ubufw8oh().s[202]++;
    return 20;
  }, []));
  cov_6ubufw8oh().s[203]++;
  return {
    performanceData: performanceData,
    matchAnalysis: matchAnalysis,
    loading: loading,
    error: error,
    analyzeMatch: analyzeMatch,
    generatePerformanceReport: generatePerformanceReport,
    getSkillProgression: getSkillProgression,
    compareWithPeers: compareWithPeers,
    refreshAnalytics: refreshAnalytics
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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