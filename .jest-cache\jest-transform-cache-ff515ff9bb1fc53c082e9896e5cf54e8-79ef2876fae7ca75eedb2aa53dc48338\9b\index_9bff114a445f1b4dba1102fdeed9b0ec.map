{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "_extends2", "_objectWithoutPropertiesLoose2", "_react", "React", "_useMergeRefs", "_usePressEvents", "_StyleSheet", "_View", "_excluded", "TouchableOpacity", "props", "forwardedRef", "activeOpacity", "delayPressIn", "delayPressOut", "delayLongPress", "disabled", "focusable", "onLongPress", "onPress", "onPressIn", "onPressOut", "rejectResponderTermination", "style", "rest", "hostRef", "useRef", "setRef", "_useState", "useState", "duration", "setDuration", "_useState2", "opacityOverride", "setOpacityOverride", "setOpacityTo", "useCallback", "value", "setOpacityActive", "setOpacityInactive", "pressConfig", "useMemo", "cancelable", "delayPressStart", "delayPressEnd", "onPressStart", "event", "isGrant", "dispatchConfig", "registrationName", "type", "onPressEnd", "pressEventHandlers", "createElement", "accessibilityDisabled", "pointerEvents", "undefined", "ref", "styles", "root", "actionable", "opacity", "transitionDuration", "create", "transitionProperty", "userSelect", "cursor", "touchAction", "MemoedTouchableOpacity", "memo", "forwardRef", "displayName", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar React = _react;\nvar _useMergeRefs = _interopRequireDefault(require(\"../../modules/useMergeRefs\"));\nvar _usePressEvents = _interopRequireDefault(require(\"../../modules/usePressEvents\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _View = _interopRequireDefault(require(\"../View\"));\nvar _excluded = [\"activeOpacity\", \"delayPressIn\", \"delayPressOut\", \"delayLongPress\", \"disabled\", \"focusable\", \"onLongPress\", \"onPress\", \"onPressIn\", \"onPressOut\", \"rejectResponderTermination\", \"style\"];\n//import { warnOnce } from '../../modules/warnOnce';\n\n/**\n * A wrapper for making views respond properly to touches.\n * On press down, the opacity of the wrapped view is decreased, dimming it.\n */\nfunction TouchableOpacity(props, forwardedRef) {\n  /*\n  warnOnce(\n    'TouchableOpacity',\n    'TouchableOpacity is deprecated. Please use Pressable.'\n  );\n  */\n\n  var activeOpacity = props.activeOpacity,\n    delayPressIn = props.delayPressIn,\n    delayPressOut = props.delayPressOut,\n    delayLongPress = props.delayLongPress,\n    disabled = props.disabled,\n    focusable = props.focusable,\n    onLongPress = props.onLongPress,\n    onPress = props.onPress,\n    onPressIn = props.onPressIn,\n    onPressOut = props.onPressOut,\n    rejectResponderTermination = props.rejectResponderTermination,\n    style = props.style,\n    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  var hostRef = (0, _react.useRef)(null);\n  var setRef = (0, _useMergeRefs.default)(forwardedRef, hostRef);\n  var _useState = (0, _react.useState)('0s'),\n    duration = _useState[0],\n    setDuration = _useState[1];\n  var _useState2 = (0, _react.useState)(null),\n    opacityOverride = _useState2[0],\n    setOpacityOverride = _useState2[1];\n  var setOpacityTo = (0, _react.useCallback)((value, duration) => {\n    setOpacityOverride(value);\n    setDuration(duration ? duration / 1000 + \"s\" : '0s');\n  }, [setOpacityOverride, setDuration]);\n  var setOpacityActive = (0, _react.useCallback)(duration => {\n    setOpacityTo(activeOpacity !== null && activeOpacity !== void 0 ? activeOpacity : 0.2, duration);\n  }, [activeOpacity, setOpacityTo]);\n  var setOpacityInactive = (0, _react.useCallback)(duration => {\n    setOpacityTo(null, duration);\n  }, [setOpacityTo]);\n  var pressConfig = (0, _react.useMemo)(() => ({\n    cancelable: !rejectResponderTermination,\n    disabled,\n    delayLongPress,\n    delayPressStart: delayPressIn,\n    delayPressEnd: delayPressOut,\n    onLongPress,\n    onPress,\n    onPressStart(event) {\n      var isGrant = event.dispatchConfig != null ? event.dispatchConfig.registrationName === 'onResponderGrant' : event.type === 'keydown';\n      setOpacityActive(isGrant ? 0 : 150);\n      if (onPressIn != null) {\n        onPressIn(event);\n      }\n    },\n    onPressEnd(event) {\n      setOpacityInactive(250);\n      if (onPressOut != null) {\n        onPressOut(event);\n      }\n    }\n  }), [delayLongPress, delayPressIn, delayPressOut, disabled, onLongPress, onPress, onPressIn, onPressOut, rejectResponderTermination, setOpacityActive, setOpacityInactive]);\n  var pressEventHandlers = (0, _usePressEvents.default)(hostRef, pressConfig);\n  return /*#__PURE__*/React.createElement(_View.default, (0, _extends2.default)({}, rest, pressEventHandlers, {\n    accessibilityDisabled: disabled,\n    focusable: !disabled && focusable !== false,\n    pointerEvents: disabled ? 'box-none' : undefined,\n    ref: setRef,\n    style: [styles.root, !disabled && styles.actionable, style, opacityOverride != null && {\n      opacity: opacityOverride\n    }, {\n      transitionDuration: duration\n    }]\n  }));\n}\nvar styles = _StyleSheet.default.create({\n  root: {\n    transitionProperty: 'opacity',\n    transitionDuration: '0.15s',\n    userSelect: 'none'\n  },\n  actionable: {\n    cursor: 'pointer',\n    touchAction: 'manipulation'\n  }\n});\nvar MemoedTouchableOpacity = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(TouchableOpacity));\nMemoedTouchableOpacity.displayName = 'TouchableOpacity';\nvar _default = exports.default = MemoedTouchableOpacity;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIM,8BAA8B,GAAGP,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIO,MAAM,GAAGL,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACtD,IAAIQ,KAAK,GAAGD,MAAM;AAClB,IAAIE,aAAa,GAAGV,sBAAsB,CAACC,OAAO,6BAA6B,CAAC,CAAC;AACjF,IAAIU,eAAe,GAAGX,sBAAsB,CAACC,OAAO,+BAA+B,CAAC,CAAC;AACrF,IAAIW,WAAW,GAAGZ,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIY,KAAK,GAAGb,sBAAsB,CAACC,OAAO,UAAU,CAAC,CAAC;AACtD,IAAIa,SAAS,GAAG,CAAC,eAAe,EAAE,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,4BAA4B,EAAE,OAAO,CAAC;AAOzM,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,YAAY,EAAE;EAQ7C,IAAIC,aAAa,GAAGF,KAAK,CAACE,aAAa;IACrCC,YAAY,GAAGH,KAAK,CAACG,YAAY;IACjCC,aAAa,GAAGJ,KAAK,CAACI,aAAa;IACnCC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,WAAW,GAAGR,KAAK,CAACQ,WAAW;IAC/BC,OAAO,GAAGT,KAAK,CAACS,OAAO;IACvBC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,UAAU,GAAGX,KAAK,CAACW,UAAU;IAC7BC,0BAA0B,GAAGZ,KAAK,CAACY,0BAA0B;IAC7DC,KAAK,GAAGb,KAAK,CAACa,KAAK;IACnBC,IAAI,GAAG,CAAC,CAAC,EAAEvB,8BAA8B,CAACL,OAAO,EAAEc,KAAK,EAAEF,SAAS,CAAC;EACtE,IAAIiB,OAAO,GAAG,CAAC,CAAC,EAAEvB,MAAM,CAACwB,MAAM,EAAE,IAAI,CAAC;EACtC,IAAIC,MAAM,GAAG,CAAC,CAAC,EAAEvB,aAAa,CAACR,OAAO,EAAEe,YAAY,EAAEc,OAAO,CAAC;EAC9D,IAAIG,SAAS,GAAG,CAAC,CAAC,EAAE1B,MAAM,CAAC2B,QAAQ,EAAE,IAAI,CAAC;IACxCC,QAAQ,GAAGF,SAAS,CAAC,CAAC,CAAC;IACvBG,WAAW,GAAGH,SAAS,CAAC,CAAC,CAAC;EAC5B,IAAII,UAAU,GAAG,CAAC,CAAC,EAAE9B,MAAM,CAAC2B,QAAQ,EAAE,IAAI,CAAC;IACzCI,eAAe,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC/BE,kBAAkB,GAAGF,UAAU,CAAC,CAAC,CAAC;EACpC,IAAIG,YAAY,GAAG,CAAC,CAAC,EAAEjC,MAAM,CAACkC,WAAW,EAAE,UAACC,KAAK,EAAEP,QAAQ,EAAK;IAC9DI,kBAAkB,CAACG,KAAK,CAAC;IACzBN,WAAW,CAACD,QAAQ,GAAGA,QAAQ,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC;EACtD,CAAC,EAAE,CAACI,kBAAkB,EAAEH,WAAW,CAAC,CAAC;EACrC,IAAIO,gBAAgB,GAAG,CAAC,CAAC,EAAEpC,MAAM,CAACkC,WAAW,EAAE,UAAAN,QAAQ,EAAI;IACzDK,YAAY,CAACvB,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,GAAG,EAAEkB,QAAQ,CAAC;EAClG,CAAC,EAAE,CAAClB,aAAa,EAAEuB,YAAY,CAAC,CAAC;EACjC,IAAII,kBAAkB,GAAG,CAAC,CAAC,EAAErC,MAAM,CAACkC,WAAW,EAAE,UAAAN,QAAQ,EAAI;IAC3DK,YAAY,CAAC,IAAI,EAAEL,QAAQ,CAAC;EAC9B,CAAC,EAAE,CAACK,YAAY,CAAC,CAAC;EAClB,IAAIK,WAAW,GAAG,CAAC,CAAC,EAAEtC,MAAM,CAACuC,OAAO,EAAE;IAAA,OAAO;MAC3CC,UAAU,EAAE,CAACpB,0BAA0B;MACvCN,QAAQ,EAARA,QAAQ;MACRD,cAAc,EAAdA,cAAc;MACd4B,eAAe,EAAE9B,YAAY;MAC7B+B,aAAa,EAAE9B,aAAa;MAC5BI,WAAW,EAAXA,WAAW;MACXC,OAAO,EAAPA,OAAO;MACP0B,YAAY,WAAZA,YAAYA,CAACC,KAAK,EAAE;QAClB,IAAIC,OAAO,GAAGD,KAAK,CAACE,cAAc,IAAI,IAAI,GAAGF,KAAK,CAACE,cAAc,CAACC,gBAAgB,KAAK,kBAAkB,GAAGH,KAAK,CAACI,IAAI,KAAK,SAAS;QACpIZ,gBAAgB,CAACS,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC;QACnC,IAAI3B,SAAS,IAAI,IAAI,EAAE;UACrBA,SAAS,CAAC0B,KAAK,CAAC;QAClB;MACF,CAAC;MACDK,UAAU,WAAVA,UAAUA,CAACL,KAAK,EAAE;QAChBP,kBAAkB,CAAC,GAAG,CAAC;QACvB,IAAIlB,UAAU,IAAI,IAAI,EAAE;UACtBA,UAAU,CAACyB,KAAK,CAAC;QACnB;MACF;IACF,CAAC;EAAA,CAAC,EAAE,CAAC/B,cAAc,EAAEF,YAAY,EAAEC,aAAa,EAAEE,QAAQ,EAAEE,WAAW,EAAEC,OAAO,EAAEC,SAAS,EAAEC,UAAU,EAAEC,0BAA0B,EAAEgB,gBAAgB,EAAEC,kBAAkB,CAAC,CAAC;EAC3K,IAAIa,kBAAkB,GAAG,CAAC,CAAC,EAAE/C,eAAe,CAACT,OAAO,EAAE6B,OAAO,EAAEe,WAAW,CAAC;EAC3E,OAAoBrC,KAAK,CAACkD,aAAa,CAAC9C,KAAK,CAACX,OAAO,EAAE,CAAC,CAAC,EAAEI,SAAS,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAE4B,IAAI,EAAE4B,kBAAkB,EAAE;IAC1GE,qBAAqB,EAAEtC,QAAQ;IAC/BC,SAAS,EAAE,CAACD,QAAQ,IAAIC,SAAS,KAAK,KAAK;IAC3CsC,aAAa,EAAEvC,QAAQ,GAAG,UAAU,GAAGwC,SAAS;IAChDC,GAAG,EAAE9B,MAAM;IACXJ,KAAK,EAAE,CAACmC,MAAM,CAACC,IAAI,EAAE,CAAC3C,QAAQ,IAAI0C,MAAM,CAACE,UAAU,EAAErC,KAAK,EAAEU,eAAe,IAAI,IAAI,IAAI;MACrF4B,OAAO,EAAE5B;IACX,CAAC,EAAE;MACD6B,kBAAkB,EAAEhC;IACtB,CAAC;EACH,CAAC,CAAC,CAAC;AACL;AACA,IAAI4B,MAAM,GAAGpD,WAAW,CAACV,OAAO,CAACmE,MAAM,CAAC;EACtCJ,IAAI,EAAE;IACJK,kBAAkB,EAAE,SAAS;IAC7BF,kBAAkB,EAAE,OAAO;IAC3BG,UAAU,EAAE;EACd,CAAC;EACDL,UAAU,EAAE;IACVM,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE;EACf;AACF,CAAC,CAAC;AACF,IAAIC,sBAAsB,GAAgBjE,KAAK,CAACkE,IAAI,CAAclE,KAAK,CAACmE,UAAU,CAAC7D,gBAAgB,CAAC,CAAC;AACrG2D,sBAAsB,CAACG,WAAW,GAAG,kBAAkB;AACvD,IAAIC,QAAQ,GAAG1E,OAAO,CAACF,OAAO,GAAGwE,sBAAsB;AACvDK,MAAM,CAAC3E,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}