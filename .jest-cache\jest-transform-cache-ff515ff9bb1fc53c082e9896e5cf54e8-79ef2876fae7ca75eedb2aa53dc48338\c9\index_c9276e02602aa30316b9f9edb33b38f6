01226d3bee09614593571f6b5638477b
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _AccessibilityUtil = _interopRequireDefault(require("../AccessibilityUtil"));
var _StyleSheet2 = _interopRequireDefault(require("../../exports/StyleSheet"));
var _warnOnce = require("../warnOnce");
var _excluded = ["aria-activedescendant", "accessibilityActiveDescendant", "aria-atomic", "accessibilityAtomic", "aria-autocomplete", "accessibilityAutoComplete", "aria-busy", "accessibilityBusy", "aria-checked", "accessibilityChecked", "aria-colcount", "accessibilityColumnCount", "aria-colindex", "accessibilityColumnIndex", "aria-colspan", "accessibilityColumnSpan", "aria-controls", "accessibilityControls", "aria-current", "accessibilityCurrent", "aria-describedby", "accessibilityDescribedBy", "aria-details", "accessibilityDetails", "aria-disabled", "accessibilityDisabled", "aria-errormessage", "accessibilityErrorMessage", "aria-expanded", "accessibilityExpanded", "aria-flowto", "accessibilityFlowTo", "aria-haspopup", "accessibilityHasPopup", "aria-hidden", "accessibilityHidden", "aria-invalid", "accessibilityInvalid", "aria-keyshortcuts", "accessibilityKeyShortcuts", "aria-label", "accessibilityLabel", "aria-labelledby", "accessibilityLabelledBy", "aria-level", "accessibilityLevel", "aria-live", "accessibilityLiveRegion", "aria-modal", "accessibilityModal", "aria-multiline", "accessibilityMultiline", "aria-multiselectable", "accessibilityMultiSelectable", "aria-orientation", "accessibilityOrientation", "aria-owns", "accessibilityOwns", "aria-placeholder", "accessibilityPlaceholder", "aria-posinset", "accessibilityPosInSet", "aria-pressed", "accessibilityPressed", "aria-readonly", "accessibilityReadOnly", "aria-required", "accessibilityRequired", "role", "accessibilityRole", "aria-roledescription", "accessibilityRoleDescription", "aria-rowcount", "accessibilityRowCount", "aria-rowindex", "accessibilityRowIndex", "aria-rowspan", "accessibilityRowSpan", "aria-selected", "accessibilitySelected", "aria-setsize", "accessibilitySetSize", "aria-sort", "accessibilitySort", "aria-valuemax", "accessibilityValueMax", "aria-valuemin", "accessibilityValueMin", "aria-valuenow", "accessibilityValueNow", "aria-valuetext", "accessibilityValueText", "dataSet", "focusable", "id", "nativeID", "pointerEvents", "style", "tabIndex", "testID"];
var emptyObject = {};
var hasOwnProperty = Object.prototype.hasOwnProperty;
var isArray = Array.isArray;
var uppercasePattern = /[A-Z]/g;
function toHyphenLower(match) {
  return '-' + match.toLowerCase();
}
function hyphenateString(str) {
  return str.replace(uppercasePattern, toHyphenLower);
}
function processIDRefList(idRefList) {
  return isArray(idRefList) ? idRefList.join(' ') : idRefList;
}
var pointerEventsStyles = _StyleSheet2.default.create({
  auto: {
    pointerEvents: 'auto'
  },
  'box-none': {
    pointerEvents: 'box-none'
  },
  'box-only': {
    pointerEvents: 'box-only'
  },
  none: {
    pointerEvents: 'none'
  }
});
var createDOMProps = function createDOMProps(elementType, props, options) {
  if (!props) {
    props = emptyObject;
  }
  var _props = props,
    ariaActiveDescendant = _props['aria-activedescendant'],
    accessibilityActiveDescendant = _props.accessibilityActiveDescendant,
    ariaAtomic = _props['aria-atomic'],
    accessibilityAtomic = _props.accessibilityAtomic,
    ariaAutoComplete = _props['aria-autocomplete'],
    accessibilityAutoComplete = _props.accessibilityAutoComplete,
    ariaBusy = _props['aria-busy'],
    accessibilityBusy = _props.accessibilityBusy,
    ariaChecked = _props['aria-checked'],
    accessibilityChecked = _props.accessibilityChecked,
    ariaColumnCount = _props['aria-colcount'],
    accessibilityColumnCount = _props.accessibilityColumnCount,
    ariaColumnIndex = _props['aria-colindex'],
    accessibilityColumnIndex = _props.accessibilityColumnIndex,
    ariaColumnSpan = _props['aria-colspan'],
    accessibilityColumnSpan = _props.accessibilityColumnSpan,
    ariaControls = _props['aria-controls'],
    accessibilityControls = _props.accessibilityControls,
    ariaCurrent = _props['aria-current'],
    accessibilityCurrent = _props.accessibilityCurrent,
    ariaDescribedBy = _props['aria-describedby'],
    accessibilityDescribedBy = _props.accessibilityDescribedBy,
    ariaDetails = _props['aria-details'],
    accessibilityDetails = _props.accessibilityDetails,
    ariaDisabled = _props['aria-disabled'],
    accessibilityDisabled = _props.accessibilityDisabled,
    ariaErrorMessage = _props['aria-errormessage'],
    accessibilityErrorMessage = _props.accessibilityErrorMessage,
    ariaExpanded = _props['aria-expanded'],
    accessibilityExpanded = _props.accessibilityExpanded,
    ariaFlowTo = _props['aria-flowto'],
    accessibilityFlowTo = _props.accessibilityFlowTo,
    ariaHasPopup = _props['aria-haspopup'],
    accessibilityHasPopup = _props.accessibilityHasPopup,
    ariaHidden = _props['aria-hidden'],
    accessibilityHidden = _props.accessibilityHidden,
    ariaInvalid = _props['aria-invalid'],
    accessibilityInvalid = _props.accessibilityInvalid,
    ariaKeyShortcuts = _props['aria-keyshortcuts'],
    accessibilityKeyShortcuts = _props.accessibilityKeyShortcuts,
    ariaLabel = _props['aria-label'],
    accessibilityLabel = _props.accessibilityLabel,
    ariaLabelledBy = _props['aria-labelledby'],
    accessibilityLabelledBy = _props.accessibilityLabelledBy,
    ariaLevel = _props['aria-level'],
    accessibilityLevel = _props.accessibilityLevel,
    ariaLive = _props['aria-live'],
    accessibilityLiveRegion = _props.accessibilityLiveRegion,
    ariaModal = _props['aria-modal'],
    accessibilityModal = _props.accessibilityModal,
    ariaMultiline = _props['aria-multiline'],
    accessibilityMultiline = _props.accessibilityMultiline,
    ariaMultiSelectable = _props['aria-multiselectable'],
    accessibilityMultiSelectable = _props.accessibilityMultiSelectable,
    ariaOrientation = _props['aria-orientation'],
    accessibilityOrientation = _props.accessibilityOrientation,
    ariaOwns = _props['aria-owns'],
    accessibilityOwns = _props.accessibilityOwns,
    ariaPlaceholder = _props['aria-placeholder'],
    accessibilityPlaceholder = _props.accessibilityPlaceholder,
    ariaPosInSet = _props['aria-posinset'],
    accessibilityPosInSet = _props.accessibilityPosInSet,
    ariaPressed = _props['aria-pressed'],
    accessibilityPressed = _props.accessibilityPressed,
    ariaReadOnly = _props['aria-readonly'],
    accessibilityReadOnly = _props.accessibilityReadOnly,
    ariaRequired = _props['aria-required'],
    accessibilityRequired = _props.accessibilityRequired,
    ariaRole = _props.role,
    accessibilityRole = _props.accessibilityRole,
    ariaRoleDescription = _props['aria-roledescription'],
    accessibilityRoleDescription = _props.accessibilityRoleDescription,
    ariaRowCount = _props['aria-rowcount'],
    accessibilityRowCount = _props.accessibilityRowCount,
    ariaRowIndex = _props['aria-rowindex'],
    accessibilityRowIndex = _props.accessibilityRowIndex,
    ariaRowSpan = _props['aria-rowspan'],
    accessibilityRowSpan = _props.accessibilityRowSpan,
    ariaSelected = _props['aria-selected'],
    accessibilitySelected = _props.accessibilitySelected,
    ariaSetSize = _props['aria-setsize'],
    accessibilitySetSize = _props.accessibilitySetSize,
    ariaSort = _props['aria-sort'],
    accessibilitySort = _props.accessibilitySort,
    ariaValueMax = _props['aria-valuemax'],
    accessibilityValueMax = _props.accessibilityValueMax,
    ariaValueMin = _props['aria-valuemin'],
    accessibilityValueMin = _props.accessibilityValueMin,
    ariaValueNow = _props['aria-valuenow'],
    accessibilityValueNow = _props.accessibilityValueNow,
    ariaValueText = _props['aria-valuetext'],
    accessibilityValueText = _props.accessibilityValueText,
    dataSet = _props.dataSet,
    focusable = _props.focusable,
    id = _props.id,
    nativeID = _props.nativeID,
    pointerEvents = _props.pointerEvents,
    style = _props.style,
    tabIndex = _props.tabIndex,
    testID = _props.testID,
    domProps = (0, _objectWithoutPropertiesLoose2.default)(_props, _excluded);
  var disabled = ariaDisabled || accessibilityDisabled;
  var role = _AccessibilityUtil.default.propsToAriaRole(props);
  var _ariaActiveDescendant = ariaActiveDescendant != null ? ariaActiveDescendant : accessibilityActiveDescendant;
  if (_ariaActiveDescendant != null) {
    domProps['aria-activedescendant'] = _ariaActiveDescendant;
  }
  var _ariaAtomic = ariaAtomic != null ? ariaActiveDescendant : accessibilityAtomic;
  if (_ariaAtomic != null) {
    domProps['aria-atomic'] = _ariaAtomic;
  }
  var _ariaAutoComplete = ariaAutoComplete != null ? ariaAutoComplete : accessibilityAutoComplete;
  if (_ariaAutoComplete != null) {
    domProps['aria-autocomplete'] = _ariaAutoComplete;
  }
  var _ariaBusy = ariaBusy != null ? ariaBusy : accessibilityBusy;
  if (_ariaBusy != null) {
    domProps['aria-busy'] = _ariaBusy;
  }
  var _ariaChecked = ariaChecked != null ? ariaChecked : accessibilityChecked;
  if (_ariaChecked != null) {
    domProps['aria-checked'] = _ariaChecked;
  }
  var _ariaColumnCount = ariaColumnCount != null ? ariaColumnCount : accessibilityColumnCount;
  if (_ariaColumnCount != null) {
    domProps['aria-colcount'] = _ariaColumnCount;
  }
  var _ariaColumnIndex = ariaColumnIndex != null ? ariaColumnIndex : accessibilityColumnIndex;
  if (_ariaColumnIndex != null) {
    domProps['aria-colindex'] = _ariaColumnIndex;
  }
  var _ariaColumnSpan = ariaColumnSpan != null ? ariaColumnSpan : accessibilityColumnSpan;
  if (_ariaColumnSpan != null) {
    domProps['aria-colspan'] = _ariaColumnSpan;
  }
  var _ariaControls = ariaControls != null ? ariaControls : accessibilityControls;
  if (_ariaControls != null) {
    domProps['aria-controls'] = processIDRefList(_ariaControls);
  }
  var _ariaCurrent = ariaCurrent != null ? ariaCurrent : accessibilityCurrent;
  if (_ariaCurrent != null) {
    domProps['aria-current'] = _ariaCurrent;
  }
  var _ariaDescribedBy = ariaDescribedBy != null ? ariaDescribedBy : accessibilityDescribedBy;
  if (_ariaDescribedBy != null) {
    domProps['aria-describedby'] = processIDRefList(_ariaDescribedBy);
  }
  var _ariaDetails = ariaDetails != null ? ariaDetails : accessibilityDetails;
  if (_ariaDetails != null) {
    domProps['aria-details'] = _ariaDetails;
  }
  if (disabled === true) {
    domProps['aria-disabled'] = true;
    if (elementType === 'button' || elementType === 'form' || elementType === 'input' || elementType === 'select' || elementType === 'textarea') {
      domProps.disabled = true;
    }
  }
  var _ariaErrorMessage = ariaErrorMessage != null ? ariaErrorMessage : accessibilityErrorMessage;
  if (_ariaErrorMessage != null) {
    domProps['aria-errormessage'] = _ariaErrorMessage;
  }
  var _ariaExpanded = ariaExpanded != null ? ariaExpanded : accessibilityExpanded;
  if (_ariaExpanded != null) {
    domProps['aria-expanded'] = _ariaExpanded;
  }
  var _ariaFlowTo = ariaFlowTo != null ? ariaFlowTo : accessibilityFlowTo;
  if (_ariaFlowTo != null) {
    domProps['aria-flowto'] = processIDRefList(_ariaFlowTo);
  }
  var _ariaHasPopup = ariaHasPopup != null ? ariaHasPopup : accessibilityHasPopup;
  if (_ariaHasPopup != null) {
    domProps['aria-haspopup'] = _ariaHasPopup;
  }
  var _ariaHidden = ariaHidden != null ? ariaHidden : accessibilityHidden;
  if (_ariaHidden === true) {
    domProps['aria-hidden'] = _ariaHidden;
  }
  var _ariaInvalid = ariaInvalid != null ? ariaInvalid : accessibilityInvalid;
  if (_ariaInvalid != null) {
    domProps['aria-invalid'] = _ariaInvalid;
  }
  var _ariaKeyShortcuts = ariaKeyShortcuts != null ? ariaKeyShortcuts : accessibilityKeyShortcuts;
  if (_ariaKeyShortcuts != null) {
    domProps['aria-keyshortcuts'] = processIDRefList(_ariaKeyShortcuts);
  }
  var _ariaLabel = ariaLabel != null ? ariaLabel : accessibilityLabel;
  if (_ariaLabel != null) {
    domProps['aria-label'] = _ariaLabel;
  }
  var _ariaLabelledBy = ariaLabelledBy != null ? ariaLabelledBy : accessibilityLabelledBy;
  if (_ariaLabelledBy != null) {
    domProps['aria-labelledby'] = processIDRefList(_ariaLabelledBy);
  }
  var _ariaLevel = ariaLevel != null ? ariaLevel : accessibilityLevel;
  if (_ariaLevel != null) {
    domProps['aria-level'] = _ariaLevel;
  }
  var _ariaLive = ariaLive != null ? ariaLive : accessibilityLiveRegion;
  if (_ariaLive != null) {
    domProps['aria-live'] = _ariaLive === 'none' ? 'off' : _ariaLive;
  }
  var _ariaModal = ariaModal != null ? ariaModal : accessibilityModal;
  if (_ariaModal != null) {
    domProps['aria-modal'] = _ariaModal;
  }
  var _ariaMultiline = ariaMultiline != null ? ariaMultiline : accessibilityMultiline;
  if (_ariaMultiline != null) {
    domProps['aria-multiline'] = _ariaMultiline;
  }
  var _ariaMultiSelectable = ariaMultiSelectable != null ? ariaMultiSelectable : accessibilityMultiSelectable;
  if (_ariaMultiSelectable != null) {
    domProps['aria-multiselectable'] = _ariaMultiSelectable;
  }
  var _ariaOrientation = ariaOrientation != null ? ariaOrientation : accessibilityOrientation;
  if (_ariaOrientation != null) {
    domProps['aria-orientation'] = _ariaOrientation;
  }
  var _ariaOwns = ariaOwns != null ? ariaOwns : accessibilityOwns;
  if (_ariaOwns != null) {
    domProps['aria-owns'] = processIDRefList(_ariaOwns);
  }
  var _ariaPlaceholder = ariaPlaceholder != null ? ariaPlaceholder : accessibilityPlaceholder;
  if (_ariaPlaceholder != null) {
    domProps['aria-placeholder'] = _ariaPlaceholder;
  }
  var _ariaPosInSet = ariaPosInSet != null ? ariaPosInSet : accessibilityPosInSet;
  if (_ariaPosInSet != null) {
    domProps['aria-posinset'] = _ariaPosInSet;
  }
  var _ariaPressed = ariaPressed != null ? ariaPressed : accessibilityPressed;
  if (_ariaPressed != null) {
    domProps['aria-pressed'] = _ariaPressed;
  }
  var _ariaReadOnly = ariaReadOnly != null ? ariaReadOnly : accessibilityReadOnly;
  if (_ariaReadOnly != null) {
    domProps['aria-readonly'] = _ariaReadOnly;
    if (elementType === 'input' || elementType === 'select' || elementType === 'textarea') {
      domProps.readOnly = true;
    }
  }
  var _ariaRequired = ariaRequired != null ? ariaRequired : accessibilityRequired;
  if (_ariaRequired != null) {
    domProps['aria-required'] = _ariaRequired;
    if (elementType === 'input' || elementType === 'select' || elementType === 'textarea') {
      domProps.required = accessibilityRequired;
    }
  }
  if (role != null) {
    domProps['role'] = role === 'none' ? 'presentation' : role;
  }
  var _ariaRoleDescription = ariaRoleDescription != null ? ariaRoleDescription : accessibilityRoleDescription;
  if (_ariaRoleDescription != null) {
    domProps['aria-roledescription'] = _ariaRoleDescription;
  }
  var _ariaRowCount = ariaRowCount != null ? ariaRowCount : accessibilityRowCount;
  if (_ariaRowCount != null) {
    domProps['aria-rowcount'] = _ariaRowCount;
  }
  var _ariaRowIndex = ariaRowIndex != null ? ariaRowIndex : accessibilityRowIndex;
  if (_ariaRowIndex != null) {
    domProps['aria-rowindex'] = _ariaRowIndex;
  }
  var _ariaRowSpan = ariaRowSpan != null ? ariaRowSpan : accessibilityRowSpan;
  if (_ariaRowSpan != null) {
    domProps['aria-rowspan'] = _ariaRowSpan;
  }
  var _ariaSelected = ariaSelected != null ? ariaSelected : accessibilitySelected;
  if (_ariaSelected != null) {
    domProps['aria-selected'] = _ariaSelected;
  }
  var _ariaSetSize = ariaSetSize != null ? ariaSetSize : accessibilitySetSize;
  if (_ariaSetSize != null) {
    domProps['aria-setsize'] = _ariaSetSize;
  }
  var _ariaSort = ariaSort != null ? ariaSort : accessibilitySort;
  if (_ariaSort != null) {
    domProps['aria-sort'] = _ariaSort;
  }
  var _ariaValueMax = ariaValueMax != null ? ariaValueMax : accessibilityValueMax;
  if (_ariaValueMax != null) {
    domProps['aria-valuemax'] = _ariaValueMax;
  }
  var _ariaValueMin = ariaValueMin != null ? ariaValueMin : accessibilityValueMin;
  if (_ariaValueMin != null) {
    domProps['aria-valuemin'] = _ariaValueMin;
  }
  var _ariaValueNow = ariaValueNow != null ? ariaValueNow : accessibilityValueNow;
  if (_ariaValueNow != null) {
    domProps['aria-valuenow'] = _ariaValueNow;
  }
  var _ariaValueText = ariaValueText != null ? ariaValueText : accessibilityValueText;
  if (_ariaValueText != null) {
    domProps['aria-valuetext'] = _ariaValueText;
  }
  if (dataSet != null) {
    for (var dataProp in dataSet) {
      if (hasOwnProperty.call(dataSet, dataProp)) {
        var dataName = hyphenateString(dataProp);
        var dataValue = dataSet[dataProp];
        if (dataValue != null) {
          domProps["data-" + dataName] = dataValue;
        }
      }
    }
  }
  if (tabIndex === 0 || tabIndex === '0' || tabIndex === -1 || tabIndex === '-1') {
    domProps.tabIndex = tabIndex;
  } else {
    if (focusable === false) {
      domProps.tabIndex = '-1';
    }
    if (elementType === 'a' || elementType === 'button' || elementType === 'input' || elementType === 'select' || elementType === 'textarea') {
      if (focusable === false || accessibilityDisabled === true) {
        domProps.tabIndex = '-1';
      }
    } else if (role === 'button' || role === 'checkbox' || role === 'link' || role === 'radio' || role === 'textbox' || role === 'switch') {
      if (focusable !== false) {
        domProps.tabIndex = '0';
      }
    } else {
      if (focusable === true) {
        domProps.tabIndex = '0';
      }
    }
  }
  if (pointerEvents != null) {
    (0, _warnOnce.warnOnce)('pointerEvents', "props.pointerEvents is deprecated. Use style.pointerEvents");
  }
  var _StyleSheet = (0, _StyleSheet2.default)([style, pointerEvents && pointerEventsStyles[pointerEvents]], (0, _objectSpread2.default)({
      writingDirection: 'ltr'
    }, options)),
    className = _StyleSheet[0],
    inlineStyle = _StyleSheet[1];
  if (className) {
    domProps.className = className;
  }
  if (inlineStyle) {
    domProps.style = inlineStyle;
  }
  var _id = id != null ? id : nativeID;
  if (_id != null) {
    domProps.id = _id;
  }
  if (testID != null) {
    domProps['data-testid'] = testID;
  }
  if (domProps.type == null && elementType === 'button') {
    domProps.type = 'button';
  }
  return domProps;
};
var _default = exports.default = createDOMProps;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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