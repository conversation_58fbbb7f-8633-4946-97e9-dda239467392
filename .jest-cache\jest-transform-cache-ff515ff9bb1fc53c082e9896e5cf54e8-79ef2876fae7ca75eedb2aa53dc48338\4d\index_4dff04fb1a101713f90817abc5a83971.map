{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "cancelIdleCallback", "_canUseDom", "_requestIdleCallback", "cb", "options", "setTimeout", "start", "Date", "now", "didTimeout", "timeRemaining", "Math", "max", "_cancelIdleCallback", "id", "clearTimeout", "isSupported", "window", "requestIdleCallback", "_default"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = exports.cancelIdleCallback = void 0;\nvar _canUseDom = _interopRequireDefault(require(\"../canUseDom\"));\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar _requestIdleCallback = function _requestIdleCallback(cb, options) {\n  return setTimeout(() => {\n    var start = Date.now();\n    cb({\n      didTimeout: false,\n      timeRemaining() {\n        return Math.max(0, 50 - (Date.now() - start));\n      }\n    });\n  }, 1);\n};\nvar _cancelIdleCallback = function _cancelIdleCallback(id) {\n  clearTimeout(id);\n};\nvar isSupported = _canUseDom.default && typeof window.requestIdleCallback !== 'undefined';\nvar requestIdleCallback = isSupported ? window.requestIdleCallback : _requestIdleCallback;\nvar cancelIdleCallback = exports.cancelIdleCallback = isSupported ? window.cancelIdleCallback : _cancelIdleCallback;\nvar _default = exports.default = requestIdleCallback;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAGC,OAAO,CAACE,kBAAkB,GAAG,KAAK,CAAC;AACrD,IAAIC,UAAU,GAAGN,sBAAsB,CAACC,OAAO,eAAe,CAAC,CAAC;AAUhE,IAAIM,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,EAAE,EAAEC,OAAO,EAAE;EACpE,OAAOC,UAAU,CAAC,YAAM;IACtB,IAAIC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IACtBL,EAAE,CAAC;MACDM,UAAU,EAAE,KAAK;MACjBC,aAAa,WAAbA,aAAaA,CAAA,EAAG;QACd,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIL,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,KAAK,CAAC,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC,CAAC;AACP,CAAC;AACD,IAAIO,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,EAAE,EAAE;EACzDC,YAAY,CAACD,EAAE,CAAC;AAClB,CAAC;AACD,IAAIE,WAAW,GAAGf,UAAU,CAACJ,OAAO,IAAI,OAAOoB,MAAM,CAACC,mBAAmB,KAAK,WAAW;AACzF,IAAIA,mBAAmB,GAAGF,WAAW,GAAGC,MAAM,CAACC,mBAAmB,GAAGhB,oBAAoB;AACzF,IAAIF,kBAAkB,GAAGF,OAAO,CAACE,kBAAkB,GAAGgB,WAAW,GAAGC,MAAM,CAACjB,kBAAkB,GAAGa,mBAAmB;AACnH,IAAIM,QAAQ,GAAGrB,OAAO,CAACD,OAAO,GAAGqB,mBAAmB", "ignoreList": []}