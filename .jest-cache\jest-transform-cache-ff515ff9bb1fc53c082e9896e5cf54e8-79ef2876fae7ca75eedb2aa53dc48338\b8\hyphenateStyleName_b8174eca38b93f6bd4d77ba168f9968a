5cc04cb63f7239d97264c57900499a5d
"use strict";

exports.__esModule = true;
exports.default = void 0;
var uppercasePattern = /[A-Z]/g;
var msPattern = /^ms-/;
var cache = {};
function toHyphenLower(match) {
  return '-' + match.toLowerCase();
}
function hyphenateStyleName(name) {
  if (name in cache) {
    return cache[name];
  }
  var hName = name.replace(uppercasePattern, toHyphenLower);
  return cache[name] = msPattern.test(hName) ? '-' + hName : hName;
}
var _default = exports.default = hyphenateStyleName;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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