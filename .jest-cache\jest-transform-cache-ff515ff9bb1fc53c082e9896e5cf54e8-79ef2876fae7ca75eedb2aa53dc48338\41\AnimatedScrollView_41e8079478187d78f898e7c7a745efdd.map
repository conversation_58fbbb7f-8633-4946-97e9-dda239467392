{"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "exports", "__esModule", "_extends2", "React", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createAnimatedComponent", "ScrollViewWithEventThrottle", "forwardRef", "props", "ref", "createElement", "scrollEventThrottle", "_default", "module"], "sources": ["AnimatedScrollView.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _ScrollView = _interopRequireDefault(require(\"../../../../exports/ScrollView\"));\nvar _createAnimatedComponent = _interopRequireDefault(require(\"../createAnimatedComponent\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n/**\n * @see https://github.com/facebook/react-native/commit/b8c8562\n */\nvar ScrollViewWithEventThrottle = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(_ScrollView.default, (0, _extends2.default)({\n  scrollEventThrottle: 0.0001\n}, props, {\n  ref: ref\n})));\nvar _default = exports.default = (0, _createAnimatedComponent.default)(ScrollViewWithEventThrottle);\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,SAAS,GAAGH,sBAAsB,CAACF,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIM,KAAK,GAAGP,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIO,WAAW,GAAGL,sBAAsB,CAACF,OAAO,iCAAiC,CAAC,CAAC;AACnF,IAAIQ,wBAAwB,GAAGN,sBAAsB,CAACF,OAAO,6BAA6B,CAAC,CAAC;AAc5F,IAAIS,2BAA2B,GAAgBH,KAAK,CAACI,UAAU,CAAC,UAACC,KAAK,EAAEC,GAAG;EAAA,OAAkBN,KAAK,CAACO,aAAa,CAACN,WAAW,CAACN,OAAO,EAAE,CAAC,CAAC,EAAEI,SAAS,CAACJ,OAAO,EAAE;IAC3Ja,mBAAmB,EAAE;EACvB,CAAC,EAAEH,KAAK,EAAE;IACRC,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AAAA,EAAC;AACJ,IAAIG,QAAQ,GAAGZ,OAAO,CAACF,OAAO,GAAG,CAAC,CAAC,EAAEO,wBAAwB,CAACP,OAAO,EAAEQ,2BAA2B,CAAC;AACnGO,MAAM,CAACb,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}