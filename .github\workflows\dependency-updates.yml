name: 🔄 Dependency Updates

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:
    inputs:
      update_type:
        description: 'Type of update to perform'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
          - all

env:
  NODE_VERSION: '18'

jobs:
  # Check for dependency updates
  check-updates:
    name: 🔍 Check for Updates
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    outputs:
      has_updates: ${{ steps.check.outputs.has_updates }}
      update_summary: ${{ steps.check.outputs.summary }}
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install npm-check-updates
        run: npm install -g npm-check-updates

      - name: 🔍 Check for frontend updates
        id: frontend-check
        run: |
          echo "🔍 Checking frontend dependencies..."
          ncu --format json > frontend-updates.json || true
          
          FRONTEND_UPDATES=$(jq 'length' frontend-updates.json)
          echo "frontend_updates=$FRONTEND_UPDATES" >> $GITHUB_OUTPUT
          
          if [ "$FRONTEND_UPDATES" -gt 0 ]; then
            echo "📦 Frontend updates available: $FRONTEND_UPDATES"
            jq -r 'to_entries[] | "- \(.key): \(.value.current) → \(.value.wanted)"' frontend-updates.json > frontend-summary.txt
          else
            echo "✅ Frontend dependencies are up to date"
            echo "No updates available" > frontend-summary.txt
          fi

      - name: 🔍 Check for backend updates
        id: backend-check
        working-directory: ./backend
        run: |
          echo "🔍 Checking backend dependencies..."
          ncu --format json > backend-updates.json || true
          
          BACKEND_UPDATES=$(jq 'length' backend-updates.json)
          echo "backend_updates=$BACKEND_UPDATES" >> $GITHUB_OUTPUT
          
          if [ "$BACKEND_UPDATES" -gt 0 ]; then
            echo "📦 Backend updates available: $BACKEND_UPDATES"
            jq -r 'to_entries[] | "- \(.key): \(.value.current) → \(.value.wanted)"' backend-updates.json > backend-summary.txt
          else
            echo "✅ Backend dependencies are up to date"
            echo "No updates available" > backend-summary.txt
          fi

      - name: 📊 Generate update summary
        id: check
        run: |
          FRONTEND_UPDATES="${{ steps.frontend-check.outputs.frontend_updates }}"
          BACKEND_UPDATES="${{ steps.backend-check.outputs.backend_updates }}"
          TOTAL_UPDATES=$((FRONTEND_UPDATES + BACKEND_UPDATES))
          
          if [ "$TOTAL_UPDATES" -gt 0 ]; then
            echo "has_updates=true" >> $GITHUB_OUTPUT
            
            SUMMARY="## 📦 Dependency Updates Available\n\n"
            SUMMARY+="### Frontend ($FRONTEND_UPDATES updates)\n"
            SUMMARY+="$(cat frontend-summary.txt)\n\n"
            SUMMARY+="### Backend ($BACKEND_UPDATES updates)\n"
            SUMMARY+="$(cat backend/backend-summary.txt)\n"
            
            echo "summary<<EOF" >> $GITHUB_OUTPUT
            echo -e "$SUMMARY" >> $GITHUB_OUTPUT
            echo "EOF" >> $GITHUB_OUTPUT
          else
            echo "has_updates=false" >> $GITHUB_OUTPUT
            echo "summary=All dependencies are up to date! ✅" >> $GITHUB_OUTPUT
          fi

      - name: 📤 Upload update reports
        uses: actions/upload-artifact@v3
        with:
          name: dependency-reports
          path: |
            frontend-updates.json
            backend/backend-updates.json
            frontend-summary.txt
            backend/backend-summary.txt
          retention-days: 7

  # Apply dependency updates
  apply-updates:
    name: 🔄 Apply Updates
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: check-updates
    if: needs.check-updates.outputs.has_updates == 'true'
    
    strategy:
      matrix:
        component: [frontend, backend]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.DEPENDENCY_UPDATE_TOKEN }}

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install npm-check-updates
        run: npm install -g npm-check-updates

      - name: 🔄 Update frontend dependencies
        if: matrix.component == 'frontend'
        run: |
          echo "🔄 Updating frontend dependencies..."
          
          UPDATE_TYPE="${{ github.event.inputs.update_type || 'patch' }}"
          
          case $UPDATE_TYPE in
            "patch")
              ncu --target patch -u
              ;;
            "minor")
              ncu --target minor -u
              ;;
            "major")
              ncu --target latest -u
              ;;
            "all")
              ncu -u
              ;;
          esac
          
          npm install

      - name: 🔄 Update backend dependencies
        if: matrix.component == 'backend'
        working-directory: ./backend
        run: |
          echo "🔄 Updating backend dependencies..."
          
          UPDATE_TYPE="${{ github.event.inputs.update_type || 'patch' }}"
          
          case $UPDATE_TYPE in
            "patch")
              ncu --target patch -u
              ;;
            "minor")
              ncu --target minor -u
              ;;
            "major")
              ncu --target latest -u
              ;;
            "all")
              ncu -u
              ;;
          esac
          
          npm install

      - name: 🧪 Run tests after update
        run: |
          if [ "${{ matrix.component }}" == "frontend" ]; then
            npm run test:ci
            npm run lint
            npm run type-check
          else
            cd backend
            npm run test:ci
            npm run lint
            npm run type-check
          fi

      - name: 📤 Upload updated package files
        uses: actions/upload-artifact@v3
        with:
          name: updated-${{ matrix.component }}-packages
          path: |
            ${{ matrix.component == 'frontend' && 'package*.json' || 'backend/package*.json' }}
          retention-days: 7

  # Create update PR
  create-update-pr:
    name: 📝 Create Update PR
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [check-updates, apply-updates]
    if: needs.check-updates.outputs.has_updates == 'true'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.DEPENDENCY_UPDATE_TOKEN }}

      - name: 📥 Download updated packages
        uses: actions/download-artifact@v3
        with:
          name: updated-frontend-packages
          path: ./

      - name: 📥 Download updated backend packages
        uses: actions/download-artifact@v3
        with:
          name: updated-backend-packages
          path: ./backend/

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: |
          npm ci
          cd backend && npm ci

      - name: 🧪 Run comprehensive tests
        run: |
          # Frontend tests
          npm run test:ci
          npm run lint
          npm run type-check
          
          # Backend tests
          cd backend
          npm run test:ci
          npm run lint
          npm run type-check

      - name: 🔒 Security audit
        run: |
          npm audit --audit-level=moderate || true
          cd backend && npm audit --audit-level=moderate || true

      - name: 📝 Generate changelog
        id: changelog
        run: |
          echo "## 📦 Dependency Updates" > DEPENDENCY_CHANGELOG.md
          echo "" >> DEPENDENCY_CHANGELOG.md
          echo "This PR updates dependencies to their latest compatible versions." >> DEPENDENCY_CHANGELOG.md
          echo "" >> DEPENDENCY_CHANGELOG.md
          echo "${{ needs.check-updates.outputs.update_summary }}" >> DEPENDENCY_CHANGELOG.md
          echo "" >> DEPENDENCY_CHANGELOG.md
          echo "### ✅ Validation" >> DEPENDENCY_CHANGELOG.md
          echo "- All tests pass" >> DEPENDENCY_CHANGELOG.md
          echo "- No linting errors" >> DEPENDENCY_CHANGELOG.md
          echo "- TypeScript compilation successful" >> DEPENDENCY_CHANGELOG.md
          echo "- Security audit completed" >> DEPENDENCY_CHANGELOG.md

      - name: 🌿 Create update branch
        run: |
          BRANCH_NAME="dependency-updates/$(date +%Y-%m-%d)"
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          
          git checkout -b "$BRANCH_NAME"
          git add .
          git commit -m "🔄 Update dependencies

          - Updated frontend and backend dependencies
          - All tests passing
          - Security audit completed
          
          Auto-generated by dependency update workflow"
          
          git push origin "$BRANCH_NAME"
          echo "branch_name=$BRANCH_NAME" >> $GITHUB_OUTPUT

      - name: 📝 Create Pull Request
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.DEPENDENCY_UPDATE_TOKEN }}
          script: |
            const fs = require('fs');
            const changelog = fs.readFileSync('DEPENDENCY_CHANGELOG.md', 'utf8');
            
            const { data: pr } = await github.rest.pulls.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: '🔄 Automated Dependency Updates',
              head: '${{ steps.changelog.outputs.branch_name }}',
              base: 'develop',
              body: changelog,
              labels: ['dependencies', 'automated']
            });
            
            console.log(`Created PR #${pr.number}: ${pr.html_url}`);

  # Security vulnerability check
  security-check:
    name: 🔒 Security Vulnerability Check
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔍 Check for security vulnerabilities
        id: security
        run: |
          # Frontend security check
          npm audit --audit-level=high --json > frontend-audit.json || true
          FRONTEND_VULNS=$(jq '.metadata.vulnerabilities.high + .metadata.vulnerabilities.critical' frontend-audit.json || echo "0")
          
          # Backend security check
          cd backend
          npm audit --audit-level=high --json > backend-audit.json || true
          BACKEND_VULNS=$(jq '.metadata.vulnerabilities.high + .metadata.vulnerabilities.critical' backend-audit.json || echo "0")
          
          TOTAL_VULNS=$((FRONTEND_VULNS + BACKEND_VULNS))
          
          echo "frontend_vulnerabilities=$FRONTEND_VULNS" >> $GITHUB_OUTPUT
          echo "backend_vulnerabilities=$BACKEND_VULNS" >> $GITHUB_OUTPUT
          echo "total_vulnerabilities=$TOTAL_VULNS" >> $GITHUB_OUTPUT
          
          if [ "$TOTAL_VULNS" -gt 0 ]; then
            echo "🚨 Found $TOTAL_VULNS high/critical vulnerabilities"
            echo "has_vulnerabilities=true" >> $GITHUB_OUTPUT
          else
            echo "✅ No high/critical vulnerabilities found"
            echo "has_vulnerabilities=false" >> $GITHUB_OUTPUT
          fi

      - name: 🚨 Create security issue
        if: steps.security.outputs.has_vulnerabilities == 'true'
        uses: actions/github-script@v7
        with:
          script: |
            const frontendVulns = '${{ steps.security.outputs.frontend_vulnerabilities }}';
            const backendVulns = '${{ steps.security.outputs.backend_vulnerabilities }}';
            const totalVulns = '${{ steps.security.outputs.total_vulnerabilities }}';
            
            const issueBody = `
            ## 🚨 Security Vulnerabilities Detected
            
            **Total High/Critical Vulnerabilities:** ${totalVulns}
            - Frontend: ${frontendVulns}
            - Backend: ${backendVulns}
            
            ### Action Required
            Please review and update the affected dependencies immediately.
            
            ### Commands to Fix
            \`\`\`bash
            # Check vulnerabilities
            npm audit
            cd backend && npm audit
            
            # Fix automatically (if possible)
            npm audit fix
            cd backend && npm audit fix
            \`\`\`
            
            **Auto-generated by security vulnerability check**
            `;
            
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `🚨 Security Alert: ${totalVulns} High/Critical Vulnerabilities`,
              body: issueBody,
              labels: ['security', 'high-priority', 'dependencies']
            });

  # Notify about updates
  notify:
    name: 📢 Notify Updates
    runs-on: ubuntu-latest
    timeout-minutes: 5
    needs: [check-updates, security-check]
    if: always()
    
    steps:
      - name: 📢 Slack notification
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          channel: '#development'
          custom_payload: |
            {
              "text": "🔄 Dependency Update Report",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": "📦 Dependency Update Report"
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Updates Available:* ${{ needs.check-updates.outputs.has_updates == 'true' && 'Yes' || 'No' }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Security Issues:* ${{ needs.security-check.outputs.has_vulnerabilities == 'true' && 'Yes' || 'No' }}"
                    }
                  ]
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "${{ needs.check-updates.outputs.update_summary }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: 📊 Update dependency dashboard
        run: |
          echo "## 📦 Dependency Status Dashboard" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Component | Status | Action |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|--------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Updates Available | ${{ needs.check-updates.outputs.has_updates == 'true' && '⚠️ Yes' || '✅ No' }} | ${{ needs.check-updates.outputs.has_updates == 'true' && 'PR Created' || 'None Required' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Security Issues | ${{ needs.security-check.outputs.has_vulnerabilities == 'true' && '🚨 Yes' || '✅ No' }} | ${{ needs.security-check.outputs.has_vulnerabilities == 'true' && 'Issue Created' || 'None Required' }} |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "🕐 **Last Check:** $(date -u)" >> $GITHUB_STEP_SUMMARY
