4dab1dd487de6c14261f2dc31d16b9c8
"use strict";

exports.__esModule = true;
exports.default = createOrderedCSSStyleSheet;
var slice = Array.prototype.slice;
function createOrderedCSSStyleSheet(sheet) {
  var groups = {};
  var selectors = {};
  if (sheet != null) {
    var group;
    slice.call(sheet.cssRules).forEach(function (cssRule, i) {
      var cssText = cssRule.cssText;
      if (cssText.indexOf('stylesheet-group') > -1) {
        group = decodeGroupRule(cssRule);
        groups[group] = {
          start: i,
          rules: [cssText]
        };
      } else {
        var selectorText = getSelectorText(cssText);
        if (selectorText != null) {
          selectors[selectorText] = true;
          groups[group].rules.push(cssText);
        }
      }
    });
  }
  function sheetInsert(sheet, group, text) {
    var orderedGroups = getOrderedGroups(groups);
    var groupIndex = orderedGroups.indexOf(group);
    var nextGroupIndex = groupIndex + 1;
    var nextGroup = orderedGroups[nextGroupIndex];
    var position = nextGroup != null && groups[nextGroup].start != null ? groups[nextGroup].start : sheet.cssRules.length;
    var isInserted = insertRuleAt(sheet, text, position);
    if (isInserted) {
      if (groups[group].start == null) {
        groups[group].start = position;
      }
      for (var i = nextGroupIndex; i < orderedGroups.length; i += 1) {
        var groupNumber = orderedGroups[i];
        var previousStart = groups[groupNumber].start || 0;
        groups[groupNumber].start = previousStart + 1;
      }
    }
    return isInserted;
  }
  var OrderedCSSStyleSheet = {
    getTextContent: function getTextContent() {
      return getOrderedGroups(groups).map(function (group) {
        var rules = groups[group].rules;
        var marker = rules.shift();
        rules.sort();
        rules.unshift(marker);
        return rules.join('\n');
      }).join('\n');
    },
    insert: function insert(cssText, groupValue) {
      var group = Number(groupValue);
      if (groups[group] == null) {
        var markerRule = encodeGroupRule(group);
        groups[group] = {
          start: null,
          rules: [markerRule]
        };
        if (sheet != null) {
          sheetInsert(sheet, group, markerRule);
        }
      }
      var selectorText = getSelectorText(cssText);
      if (selectorText != null && selectors[selectorText] == null) {
        selectors[selectorText] = true;
        groups[group].rules.push(cssText);
        if (sheet != null) {
          var isInserted = sheetInsert(sheet, group, cssText);
          if (!isInserted) {
            groups[group].rules.pop();
          }
        }
      }
    }
  };
  return OrderedCSSStyleSheet;
}
function encodeGroupRule(group) {
  return "[stylesheet-group=\"" + group + "\"]{}";
}
var groupPattern = /["']/g;
function decodeGroupRule(cssRule) {
  return Number(cssRule.selectorText.split(groupPattern)[1]);
}
function getOrderedGroups(obj) {
  return Object.keys(obj).map(Number).sort(function (a, b) {
    return a > b ? 1 : -1;
  });
}
var selectorPattern = /\s*([,])\s*/g;
function getSelectorText(cssText) {
  var selector = cssText.split('{')[0].trim();
  return selector !== '' ? selector.replace(selectorPattern, '$1') : null;
}
function insertRuleAt(root, cssText, position) {
  try {
    root.insertRule(cssText, position);
    return true;
  } catch (e) {
    return false;
  }
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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