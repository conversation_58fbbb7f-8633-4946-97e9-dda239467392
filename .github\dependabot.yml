version: 2
updates:
  # Frontend dependencies
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 5
    reviewers:
      - "acemind-team"
    assignees:
      - "acemind-team"
    commit-message:
      prefix: "⬆️"
      include: "scope"
    labels:
      - "dependencies"
      - "frontend"
    ignore:
      # Ignore major version updates for critical dependencies
      - dependency-name: "react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "react-native"
        update-types: ["version-update:semver-major"]
      - dependency-name: "expo"
        update-types: ["version-update:semver-major"]

  # Backend dependencies
  - package-ecosystem: "npm"
    directory: "/backend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 5
    reviewers:
      - "acemind-team"
    assignees:
      - "acemind-team"
    commit-message:
      prefix: "⬆️"
      include: "scope"
    labels:
      - "dependencies"
      - "backend"
    ignore:
      # Ignore major version updates for critical dependencies
      - dependency-name: "express"
        update-types: ["version-update:semver-major"]
      - dependency-name: "typescript"
        update-types: ["version-update:semver-major"]

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 3
    reviewers:
      - "acemind-team"
    assignees:
      - "acemind-team"
    commit-message:
      prefix: "⬆️"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"

  # Docker dependencies
  - package-ecosystem: "docker"
    directory: "/backend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 2
    reviewers:
      - "acemind-team"
    assignees:
      - "acemind-team"
    commit-message:
      prefix: "⬆️"
      include: "scope"
    labels:
      - "dependencies"
      - "docker"
