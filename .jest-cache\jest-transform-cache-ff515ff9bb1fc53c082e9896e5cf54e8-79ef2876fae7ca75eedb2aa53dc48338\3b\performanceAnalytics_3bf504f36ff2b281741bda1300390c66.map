{"version": 3, "names": ["PerformanceAnalyticsService", "_classCallCheck", "_createClass", "key", "value", "analyzeMatchPerformance", "stats", "cov_2gc0uz0ypt", "f", "overallRating", "s", "calculateOverallRating", "insights", "generatePerformanceInsights", "advancedMetrics", "calculateAdvancedMetrics", "analyzeShotPerformance", "shots", "sortedByAccuracy", "_toConsumableArray", "sort", "a", "b", "accuracy", "bestShots", "slice", "weakestShots", "overallAccuracy", "reduce", "sum", "shot", "length", "recommendations", "generateShotRecommendations", "analyzePerformanceTrends", "recentMatches", "historicalData", "trendAnalysis", "analyzeTrends", "projectedImprovement", "projectSkillImprovement", "skillProgression", "trainingRecommendations", "generateTrainingRecommendations", "analyzeTacticalPerformance", "playerStats", "opponent<PERSON><PERSON><PERSON>", "courtSurface", "tacticalEffectiveness", "calculateTacticalEffectiveness", "successfulTactics", "identifySuccessfulTactics", "tacticalAdjustments", "suggestTacticalAdjustments", "surfaceAdaptation", "calculateSurfaceAdaptation", "analyzeFitnessMetrics", "performanceBySet", "matchDuration", "enduranceRating", "calculateEnduranceRating", "fatigueImpact", "calculateFatigueImpact", "fitnessRecommendations", "generateFitnessRecommendations", "recoveryNeeded", "calculateRecoveryTime", "winPercentage", "pointsWon", "totalPoints", "errorRate", "unforcedErrors", "doubleFaults", "winnerRate", "winners", "rating", "firstServePercentage", "Math", "round", "max", "min", "strengths", "weaknesses", "nextTrainingFocus", "push", "netApproaches", "netSuccessRate", "netPointsWon", "competitiveReadiness", "calculateCompetitiveReadiness", "aggressiveness", "consistency", "courtCoverage", "mentalToughness", "calculateMentalToughness", "adaptability", "fitnessLevel", "breakPointConversion", "breakPointsTotal", "breakPointsConverted", "factors", "factor", "for<PERSON>ach", "shotType", "errors", "trends", "recentAvgWinRate", "match", "filter", "skill", "trend", "map", "projectedRating", "currentRating", "changeRate", "some", "includes", "baseEffectiveness", "adjustment", "tactics", "adjustments", "surface", "baseScore", "firstSetWinRate", "lastSetWinRate", "enduranceRatio", "errorRateIncrease", "set", "avgIncrease", "endurance", "fatigue", "baseRecovery", "fatigueMultiplier", "performanceAnalyticsService"], "sources": ["performanceAnalytics.ts"], "sourcesContent": ["// Performance Analytics Service for Tennis Match Statistics and Analysis\n\nexport interface MatchStatistics {\n  totalPoints: number;\n  pointsWon: number;\n  winners: number;\n  unforcedErrors: number;\n  forcedErrors: number;\n  aces: number;\n  doubleFaults: number;\n  firstServePercentage: number;\n  firstServePointsWon: number;\n  secondServePointsWon: number;\n  breakPointsConverted: number;\n  breakPointsTotal: number;\n  netApproaches: number;\n  netPointsWon: number;\n  returnPointsWon: number;\n  totalGameTime: number; // in minutes\n}\n\nexport interface ShotAnalytics {\n  shotType: 'forehand' | 'backhand' | 'serve' | 'volley' | 'overhead' | 'return';\n  total: number;\n  winners: number;\n  errors: number;\n  accuracy: number;\n  averageSpeed?: number;\n  placement: {\n    crossCourt: number;\n    downTheLine: number;\n    center: number;\n  };\n}\n\nexport interface CourtPositionData {\n  position: 'baseline' | 'mid_court' | 'net' | 'wide_left' | 'wide_right';\n  timeSpent: number; // percentage\n  pointsWon: number;\n  pointsPlayed: number;\n}\n\nexport interface PerformanceTrends {\n  skillProgression: {\n    skill: string;\n    previousRating: number;\n    currentRating: number;\n    trend: 'improving' | 'stable' | 'declining';\n    changeRate: number;\n  }[];\n  matchPerformance: {\n    date: string;\n    opponent: string;\n    result: 'win' | 'loss';\n    score: string;\n    keyMetrics: {\n      winners: number;\n      errors: number;\n      firstServePercentage: number;\n    };\n  }[];\n  weeklyProgress: {\n    week: string;\n    sessionsCompleted: number;\n    averageScore: number;\n    improvementAreas: string[];\n  }[];\n}\n\nexport interface AdvancedMetrics {\n  aggressiveness: number; // 0-100, based on shot selection\n  consistency: number; // 0-100, based on error rate\n  courtCoverage: number; // 0-100, based on movement efficiency\n  mentalToughness: number; // 0-100, based on pressure point performance\n  adaptability: number; // 0-100, based on tactical adjustments\n  fitnessLevel: number; // 0-100, based on performance over time\n}\n\nexport interface PerformanceInsights {\n  strengths: string[];\n  weaknesses: string[];\n  recommendations: string[];\n  nextTrainingFocus: string[];\n  competitiveReadiness: number;\n}\n\nclass PerformanceAnalyticsService {\n  /**\n   * Analyze match statistics and generate insights\n   */\n  analyzeMatchPerformance(stats: MatchStatistics): {\n    overallRating: number;\n    insights: PerformanceInsights;\n    advancedMetrics: AdvancedMetrics;\n  } {\n    const overallRating = this.calculateOverallRating(stats);\n    const insights = this.generatePerformanceInsights(stats);\n    const advancedMetrics = this.calculateAdvancedMetrics(stats);\n\n    return {\n      overallRating,\n      insights,\n      advancedMetrics,\n    };\n  }\n\n  /**\n   * Analyze shot-by-shot performance\n   */\n  analyzeShotPerformance(shots: ShotAnalytics[]): {\n    bestShots: ShotAnalytics[];\n    weakestShots: ShotAnalytics[];\n    overallAccuracy: number;\n    recommendations: string[];\n  } {\n    const sortedByAccuracy = [...shots].sort((a, b) => b.accuracy - a.accuracy);\n    const bestShots = sortedByAccuracy.slice(0, 2);\n    const weakestShots = sortedByAccuracy.slice(-2);\n    \n    const overallAccuracy = shots.reduce((sum, shot) => sum + shot.accuracy, 0) / shots.length;\n    \n    const recommendations = this.generateShotRecommendations(shots);\n\n    return {\n      bestShots,\n      weakestShots,\n      overallAccuracy,\n      recommendations,\n    };\n  }\n\n  /**\n   * Track performance trends over time\n   */\n  analyzePerformanceTrends(\n    recentMatches: MatchStatistics[],\n    historicalData: PerformanceTrends\n  ): {\n    trendAnalysis: string[];\n    projectedImprovement: { skill: string; projectedRating: number }[];\n    trainingRecommendations: string[];\n  } {\n    const trendAnalysis = this.analyzeTrends(recentMatches, historicalData);\n    const projectedImprovement = this.projectSkillImprovement(historicalData.skillProgression);\n    const trainingRecommendations = this.generateTrainingRecommendations(trendAnalysis);\n\n    return {\n      trendAnalysis,\n      projectedImprovement,\n      trainingRecommendations,\n    };\n  }\n\n  /**\n   * Generate tactical analysis for specific opponents\n   */\n  analyzeTacticalPerformance(\n    playerStats: MatchStatistics,\n    opponentStyle: string,\n    courtSurface: string\n  ): {\n    tacticalEffectiveness: number;\n    successfulTactics: string[];\n    tacticalAdjustments: string[];\n    surfaceAdaptation: number;\n  } {\n    const tacticalEffectiveness = this.calculateTacticalEffectiveness(playerStats, opponentStyle);\n    const successfulTactics = this.identifySuccessfulTactics(playerStats, opponentStyle);\n    const tacticalAdjustments = this.suggestTacticalAdjustments(playerStats, opponentStyle);\n    const surfaceAdaptation = this.calculateSurfaceAdaptation(playerStats, courtSurface);\n\n    return {\n      tacticalEffectiveness,\n      successfulTactics,\n      tacticalAdjustments,\n      surfaceAdaptation,\n    };\n  }\n\n  /**\n   * Calculate fitness and endurance metrics\n   */\n  analyzeFitnessMetrics(\n    performanceBySet: MatchStatistics[],\n    matchDuration: number\n  ): {\n    enduranceRating: number;\n    fatigueImpact: number;\n    fitnessRecommendations: string[];\n    recoveryNeeded: number; // hours\n  } {\n    const enduranceRating = this.calculateEnduranceRating(performanceBySet);\n    const fatigueImpact = this.calculateFatigueImpact(performanceBySet);\n    const fitnessRecommendations = this.generateFitnessRecommendations(enduranceRating, fatigueImpact);\n    const recoveryNeeded = this.calculateRecoveryTime(matchDuration, fatigueImpact);\n\n    return {\n      enduranceRating,\n      fatigueImpact,\n      fitnessRecommendations,\n      recoveryNeeded,\n    };\n  }\n\n  // Private calculation methods\n\n  private calculateOverallRating(stats: MatchStatistics): number {\n    const winPercentage = (stats.pointsWon / stats.totalPoints) * 100;\n    const errorRate = ((stats.unforcedErrors + stats.doubleFaults) / stats.totalPoints) * 100;\n    const winnerRate = (stats.winners / stats.totalPoints) * 100;\n    \n    // Weighted scoring\n    const rating = (\n      winPercentage * 0.4 +\n      (100 - errorRate) * 0.3 +\n      winnerRate * 0.2 +\n      stats.firstServePercentage * 0.1\n    );\n\n    return Math.round(Math.max(0, Math.min(100, rating)));\n  }\n\n  private generatePerformanceInsights(stats: MatchStatistics): PerformanceInsights {\n    const strengths: string[] = [];\n    const weaknesses: string[] = [];\n    const recommendations: string[] = [];\n    const nextTrainingFocus: string[] = [];\n\n    // Analyze serve performance\n    if (stats.firstServePercentage > 65) {\n      strengths.push('Consistent first serve');\n    } else {\n      weaknesses.push('First serve consistency');\n      recommendations.push('Focus on serve placement drills');\n      nextTrainingFocus.push('Serve technique');\n    }\n\n    // Analyze error rate\n    const errorRate = (stats.unforcedErrors / stats.totalPoints) * 100;\n    if (errorRate < 15) {\n      strengths.push('Low unforced error rate');\n    } else {\n      weaknesses.push('Too many unforced errors');\n      recommendations.push('Work on shot selection and patience');\n      nextTrainingFocus.push('Consistency training');\n    }\n\n    // Analyze net play\n    if (stats.netApproaches > 0) {\n      const netSuccessRate = (stats.netPointsWon / stats.netApproaches) * 100;\n      if (netSuccessRate > 70) {\n        strengths.push('Effective net play');\n      } else {\n        weaknesses.push('Net game needs improvement');\n        nextTrainingFocus.push('Volley practice');\n      }\n    }\n\n    const competitiveReadiness = this.calculateCompetitiveReadiness(stats);\n\n    return {\n      strengths,\n      weaknesses,\n      recommendations,\n      nextTrainingFocus,\n      competitiveReadiness,\n    };\n  }\n\n  private calculateAdvancedMetrics(stats: MatchStatistics): AdvancedMetrics {\n    const aggressiveness = Math.min(100, (stats.winners / (stats.winners + stats.unforcedErrors)) * 100);\n    const consistency = Math.max(0, 100 - ((stats.unforcedErrors / stats.totalPoints) * 100 * 5));\n    const courtCoverage = 75; // Would be calculated from movement data\n    const mentalToughness = this.calculateMentalToughness(stats);\n    const adaptability = 70; // Would be calculated from tactical adjustments\n    const fitnessLevel = 80; // Would be calculated from performance over time\n\n    return {\n      aggressiveness: Math.round(aggressiveness),\n      consistency: Math.round(consistency),\n      courtCoverage,\n      mentalToughness,\n      adaptability,\n      fitnessLevel,\n    };\n  }\n\n  private calculateMentalToughness(stats: MatchStatistics): number {\n    // Calculate based on break point conversion and pressure situations\n    const breakPointConversion = stats.breakPointsTotal > 0 \n      ? (stats.breakPointsConverted / stats.breakPointsTotal) * 100 \n      : 50;\n    \n    return Math.round(Math.min(100, breakPointConversion * 1.2));\n  }\n\n  private calculateCompetitiveReadiness(stats: MatchStatistics): number {\n    const factors = [\n      stats.firstServePercentage,\n      Math.max(0, 100 - ((stats.unforcedErrors / stats.totalPoints) * 100 * 3)),\n      (stats.pointsWon / stats.totalPoints) * 100,\n      stats.breakPointsTotal > 0 ? (stats.breakPointsConverted / stats.breakPointsTotal) * 100 : 70,\n    ];\n\n    return Math.round(factors.reduce((sum, factor) => sum + factor, 0) / factors.length);\n  }\n\n  private generateShotRecommendations(shots: ShotAnalytics[]): string[] {\n    const recommendations: string[] = [];\n    \n    shots.forEach(shot => {\n      if (shot.accuracy < 60) {\n        recommendations.push(`Improve ${shot.shotType} consistency through repetition drills`);\n      }\n      if (shot.errors > shot.winners) {\n        recommendations.push(`Focus on ${shot.shotType} placement over power`);\n      }\n    });\n\n    return recommendations;\n  }\n\n  private analyzeTrends(recentMatches: MatchStatistics[], historicalData: PerformanceTrends): string[] {\n    const trends: string[] = [];\n    \n    if (recentMatches.length >= 3) {\n      const recentAvgWinRate = recentMatches.reduce((sum, match) => \n        sum + (match.pointsWon / match.totalPoints), 0) / recentMatches.length;\n      \n      if (recentAvgWinRate > 0.55) {\n        trends.push('Improving match performance over recent games');\n      } else if (recentAvgWinRate < 0.45) {\n        trends.push('Performance decline in recent matches - focus on fundamentals');\n      }\n    }\n\n    return trends;\n  }\n\n  private projectSkillImprovement(skillProgression: PerformanceTrends['skillProgression']) {\n    return skillProgression\n      .filter(skill => skill.trend === 'improving')\n      .map(skill => ({\n        skill: skill.skill,\n        projectedRating: Math.min(100, skill.currentRating + (skill.changeRate * 4)), // 4 weeks projection\n      }));\n  }\n\n  private generateTrainingRecommendations(trendAnalysis: string[]): string[] {\n    const recommendations: string[] = [];\n    \n    if (trendAnalysis.some(trend => trend.includes('decline'))) {\n      recommendations.push('Increase practice frequency and focus on fundamentals');\n      recommendations.push('Consider working with a coach to identify technical issues');\n    }\n    \n    if (trendAnalysis.some(trend => trend.includes('improving'))) {\n      recommendations.push('Maintain current training routine');\n      recommendations.push('Consider adding more challenging practice scenarios');\n    }\n\n    return recommendations;\n  }\n\n  private calculateTacticalEffectiveness(stats: MatchStatistics, opponentStyle: string): number {\n    // Calculate based on how well tactics worked against specific opponent style\n    const baseEffectiveness = (stats.pointsWon / stats.totalPoints) * 100;\n    \n    // Adjust based on opponent style\n    let adjustment = 0;\n    if (opponentStyle === 'aggressive' && stats.unforcedErrors < stats.totalPoints * 0.15) {\n      adjustment = 10; // Good defense against aggressive player\n    }\n    \n    return Math.round(Math.min(100, baseEffectiveness + adjustment));\n  }\n\n  private identifySuccessfulTactics(stats: MatchStatistics, opponentStyle: string): string[] {\n    const tactics: string[] = [];\n    \n    if (stats.netApproaches > 0 && (stats.netPointsWon / stats.netApproaches) > 0.7) {\n      tactics.push('Effective net approaches');\n    }\n    \n    if (stats.firstServePercentage > 70) {\n      tactics.push('Strong serve placement');\n    }\n    \n    return tactics;\n  }\n\n  private suggestTacticalAdjustments(stats: MatchStatistics, opponentStyle: string): string[] {\n    const adjustments: string[] = [];\n    \n    if (stats.unforcedErrors > stats.totalPoints * 0.2) {\n      adjustments.push('Reduce risk-taking, focus on consistency');\n    }\n    \n    if (opponentStyle === 'defensive' && stats.winners < stats.totalPoints * 0.1) {\n      adjustments.push('Increase aggression and court positioning');\n    }\n    \n    return adjustments;\n  }\n\n  private calculateSurfaceAdaptation(stats: MatchStatistics, surface: string): number {\n    // Calculate how well player adapted to surface\n    let baseScore = 70;\n    \n    if (surface === 'clay' && stats.unforcedErrors < stats.totalPoints * 0.15) {\n      baseScore += 15; // Good patience on clay\n    }\n    \n    if (surface === 'grass' && stats.netApproaches > stats.totalPoints * 0.1) {\n      baseScore += 10; // Good net play on grass\n    }\n    \n    return Math.min(100, baseScore);\n  }\n\n  private calculateEnduranceRating(performanceBySet: MatchStatistics[]): number {\n    if (performanceBySet.length < 2) return 75;\n    \n    const firstSetWinRate = performanceBySet[0].pointsWon / performanceBySet[0].totalPoints;\n    const lastSetWinRate = performanceBySet[performanceBySet.length - 1].pointsWon / performanceBySet[performanceBySet.length - 1].totalPoints;\n    \n    const enduranceRatio = lastSetWinRate / firstSetWinRate;\n    return Math.round(Math.min(100, enduranceRatio * 75));\n  }\n\n  private calculateFatigueImpact(performanceBySet: MatchStatistics[]): number {\n    if (performanceBySet.length < 2) return 10;\n    \n    const errorRateIncrease = performanceBySet.map(set => set.unforcedErrors / set.totalPoints);\n    const avgIncrease = errorRateIncrease[errorRateIncrease.length - 1] - errorRateIncrease[0];\n    \n    return Math.round(Math.max(0, Math.min(100, avgIncrease * 500)));\n  }\n\n  private generateFitnessRecommendations(endurance: number, fatigue: number): string[] {\n    const recommendations: string[] = [];\n    \n    if (endurance < 60) {\n      recommendations.push('Increase cardiovascular training');\n      recommendations.push('Add longer practice sessions to build endurance');\n    }\n    \n    if (fatigue > 30) {\n      recommendations.push('Focus on recovery between points');\n      recommendations.push('Improve physical conditioning');\n    }\n    \n    return recommendations;\n  }\n\n  private calculateRecoveryTime(matchDuration: number, fatigueImpact: number): number {\n    const baseRecovery = matchDuration / 60 * 2; // 2 hours per hour of play\n    const fatigueMultiplier = 1 + (fatigueImpact / 100);\n    \n    return Math.round(baseRecovery * fatigueMultiplier);\n  }\n}\n\nexport const performanceAnalyticsService = new PerformanceAnalyticsService();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsFMA,2BAA2B;EAAA,SAAAA,4BAAA;IAAAC,eAAA,OAAAD,2BAAA;EAAA;EAAA,OAAAE,YAAA,CAAAF,2BAAA;IAAAG,GAAA;IAAAC,KAAA,EAI/B,SAAAC,uBAAuBA,CAACC,KAAsB,EAI5C;MAAAC,cAAA,GAAAC,CAAA;MACA,IAAMC,aAAa,IAAAF,cAAA,GAAAG,CAAA,OAAG,IAAI,CAACC,sBAAsB,CAACL,KAAK,CAAC;MACxD,IAAMM,QAAQ,IAAAL,cAAA,GAAAG,CAAA,OAAG,IAAI,CAACG,2BAA2B,CAACP,KAAK,CAAC;MACxD,IAAMQ,eAAe,IAAAP,cAAA,GAAAG,CAAA,OAAG,IAAI,CAACK,wBAAwB,CAACT,KAAK,CAAC;MAACC,cAAA,GAAAG,CAAA;MAE7D,OAAO;QACLD,aAAa,EAAbA,aAAa;QACbG,QAAQ,EAARA,QAAQ;QACRE,eAAe,EAAfA;MACF,CAAC;IACH;EAAC;IAAAX,GAAA;IAAAC,KAAA,EAKD,SAAAY,sBAAsBA,CAACC,KAAsB,EAK3C;MAAAV,cAAA,GAAAC,CAAA;MACA,IAAMU,gBAAgB,IAAAX,cAAA,GAAAG,CAAA,OAAGS,kBAAA,CAAIF,KAAK,EAAEG,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;QAAAf,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QAAA,OAAAY,CAAC,CAACC,QAAQ,GAAGF,CAAC,CAACE,QAAQ;MAAD,CAAC,CAAC;MAC3E,IAAMC,SAAS,IAAAjB,cAAA,GAAAG,CAAA,OAAGQ,gBAAgB,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAC9C,IAAMC,YAAY,IAAAnB,cAAA,GAAAG,CAAA,OAAGQ,gBAAgB,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC;MAE/C,IAAME,eAAe,IAAApB,cAAA,GAAAG,CAAA,OAAGO,KAAK,CAACW,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI,EAAK;QAAAvB,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QAAA,OAAAmB,GAAG,GAAGC,IAAI,CAACP,QAAQ;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGN,KAAK,CAACc,MAAM;MAE1F,IAAMC,eAAe,IAAAzB,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACuB,2BAA2B,CAAChB,KAAK,CAAC;MAACV,cAAA,GAAAG,CAAA;MAEhE,OAAO;QACLc,SAAS,EAATA,SAAS;QACTE,YAAY,EAAZA,YAAY;QACZC,eAAe,EAAfA,eAAe;QACfK,eAAe,EAAfA;MACF,CAAC;IACH;EAAC;IAAA7B,GAAA;IAAAC,KAAA,EAKD,SAAA8B,wBAAwBA,CACtBC,aAAgC,EAChCC,cAAiC,EAKjC;MAAA7B,cAAA,GAAAC,CAAA;MACA,IAAM6B,aAAa,IAAA9B,cAAA,GAAAG,CAAA,QAAG,IAAI,CAAC4B,aAAa,CAACH,aAAa,EAAEC,cAAc,CAAC;MACvE,IAAMG,oBAAoB,IAAAhC,cAAA,GAAAG,CAAA,QAAG,IAAI,CAAC8B,uBAAuB,CAACJ,cAAc,CAACK,gBAAgB,CAAC;MAC1F,IAAMC,uBAAuB,IAAAnC,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACiC,+BAA+B,CAACN,aAAa,CAAC;MAAC9B,cAAA,GAAAG,CAAA;MAEpF,OAAO;QACL2B,aAAa,EAAbA,aAAa;QACbE,oBAAoB,EAApBA,oBAAoB;QACpBG,uBAAuB,EAAvBA;MACF,CAAC;IACH;EAAC;IAAAvC,GAAA;IAAAC,KAAA,EAKD,SAAAwC,0BAA0BA,CACxBC,WAA4B,EAC5BC,aAAqB,EACrBC,YAAoB,EAMpB;MAAAxC,cAAA,GAAAC,CAAA;MACA,IAAMwC,qBAAqB,IAAAzC,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACuC,8BAA8B,CAACJ,WAAW,EAAEC,aAAa,CAAC;MAC7F,IAAMI,iBAAiB,IAAA3C,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACyC,yBAAyB,CAACN,WAAW,EAAEC,aAAa,CAAC;MACpF,IAAMM,mBAAmB,IAAA7C,cAAA,GAAAG,CAAA,QAAG,IAAI,CAAC2C,0BAA0B,CAACR,WAAW,EAAEC,aAAa,CAAC;MACvF,IAAMQ,iBAAiB,IAAA/C,cAAA,GAAAG,CAAA,QAAG,IAAI,CAAC6C,0BAA0B,CAACV,WAAW,EAAEE,YAAY,CAAC;MAACxC,cAAA,GAAAG,CAAA;MAErF,OAAO;QACLsC,qBAAqB,EAArBA,qBAAqB;QACrBE,iBAAiB,EAAjBA,iBAAiB;QACjBE,mBAAmB,EAAnBA,mBAAmB;QACnBE,iBAAiB,EAAjBA;MACF,CAAC;IACH;EAAC;IAAAnD,GAAA;IAAAC,KAAA,EAKD,SAAAoD,qBAAqBA,CACnBC,gBAAmC,EACnCC,aAAqB,EAMrB;MAAAnD,cAAA,GAAAC,CAAA;MACA,IAAMmD,eAAe,IAAApD,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACkD,wBAAwB,CAACH,gBAAgB,CAAC;MACvE,IAAMI,aAAa,IAAAtD,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACoD,sBAAsB,CAACL,gBAAgB,CAAC;MACnE,IAAMM,sBAAsB,IAAAxD,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACsD,8BAA8B,CAACL,eAAe,EAAEE,aAAa,CAAC;MAClG,IAAMI,cAAc,IAAA1D,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACwD,qBAAqB,CAACR,aAAa,EAAEG,aAAa,CAAC;MAACtD,cAAA,GAAAG,CAAA;MAEhF,OAAO;QACLiD,eAAe,EAAfA,eAAe;QACfE,aAAa,EAAbA,aAAa;QACbE,sBAAsB,EAAtBA,sBAAsB;QACtBE,cAAc,EAAdA;MACF,CAAC;IACH;EAAC;IAAA9D,GAAA;IAAAC,KAAA,EAID,SAAQO,sBAAsBA,CAACL,KAAsB,EAAU;MAAAC,cAAA,GAAAC,CAAA;MAC7D,IAAM2D,aAAa,IAAA5D,cAAA,GAAAG,CAAA,QAAIJ,KAAK,CAAC8D,SAAS,GAAG9D,KAAK,CAAC+D,WAAW,GAAI,GAAG;MACjE,IAAMC,SAAS,IAAA/D,cAAA,GAAAG,CAAA,QAAI,CAACJ,KAAK,CAACiE,cAAc,GAAGjE,KAAK,CAACkE,YAAY,IAAIlE,KAAK,CAAC+D,WAAW,GAAI,GAAG;MACzF,IAAMI,UAAU,IAAAlE,cAAA,GAAAG,CAAA,QAAIJ,KAAK,CAACoE,OAAO,GAAGpE,KAAK,CAAC+D,WAAW,GAAI,GAAG;MAG5D,IAAMM,MAAM,IAAApE,cAAA,GAAAG,CAAA,QACVyD,aAAa,GAAG,GAAG,GACnB,CAAC,GAAG,GAAGG,SAAS,IAAI,GAAG,GACvBG,UAAU,GAAG,GAAG,GAChBnE,KAAK,CAACsE,oBAAoB,GAAG,GAAG,CACjC;MAACrE,cAAA,GAAAG,CAAA;MAEF,OAAOmE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACG,GAAG,CAAC,GAAG,EAAEL,MAAM,CAAC,CAAC,CAAC;IACvD;EAAC;IAAAxE,GAAA;IAAAC,KAAA,EAED,SAAQS,2BAA2BA,CAACP,KAAsB,EAAuB;MAAAC,cAAA,GAAAC,CAAA;MAC/E,IAAMyE,SAAmB,IAAA1E,cAAA,GAAAG,CAAA,QAAG,EAAE;MAC9B,IAAMwE,UAAoB,IAAA3E,cAAA,GAAAG,CAAA,QAAG,EAAE;MAC/B,IAAMsB,eAAyB,IAAAzB,cAAA,GAAAG,CAAA,QAAG,EAAE;MACpC,IAAMyE,iBAA2B,IAAA5E,cAAA,GAAAG,CAAA,QAAG,EAAE;MAACH,cAAA,GAAAG,CAAA;MAGvC,IAAIJ,KAAK,CAACsE,oBAAoB,GAAG,EAAE,EAAE;QAAArE,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QACnCuE,SAAS,CAACG,IAAI,CAAC,wBAAwB,CAAC;MAC1C,CAAC,MAAM;QAAA7E,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QACLwE,UAAU,CAACE,IAAI,CAAC,yBAAyB,CAAC;QAAC7E,cAAA,GAAAG,CAAA;QAC3CsB,eAAe,CAACoD,IAAI,CAAC,iCAAiC,CAAC;QAAC7E,cAAA,GAAAG,CAAA;QACxDyE,iBAAiB,CAACC,IAAI,CAAC,iBAAiB,CAAC;MAC3C;MAGA,IAAMd,SAAS,IAAA/D,cAAA,GAAAG,CAAA,QAAIJ,KAAK,CAACiE,cAAc,GAAGjE,KAAK,CAAC+D,WAAW,GAAI,GAAG;MAAC9D,cAAA,GAAAG,CAAA;MACnE,IAAI4D,SAAS,GAAG,EAAE,EAAE;QAAA/D,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAClBuE,SAAS,CAACG,IAAI,CAAC,yBAAyB,CAAC;MAC3C,CAAC,MAAM;QAAA7E,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QACLwE,UAAU,CAACE,IAAI,CAAC,0BAA0B,CAAC;QAAC7E,cAAA,GAAAG,CAAA;QAC5CsB,eAAe,CAACoD,IAAI,CAAC,qCAAqC,CAAC;QAAC7E,cAAA,GAAAG,CAAA;QAC5DyE,iBAAiB,CAACC,IAAI,CAAC,sBAAsB,CAAC;MAChD;MAAC7E,cAAA,GAAAG,CAAA;MAGD,IAAIJ,KAAK,CAAC+E,aAAa,GAAG,CAAC,EAAE;QAAA9E,cAAA,GAAAe,CAAA;QAC3B,IAAMgE,cAAc,IAAA/E,cAAA,GAAAG,CAAA,QAAIJ,KAAK,CAACiF,YAAY,GAAGjF,KAAK,CAAC+E,aAAa,GAAI,GAAG;QAAC9E,cAAA,GAAAG,CAAA;QACxE,IAAI4E,cAAc,GAAG,EAAE,EAAE;UAAA/E,cAAA,GAAAe,CAAA;UAAAf,cAAA,GAAAG,CAAA;UACvBuE,SAAS,CAACG,IAAI,CAAC,oBAAoB,CAAC;QACtC,CAAC,MAAM;UAAA7E,cAAA,GAAAe,CAAA;UAAAf,cAAA,GAAAG,CAAA;UACLwE,UAAU,CAACE,IAAI,CAAC,4BAA4B,CAAC;UAAC7E,cAAA,GAAAG,CAAA;UAC9CyE,iBAAiB,CAACC,IAAI,CAAC,iBAAiB,CAAC;QAC3C;MACF,CAAC;QAAA7E,cAAA,GAAAe,CAAA;MAAA;MAED,IAAMkE,oBAAoB,IAAAjF,cAAA,GAAAG,CAAA,QAAG,IAAI,CAAC+E,6BAA6B,CAACnF,KAAK,CAAC;MAACC,cAAA,GAAAG,CAAA;MAEvE,OAAO;QACLuE,SAAS,EAATA,SAAS;QACTC,UAAU,EAAVA,UAAU;QACVlD,eAAe,EAAfA,eAAe;QACfmD,iBAAiB,EAAjBA,iBAAiB;QACjBK,oBAAoB,EAApBA;MACF,CAAC;IACH;EAAC;IAAArF,GAAA;IAAAC,KAAA,EAED,SAAQW,wBAAwBA,CAACT,KAAsB,EAAmB;MAAAC,cAAA,GAAAC,CAAA;MACxE,IAAMkF,cAAc,IAAAnF,cAAA,GAAAG,CAAA,QAAGmE,IAAI,CAACG,GAAG,CAAC,GAAG,EAAG1E,KAAK,CAACoE,OAAO,IAAIpE,KAAK,CAACoE,OAAO,GAAGpE,KAAK,CAACiE,cAAc,CAAC,GAAI,GAAG,CAAC;MACpG,IAAMoB,WAAW,IAAApF,cAAA,GAAAG,CAAA,QAAGmE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,GAAG,GAAKzE,KAAK,CAACiE,cAAc,GAAGjE,KAAK,CAAC+D,WAAW,GAAI,GAAG,GAAG,CAAE,CAAC;MAC7F,IAAMuB,aAAa,IAAArF,cAAA,GAAAG,CAAA,QAAG,EAAE;MACxB,IAAMmF,eAAe,IAAAtF,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACoF,wBAAwB,CAACxF,KAAK,CAAC;MAC5D,IAAMyF,YAAY,IAAAxF,cAAA,GAAAG,CAAA,QAAG,EAAE;MACvB,IAAMsF,YAAY,IAAAzF,cAAA,GAAAG,CAAA,QAAG,EAAE;MAACH,cAAA,GAAAG,CAAA;MAExB,OAAO;QACLgF,cAAc,EAAEb,IAAI,CAACC,KAAK,CAACY,cAAc,CAAC;QAC1CC,WAAW,EAAEd,IAAI,CAACC,KAAK,CAACa,WAAW,CAAC;QACpCC,aAAa,EAAbA,aAAa;QACbC,eAAe,EAAfA,eAAe;QACfE,YAAY,EAAZA,YAAY;QACZC,YAAY,EAAZA;MACF,CAAC;IACH;EAAC;IAAA7F,GAAA;IAAAC,KAAA,EAED,SAAQ0F,wBAAwBA,CAACxF,KAAsB,EAAU;MAAAC,cAAA,GAAAC,CAAA;MAE/D,IAAMyF,oBAAoB,IAAA1F,cAAA,GAAAG,CAAA,QAAGJ,KAAK,CAAC4F,gBAAgB,GAAG,CAAC,IAAA3F,cAAA,GAAAe,CAAA,UAClDhB,KAAK,CAAC6F,oBAAoB,GAAG7F,KAAK,CAAC4F,gBAAgB,GAAI,GAAG,KAAA3F,cAAA,GAAAe,CAAA,UAC3D,EAAE;MAACf,cAAA,GAAAG,CAAA;MAEP,OAAOmE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACG,GAAG,CAAC,GAAG,EAAEiB,oBAAoB,GAAG,GAAG,CAAC,CAAC;IAC9D;EAAC;IAAA9F,GAAA;IAAAC,KAAA,EAED,SAAQqF,6BAA6BA,CAACnF,KAAsB,EAAU;MAAAC,cAAA,GAAAC,CAAA;MACpE,IAAM4F,OAAO,IAAA7F,cAAA,GAAAG,CAAA,QAAG,CACdJ,KAAK,CAACsE,oBAAoB,EAC1BC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,GAAG,GAAKzE,KAAK,CAACiE,cAAc,GAAGjE,KAAK,CAAC+D,WAAW,GAAI,GAAG,GAAG,CAAE,CAAC,EACxE/D,KAAK,CAAC8D,SAAS,GAAG9D,KAAK,CAAC+D,WAAW,GAAI,GAAG,EAC3C/D,KAAK,CAAC4F,gBAAgB,GAAG,CAAC,IAAA3F,cAAA,GAAAe,CAAA,UAAIhB,KAAK,CAAC6F,oBAAoB,GAAG7F,KAAK,CAAC4F,gBAAgB,GAAI,GAAG,KAAA3F,cAAA,GAAAe,CAAA,UAAG,EAAE,EAC9F;MAACf,cAAA,GAAAG,CAAA;MAEF,OAAOmE,IAAI,CAACC,KAAK,CAACsB,OAAO,CAACxE,MAAM,CAAC,UAACC,GAAG,EAAEwE,MAAM,EAAK;QAAA9F,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QAAA,OAAAmB,GAAG,GAAGwE,MAAM;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGD,OAAO,CAACrE,MAAM,CAAC;IACtF;EAAC;IAAA5B,GAAA;IAAAC,KAAA,EAED,SAAQ6B,2BAA2BA,CAAChB,KAAsB,EAAY;MAAAV,cAAA,GAAAC,CAAA;MACpE,IAAMwB,eAAyB,IAAAzB,cAAA,GAAAG,CAAA,QAAG,EAAE;MAACH,cAAA,GAAAG,CAAA;MAErCO,KAAK,CAACqF,OAAO,CAAC,UAAAxE,IAAI,EAAI;QAAAvB,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QACpB,IAAIoB,IAAI,CAACP,QAAQ,GAAG,EAAE,EAAE;UAAAhB,cAAA,GAAAe,CAAA;UAAAf,cAAA,GAAAG,CAAA;UACtBsB,eAAe,CAACoD,IAAI,CAAC,WAAWtD,IAAI,CAACyE,QAAQ,wCAAwC,CAAC;QACxF,CAAC;UAAAhG,cAAA,GAAAe,CAAA;QAAA;QAAAf,cAAA,GAAAG,CAAA;QACD,IAAIoB,IAAI,CAAC0E,MAAM,GAAG1E,IAAI,CAAC4C,OAAO,EAAE;UAAAnE,cAAA,GAAAe,CAAA;UAAAf,cAAA,GAAAG,CAAA;UAC9BsB,eAAe,CAACoD,IAAI,CAAC,YAAYtD,IAAI,CAACyE,QAAQ,uBAAuB,CAAC;QACxE,CAAC;UAAAhG,cAAA,GAAAe,CAAA;QAAA;MACH,CAAC,CAAC;MAACf,cAAA,GAAAG,CAAA;MAEH,OAAOsB,eAAe;IACxB;EAAC;IAAA7B,GAAA;IAAAC,KAAA,EAED,SAAQkC,aAAaA,CAACH,aAAgC,EAAEC,cAAiC,EAAY;MAAA7B,cAAA,GAAAC,CAAA;MACnG,IAAMiG,MAAgB,IAAAlG,cAAA,GAAAG,CAAA,QAAG,EAAE;MAACH,cAAA,GAAAG,CAAA;MAE5B,IAAIyB,aAAa,CAACJ,MAAM,IAAI,CAAC,EAAE;QAAAxB,cAAA,GAAAe,CAAA;QAC7B,IAAMoF,gBAAgB,IAAAnG,cAAA,GAAAG,CAAA,QAAGyB,aAAa,CAACP,MAAM,CAAC,UAACC,GAAG,EAAE8E,KAAK,EACvD;UAAApG,cAAA,GAAAC,CAAA;UAAAD,cAAA,GAAAG,CAAA;UAAA,OAAAmB,GAAG,GAAI8E,KAAK,CAACvC,SAAS,GAAGuC,KAAK,CAACtC,WAAY;QAAD,CAAC,EAAE,CAAC,CAAC,GAAGlC,aAAa,CAACJ,MAAM;QAACxB,cAAA,GAAAG,CAAA;QAEzE,IAAIgG,gBAAgB,GAAG,IAAI,EAAE;UAAAnG,cAAA,GAAAe,CAAA;UAAAf,cAAA,GAAAG,CAAA;UAC3B+F,MAAM,CAACrB,IAAI,CAAC,+CAA+C,CAAC;QAC9D,CAAC,MAAM;UAAA7E,cAAA,GAAAe,CAAA;UAAAf,cAAA,GAAAG,CAAA;UAAA,IAAIgG,gBAAgB,GAAG,IAAI,EAAE;YAAAnG,cAAA,GAAAe,CAAA;YAAAf,cAAA,GAAAG,CAAA;YAClC+F,MAAM,CAACrB,IAAI,CAAC,+DAA+D,CAAC;UAC9E,CAAC;YAAA7E,cAAA,GAAAe,CAAA;UAAA;QAAD;MACF,CAAC;QAAAf,cAAA,GAAAe,CAAA;MAAA;MAAAf,cAAA,GAAAG,CAAA;MAED,OAAO+F,MAAM;IACf;EAAC;IAAAtG,GAAA;IAAAC,KAAA,EAED,SAAQoC,uBAAuBA,CAACC,gBAAuD,EAAE;MAAAlC,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MACvF,OAAO+B,gBAAgB,CACpBmE,MAAM,CAAC,UAAAC,KAAK,EAAI;QAAAtG,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QAAA,OAAAmG,KAAK,CAACC,KAAK,KAAK,WAAW;MAAD,CAAC,CAAC,CAC5CC,GAAG,CAAC,UAAAF,KAAK,EAAK;QAAAtG,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QAAA;UACbmG,KAAK,EAAEA,KAAK,CAACA,KAAK;UAClBG,eAAe,EAAEnC,IAAI,CAACG,GAAG,CAAC,GAAG,EAAE6B,KAAK,CAACI,aAAa,GAAIJ,KAAK,CAACK,UAAU,GAAG,CAAE;QAC7E,CAAC;MAAD,CAAE,CAAC;IACP;EAAC;IAAA/G,GAAA;IAAAC,KAAA,EAED,SAAQuC,+BAA+BA,CAACN,aAAuB,EAAY;MAAA9B,cAAA,GAAAC,CAAA;MACzE,IAAMwB,eAAyB,IAAAzB,cAAA,GAAAG,CAAA,QAAG,EAAE;MAACH,cAAA,GAAAG,CAAA;MAErC,IAAI2B,aAAa,CAAC8E,IAAI,CAAC,UAAAL,KAAK,EAAI;QAAAvG,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QAAA,OAAAoG,KAAK,CAACM,QAAQ,CAAC,SAAS,CAAC;MAAD,CAAC,CAAC,EAAE;QAAA7G,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAC1DsB,eAAe,CAACoD,IAAI,CAAC,uDAAuD,CAAC;QAAC7E,cAAA,GAAAG,CAAA;QAC9EsB,eAAe,CAACoD,IAAI,CAAC,4DAA4D,CAAC;MACpF,CAAC;QAAA7E,cAAA,GAAAe,CAAA;MAAA;MAAAf,cAAA,GAAAG,CAAA;MAED,IAAI2B,aAAa,CAAC8E,IAAI,CAAC,UAAAL,KAAK,EAAI;QAAAvG,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QAAA,OAAAoG,KAAK,CAACM,QAAQ,CAAC,WAAW,CAAC;MAAD,CAAC,CAAC,EAAE;QAAA7G,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAC5DsB,eAAe,CAACoD,IAAI,CAAC,mCAAmC,CAAC;QAAC7E,cAAA,GAAAG,CAAA;QAC1DsB,eAAe,CAACoD,IAAI,CAAC,qDAAqD,CAAC;MAC7E,CAAC;QAAA7E,cAAA,GAAAe,CAAA;MAAA;MAAAf,cAAA,GAAAG,CAAA;MAED,OAAOsB,eAAe;IACxB;EAAC;IAAA7B,GAAA;IAAAC,KAAA,EAED,SAAQ6C,8BAA8BA,CAAC3C,KAAsB,EAAEwC,aAAqB,EAAU;MAAAvC,cAAA,GAAAC,CAAA;MAE5F,IAAM6G,iBAAiB,IAAA9G,cAAA,GAAAG,CAAA,QAAIJ,KAAK,CAAC8D,SAAS,GAAG9D,KAAK,CAAC+D,WAAW,GAAI,GAAG;MAGrE,IAAIiD,UAAU,IAAA/G,cAAA,GAAAG,CAAA,QAAG,CAAC;MAACH,cAAA,GAAAG,CAAA;MACnB,IAAI,CAAAH,cAAA,GAAAe,CAAA,WAAAwB,aAAa,KAAK,YAAY,MAAAvC,cAAA,GAAAe,CAAA,WAAIhB,KAAK,CAACiE,cAAc,GAAGjE,KAAK,CAAC+D,WAAW,GAAG,IAAI,GAAE;QAAA9D,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QACrF4G,UAAU,GAAG,EAAE;MACjB,CAAC;QAAA/G,cAAA,GAAAe,CAAA;MAAA;MAAAf,cAAA,GAAAG,CAAA;MAED,OAAOmE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACG,GAAG,CAAC,GAAG,EAAEqC,iBAAiB,GAAGC,UAAU,CAAC,CAAC;IAClE;EAAC;IAAAnH,GAAA;IAAAC,KAAA,EAED,SAAQ+C,yBAAyBA,CAAC7C,KAAsB,EAAEwC,aAAqB,EAAY;MAAAvC,cAAA,GAAAC,CAAA;MACzF,IAAM+G,OAAiB,IAAAhH,cAAA,GAAAG,CAAA,SAAG,EAAE;MAACH,cAAA,GAAAG,CAAA;MAE7B,IAAI,CAAAH,cAAA,GAAAe,CAAA,WAAAhB,KAAK,CAAC+E,aAAa,GAAG,CAAC,MAAA9E,cAAA,GAAAe,CAAA,WAAKhB,KAAK,CAACiF,YAAY,GAAGjF,KAAK,CAAC+E,aAAa,GAAI,GAAG,GAAE;QAAA9E,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAC/E6G,OAAO,CAACnC,IAAI,CAAC,0BAA0B,CAAC;MAC1C,CAAC;QAAA7E,cAAA,GAAAe,CAAA;MAAA;MAAAf,cAAA,GAAAG,CAAA;MAED,IAAIJ,KAAK,CAACsE,oBAAoB,GAAG,EAAE,EAAE;QAAArE,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QACnC6G,OAAO,CAACnC,IAAI,CAAC,wBAAwB,CAAC;MACxC,CAAC;QAAA7E,cAAA,GAAAe,CAAA;MAAA;MAAAf,cAAA,GAAAG,CAAA;MAED,OAAO6G,OAAO;IAChB;EAAC;IAAApH,GAAA;IAAAC,KAAA,EAED,SAAQiD,0BAA0BA,CAAC/C,KAAsB,EAAEwC,aAAqB,EAAY;MAAAvC,cAAA,GAAAC,CAAA;MAC1F,IAAMgH,WAAqB,IAAAjH,cAAA,GAAAG,CAAA,SAAG,EAAE;MAACH,cAAA,GAAAG,CAAA;MAEjC,IAAIJ,KAAK,CAACiE,cAAc,GAAGjE,KAAK,CAAC+D,WAAW,GAAG,GAAG,EAAE;QAAA9D,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAClD8G,WAAW,CAACpC,IAAI,CAAC,0CAA0C,CAAC;MAC9D,CAAC;QAAA7E,cAAA,GAAAe,CAAA;MAAA;MAAAf,cAAA,GAAAG,CAAA;MAED,IAAI,CAAAH,cAAA,GAAAe,CAAA,WAAAwB,aAAa,KAAK,WAAW,MAAAvC,cAAA,GAAAe,CAAA,WAAIhB,KAAK,CAACoE,OAAO,GAAGpE,KAAK,CAAC+D,WAAW,GAAG,GAAG,GAAE;QAAA9D,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAC5E8G,WAAW,CAACpC,IAAI,CAAC,2CAA2C,CAAC;MAC/D,CAAC;QAAA7E,cAAA,GAAAe,CAAA;MAAA;MAAAf,cAAA,GAAAG,CAAA;MAED,OAAO8G,WAAW;IACpB;EAAC;IAAArH,GAAA;IAAAC,KAAA,EAED,SAAQmD,0BAA0BA,CAACjD,KAAsB,EAAEmH,OAAe,EAAU;MAAAlH,cAAA,GAAAC,CAAA;MAElF,IAAIkH,SAAS,IAAAnH,cAAA,GAAAG,CAAA,SAAG,EAAE;MAACH,cAAA,GAAAG,CAAA;MAEnB,IAAI,CAAAH,cAAA,GAAAe,CAAA,WAAAmG,OAAO,KAAK,MAAM,MAAAlH,cAAA,GAAAe,CAAA,WAAIhB,KAAK,CAACiE,cAAc,GAAGjE,KAAK,CAAC+D,WAAW,GAAG,IAAI,GAAE;QAAA9D,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QACzEgH,SAAS,IAAI,EAAE;MACjB,CAAC;QAAAnH,cAAA,GAAAe,CAAA;MAAA;MAAAf,cAAA,GAAAG,CAAA;MAED,IAAI,CAAAH,cAAA,GAAAe,CAAA,WAAAmG,OAAO,KAAK,OAAO,MAAAlH,cAAA,GAAAe,CAAA,WAAIhB,KAAK,CAAC+E,aAAa,GAAG/E,KAAK,CAAC+D,WAAW,GAAG,GAAG,GAAE;QAAA9D,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QACxEgH,SAAS,IAAI,EAAE;MACjB,CAAC;QAAAnH,cAAA,GAAAe,CAAA;MAAA;MAAAf,cAAA,GAAAG,CAAA;MAED,OAAOmE,IAAI,CAACG,GAAG,CAAC,GAAG,EAAE0C,SAAS,CAAC;IACjC;EAAC;IAAAvH,GAAA;IAAAC,KAAA,EAED,SAAQwD,wBAAwBA,CAACH,gBAAmC,EAAU;MAAAlD,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MAC5E,IAAI+C,gBAAgB,CAAC1B,MAAM,GAAG,CAAC,EAAE;QAAAxB,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAAA,OAAO,EAAE;MAAA,CAAC;QAAAH,cAAA,GAAAe,CAAA;MAAA;MAE3C,IAAMqG,eAAe,IAAApH,cAAA,GAAAG,CAAA,SAAG+C,gBAAgB,CAAC,CAAC,CAAC,CAACW,SAAS,GAAGX,gBAAgB,CAAC,CAAC,CAAC,CAACY,WAAW;MACvF,IAAMuD,cAAc,IAAArH,cAAA,GAAAG,CAAA,SAAG+C,gBAAgB,CAACA,gBAAgB,CAAC1B,MAAM,GAAG,CAAC,CAAC,CAACqC,SAAS,GAAGX,gBAAgB,CAACA,gBAAgB,CAAC1B,MAAM,GAAG,CAAC,CAAC,CAACsC,WAAW;MAE1I,IAAMwD,cAAc,IAAAtH,cAAA,GAAAG,CAAA,SAAGkH,cAAc,GAAGD,eAAe;MAACpH,cAAA,GAAAG,CAAA;MACxD,OAAOmE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACG,GAAG,CAAC,GAAG,EAAE6C,cAAc,GAAG,EAAE,CAAC,CAAC;IACvD;EAAC;IAAA1H,GAAA;IAAAC,KAAA,EAED,SAAQ0D,sBAAsBA,CAACL,gBAAmC,EAAU;MAAAlD,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MAC1E,IAAI+C,gBAAgB,CAAC1B,MAAM,GAAG,CAAC,EAAE;QAAAxB,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAAA,OAAO,EAAE;MAAA,CAAC;QAAAH,cAAA,GAAAe,CAAA;MAAA;MAE3C,IAAMwG,iBAAiB,IAAAvH,cAAA,GAAAG,CAAA,SAAG+C,gBAAgB,CAACsD,GAAG,CAAC,UAAAgB,GAAG,EAAI;QAAAxH,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QAAA,OAAAqH,GAAG,CAACxD,cAAc,GAAGwD,GAAG,CAAC1D,WAAW;MAAD,CAAC,CAAC;MAC3F,IAAM2D,WAAW,IAAAzH,cAAA,GAAAG,CAAA,SAAGoH,iBAAiB,CAACA,iBAAiB,CAAC/F,MAAM,GAAG,CAAC,CAAC,GAAG+F,iBAAiB,CAAC,CAAC,CAAC;MAACvH,cAAA,GAAAG,CAAA;MAE3F,OAAOmE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACG,GAAG,CAAC,GAAG,EAAEgD,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;IAClE;EAAC;IAAA7H,GAAA;IAAAC,KAAA,EAED,SAAQ4D,8BAA8BA,CAACiE,SAAiB,EAAEC,OAAe,EAAY;MAAA3H,cAAA,GAAAC,CAAA;MACnF,IAAMwB,eAAyB,IAAAzB,cAAA,GAAAG,CAAA,SAAG,EAAE;MAACH,cAAA,GAAAG,CAAA;MAErC,IAAIuH,SAAS,GAAG,EAAE,EAAE;QAAA1H,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAClBsB,eAAe,CAACoD,IAAI,CAAC,kCAAkC,CAAC;QAAC7E,cAAA,GAAAG,CAAA;QACzDsB,eAAe,CAACoD,IAAI,CAAC,iDAAiD,CAAC;MACzE,CAAC;QAAA7E,cAAA,GAAAe,CAAA;MAAA;MAAAf,cAAA,GAAAG,CAAA;MAED,IAAIwH,OAAO,GAAG,EAAE,EAAE;QAAA3H,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAChBsB,eAAe,CAACoD,IAAI,CAAC,kCAAkC,CAAC;QAAC7E,cAAA,GAAAG,CAAA;QACzDsB,eAAe,CAACoD,IAAI,CAAC,+BAA+B,CAAC;MACvD,CAAC;QAAA7E,cAAA,GAAAe,CAAA;MAAA;MAAAf,cAAA,GAAAG,CAAA;MAED,OAAOsB,eAAe;IACxB;EAAC;IAAA7B,GAAA;IAAAC,KAAA,EAED,SAAQ8D,qBAAqBA,CAACR,aAAqB,EAAEG,aAAqB,EAAU;MAAAtD,cAAA,GAAAC,CAAA;MAClF,IAAM2H,YAAY,IAAA5H,cAAA,GAAAG,CAAA,SAAGgD,aAAa,GAAG,EAAE,GAAG,CAAC;MAC3C,IAAM0E,iBAAiB,IAAA7H,cAAA,GAAAG,CAAA,SAAG,CAAC,GAAImD,aAAa,GAAG,GAAI;MAACtD,cAAA,GAAAG,CAAA;MAEpD,OAAOmE,IAAI,CAACC,KAAK,CAACqD,YAAY,GAAGC,iBAAiB,CAAC;IACrD;EAAC;AAAA;AAGH,OAAO,IAAMC,2BAA2B,IAAA9H,cAAA,GAAAG,CAAA,SAAG,IAAIV,2BAA2B,CAAC,CAAC", "ignoreList": []}