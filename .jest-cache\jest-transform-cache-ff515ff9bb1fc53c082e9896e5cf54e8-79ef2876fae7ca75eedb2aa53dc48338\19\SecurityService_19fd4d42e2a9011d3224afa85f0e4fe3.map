{"version": 3, "names": ["CryptoJS", "AsyncStorage", "handleError", "logError", "env", "SecurityService", "_classCallCheck", "rateLimitStore", "cov_18lsccydd9", "s", "Map", "loginAttempts", "securityEvents", "f", "config", "<PERSON><PERSON><PERSON>", "b", "get", "generateEncryptionKey", "rateLimitWindow", "rateLimitMaxRequests", "sessionTimeout", "maxLogin<PERSON><PERSON><PERSON>s", "lockoutDuration", "initializeSecurityMonitoring", "_createClass", "key", "value", "_this", "setInterval", "cleanupRateLimitStore", "cleanupSecurityEvents", "encrypt", "data", "customKey", "salt", "lib", "WordArray", "random", "iv", "<PERSON><PERSON><PERSON>", "PBKDF2", "keySize", "iterations", "encrypted", "AES", "mode", "CBC", "padding", "pad", "Pkcs7", "toString", "error", "context", "Error", "decrypt", "encryptedData", "enc", "Hex", "parse", "decrypted", "Utf8", "hashPassword", "password", "passwordSalt", "hash", "verifyPassword", "_ref", "computedHash", "checkRateLimit", "identifier", "maxRequests", "windowMinutes", "max", "window", "now", "Date", "entry", "set", "count", "firstRequest", "lastRequest", "allowed", "remaining", "resetTime", "logSecurityEvent", "type", "details", "timestamp", "severity", "trackLoginAttempt", "success", "attempts", "delete", "attemptsRemaining", "lastAttempt", "attempt", "isLocked", "locked", "Math", "lockoutTime", "undefined", "sanitizeInput", "input", "replace", "trim", "sanitizeSQLInput", "generateSecureToken", "length", "arguments", "chars", "result", "i", "char<PERSON>t", "floor", "_secureStore", "_asyncToGenerator", "setItem", "JSON", "stringify", "secureStore", "_x", "_x2", "apply", "_secureRetrieve", "stored", "getItem", "secureRetrieve", "_x3", "event", "securityEvent", "Object", "assign", "push", "console", "getEnvironment", "sendToSecurityMonitoring", "getSecurityEvents", "limit", "events", "filter", "sort", "a", "slice", "_ref2", "entries", "_ref3", "_slicedToArray", "maxAge", "_sendToSecurityMonitoring", "log", "_x4", "validateSessionToken", "token", "base64Regex", "test", "detectSuspiciousActivity", "userId", "activity", "recentEvents", "failed<PERSON>ogins", "reason", "rapidRequests", "getSecurityStatus", "criticalEvents", "rateLimitEntries", "size", "securityService"], "sources": ["SecurityService.ts"], "sourcesContent": ["/**\n * Security Service\n * \n * Comprehensive security features including encryption, rate limiting,\n * input validation, and security monitoring\n */\n\nimport CryptoJS from 'crypto-js';\nimport { Platform } from 'react-native';\nimport AsyncStorage from '@react-native-async-storage/async-storage';\nimport { handleError, logError } from '@/utils/errorHandling';\nimport env from '@/config/environment';\n\nexport interface SecurityConfig {\n  encryptionKey: string;\n  rateLimitWindow: number; // minutes\n  rateLimitMaxRequests: number;\n  sessionTimeout: number; // minutes\n  maxLoginAttempts: number;\n  lockoutDuration: number; // minutes\n}\n\nexport interface RateLimitEntry {\n  count: number;\n  firstRequest: number;\n  lastRequest: number;\n}\n\nexport interface SecurityEvent {\n  type: 'login_attempt' | 'failed_login' | 'rate_limit_exceeded' | 'suspicious_activity' | 'data_access' | 'permission_denied';\n  userId?: string;\n  ipAddress?: string;\n  userAgent?: string;\n  details: any;\n  timestamp: number;\n  severity: 'low' | 'medium' | 'high' | 'critical';\n}\n\nexport interface EncryptedData {\n  data: string;\n  iv: string;\n  salt: string;\n}\n\nclass SecurityService {\n  private config: SecurityConfig;\n  private rateLimitStore: Map<string, RateLimitEntry> = new Map();\n  private loginAttempts: Map<string, { count: number; lastAttempt: number }> = new Map();\n  private securityEvents: SecurityEvent[] = [];\n  private encryptionKey: string;\n\n  constructor() {\n    this.config = {\n      encryptionKey: env.get('ENCRYPTION_KEY') || this.generateEncryptionKey(),\n      rateLimitWindow: 15, // 15 minutes\n      rateLimitMaxRequests: 100,\n      sessionTimeout: 60, // 1 hour\n      maxLoginAttempts: 5,\n      lockoutDuration: 30, // 30 minutes\n    };\n    \n    this.encryptionKey = this.config.encryptionKey;\n    this.initializeSecurityMonitoring();\n  }\n\n  /**\n   * Initialize security monitoring\n   */\n  private initializeSecurityMonitoring(): void {\n    // Clean up old rate limit entries every 5 minutes\n    setInterval(() => {\n      this.cleanupRateLimitStore();\n    }, 5 * 60 * 1000);\n\n    // Clean up old security events every hour\n    setInterval(() => {\n      this.cleanupSecurityEvents();\n    }, 60 * 60 * 1000);\n  }\n\n  /**\n   * Encrypt sensitive data\n   */\n  encrypt(data: string, customKey?: string): EncryptedData {\n    try {\n      const key = customKey || this.encryptionKey;\n      const salt = CryptoJS.lib.WordArray.random(256/8);\n      const iv = CryptoJS.lib.WordArray.random(128/8);\n      \n      const derivedKey = CryptoJS.PBKDF2(key, salt, {\n        keySize: 256/32,\n        iterations: 10000\n      });\n\n      const encrypted = CryptoJS.AES.encrypt(data, derivedKey, {\n        iv: iv,\n        mode: CryptoJS.mode.CBC,\n        padding: CryptoJS.pad.Pkcs7\n      });\n\n      return {\n        data: encrypted.toString(),\n        iv: iv.toString(),\n        salt: salt.toString(),\n      };\n    } catch (error) {\n      logError(handleError(error), { context: 'encrypt' });\n      throw new Error('Encryption failed');\n    }\n  }\n\n  /**\n   * Decrypt sensitive data\n   */\n  decrypt(encryptedData: EncryptedData, customKey?: string): string {\n    try {\n      const key = customKey || this.encryptionKey;\n      const salt = CryptoJS.enc.Hex.parse(encryptedData.salt);\n      const iv = CryptoJS.enc.Hex.parse(encryptedData.iv);\n      \n      const derivedKey = CryptoJS.PBKDF2(key, salt, {\n        keySize: 256/32,\n        iterations: 10000\n      });\n\n      const decrypted = CryptoJS.AES.decrypt(encryptedData.data, derivedKey, {\n        iv: iv,\n        mode: CryptoJS.mode.CBC,\n        padding: CryptoJS.pad.Pkcs7\n      });\n\n      return decrypted.toString(CryptoJS.enc.Utf8);\n    } catch (error) {\n      logError(handleError(error), { context: 'decrypt' });\n      throw new Error('Decryption failed');\n    }\n  }\n\n  /**\n   * Hash password with salt\n   */\n  hashPassword(password: string, salt?: string): { hash: string; salt: string } {\n    const passwordSalt = salt || CryptoJS.lib.WordArray.random(256/8).toString();\n    const hash = CryptoJS.PBKDF2(password, passwordSalt, {\n      keySize: 256/32,\n      iterations: 10000\n    }).toString();\n\n    return { hash, salt: passwordSalt };\n  }\n\n  /**\n   * Verify password against hash\n   */\n  verifyPassword(password: string, hash: string, salt: string): boolean {\n    const { hash: computedHash } = this.hashPassword(password, salt);\n    return computedHash === hash;\n  }\n\n  /**\n   * Check rate limiting\n   */\n  checkRateLimit(identifier: string, maxRequests?: number, windowMinutes?: number): {\n    allowed: boolean;\n    remaining: number;\n    resetTime: number;\n  } {\n    const max = maxRequests || this.config.rateLimitMaxRequests;\n    const window = (windowMinutes || this.config.rateLimitWindow) * 60 * 1000;\n    const now = Date.now();\n\n    const entry = this.rateLimitStore.get(identifier);\n    \n    if (!entry) {\n      // First request\n      this.rateLimitStore.set(identifier, {\n        count: 1,\n        firstRequest: now,\n        lastRequest: now,\n      });\n      \n      return {\n        allowed: true,\n        remaining: max - 1,\n        resetTime: now + window,\n      };\n    }\n\n    // Check if window has expired\n    if (now - entry.firstRequest > window) {\n      // Reset window\n      this.rateLimitStore.set(identifier, {\n        count: 1,\n        firstRequest: now,\n        lastRequest: now,\n      });\n      \n      return {\n        allowed: true,\n        remaining: max - 1,\n        resetTime: now + window,\n      };\n    }\n\n    // Check if limit exceeded\n    if (entry.count >= max) {\n      this.logSecurityEvent({\n        type: 'rate_limit_exceeded',\n        details: { identifier, count: entry.count, max },\n        timestamp: now,\n        severity: 'medium',\n      });\n\n      return {\n        allowed: false,\n        remaining: 0,\n        resetTime: entry.firstRequest + window,\n      };\n    }\n\n    // Increment count\n    entry.count++;\n    entry.lastRequest = now;\n    this.rateLimitStore.set(identifier, entry);\n\n    return {\n      allowed: true,\n      remaining: max - entry.count,\n      resetTime: entry.firstRequest + window,\n    };\n  }\n\n  /**\n   * Track login attempts\n   */\n  trackLoginAttempt(identifier: string, success: boolean): {\n    allowed: boolean;\n    attemptsRemaining: number;\n    lockoutTime?: number;\n  } {\n    const now = Date.now();\n    const lockoutDuration = this.config.lockoutDuration * 60 * 1000;\n    \n    const attempts = this.loginAttempts.get(identifier);\n\n    if (success) {\n      // Clear attempts on successful login\n      this.loginAttempts.delete(identifier);\n      \n      this.logSecurityEvent({\n        type: 'login_attempt',\n        details: { identifier, success: true },\n        timestamp: now,\n        severity: 'low',\n      });\n\n      return {\n        allowed: true,\n        attemptsRemaining: this.config.maxLoginAttempts,\n      };\n    }\n\n    if (!attempts) {\n      // First failed attempt\n      this.loginAttempts.set(identifier, {\n        count: 1,\n        lastAttempt: now,\n      });\n\n      this.logSecurityEvent({\n        type: 'failed_login',\n        details: { identifier, attempt: 1 },\n        timestamp: now,\n        severity: 'low',\n      });\n\n      return {\n        allowed: true,\n        attemptsRemaining: this.config.maxLoginAttempts - 1,\n      };\n    }\n\n    // Check if lockout period has expired\n    if (now - attempts.lastAttempt > lockoutDuration) {\n      // Reset attempts\n      this.loginAttempts.set(identifier, {\n        count: 1,\n        lastAttempt: now,\n      });\n\n      return {\n        allowed: true,\n        attemptsRemaining: this.config.maxLoginAttempts - 1,\n      };\n    }\n\n    // Increment failed attempts\n    attempts.count++;\n    attempts.lastAttempt = now;\n    this.loginAttempts.set(identifier, attempts);\n\n    const isLocked = attempts.count >= this.config.maxLoginAttempts;\n    \n    this.logSecurityEvent({\n      type: 'failed_login',\n      details: { identifier, attempt: attempts.count, locked: isLocked },\n      timestamp: now,\n      severity: isLocked ? 'high' : 'medium',\n    });\n\n    return {\n      allowed: !isLocked,\n      attemptsRemaining: Math.max(0, this.config.maxLoginAttempts - attempts.count),\n      lockoutTime: isLocked ? attempts.lastAttempt + lockoutDuration : undefined,\n    };\n  }\n\n  /**\n   * Sanitize input to prevent XSS and injection attacks\n   */\n  sanitizeInput(input: string): string {\n    if (typeof input !== 'string') {\n      return '';\n    }\n\n    return input\n      .replace(/[<>]/g, '') // Remove angle brackets\n      .replace(/javascript:/gi, '') // Remove javascript: protocol\n      .replace(/on\\w+=/gi, '') // Remove event handlers\n      .replace(/script/gi, '') // Remove script tags\n      .trim();\n  }\n\n  /**\n   * Validate and sanitize SQL input\n   */\n  sanitizeSQLInput(input: string): string {\n    if (typeof input !== 'string') {\n      return '';\n    }\n\n    return input\n      .replace(/['\";\\\\]/g, '') // Remove SQL injection characters\n      .replace(/--/g, '') // Remove SQL comments\n      .replace(/\\/\\*/g, '') // Remove SQL block comments\n      .replace(/\\*\\//g, '')\n      .trim();\n  }\n\n  /**\n   * Generate secure random token\n   */\n  generateSecureToken(length: number = 32): string {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    let result = '';\n    \n    for (let i = 0; i < length; i++) {\n      result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    \n    return result;\n  }\n\n  /**\n   * Generate encryption key\n   */\n  private generateEncryptionKey(): string {\n    return CryptoJS.lib.WordArray.random(256/8).toString();\n  }\n\n  /**\n   * Securely store sensitive data\n   */\n  async secureStore(key: string, data: string): Promise<void> {\n    try {\n      const encrypted = this.encrypt(data);\n      await AsyncStorage.setItem(key, JSON.stringify(encrypted));\n    } catch (error) {\n      logError(handleError(error), { context: 'secureStore', key });\n      throw new Error('Failed to securely store data');\n    }\n  }\n\n  /**\n   * Securely retrieve sensitive data\n   */\n  async secureRetrieve(key: string): Promise<string | null> {\n    try {\n      const stored = await AsyncStorage.getItem(key);\n      if (!stored) {\n        return null;\n      }\n\n      const encrypted: EncryptedData = JSON.parse(stored);\n      return this.decrypt(encrypted);\n    } catch (error) {\n      logError(handleError(error), { context: 'secureRetrieve', key });\n      return null;\n    }\n  }\n\n  /**\n   * Log security event\n   */\n  logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'> & { timestamp?: number }): void {\n    const securityEvent: SecurityEvent = {\n      ...event,\n      timestamp: event.timestamp || Date.now(),\n    };\n\n    this.securityEvents.push(securityEvent);\n\n    // Log critical events immediately\n    if (event.severity === 'critical') {\n      console.error('CRITICAL SECURITY EVENT:', securityEvent);\n      \n      // In production, send to security monitoring service\n      if (env.getEnvironment() === 'production') {\n        this.sendToSecurityMonitoring(securityEvent);\n      }\n    }\n  }\n\n  /**\n   * Get security events\n   */\n  getSecurityEvents(severity?: SecurityEvent['severity'], limit: number = 100): SecurityEvent[] {\n    let events = this.securityEvents;\n    \n    if (severity) {\n      events = events.filter(event => event.severity === severity);\n    }\n\n    return events\n      .sort((a, b) => b.timestamp - a.timestamp)\n      .slice(0, limit);\n  }\n\n  /**\n   * Clean up old rate limit entries\n   */\n  private cleanupRateLimitStore(): void {\n    const now = Date.now();\n    const window = this.config.rateLimitWindow * 60 * 1000;\n\n    for (const [key, entry] of this.rateLimitStore.entries()) {\n      if (now - entry.firstRequest > window) {\n        this.rateLimitStore.delete(key);\n      }\n    }\n  }\n\n  /**\n   * Clean up old security events\n   */\n  private cleanupSecurityEvents(): void {\n    const now = Date.now();\n    const maxAge = 24 * 60 * 60 * 1000; // 24 hours\n\n    this.securityEvents = this.securityEvents.filter(\n      event => now - event.timestamp < maxAge\n    );\n  }\n\n  /**\n   * Send to security monitoring service\n   */\n  private async sendToSecurityMonitoring(event: SecurityEvent): Promise<void> {\n    try {\n      // In a real implementation, this would send to a security monitoring service\n      // like Sentry, DataDog, or a custom security service\n      console.log('Sending to security monitoring:', event);\n    } catch (error) {\n      console.error('Failed to send security event:', error);\n    }\n  }\n\n  /**\n   * Validate session token\n   */\n  validateSessionToken(token: string): boolean {\n    try {\n      // Basic token validation\n      if (!token || token.length < 32) {\n        return false;\n      }\n\n      // Check token format (base64 encoded)\n      const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;\n      return base64Regex.test(token);\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Check for suspicious activity patterns\n   */\n  detectSuspiciousActivity(userId: string, activity: {\n    type: string;\n    timestamp: number;\n    metadata?: any;\n  }): boolean {\n    // Simple suspicious activity detection\n    const recentEvents = this.securityEvents.filter(\n      event => event.userId === userId && \n               event.timestamp > Date.now() - 60 * 60 * 1000 // Last hour\n    );\n\n    // Too many failed logins\n    const failedLogins = recentEvents.filter(event => event.type === 'failed_login');\n    if (failedLogins.length > 10) {\n      this.logSecurityEvent({\n        type: 'suspicious_activity',\n        userId,\n        details: { reason: 'excessive_failed_logins', count: failedLogins.length },\n        severity: 'high',\n      });\n      return true;\n    }\n\n    // Rapid successive requests\n    const rapidRequests = recentEvents.filter(\n      event => event.timestamp > Date.now() - 5 * 60 * 1000 // Last 5 minutes\n    );\n    if (rapidRequests.length > 50) {\n      this.logSecurityEvent({\n        type: 'suspicious_activity',\n        userId,\n        details: { reason: 'rapid_requests', count: rapidRequests.length },\n        severity: 'medium',\n      });\n      return true;\n    }\n\n    return false;\n  }\n\n  /**\n   * Get security status\n   */\n  getSecurityStatus(): {\n    rateLimitEntries: number;\n    loginAttempts: number;\n    securityEvents: number;\n    criticalEvents: number;\n  } {\n    const criticalEvents = this.securityEvents.filter(\n      event => event.severity === 'critical'\n    ).length;\n\n    return {\n      rateLimitEntries: this.rateLimitStore.size,\n      loginAttempts: this.loginAttempts.size,\n      securityEvents: this.securityEvents.length,\n      criticalEvents,\n    };\n  }\n}\n\n// Export singleton instance\nexport const securityService = new SecurityService();\nexport default securityService;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,OAAOA,QAAQ,MAAM,WAAW;AAEhC,OAAOC,YAAY,MAAM,2CAA2C;AACpE,SAASC,WAAW,EAAEC,QAAQ;AAC9B,OAAOC,GAAG;AAA6B,IAiCjCC,eAAe;EAOnB,SAAAA,gBAAA,EAAc;IAAAC,eAAA,OAAAD,eAAA;IAAA,KALNE,cAAc,IAAAC,cAAA,GAAAC,CAAA,OAAgC,IAAIC,GAAG,CAAC,CAAC;IAAA,KACvDC,aAAa,IAAAH,cAAA,GAAAC,CAAA,OAAwD,IAAIC,GAAG,CAAC,CAAC;IAAA,KAC9EE,cAAc,IAAAJ,cAAA,GAAAC,CAAA,OAAoB,EAAE;IAAAD,cAAA,GAAAK,CAAA;IAAAL,cAAA,GAAAC,CAAA;IAI1C,IAAI,CAACK,MAAM,GAAG;MACZC,aAAa,EAAE,CAAAP,cAAA,GAAAQ,CAAA,UAAAZ,GAAG,CAACa,GAAG,CAAC,gBAAgB,CAAC,MAAAT,cAAA,GAAAQ,CAAA,UAAI,IAAI,CAACE,qBAAqB,CAAC,CAAC;MACxEC,eAAe,EAAE,EAAE;MACnBC,oBAAoB,EAAE,GAAG;MACzBC,cAAc,EAAE,EAAE;MAClBC,gBAAgB,EAAE,CAAC;MACnBC,eAAe,EAAE;IACnB,CAAC;IAACf,cAAA,GAAAC,CAAA;IAEF,IAAI,CAACM,aAAa,GAAG,IAAI,CAACD,MAAM,CAACC,aAAa;IAACP,cAAA,GAAAC,CAAA;IAC/C,IAAI,CAACe,4BAA4B,CAAC,CAAC;EACrC;EAAC,OAAAC,YAAA,CAAApB,eAAA;IAAAqB,GAAA;IAAAC,KAAA,EAKD,SAAQH,4BAA4BA,CAAA,EAAS;MAAA,IAAAI,KAAA;MAAApB,cAAA,GAAAK,CAAA;MAAAL,cAAA,GAAAC,CAAA;MAE3CoB,WAAW,CAAC,YAAM;QAAArB,cAAA,GAAAK,CAAA;QAAAL,cAAA,GAAAC,CAAA;QAChBmB,KAAI,CAACE,qBAAqB,CAAC,CAAC;MAC9B,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;MAACtB,cAAA,GAAAC,CAAA;MAGlBoB,WAAW,CAAC,YAAM;QAAArB,cAAA,GAAAK,CAAA;QAAAL,cAAA,GAAAC,CAAA;QAChBmB,KAAI,CAACG,qBAAqB,CAAC,CAAC;MAC9B,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpB;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAKD,SAAAK,OAAOA,CAACC,IAAY,EAAEC,SAAkB,EAAiB;MAAA1B,cAAA,GAAAK,CAAA;MAAAL,cAAA,GAAAC,CAAA;MACvD,IAAI;QACF,IAAMiB,GAAG,IAAAlB,cAAA,GAAAC,CAAA,QAAG,CAAAD,cAAA,GAAAQ,CAAA,UAAAkB,SAAS,MAAA1B,cAAA,GAAAQ,CAAA,UAAI,IAAI,CAACD,aAAa;QAC3C,IAAMoB,IAAI,IAAA3B,cAAA,GAAAC,CAAA,QAAGT,QAAQ,CAACoC,GAAG,CAACC,SAAS,CAACC,MAAM,CAAC,GAAG,GAAC,CAAC,CAAC;QACjD,IAAMC,EAAE,IAAA/B,cAAA,GAAAC,CAAA,QAAGT,QAAQ,CAACoC,GAAG,CAACC,SAAS,CAACC,MAAM,CAAC,GAAG,GAAC,CAAC,CAAC;QAE/C,IAAME,UAAU,IAAAhC,cAAA,GAAAC,CAAA,QAAGT,QAAQ,CAACyC,MAAM,CAACf,GAAG,EAAES,IAAI,EAAE;UAC5CO,OAAO,EAAE,GAAG,GAAC,EAAE;UACfC,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,IAAMC,SAAS,IAAApC,cAAA,GAAAC,CAAA,QAAGT,QAAQ,CAAC6C,GAAG,CAACb,OAAO,CAACC,IAAI,EAAEO,UAAU,EAAE;UACvDD,EAAE,EAAEA,EAAE;UACNO,IAAI,EAAE9C,QAAQ,CAAC8C,IAAI,CAACC,GAAG;UACvBC,OAAO,EAAEhD,QAAQ,CAACiD,GAAG,CAACC;QACxB,CAAC,CAAC;QAAC1C,cAAA,GAAAC,CAAA;QAEH,OAAO;UACLwB,IAAI,EAAEW,SAAS,CAACO,QAAQ,CAAC,CAAC;UAC1BZ,EAAE,EAAEA,EAAE,CAACY,QAAQ,CAAC,CAAC;UACjBhB,IAAI,EAAEA,IAAI,CAACgB,QAAQ,CAAC;QACtB,CAAC;MACH,CAAC,CAAC,OAAOC,KAAK,EAAE;QAAA5C,cAAA,GAAAC,CAAA;QACdN,QAAQ,CAACD,WAAW,CAACkD,KAAK,CAAC,EAAE;UAAEC,OAAO,EAAE;QAAU,CAAC,CAAC;QAAC7C,cAAA,GAAAC,CAAA;QACrD,MAAM,IAAI6C,KAAK,CAAC,mBAAmB,CAAC;MACtC;IACF;EAAC;IAAA5B,GAAA;IAAAC,KAAA,EAKD,SAAA4B,OAAOA,CAACC,aAA4B,EAAEtB,SAAkB,EAAU;MAAA1B,cAAA,GAAAK,CAAA;MAAAL,cAAA,GAAAC,CAAA;MAChE,IAAI;QACF,IAAMiB,GAAG,IAAAlB,cAAA,GAAAC,CAAA,QAAG,CAAAD,cAAA,GAAAQ,CAAA,UAAAkB,SAAS,MAAA1B,cAAA,GAAAQ,CAAA,UAAI,IAAI,CAACD,aAAa;QAC3C,IAAMoB,IAAI,IAAA3B,cAAA,GAAAC,CAAA,QAAGT,QAAQ,CAACyD,GAAG,CAACC,GAAG,CAACC,KAAK,CAACH,aAAa,CAACrB,IAAI,CAAC;QACvD,IAAMI,EAAE,IAAA/B,cAAA,GAAAC,CAAA,QAAGT,QAAQ,CAACyD,GAAG,CAACC,GAAG,CAACC,KAAK,CAACH,aAAa,CAACjB,EAAE,CAAC;QAEnD,IAAMC,UAAU,IAAAhC,cAAA,GAAAC,CAAA,QAAGT,QAAQ,CAACyC,MAAM,CAACf,GAAG,EAAES,IAAI,EAAE;UAC5CO,OAAO,EAAE,GAAG,GAAC,EAAE;UACfC,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,IAAMiB,SAAS,IAAApD,cAAA,GAAAC,CAAA,QAAGT,QAAQ,CAAC6C,GAAG,CAACU,OAAO,CAACC,aAAa,CAACvB,IAAI,EAAEO,UAAU,EAAE;UACrED,EAAE,EAAEA,EAAE;UACNO,IAAI,EAAE9C,QAAQ,CAAC8C,IAAI,CAACC,GAAG;UACvBC,OAAO,EAAEhD,QAAQ,CAACiD,GAAG,CAACC;QACxB,CAAC,CAAC;QAAC1C,cAAA,GAAAC,CAAA;QAEH,OAAOmD,SAAS,CAACT,QAAQ,CAACnD,QAAQ,CAACyD,GAAG,CAACI,IAAI,CAAC;MAC9C,CAAC,CAAC,OAAOT,KAAK,EAAE;QAAA5C,cAAA,GAAAC,CAAA;QACdN,QAAQ,CAACD,WAAW,CAACkD,KAAK,CAAC,EAAE;UAAEC,OAAO,EAAE;QAAU,CAAC,CAAC;QAAC7C,cAAA,GAAAC,CAAA;QACrD,MAAM,IAAI6C,KAAK,CAAC,mBAAmB,CAAC;MACtC;IACF;EAAC;IAAA5B,GAAA;IAAAC,KAAA,EAKD,SAAAmC,YAAYA,CAACC,QAAgB,EAAE5B,IAAa,EAAkC;MAAA3B,cAAA,GAAAK,CAAA;MAC5E,IAAMmD,YAAY,IAAAxD,cAAA,GAAAC,CAAA,QAAG,CAAAD,cAAA,GAAAQ,CAAA,UAAAmB,IAAI,MAAA3B,cAAA,GAAAQ,CAAA,UAAIhB,QAAQ,CAACoC,GAAG,CAACC,SAAS,CAACC,MAAM,CAAC,GAAG,GAAC,CAAC,CAAC,CAACa,QAAQ,CAAC,CAAC;MAC5E,IAAMc,IAAI,IAAAzD,cAAA,GAAAC,CAAA,QAAGT,QAAQ,CAACyC,MAAM,CAACsB,QAAQ,EAAEC,YAAY,EAAE;QACnDtB,OAAO,EAAE,GAAG,GAAC,EAAE;QACfC,UAAU,EAAE;MACd,CAAC,CAAC,CAACQ,QAAQ,CAAC,CAAC;MAAC3C,cAAA,GAAAC,CAAA;MAEd,OAAO;QAAEwD,IAAI,EAAJA,IAAI;QAAE9B,IAAI,EAAE6B;MAAa,CAAC;IACrC;EAAC;IAAAtC,GAAA;IAAAC,KAAA,EAKD,SAAAuC,cAAcA,CAACH,QAAgB,EAAEE,IAAY,EAAE9B,IAAY,EAAW;MAAA3B,cAAA,GAAAK,CAAA;MACpE,IAAAsD,IAAA,IAAA3D,cAAA,GAAAC,CAAA,QAA+B,IAAI,CAACqD,YAAY,CAACC,QAAQ,EAAE5B,IAAI,CAAC;QAAlDiC,YAAY,GAAAD,IAAA,CAAlBF,IAAI;MAAqDzD,cAAA,GAAAC,CAAA;MACjE,OAAO2D,YAAY,KAAKH,IAAI;IAC9B;EAAC;IAAAvC,GAAA;IAAAC,KAAA,EAKD,SAAA0C,cAAcA,CAACC,UAAkB,EAAEC,WAAoB,EAAEC,aAAsB,EAI7E;MAAAhE,cAAA,GAAAK,CAAA;MACA,IAAM4D,GAAG,IAAAjE,cAAA,GAAAC,CAAA,QAAG,CAAAD,cAAA,GAAAQ,CAAA,UAAAuD,WAAW,MAAA/D,cAAA,GAAAQ,CAAA,UAAI,IAAI,CAACF,MAAM,CAACM,oBAAoB;MAC3D,IAAMsD,MAAM,IAAAlE,cAAA,GAAAC,CAAA,QAAG,CAAC,CAAAD,cAAA,GAAAQ,CAAA,UAAAwD,aAAa,MAAAhE,cAAA,GAAAQ,CAAA,UAAI,IAAI,CAACF,MAAM,CAACK,eAAe,KAAI,EAAE,GAAG,IAAI;MACzE,IAAMwD,GAAG,IAAAnE,cAAA,GAAAC,CAAA,QAAGmE,IAAI,CAACD,GAAG,CAAC,CAAC;MAEtB,IAAME,KAAK,IAAArE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,cAAc,CAACU,GAAG,CAACqD,UAAU,CAAC;MAAC9D,cAAA,GAAAC,CAAA;MAElD,IAAI,CAACoE,KAAK,EAAE;QAAArE,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAEV,IAAI,CAACF,cAAc,CAACuE,GAAG,CAACR,UAAU,EAAE;UAClCS,KAAK,EAAE,CAAC;UACRC,YAAY,EAAEL,GAAG;UACjBM,WAAW,EAAEN;QACf,CAAC,CAAC;QAACnE,cAAA,GAAAC,CAAA;QAEH,OAAO;UACLyE,OAAO,EAAE,IAAI;UACbC,SAAS,EAAEV,GAAG,GAAG,CAAC;UAClBW,SAAS,EAAET,GAAG,GAAGD;QACnB,CAAC;MACH,CAAC;QAAAlE,cAAA,GAAAQ,CAAA;MAAA;MAAAR,cAAA,GAAAC,CAAA;MAGD,IAAIkE,GAAG,GAAGE,KAAK,CAACG,YAAY,GAAGN,MAAM,EAAE;QAAAlE,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAErC,IAAI,CAACF,cAAc,CAACuE,GAAG,CAACR,UAAU,EAAE;UAClCS,KAAK,EAAE,CAAC;UACRC,YAAY,EAAEL,GAAG;UACjBM,WAAW,EAAEN;QACf,CAAC,CAAC;QAACnE,cAAA,GAAAC,CAAA;QAEH,OAAO;UACLyE,OAAO,EAAE,IAAI;UACbC,SAAS,EAAEV,GAAG,GAAG,CAAC;UAClBW,SAAS,EAAET,GAAG,GAAGD;QACnB,CAAC;MACH,CAAC;QAAAlE,cAAA,GAAAQ,CAAA;MAAA;MAAAR,cAAA,GAAAC,CAAA;MAGD,IAAIoE,KAAK,CAACE,KAAK,IAAIN,GAAG,EAAE;QAAAjE,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QACtB,IAAI,CAAC4E,gBAAgB,CAAC;UACpBC,IAAI,EAAE,qBAAqB;UAC3BC,OAAO,EAAE;YAAEjB,UAAU,EAAVA,UAAU;YAAES,KAAK,EAAEF,KAAK,CAACE,KAAK;YAAEN,GAAG,EAAHA;UAAI,CAAC;UAChDe,SAAS,EAAEb,GAAG;UACdc,QAAQ,EAAE;QACZ,CAAC,CAAC;QAACjF,cAAA,GAAAC,CAAA;QAEH,OAAO;UACLyE,OAAO,EAAE,KAAK;UACdC,SAAS,EAAE,CAAC;UACZC,SAAS,EAAEP,KAAK,CAACG,YAAY,GAAGN;QAClC,CAAC;MACH,CAAC;QAAAlE,cAAA,GAAAQ,CAAA;MAAA;MAAAR,cAAA,GAAAC,CAAA;MAGDoE,KAAK,CAACE,KAAK,EAAE;MAACvE,cAAA,GAAAC,CAAA;MACdoE,KAAK,CAACI,WAAW,GAAGN,GAAG;MAACnE,cAAA,GAAAC,CAAA;MACxB,IAAI,CAACF,cAAc,CAACuE,GAAG,CAACR,UAAU,EAAEO,KAAK,CAAC;MAACrE,cAAA,GAAAC,CAAA;MAE3C,OAAO;QACLyE,OAAO,EAAE,IAAI;QACbC,SAAS,EAAEV,GAAG,GAAGI,KAAK,CAACE,KAAK;QAC5BK,SAAS,EAAEP,KAAK,CAACG,YAAY,GAAGN;MAClC,CAAC;IACH;EAAC;IAAAhD,GAAA;IAAAC,KAAA,EAKD,SAAA+D,iBAAiBA,CAACpB,UAAkB,EAAEqB,OAAgB,EAIpD;MAAAnF,cAAA,GAAAK,CAAA;MACA,IAAM8D,GAAG,IAAAnE,cAAA,GAAAC,CAAA,QAAGmE,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,IAAMpD,eAAe,IAAAf,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACK,MAAM,CAACS,eAAe,GAAG,EAAE,GAAG,IAAI;MAE/D,IAAMqE,QAAQ,IAAApF,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACE,aAAa,CAACM,GAAG,CAACqD,UAAU,CAAC;MAAC9D,cAAA,GAAAC,CAAA;MAEpD,IAAIkF,OAAO,EAAE;QAAAnF,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAEX,IAAI,CAACE,aAAa,CAACkF,MAAM,CAACvB,UAAU,CAAC;QAAC9D,cAAA,GAAAC,CAAA;QAEtC,IAAI,CAAC4E,gBAAgB,CAAC;UACpBC,IAAI,EAAE,eAAe;UACrBC,OAAO,EAAE;YAAEjB,UAAU,EAAVA,UAAU;YAAEqB,OAAO,EAAE;UAAK,CAAC;UACtCH,SAAS,EAAEb,GAAG;UACdc,QAAQ,EAAE;QACZ,CAAC,CAAC;QAACjF,cAAA,GAAAC,CAAA;QAEH,OAAO;UACLyE,OAAO,EAAE,IAAI;UACbY,iBAAiB,EAAE,IAAI,CAAChF,MAAM,CAACQ;QACjC,CAAC;MACH,CAAC;QAAAd,cAAA,GAAAQ,CAAA;MAAA;MAAAR,cAAA,GAAAC,CAAA;MAED,IAAI,CAACmF,QAAQ,EAAE;QAAApF,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAEb,IAAI,CAACE,aAAa,CAACmE,GAAG,CAACR,UAAU,EAAE;UACjCS,KAAK,EAAE,CAAC;UACRgB,WAAW,EAAEpB;QACf,CAAC,CAAC;QAACnE,cAAA,GAAAC,CAAA;QAEH,IAAI,CAAC4E,gBAAgB,CAAC;UACpBC,IAAI,EAAE,cAAc;UACpBC,OAAO,EAAE;YAAEjB,UAAU,EAAVA,UAAU;YAAE0B,OAAO,EAAE;UAAE,CAAC;UACnCR,SAAS,EAAEb,GAAG;UACdc,QAAQ,EAAE;QACZ,CAAC,CAAC;QAACjF,cAAA,GAAAC,CAAA;QAEH,OAAO;UACLyE,OAAO,EAAE,IAAI;UACbY,iBAAiB,EAAE,IAAI,CAAChF,MAAM,CAACQ,gBAAgB,GAAG;QACpD,CAAC;MACH,CAAC;QAAAd,cAAA,GAAAQ,CAAA;MAAA;MAAAR,cAAA,GAAAC,CAAA;MAGD,IAAIkE,GAAG,GAAGiB,QAAQ,CAACG,WAAW,GAAGxE,eAAe,EAAE;QAAAf,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAEhD,IAAI,CAACE,aAAa,CAACmE,GAAG,CAACR,UAAU,EAAE;UACjCS,KAAK,EAAE,CAAC;UACRgB,WAAW,EAAEpB;QACf,CAAC,CAAC;QAACnE,cAAA,GAAAC,CAAA;QAEH,OAAO;UACLyE,OAAO,EAAE,IAAI;UACbY,iBAAiB,EAAE,IAAI,CAAChF,MAAM,CAACQ,gBAAgB,GAAG;QACpD,CAAC;MACH,CAAC;QAAAd,cAAA,GAAAQ,CAAA;MAAA;MAAAR,cAAA,GAAAC,CAAA;MAGDmF,QAAQ,CAACb,KAAK,EAAE;MAACvE,cAAA,GAAAC,CAAA;MACjBmF,QAAQ,CAACG,WAAW,GAAGpB,GAAG;MAACnE,cAAA,GAAAC,CAAA;MAC3B,IAAI,CAACE,aAAa,CAACmE,GAAG,CAACR,UAAU,EAAEsB,QAAQ,CAAC;MAE5C,IAAMK,QAAQ,IAAAzF,cAAA,GAAAC,CAAA,QAAGmF,QAAQ,CAACb,KAAK,IAAI,IAAI,CAACjE,MAAM,CAACQ,gBAAgB;MAACd,cAAA,GAAAC,CAAA;MAEhE,IAAI,CAAC4E,gBAAgB,CAAC;QACpBC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;UAAEjB,UAAU,EAAVA,UAAU;UAAE0B,OAAO,EAAEJ,QAAQ,CAACb,KAAK;UAAEmB,MAAM,EAAED;QAAS,CAAC;QAClET,SAAS,EAAEb,GAAG;QACdc,QAAQ,EAAEQ,QAAQ,IAAAzF,cAAA,GAAAQ,CAAA,WAAG,MAAM,KAAAR,cAAA,GAAAQ,CAAA,WAAG,QAAQ;MACxC,CAAC,CAAC;MAACR,cAAA,GAAAC,CAAA;MAEH,OAAO;QACLyE,OAAO,EAAE,CAACe,QAAQ;QAClBH,iBAAiB,EAAEK,IAAI,CAAC1B,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC3D,MAAM,CAACQ,gBAAgB,GAAGsE,QAAQ,CAACb,KAAK,CAAC;QAC7EqB,WAAW,EAAEH,QAAQ,IAAAzF,cAAA,GAAAQ,CAAA,WAAG4E,QAAQ,CAACG,WAAW,GAAGxE,eAAe,KAAAf,cAAA,GAAAQ,CAAA,WAAGqF,SAAS;MAC5E,CAAC;IACH;EAAC;IAAA3E,GAAA;IAAAC,KAAA,EAKD,SAAA2E,aAAaA,CAACC,KAAa,EAAU;MAAA/F,cAAA,GAAAK,CAAA;MAAAL,cAAA,GAAAC,CAAA;MACnC,IAAI,OAAO8F,KAAK,KAAK,QAAQ,EAAE;QAAA/F,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAC7B,OAAO,EAAE;MACX,CAAC;QAAAD,cAAA,GAAAQ,CAAA;MAAA;MAAAR,cAAA,GAAAC,CAAA;MAED,OAAO8F,KAAK,CACTC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CACpBA,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAC5BA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CACvBA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CACvBC,IAAI,CAAC,CAAC;IACX;EAAC;IAAA/E,GAAA;IAAAC,KAAA,EAKD,SAAA+E,gBAAgBA,CAACH,KAAa,EAAU;MAAA/F,cAAA,GAAAK,CAAA;MAAAL,cAAA,GAAAC,CAAA;MACtC,IAAI,OAAO8F,KAAK,KAAK,QAAQ,EAAE;QAAA/F,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAC7B,OAAO,EAAE;MACX,CAAC;QAAAD,cAAA,GAAAQ,CAAA;MAAA;MAAAR,cAAA,GAAAC,CAAA;MAED,OAAO8F,KAAK,CACTC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CACvBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CACpBA,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CACpBC,IAAI,CAAC,CAAC;IACX;EAAC;IAAA/E,GAAA;IAAAC,KAAA,EAKD,SAAAgF,mBAAmBA,CAAA,EAA8B;MAAA,IAA7BC,MAAc,GAAAC,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAR,SAAA,GAAAQ,SAAA,OAAArG,cAAA,GAAAQ,CAAA,WAAG,EAAE;MAAAR,cAAA,GAAAK,CAAA;MACrC,IAAMiG,KAAK,IAAAtG,cAAA,GAAAC,CAAA,QAAG,gEAAgE;MAC9E,IAAIsG,MAAM,IAAAvG,cAAA,GAAAC,CAAA,QAAG,EAAE;MAACD,cAAA,GAAAC,CAAA;MAEhB,KAAK,IAAIuG,CAAC,IAAAxG,cAAA,GAAAC,CAAA,QAAG,CAAC,GAAEuG,CAAC,GAAGJ,MAAM,EAAEI,CAAC,EAAE,EAAE;QAAAxG,cAAA,GAAAC,CAAA;QAC/BsG,MAAM,IAAID,KAAK,CAACG,MAAM,CAACd,IAAI,CAACe,KAAK,CAACf,IAAI,CAAC7D,MAAM,CAAC,CAAC,GAAGwE,KAAK,CAACF,MAAM,CAAC,CAAC;MAClE;MAACpG,cAAA,GAAAC,CAAA;MAED,OAAOsG,MAAM;IACf;EAAC;IAAArF,GAAA;IAAAC,KAAA,EAKD,SAAQT,qBAAqBA,CAAA,EAAW;MAAAV,cAAA,GAAAK,CAAA;MAAAL,cAAA,GAAAC,CAAA;MACtC,OAAOT,QAAQ,CAACoC,GAAG,CAACC,SAAS,CAACC,MAAM,CAAC,GAAG,GAAC,CAAC,CAAC,CAACa,QAAQ,CAAC,CAAC;IACxD;EAAC;IAAAzB,GAAA;IAAAC,KAAA;MAAA,IAAAwF,YAAA,GAAAC,iBAAA,CAKD,WAAkB1F,GAAW,EAAEO,IAAY,EAAiB;QAAAzB,cAAA,GAAAK,CAAA;QAAAL,cAAA,GAAAC,CAAA;QAC1D,IAAI;UACF,IAAMmC,SAAS,IAAApC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACuB,OAAO,CAACC,IAAI,CAAC;UAACzB,cAAA,GAAAC,CAAA;UACrC,MAAMR,YAAY,CAACoH,OAAO,CAAC3F,GAAG,EAAE4F,IAAI,CAACC,SAAS,CAAC3E,SAAS,CAAC,CAAC;QAC5D,CAAC,CAAC,OAAOQ,KAAK,EAAE;UAAA5C,cAAA,GAAAC,CAAA;UACdN,QAAQ,CAACD,WAAW,CAACkD,KAAK,CAAC,EAAE;YAAEC,OAAO,EAAE,aAAa;YAAE3B,GAAG,EAAHA;UAAI,CAAC,CAAC;UAAClB,cAAA,GAAAC,CAAA;UAC9D,MAAM,IAAI6C,KAAK,CAAC,+BAA+B,CAAC;QAClD;MACF,CAAC;MAAA,SARKkE,WAAWA,CAAAC,EAAA,EAAAC,GAAA;QAAA,OAAAP,YAAA,CAAAQ,KAAA,OAAAd,SAAA;MAAA;MAAA,OAAXW,WAAW;IAAA;EAAA;IAAA9F,GAAA;IAAAC,KAAA;MAAA,IAAAiG,eAAA,GAAAR,iBAAA,CAajB,WAAqB1F,GAAW,EAA0B;QAAAlB,cAAA,GAAAK,CAAA;QAAAL,cAAA,GAAAC,CAAA;QACxD,IAAI;UACF,IAAMoH,MAAM,IAAArH,cAAA,GAAAC,CAAA,cAASR,YAAY,CAAC6H,OAAO,CAACpG,GAAG,CAAC;UAAClB,cAAA,GAAAC,CAAA;UAC/C,IAAI,CAACoH,MAAM,EAAE;YAAArH,cAAA,GAAAQ,CAAA;YAAAR,cAAA,GAAAC,CAAA;YACX,OAAO,IAAI;UACb,CAAC;YAAAD,cAAA,GAAAQ,CAAA;UAAA;UAED,IAAM4B,SAAwB,IAAApC,cAAA,GAAAC,CAAA,QAAG6G,IAAI,CAAC3D,KAAK,CAACkE,MAAM,CAAC;UAACrH,cAAA,GAAAC,CAAA;UACpD,OAAO,IAAI,CAAC8C,OAAO,CAACX,SAAS,CAAC;QAChC,CAAC,CAAC,OAAOQ,KAAK,EAAE;UAAA5C,cAAA,GAAAC,CAAA;UACdN,QAAQ,CAACD,WAAW,CAACkD,KAAK,CAAC,EAAE;YAAEC,OAAO,EAAE,gBAAgB;YAAE3B,GAAG,EAAHA;UAAI,CAAC,CAAC;UAAClB,cAAA,GAAAC,CAAA;UACjE,OAAO,IAAI;QACb;MACF,CAAC;MAAA,SAbKsH,cAAcA,CAAAC,GAAA;QAAA,OAAAJ,eAAA,CAAAD,KAAA,OAAAd,SAAA;MAAA;MAAA,OAAdkB,cAAc;IAAA;EAAA;IAAArG,GAAA;IAAAC,KAAA,EAkBpB,SAAA0D,gBAAgBA,CAAC4C,KAAgE,EAAQ;MAAAzH,cAAA,GAAAK,CAAA;MACvF,IAAMqH,aAA4B,IAAA1H,cAAA,GAAAC,CAAA,QAAA0H,MAAA,CAAAC,MAAA,KAC7BH,KAAK;QACRzC,SAAS,EAAE,CAAAhF,cAAA,GAAAQ,CAAA,WAAAiH,KAAK,CAACzC,SAAS,MAAAhF,cAAA,GAAAQ,CAAA,WAAI4D,IAAI,CAACD,GAAG,CAAC,CAAC;MAAA,GACzC;MAACnE,cAAA,GAAAC,CAAA;MAEF,IAAI,CAACG,cAAc,CAACyH,IAAI,CAACH,aAAa,CAAC;MAAC1H,cAAA,GAAAC,CAAA;MAGxC,IAAIwH,KAAK,CAACxC,QAAQ,KAAK,UAAU,EAAE;QAAAjF,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QACjC6H,OAAO,CAAClF,KAAK,CAAC,0BAA0B,EAAE8E,aAAa,CAAC;QAAC1H,cAAA,GAAAC,CAAA;QAGzD,IAAIL,GAAG,CAACmI,cAAc,CAAC,CAAC,KAAK,YAAY,EAAE;UAAA/H,cAAA,GAAAQ,CAAA;UAAAR,cAAA,GAAAC,CAAA;UACzC,IAAI,CAAC+H,wBAAwB,CAACN,aAAa,CAAC;QAC9C,CAAC;UAAA1H,cAAA,GAAAQ,CAAA;QAAA;MACH,CAAC;QAAAR,cAAA,GAAAQ,CAAA;MAAA;IACH;EAAC;IAAAU,GAAA;IAAAC,KAAA,EAKD,SAAA8G,iBAAiBA,CAAChD,QAAoC,EAAwC;MAAA,IAAtCiD,KAAa,GAAA7B,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAR,SAAA,GAAAQ,SAAA,OAAArG,cAAA,GAAAQ,CAAA,WAAG,GAAG;MAAAR,cAAA,GAAAK,CAAA;MACzE,IAAI8H,MAAM,IAAAnI,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACG,cAAc;MAACJ,cAAA,GAAAC,CAAA;MAEjC,IAAIgF,QAAQ,EAAE;QAAAjF,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QACZkI,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC,UAAAX,KAAK,EAAI;UAAAzH,cAAA,GAAAK,CAAA;UAAAL,cAAA,GAAAC,CAAA;UAAA,OAAAwH,KAAK,CAACxC,QAAQ,KAAKA,QAAQ;QAAD,CAAC,CAAC;MAC9D,CAAC;QAAAjF,cAAA,GAAAQ,CAAA;MAAA;MAAAR,cAAA,GAAAC,CAAA;MAED,OAAOkI,MAAM,CACVE,IAAI,CAAC,UAACC,CAAC,EAAE9H,CAAC,EAAK;QAAAR,cAAA,GAAAK,CAAA;QAAAL,cAAA,GAAAC,CAAA;QAAA,OAAAO,CAAC,CAACwE,SAAS,GAAGsD,CAAC,CAACtD,SAAS;MAAD,CAAC,CAAC,CACzCuD,KAAK,CAAC,CAAC,EAAEL,KAAK,CAAC;IACpB;EAAC;IAAAhH,GAAA;IAAAC,KAAA,EAKD,SAAQG,qBAAqBA,CAAA,EAAS;MAAAtB,cAAA,GAAAK,CAAA;MACpC,IAAM8D,GAAG,IAAAnE,cAAA,GAAAC,CAAA,SAAGmE,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,IAAMD,MAAM,IAAAlE,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACK,MAAM,CAACK,eAAe,GAAG,EAAE,GAAG,IAAI;MAACX,cAAA,GAAAC,CAAA;MAEvD,SAAAuI,KAAA,IAA2B,IAAI,CAACzI,cAAc,CAAC0I,OAAO,CAAC,CAAC,EAAE;QAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAH,KAAA;QAAA,IAA9CtH,GAAG,GAAAwH,KAAA;QAAA,IAAErE,KAAK,GAAAqE,KAAA;QAAA1I,cAAA,GAAAC,CAAA;QACpB,IAAIkE,GAAG,GAAGE,KAAK,CAACG,YAAY,GAAGN,MAAM,EAAE;UAAAlE,cAAA,GAAAQ,CAAA;UAAAR,cAAA,GAAAC,CAAA;UACrC,IAAI,CAACF,cAAc,CAACsF,MAAM,CAACnE,GAAG,CAAC;QACjC,CAAC;UAAAlB,cAAA,GAAAQ,CAAA;QAAA;MACH;IACF;EAAC;IAAAU,GAAA;IAAAC,KAAA,EAKD,SAAQI,qBAAqBA,CAAA,EAAS;MAAAvB,cAAA,GAAAK,CAAA;MACpC,IAAM8D,GAAG,IAAAnE,cAAA,GAAAC,CAAA,SAAGmE,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,IAAMyE,MAAM,IAAA5I,cAAA,GAAAC,CAAA,SAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;MAACD,cAAA,GAAAC,CAAA;MAEnC,IAAI,CAACG,cAAc,GAAG,IAAI,CAACA,cAAc,CAACgI,MAAM,CAC9C,UAAAX,KAAK,EAAI;QAAAzH,cAAA,GAAAK,CAAA;QAAAL,cAAA,GAAAC,CAAA;QAAA,OAAAkE,GAAG,GAAGsD,KAAK,CAACzC,SAAS,GAAG4D,MAAM;MAAD,CACxC,CAAC;IACH;EAAC;IAAA1H,GAAA;IAAAC,KAAA;MAAA,IAAA0H,yBAAA,GAAAjC,iBAAA,CAKD,WAAuCa,KAAoB,EAAiB;QAAAzH,cAAA,GAAAK,CAAA;QAAAL,cAAA,GAAAC,CAAA;QAC1E,IAAI;UAAAD,cAAA,GAAAC,CAAA;UAGF6H,OAAO,CAACgB,GAAG,CAAC,iCAAiC,EAAErB,KAAK,CAAC;QACvD,CAAC,CAAC,OAAO7E,KAAK,EAAE;UAAA5C,cAAA,GAAAC,CAAA;UACd6H,OAAO,CAAClF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;MACF,CAAC;MAAA,SARaoF,wBAAwBA,CAAAe,GAAA;QAAA,OAAAF,yBAAA,CAAA1B,KAAA,OAAAd,SAAA;MAAA;MAAA,OAAxB2B,wBAAwB;IAAA;EAAA;IAAA9G,GAAA;IAAAC,KAAA,EAatC,SAAA6H,oBAAoBA,CAACC,KAAa,EAAW;MAAAjJ,cAAA,GAAAK,CAAA;MAAAL,cAAA,GAAAC,CAAA;MAC3C,IAAI;QAAAD,cAAA,GAAAC,CAAA;QAEF,IAAI,CAAAD,cAAA,GAAAQ,CAAA,YAACyI,KAAK,MAAAjJ,cAAA,GAAAQ,CAAA,WAAIyI,KAAK,CAAC7C,MAAM,GAAG,EAAE,GAAE;UAAApG,cAAA,GAAAQ,CAAA;UAAAR,cAAA,GAAAC,CAAA;UAC/B,OAAO,KAAK;QACd,CAAC;UAAAD,cAAA,GAAAQ,CAAA;QAAA;QAGD,IAAM0I,WAAW,IAAAlJ,cAAA,GAAAC,CAAA,SAAG,wBAAwB;QAACD,cAAA,GAAAC,CAAA;QAC7C,OAAOiJ,WAAW,CAACC,IAAI,CAACF,KAAK,CAAC;MAChC,CAAC,CAAC,OAAOrG,KAAK,EAAE;QAAA5C,cAAA,GAAAC,CAAA;QACd,OAAO,KAAK;MACd;IACF;EAAC;IAAAiB,GAAA;IAAAC,KAAA,EAKD,SAAAiI,wBAAwBA,CAACC,MAAc,EAAEC,QAIxC,EAAW;MAAAtJ,cAAA,GAAAK,CAAA;MAEV,IAAMkJ,YAAY,IAAAvJ,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACG,cAAc,CAACgI,MAAM,CAC7C,UAAAX,KAAK,EAAI;QAAAzH,cAAA,GAAAK,CAAA;QAAAL,cAAA,GAAAC,CAAA;QAAA,QAAAD,cAAA,GAAAQ,CAAA,WAAAiH,KAAK,CAAC4B,MAAM,KAAKA,MAAM,MAAArJ,cAAA,GAAAQ,CAAA,WACvBiH,KAAK,CAACzC,SAAS,GAAGZ,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;MAAD,CACvD,CAAC;MAGD,IAAMqF,YAAY,IAAAxJ,cAAA,GAAAC,CAAA,SAAGsJ,YAAY,CAACnB,MAAM,CAAC,UAAAX,KAAK,EAAI;QAAAzH,cAAA,GAAAK,CAAA;QAAAL,cAAA,GAAAC,CAAA;QAAA,OAAAwH,KAAK,CAAC3C,IAAI,KAAK,cAAc;MAAD,CAAC,CAAC;MAAC9E,cAAA,GAAAC,CAAA;MACjF,IAAIuJ,YAAY,CAACpD,MAAM,GAAG,EAAE,EAAE;QAAApG,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAC5B,IAAI,CAAC4E,gBAAgB,CAAC;UACpBC,IAAI,EAAE,qBAAqB;UAC3BuE,MAAM,EAANA,MAAM;UACNtE,OAAO,EAAE;YAAE0E,MAAM,EAAE,yBAAyB;YAAElF,KAAK,EAAEiF,YAAY,CAACpD;UAAO,CAAC;UAC1EnB,QAAQ,EAAE;QACZ,CAAC,CAAC;QAACjF,cAAA,GAAAC,CAAA;QACH,OAAO,IAAI;MACb,CAAC;QAAAD,cAAA,GAAAQ,CAAA;MAAA;MAGD,IAAMkJ,aAAa,IAAA1J,cAAA,GAAAC,CAAA,SAAGsJ,YAAY,CAACnB,MAAM,CACvC,UAAAX,KAAK,EAAI;QAAAzH,cAAA,GAAAK,CAAA;QAAAL,cAAA,GAAAC,CAAA;QAAA,OAAAwH,KAAK,CAACzC,SAAS,GAAGZ,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;MAAD,CACtD,CAAC;MAACnE,cAAA,GAAAC,CAAA;MACF,IAAIyJ,aAAa,CAACtD,MAAM,GAAG,EAAE,EAAE;QAAApG,cAAA,GAAAQ,CAAA;QAAAR,cAAA,GAAAC,CAAA;QAC7B,IAAI,CAAC4E,gBAAgB,CAAC;UACpBC,IAAI,EAAE,qBAAqB;UAC3BuE,MAAM,EAANA,MAAM;UACNtE,OAAO,EAAE;YAAE0E,MAAM,EAAE,gBAAgB;YAAElF,KAAK,EAAEmF,aAAa,CAACtD;UAAO,CAAC;UAClEnB,QAAQ,EAAE;QACZ,CAAC,CAAC;QAACjF,cAAA,GAAAC,CAAA;QACH,OAAO,IAAI;MACb,CAAC;QAAAD,cAAA,GAAAQ,CAAA;MAAA;MAAAR,cAAA,GAAAC,CAAA;MAED,OAAO,KAAK;IACd;EAAC;IAAAiB,GAAA;IAAAC,KAAA,EAKD,SAAAwI,iBAAiBA,CAAA,EAKf;MAAA3J,cAAA,GAAAK,CAAA;MACA,IAAMuJ,cAAc,IAAA5J,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACG,cAAc,CAACgI,MAAM,CAC/C,UAAAX,KAAK,EAAI;QAAAzH,cAAA,GAAAK,CAAA;QAAAL,cAAA,GAAAC,CAAA;QAAA,OAAAwH,KAAK,CAACxC,QAAQ,KAAK,UAAU;MAAD,CACvC,CAAC,CAACmB,MAAM;MAACpG,cAAA,GAAAC,CAAA;MAET,OAAO;QACL4J,gBAAgB,EAAE,IAAI,CAAC9J,cAAc,CAAC+J,IAAI;QAC1C3J,aAAa,EAAE,IAAI,CAACA,aAAa,CAAC2J,IAAI;QACtC1J,cAAc,EAAE,IAAI,CAACA,cAAc,CAACgG,MAAM;QAC1CwD,cAAc,EAAdA;MACF,CAAC;IACH;EAAC;AAAA;AAIH,OAAO,IAAMG,eAAe,IAAA/J,cAAA,GAAAC,CAAA,SAAG,IAAIJ,eAAe,CAAC,CAAC;AACpD,eAAekK,eAAe", "ignoreList": []}