{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_interopRequireDefault", "default", "exports", "__esModule", "_invariant", "_EventEmitter", "_canUseDom", "isPrefixed", "document", "hasOwnProperty", "EVENT_TYPES", "VISIBILITY_CHANGE_EVENT", "VISIBILITY_STATE_PROPERTY", "AppStates", "BACKGROUND", "ACTIVE", "changeEmitter", "AppState", "key", "get", "isAvailable", "value", "addEventListener", "type", "handler", "indexOf", "emit", "currentState", "addListener", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nvar _EventEmitter = _interopRequireDefault(require(\"../../vendor/react-native/vendor/emitter/EventEmitter\"));\nvar _canUseDom = _interopRequireDefault(require(\"../../modules/canUseDom\"));\n// Android 4.4 browser\nvar isPrefixed = _canUseDom.default && !document.hasOwnProperty('hidden') && document.hasOwnProperty('webkitHidden');\nvar EVENT_TYPES = ['change', 'memoryWarning'];\nvar VISIBILITY_CHANGE_EVENT = isPrefixed ? 'webkitvisibilitychange' : 'visibilitychange';\nvar VISIBILITY_STATE_PROPERTY = isPrefixed ? 'webkitVisibilityState' : 'visibilityState';\nvar AppStates = {\n  BACKGROUND: 'background',\n  ACTIVE: 'active'\n};\nvar changeEmitter = null;\nclass AppState {\n  static get currentState() {\n    if (!AppState.isAvailable) {\n      return AppStates.ACTIVE;\n    }\n    switch (document[VISIBILITY_STATE_PROPERTY]) {\n      case 'hidden':\n      case 'prerender':\n      case 'unloaded':\n        return AppStates.BACKGROUND;\n      default:\n        return AppStates.ACTIVE;\n    }\n  }\n  static addEventListener(type, handler) {\n    if (AppState.isAvailable) {\n      (0, _invariant.default)(EVENT_TYPES.indexOf(type) !== -1, 'Trying to subscribe to unknown event: \"%s\"', type);\n      if (type === 'change') {\n        if (!changeEmitter) {\n          changeEmitter = new _EventEmitter.default();\n          document.addEventListener(VISIBILITY_CHANGE_EVENT, () => {\n            if (changeEmitter) {\n              changeEmitter.emit('change', AppState.currentState);\n            }\n          }, false);\n        }\n        return changeEmitter.addListener(type, handler);\n      }\n    }\n  }\n}\nexports.default = AppState;\nAppState.isAvailable = _canUseDom.default && !!document[VISIBILITY_STATE_PROPERTY];\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAEb,IAAIG,sBAAsB,GAAGH,OAAO,CAAC,8CAA8C,CAAC,CAACI,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,UAAU,GAAGJ,sBAAsB,CAACH,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAIQ,aAAa,GAAGL,sBAAsB,CAACH,OAAO,wDAAwD,CAAC,CAAC;AAC5G,IAAIS,UAAU,GAAGN,sBAAsB,CAACH,OAAO,0BAA0B,CAAC,CAAC;AAE3E,IAAIU,UAAU,GAAGD,UAAU,CAACL,OAAO,IAAI,CAACO,QAAQ,CAACC,cAAc,CAAC,QAAQ,CAAC,IAAID,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;AACpH,IAAIC,WAAW,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC;AAC7C,IAAIC,uBAAuB,GAAGJ,UAAU,GAAG,wBAAwB,GAAG,kBAAkB;AACxF,IAAIK,yBAAyB,GAAGL,UAAU,GAAG,uBAAuB,GAAG,iBAAiB;AACxF,IAAIM,SAAS,GAAG;EACdC,UAAU,EAAE,YAAY;EACxBC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,aAAa,GAAG,IAAI;AAAC,IACnBC,QAAQ;EAAA,SAAAA,SAAA;IAAA,IAAAnB,gBAAA,CAAAG,OAAA,QAAAgB,QAAA;EAAA;EAAA,WAAAlB,aAAA,CAAAE,OAAA,EAAAgB,QAAA;IAAAC,GAAA;IAAAC,GAAA,EACZ,SAAAA,IAAA,EAA0B;MACxB,IAAI,CAACF,QAAQ,CAACG,WAAW,EAAE;QACzB,OAAOP,SAAS,CAACE,MAAM;MACzB;MACA,QAAQP,QAAQ,CAACI,yBAAyB,CAAC;QACzC,KAAK,QAAQ;QACb,KAAK,WAAW;QAChB,KAAK,UAAU;UACb,OAAOC,SAAS,CAACC,UAAU;QAC7B;UACE,OAAOD,SAAS,CAACE,MAAM;MAC3B;IACF;EAAC;IAAAG,GAAA;IAAAG,KAAA,EACD,SAAOC,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;MACrC,IAAIP,QAAQ,CAACG,WAAW,EAAE;QACxB,CAAC,CAAC,EAAEhB,UAAU,CAACH,OAAO,EAAES,WAAW,CAACe,OAAO,CAACF,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,4CAA4C,EAAEA,IAAI,CAAC;QAC7G,IAAIA,IAAI,KAAK,QAAQ,EAAE;UACrB,IAAI,CAACP,aAAa,EAAE;YAClBA,aAAa,GAAG,IAAIX,aAAa,CAACJ,OAAO,CAAC,CAAC;YAC3CO,QAAQ,CAACc,gBAAgB,CAACX,uBAAuB,EAAE,YAAM;cACvD,IAAIK,aAAa,EAAE;gBACjBA,aAAa,CAACU,IAAI,CAAC,QAAQ,EAAET,QAAQ,CAACU,YAAY,CAAC;cACrD;YACF,CAAC,EAAE,KAAK,CAAC;UACX;UACA,OAAOX,aAAa,CAACY,WAAW,CAACL,IAAI,EAAEC,OAAO,CAAC;QACjD;MACF;IACF;EAAC;AAAA;AAEHtB,OAAO,CAACD,OAAO,GAAGgB,QAAQ;AAC1BA,QAAQ,CAACG,WAAW,GAAGd,UAAU,CAACL,OAAO,IAAI,CAAC,CAACO,QAAQ,CAACI,yBAAyB,CAAC;AAClFiB,MAAM,CAAC3B,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}