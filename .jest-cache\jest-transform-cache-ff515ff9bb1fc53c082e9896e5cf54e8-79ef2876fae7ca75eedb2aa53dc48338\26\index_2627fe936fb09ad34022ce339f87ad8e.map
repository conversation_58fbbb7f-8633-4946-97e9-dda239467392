{"version": 3, "names": ["exports", "__esModule", "warnOnce", "<PERSON><PERSON><PERSON><PERSON>", "key", "message", "process", "env", "NODE_ENV", "console", "warn"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.warnOnce = warnOnce;\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar warnedKeys = {};\n\n/**\n * A simple function that prints a warning message once per session.\n *\n * @param {string} key - The key used to ensure the message is printed once.\n *                       This should be unique to the callsite.\n * @param {string} message - The message to print\n */\nfunction warnOnce(key, message) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (warnedKeys[key]) {\n      return;\n    }\n    console.warn(message);\n    warnedKeys[key] = true;\n  }\n}"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,QAAQ,GAAGA,QAAQ;AAU3B,IAAIC,UAAU,GAAG,CAAC,CAAC;AASnB,SAASD,QAAQA,CAACE,GAAG,EAAEC,OAAO,EAAE;EAC9B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIL,UAAU,CAACC,GAAG,CAAC,EAAE;MACnB;IACF;IACAK,OAAO,CAACC,IAAI,CAACL,OAAO,CAAC;IACrBF,UAAU,CAACC,GAAG,CAAC,GAAG,IAAI;EACxB;AACF", "ignoreList": []}