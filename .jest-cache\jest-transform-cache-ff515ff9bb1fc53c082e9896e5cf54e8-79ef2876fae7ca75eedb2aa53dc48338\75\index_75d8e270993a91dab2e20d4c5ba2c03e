9f2034639fd163a55baaf2828e388c62
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _InteractionManager = _interopRequireDefault(require("../../../exports/InteractionManager"));
var Batchinator = function () {
  function Batchinator(callback, delayMS) {
    (0, _classCallCheck2.default)(this, Batchinator);
    this._delay = delayMS;
    this._callback = callback;
  }
  return (0, _createClass2.default)(Batchina<PERSON>, [{
    key: "dispose",
    value: function dispose(options) {
      if (options === void 0) {
        options = {
          abort: false
        };
      }
      if (this._taskHandle) {
        this._taskHandle.cancel();
        if (!options.abort) {
          this._callback();
        }
        this._taskHandle = null;
      }
    }
  }, {
    key: "schedule",
    value: function schedule() {
      var _this = this;
      if (this._taskHandle) {
        return;
      }
      var timeoutHandle = setTimeout(function () {
        _this._taskHandle = _InteractionManager.default.runAfterInteractions(function () {
          _this._taskHandle = null;
          _this._callback();
        });
      }, this._delay);
      this._taskHandle = {
        cancel: function cancel() {
          return clearTimeout(timeoutHandle);
        }
      };
    }
  }]);
}();
var _default = exports.default = Batchinator;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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