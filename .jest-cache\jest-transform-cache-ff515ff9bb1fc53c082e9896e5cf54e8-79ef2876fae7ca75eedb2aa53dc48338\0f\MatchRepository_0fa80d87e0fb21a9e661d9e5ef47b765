5d1f89689609a7b5cfae26098950ac72
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_q5ev797dr() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\services\\database\\MatchRepository.ts";
  var hash = "cef1bdde1ea453cffc9118ee4a42b0c9763f46a5";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\services\\database\\MatchRepository.ts",
    statementMap: {
      "0": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 98,
          column: 5
        }
      },
      "1": {
        start: {
          line: 83,
          column: 30
        },
        end: {
          line: 87,
          column: 17
        }
      },
      "2": {
        start: {
          line: 89,
          column: 6
        },
        end: {
          line: 92,
          column: 7
        }
      },
      "3": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 90,
          column: 54
        }
      },
      "4": {
        start: {
          line: 91,
          column: 8
        },
        end: {
          line: 91,
          column: 52
        }
      },
      "5": {
        start: {
          line: 94,
          column: 6
        },
        end: {
          line: 94,
          column: 35
        }
      },
      "6": {
        start: {
          line: 96,
          column: 6
        },
        end: {
          line: 96,
          column: 52
        }
      },
      "7": {
        start: {
          line: 97,
          column: 6
        },
        end: {
          line: 97,
          column: 61
        }
      },
      "8": {
        start: {
          line: 108,
          column: 4
        },
        end: {
          line: 125,
          column: 5
        }
      },
      "9": {
        start: {
          line: 109,
          column: 30
        },
        end: {
          line: 114,
          column: 17
        }
      },
      "10": {
        start: {
          line: 116,
          column: 6
        },
        end: {
          line: 119,
          column: 7
        }
      },
      "11": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 117,
          column: 54
        }
      },
      "12": {
        start: {
          line: 118,
          column: 8
        },
        end: {
          line: 118,
          column: 52
        }
      },
      "13": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 121,
          column: 35
        }
      },
      "14": {
        start: {
          line: 123,
          column: 6
        },
        end: {
          line: 123,
          column: 52
        }
      },
      "15": {
        start: {
          line: 124,
          column: 6
        },
        end: {
          line: 124,
          column: 61
        }
      },
      "16": {
        start: {
          line: 132,
          column: 4
        },
        end: {
          line: 148,
          column: 5
        }
      },
      "17": {
        start: {
          line: 133,
          column: 30
        },
        end: {
          line: 137,
          column: 17
        }
      },
      "18": {
        start: {
          line: 139,
          column: 6
        },
        end: {
          line: 142,
          column: 7
        }
      },
      "19": {
        start: {
          line: 140,
          column: 8
        },
        end: {
          line: 140,
          column: 54
        }
      },
      "20": {
        start: {
          line: 141,
          column: 8
        },
        end: {
          line: 141,
          column: 52
        }
      },
      "21": {
        start: {
          line: 144,
          column: 6
        },
        end: {
          line: 144,
          column: 35
        }
      },
      "22": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 146,
          column: 52
        }
      },
      "23": {
        start: {
          line: 147,
          column: 6
        },
        end: {
          line: 147,
          column: 60
        }
      },
      "24": {
        start: {
          line: 159,
          column: 4
        },
        end: {
          line: 176,
          column: 5
        }
      },
      "25": {
        start: {
          line: 160,
          column: 30
        },
        end: {
          line: 165,
          column: 42
        }
      },
      "26": {
        start: {
          line: 167,
          column: 6
        },
        end: {
          line: 170,
          column: 7
        }
      },
      "27": {
        start: {
          line: 168,
          column: 8
        },
        end: {
          line: 168,
          column: 61
        }
      },
      "28": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 169,
          column: 52
        }
      },
      "29": {
        start: {
          line: 172,
          column: 6
        },
        end: {
          line: 172,
          column: 35
        }
      },
      "30": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 59
        }
      },
      "31": {
        start: {
          line: 175,
          column: 6
        },
        end: {
          line: 175,
          column: 62
        }
      },
      "32": {
        start: {
          line: 183,
          column: 4
        },
        end: {
          line: 198,
          column: 5
        }
      },
      "33": {
        start: {
          line: 184,
          column: 24
        },
        end: {
          line: 187,
          column: 26
        }
      },
      "34": {
        start: {
          line: 189,
          column: 6
        },
        end: {
          line: 192,
          column: 7
        }
      },
      "35": {
        start: {
          line: 190,
          column: 8
        },
        end: {
          line: 190,
          column: 54
        }
      },
      "36": {
        start: {
          line: 191,
          column: 8
        },
        end: {
          line: 191,
          column: 40
        }
      },
      "37": {
        start: {
          line: 194,
          column: 6
        },
        end: {
          line: 194,
          column: 29
        }
      },
      "38": {
        start: {
          line: 196,
          column: 6
        },
        end: {
          line: 196,
          column: 52
        }
      },
      "39": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 197,
          column: 49
        }
      },
      "40": {
        start: {
          line: 208,
          column: 4
        },
        end: {
          line: 228,
          column: 5
        }
      },
      "41": {
        start: {
          line: 209,
          column: 23
        },
        end: {
          line: 212,
          column: 9
        }
      },
      "42": {
        start: {
          line: 209,
          column: 40
        },
        end: {
          line: 212,
          column: 7
        }
      },
      "43": {
        start: {
          line: 214,
          column: 30
        },
        end: {
          line: 217,
          column: 17
        }
      },
      "44": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 222,
          column: 7
        }
      },
      "45": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 220,
          column: 59
        }
      },
      "46": {
        start: {
          line: 221,
          column: 8
        },
        end: {
          line: 221,
          column: 52
        }
      },
      "47": {
        start: {
          line: 224,
          column: 6
        },
        end: {
          line: 224,
          column: 35
        }
      },
      "48": {
        start: {
          line: 226,
          column: 6
        },
        end: {
          line: 226,
          column: 57
        }
      },
      "49": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 66
        }
      },
      "50": {
        start: {
          line: 235,
          column: 4
        },
        end: {
          line: 251,
          column: 5
        }
      },
      "51": {
        start: {
          line: 236,
          column: 30
        },
        end: {
          line: 240,
          column: 28
        }
      },
      "52": {
        start: {
          line: 242,
          column: 6
        },
        end: {
          line: 245,
          column: 7
        }
      },
      "53": {
        start: {
          line: 243,
          column: 8
        },
        end: {
          line: 243,
          column: 59
        }
      },
      "54": {
        start: {
          line: 244,
          column: 8
        },
        end: {
          line: 244,
          column: 52
        }
      },
      "55": {
        start: {
          line: 247,
          column: 6
        },
        end: {
          line: 247,
          column: 35
        }
      },
      "56": {
        start: {
          line: 249,
          column: 6
        },
        end: {
          line: 249,
          column: 57
        }
      },
      "57": {
        start: {
          line: 250,
          column: 6
        },
        end: {
          line: 250,
          column: 65
        }
      },
      "58": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 276,
          column: 5
        }
      },
      "59": {
        start: {
          line: 261,
          column: 30
        },
        end: {
          line: 265,
          column: 17
        }
      },
      "60": {
        start: {
          line: 267,
          column: 6
        },
        end: {
          line: 270,
          column: 7
        }
      },
      "61": {
        start: {
          line: 268,
          column: 8
        },
        end: {
          line: 268,
          column: 65
        }
      },
      "62": {
        start: {
          line: 269,
          column: 8
        },
        end: {
          line: 269,
          column: 52
        }
      },
      "63": {
        start: {
          line: 272,
          column: 6
        },
        end: {
          line: 272,
          column: 35
        }
      },
      "64": {
        start: {
          line: 274,
          column: 6
        },
        end: {
          line: 274,
          column: 63
        }
      },
      "65": {
        start: {
          line: 275,
          column: 6
        },
        end: {
          line: 275,
          column: 72
        }
      },
      "66": {
        start: {
          line: 287,
          column: 4
        },
        end: {
          line: 305,
          column: 5
        }
      },
      "67": {
        start: {
          line: 288,
          column: 30
        },
        end: {
          line: 294,
          column: 17
        }
      },
      "68": {
        start: {
          line: 296,
          column: 6
        },
        end: {
          line: 299,
          column: 7
        }
      },
      "69": {
        start: {
          line: 297,
          column: 8
        },
        end: {
          line: 297,
          column: 65
        }
      },
      "70": {
        start: {
          line: 298,
          column: 8
        },
        end: {
          line: 298,
          column: 52
        }
      },
      "71": {
        start: {
          line: 301,
          column: 6
        },
        end: {
          line: 301,
          column: 35
        }
      },
      "72": {
        start: {
          line: 303,
          column: 6
        },
        end: {
          line: 303,
          column: 63
        }
      },
      "73": {
        start: {
          line: 304,
          column: 6
        },
        end: {
          line: 304,
          column: 72
        }
      },
      "74": {
        start: {
          line: 315,
          column: 4
        },
        end: {
          line: 332,
          column: 5
        }
      },
      "75": {
        start: {
          line: 316,
          column: 30
        },
        end: {
          line: 321,
          column: 17
        }
      },
      "76": {
        start: {
          line: 323,
          column: 6
        },
        end: {
          line: 326,
          column: 7
        }
      },
      "77": {
        start: {
          line: 324,
          column: 8
        },
        end: {
          line: 324,
          column: 65
        }
      },
      "78": {
        start: {
          line: 325,
          column: 8
        },
        end: {
          line: 325,
          column: 52
        }
      },
      "79": {
        start: {
          line: 328,
          column: 6
        },
        end: {
          line: 328,
          column: 35
        }
      },
      "80": {
        start: {
          line: 330,
          column: 6
        },
        end: {
          line: 330,
          column: 63
        }
      },
      "81": {
        start: {
          line: 331,
          column: 6
        },
        end: {
          line: 331,
          column: 71
        }
      },
      "82": {
        start: {
          line: 339,
          column: 4
        },
        end: {
          line: 362,
          column: 6
        }
      },
      "83": {
        start: {
          line: 369,
          column: 4
        },
        end: {
          line: 425,
          column: 6
        }
      },
      "84": {
        start: {
          line: 430,
          column: 31
        },
        end: {
          line: 430,
          column: 52
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 81,
            column: 2
          },
          end: {
            line: 81,
            column: 3
          }
        },
        loc: {
          start: {
            line: 81,
            column: 118
          },
          end: {
            line: 99,
            column: 3
          }
        },
        line: 81
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 104,
            column: 2
          },
          end: {
            line: 104,
            column: 3
          }
        },
        loc: {
          start: {
            line: 107,
            column: 67
          },
          end: {
            line: 126,
            column: 3
          }
        },
        line: 107
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 131,
            column: 2
          },
          end: {
            line: 131,
            column: 3
          }
        },
        loc: {
          start: {
            line: 131,
            column: 97
          },
          end: {
            line: 149,
            column: 3
          }
        },
        line: 131
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 154,
            column: 2
          },
          end: {
            line: 154,
            column: 3
          }
        },
        loc: {
          start: {
            line: 158,
            column: 69
          },
          end: {
            line: 177,
            column: 3
          }
        },
        line: 158
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 182,
            column: 2
          },
          end: {
            line: 182,
            column: 3
          }
        },
        loc: {
          start: {
            line: 182,
            column: 72
          },
          end: {
            line: 199,
            column: 3
          }
        },
        line: 182
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 204,
            column: 2
          },
          end: {
            line: 204,
            column: 3
          }
        },
        loc: {
          start: {
            line: 207,
            column: 72
          },
          end: {
            line: 229,
            column: 3
          }
        },
        line: 207
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 209,
            column: 32
          },
          end: {
            line: 209,
            column: 33
          }
        },
        loc: {
          start: {
            line: 209,
            column: 40
          },
          end: {
            line: 212,
            column: 7
          }
        },
        line: 209
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 234,
            column: 2
          },
          end: {
            line: 234,
            column: 3
          }
        },
        loc: {
          start: {
            line: 234,
            column: 106
          },
          end: {
            line: 252,
            column: 3
          }
        },
        line: 234
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 257,
            column: 2
          },
          end: {
            line: 257,
            column: 3
          }
        },
        loc: {
          start: {
            line: 259,
            column: 77
          },
          end: {
            line: 277,
            column: 3
          }
        },
        line: 259
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 282,
            column: 2
          },
          end: {
            line: 282,
            column: 3
          }
        },
        loc: {
          start: {
            line: 286,
            column: 77
          },
          end: {
            line: 306,
            column: 3
          }
        },
        line: 286
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 311,
            column: 2
          },
          end: {
            line: 311,
            column: 3
          }
        },
        loc: {
          start: {
            line: 314,
            column: 77
          },
          end: {
            line: 333,
            column: 3
          }
        },
        line: 314
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 338,
            column: 2
          },
          end: {
            line: 338,
            column: 3
          }
        },
        loc: {
          start: {
            line: 338,
            column: 67
          },
          end: {
            line: 363,
            column: 3
          }
        },
        line: 338
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 368,
            column: 2
          },
          end: {
            line: 368,
            column: 3
          }
        },
        loc: {
          start: {
            line: 368,
            column: 62
          },
          end: {
            line: 426,
            column: 3
          }
        },
        line: 368
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 89,
            column: 6
          },
          end: {
            line: 92,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 89,
            column: 6
          },
          end: {
            line: 92,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 89
      },
      "1": {
        loc: {
          start: {
            line: 116,
            column: 6
          },
          end: {
            line: 119,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 116,
            column: 6
          },
          end: {
            line: 119,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 116
      },
      "2": {
        loc: {
          start: {
            line: 139,
            column: 6
          },
          end: {
            line: 142,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 6
          },
          end: {
            line: 142,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 139
      },
      "3": {
        loc: {
          start: {
            line: 156,
            column: 4
          },
          end: {
            line: 156,
            column: 22
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 156,
            column: 20
          },
          end: {
            line: 156,
            column: 22
          }
        }],
        line: 156
      },
      "4": {
        loc: {
          start: {
            line: 157,
            column: 4
          },
          end: {
            line: 157,
            column: 22
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 157,
            column: 21
          },
          end: {
            line: 157,
            column: 22
          }
        }],
        line: 157
      },
      "5": {
        loc: {
          start: {
            line: 167,
            column: 6
          },
          end: {
            line: 170,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 167,
            column: 6
          },
          end: {
            line: 170,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 167
      },
      "6": {
        loc: {
          start: {
            line: 189,
            column: 6
          },
          end: {
            line: 192,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 189,
            column: 6
          },
          end: {
            line: 192,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 189
      },
      "7": {
        loc: {
          start: {
            line: 219,
            column: 6
          },
          end: {
            line: 222,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 6
          },
          end: {
            line: 222,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 219
      },
      "8": {
        loc: {
          start: {
            line: 242,
            column: 6
          },
          end: {
            line: 245,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 242,
            column: 6
          },
          end: {
            line: 245,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 242
      },
      "9": {
        loc: {
          start: {
            line: 267,
            column: 6
          },
          end: {
            line: 270,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 267,
            column: 6
          },
          end: {
            line: 270,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 267
      },
      "10": {
        loc: {
          start: {
            line: 296,
            column: 6
          },
          end: {
            line: 299,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 296,
            column: 6
          },
          end: {
            line: 299,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 296
      },
      "11": {
        loc: {
          start: {
            line: 323,
            column: 6
          },
          end: {
            line: 326,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 323,
            column: 6
          },
          end: {
            line: 326,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 323
      },
      "12": {
        loc: {
          start: {
            line: 347,
            column: 26
          },
          end: {
            line: 350,
            column: 14
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 347,
            column: 61
          },
          end: {
            line: 350,
            column: 7
          }
        }, {
          start: {
            line: 350,
            column: 10
          },
          end: {
            line: 350,
            column: 14
          }
        }],
        line: 347
      },
      "13": {
        loc: {
          start: {
            line: 386,
            column: 13
          },
          end: {
            line: 392,
            column: 7
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 386,
            column: 13
          },
          end: {
            line: 386,
            column: 32
          }
        }, {
          start: {
            line: 386,
            column: 36
          },
          end: {
            line: 392,
            column: 7
          }
        }],
        line: 386
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0],
      "4": [0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "cef1bdde1ea453cffc9118ee4a42b0c9763f46a5"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_q5ev797dr = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_q5ev797dr();
import { supabase } from "../../../lib/supabase";
var MatchRepository = function () {
  function MatchRepository() {
    _classCallCheck(this, MatchRepository);
  }
  return _createClass(MatchRepository, [{
    key: "createMatch",
    value: (function () {
      var _createMatch = _asyncToGenerator(function* (matchData) {
        cov_q5ev797dr().f[0]++;
        cov_q5ev797dr().s[0]++;
        try {
          var _ref = (cov_q5ev797dr().s[1]++, yield supabase.from('matches').insert([matchData]).select().single()),
            data = _ref.data,
            error = _ref.error;
          cov_q5ev797dr().s[2]++;
          if (error) {
            cov_q5ev797dr().b[0][0]++;
            cov_q5ev797dr().s[3]++;
            console.error('Error creating match:', error);
            cov_q5ev797dr().s[4]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[0][1]++;
          }
          cov_q5ev797dr().s[5]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[6]++;
          console.error('Error creating match:', error);
          cov_q5ev797dr().s[7]++;
          return {
            data: null,
            error: 'Failed to create match'
          };
        }
      });
      function createMatch(_x) {
        return _createMatch.apply(this, arguments);
      }
      return createMatch;
    }())
  }, {
    key: "updateMatch",
    value: (function () {
      var _updateMatch = _asyncToGenerator(function* (matchId, updates) {
        cov_q5ev797dr().f[1]++;
        cov_q5ev797dr().s[8]++;
        try {
          var _ref2 = (cov_q5ev797dr().s[9]++, yield supabase.from('matches').update(Object.assign({}, updates, {
              updated_at: new Date().toISOString()
            })).eq('id', matchId).select().single()),
            data = _ref2.data,
            error = _ref2.error;
          cov_q5ev797dr().s[10]++;
          if (error) {
            cov_q5ev797dr().b[1][0]++;
            cov_q5ev797dr().s[11]++;
            console.error('Error updating match:', error);
            cov_q5ev797dr().s[12]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[1][1]++;
          }
          cov_q5ev797dr().s[13]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[14]++;
          console.error('Error updating match:', error);
          cov_q5ev797dr().s[15]++;
          return {
            data: null,
            error: 'Failed to update match'
          };
        }
      });
      function updateMatch(_x2, _x3) {
        return _updateMatch.apply(this, arguments);
      }
      return updateMatch;
    }())
  }, {
    key: "getMatch",
    value: (function () {
      var _getMatch = _asyncToGenerator(function* (matchId) {
        cov_q5ev797dr().f[2]++;
        cov_q5ev797dr().s[16]++;
        try {
          var _ref3 = (cov_q5ev797dr().s[17]++, yield supabase.from('matches').select('*').eq('id', matchId).single()),
            data = _ref3.data,
            error = _ref3.error;
          cov_q5ev797dr().s[18]++;
          if (error) {
            cov_q5ev797dr().b[2][0]++;
            cov_q5ev797dr().s[19]++;
            console.error('Error fetching match:', error);
            cov_q5ev797dr().s[20]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[2][1]++;
          }
          cov_q5ev797dr().s[21]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[22]++;
          console.error('Error fetching match:', error);
          cov_q5ev797dr().s[23]++;
          return {
            data: null,
            error: 'Failed to fetch match'
          };
        }
      });
      function getMatch(_x4) {
        return _getMatch.apply(this, arguments);
      }
      return getMatch;
    }())
  }, {
    key: "getUserMatches",
    value: (function () {
      var _getUserMatches = _asyncToGenerator(function* (userId) {
        var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_q5ev797dr().b[3][0]++, 50);
        var offset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_q5ev797dr().b[4][0]++, 0);
        cov_q5ev797dr().f[3]++;
        cov_q5ev797dr().s[24]++;
        try {
          var _ref4 = (cov_q5ev797dr().s[25]++, yield supabase.from('matches').select('*').eq('user_id', userId).order('created_at', {
              ascending: false
            }).range(offset, offset + limit - 1)),
            data = _ref4.data,
            error = _ref4.error;
          cov_q5ev797dr().s[26]++;
          if (error) {
            cov_q5ev797dr().b[5][0]++;
            cov_q5ev797dr().s[27]++;
            console.error('Error fetching user matches:', error);
            cov_q5ev797dr().s[28]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[5][1]++;
          }
          cov_q5ev797dr().s[29]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[30]++;
          console.error('Error fetching user matches:', error);
          cov_q5ev797dr().s[31]++;
          return {
            data: null,
            error: 'Failed to fetch matches'
          };
        }
      });
      function getUserMatches(_x5) {
        return _getUserMatches.apply(this, arguments);
      }
      return getUserMatches;
    }())
  }, {
    key: "deleteMatch",
    value: (function () {
      var _deleteMatch = _asyncToGenerator(function* (matchId) {
        cov_q5ev797dr().f[4]++;
        cov_q5ev797dr().s[32]++;
        try {
          var _ref5 = (cov_q5ev797dr().s[33]++, yield supabase.from('matches').delete().eq('id', matchId)),
            error = _ref5.error;
          cov_q5ev797dr().s[34]++;
          if (error) {
            cov_q5ev797dr().b[6][0]++;
            cov_q5ev797dr().s[35]++;
            console.error('Error deleting match:', error);
            cov_q5ev797dr().s[36]++;
            return {
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[6][1]++;
          }
          cov_q5ev797dr().s[37]++;
          return {
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[38]++;
          console.error('Error deleting match:', error);
          cov_q5ev797dr().s[39]++;
          return {
            error: 'Failed to delete match'
          };
        }
      });
      function deleteMatch(_x6) {
        return _deleteMatch.apply(this, arguments);
      }
      return deleteMatch;
    }())
  }, {
    key: "createMatchSets",
    value: (function () {
      var _createMatchSets = _asyncToGenerator(function* (matchId, sets) {
        cov_q5ev797dr().f[5]++;
        cov_q5ev797dr().s[40]++;
        try {
          var setsData = (cov_q5ev797dr().s[41]++, sets.map(function (set) {
            cov_q5ev797dr().f[6]++;
            cov_q5ev797dr().s[42]++;
            return Object.assign({}, set, {
              match_id: matchId
            });
          }));
          var _ref6 = (cov_q5ev797dr().s[43]++, yield supabase.from('match_sets').insert(setsData).select()),
            data = _ref6.data,
            error = _ref6.error;
          cov_q5ev797dr().s[44]++;
          if (error) {
            cov_q5ev797dr().b[7][0]++;
            cov_q5ev797dr().s[45]++;
            console.error('Error creating match sets:', error);
            cov_q5ev797dr().s[46]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[7][1]++;
          }
          cov_q5ev797dr().s[47]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[48]++;
          console.error('Error creating match sets:', error);
          cov_q5ev797dr().s[49]++;
          return {
            data: null,
            error: 'Failed to create match sets'
          };
        }
      });
      function createMatchSets(_x7, _x8) {
        return _createMatchSets.apply(this, arguments);
      }
      return createMatchSets;
    }())
  }, {
    key: "getMatchSets",
    value: (function () {
      var _getMatchSets = _asyncToGenerator(function* (matchId) {
        cov_q5ev797dr().f[7]++;
        cov_q5ev797dr().s[50]++;
        try {
          var _ref7 = (cov_q5ev797dr().s[51]++, yield supabase.from('match_sets').select('*').eq('match_id', matchId).order('set_number')),
            data = _ref7.data,
            error = _ref7.error;
          cov_q5ev797dr().s[52]++;
          if (error) {
            cov_q5ev797dr().b[8][0]++;
            cov_q5ev797dr().s[53]++;
            console.error('Error fetching match sets:', error);
            cov_q5ev797dr().s[54]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[8][1]++;
          }
          cov_q5ev797dr().s[55]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[56]++;
          console.error('Error fetching match sets:', error);
          cov_q5ev797dr().s[57]++;
          return {
            data: null,
            error: 'Failed to fetch match sets'
          };
        }
      });
      function getMatchSets(_x9) {
        return _getMatchSets.apply(this, arguments);
      }
      return getMatchSets;
    }())
  }, {
    key: "createMatchStatistics",
    value: (function () {
      var _createMatchStatistics = _asyncToGenerator(function* (statistics) {
        cov_q5ev797dr().f[8]++;
        cov_q5ev797dr().s[58]++;
        try {
          var _ref8 = (cov_q5ev797dr().s[59]++, yield supabase.from('match_statistics').insert([statistics]).select().single()),
            data = _ref8.data,
            error = _ref8.error;
          cov_q5ev797dr().s[60]++;
          if (error) {
            cov_q5ev797dr().b[9][0]++;
            cov_q5ev797dr().s[61]++;
            console.error('Error creating match statistics:', error);
            cov_q5ev797dr().s[62]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[9][1]++;
          }
          cov_q5ev797dr().s[63]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[64]++;
          console.error('Error creating match statistics:', error);
          cov_q5ev797dr().s[65]++;
          return {
            data: null,
            error: 'Failed to create match statistics'
          };
        }
      });
      function createMatchStatistics(_x0) {
        return _createMatchStatistics.apply(this, arguments);
      }
      return createMatchStatistics;
    }())
  }, {
    key: "updateMatchStatistics",
    value: (function () {
      var _updateMatchStatistics = _asyncToGenerator(function* (matchId, userId, updates) {
        cov_q5ev797dr().f[9]++;
        cov_q5ev797dr().s[66]++;
        try {
          var _ref9 = (cov_q5ev797dr().s[67]++, yield supabase.from('match_statistics').update(updates).eq('match_id', matchId).eq('user_id', userId).select().single()),
            data = _ref9.data,
            error = _ref9.error;
          cov_q5ev797dr().s[68]++;
          if (error) {
            cov_q5ev797dr().b[10][0]++;
            cov_q5ev797dr().s[69]++;
            console.error('Error updating match statistics:', error);
            cov_q5ev797dr().s[70]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[10][1]++;
          }
          cov_q5ev797dr().s[71]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[72]++;
          console.error('Error updating match statistics:', error);
          cov_q5ev797dr().s[73]++;
          return {
            data: null,
            error: 'Failed to update match statistics'
          };
        }
      });
      function updateMatchStatistics(_x1, _x10, _x11) {
        return _updateMatchStatistics.apply(this, arguments);
      }
      return updateMatchStatistics;
    }())
  }, {
    key: "getMatchStatistics",
    value: (function () {
      var _getMatchStatistics = _asyncToGenerator(function* (matchId, userId) {
        cov_q5ev797dr().f[10]++;
        cov_q5ev797dr().s[74]++;
        try {
          var _ref0 = (cov_q5ev797dr().s[75]++, yield supabase.from('match_statistics').select('*').eq('match_id', matchId).eq('user_id', userId).single()),
            data = _ref0.data,
            error = _ref0.error;
          cov_q5ev797dr().s[76]++;
          if (error) {
            cov_q5ev797dr().b[11][0]++;
            cov_q5ev797dr().s[77]++;
            console.error('Error fetching match statistics:', error);
            cov_q5ev797dr().s[78]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_q5ev797dr().b[11][1]++;
          }
          cov_q5ev797dr().s[79]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_q5ev797dr().s[80]++;
          console.error('Error fetching match statistics:', error);
          cov_q5ev797dr().s[81]++;
          return {
            data: null,
            error: 'Failed to fetch match statistics'
          };
        }
      });
      function getMatchStatistics(_x12, _x13) {
        return _getMatchStatistics.apply(this, arguments);
      }
      return getMatchStatistics;
    }())
  }, {
    key: "convertToDatabase",
    value: function convertToDatabase(match) {
      cov_q5ev797dr().f[11]++;
      cov_q5ev797dr().s[82]++;
      return {
        user_id: match.metadata.userId,
        opponent_name: match.metadata.opponentName,
        opponent_id: match.metadata.opponentId,
        match_type: match.metadata.matchType,
        match_format: match.metadata.matchFormat,
        court_type: match.metadata.surface,
        court_location: match.metadata.location,
        weather_conditions: match.metadata.weatherConditions ? (cov_q5ev797dr().b[12][0]++, {
          conditions: match.metadata.weatherConditions,
          temperature: match.metadata.temperature
        }) : (cov_q5ev797dr().b[12][1]++, null),
        match_status: match.status,
        start_time: match.metadata.startTime,
        end_time: match.metadata.endTime,
        duration_minutes: match.metadata.durationMinutes,
        final_score: match.score,
        match_notes: '',
        video_url: match.videoUrl,
        video_thumbnail_url: match.videoThumbnailUrl,
        video_duration_seconds: match.videoDurationSeconds,
        video_file_size_bytes: match.videoFileSizeBytes,
        analysis_status: 'pending'
      };
    }
  }, {
    key: "convertFromDatabase",
    value: function convertFromDatabase(dbMatch) {
      var _dbMatch$weather_cond, _dbMatch$weather_cond2;
      cov_q5ev797dr().f[12]++;
      cov_q5ev797dr().s[83]++;
      return {
        id: dbMatch.id,
        metadata: {
          userId: dbMatch.user_id,
          opponentName: dbMatch.opponent_name,
          opponentId: dbMatch.opponent_id,
          matchType: dbMatch.match_type,
          matchFormat: dbMatch.match_format,
          surface: dbMatch.court_type,
          location: dbMatch.court_location,
          weatherConditions: (_dbMatch$weather_cond = dbMatch.weather_conditions) == null ? void 0 : _dbMatch$weather_cond.conditions,
          temperature: (_dbMatch$weather_cond2 = dbMatch.weather_conditions) == null ? void 0 : _dbMatch$weather_cond2.temperature,
          matchDate: dbMatch.created_at.split('T')[0],
          startTime: dbMatch.start_time,
          endTime: dbMatch.end_time,
          durationMinutes: dbMatch.duration_minutes
        },
        score: (cov_q5ev797dr().b[13][0]++, dbMatch.final_score) || (cov_q5ev797dr().b[13][1]++, {
          sets: [],
          finalScore: '',
          result: 'win',
          setsWon: 0,
          setsLost: 0
        }),
        statistics: {
          matchId: dbMatch.id,
          userId: dbMatch.user_id,
          aces: 0,
          doubleFaults: 0,
          firstServesIn: 0,
          firstServesAttempted: 0,
          firstServePointsWon: 0,
          secondServePointsWon: 0,
          firstServeReturnPointsWon: 0,
          secondServeReturnPointsWon: 0,
          breakPointsConverted: 0,
          breakPointsFaced: 0,
          winners: 0,
          unforcedErrors: 0,
          forcedErrors: 0,
          totalPointsWon: 0,
          totalPointsPlayed: 0,
          netPointsAttempted: 0,
          netPointsWon: 0,
          forehandWinners: 0,
          backhandWinners: 0,
          forehandErrors: 0,
          backhandErrors: 0
        },
        videoUrl: dbMatch.video_url,
        videoThumbnailUrl: dbMatch.video_thumbnail_url,
        videoDurationSeconds: dbMatch.video_duration_seconds,
        videoFileSizeBytes: dbMatch.video_file_size_bytes,
        status: dbMatch.match_status,
        createdAt: dbMatch.created_at,
        updatedAt: dbMatch.updated_at
      };
    }
  }]);
}();
export var matchRepository = (cov_q5ev797dr().s[84]++, new MatchRepository());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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