{"version": 3, "names": ["mediaPipeService", "coachingAnalysisService", "performanceMonitor", "VideoProcessingService", "_classCallCheck", "processingQueue", "cov_bxwhcoz4r", "s", "activeJobs", "Map", "jobListeners", "config", "maxConcurrent<PERSON><PERSON>s", "frameExtractionRate", "segmentationThreshold", "minSegmentDuration", "maxSegmentDuration", "qualityThreshold", "enableAutoSegmentation", "enableRealTimeProcessing", "_createClass", "key", "value", "_submitVideoForProcessing", "_asyncToGenerator", "videoUrl", "matchId", "userId", "player<PERSON><PERSON><PERSON><PERSON>", "matchContext", "priority", "arguments", "length", "undefined", "b", "f", "start", "job", "id", "generateJobId", "status", "progress", "segments", "createdAt", "Date", "toISOString", "estimatedDuration", "estimateProcessingDuration", "addToQueue", "processQueue", "end", "error", "console", "Error", "submitVideoForProcessing", "_x", "_x2", "_x3", "_x4", "_x5", "apply", "getJobStatus", "jobId", "get", "find", "_cancelJob", "delete", "notifyJobUpdate", "queueIndex", "findIndex", "j", "splice", "cancelJob", "_x6", "addJobListener", "listener", "set", "removeJobListener", "_processJob", "startedAt", "qualityMetrics", "assessVideoQuality", "videoQuality", "segmentVideo", "segmentAnalyses", "processSegments", "overallAnalysis", "generateOverallAnalysis", "result", "compileResults", "completedAt", "errorMessage", "message", "processJob", "_x7", "_assessVideoQuality", "Promise", "resolve", "setTimeout", "lightingConditions", "cameraStability", "playerVisibility", "_x8", "_segmentVideo", "startTime", "endTime", "type", "description", "importance", "_x9", "_x0", "_processSegments", "_this", "results", "progressPerSegment", "_loop", "i", "segment", "now", "frames", "extractFrames", "movementAnalyses", "processVideoFrames", "segmentProgress", "coachingAnalysis", "generateCoachingAnalysis", "videoSegment", "processingTime", "push", "_x1", "_x10", "_extractFrames", "frameCount", "Math", "ceil", "canvas", "document", "createElement", "width", "height", "_x11", "_x12", "_generateOverallAnalysis", "_segmentAnalyses", "allMovementAnalyses", "flatMap", "sa", "_x13", "_x14", "_x15", "_compileResults", "totalFrames", "reduce", "sum", "totalProcessingTime", "averageProcessingTime", "allAnalyses", "avgConfidence", "a", "confidence", "processingMetrics", "processedFrames", "poseDetectionAccuracy", "_x16", "_x17", "_x18", "_x19", "_estimateProcessingDuration", "videoDurationMinutes", "processingRatio", "_x20", "_this2", "insertIndex", "queuedJob", "getPriorityScore", "_processQueue", "size", "next<PERSON>ob", "shift", "random", "toString", "substr", "updateConfig", "Object", "assign", "getConfig", "getQueueStatus", "queueLength", "totalCapacity", "cleanup", "clear", "videoProcessingService"], "sources": ["VideoProcessingService.ts"], "sourcesContent": ["/**\n * Video Processing Pipeline Service\n * Handles automated video analysis workflow for tennis matches\n */\n\nimport { mediaPipeService, TennisMovementAnalysis } from './MediaPipeService';\nimport { coachingAnalysisService, CoachingAnalysisResult, PlayerProfile, MatchContext } from './CoachingAnalysisService';\nimport { performanceMonitor } from '@/utils/performance';\n\nexport interface VideoSegment {\n  id: string;\n  startTime: number; // seconds\n  endTime: number; // seconds\n  type: 'serve' | 'rally' | 'point' | 'break' | 'full_match';\n  description: string;\n  importance: 'high' | 'medium' | 'low';\n}\n\nexport interface ProcessingJob {\n  id: string;\n  videoUrl: string;\n  matchId: string;\n  userId: string;\n  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';\n  progress: number; // 0-100\n  segments: VideoSegment[];\n  playerProfile: PlayerProfile;\n  matchContext: MatchContext;\n  priority: 'high' | 'medium' | 'low';\n  createdAt: string;\n  startedAt?: string;\n  completedAt?: string;\n  errorMessage?: string;\n  estimatedDuration?: number; // seconds\n}\n\nexport interface ProcessingResult {\n  jobId: string;\n  matchId: string;\n  segments: {\n    segment: VideoSegment;\n    movementAnalyses: TennisMovementAnalysis[];\n    coachingAnalysis: CoachingAnalysisResult;\n    processingTime: number;\n  }[];\n  overallAnalysis: CoachingAnalysisResult;\n  processingMetrics: {\n    totalFrames: number;\n    processedFrames: number;\n    averageProcessingTime: number;\n    poseDetectionAccuracy: number;\n    totalProcessingTime: number;\n  };\n  qualityMetrics: {\n    videoQuality: 'excellent' | 'good' | 'fair' | 'poor';\n    lightingConditions: 'excellent' | 'good' | 'fair' | 'poor';\n    cameraStability: 'excellent' | 'good' | 'fair' | 'poor';\n    playerVisibility: 'excellent' | 'good' | 'fair' | 'poor';\n  };\n}\n\nexport interface ProcessingConfig {\n  maxConcurrentJobs: number;\n  frameExtractionRate: number; // frames per second\n  segmentationThreshold: number;\n  minSegmentDuration: number; // seconds\n  maxSegmentDuration: number; // seconds\n  qualityThreshold: number;\n  enableAutoSegmentation: boolean;\n  enableRealTimeProcessing: boolean;\n}\n\nclass VideoProcessingService {\n  private processingQueue: ProcessingJob[] = [];\n  private activeJobs: Map<string, ProcessingJob> = new Map();\n  private jobListeners: Map<string, (job: ProcessingJob) => void> = new Map();\n  \n  private config: ProcessingConfig = {\n    maxConcurrentJobs: 2,\n    frameExtractionRate: 2, // 2 FPS for analysis\n    segmentationThreshold: 0.7,\n    minSegmentDuration: 5,\n    maxSegmentDuration: 30,\n    qualityThreshold: 0.6,\n    enableAutoSegmentation: true,\n    enableRealTimeProcessing: false,\n  };\n\n  /**\n   * Submit video for processing\n   */\n  async submitVideoForProcessing(\n    videoUrl: string,\n    matchId: string,\n    userId: string,\n    playerProfile: PlayerProfile,\n    matchContext: MatchContext,\n    priority: 'high' | 'medium' | 'low' = 'medium'\n  ): Promise<string> {\n    try {\n      performanceMonitor.start('video_submission');\n\n      const job: ProcessingJob = {\n        id: this.generateJobId(),\n        videoUrl,\n        matchId,\n        userId,\n        status: 'queued',\n        progress: 0,\n        segments: [],\n        playerProfile,\n        matchContext,\n        priority,\n        createdAt: new Date().toISOString(),\n      };\n\n      // Estimate processing duration\n      job.estimatedDuration = await this.estimateProcessingDuration(videoUrl);\n\n      // Add to queue\n      this.addToQueue(job);\n\n      // Start processing if capacity available\n      this.processQueue();\n\n      performanceMonitor.end('video_submission');\n      return job.id;\n    } catch (error) {\n      console.error('Failed to submit video for processing:', error);\n      throw new Error('Video submission failed');\n    }\n  }\n\n  /**\n   * Get job status\n   */\n  getJobStatus(jobId: string): ProcessingJob | null {\n    return this.activeJobs.get(jobId) || \n           this.processingQueue.find(job => job.id === jobId) || \n           null;\n  }\n\n  /**\n   * Cancel processing job\n   */\n  async cancelJob(jobId: string): Promise<boolean> {\n    try {\n      const job = this.activeJobs.get(jobId);\n      if (job) {\n        job.status = 'cancelled';\n        this.activeJobs.delete(jobId);\n        this.notifyJobUpdate(job);\n        return true;\n      }\n\n      const queueIndex = this.processingQueue.findIndex(j => j.id === jobId);\n      if (queueIndex !== -1) {\n        this.processingQueue[queueIndex].status = 'cancelled';\n        this.processingQueue.splice(queueIndex, 1);\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      console.error('Failed to cancel job:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Add job progress listener\n   */\n  addJobListener(jobId: string, listener: (job: ProcessingJob) => void): void {\n    this.jobListeners.set(jobId, listener);\n  }\n\n  /**\n   * Remove job progress listener\n   */\n  removeJobListener(jobId: string): void {\n    this.jobListeners.delete(jobId);\n  }\n\n  /**\n   * Process video analysis job\n   */\n  private async processJob(job: ProcessingJob): Promise<ProcessingResult> {\n    try {\n      performanceMonitor.start(`video_processing_${job.id}`);\n\n      job.status = 'processing';\n      job.startedAt = new Date().toISOString();\n      job.progress = 0;\n      this.notifyJobUpdate(job);\n\n      // Step 1: Video quality assessment\n      job.progress = 10;\n      this.notifyJobUpdate(job);\n      const qualityMetrics = await this.assessVideoQuality(job.videoUrl);\n\n      if (qualityMetrics.videoQuality === 'poor') {\n        throw new Error('Video quality too low for reliable analysis');\n      }\n\n      // Step 2: Video segmentation\n      job.progress = 20;\n      this.notifyJobUpdate(job);\n      const segments = await this.segmentVideo(job.videoUrl, job.matchContext);\n      job.segments = segments;\n\n      // Step 3: Frame extraction and pose detection\n      job.progress = 30;\n      this.notifyJobUpdate(job);\n      const segmentAnalyses = await this.processSegments(job, segments);\n\n      // Step 4: Generate coaching analysis\n      job.progress = 80;\n      this.notifyJobUpdate(job);\n      const overallAnalysis = await this.generateOverallAnalysis(\n        segmentAnalyses,\n        job.playerProfile,\n        job.matchContext\n      );\n\n      // Step 5: Compile results\n      job.progress = 95;\n      this.notifyJobUpdate(job);\n      const result = await this.compileResults(\n        job,\n        segmentAnalyses,\n        overallAnalysis,\n        qualityMetrics\n      );\n\n      // Complete job\n      job.status = 'completed';\n      job.progress = 100;\n      job.completedAt = new Date().toISOString();\n      this.notifyJobUpdate(job);\n\n      performanceMonitor.end(`video_processing_${job.id}`);\n      return result;\n    } catch (error) {\n      console.error(`Job ${job.id} failed:`, error);\n      job.status = 'failed';\n      job.errorMessage = error instanceof Error ? error.message : 'Unknown error';\n      this.notifyJobUpdate(job);\n      throw error;\n    } finally {\n      this.activeJobs.delete(job.id);\n      this.processQueue(); // Process next job in queue\n    }\n  }\n\n  /**\n   * Assess video quality for analysis\n   */\n  private async assessVideoQuality(videoUrl: string): Promise<ProcessingResult['qualityMetrics']> {\n    // Simulate video quality assessment\n    await new Promise(resolve => setTimeout(resolve, 500));\n\n    // In a real implementation, this would analyze:\n    // - Resolution and bitrate\n    // - Lighting conditions\n    // - Camera stability (motion blur)\n    // - Player visibility and occlusion\n\n    return {\n      videoQuality: 'good',\n      lightingConditions: 'good',\n      cameraStability: 'excellent',\n      playerVisibility: 'good',\n    };\n  }\n\n  /**\n   * Segment video into analysis chunks\n   */\n  private async segmentVideo(\n    videoUrl: string,\n    matchContext: MatchContext\n  ): Promise<VideoSegment[]> {\n    // Simulate video segmentation\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // In a real implementation, this would:\n    // - Detect scene changes\n    // - Identify tennis-specific events (serves, rallies)\n    // - Create optimal segments for analysis\n\n    const segments: VideoSegment[] = [\n      {\n        id: 'segment_1',\n        startTime: 0,\n        endTime: 15,\n        type: 'serve',\n        description: 'Service games analysis',\n        importance: 'high',\n      },\n      {\n        id: 'segment_2',\n        startTime: 15,\n        endTime: 45,\n        type: 'rally',\n        description: 'Baseline rally analysis',\n        importance: 'high',\n      },\n      {\n        id: 'segment_3',\n        startTime: 45,\n        endTime: 60,\n        type: 'point',\n        description: 'Net play and volleys',\n        importance: 'medium',\n      },\n    ];\n\n    return segments;\n  }\n\n  /**\n   * Process individual video segments\n   */\n  private async processSegments(\n    job: ProcessingJob,\n    segments: VideoSegment[]\n  ): Promise<Array<{\n    segment: VideoSegment;\n    movementAnalyses: TennisMovementAnalysis[];\n    coachingAnalysis: CoachingAnalysisResult;\n    processingTime: number;\n  }>> {\n    const results = [];\n    const progressPerSegment = 50 / segments.length; // 50% of total progress for segment processing\n\n    for (let i = 0; i < segments.length; i++) {\n      const segment = segments[i];\n      const startTime = Date.now();\n\n      // Extract frames from segment\n      const frames = await this.extractFrames(job.videoUrl, segment);\n\n      // Perform pose detection on frames\n      const movementAnalyses = await mediaPipeService.processVideoFrames(\n        frames,\n        (progress) => {\n          const segmentProgress = (i * progressPerSegment) + (progress * progressPerSegment);\n          job.progress = 30 + segmentProgress;\n          this.notifyJobUpdate(job);\n        }\n      );\n\n      // Generate coaching analysis for segment\n      const coachingAnalysis = await coachingAnalysisService.generateCoachingAnalysis({\n        movementAnalyses,\n        playerProfile: job.playerProfile,\n        matchContext: job.matchContext,\n        videoSegment: {\n          startTime: segment.startTime,\n          endTime: segment.endTime,\n          description: segment.description,\n        },\n      });\n\n      const processingTime = Date.now() - startTime;\n\n      results.push({\n        segment,\n        movementAnalyses,\n        coachingAnalysis,\n        processingTime,\n      });\n    }\n\n    return results;\n  }\n\n  /**\n   * Extract frames from video segment\n   */\n  private async extractFrames(\n    videoUrl: string,\n    segment: VideoSegment\n  ): Promise<HTMLCanvasElement[]> {\n    // Simulate frame extraction\n    await new Promise(resolve => setTimeout(resolve, 200));\n\n    // In a real implementation, this would:\n    // - Load video at specified time range\n    // - Extract frames at configured rate\n    // - Return canvas elements with frame data\n\n    const frameCount = Math.ceil((segment.endTime - segment.startTime) * this.config.frameExtractionRate);\n    const frames: HTMLCanvasElement[] = [];\n\n    for (let i = 0; i < frameCount; i++) {\n      const canvas = document.createElement('canvas');\n      canvas.width = 640;\n      canvas.height = 480;\n      frames.push(canvas);\n    }\n\n    return frames;\n  }\n\n  /**\n   * Generate overall analysis from all segments\n   */\n  private async generateOverallAnalysis(\n    segmentAnalyses: Array<{\n      segment: VideoSegment;\n      movementAnalyses: TennisMovementAnalysis[];\n      coachingAnalysis: CoachingAnalysisResult;\n    }>,\n    playerProfile: PlayerProfile,\n    matchContext: MatchContext\n  ): Promise<CoachingAnalysisResult> {\n    // Combine all movement analyses\n    const allMovementAnalyses = segmentAnalyses.flatMap(sa => sa.movementAnalyses);\n\n    // Generate comprehensive coaching analysis\n    return await coachingAnalysisService.generateCoachingAnalysis({\n      movementAnalyses: allMovementAnalyses,\n      playerProfile,\n      matchContext,\n      videoSegment: {\n        startTime: 0,\n        endTime: segmentAnalyses[segmentAnalyses.length - 1]?.segment.endTime || 0,\n        description: 'Complete match analysis',\n      },\n    });\n  }\n\n  /**\n   * Compile final processing results\n   */\n  private async compileResults(\n    job: ProcessingJob,\n    segmentAnalyses: Array<{\n      segment: VideoSegment;\n      movementAnalyses: TennisMovementAnalysis[];\n      coachingAnalysis: CoachingAnalysisResult;\n      processingTime: number;\n    }>,\n    overallAnalysis: CoachingAnalysisResult,\n    qualityMetrics: ProcessingResult['qualityMetrics']\n  ): Promise<ProcessingResult> {\n    const totalFrames = segmentAnalyses.reduce((sum, sa) => sum + sa.movementAnalyses.length, 0);\n    const totalProcessingTime = segmentAnalyses.reduce((sum, sa) => sum + sa.processingTime, 0);\n    const averageProcessingTime = totalProcessingTime / segmentAnalyses.length;\n\n    // Calculate pose detection accuracy\n    const allAnalyses = segmentAnalyses.flatMap(sa => sa.movementAnalyses);\n    const avgConfidence = allAnalyses.reduce((sum, a) => sum + a.confidence, 0) / allAnalyses.length;\n\n    return {\n      jobId: job.id,\n      matchId: job.matchId,\n      segments: segmentAnalyses,\n      overallAnalysis,\n      processingMetrics: {\n        totalFrames,\n        processedFrames: totalFrames,\n        averageProcessingTime,\n        poseDetectionAccuracy: avgConfidence,\n        totalProcessingTime,\n      },\n      qualityMetrics,\n    };\n  }\n\n  /**\n   * Estimate processing duration\n   */\n  private async estimateProcessingDuration(videoUrl: string): Promise<number> {\n    // Simulate video duration detection\n    await new Promise(resolve => setTimeout(resolve, 100));\n\n    // In a real implementation, this would:\n    // - Get video metadata (duration, resolution, etc.)\n    // - Estimate based on video length and processing complexity\n    // - Consider current system load\n\n    const videoDurationMinutes = 5; // Assume 5-minute video\n    const processingRatio = 0.3; // 30% of video duration for processing\n    \n    return videoDurationMinutes * 60 * processingRatio;\n  }\n\n  /**\n   * Add job to processing queue\n   */\n  private addToQueue(job: ProcessingJob): void {\n    // Insert job based on priority\n    const insertIndex = this.processingQueue.findIndex(\n      queuedJob => this.getPriorityScore(queuedJob.priority) < this.getPriorityScore(job.priority)\n    );\n\n    if (insertIndex === -1) {\n      this.processingQueue.push(job);\n    } else {\n      this.processingQueue.splice(insertIndex, 0, job);\n    }\n  }\n\n  /**\n   * Process jobs from queue\n   */\n  private async processQueue(): Promise<void> {\n    if (this.activeJobs.size >= this.config.maxConcurrentJobs) {\n      return; // At capacity\n    }\n\n    const nextJob = this.processingQueue.shift();\n    if (!nextJob) {\n      return; // No jobs in queue\n    }\n\n    if (nextJob.status === 'cancelled') {\n      this.processQueue(); // Skip cancelled job and process next\n      return;\n    }\n\n    this.activeJobs.set(nextJob.id, nextJob);\n\n    try {\n      await this.processJob(nextJob);\n    } catch (error) {\n      console.error(`Failed to process job ${nextJob.id}:`, error);\n    }\n  }\n\n  /**\n   * Notify job update to listeners\n   */\n  private notifyJobUpdate(job: ProcessingJob): void {\n    const listener = this.jobListeners.get(job.id);\n    if (listener) {\n      listener(job);\n    }\n  }\n\n  /**\n   * Get priority score for sorting\n   */\n  private getPriorityScore(priority: 'high' | 'medium' | 'low'): number {\n    switch (priority) {\n      case 'high': return 3;\n      case 'medium': return 2;\n      case 'low': return 1;\n    }\n  }\n\n  /**\n   * Generate unique job ID\n   */\n  private generateJobId(): string {\n    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  /**\n   * Update processing configuration\n   */\n  updateConfig(config: Partial<ProcessingConfig>): void {\n    this.config = { ...this.config, ...config };\n  }\n\n  /**\n   * Get current configuration\n   */\n  getConfig(): ProcessingConfig {\n    return { ...this.config };\n  }\n\n  /**\n   * Get queue status\n   */\n  getQueueStatus(): {\n    queueLength: number;\n    activeJobs: number;\n    totalCapacity: number;\n  } {\n    return {\n      queueLength: this.processingQueue.length,\n      activeJobs: this.activeJobs.size,\n      totalCapacity: this.config.maxConcurrentJobs,\n    };\n  }\n\n  /**\n   * Cleanup resources\n   */\n  cleanup(): void {\n    this.processingQueue.length = 0;\n    this.activeJobs.clear();\n    this.jobListeners.clear();\n  }\n}\n\n// Export singleton instance\nexport const videoProcessingService = new VideoProcessingService();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,SAASA,gBAAgB;AACzB,SAASC,uBAAuB;AAChC,SAASC,kBAAkB;AAA8B,IAiEnDC,sBAAsB;EAAA,SAAAA,uBAAA;IAAAC,eAAA,OAAAD,sBAAA;IAAA,KAClBE,eAAe,IAAAC,aAAA,GAAAC,CAAA,OAAoB,EAAE;IAAA,KACrCC,UAAU,IAAAF,aAAA,GAAAC,CAAA,OAA+B,IAAIE,GAAG,CAAC,CAAC;IAAA,KAClDC,YAAY,IAAAJ,aAAA,GAAAC,CAAA,OAA8C,IAAIE,GAAG,CAAC,CAAC;IAAA,KAEnEE,MAAM,IAAAL,aAAA,GAAAC,CAAA,OAAqB;MACjCK,iBAAiB,EAAE,CAAC;MACpBC,mBAAmB,EAAE,CAAC;MACtBC,qBAAqB,EAAE,GAAG;MAC1BC,kBAAkB,EAAE,CAAC;MACrBC,kBAAkB,EAAE,EAAE;MACtBC,gBAAgB,EAAE,GAAG;MACrBC,sBAAsB,EAAE,IAAI;MAC5BC,wBAAwB,EAAE;IAC5B,CAAC;EAAA;EAAA,OAAAC,YAAA,CAAAjB,sBAAA;IAAAkB,GAAA;IAAAC,KAAA;MAAA,IAAAC,yBAAA,GAAAC,iBAAA,CAKD,WACEC,QAAgB,EAChBC,OAAe,EACfC,MAAc,EACdC,aAA4B,EAC5BC,YAA0B,EAET;QAAA,IADjBC,QAAmC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAzB,aAAA,GAAA4B,CAAA,UAAG,QAAQ;QAAA5B,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAC,CAAA;QAE9C,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACFL,kBAAkB,CAACkC,KAAK,CAAC,kBAAkB,CAAC;UAE5C,IAAMC,GAAkB,IAAA/B,aAAA,GAAAC,CAAA,OAAG;YACzB+B,EAAE,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC;YACxBd,QAAQ,EAARA,QAAQ;YACRC,OAAO,EAAPA,OAAO;YACPC,MAAM,EAANA,MAAM;YACNa,MAAM,EAAE,QAAQ;YAChBC,QAAQ,EAAE,CAAC;YACXC,QAAQ,EAAE,EAAE;YACZd,aAAa,EAAbA,aAAa;YACbC,YAAY,EAAZA,YAAY;YACZC,QAAQ,EAARA,QAAQ;YACRa,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACpC,CAAC;UAACvC,aAAA,GAAAC,CAAA;UAGF8B,GAAG,CAACS,iBAAiB,SAAS,IAAI,CAACC,0BAA0B,CAACtB,QAAQ,CAAC;UAACnB,aAAA,GAAAC,CAAA;UAGxE,IAAI,CAACyC,UAAU,CAACX,GAAG,CAAC;UAAC/B,aAAA,GAAAC,CAAA;UAGrB,IAAI,CAAC0C,YAAY,CAAC,CAAC;UAAC3C,aAAA,GAAAC,CAAA;UAEpBL,kBAAkB,CAACgD,GAAG,CAAC,kBAAkB,CAAC;UAAC5C,aAAA,GAAAC,CAAA;UAC3C,OAAO8B,GAAG,CAACC,EAAE;QACf,CAAC,CAAC,OAAOa,KAAK,EAAE;UAAA7C,aAAA,GAAAC,CAAA;UACd6C,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;UAAC7C,aAAA,GAAAC,CAAA;UAC/D,MAAM,IAAI8C,KAAK,CAAC,yBAAyB,CAAC;QAC5C;MACF,CAAC;MAAA,SAxCKC,wBAAwBA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAApC,yBAAA,CAAAqC,KAAA,OAAA7B,SAAA;MAAA;MAAA,OAAxBuB,wBAAwB;IAAA;EAAA;IAAAjC,GAAA;IAAAC,KAAA,EA6C9B,SAAAuC,YAAYA,CAACC,KAAa,EAAwB;MAAAxD,aAAA,GAAA6B,CAAA;MAAA7B,aAAA,GAAAC,CAAA;MAChD,OAAO,CAAAD,aAAA,GAAA4B,CAAA,cAAI,CAAC1B,UAAU,CAACuD,GAAG,CAACD,KAAK,CAAC,MAAAxD,aAAA,GAAA4B,CAAA,UAC1B,IAAI,CAAC7B,eAAe,CAAC2D,IAAI,CAAC,UAAA3B,GAAG,EAAI;QAAA/B,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAC,CAAA;QAAA,OAAA8B,GAAG,CAACC,EAAE,KAAKwB,KAAK;MAAD,CAAC,CAAC,MAAAxD,aAAA,GAAA4B,CAAA,UAClD,IAAI;IACb;EAAC;IAAAb,GAAA;IAAAC,KAAA;MAAA,IAAA2C,UAAA,GAAAzC,iBAAA,CAKD,WAAgBsC,KAAa,EAAoB;QAAAxD,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAC,CAAA;QAC/C,IAAI;UACF,IAAM8B,GAAG,IAAA/B,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACC,UAAU,CAACuD,GAAG,CAACD,KAAK,CAAC;UAACxD,aAAA,GAAAC,CAAA;UACvC,IAAI8B,GAAG,EAAE;YAAA/B,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YACP8B,GAAG,CAACG,MAAM,GAAG,WAAW;YAAClC,aAAA,GAAAC,CAAA;YACzB,IAAI,CAACC,UAAU,CAAC0D,MAAM,CAACJ,KAAK,CAAC;YAACxD,aAAA,GAAAC,CAAA;YAC9B,IAAI,CAAC4D,eAAe,CAAC9B,GAAG,CAAC;YAAC/B,aAAA,GAAAC,CAAA;YAC1B,OAAO,IAAI;UACb,CAAC;YAAAD,aAAA,GAAA4B,CAAA;UAAA;UAED,IAAMkC,UAAU,IAAA9D,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,eAAe,CAACgE,SAAS,CAAC,UAAAC,CAAC,EAAI;YAAAhE,aAAA,GAAA6B,CAAA;YAAA7B,aAAA,GAAAC,CAAA;YAAA,OAAA+D,CAAC,CAAChC,EAAE,KAAKwB,KAAK;UAAD,CAAC,CAAC;UAACxD,aAAA,GAAAC,CAAA;UACvE,IAAI6D,UAAU,KAAK,CAAC,CAAC,EAAE;YAAA9D,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YACrB,IAAI,CAACF,eAAe,CAAC+D,UAAU,CAAC,CAAC5B,MAAM,GAAG,WAAW;YAAClC,aAAA,GAAAC,CAAA;YACtD,IAAI,CAACF,eAAe,CAACkE,MAAM,CAACH,UAAU,EAAE,CAAC,CAAC;YAAC9D,aAAA,GAAAC,CAAA;YAC3C,OAAO,IAAI;UACb,CAAC;YAAAD,aAAA,GAAA4B,CAAA;UAAA;UAAA5B,aAAA,GAAAC,CAAA;UAED,OAAO,KAAK;QACd,CAAC,CAAC,OAAO4C,KAAK,EAAE;UAAA7C,aAAA,GAAAC,CAAA;UACd6C,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAAC7C,aAAA,GAAAC,CAAA;UAC9C,OAAO,KAAK;QACd;MACF,CAAC;MAAA,SAtBKiE,SAASA,CAAAC,GAAA;QAAA,OAAAR,UAAA,CAAAL,KAAA,OAAA7B,SAAA;MAAA;MAAA,OAATyC,SAAS;IAAA;EAAA;IAAAnD,GAAA;IAAAC,KAAA,EA2Bf,SAAAoD,cAAcA,CAACZ,KAAa,EAAEa,QAAsC,EAAQ;MAAArE,aAAA,GAAA6B,CAAA;MAAA7B,aAAA,GAAAC,CAAA;MAC1E,IAAI,CAACG,YAAY,CAACkE,GAAG,CAACd,KAAK,EAAEa,QAAQ,CAAC;IACxC;EAAC;IAAAtD,GAAA;IAAAC,KAAA,EAKD,SAAAuD,iBAAiBA,CAACf,KAAa,EAAQ;MAAAxD,aAAA,GAAA6B,CAAA;MAAA7B,aAAA,GAAAC,CAAA;MACrC,IAAI,CAACG,YAAY,CAACwD,MAAM,CAACJ,KAAK,CAAC;IACjC;EAAC;IAAAzC,GAAA;IAAAC,KAAA;MAAA,IAAAwD,WAAA,GAAAtD,iBAAA,CAKD,WAAyBa,GAAkB,EAA6B;QAAA/B,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAC,CAAA;QACtE,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACFL,kBAAkB,CAACkC,KAAK,CAAC,oBAAoBC,GAAG,CAACC,EAAE,EAAE,CAAC;UAAChC,aAAA,GAAAC,CAAA;UAEvD8B,GAAG,CAACG,MAAM,GAAG,YAAY;UAAClC,aAAA,GAAAC,CAAA;UAC1B8B,GAAG,CAAC0C,SAAS,GAAG,IAAInC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAACvC,aAAA,GAAAC,CAAA;UACzC8B,GAAG,CAACI,QAAQ,GAAG,CAAC;UAACnC,aAAA,GAAAC,CAAA;UACjB,IAAI,CAAC4D,eAAe,CAAC9B,GAAG,CAAC;UAAC/B,aAAA,GAAAC,CAAA;UAG1B8B,GAAG,CAACI,QAAQ,GAAG,EAAE;UAACnC,aAAA,GAAAC,CAAA;UAClB,IAAI,CAAC4D,eAAe,CAAC9B,GAAG,CAAC;UACzB,IAAM2C,cAAc,IAAA1E,aAAA,GAAAC,CAAA,cAAS,IAAI,CAAC0E,kBAAkB,CAAC5C,GAAG,CAACZ,QAAQ,CAAC;UAACnB,aAAA,GAAAC,CAAA;UAEnE,IAAIyE,cAAc,CAACE,YAAY,KAAK,MAAM,EAAE;YAAA5E,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAC1C,MAAM,IAAI8C,KAAK,CAAC,6CAA6C,CAAC;UAChE,CAAC;YAAA/C,aAAA,GAAA4B,CAAA;UAAA;UAAA5B,aAAA,GAAAC,CAAA;UAGD8B,GAAG,CAACI,QAAQ,GAAG,EAAE;UAACnC,aAAA,GAAAC,CAAA;UAClB,IAAI,CAAC4D,eAAe,CAAC9B,GAAG,CAAC;UACzB,IAAMK,QAAQ,IAAApC,aAAA,GAAAC,CAAA,cAAS,IAAI,CAAC4E,YAAY,CAAC9C,GAAG,CAACZ,QAAQ,EAAEY,GAAG,CAACR,YAAY,CAAC;UAACvB,aAAA,GAAAC,CAAA;UACzE8B,GAAG,CAACK,QAAQ,GAAGA,QAAQ;UAACpC,aAAA,GAAAC,CAAA;UAGxB8B,GAAG,CAACI,QAAQ,GAAG,EAAE;UAACnC,aAAA,GAAAC,CAAA;UAClB,IAAI,CAAC4D,eAAe,CAAC9B,GAAG,CAAC;UACzB,IAAM+C,eAAe,IAAA9E,aAAA,GAAAC,CAAA,cAAS,IAAI,CAAC8E,eAAe,CAAChD,GAAG,EAAEK,QAAQ,CAAC;UAACpC,aAAA,GAAAC,CAAA;UAGlE8B,GAAG,CAACI,QAAQ,GAAG,EAAE;UAACnC,aAAA,GAAAC,CAAA;UAClB,IAAI,CAAC4D,eAAe,CAAC9B,GAAG,CAAC;UACzB,IAAMiD,eAAe,IAAAhF,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACgF,uBAAuB,CACxDH,eAAe,EACf/C,GAAG,CAACT,aAAa,EACjBS,GAAG,CAACR,YACN,CAAC;UAACvB,aAAA,GAAAC,CAAA;UAGF8B,GAAG,CAACI,QAAQ,GAAG,EAAE;UAACnC,aAAA,GAAAC,CAAA;UAClB,IAAI,CAAC4D,eAAe,CAAC9B,GAAG,CAAC;UACzB,IAAMmD,MAAM,IAAAlF,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACkF,cAAc,CACtCpD,GAAG,EACH+C,eAAe,EACfE,eAAe,EACfN,cACF,CAAC;UAAC1E,aAAA,GAAAC,CAAA;UAGF8B,GAAG,CAACG,MAAM,GAAG,WAAW;UAAClC,aAAA,GAAAC,CAAA;UACzB8B,GAAG,CAACI,QAAQ,GAAG,GAAG;UAACnC,aAAA,GAAAC,CAAA;UACnB8B,GAAG,CAACqD,WAAW,GAAG,IAAI9C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAACvC,aAAA,GAAAC,CAAA;UAC3C,IAAI,CAAC4D,eAAe,CAAC9B,GAAG,CAAC;UAAC/B,aAAA,GAAAC,CAAA;UAE1BL,kBAAkB,CAACgD,GAAG,CAAC,oBAAoBb,GAAG,CAACC,EAAE,EAAE,CAAC;UAAChC,aAAA,GAAAC,CAAA;UACrD,OAAOiF,MAAM;QACf,CAAC,CAAC,OAAOrC,KAAK,EAAE;UAAA7C,aAAA,GAAAC,CAAA;UACd6C,OAAO,CAACD,KAAK,CAAC,OAAOd,GAAG,CAACC,EAAE,UAAU,EAAEa,KAAK,CAAC;UAAC7C,aAAA,GAAAC,CAAA;UAC9C8B,GAAG,CAACG,MAAM,GAAG,QAAQ;UAAClC,aAAA,GAAAC,CAAA;UACtB8B,GAAG,CAACsD,YAAY,GAAGxC,KAAK,YAAYE,KAAK,IAAA/C,aAAA,GAAA4B,CAAA,UAAGiB,KAAK,CAACyC,OAAO,KAAAtF,aAAA,GAAA4B,CAAA,UAAG,eAAe;UAAC5B,aAAA,GAAAC,CAAA;UAC5E,IAAI,CAAC4D,eAAe,CAAC9B,GAAG,CAAC;UAAC/B,aAAA,GAAAC,CAAA;UAC1B,MAAM4C,KAAK;QACb,CAAC,SAAS;UAAA7C,aAAA,GAAAC,CAAA;UACR,IAAI,CAACC,UAAU,CAAC0D,MAAM,CAAC7B,GAAG,CAACC,EAAE,CAAC;UAAChC,aAAA,GAAAC,CAAA;UAC/B,IAAI,CAAC0C,YAAY,CAAC,CAAC;QACrB;MACF,CAAC;MAAA,SAlEa4C,UAAUA,CAAAC,GAAA;QAAA,OAAAhB,WAAA,CAAAlB,KAAA,OAAA7B,SAAA;MAAA;MAAA,OAAV8D,UAAU;IAAA;EAAA;IAAAxE,GAAA;IAAAC,KAAA;MAAA,IAAAyE,mBAAA,GAAAvE,iBAAA,CAuExB,WAAiCC,QAAgB,EAA+C;QAAAnB,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAC,CAAA;QAE9F,MAAM,IAAIyF,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAA3F,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAC,CAAA;UAAA,OAAA2F,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC;QAAD,CAAC,CAAC;QAAC3F,aAAA,GAAAC,CAAA;QAQvD,OAAO;UACL2E,YAAY,EAAE,MAAM;UACpBiB,kBAAkB,EAAE,MAAM;UAC1BC,eAAe,EAAE,WAAW;UAC5BC,gBAAgB,EAAE;QACpB,CAAC;MACH,CAAC;MAAA,SAhBapB,kBAAkBA,CAAAqB,GAAA;QAAA,OAAAP,mBAAA,CAAAnC,KAAA,OAAA7B,SAAA;MAAA;MAAA,OAAlBkD,kBAAkB;IAAA;EAAA;IAAA5D,GAAA;IAAAC,KAAA;MAAA,IAAAiF,aAAA,GAAA/E,iBAAA,CAqBhC,WACEC,QAAgB,EAChBI,YAA0B,EACD;QAAAvB,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAC,CAAA;QAEzB,MAAM,IAAIyF,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAA3F,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAC,CAAA;UAAA,OAAA2F,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;QAAD,CAAC,CAAC;QAOvD,IAAMvD,QAAwB,IAAApC,aAAA,GAAAC,CAAA,QAAG,CAC/B;UACE+B,EAAE,EAAE,WAAW;UACfkE,SAAS,EAAE,CAAC;UACZC,OAAO,EAAE,EAAE;UACXC,IAAI,EAAE,OAAO;UACbC,WAAW,EAAE,wBAAwB;UACrCC,UAAU,EAAE;QACd,CAAC,EACD;UACEtE,EAAE,EAAE,WAAW;UACfkE,SAAS,EAAE,EAAE;UACbC,OAAO,EAAE,EAAE;UACXC,IAAI,EAAE,OAAO;UACbC,WAAW,EAAE,yBAAyB;UACtCC,UAAU,EAAE;QACd,CAAC,EACD;UACEtE,EAAE,EAAE,WAAW;UACfkE,SAAS,EAAE,EAAE;UACbC,OAAO,EAAE,EAAE;UACXC,IAAI,EAAE,OAAO;UACbC,WAAW,EAAE,sBAAsB;UACnCC,UAAU,EAAE;QACd,CAAC,CACF;QAACtG,aAAA,GAAAC,CAAA;QAEF,OAAOmC,QAAQ;MACjB,CAAC;MAAA,SAxCayC,YAAYA,CAAA0B,GAAA,EAAAC,GAAA;QAAA,OAAAP,aAAA,CAAA3C,KAAA,OAAA7B,SAAA;MAAA;MAAA,OAAZoD,YAAY;IAAA;EAAA;IAAA9D,GAAA;IAAAC,KAAA;MAAA,IAAAyF,gBAAA,GAAAvF,iBAAA,CA6C1B,WACEa,GAAkB,EAClBK,QAAwB,EAMtB;QAAA,IAAAsE,KAAA;QAAA1G,aAAA,GAAA6B,CAAA;QACF,IAAM8E,OAAO,IAAA3G,aAAA,GAAAC,CAAA,QAAG,EAAE;QAClB,IAAM2G,kBAAkB,IAAA5G,aAAA,GAAAC,CAAA,QAAG,EAAE,GAAGmC,QAAQ,CAACV,MAAM;QAAC1B,aAAA,GAAAC,CAAA;QAAA,IAAA4G,KAAA,aAAAA,MAAAC,CAAA,EAEN;UACxC,IAAMC,OAAO,IAAA/G,aAAA,GAAAC,CAAA,QAAGmC,QAAQ,CAAC0E,CAAC,CAAC;UAC3B,IAAMZ,SAAS,IAAAlG,aAAA,GAAAC,CAAA,QAAGqC,IAAI,CAAC0E,GAAG,CAAC,CAAC;UAG5B,IAAMC,MAAM,IAAAjH,aAAA,GAAAC,CAAA,cAASyG,KAAI,CAACQ,aAAa,CAACnF,GAAG,CAACZ,QAAQ,EAAE4F,OAAO,CAAC;UAG9D,IAAMI,gBAAgB,IAAAnH,aAAA,GAAAC,CAAA,cAASP,gBAAgB,CAAC0H,kBAAkB,CAChEH,MAAM,EACN,UAAC9E,QAAQ,EAAK;YAAAnC,aAAA,GAAA6B,CAAA;YACZ,IAAMwF,eAAe,IAAArH,aAAA,GAAAC,CAAA,QAAI6G,CAAC,GAAGF,kBAAkB,GAAKzE,QAAQ,GAAGyE,kBAAmB;YAAC5G,aAAA,GAAAC,CAAA;YACnF8B,GAAG,CAACI,QAAQ,GAAG,EAAE,GAAGkF,eAAe;YAACrH,aAAA,GAAAC,CAAA;YACpCyG,KAAI,CAAC7C,eAAe,CAAC9B,GAAG,CAAC;UAC3B,CACF,CAAC;UAGD,IAAMuF,gBAAgB,IAAAtH,aAAA,GAAAC,CAAA,cAASN,uBAAuB,CAAC4H,wBAAwB,CAAC;YAC9EJ,gBAAgB,EAAhBA,gBAAgB;YAChB7F,aAAa,EAAES,GAAG,CAACT,aAAa;YAChCC,YAAY,EAAEQ,GAAG,CAACR,YAAY;YAC9BiG,YAAY,EAAE;cACZtB,SAAS,EAAEa,OAAO,CAACb,SAAS;cAC5BC,OAAO,EAAEY,OAAO,CAACZ,OAAO;cACxBE,WAAW,EAAEU,OAAO,CAACV;YACvB;UACF,CAAC,CAAC;UAEF,IAAMoB,cAAc,IAAAzH,aAAA,GAAAC,CAAA,QAAGqC,IAAI,CAAC0E,GAAG,CAAC,CAAC,GAAGd,SAAS;UAAClG,aAAA,GAAAC,CAAA;UAE9C0G,OAAO,CAACe,IAAI,CAAC;YACXX,OAAO,EAAPA,OAAO;YACPI,gBAAgB,EAAhBA,gBAAgB;YAChBG,gBAAgB,EAAhBA,gBAAgB;YAChBG,cAAc,EAAdA;UACF,CAAC,CAAC;QACJ,CAAC;QArCD,KAAK,IAAIX,CAAC,IAAA9G,aAAA,GAAAC,CAAA,QAAG,CAAC,GAAE6G,CAAC,GAAG1E,QAAQ,CAACV,MAAM,EAAEoF,CAAC,EAAE;UAAA,OAAAD,KAAA,CAAAC,CAAA;QAAA;QAqCvC9G,aAAA,GAAAC,CAAA;QAED,OAAO0G,OAAO;MAChB,CAAC;MAAA,SApDa5B,eAAeA,CAAA4C,GAAA,EAAAC,IAAA;QAAA,OAAAnB,gBAAA,CAAAnD,KAAA,OAAA7B,SAAA;MAAA;MAAA,OAAfsD,eAAe;IAAA;EAAA;IAAAhE,GAAA;IAAAC,KAAA;MAAA,IAAA6G,cAAA,GAAA3G,iBAAA,CAyD7B,WACEC,QAAgB,EAChB4F,OAAqB,EACS;QAAA/G,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAC,CAAA;QAE9B,MAAM,IAAIyF,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAA3F,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAC,CAAA;UAAA,OAAA2F,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC;QAAD,CAAC,CAAC;QAOtD,IAAMmC,UAAU,IAAA9H,aAAA,GAAAC,CAAA,QAAG8H,IAAI,CAACC,IAAI,CAAC,CAACjB,OAAO,CAACZ,OAAO,GAAGY,OAAO,CAACb,SAAS,IAAI,IAAI,CAAC7F,MAAM,CAACE,mBAAmB,CAAC;QACrG,IAAM0G,MAA2B,IAAAjH,aAAA,GAAAC,CAAA,QAAG,EAAE;QAACD,aAAA,GAAAC,CAAA;QAEvC,KAAK,IAAI6G,CAAC,IAAA9G,aAAA,GAAAC,CAAA,QAAG,CAAC,GAAE6G,CAAC,GAAGgB,UAAU,EAAEhB,CAAC,EAAE,EAAE;UACnC,IAAMmB,MAAM,IAAAjI,aAAA,GAAAC,CAAA,QAAGiI,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAACnI,aAAA,GAAAC,CAAA;UAChDgI,MAAM,CAACG,KAAK,GAAG,GAAG;UAACpI,aAAA,GAAAC,CAAA;UACnBgI,MAAM,CAACI,MAAM,GAAG,GAAG;UAACrI,aAAA,GAAAC,CAAA;UACpBgH,MAAM,CAACS,IAAI,CAACO,MAAM,CAAC;QACrB;QAACjI,aAAA,GAAAC,CAAA;QAED,OAAOgH,MAAM;MACf,CAAC;MAAA,SAvBaC,aAAaA,CAAAoB,IAAA,EAAAC,IAAA;QAAA,OAAAV,cAAA,CAAAvE,KAAA,OAAA7B,SAAA;MAAA;MAAA,OAAbyF,aAAa;IAAA;EAAA;IAAAnG,GAAA;IAAAC,KAAA;MAAA,IAAAwH,wBAAA,GAAAtH,iBAAA,CA4B3B,WACE4D,eAIE,EACFxD,aAA4B,EAC5BC,YAA0B,EACO;QAAA,IAAAkH,gBAAA;QAAAzI,aAAA,GAAA6B,CAAA;QAEjC,IAAM6G,mBAAmB,IAAA1I,aAAA,GAAAC,CAAA,SAAG6E,eAAe,CAAC6D,OAAO,CAAC,UAAAC,EAAE,EAAI;UAAA5I,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAC,CAAA;UAAA,OAAA2I,EAAE,CAACzB,gBAAgB;QAAD,CAAC,CAAC;QAACnH,aAAA,GAAAC,CAAA;QAG/E,aAAaN,uBAAuB,CAAC4H,wBAAwB,CAAC;UAC5DJ,gBAAgB,EAAEuB,mBAAmB;UACrCpH,aAAa,EAAbA,aAAa;UACbC,YAAY,EAAZA,YAAY;UACZiG,YAAY,EAAE;YACZtB,SAAS,EAAE,CAAC;YACZC,OAAO,EAAE,CAAAnG,aAAA,GAAA4B,CAAA,WAAA6G,gBAAA,GAAA3D,eAAe,CAACA,eAAe,CAACpD,MAAM,GAAG,CAAC,CAAC,qBAA3C+G,gBAAA,CAA6C1B,OAAO,CAACZ,OAAO,MAAAnG,aAAA,GAAA4B,CAAA,UAAI,CAAC;YAC1EyE,WAAW,EAAE;UACf;QACF,CAAC,CAAC;MACJ,CAAC;MAAA,SAvBapB,uBAAuBA,CAAA4D,IAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAP,wBAAA,CAAAlF,KAAA,OAAA7B,SAAA;MAAA;MAAA,OAAvBwD,uBAAuB;IAAA;EAAA;IAAAlE,GAAA;IAAAC,KAAA;MAAA,IAAAgI,eAAA,GAAA9H,iBAAA,CA4BrC,WACEa,GAAkB,EAClB+C,eAKE,EACFE,eAAuC,EACvCN,cAAkD,EACvB;QAAA1E,aAAA,GAAA6B,CAAA;QAC3B,IAAMoH,WAAW,IAAAjJ,aAAA,GAAAC,CAAA,SAAG6E,eAAe,CAACoE,MAAM,CAAC,UAACC,GAAG,EAAEP,EAAE,EAAK;UAAA5I,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAC,CAAA;UAAA,OAAAkJ,GAAG,GAAGP,EAAE,CAACzB,gBAAgB,CAACzF,MAAM;QAAD,CAAC,EAAE,CAAC,CAAC;QAC5F,IAAM0H,mBAAmB,IAAApJ,aAAA,GAAAC,CAAA,SAAG6E,eAAe,CAACoE,MAAM,CAAC,UAACC,GAAG,EAAEP,EAAE,EAAK;UAAA5I,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAC,CAAA;UAAA,OAAAkJ,GAAG,GAAGP,EAAE,CAACnB,cAAc;QAAD,CAAC,EAAE,CAAC,CAAC;QAC3F,IAAM4B,qBAAqB,IAAArJ,aAAA,GAAAC,CAAA,SAAGmJ,mBAAmB,GAAGtE,eAAe,CAACpD,MAAM;QAG1E,IAAM4H,WAAW,IAAAtJ,aAAA,GAAAC,CAAA,SAAG6E,eAAe,CAAC6D,OAAO,CAAC,UAAAC,EAAE,EAAI;UAAA5I,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAC,CAAA;UAAA,OAAA2I,EAAE,CAACzB,gBAAgB;QAAD,CAAC,CAAC;QACtE,IAAMoC,aAAa,IAAAvJ,aAAA,GAAAC,CAAA,SAAGqJ,WAAW,CAACJ,MAAM,CAAC,UAACC,GAAG,EAAEK,CAAC,EAAK;UAAAxJ,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAC,CAAA;UAAA,OAAAkJ,GAAG,GAAGK,CAAC,CAACC,UAAU;QAAD,CAAC,EAAE,CAAC,CAAC,GAAGH,WAAW,CAAC5H,MAAM;QAAC1B,aAAA,GAAAC,CAAA;QAEjG,OAAO;UACLuD,KAAK,EAAEzB,GAAG,CAACC,EAAE;UACbZ,OAAO,EAAEW,GAAG,CAACX,OAAO;UACpBgB,QAAQ,EAAE0C,eAAe;UACzBE,eAAe,EAAfA,eAAe;UACf0E,iBAAiB,EAAE;YACjBT,WAAW,EAAXA,WAAW;YACXU,eAAe,EAAEV,WAAW;YAC5BI,qBAAqB,EAArBA,qBAAqB;YACrBO,qBAAqB,EAAEL,aAAa;YACpCH,mBAAmB,EAAnBA;UACF,CAAC;UACD1E,cAAc,EAAdA;QACF,CAAC;MACH,CAAC;MAAA,SAjCaS,cAAcA,CAAA0E,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAhB,eAAA,CAAA1F,KAAA,OAAA7B,SAAA;MAAA;MAAA,OAAd0D,cAAc;IAAA;EAAA;IAAApE,GAAA;IAAAC,KAAA;MAAA,IAAAiJ,2BAAA,GAAA/I,iBAAA,CAsC5B,WAAyCC,QAAgB,EAAmB;QAAAnB,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAC,CAAA;QAE1E,MAAM,IAAIyF,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAA3F,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAC,CAAA;UAAA,OAAA2F,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC;QAAD,CAAC,CAAC;QAOtD,IAAMuE,oBAAoB,IAAAlK,aAAA,GAAAC,CAAA,SAAG,CAAC;QAC9B,IAAMkK,eAAe,IAAAnK,aAAA,GAAAC,CAAA,SAAG,GAAG;QAACD,aAAA,GAAAC,CAAA;QAE5B,OAAOiK,oBAAoB,GAAG,EAAE,GAAGC,eAAe;MACpD,CAAC;MAAA,SAba1H,0BAA0BA,CAAA2H,IAAA;QAAA,OAAAH,2BAAA,CAAA3G,KAAA,OAAA7B,SAAA;MAAA;MAAA,OAA1BgB,0BAA0B;IAAA;EAAA;IAAA1B,GAAA;IAAAC,KAAA,EAkBxC,SAAQ0B,UAAUA,CAACX,GAAkB,EAAQ;MAAA,IAAAsI,MAAA;MAAArK,aAAA,GAAA6B,CAAA;MAE3C,IAAMyI,WAAW,IAAAtK,aAAA,GAAAC,CAAA,SAAG,IAAI,CAACF,eAAe,CAACgE,SAAS,CAChD,UAAAwG,SAAS,EAAI;QAAAvK,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAC,CAAA;QAAA,OAAAoK,MAAI,CAACG,gBAAgB,CAACD,SAAS,CAAC/I,QAAQ,CAAC,GAAG6I,MAAI,CAACG,gBAAgB,CAACzI,GAAG,CAACP,QAAQ,CAAC;MAAD,CAC7F,CAAC;MAACxB,aAAA,GAAAC,CAAA;MAEF,IAAIqK,WAAW,KAAK,CAAC,CAAC,EAAE;QAAAtK,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QACtB,IAAI,CAACF,eAAe,CAAC2H,IAAI,CAAC3F,GAAG,CAAC;MAChC,CAAC,MAAM;QAAA/B,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QACL,IAAI,CAACF,eAAe,CAACkE,MAAM,CAACqG,WAAW,EAAE,CAAC,EAAEvI,GAAG,CAAC;MAClD;IACF;EAAC;IAAAhB,GAAA;IAAAC,KAAA;MAAA,IAAAyJ,aAAA,GAAAvJ,iBAAA,CAKD,aAA4C;QAAAlB,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAC,CAAA;QAC1C,IAAI,IAAI,CAACC,UAAU,CAACwK,IAAI,IAAI,IAAI,CAACrK,MAAM,CAACC,iBAAiB,EAAE;UAAAN,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UACzD;QACF,CAAC;UAAAD,aAAA,GAAA4B,CAAA;QAAA;QAED,IAAM+I,OAAO,IAAA3K,aAAA,GAAAC,CAAA,SAAG,IAAI,CAACF,eAAe,CAAC6K,KAAK,CAAC,CAAC;QAAC5K,aAAA,GAAAC,CAAA;QAC7C,IAAI,CAAC0K,OAAO,EAAE;UAAA3K,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UACZ;QACF,CAAC;UAAAD,aAAA,GAAA4B,CAAA;QAAA;QAAA5B,aAAA,GAAAC,CAAA;QAED,IAAI0K,OAAO,CAACzI,MAAM,KAAK,WAAW,EAAE;UAAAlC,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UAClC,IAAI,CAAC0C,YAAY,CAAC,CAAC;UAAC3C,aAAA,GAAAC,CAAA;UACpB;QACF,CAAC;UAAAD,aAAA,GAAA4B,CAAA;QAAA;QAAA5B,aAAA,GAAAC,CAAA;QAED,IAAI,CAACC,UAAU,CAACoE,GAAG,CAACqG,OAAO,CAAC3I,EAAE,EAAE2I,OAAO,CAAC;QAAC3K,aAAA,GAAAC,CAAA;QAEzC,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACF,MAAM,IAAI,CAACsF,UAAU,CAACoF,OAAO,CAAC;QAChC,CAAC,CAAC,OAAO9H,KAAK,EAAE;UAAA7C,aAAA,GAAAC,CAAA;UACd6C,OAAO,CAACD,KAAK,CAAC,yBAAyB8H,OAAO,CAAC3I,EAAE,GAAG,EAAEa,KAAK,CAAC;QAC9D;MACF,CAAC;MAAA,SAtBaF,YAAYA,CAAA;QAAA,OAAA8H,aAAA,CAAAnH,KAAA,OAAA7B,SAAA;MAAA;MAAA,OAAZkB,YAAY;IAAA;EAAA;IAAA5B,GAAA;IAAAC,KAAA,EA2B1B,SAAQ6C,eAAeA,CAAC9B,GAAkB,EAAQ;MAAA/B,aAAA,GAAA6B,CAAA;MAChD,IAAMwC,QAAQ,IAAArE,aAAA,GAAAC,CAAA,SAAG,IAAI,CAACG,YAAY,CAACqD,GAAG,CAAC1B,GAAG,CAACC,EAAE,CAAC;MAAChC,aAAA,GAAAC,CAAA;MAC/C,IAAIoE,QAAQ,EAAE;QAAArE,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QACZoE,QAAQ,CAACtC,GAAG,CAAC;MACf,CAAC;QAAA/B,aAAA,GAAA4B,CAAA;MAAA;IACH;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAKD,SAAQwJ,gBAAgBA,CAAChJ,QAAmC,EAAU;MAAAxB,aAAA,GAAA6B,CAAA;MAAA7B,aAAA,GAAAC,CAAA;MACpE,QAAQuB,QAAQ;QACd,KAAK,MAAM;UAAAxB,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UAAE,OAAO,CAAC;QACrB,KAAK,QAAQ;UAAAD,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UAAE,OAAO,CAAC;QACvB,KAAK,KAAK;UAAAD,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UAAE,OAAO,CAAC;MACtB;IACF;EAAC;IAAAc,GAAA;IAAAC,KAAA,EAKD,SAAQiB,aAAaA,CAAA,EAAW;MAAAjC,aAAA,GAAA6B,CAAA;MAAA7B,aAAA,GAAAC,CAAA;MAC9B,OAAO,OAAOqC,IAAI,CAAC0E,GAAG,CAAC,CAAC,IAAIe,IAAI,CAAC8C,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACvE;EAAC;IAAAhK,GAAA;IAAAC,KAAA,EAKD,SAAAgK,YAAYA,CAAC3K,MAAiC,EAAQ;MAAAL,aAAA,GAAA6B,CAAA;MAAA7B,aAAA,GAAAC,CAAA;MACpD,IAAI,CAACI,MAAM,GAAA4K,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAAC7K,MAAM,EAAKA,MAAM,CAAE;IAC7C;EAAC;IAAAU,GAAA;IAAAC,KAAA,EAKD,SAAAmK,SAASA,CAAA,EAAqB;MAAAnL,aAAA,GAAA6B,CAAA;MAAA7B,aAAA,GAAAC,CAAA;MAC5B,OAAAgL,MAAA,CAAAC,MAAA,KAAY,IAAI,CAAC7K,MAAM;IACzB;EAAC;IAAAU,GAAA;IAAAC,KAAA,EAKD,SAAAoK,cAAcA,CAAA,EAIZ;MAAApL,aAAA,GAAA6B,CAAA;MAAA7B,aAAA,GAAAC,CAAA;MACA,OAAO;QACLoL,WAAW,EAAE,IAAI,CAACtL,eAAe,CAAC2B,MAAM;QACxCxB,UAAU,EAAE,IAAI,CAACA,UAAU,CAACwK,IAAI;QAChCY,aAAa,EAAE,IAAI,CAACjL,MAAM,CAACC;MAC7B,CAAC;IACH;EAAC;IAAAS,GAAA;IAAAC,KAAA,EAKD,SAAAuK,OAAOA,CAAA,EAAS;MAAAvL,aAAA,GAAA6B,CAAA;MAAA7B,aAAA,GAAAC,CAAA;MACd,IAAI,CAACF,eAAe,CAAC2B,MAAM,GAAG,CAAC;MAAC1B,aAAA,GAAAC,CAAA;MAChC,IAAI,CAACC,UAAU,CAACsL,KAAK,CAAC,CAAC;MAACxL,aAAA,GAAAC,CAAA;MACxB,IAAI,CAACG,YAAY,CAACoL,KAAK,CAAC,CAAC;IAC3B;EAAC;AAAA;AAIH,OAAO,IAAMC,sBAAsB,IAAAzL,aAAA,GAAAC,CAAA,SAAG,IAAIJ,sBAAsB,CAAC,CAAC", "ignoreList": []}