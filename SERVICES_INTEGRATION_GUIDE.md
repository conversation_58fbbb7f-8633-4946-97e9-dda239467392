# Services Integration Guide

This guide documents the complete refactoring of notification, analytics, error monitoring, authentication, and payment systems according to the specified requirements.

## 🚀 Overview

The following services have been integrated to replace the previous stack:

- **Firebase Cloud Messaging + Expo Push** → Replaces OneSignal
- **PostHog Analytics** → Replaces Amplitude/Mixpanel  
- **Sentry Error Monitoring** → Replaces LogRocket/OpenReplay
- **Magic.link + Social Auth** → Enhanced authentication options
- **Stripe Payments** → Verified as the primary payment processor

## 📱 1. Notifications (Firebase FCM + Expo Push)

### Implementation
- **Service**: `services/notifications/NotificationService.ts`
- **Backend**: `services/notifications/PushNotificationBackend.ts`
- **Features**: Unified notification handling for web, iOS, and Android

### Key Features
```typescript
// Initialize notification service
await notificationService.initialize();

// Request permissions
const permissions = await notificationService.requestPermissions();

// Send local notification
await notificationService.sendLocalNotification({
  title: 'Training Reminder',
  body: 'Time for your tennis practice!',
  data: { type: 'training_reminder' }
});

// Schedule notification
await notificationService.scheduleNotification(notification, 3600); // 1 hour
```

### Backend Example
```typescript
// Send to specific user
await pushNotificationBackend.sendNotificationToUser(userId, {
  title: 'Match Result',
  body: 'Your match analysis is ready!',
  type: 'match_result'
});

// Send training reminder
await pushNotificationBackend.sendTrainingReminder(userId, 'serve_practice');
```

### Environment Variables
```env
FCM_SERVER_KEY=your-fcm-server-key
EXPO_PUBLIC_FCM_VAPID_KEY=your-vapid-key
EXPO_PUSH_TOKEN=your-expo-push-token
```

## 📊 2. Analytics (PostHog)

### Implementation
- **Service**: `services/analytics/PostHogService.ts`
- **Features**: Event tracking, user identification, tennis-specific analytics

### Key Features
```typescript
// Initialize PostHog
await postHogService.initialize();

// Identify user
postHogService.identify(userId, {
  email: user.email,
  tennisLevel: 'intermediate',
  subscriptionTier: 'premium'
});

// Track events
postHogService.trackLogin('magic_link');
postHogService.trackVideoUpload('forehand', 120, 50);
postHogService.trackTrainingSession('serve_practice', 1800, true);
postHogService.trackPurchase('premium_monthly', 999, 'usd', 'premium');
```

### Tennis-Specific Events
- `user_login` / `user_registration`
- `video_uploaded` 
- `training_session`
- `purchase_completed`
- `match_recorded`
- `ai_analysis_completed`
- `feature_used`
- `error_occurred`

### Environment Variables
```env
POSTHOG_PROJECT_API_KEY=phc_your_project_key
POSTHOG_API_HOST=https://app.posthog.com
```

## 🐛 3. Error Monitoring (Sentry)

### Implementation
- **Service**: `services/monitoring/SentryService.ts`
- **Enhanced Error Boundary**: `components/ErrorBoundary.tsx`
- **Features**: Error capture, performance monitoring, breadcrumbs

### Key Features
```typescript
// Initialize Sentry
await sentryService.initialize();

// Set user context
sentryService.setUser({
  id: userId,
  email: user.email,
  tennisLevel: 'intermediate'
});

// Capture exception
sentryService.captureException(error, {
  tags: { feature: 'video_analysis' },
  extra: { videoId: 'abc123' }
});

// Performance monitoring
await sentryService.measurePerformance('video_processing', async () => {
  return await processVideo(videoFile);
});
```

### Error Boundary Integration
```tsx
<ErrorBoundary
  showDetails={__DEV__}
  onError={(error, errorInfo) => {
    // Automatically sent to Sentry
  }}
>
  <YourComponent />
</ErrorBoundary>
```

### Environment Variables
```env
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ORG=your-organization
SENTRY_PROJECT=acemind-tennis
SENTRY_AUTH_TOKEN=your-auth-token
```

## 🔐 4. Authentication (Magic.link + Social)

### Implementation
- **Magic.link Service**: `services/auth/MagicLinkService.ts`
- **Enhanced Auth Service**: `services/auth/AuthService.ts`
- **Features**: Passwordless auth, Google/Facebook login, session management

### Key Features
```typescript
// Magic.link passwordless login
const result = await authService.signInWithMagicLink('<EMAIL>');

// Google login
const googleResult = await authService.signInWithGoogle(idToken);

// Facebook login
const facebookResult = await authService.signInWithFacebook(accessToken);

// Traditional email/password
const emailResult = await authService.signIn({
  email: '<EMAIL>',
  password: 'password'
});
```

### Magic.link Direct Usage
```typescript
// Initialize Magic.link
await magicLinkService.initialize();

// Login with email
const result = await magicLinkService.loginWithEmail({
  email: '<EMAIL>',
  showUI: true
});

// Check login status
const isLoggedIn = await magicLinkService.isLoggedIn();

// Get current user
const user = await magicLinkService.getCurrentUser();
```

### Environment Variables
```env
MAGIC_PUBLISHABLE_KEY=pk_live_your_magic_key
MAGIC_SECRET_KEY=sk_live_your_magic_secret
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
```

## 💳 5. Payments (Stripe)

### Implementation
- **Enhanced Payment Service**: `src/services/payment/PaymentService.ts`
- **Stripe Service**: `services/payment/StripeService.ts`
- **Features**: Web/mobile payments, Apple Pay, Google Pay, subscriptions

### Key Features
```typescript
// Initialize Stripe
await paymentService.initialize();

// Create subscription
const subscription = await paymentService.createSubscription(
  userId,
  'premium_monthly'
);

// Process one-time payment
const payment = await paymentService.processPurchase(userId, {
  type: 'coaching_session',
  itemId: 'session_123',
  amount: 5000, // $50.00
  currency: 'usd',
  paymentMethodId: 'pm_123'
});

// Apple Pay / Google Pay
const applePayResult = await paymentService.processApplePay({
  amount: 999,
  currency: 'usd',
  merchantId: process.env.APPLE_PAY_MERCHANT_ID
});
```

### Environment Variables
```env
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
APPLE_PAY_MERCHANT_ID=merchant.com.acemind.tennis
GOOGLE_PAY_MERCHANT_ID=your-google-pay-merchant-id
```

## 🎯 6. Demo Implementation

### Integration Demo Component
- **Component**: `components/IntegrationDemo.tsx`
- **Page**: `app/integration-demo.tsx`
- **Features**: Interactive examples of all services

### Demo Features
- Notification permissions and sending
- Analytics event tracking
- Error monitoring examples
- Magic.link authentication
- Performance measurement

### Access Demo
1. Start development server: `npm run dev`
2. Navigate to Training tab
3. Click "View Integrations" button
4. Explore interactive examples

## 🔧 7. Environment Setup

### Complete .env Configuration
```env
# Firebase Configuration
FIREBASE_API_KEY=your-firebase-api-key
FIREBASE_PROJECT_ID=your-project-id
FCM_SERVER_KEY=your-fcm-server-key
EXPO_PUBLIC_FCM_VAPID_KEY=your-vapid-key

# Push Notifications
EXPO_PUSH_TOKEN=your-expo-push-token

# PostHog Analytics
POSTHOG_PROJECT_API_KEY=phc_your_project_key
POSTHOG_API_HOST=https://app.posthog.com

# Sentry Error Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ORG=your-organization
SENTRY_PROJECT=acemind-tennis

# Magic.link Authentication
MAGIC_PUBLISHABLE_KEY=pk_live_your_key
MAGIC_SECRET_KEY=sk_live_your_secret

# Social Authentication
GOOGLE_CLIENT_ID=your-google-client-id
FACEBOOK_APP_ID=your-facebook-app-id

# Stripe Payments
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_key
STRIPE_SECRET_KEY=sk_test_your_secret
APPLE_PAY_MERCHANT_ID=merchant.com.acemind
GOOGLE_PAY_MERCHANT_ID=your-google-pay-id
```

## 📦 8. Dependencies Installed

```json
{
  "dependencies": {
    "firebase": "^10.x.x",
    "expo-notifications": "^0.x.x",
    "posthog-js": "^1.x.x",
    "@sentry/react-native": "^5.x.x",
    "magic-sdk": "^21.x.x"
  }
}
```

## ✅ 9. Removed Services

The following services have been removed/replaced:
- ❌ OneSignal → ✅ Firebase FCM + Expo Push
- ❌ Amplitude/Mixpanel → ✅ PostHog
- ❌ LogRocket/OpenReplay → ✅ Sentry
- ❌ Custom notification code → ✅ Unified notification service

## 🧪 10. Testing

### Manual Testing
1. **Notifications**: Test permission requests, local notifications, push notifications
2. **Analytics**: Verify events in PostHog dashboard
3. **Error Monitoring**: Check error capture in Sentry dashboard
4. **Authentication**: Test Magic.link, Google, Facebook login flows
5. **Payments**: Test Stripe payment flows (use test mode)

### Integration Demo
- Access via Training tab → "View Integrations"
- Interactive examples for all services
- Real-time testing capabilities

## 🎯 Conclusion

All services have been successfully integrated according to specifications:
- ✅ Firebase FCM for notifications
- ✅ PostHog for analytics
- ✅ Sentry for error monitoring
- ✅ Magic.link for passwordless auth
- ✅ Stripe for payments
- ✅ Complete environment variable cleanup
- ✅ Working demo implementation
- ✅ Comprehensive documentation
