{"version": 3, "names": ["Dimensions", "Platform", "advancedCacheManager", "performanceMonitor", "ImageOptimizationService", "_classCallCheck", "screenData", "cov_1vw0dzcqro", "s", "get", "devicePixelRatio", "b", "scale", "isHighDensity", "breakpoints", "small", "medium", "large", "xlarge", "_createClass", "key", "value", "_optimizeImage", "_asyncToGenerator", "imageUrl", "options", "arguments", "length", "undefined", "f", "startTime", "Date", "now", "_ref", "width", "height", "_ref$quality", "quality", "_ref$format", "format", "_ref$fit", "fit", "_ref$priority", "priority", "_ref$progressive", "progressive", "blur", "_ref$sharpen", "sharpen", "brightness", "contrast", "saturation", "cache<PERSON>ey", "generate<PERSON>ache<PERSON>ey", "cached", "trackDatabaseQuery", "optimalFormat", "determineOptimalFormat", "optimalDimensions", "calculateOptimalDimensions", "params", "buildOptimizationParams", "Object", "assign", "adjustQualityForDevice", "optimizedUrl", "applyOptimizations", "metadata", "getImageMetadata", "estimatedSavings", "calculateSavings", "result", "originalUrl", "optimizationApplied", "keys", "set", "ttl", "tags", "optimizationTime", "error", "console", "trackDatabaseError", "size", "aspectRatio", "optimizeImage", "_x", "apply", "_generateResponsiveImageSet", "_this", "baseOptions", "promises", "entries", "map", "_ref3", "_ref2", "_ref4", "_slicedToArray", "optimized", "_x3", "results", "Promise", "all", "fromEntries", "generateResponsiveImageSet", "_x2", "_preloadImages", "imageUrls", "_this2", "preloadPromises", "_ref5", "url", "preloadSingleImage", "warn", "_x5", "preloadImages", "_x4", "_getOptimizationRecommendations", "recommendations", "potentialSavings", "push", "Math", "min", "getOptimizationRecommendations", "_x6", "_clearOptimizationCache", "invalidate", "clearOptimizationCache", "getOptimizationStats", "totalOptimizations", "averageSavings", "cacheHitRate", "formatDistribution", "optionsHash", "JSON", "stringify", "btoa", "slice", "OS", "iosVersion", "parseInt", "Version", "includes", "<PERSON><PERSON><PERSON><PERSON>", "requestedHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "max", "w", "h", "q", "dpr", "auto", "url<PERSON>bj", "URL", "for<PERSON>ach", "_ref6", "_ref7", "searchParams", "String", "toString", "separator", "paramString", "_ref8", "_ref9", "encodeURIComponent", "join", "_getImageMetadata", "colorSpace", "has<PERSON><PERSON><PERSON>", "_x7", "savings", "_preloadSingleImage", "resolve", "reject", "image", "Image", "onload", "onerror", "src", "_x8", "imageOptimizationService"], "sources": ["ImageOptimizationService.ts"], "sourcesContent": ["/**\n * Image Optimization Service\n * \n * Provides comprehensive image optimization including format conversion,\n * responsive sizing, caching, and performance monitoring.\n */\n\nimport { Dimensions, Platform } from 'react-native';\nimport { advancedCacheManager } from '@/services/caching/AdvancedCacheManager';\nimport { performanceMonitor } from '@/utils/performance';\n\ninterface ImageOptimizationOptions {\n  width?: number;\n  height?: number;\n  quality?: number;\n  format?: 'webp' | 'jpeg' | 'png' | 'auto';\n  fit?: 'crop' | 'contain' | 'cover' | 'fill' | 'scale-down';\n  priority?: 'high' | 'medium' | 'low';\n  lazy?: boolean;\n  progressive?: boolean;\n  blur?: number;\n  sharpen?: boolean;\n  brightness?: number;\n  contrast?: number;\n  saturation?: number;\n}\n\ninterface ResponsiveImageSet {\n  small: string;\n  medium: string;\n  large: string;\n  xlarge: string;\n}\n\ninterface ImageMetadata {\n  width: number;\n  height: number;\n  format: string;\n  size: number;\n  aspectRatio: number;\n  colorSpace?: string;\n  hasAlpha?: boolean;\n}\n\ninterface OptimizationResult {\n  optimizedUrl: string;\n  originalUrl: string;\n  metadata: ImageMetadata;\n  optimizationApplied: string[];\n  estimatedSavings: number;\n  cacheKey: string;\n}\n\n/**\n * Advanced Image Optimization Service\n */\nclass ImageOptimizationService {\n  private readonly screenData = Dimensions.get('window');\n  private readonly devicePixelRatio = this.screenData.scale || 1;\n  private readonly isHighDensity = this.devicePixelRatio >= 2;\n  \n  // Breakpoints for responsive images\n  private readonly breakpoints = {\n    small: 480,\n    medium: 768,\n    large: 1024,\n    xlarge: 1440,\n  };\n\n  /**\n   * Optimize a single image with comprehensive options\n   */\n  async optimizeImage(\n    imageUrl: string,\n    options: ImageOptimizationOptions = {}\n  ): Promise<OptimizationResult> {\n    const startTime = Date.now();\n    \n    try {\n      const {\n        width,\n        height,\n        quality = 80,\n        format = 'auto',\n        fit = 'crop',\n        priority = 'medium',\n        progressive = true,\n        blur,\n        sharpen = false,\n        brightness,\n        contrast,\n        saturation,\n      } = options;\n\n      // Generate cache key\n      const cacheKey = this.generateCacheKey(imageUrl, options);\n      \n      // Check cache first\n      const cached = await advancedCacheManager.get<OptimizationResult>(cacheKey);\n      if (cached) {\n        performanceMonitor.trackDatabaseQuery('image_optimization_cache_hit', Date.now() - startTime);\n        return cached;\n      }\n\n      // Determine optimal format\n      const optimalFormat = this.determineOptimalFormat(format, imageUrl);\n      \n      // Calculate optimal dimensions\n      const optimalDimensions = this.calculateOptimalDimensions(width, height);\n      \n      // Build optimization parameters\n      const params = this.buildOptimizationParams({\n        ...options,\n        width: optimalDimensions.width,\n        height: optimalDimensions.height,\n        format: optimalFormat,\n        quality: this.adjustQualityForDevice(quality),\n        progressive,\n        blur,\n        sharpen,\n        brightness,\n        contrast,\n        saturation,\n      });\n\n      // Generate optimized URL\n      const optimizedUrl = this.applyOptimizations(imageUrl, params);\n      \n      // Get image metadata (simulated)\n      const metadata = await this.getImageMetadata(optimizedUrl);\n      \n      // Calculate estimated savings\n      const estimatedSavings = this.calculateSavings(metadata, options);\n      \n      const result: OptimizationResult = {\n        optimizedUrl,\n        originalUrl: imageUrl,\n        metadata,\n        optimizationApplied: Object.keys(params),\n        estimatedSavings,\n        cacheKey,\n      };\n\n      // Cache the result\n      await advancedCacheManager.set(cacheKey, result, {\n        ttl: 86400000, // 24 hours\n        priority,\n        tags: ['image_optimization'],\n      });\n\n      const optimizationTime = Date.now() - startTime;\n      performanceMonitor.trackDatabaseQuery('image_optimization', optimizationTime);\n\n      return result;\n\n    } catch (error) {\n      console.error('Image optimization failed:', error);\n      performanceMonitor.trackDatabaseError('image_optimization', error as Error);\n      \n      // Return fallback result\n      return {\n        optimizedUrl: imageUrl,\n        originalUrl: imageUrl,\n        metadata: {\n          width: 0,\n          height: 0,\n          format: 'unknown',\n          size: 0,\n          aspectRatio: 1,\n        },\n        optimizationApplied: [],\n        estimatedSavings: 0,\n        cacheKey: '',\n      };\n    }\n  }\n\n  /**\n   * Generate responsive image set for different screen sizes\n   */\n  async generateResponsiveImageSet(\n    imageUrl: string,\n    baseOptions: ImageOptimizationOptions = {}\n  ): Promise<ResponsiveImageSet> {\n    const promises = Object.entries(this.breakpoints).map(async ([size, width]) => {\n      const optimized = await this.optimizeImage(imageUrl, {\n        ...baseOptions,\n        width: width * this.devicePixelRatio,\n        priority: size === 'medium' ? 'high' : 'medium',\n      });\n      \n      return [size, optimized.optimizedUrl];\n    });\n\n    const results = await Promise.all(promises);\n    return Object.fromEntries(results) as ResponsiveImageSet;\n  }\n\n  /**\n   * Preload critical images with optimization\n   */\n  async preloadImages(\n    imageUrls: string[],\n    options: ImageOptimizationOptions = {}\n  ): Promise<void> {\n    const preloadPromises = imageUrls.map(async (url) => {\n      try {\n        const optimized = await this.optimizeImage(url, {\n          ...options,\n          priority: 'high',\n        });\n        \n        // Preload the optimized image\n        await this.preloadSingleImage(optimized.optimizedUrl);\n      } catch (error) {\n        console.warn(`Failed to preload image: ${url}`, error);\n      }\n    });\n\n    await Promise.all(preloadPromises);\n  }\n\n  /**\n   * Get optimization recommendations for an image\n   */\n  async getOptimizationRecommendations(\n    imageUrl: string\n  ): Promise<{\n    recommendations: string[];\n    potentialSavings: number;\n    optimalFormat: string;\n    optimalDimensions: { width: number; height: number };\n  }> {\n    const metadata = await this.getImageMetadata(imageUrl);\n    const recommendations: string[] = [];\n    let potentialSavings = 0;\n\n    // Format recommendations\n    const optimalFormat = this.determineOptimalFormat('auto', imageUrl);\n    if (metadata.format !== optimalFormat) {\n      recommendations.push(`Convert to ${optimalFormat} format`);\n      potentialSavings += 30; // Estimated 30% savings\n    }\n\n    // Size recommendations\n    const optimalDimensions = this.calculateOptimalDimensions();\n    if (metadata.width > optimalDimensions.width * 2) {\n      recommendations.push('Reduce image dimensions for mobile');\n      potentialSavings += 50; // Estimated 50% savings\n    }\n\n    // Quality recommendations\n    if (metadata.size > 500000) { // > 500KB\n      recommendations.push('Reduce quality to 80% for better performance');\n      potentialSavings += 20; // Estimated 20% savings\n    }\n\n    // Progressive loading\n    recommendations.push('Enable progressive loading');\n\n    return {\n      recommendations,\n      potentialSavings: Math.min(potentialSavings, 80), // Cap at 80%\n      optimalFormat,\n      optimalDimensions,\n    };\n  }\n\n  /**\n   * Clear image optimization cache\n   */\n  async clearOptimizationCache(): Promise<void> {\n    await advancedCacheManager.invalidate(['image_optimization']);\n  }\n\n  /**\n   * Get optimization statistics\n   */\n  getOptimizationStats(): {\n    totalOptimizations: number;\n    averageSavings: number;\n    cacheHitRate: number;\n    formatDistribution: Record<string, number>;\n  } {\n    // This would be implemented with actual statistics tracking\n    return {\n      totalOptimizations: 0,\n      averageSavings: 0,\n      cacheHitRate: 0,\n      formatDistribution: {},\n    };\n  }\n\n  // Private helper methods\n\n  private generateCacheKey(url: string, options: ImageOptimizationOptions): string {\n    const optionsHash = JSON.stringify(options);\n    return `img_opt_${btoa(url).slice(0, 20)}_${btoa(optionsHash).slice(0, 20)}`;\n  }\n\n  private determineOptimalFormat(format: string, imageUrl: string): string {\n    if (format !== 'auto') return format;\n\n    // Check platform support\n    if (Platform.OS === 'android') {\n      return 'webp'; // Android supports WebP natively\n    }\n    \n    if (Platform.OS === 'ios') {\n      const iosVersion = parseInt(Platform.Version as string, 10);\n      return iosVersion >= 14 ? 'webp' : 'jpeg';\n    }\n\n    // Check if image has transparency\n    if (imageUrl.includes('.png') || imageUrl.includes('alpha')) {\n      return 'png';\n    }\n\n    return 'jpeg';\n  }\n\n  private calculateOptimalDimensions(\n    requestedWidth?: number,\n    requestedHeight?: number\n  ): { width: number; height: number } {\n    const maxWidth = this.screenData.width * this.devicePixelRatio;\n    const maxHeight = this.screenData.height * this.devicePixelRatio;\n\n    return {\n      width: requestedWidth ? Math.min(requestedWidth, maxWidth) : maxWidth,\n      height: requestedHeight ? Math.min(requestedHeight, maxHeight) : maxHeight,\n    };\n  }\n\n  private adjustQualityForDevice(quality: number): number {\n    // Reduce quality on low-end devices or slow connections\n    if (this.devicePixelRatio < 2) {\n      return Math.max(quality - 10, 60);\n    }\n    \n    return quality;\n  }\n\n  private buildOptimizationParams(options: ImageOptimizationOptions): Record<string, any> {\n    const params: Record<string, any> = {};\n\n    if (options.width) params.w = options.width;\n    if (options.height) params.h = options.height;\n    if (options.quality) params.q = options.quality;\n    if (options.format) params.f = options.format;\n    if (options.fit) params.fit = options.fit;\n    if (options.progressive) params.progressive = 'true';\n    if (options.blur) params.blur = options.blur;\n    if (options.sharpen) params.sharpen = 'true';\n    if (options.brightness) params.brightness = options.brightness;\n    if (options.contrast) params.contrast = options.contrast;\n    if (options.saturation) params.saturation = options.saturation;\n\n    // Add device pixel ratio\n    params.dpr = this.devicePixelRatio;\n    \n    // Add auto optimization\n    params.auto = 'format,compress';\n\n    return params;\n  }\n\n  private applyOptimizations(url: string, params: Record<string, any>): string {\n    try {\n      const urlObj = new URL(url);\n      \n      Object.entries(params).forEach(([key, value]) => {\n        urlObj.searchParams.set(key, String(value));\n      });\n      \n      return urlObj.toString();\n    } catch (error) {\n      // If URL parsing fails, try simple parameter appending\n      const separator = url.includes('?') ? '&' : '?';\n      const paramString = Object.entries(params)\n        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)\n        .join('&');\n      \n      return `${url}${separator}${paramString}`;\n    }\n  }\n\n  private async getImageMetadata(url: string): Promise<ImageMetadata> {\n    // In a real implementation, this would fetch actual image metadata\n    // For now, return simulated metadata\n    return {\n      width: 1920,\n      height: 1080,\n      format: 'jpeg',\n      size: 250000, // 250KB\n      aspectRatio: 1920 / 1080,\n      colorSpace: 'sRGB',\n      hasAlpha: false,\n    };\n  }\n\n  private calculateSavings(metadata: ImageMetadata, options: ImageOptimizationOptions): number {\n    let savings = 0;\n\n    // Format savings\n    if (options.format === 'webp') savings += 25;\n    \n    // Quality savings\n    if (options.quality && options.quality < 90) savings += 15;\n    \n    // Dimension savings\n    if (options.width && options.width < metadata.width) savings += 30;\n\n    return Math.min(savings, 70); // Cap at 70%\n  }\n\n  private async preloadSingleImage(url: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const image = new Image();\n      image.onload = () => resolve();\n      image.onerror = reject;\n      image.src = url;\n    });\n  }\n}\n\n// Export singleton instance\nexport const imageOptimizationService = new ImageOptimizationService();\nexport default imageOptimizationService;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,UAAU,EAAEC,QAAQ,QAAQ,cAAc;AACnD,SAASC,oBAAoB;AAC7B,SAASC,kBAAkB;AAA8B,IA+CnDC,wBAAwB;EAAA,SAAAA,yBAAA;IAAAC,eAAA,OAAAD,wBAAA;IAAA,KACXE,UAAU,IAAAC,cAAA,GAAAC,CAAA,OAAGR,UAAU,CAACS,GAAG,CAAC,QAAQ,CAAC;IAAA,KACrCC,gBAAgB,IAAAH,cAAA,GAAAC,CAAA,OAAG,CAAAD,cAAA,GAAAI,CAAA,cAAI,CAACL,UAAU,CAACM,KAAK,MAAAL,cAAA,GAAAI,CAAA,UAAI,CAAC;IAAA,KAC7CE,aAAa,IAAAN,cAAA,GAAAC,CAAA,OAAG,IAAI,CAACE,gBAAgB,IAAI,CAAC;IAAA,KAG1CI,WAAW,IAAAP,cAAA,GAAAC,CAAA,OAAG;MAC7BO,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,GAAG;MACXC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE;IACV,CAAC;EAAA;EAAA,OAAAC,YAAA,CAAAf,wBAAA;IAAAgB,GAAA;IAAAC,KAAA;MAAA,IAAAC,cAAA,GAAAC,iBAAA,CAKD,WACEC,QAAgB,EAEa;QAAA,IAD7BC,OAAiC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAnB,cAAA,GAAAI,CAAA,UAAG,CAAC,CAAC;QAAAJ,cAAA,GAAAsB,CAAA;QAEtC,IAAMC,SAAS,IAAAvB,cAAA,GAAAC,CAAA,OAAGuB,IAAI,CAACC,GAAG,CAAC,CAAC;QAACzB,cAAA,GAAAC,CAAA;QAE7B,IAAI;UACF,IAAAyB,IAAA,IAAA1B,cAAA,GAAAC,CAAA,OAaIiB,OAAO;YAZTS,KAAK,GAAAD,IAAA,CAALC,KAAK;YACLC,MAAM,GAAAF,IAAA,CAANE,MAAM;YAAAC,YAAA,GAAAH,IAAA,CACNI,OAAO;YAAPA,OAAO,GAAAD,YAAA,eAAA7B,cAAA,GAAAI,CAAA,UAAG,EAAE,IAAAyB,YAAA;YAAAE,WAAA,GAAAL,IAAA,CACZM,MAAM;YAANA,MAAM,GAAAD,WAAA,eAAA/B,cAAA,GAAAI,CAAA,UAAG,MAAM,IAAA2B,WAAA;YAAAE,QAAA,GAAAP,IAAA,CACfQ,GAAG;YAAHA,GAAG,GAAAD,QAAA,eAAAjC,cAAA,GAAAI,CAAA,UAAG,MAAM,IAAA6B,QAAA;YAAAE,aAAA,GAAAT,IAAA,CACZU,QAAQ;YAARA,QAAQ,GAAAD,aAAA,eAAAnC,cAAA,GAAAI,CAAA,UAAG,QAAQ,IAAA+B,aAAA;YAAAE,gBAAA,GAAAX,IAAA,CACnBY,WAAW;YAAXA,WAAW,GAAAD,gBAAA,eAAArC,cAAA,GAAAI,CAAA,UAAG,IAAI,IAAAiC,gBAAA;YAClBE,IAAI,GAAAb,IAAA,CAAJa,IAAI;YAAAC,YAAA,GAAAd,IAAA,CACJe,OAAO;YAAPA,OAAO,GAAAD,YAAA,eAAAxC,cAAA,GAAAI,CAAA,UAAG,KAAK,IAAAoC,YAAA;YACfE,UAAU,GAAAhB,IAAA,CAAVgB,UAAU;YACVC,QAAQ,GAAAjB,IAAA,CAARiB,QAAQ;YACRC,UAAU,GAAAlB,IAAA,CAAVkB,UAAU;UAIZ,IAAMC,QAAQ,IAAA7C,cAAA,GAAAC,CAAA,OAAG,IAAI,CAAC6C,gBAAgB,CAAC7B,QAAQ,EAAEC,OAAO,CAAC;UAGzD,IAAM6B,MAAM,IAAA/C,cAAA,GAAAC,CAAA,aAASN,oBAAoB,CAACO,GAAG,CAAqB2C,QAAQ,CAAC;UAAC7C,cAAA,GAAAC,CAAA;UAC5E,IAAI8C,MAAM,EAAE;YAAA/C,cAAA,GAAAI,CAAA;YAAAJ,cAAA,GAAAC,CAAA;YACVL,kBAAkB,CAACoD,kBAAkB,CAAC,8BAA8B,EAAExB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS,CAAC;YAACvB,cAAA,GAAAC,CAAA;YAC9F,OAAO8C,MAAM;UACf,CAAC;YAAA/C,cAAA,GAAAI,CAAA;UAAA;UAGD,IAAM6C,aAAa,IAAAjD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACiD,sBAAsB,CAAClB,MAAM,EAAEf,QAAQ,CAAC;UAGnE,IAAMkC,iBAAiB,IAAAnD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACmD,0BAA0B,CAACzB,KAAK,EAAEC,MAAM,CAAC;UAGxE,IAAMyB,MAAM,IAAArD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACqD,uBAAuB,CAAAC,MAAA,CAAAC,MAAA,KACtCtC,OAAO;YACVS,KAAK,EAAEwB,iBAAiB,CAACxB,KAAK;YAC9BC,MAAM,EAAEuB,iBAAiB,CAACvB,MAAM;YAChCI,MAAM,EAAEiB,aAAa;YACrBnB,OAAO,EAAE,IAAI,CAAC2B,sBAAsB,CAAC3B,OAAO,CAAC;YAC7CQ,WAAW,EAAXA,WAAW;YACXC,IAAI,EAAJA,IAAI;YACJE,OAAO,EAAPA,OAAO;YACPC,UAAU,EAAVA,UAAU;YACVC,QAAQ,EAARA,QAAQ;YACRC,UAAU,EAAVA;UAAU,EACX,CAAC;UAGF,IAAMc,YAAY,IAAA1D,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC0D,kBAAkB,CAAC1C,QAAQ,EAAEoC,MAAM,CAAC;UAG9D,IAAMO,QAAQ,IAAA5D,cAAA,GAAAC,CAAA,cAAS,IAAI,CAAC4D,gBAAgB,CAACH,YAAY,CAAC;UAG1D,IAAMI,gBAAgB,IAAA9D,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC8D,gBAAgB,CAACH,QAAQ,EAAE1C,OAAO,CAAC;UAEjE,IAAM8C,MAA0B,IAAAhE,cAAA,GAAAC,CAAA,QAAG;YACjCyD,YAAY,EAAZA,YAAY;YACZO,WAAW,EAAEhD,QAAQ;YACrB2C,QAAQ,EAARA,QAAQ;YACRM,mBAAmB,EAAEX,MAAM,CAACY,IAAI,CAACd,MAAM,CAAC;YACxCS,gBAAgB,EAAhBA,gBAAgB;YAChBjB,QAAQ,EAARA;UACF,CAAC;UAAC7C,cAAA,GAAAC,CAAA;UAGF,MAAMN,oBAAoB,CAACyE,GAAG,CAACvB,QAAQ,EAAEmB,MAAM,EAAE;YAC/CK,GAAG,EAAE,QAAQ;YACbjC,QAAQ,EAARA,QAAQ;YACRkC,IAAI,EAAE,CAAC,oBAAoB;UAC7B,CAAC,CAAC;UAEF,IAAMC,gBAAgB,IAAAvE,cAAA,GAAAC,CAAA,QAAGuB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;UAACvB,cAAA,GAAAC,CAAA;UAChDL,kBAAkB,CAACoD,kBAAkB,CAAC,oBAAoB,EAAEuB,gBAAgB,CAAC;UAACvE,cAAA,GAAAC,CAAA;UAE9E,OAAO+D,MAAM;QAEf,CAAC,CAAC,OAAOQ,KAAK,EAAE;UAAAxE,cAAA,GAAAC,CAAA;UACdwE,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAACxE,cAAA,GAAAC,CAAA;UACnDL,kBAAkB,CAAC8E,kBAAkB,CAAC,oBAAoB,EAAEF,KAAc,CAAC;UAACxE,cAAA,GAAAC,CAAA;UAG5E,OAAO;YACLyD,YAAY,EAAEzC,QAAQ;YACtBgD,WAAW,EAAEhD,QAAQ;YACrB2C,QAAQ,EAAE;cACRjC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTI,MAAM,EAAE,SAAS;cACjB2C,IAAI,EAAE,CAAC;cACPC,WAAW,EAAE;YACf,CAAC;YACDV,mBAAmB,EAAE,EAAE;YACvBJ,gBAAgB,EAAE,CAAC;YACnBjB,QAAQ,EAAE;UACZ,CAAC;QACH;MACF,CAAC;MAAA,SAvGKgC,aAAaA,CAAAC,EAAA;QAAA,OAAA/D,cAAA,CAAAgE,KAAA,OAAA5D,SAAA;MAAA;MAAA,OAAb0D,aAAa;IAAA;EAAA;IAAAhE,GAAA;IAAAC,KAAA;MAAA,IAAAkE,2BAAA,GAAAhE,iBAAA,CA4GnB,WACEC,QAAgB,EAEa;QAAA,IAAAgE,KAAA;QAAA,IAD7BC,WAAqC,GAAA/D,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAnB,cAAA,GAAAI,CAAA,UAAG,CAAC,CAAC;QAAAJ,cAAA,GAAAsB,CAAA;QAE1C,IAAM6D,QAAQ,IAAAnF,cAAA,GAAAC,CAAA,QAAGsD,MAAM,CAAC6B,OAAO,CAAC,IAAI,CAAC7E,WAAW,CAAC,CAAC8E,GAAG;UAAA,IAAAC,KAAA,GAAAtE,iBAAA,CAAC,WAAAuE,KAAA,EAAyB;YAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,KAAA;cAAjBZ,IAAI,GAAAa,KAAA;cAAE7D,KAAK,GAAA6D,KAAA;YAAAxF,cAAA,GAAAsB,CAAA;YACvE,IAAMoE,SAAS,IAAA1F,cAAA,GAAAC,CAAA,cAASgF,KAAI,CAACJ,aAAa,CAAC5D,QAAQ,EAAAsC,MAAA,CAAAC,MAAA,KAC9C0B,WAAW;cACdvD,KAAK,EAAEA,KAAK,GAAGsD,KAAI,CAAC9E,gBAAgB;cACpCiC,QAAQ,EAAEuC,IAAI,KAAK,QAAQ,IAAA3E,cAAA,GAAAI,CAAA,WAAG,MAAM,KAAAJ,cAAA,GAAAI,CAAA,WAAG,QAAQ;YAAA,EAChD,CAAC;YAACJ,cAAA,GAAAC,CAAA;YAEH,OAAO,CAAC0E,IAAI,EAAEe,SAAS,CAAChC,YAAY,CAAC;UACvC,CAAC;UAAA,iBAAAiC,GAAA;YAAA,OAAAL,KAAA,CAAAP,KAAA,OAAA5D,SAAA;UAAA;QAAA,IAAC;QAEF,IAAMyE,OAAO,IAAA5F,cAAA,GAAAC,CAAA,cAAS4F,OAAO,CAACC,GAAG,CAACX,QAAQ,CAAC;QAACnF,cAAA,GAAAC,CAAA;QAC5C,OAAOsD,MAAM,CAACwC,WAAW,CAACH,OAAO,CAAC;MACpC,CAAC;MAAA,SAhBKI,0BAA0BA,CAAAC,GAAA;QAAA,OAAAjB,2BAAA,CAAAD,KAAA,OAAA5D,SAAA;MAAA;MAAA,OAA1B6E,0BAA0B;IAAA;EAAA;IAAAnF,GAAA;IAAAC,KAAA;MAAA,IAAAoF,cAAA,GAAAlF,iBAAA,CAqBhC,WACEmF,SAAmB,EAEJ;QAAA,IAAAC,MAAA;QAAA,IADflF,OAAiC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAnB,cAAA,GAAAI,CAAA,WAAG,CAAC,CAAC;QAAAJ,cAAA,GAAAsB,CAAA;QAEtC,IAAM+E,eAAe,IAAArG,cAAA,GAAAC,CAAA,QAAGkG,SAAS,CAACd,GAAG;UAAA,IAAAiB,KAAA,GAAAtF,iBAAA,CAAC,WAAOuF,GAAG,EAAK;YAAAvG,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAC,CAAA;YACnD,IAAI;cACF,IAAMyF,SAAS,IAAA1F,cAAA,GAAAC,CAAA,cAASmG,MAAI,CAACvB,aAAa,CAAC0B,GAAG,EAAAhD,MAAA,CAAAC,MAAA,KACzCtC,OAAO;gBACVkB,QAAQ,EAAE;cAAM,EACjB,CAAC;cAACpC,cAAA,GAAAC,CAAA;cAGH,MAAMmG,MAAI,CAACI,kBAAkB,CAACd,SAAS,CAAChC,YAAY,CAAC;YACvD,CAAC,CAAC,OAAOc,KAAK,EAAE;cAAAxE,cAAA,GAAAC,CAAA;cACdwE,OAAO,CAACgC,IAAI,CAAC,4BAA4BF,GAAG,EAAE,EAAE/B,KAAK,CAAC;YACxD;UACF,CAAC;UAAA,iBAAAkC,GAAA;YAAA,OAAAJ,KAAA,CAAAvB,KAAA,OAAA5D,SAAA;UAAA;QAAA,IAAC;QAACnB,cAAA,GAAAC,CAAA;QAEH,MAAM4F,OAAO,CAACC,GAAG,CAACO,eAAe,CAAC;MACpC,CAAC;MAAA,SAnBKM,aAAaA,CAAAC,GAAA;QAAA,OAAAV,cAAA,CAAAnB,KAAA,OAAA5D,SAAA;MAAA;MAAA,OAAbwF,aAAa;IAAA;EAAA;IAAA9F,GAAA;IAAAC,KAAA;MAAA,IAAA+F,+BAAA,GAAA7F,iBAAA,CAwBnB,WACEC,QAAgB,EAMf;QAAAjB,cAAA,GAAAsB,CAAA;QACD,IAAMsC,QAAQ,IAAA5D,cAAA,GAAAC,CAAA,cAAS,IAAI,CAAC4D,gBAAgB,CAAC5C,QAAQ,CAAC;QACtD,IAAM6F,eAAyB,IAAA9G,cAAA,GAAAC,CAAA,QAAG,EAAE;QACpC,IAAI8G,gBAAgB,IAAA/G,cAAA,GAAAC,CAAA,QAAG,CAAC;QAGxB,IAAMgD,aAAa,IAAAjD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACiD,sBAAsB,CAAC,MAAM,EAAEjC,QAAQ,CAAC;QAACjB,cAAA,GAAAC,CAAA;QACpE,IAAI2D,QAAQ,CAAC5B,MAAM,KAAKiB,aAAa,EAAE;UAAAjD,cAAA,GAAAI,CAAA;UAAAJ,cAAA,GAAAC,CAAA;UACrC6G,eAAe,CAACE,IAAI,CAAC,cAAc/D,aAAa,SAAS,CAAC;UAACjD,cAAA,GAAAC,CAAA;UAC3D8G,gBAAgB,IAAI,EAAE;QACxB,CAAC;UAAA/G,cAAA,GAAAI,CAAA;QAAA;QAGD,IAAM+C,iBAAiB,IAAAnD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACmD,0BAA0B,CAAC,CAAC;QAACpD,cAAA,GAAAC,CAAA;QAC5D,IAAI2D,QAAQ,CAACjC,KAAK,GAAGwB,iBAAiB,CAACxB,KAAK,GAAG,CAAC,EAAE;UAAA3B,cAAA,GAAAI,CAAA;UAAAJ,cAAA,GAAAC,CAAA;UAChD6G,eAAe,CAACE,IAAI,CAAC,oCAAoC,CAAC;UAAChH,cAAA,GAAAC,CAAA;UAC3D8G,gBAAgB,IAAI,EAAE;QACxB,CAAC;UAAA/G,cAAA,GAAAI,CAAA;QAAA;QAAAJ,cAAA,GAAAC,CAAA;QAGD,IAAI2D,QAAQ,CAACe,IAAI,GAAG,MAAM,EAAE;UAAA3E,cAAA,GAAAI,CAAA;UAAAJ,cAAA,GAAAC,CAAA;UAC1B6G,eAAe,CAACE,IAAI,CAAC,8CAA8C,CAAC;UAAChH,cAAA,GAAAC,CAAA;UACrE8G,gBAAgB,IAAI,EAAE;QACxB,CAAC;UAAA/G,cAAA,GAAAI,CAAA;QAAA;QAAAJ,cAAA,GAAAC,CAAA;QAGD6G,eAAe,CAACE,IAAI,CAAC,4BAA4B,CAAC;QAAChH,cAAA,GAAAC,CAAA;QAEnD,OAAO;UACL6G,eAAe,EAAfA,eAAe;UACfC,gBAAgB,EAAEE,IAAI,CAACC,GAAG,CAACH,gBAAgB,EAAE,EAAE,CAAC;UAChD9D,aAAa,EAAbA,aAAa;UACbE,iBAAiB,EAAjBA;QACF,CAAC;MACH,CAAC;MAAA,SAzCKgE,8BAA8BA,CAAAC,GAAA;QAAA,OAAAP,+BAAA,CAAA9B,KAAA,OAAA5D,SAAA;MAAA;MAAA,OAA9BgG,8BAA8B;IAAA;EAAA;IAAAtG,GAAA;IAAAC,KAAA;MAAA,IAAAuG,uBAAA,GAAArG,iBAAA,CA8CpC,aAA8C;QAAAhB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAC,CAAA;QAC5C,MAAMN,oBAAoB,CAAC2H,UAAU,CAAC,CAAC,oBAAoB,CAAC,CAAC;MAC/D,CAAC;MAAA,SAFKC,sBAAsBA,CAAA;QAAA,OAAAF,uBAAA,CAAAtC,KAAA,OAAA5D,SAAA;MAAA;MAAA,OAAtBoG,sBAAsB;IAAA;EAAA;IAAA1G,GAAA;IAAAC,KAAA,EAO5B,SAAA0G,oBAAoBA,CAAA,EAKlB;MAAAxH,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAC,CAAA;MAEA,OAAO;QACLwH,kBAAkB,EAAE,CAAC;QACrBC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACfC,kBAAkB,EAAE,CAAC;MACvB,CAAC;IACH;EAAC;IAAA/G,GAAA;IAAAC,KAAA,EAID,SAAQgC,gBAAgBA,CAACyD,GAAW,EAAErF,OAAiC,EAAU;MAAAlB,cAAA,GAAAsB,CAAA;MAC/E,IAAMuG,WAAW,IAAA7H,cAAA,GAAAC,CAAA,QAAG6H,IAAI,CAACC,SAAS,CAAC7G,OAAO,CAAC;MAAClB,cAAA,GAAAC,CAAA;MAC5C,OAAO,WAAW+H,IAAI,CAACzB,GAAG,CAAC,CAAC0B,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,IAAID,IAAI,CAACH,WAAW,CAAC,CAACI,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IAC9E;EAAC;IAAApH,GAAA;IAAAC,KAAA,EAED,SAAQoC,sBAAsBA,CAAClB,MAAc,EAAEf,QAAgB,EAAU;MAAAjB,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAC,CAAA;MACvE,IAAI+B,MAAM,KAAK,MAAM,EAAE;QAAAhC,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAA,OAAO+B,MAAM;MAAA,CAAC;QAAAhC,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAGrC,IAAIP,QAAQ,CAACwI,EAAE,KAAK,SAAS,EAAE;QAAAlI,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAC7B,OAAO,MAAM;MACf,CAAC;QAAAD,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAED,IAAIP,QAAQ,CAACwI,EAAE,KAAK,KAAK,EAAE;QAAAlI,cAAA,GAAAI,CAAA;QACzB,IAAM+H,UAAU,IAAAnI,cAAA,GAAAC,CAAA,QAAGmI,QAAQ,CAAC1I,QAAQ,CAAC2I,OAAO,EAAY,EAAE,CAAC;QAACrI,cAAA,GAAAC,CAAA;QAC5D,OAAOkI,UAAU,IAAI,EAAE,IAAAnI,cAAA,GAAAI,CAAA,WAAG,MAAM,KAAAJ,cAAA,GAAAI,CAAA,WAAG,MAAM;MAC3C,CAAC;QAAAJ,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAGD,IAAI,CAAAD,cAAA,GAAAI,CAAA,WAAAa,QAAQ,CAACqH,QAAQ,CAAC,MAAM,CAAC,MAAAtI,cAAA,GAAAI,CAAA,WAAIa,QAAQ,CAACqH,QAAQ,CAAC,OAAO,CAAC,GAAE;QAAAtI,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAC3D,OAAO,KAAK;MACd,CAAC;QAAAD,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAED,OAAO,MAAM;IACf;EAAC;IAAAY,GAAA;IAAAC,KAAA,EAED,SAAQsC,0BAA0BA,CAChCmF,cAAuB,EACvBC,eAAwB,EACW;MAAAxI,cAAA,GAAAsB,CAAA;MACnC,IAAMmH,QAAQ,IAAAzI,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,UAAU,CAAC4B,KAAK,GAAG,IAAI,CAACxB,gBAAgB;MAC9D,IAAMuI,SAAS,IAAA1I,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,UAAU,CAAC6B,MAAM,GAAG,IAAI,CAACzB,gBAAgB;MAACH,cAAA,GAAAC,CAAA;MAEjE,OAAO;QACL0B,KAAK,EAAE4G,cAAc,IAAAvI,cAAA,GAAAI,CAAA,WAAG6G,IAAI,CAACC,GAAG,CAACqB,cAAc,EAAEE,QAAQ,CAAC,KAAAzI,cAAA,GAAAI,CAAA,WAAGqI,QAAQ;QACrE7G,MAAM,EAAE4G,eAAe,IAAAxI,cAAA,GAAAI,CAAA,WAAG6G,IAAI,CAACC,GAAG,CAACsB,eAAe,EAAEE,SAAS,CAAC,KAAA1I,cAAA,GAAAI,CAAA,WAAGsI,SAAS;MAC5E,CAAC;IACH;EAAC;IAAA7H,GAAA;IAAAC,KAAA,EAED,SAAQ2C,sBAAsBA,CAAC3B,OAAe,EAAU;MAAA9B,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAC,CAAA;MAEtD,IAAI,IAAI,CAACE,gBAAgB,GAAG,CAAC,EAAE;QAAAH,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAC7B,OAAOgH,IAAI,CAAC0B,GAAG,CAAC7G,OAAO,GAAG,EAAE,EAAE,EAAE,CAAC;MACnC,CAAC;QAAA9B,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAED,OAAO6B,OAAO;IAChB;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAED,SAAQwC,uBAAuBA,CAACpC,OAAiC,EAAuB;MAAAlB,cAAA,GAAAsB,CAAA;MACtF,IAAM+B,MAA2B,IAAArD,cAAA,GAAAC,CAAA,QAAG,CAAC,CAAC;MAACD,cAAA,GAAAC,CAAA;MAEvC,IAAIiB,OAAO,CAACS,KAAK,EAAE;QAAA3B,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAAoD,MAAM,CAACuF,CAAC,GAAG1H,OAAO,CAACS,KAAK;MAAA,CAAC;QAAA3B,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAC5C,IAAIiB,OAAO,CAACU,MAAM,EAAE;QAAA5B,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAAoD,MAAM,CAACwF,CAAC,GAAG3H,OAAO,CAACU,MAAM;MAAA,CAAC;QAAA5B,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAC9C,IAAIiB,OAAO,CAACY,OAAO,EAAE;QAAA9B,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAAoD,MAAM,CAACyF,CAAC,GAAG5H,OAAO,CAACY,OAAO;MAAA,CAAC;QAAA9B,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAChD,IAAIiB,OAAO,CAACc,MAAM,EAAE;QAAAhC,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAAoD,MAAM,CAAC/B,CAAC,GAAGJ,OAAO,CAACc,MAAM;MAAA,CAAC;QAAAhC,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAC9C,IAAIiB,OAAO,CAACgB,GAAG,EAAE;QAAAlC,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAAoD,MAAM,CAACnB,GAAG,GAAGhB,OAAO,CAACgB,GAAG;MAAA,CAAC;QAAAlC,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAC1C,IAAIiB,OAAO,CAACoB,WAAW,EAAE;QAAAtC,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAAoD,MAAM,CAACf,WAAW,GAAG,MAAM;MAAA,CAAC;QAAAtC,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MACrD,IAAIiB,OAAO,CAACqB,IAAI,EAAE;QAAAvC,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAAoD,MAAM,CAACd,IAAI,GAAGrB,OAAO,CAACqB,IAAI;MAAA,CAAC;QAAAvC,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAC7C,IAAIiB,OAAO,CAACuB,OAAO,EAAE;QAAAzC,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAAoD,MAAM,CAACZ,OAAO,GAAG,MAAM;MAAA,CAAC;QAAAzC,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAC7C,IAAIiB,OAAO,CAACwB,UAAU,EAAE;QAAA1C,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAAoD,MAAM,CAACX,UAAU,GAAGxB,OAAO,CAACwB,UAAU;MAAA,CAAC;QAAA1C,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAC/D,IAAIiB,OAAO,CAACyB,QAAQ,EAAE;QAAA3C,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAAoD,MAAM,CAACV,QAAQ,GAAGzB,OAAO,CAACyB,QAAQ;MAAA,CAAC;QAAA3C,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MACzD,IAAIiB,OAAO,CAAC0B,UAAU,EAAE;QAAA5C,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAAoD,MAAM,CAACT,UAAU,GAAG1B,OAAO,CAAC0B,UAAU;MAAA,CAAC;QAAA5C,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAG/DoD,MAAM,CAAC0F,GAAG,GAAG,IAAI,CAAC5I,gBAAgB;MAACH,cAAA,GAAAC,CAAA;MAGnCoD,MAAM,CAAC2F,IAAI,GAAG,iBAAiB;MAAChJ,cAAA,GAAAC,CAAA;MAEhC,OAAOoD,MAAM;IACf;EAAC;IAAAxC,GAAA;IAAAC,KAAA,EAED,SAAQ6C,kBAAkBA,CAAC4C,GAAW,EAAElD,MAA2B,EAAU;MAAArD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAC,CAAA;MAC3E,IAAI;QACF,IAAMgJ,MAAM,IAAAjJ,cAAA,GAAAC,CAAA,SAAG,IAAIiJ,GAAG,CAAC3C,GAAG,CAAC;QAACvG,cAAA,GAAAC,CAAA;QAE5BsD,MAAM,CAAC6B,OAAO,CAAC/B,MAAM,CAAC,CAAC8F,OAAO,CAAC,UAAAC,KAAA,EAAkB;UAAA,IAAAC,KAAA,GAAA5D,cAAA,CAAA2D,KAAA;YAAhBvI,GAAG,GAAAwI,KAAA;YAAEvI,KAAK,GAAAuI,KAAA;UAAArJ,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAC,CAAA;UACzCgJ,MAAM,CAACK,YAAY,CAAClF,GAAG,CAACvD,GAAG,EAAE0I,MAAM,CAACzI,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC;QAACd,cAAA,GAAAC,CAAA;QAEH,OAAOgJ,MAAM,CAACO,QAAQ,CAAC,CAAC;MAC1B,CAAC,CAAC,OAAOhF,KAAK,EAAE;QAEd,IAAMiF,SAAS,IAAAzJ,cAAA,GAAAC,CAAA,SAAGsG,GAAG,CAAC+B,QAAQ,CAAC,GAAG,CAAC,IAAAtI,cAAA,GAAAI,CAAA,WAAG,GAAG,KAAAJ,cAAA,GAAAI,CAAA,WAAG,GAAG;QAC/C,IAAMsJ,WAAW,IAAA1J,cAAA,GAAAC,CAAA,SAAGsD,MAAM,CAAC6B,OAAO,CAAC/B,MAAM,CAAC,CACvCgC,GAAG,CAAC,UAAAsE,KAAA,EAAkB;UAAA,IAAAC,KAAA,GAAAnE,cAAA,CAAAkE,KAAA;YAAhB9I,GAAG,GAAA+I,KAAA;YAAE9I,KAAK,GAAA8I,KAAA;UAAA5J,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAC,CAAA;UAAM,UAAGY,GAAG,IAAIgJ,kBAAkB,CAAC/I,KAAK,CAAC,EAAE;QAAD,CAAC,CAAC,CAC5DgJ,IAAI,CAAC,GAAG,CAAC;QAAC9J,cAAA,GAAAC,CAAA;QAEb,OAAO,GAAGsG,GAAG,GAAGkD,SAAS,GAAGC,WAAW,EAAE;MAC3C;IACF;EAAC;IAAA7I,GAAA;IAAAC,KAAA;MAAA,IAAAiJ,iBAAA,GAAA/I,iBAAA,CAED,WAA+BuF,GAAW,EAA0B;QAAAvG,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAC,CAAA;QAGlE,OAAO;UACL0B,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,IAAI;UACZI,MAAM,EAAE,MAAM;UACd2C,IAAI,EAAE,MAAM;UACZC,WAAW,EAAE,IAAI,GAAG,IAAI;UACxBoF,UAAU,EAAE,MAAM;UAClBC,QAAQ,EAAE;QACZ,CAAC;MACH,CAAC;MAAA,SAZapG,gBAAgBA,CAAAqG,GAAA;QAAA,OAAAH,iBAAA,CAAAhF,KAAA,OAAA5D,SAAA;MAAA;MAAA,OAAhB0C,gBAAgB;IAAA;EAAA;IAAAhD,GAAA;IAAAC,KAAA,EAc9B,SAAQiD,gBAAgBA,CAACH,QAAuB,EAAE1C,OAAiC,EAAU;MAAAlB,cAAA,GAAAsB,CAAA;MAC3F,IAAI6I,OAAO,IAAAnK,cAAA,GAAAC,CAAA,SAAG,CAAC;MAACD,cAAA,GAAAC,CAAA;MAGhB,IAAIiB,OAAO,CAACc,MAAM,KAAK,MAAM,EAAE;QAAAhC,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAAkK,OAAO,IAAI,EAAE;MAAA,CAAC;QAAAnK,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAG7C,IAAI,CAAAD,cAAA,GAAAI,CAAA,WAAAc,OAAO,CAACY,OAAO,MAAA9B,cAAA,GAAAI,CAAA,WAAIc,OAAO,CAACY,OAAO,GAAG,EAAE,GAAE;QAAA9B,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAAkK,OAAO,IAAI,EAAE;MAAA,CAAC;QAAAnK,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAG3D,IAAI,CAAAD,cAAA,GAAAI,CAAA,WAAAc,OAAO,CAACS,KAAK,MAAA3B,cAAA,GAAAI,CAAA,WAAIc,OAAO,CAACS,KAAK,GAAGiC,QAAQ,CAACjC,KAAK,GAAE;QAAA3B,cAAA,GAAAI,CAAA;QAAAJ,cAAA,GAAAC,CAAA;QAAAkK,OAAO,IAAI,EAAE;MAAA,CAAC;QAAAnK,cAAA,GAAAI,CAAA;MAAA;MAAAJ,cAAA,GAAAC,CAAA;MAEnE,OAAOgH,IAAI,CAACC,GAAG,CAACiD,OAAO,EAAE,EAAE,CAAC;IAC9B;EAAC;IAAAtJ,GAAA;IAAAC,KAAA;MAAA,IAAAsJ,mBAAA,GAAApJ,iBAAA,CAED,WAAiCuF,GAAW,EAAiB;QAAAvG,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAC,CAAA;QAC3D,OAAO,IAAI4F,OAAO,CAAC,UAACwE,OAAO,EAAEC,MAAM,EAAK;UAAAtK,cAAA,GAAAsB,CAAA;UACtC,IAAMiJ,KAAK,IAAAvK,cAAA,GAAAC,CAAA,SAAG,IAAIuK,KAAK,CAAC,CAAC;UAACxK,cAAA,GAAAC,CAAA;UAC1BsK,KAAK,CAACE,MAAM,GAAG,YAAM;YAAAzK,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAC,CAAA;YAAA,OAAAoK,OAAO,CAAC,CAAC;UAAD,CAAC;UAACrK,cAAA,GAAAC,CAAA;UAC/BsK,KAAK,CAACG,OAAO,GAAGJ,MAAM;UAACtK,cAAA,GAAAC,CAAA;UACvBsK,KAAK,CAACI,GAAG,GAAGpE,GAAG;QACjB,CAAC,CAAC;MACJ,CAAC;MAAA,SAPaC,kBAAkBA,CAAAoE,GAAA;QAAA,OAAAR,mBAAA,CAAArF,KAAA,OAAA5D,SAAA;MAAA;MAAA,OAAlBqF,kBAAkB;IAAA;EAAA;AAAA;AAWlC,OAAO,IAAMqE,wBAAwB,IAAA7K,cAAA,GAAAC,CAAA,SAAG,IAAIJ,wBAAwB,CAAC,CAAC;AACtE,eAAegL,wBAAwB", "ignoreList": []}