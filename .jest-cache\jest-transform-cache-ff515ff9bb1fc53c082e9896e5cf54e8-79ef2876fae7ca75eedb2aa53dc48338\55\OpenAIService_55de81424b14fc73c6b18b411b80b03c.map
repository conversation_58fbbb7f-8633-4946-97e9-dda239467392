{"version": 3, "names": ["handleError", "logError", "env", "OpenAIService", "_classCallCheck", "apiBaseUrl", "cov_2eowt29e6q", "s", "model", "f", "<PERSON><PERSON><PERSON><PERSON>", "b", "get", "_createClass", "key", "value", "_generateTennisCoaching", "_asyncToGenerator", "request", "_data$choices$", "Error", "prompt", "buildCoachingPrompt", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "messages", "role", "content", "temperature", "max_tokens", "ok", "_error$error", "error", "json", "message", "data", "choices", "parseCoachingResponse", "appError", "show<PERSON><PERSON><PERSON>", "context", "getFallbackCoachingResponse", "generateTennisCoaching", "_x", "apply", "arguments", "_analyzeMatchPerformance", "_data$choices$2", "buildMatchAnalysisPrompt", "_error$error2", "parseMatchAnalysisResponse", "getFallbackMatchAnalysis", "analyzeMatchPerformance", "_x2", "_generateTrainingPlan", "_data$choices$3", "buildTrainingPlanPrompt", "_error$error3", "parseTrainingPlanResponse", "getFallbackTrainingPlan", "generateTrainingPlan", "_x3", "strokeType", "userLevel", "biomechanicsScores", "preparation", "execution", "follow<PERSON><PERSON><PERSON>", "timing", "balance", "technicalAnalysis", "bodyRotation", "weightTransfer", "footwork", "contactPoint", "optimal", "specificConcerns", "join", "trim", "matchData", "result", "toUpperCase", "score", "duration", "surface", "opponentLevel", "statistics", "aces", "doubleFaults", "firstServePercentage", "winnersCount", "unforcedErrors", "breakPointsConverted", "breakPointsFaced", "userNotes", "timeframe", "userProfile", "level", "goals", "availableTime", "strengths", "weaknesses", "focusAreas", "sections", "split", "overallAssessment", "extractSection", "extractListItems", "improvements", "recommendations", "drillSuggestions", "name", "description", "difficulty", "technicalTips", "mentalTips", "performanceAnalysis", "keyInsights", "areasForImprovement", "tacticalSuggestions", "nextMatchStrategy", "planOverview", "weeklySchedule", "week", "focus", "sessions", "day", "type", "activities", "progressMilestones", "milestone", "assessmentCriteria", "keyword", "lines", "i", "length", "toLowerCase", "includes", "items", "inSection", "line", "startsWith", "match", "push", "replace", "openAIService"], "sources": ["OpenAIService.ts"], "sourcesContent": ["/**\n * OpenAI Service\n * \n * AI-powered coaching and analysis using OpenAI GPT models\n */\n\nimport { handleError, logError } from '@/utils/errorHandling';\nimport env from '@/config/environment';\n\nexport interface TennisCoachingRequest {\n  strokeType: 'serve' | 'forehand' | 'backhand' | 'volley' | 'overhead' | 'slice';\n  biomechanicsScores: {\n    preparation: number;\n    execution: number;\n    followThrough: number;\n    timing: number;\n    balance: number;\n  };\n  technicalAnalysis: {\n    bodyRotation: number;\n    weightTransfer: number;\n    footwork: number;\n    contactPoint: { optimal: boolean };\n  };\n  userLevel?: 'beginner' | 'intermediate' | 'advanced' | 'professional';\n  specificConcerns?: string[];\n}\n\nexport interface CoachingResponse {\n  overallAssessment: string;\n  strengths: string[];\n  improvements: string[];\n  recommendations: string[];\n  drillSuggestions: Array<{\n    name: string;\n    description: string;\n    difficulty: 'easy' | 'medium' | 'hard';\n    duration: string;\n  }>;\n  technicalTips: string[];\n  mentalTips: string[];\n}\n\nexport interface MatchAnalysisRequest {\n  matchData: {\n    result: 'win' | 'loss';\n    score: string;\n    duration: number;\n    surface: 'hard' | 'clay' | 'grass' | 'indoor';\n    opponentLevel: string;\n  };\n  statistics: {\n    aces: number;\n    doubleFaults: number;\n    firstServePercentage: number;\n    winnersCount: number;\n    unforcedErrors: number;\n    breakPointsConverted: number;\n    breakPointsFaced: number;\n  };\n  userNotes?: string;\n}\n\nexport interface MatchAnalysisResponse {\n  performanceAnalysis: string;\n  keyInsights: string[];\n  areasForImprovement: string[];\n  tacticalSuggestions: string[];\n  nextMatchStrategy: string[];\n}\n\nexport interface TrainingPlanRequest {\n  userProfile: {\n    level: 'beginner' | 'intermediate' | 'advanced' | 'professional';\n    goals: string[];\n    availableTime: number; // hours per week\n    weaknesses: string[];\n    strengths: string[];\n  };\n  timeframe: number; // weeks\n  focusAreas: string[];\n}\n\nexport interface TrainingPlanResponse {\n  planOverview: string;\n  weeklySchedule: Array<{\n    week: number;\n    focus: string;\n    sessions: Array<{\n      day: string;\n      type: 'technique' | 'fitness' | 'match_play' | 'mental';\n      duration: number;\n      activities: string[];\n    }>;\n  }>;\n  progressMilestones: Array<{\n    week: number;\n    milestone: string;\n    assessmentCriteria: string[];\n  }>;\n}\n\nclass OpenAIService {\n  private apiKey: string;\n  private apiBaseUrl: string = 'https://api.openai.com/v1';\n  private model: string = 'gpt-4';\n\n  constructor() {\n    this.apiKey = env.get('OPENAI_API_KEY') || '';\n  }\n\n  /**\n   * Generate tennis coaching insights\n   */\n  async generateTennisCoaching(request: TennisCoachingRequest): Promise<CoachingResponse> {\n    try {\n      if (!this.apiKey) {\n        throw new Error('OpenAI API key not configured');\n      }\n\n      const prompt = this.buildCoachingPrompt(request);\n      \n      const response = await fetch(`${this.apiBaseUrl}/chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${this.apiKey}`,\n        },\n        body: JSON.stringify({\n          model: this.model,\n          messages: [\n            {\n              role: 'system',\n              content: 'You are a professional tennis coach with 20+ years of experience coaching players at all levels. Provide detailed, actionable coaching advice based on technical analysis data.',\n            },\n            {\n              role: 'user',\n              content: prompt,\n            },\n          ],\n          temperature: 0.7,\n          max_tokens: 1500,\n        }),\n      });\n\n      if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.error?.message || 'OpenAI API request failed');\n      }\n\n      const data = await response.json();\n      const content = data.choices[0]?.message?.content;\n\n      if (!content) {\n        throw new Error('No response from OpenAI');\n      }\n\n      return this.parseCoachingResponse(content);\n    } catch (error) {\n      const appError = handleError(error, { showAlert: false });\n      logError(appError, { context: 'generateTennisCoaching', request });\n      \n      // Return fallback coaching response\n      return this.getFallbackCoachingResponse(request);\n    }\n  }\n\n  /**\n   * Analyze match performance\n   */\n  async analyzeMatchPerformance(request: MatchAnalysisRequest): Promise<MatchAnalysisResponse> {\n    try {\n      if (!this.apiKey) {\n        throw new Error('OpenAI API key not configured');\n      }\n\n      const prompt = this.buildMatchAnalysisPrompt(request);\n      \n      const response = await fetch(`${this.apiBaseUrl}/chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${this.apiKey}`,\n        },\n        body: JSON.stringify({\n          model: this.model,\n          messages: [\n            {\n              role: 'system',\n              content: 'You are a tennis analyst and coach. Analyze match statistics and provide insights for improvement.',\n            },\n            {\n              role: 'user',\n              content: prompt,\n            },\n          ],\n          temperature: 0.6,\n          max_tokens: 1200,\n        }),\n      });\n\n      if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.error?.message || 'OpenAI API request failed');\n      }\n\n      const data = await response.json();\n      const content = data.choices[0]?.message?.content;\n\n      if (!content) {\n        throw new Error('No response from OpenAI');\n      }\n\n      return this.parseMatchAnalysisResponse(content);\n    } catch (error) {\n      const appError = handleError(error, { showAlert: false });\n      logError(appError, { context: 'analyzeMatchPerformance', request });\n      \n      return this.getFallbackMatchAnalysis(request);\n    }\n  }\n\n  /**\n   * Generate personalized training plan\n   */\n  async generateTrainingPlan(request: TrainingPlanRequest): Promise<TrainingPlanResponse> {\n    try {\n      if (!this.apiKey) {\n        throw new Error('OpenAI API key not configured');\n      }\n\n      const prompt = this.buildTrainingPlanPrompt(request);\n      \n      const response = await fetch(`${this.apiBaseUrl}/chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${this.apiKey}`,\n        },\n        body: JSON.stringify({\n          model: this.model,\n          messages: [\n            {\n              role: 'system',\n              content: 'You are a professional tennis coach specializing in creating personalized training programs. Design comprehensive training plans based on player profiles and goals.',\n            },\n            {\n              role: 'user',\n              content: prompt,\n            },\n          ],\n          temperature: 0.7,\n          max_tokens: 2000,\n        }),\n      });\n\n      if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.error?.message || 'OpenAI API request failed');\n      }\n\n      const data = await response.json();\n      const content = data.choices[0]?.message?.content;\n\n      if (!content) {\n        throw new Error('No response from OpenAI');\n      }\n\n      return this.parseTrainingPlanResponse(content);\n    } catch (error) {\n      const appError = handleError(error, { showAlert: false });\n      logError(appError, { context: 'generateTrainingPlan', request });\n      \n      return this.getFallbackTrainingPlan(request);\n    }\n  }\n\n  /**\n   * Build coaching prompt\n   */\n  private buildCoachingPrompt(request: TennisCoachingRequest): string {\n    return `\nAnalyze this tennis stroke and provide coaching advice:\n\nStroke Type: ${request.strokeType}\nPlayer Level: ${request.userLevel || 'intermediate'}\n\nBiomechanics Scores (0-100):\n- Preparation: ${request.biomechanicsScores.preparation}\n- Execution: ${request.biomechanicsScores.execution}\n- Follow-through: ${request.biomechanicsScores.followThrough}\n- Timing: ${request.biomechanicsScores.timing}\n- Balance: ${request.biomechanicsScores.balance}\n\nTechnical Analysis:\n- Body Rotation: ${request.technicalAnalysis.bodyRotation}/100\n- Weight Transfer: ${request.technicalAnalysis.weightTransfer}/100\n- Footwork: ${request.technicalAnalysis.footwork}/100\n- Contact Point: ${request.technicalAnalysis.contactPoint.optimal ? 'Optimal' : 'Needs improvement'}\n\n${request.specificConcerns ? `Specific Concerns: ${request.specificConcerns.join(', ')}` : ''}\n\nPlease provide:\n1. Overall assessment\n2. Top 3 strengths\n3. Top 3 areas for improvement\n4. Specific recommendations\n5. 2-3 drill suggestions with difficulty levels\n6. Technical tips\n7. Mental game advice\n\nFormat your response clearly with sections.\n    `.trim();\n  }\n\n  /**\n   * Build match analysis prompt\n   */\n  private buildMatchAnalysisPrompt(request: MatchAnalysisRequest): string {\n    return `\nAnalyze this tennis match performance:\n\nMatch Result: ${request.matchData.result.toUpperCase()}\nScore: ${request.matchData.score}\nDuration: ${request.matchData.duration} minutes\nSurface: ${request.matchData.surface}\nOpponent Level: ${request.matchData.opponentLevel}\n\nStatistics:\n- Aces: ${request.statistics.aces}\n- Double Faults: ${request.statistics.doubleFaults}\n- First Serve %: ${request.statistics.firstServePercentage}%\n- Winners: ${request.statistics.winnersCount}\n- Unforced Errors: ${request.statistics.unforcedErrors}\n- Break Points Converted: ${request.statistics.breakPointsConverted}/${request.statistics.breakPointsFaced}\n\n${request.userNotes ? `Player Notes: ${request.userNotes}` : ''}\n\nPlease provide:\n1. Performance analysis\n2. Key insights from the match\n3. Areas for improvement\n4. Tactical suggestions\n5. Strategy for next match\n\nBe specific and actionable in your advice.\n    `.trim();\n  }\n\n  /**\n   * Build training plan prompt\n   */\n  private buildTrainingPlanPrompt(request: TrainingPlanRequest): string {\n    return `\nCreate a ${request.timeframe}-week tennis training plan:\n\nPlayer Profile:\n- Level: ${request.userProfile.level}\n- Goals: ${request.userProfile.goals.join(', ')}\n- Available Time: ${request.userProfile.availableTime} hours/week\n- Strengths: ${request.userProfile.strengths.join(', ')}\n- Weaknesses: ${request.userProfile.weaknesses.join(', ')}\n\nFocus Areas: ${request.focusAreas.join(', ')}\n\nPlease provide:\n1. Plan overview\n2. Weekly schedule breakdown\n3. Progress milestones\n4. Session types and activities\n5. Assessment criteria\n\nMake it practical and progressive.\n    `.trim();\n  }\n\n  /**\n   * Parse coaching response\n   */\n  private parseCoachingResponse(content: string): CoachingResponse {\n    // Simple parsing - in a real implementation, you might use more sophisticated parsing\n    const sections = content.split('\\n\\n');\n    \n    return {\n      overallAssessment: this.extractSection(content, 'assessment') || 'Good technique with room for improvement.',\n      strengths: this.extractListItems(content, 'strength') || ['Consistent contact', 'Good balance'],\n      improvements: this.extractListItems(content, 'improvement') || ['Work on follow-through', 'Improve timing'],\n      recommendations: this.extractListItems(content, 'recommendation') || ['Practice with a coach', 'Focus on fundamentals'],\n      drillSuggestions: [\n        { name: 'Shadow Swings', description: 'Practice stroke motion without ball', difficulty: 'easy', duration: '10 minutes' },\n        { name: 'Wall Practice', description: 'Hit against wall for consistency', difficulty: 'medium', duration: '15 minutes' },\n      ],\n      technicalTips: this.extractListItems(content, 'technical') || ['Keep eye on ball', 'Follow through completely'],\n      mentalTips: this.extractListItems(content, 'mental') || ['Stay focused', 'Trust your technique'],\n    };\n  }\n\n  /**\n   * Parse match analysis response\n   */\n  private parseMatchAnalysisResponse(content: string): MatchAnalysisResponse {\n    return {\n      performanceAnalysis: this.extractSection(content, 'performance') || 'Solid overall performance with areas to improve.',\n      keyInsights: this.extractListItems(content, 'insight') || ['Good serving performance', 'Need to reduce errors'],\n      areasForImprovement: this.extractListItems(content, 'improvement') || ['Return of serve', 'Net play'],\n      tacticalSuggestions: this.extractListItems(content, 'tactical') || ['Vary serve placement', 'Attack short balls'],\n      nextMatchStrategy: this.extractListItems(content, 'strategy') || ['Focus on consistency', 'Play to strengths'],\n    };\n  }\n\n  /**\n   * Parse training plan response\n   */\n  private parseTrainingPlanResponse(content: string): TrainingPlanResponse {\n    return {\n      planOverview: this.extractSection(content, 'overview') || 'Comprehensive training plan focusing on technique and fitness.',\n      weeklySchedule: [\n        {\n          week: 1,\n          focus: 'Foundation',\n          sessions: [\n            { day: 'Monday', type: 'technique', duration: 60, activities: ['Forehand practice', 'Footwork drills'] },\n            { day: 'Wednesday', type: 'fitness', duration: 45, activities: ['Cardio', 'Agility training'] },\n            { day: 'Friday', type: 'match_play', duration: 90, activities: ['Practice match', 'Point play'] },\n          ],\n        },\n      ],\n      progressMilestones: [\n        { week: 2, milestone: 'Improved consistency', assessmentCriteria: ['10 consecutive forehands', 'Proper footwork'] },\n        { week: 4, milestone: 'Match readiness', assessmentCriteria: ['Competitive match play', 'Strategic thinking'] },\n      ],\n    };\n  }\n\n  /**\n   * Extract section from content\n   */\n  private extractSection(content: string, keyword: string): string | null {\n    const lines = content.split('\\n');\n    for (let i = 0; i < lines.length; i++) {\n      if (lines[i].toLowerCase().includes(keyword)) {\n        return lines[i + 1] || null;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Extract list items from content\n   */\n  private extractListItems(content: string, keyword: string): string[] | null {\n    const lines = content.split('\\n');\n    const items: string[] = [];\n    let inSection = false;\n    \n    for (const line of lines) {\n      if (line.toLowerCase().includes(keyword)) {\n        inSection = true;\n        continue;\n      }\n      \n      if (inSection && (line.startsWith('-') || line.startsWith('•') || line.match(/^\\d+\\./))) {\n        items.push(line.replace(/^[-•\\d.]\\s*/, '').trim());\n      } else if (inSection && line.trim() === '') {\n        break;\n      }\n    }\n    \n    return items.length > 0 ? items : null;\n  }\n\n  /**\n   * Fallback coaching response\n   */\n  private getFallbackCoachingResponse(request: TennisCoachingRequest): CoachingResponse {\n    return {\n      overallAssessment: `Your ${request.strokeType} shows good fundamentals with room for improvement in timing and consistency.`,\n      strengths: ['Good preparation', 'Consistent contact point'],\n      improvements: ['Improve follow-through', 'Work on timing', 'Enhance balance'],\n      recommendations: ['Practice with a coach', 'Focus on slow, controlled swings', 'Work on footwork'],\n      drillSuggestions: [\n        { name: 'Shadow Swings', description: 'Practice stroke motion without ball', difficulty: 'easy', duration: '10 minutes' },\n        { name: 'Wall Practice', description: 'Hit against wall for consistency', difficulty: 'medium', duration: '15 minutes' },\n      ],\n      technicalTips: ['Keep your eye on the ball', 'Complete your follow-through', 'Stay balanced'],\n      mentalTips: ['Stay relaxed', 'Trust your technique', 'Focus on process, not outcome'],\n    };\n  }\n\n  /**\n   * Fallback match analysis\n   */\n  private getFallbackMatchAnalysis(request: MatchAnalysisRequest): MatchAnalysisResponse {\n    return {\n      performanceAnalysis: `${request.matchData.result === 'win' ? 'Good' : 'Challenging'} match with valuable learning opportunities.`,\n      keyInsights: ['Serving was a key factor', 'Consistency played a major role'],\n      areasForImprovement: ['Return of serve', 'Reducing unforced errors', 'Net play'],\n      tacticalSuggestions: ['Vary serve placement', 'Be more aggressive on short balls', 'Improve court positioning'],\n      nextMatchStrategy: ['Focus on consistency', 'Play to your strengths', 'Stay mentally tough'],\n    };\n  }\n\n  /**\n   * Fallback training plan\n   */\n  private getFallbackTrainingPlan(request: TrainingPlanRequest): TrainingPlanResponse {\n    return {\n      planOverview: `${request.timeframe}-week progressive training plan focusing on ${request.focusAreas.join(' and ')}.`,\n      weeklySchedule: [\n        {\n          week: 1,\n          focus: 'Foundation Building',\n          sessions: [\n            { day: 'Monday', type: 'technique', duration: 60, activities: ['Stroke fundamentals', 'Footwork'] },\n            { day: 'Wednesday', type: 'fitness', duration: 45, activities: ['Cardio training', 'Agility'] },\n            { day: 'Friday', type: 'match_play', duration: 90, activities: ['Practice points', 'Strategy'] },\n          ],\n        },\n      ],\n      progressMilestones: [\n        { week: 2, milestone: 'Improved stroke consistency', assessmentCriteria: ['Better timing', 'Fewer errors'] },\n        { week: 4, milestone: 'Enhanced match play', assessmentCriteria: ['Strategic thinking', 'Competitive performance'] },\n      ],\n    };\n  }\n}\n\n// Export singleton instance\nexport const openAIService = new OpenAIService();\nexport default openAIService;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,SAASA,WAAW,EAAEC,QAAQ;AAC9B,OAAOC,GAAG;AAA6B,IA+FjCC,aAAa;EAKjB,SAAAA,cAAA,EAAc;IAAAC,eAAA,OAAAD,aAAA;IAAA,KAHNE,UAAU,IAAAC,cAAA,GAAAC,CAAA,OAAW,2BAA2B;IAAA,KAChDC,KAAK,IAAAF,cAAA,GAAAC,CAAA,OAAW,OAAO;IAAAD,cAAA,GAAAG,CAAA;IAAAH,cAAA,GAAAC,CAAA;IAG7B,IAAI,CAACG,MAAM,GAAG,CAAAJ,cAAA,GAAAK,CAAA,UAAAT,GAAG,CAACU,GAAG,CAAC,gBAAgB,CAAC,MAAAN,cAAA,GAAAK,CAAA,UAAI,EAAE;EAC/C;EAAC,OAAAE,YAAA,CAAAV,aAAA;IAAAW,GAAA;IAAAC,KAAA;MAAA,IAAAC,uBAAA,GAAAC,iBAAA,CAKD,WAA6BC,OAA8B,EAA6B;QAAAZ,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QACtF,IAAI;UAAA,IAAAY,cAAA;UAAAb,cAAA,GAAAC,CAAA;UACF,IAAI,CAAC,IAAI,CAACG,MAAM,EAAE;YAAAJ,cAAA,GAAAK,CAAA;YAAAL,cAAA,GAAAC,CAAA;YAChB,MAAM,IAAIa,KAAK,CAAC,+BAA+B,CAAC;UAClD,CAAC;YAAAd,cAAA,GAAAK,CAAA;UAAA;UAED,IAAMU,MAAM,IAAAf,cAAA,GAAAC,CAAA,OAAG,IAAI,CAACe,mBAAmB,CAACJ,OAAO,CAAC;UAEhD,IAAMK,QAAQ,IAAAjB,cAAA,GAAAC,CAAA,aAASiB,KAAK,CAAC,GAAG,IAAI,CAACnB,UAAU,mBAAmB,EAAE;YAClEoB,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClC,eAAe,EAAE,UAAU,IAAI,CAAChB,MAAM;YACxC,CAAC;YACDiB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBrB,KAAK,EAAE,IAAI,CAACA,KAAK;cACjBsB,QAAQ,EAAE,CACR;gBACEC,IAAI,EAAE,QAAQ;gBACdC,OAAO,EAAE;cACX,CAAC,EACD;gBACED,IAAI,EAAE,MAAM;gBACZC,OAAO,EAAEX;cACX,CAAC,CACF;cACDY,WAAW,EAAE,GAAG;cAChBC,UAAU,EAAE;YACd,CAAC;UACH,CAAC,CAAC;UAAC5B,cAAA,GAAAC,CAAA;UAEH,IAAI,CAACgB,QAAQ,CAACY,EAAE,EAAE;YAAA,IAAAC,YAAA;YAAA9B,cAAA,GAAAK,CAAA;YAChB,IAAM0B,KAAK,IAAA/B,cAAA,GAAAC,CAAA,aAASgB,QAAQ,CAACe,IAAI,CAAC,CAAC;YAAChC,cAAA,GAAAC,CAAA;YACpC,MAAM,IAAIa,KAAK,CAAC,CAAAd,cAAA,GAAAK,CAAA,WAAAyB,YAAA,GAAAC,KAAK,CAACA,KAAK,qBAAXD,YAAA,CAAaG,OAAO,MAAAjC,cAAA,GAAAK,CAAA,UAAI,2BAA2B,EAAC;UACtE,CAAC;YAAAL,cAAA,GAAAK,CAAA;UAAA;UAED,IAAM6B,IAAI,IAAAlC,cAAA,GAAAC,CAAA,cAASgB,QAAQ,CAACe,IAAI,CAAC,CAAC;UAClC,IAAMN,OAAO,IAAA1B,cAAA,GAAAC,CAAA,SAAAY,cAAA,GAAGqB,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,cAAAtB,cAAA,GAAfA,cAAA,CAAiBoB,OAAO,qBAAxBpB,cAAA,CAA0Ba,OAAO;UAAC1B,cAAA,GAAAC,CAAA;UAElD,IAAI,CAACyB,OAAO,EAAE;YAAA1B,cAAA,GAAAK,CAAA;YAAAL,cAAA,GAAAC,CAAA;YACZ,MAAM,IAAIa,KAAK,CAAC,yBAAyB,CAAC;UAC5C,CAAC;YAAAd,cAAA,GAAAK,CAAA;UAAA;UAAAL,cAAA,GAAAC,CAAA;UAED,OAAO,IAAI,CAACmC,qBAAqB,CAACV,OAAO,CAAC;QAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;UACd,IAAMM,QAAQ,IAAArC,cAAA,GAAAC,CAAA,QAAGP,WAAW,CAACqC,KAAK,EAAE;YAAEO,SAAS,EAAE;UAAM,CAAC,CAAC;UAACtC,cAAA,GAAAC,CAAA;UAC1DN,QAAQ,CAAC0C,QAAQ,EAAE;YAAEE,OAAO,EAAE,wBAAwB;YAAE3B,OAAO,EAAPA;UAAQ,CAAC,CAAC;UAACZ,cAAA,GAAAC,CAAA;UAGnE,OAAO,IAAI,CAACuC,2BAA2B,CAAC5B,OAAO,CAAC;QAClD;MACF,CAAC;MAAA,SAnDK6B,sBAAsBA,CAAAC,EAAA;QAAA,OAAAhC,uBAAA,CAAAiC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAtBH,sBAAsB;IAAA;EAAA;IAAAjC,GAAA;IAAAC,KAAA;MAAA,IAAAoC,wBAAA,GAAAlC,iBAAA,CAwD5B,WAA8BC,OAA6B,EAAkC;QAAAZ,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QAC3F,IAAI;UAAA,IAAA6C,eAAA;UAAA9C,cAAA,GAAAC,CAAA;UACF,IAAI,CAAC,IAAI,CAACG,MAAM,EAAE;YAAAJ,cAAA,GAAAK,CAAA;YAAAL,cAAA,GAAAC,CAAA;YAChB,MAAM,IAAIa,KAAK,CAAC,+BAA+B,CAAC;UAClD,CAAC;YAAAd,cAAA,GAAAK,CAAA;UAAA;UAED,IAAMU,MAAM,IAAAf,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC8C,wBAAwB,CAACnC,OAAO,CAAC;UAErD,IAAMK,QAAQ,IAAAjB,cAAA,GAAAC,CAAA,cAASiB,KAAK,CAAC,GAAG,IAAI,CAACnB,UAAU,mBAAmB,EAAE;YAClEoB,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClC,eAAe,EAAE,UAAU,IAAI,CAAChB,MAAM;YACxC,CAAC;YACDiB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBrB,KAAK,EAAE,IAAI,CAACA,KAAK;cACjBsB,QAAQ,EAAE,CACR;gBACEC,IAAI,EAAE,QAAQ;gBACdC,OAAO,EAAE;cACX,CAAC,EACD;gBACED,IAAI,EAAE,MAAM;gBACZC,OAAO,EAAEX;cACX,CAAC,CACF;cACDY,WAAW,EAAE,GAAG;cAChBC,UAAU,EAAE;YACd,CAAC;UACH,CAAC,CAAC;UAAC5B,cAAA,GAAAC,CAAA;UAEH,IAAI,CAACgB,QAAQ,CAACY,EAAE,EAAE;YAAA,IAAAmB,aAAA;YAAAhD,cAAA,GAAAK,CAAA;YAChB,IAAM0B,KAAK,IAAA/B,cAAA,GAAAC,CAAA,cAASgB,QAAQ,CAACe,IAAI,CAAC,CAAC;YAAChC,cAAA,GAAAC,CAAA;YACpC,MAAM,IAAIa,KAAK,CAAC,CAAAd,cAAA,GAAAK,CAAA,WAAA2C,aAAA,GAAAjB,KAAK,CAACA,KAAK,qBAAXiB,aAAA,CAAaf,OAAO,MAAAjC,cAAA,GAAAK,CAAA,UAAI,2BAA2B,EAAC;UACtE,CAAC;YAAAL,cAAA,GAAAK,CAAA;UAAA;UAED,IAAM6B,IAAI,IAAAlC,cAAA,GAAAC,CAAA,cAASgB,QAAQ,CAACe,IAAI,CAAC,CAAC;UAClC,IAAMN,OAAO,IAAA1B,cAAA,GAAAC,CAAA,SAAA6C,eAAA,GAAGZ,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,cAAAW,eAAA,GAAfA,eAAA,CAAiBb,OAAO,qBAAxBa,eAAA,CAA0BpB,OAAO;UAAC1B,cAAA,GAAAC,CAAA;UAElD,IAAI,CAACyB,OAAO,EAAE;YAAA1B,cAAA,GAAAK,CAAA;YAAAL,cAAA,GAAAC,CAAA;YACZ,MAAM,IAAIa,KAAK,CAAC,yBAAyB,CAAC;UAC5C,CAAC;YAAAd,cAAA,GAAAK,CAAA;UAAA;UAAAL,cAAA,GAAAC,CAAA;UAED,OAAO,IAAI,CAACgD,0BAA0B,CAACvB,OAAO,CAAC;QACjD,CAAC,CAAC,OAAOK,KAAK,EAAE;UACd,IAAMM,QAAQ,IAAArC,cAAA,GAAAC,CAAA,QAAGP,WAAW,CAACqC,KAAK,EAAE;YAAEO,SAAS,EAAE;UAAM,CAAC,CAAC;UAACtC,cAAA,GAAAC,CAAA;UAC1DN,QAAQ,CAAC0C,QAAQ,EAAE;YAAEE,OAAO,EAAE,yBAAyB;YAAE3B,OAAO,EAAPA;UAAQ,CAAC,CAAC;UAACZ,cAAA,GAAAC,CAAA;UAEpE,OAAO,IAAI,CAACiD,wBAAwB,CAACtC,OAAO,CAAC;QAC/C;MACF,CAAC;MAAA,SAlDKuC,uBAAuBA,CAAAC,GAAA;QAAA,OAAAP,wBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAvBO,uBAAuB;IAAA;EAAA;IAAA3C,GAAA;IAAAC,KAAA;MAAA,IAAA4C,qBAAA,GAAA1C,iBAAA,CAuD7B,WAA2BC,OAA4B,EAAiC;QAAAZ,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QACtF,IAAI;UAAA,IAAAqD,eAAA;UAAAtD,cAAA,GAAAC,CAAA;UACF,IAAI,CAAC,IAAI,CAACG,MAAM,EAAE;YAAAJ,cAAA,GAAAK,CAAA;YAAAL,cAAA,GAAAC,CAAA;YAChB,MAAM,IAAIa,KAAK,CAAC,+BAA+B,CAAC;UAClD,CAAC;YAAAd,cAAA,GAAAK,CAAA;UAAA;UAED,IAAMU,MAAM,IAAAf,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACsD,uBAAuB,CAAC3C,OAAO,CAAC;UAEpD,IAAMK,QAAQ,IAAAjB,cAAA,GAAAC,CAAA,cAASiB,KAAK,CAAC,GAAG,IAAI,CAACnB,UAAU,mBAAmB,EAAE;YAClEoB,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClC,eAAe,EAAE,UAAU,IAAI,CAAChB,MAAM;YACxC,CAAC;YACDiB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBrB,KAAK,EAAE,IAAI,CAACA,KAAK;cACjBsB,QAAQ,EAAE,CACR;gBACEC,IAAI,EAAE,QAAQ;gBACdC,OAAO,EAAE;cACX,CAAC,EACD;gBACED,IAAI,EAAE,MAAM;gBACZC,OAAO,EAAEX;cACX,CAAC,CACF;cACDY,WAAW,EAAE,GAAG;cAChBC,UAAU,EAAE;YACd,CAAC;UACH,CAAC,CAAC;UAAC5B,cAAA,GAAAC,CAAA;UAEH,IAAI,CAACgB,QAAQ,CAACY,EAAE,EAAE;YAAA,IAAA2B,aAAA;YAAAxD,cAAA,GAAAK,CAAA;YAChB,IAAM0B,KAAK,IAAA/B,cAAA,GAAAC,CAAA,cAASgB,QAAQ,CAACe,IAAI,CAAC,CAAC;YAAChC,cAAA,GAAAC,CAAA;YACpC,MAAM,IAAIa,KAAK,CAAC,CAAAd,cAAA,GAAAK,CAAA,YAAAmD,aAAA,GAAAzB,KAAK,CAACA,KAAK,qBAAXyB,aAAA,CAAavB,OAAO,MAAAjC,cAAA,GAAAK,CAAA,WAAI,2BAA2B,EAAC;UACtE,CAAC;YAAAL,cAAA,GAAAK,CAAA;UAAA;UAED,IAAM6B,IAAI,IAAAlC,cAAA,GAAAC,CAAA,cAASgB,QAAQ,CAACe,IAAI,CAAC,CAAC;UAClC,IAAMN,OAAO,IAAA1B,cAAA,GAAAC,CAAA,SAAAqD,eAAA,GAAGpB,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,cAAAmB,eAAA,GAAfA,eAAA,CAAiBrB,OAAO,qBAAxBqB,eAAA,CAA0B5B,OAAO;UAAC1B,cAAA,GAAAC,CAAA;UAElD,IAAI,CAACyB,OAAO,EAAE;YAAA1B,cAAA,GAAAK,CAAA;YAAAL,cAAA,GAAAC,CAAA;YACZ,MAAM,IAAIa,KAAK,CAAC,yBAAyB,CAAC;UAC5C,CAAC;YAAAd,cAAA,GAAAK,CAAA;UAAA;UAAAL,cAAA,GAAAC,CAAA;UAED,OAAO,IAAI,CAACwD,yBAAyB,CAAC/B,OAAO,CAAC;QAChD,CAAC,CAAC,OAAOK,KAAK,EAAE;UACd,IAAMM,QAAQ,IAAArC,cAAA,GAAAC,CAAA,QAAGP,WAAW,CAACqC,KAAK,EAAE;YAAEO,SAAS,EAAE;UAAM,CAAC,CAAC;UAACtC,cAAA,GAAAC,CAAA;UAC1DN,QAAQ,CAAC0C,QAAQ,EAAE;YAAEE,OAAO,EAAE,sBAAsB;YAAE3B,OAAO,EAAPA;UAAQ,CAAC,CAAC;UAACZ,cAAA,GAAAC,CAAA;UAEjE,OAAO,IAAI,CAACyD,uBAAuB,CAAC9C,OAAO,CAAC;QAC9C;MACF,CAAC;MAAA,SAlDK+C,oBAAoBA,CAAAC,GAAA;QAAA,OAAAP,qBAAA,CAAAV,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBe,oBAAoB;IAAA;EAAA;IAAAnD,GAAA;IAAAC,KAAA,EAuD1B,SAAQO,mBAAmBA,CAACJ,OAA8B,EAAU;MAAAZ,cAAA,GAAAG,CAAA;MAAAH,cAAA,GAAAC,CAAA;MAClE,OAAO;AACX;AACA;AACA,eAAeW,OAAO,CAACiD,UAAU;AACjC,gBAAgB,CAAA7D,cAAA,GAAAK,CAAA,WAAAO,OAAO,CAACkD,SAAS,MAAA9D,cAAA,GAAAK,CAAA,WAAI,cAAc;AACnD;AACA;AACA,iBAAiBO,OAAO,CAACmD,kBAAkB,CAACC,WAAW;AACvD,eAAepD,OAAO,CAACmD,kBAAkB,CAACE,SAAS;AACnD,oBAAoBrD,OAAO,CAACmD,kBAAkB,CAACG,aAAa;AAC5D,YAAYtD,OAAO,CAACmD,kBAAkB,CAACI,MAAM;AAC7C,aAAavD,OAAO,CAACmD,kBAAkB,CAACK,OAAO;AAC/C;AACA;AACA,mBAAmBxD,OAAO,CAACyD,iBAAiB,CAACC,YAAY;AACzD,qBAAqB1D,OAAO,CAACyD,iBAAiB,CAACE,cAAc;AAC7D,cAAc3D,OAAO,CAACyD,iBAAiB,CAACG,QAAQ;AAChD,mBAAmB5D,OAAO,CAACyD,iBAAiB,CAACI,YAAY,CAACC,OAAO,IAAA1E,cAAA,GAAAK,CAAA,WAAG,SAAS,KAAAL,cAAA,GAAAK,CAAA,WAAG,mBAAmB;AACnG;AACA,EAAEO,OAAO,CAAC+D,gBAAgB,IAAA3E,cAAA,GAAAK,CAAA,WAAG,sBAAsBO,OAAO,CAAC+D,gBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAA5E,cAAA,GAAAK,CAAA,WAAG,EAAE;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAACwE,IAAI,CAAC,CAAC;IACV;EAAC;IAAArE,GAAA;IAAAC,KAAA,EAKD,SAAQsC,wBAAwBA,CAACnC,OAA6B,EAAU;MAAAZ,cAAA,GAAAG,CAAA;MAAAH,cAAA,GAAAC,CAAA;MACtE,OAAO;AACX;AACA;AACA,gBAAgBW,OAAO,CAACkE,SAAS,CAACC,MAAM,CAACC,WAAW,CAAC,CAAC;AACtD,SAASpE,OAAO,CAACkE,SAAS,CAACG,KAAK;AAChC,YAAYrE,OAAO,CAACkE,SAAS,CAACI,QAAQ;AACtC,WAAWtE,OAAO,CAACkE,SAAS,CAACK,OAAO;AACpC,kBAAkBvE,OAAO,CAACkE,SAAS,CAACM,aAAa;AACjD;AACA;AACA,UAAUxE,OAAO,CAACyE,UAAU,CAACC,IAAI;AACjC,mBAAmB1E,OAAO,CAACyE,UAAU,CAACE,YAAY;AAClD,mBAAmB3E,OAAO,CAACyE,UAAU,CAACG,oBAAoB;AAC1D,aAAa5E,OAAO,CAACyE,UAAU,CAACI,YAAY;AAC5C,qBAAqB7E,OAAO,CAACyE,UAAU,CAACK,cAAc;AACtD,4BAA4B9E,OAAO,CAACyE,UAAU,CAACM,oBAAoB,IAAI/E,OAAO,CAACyE,UAAU,CAACO,gBAAgB;AAC1G;AACA,EAAEhF,OAAO,CAACiF,SAAS,IAAA7F,cAAA,GAAAK,CAAA,WAAG,iBAAiBO,OAAO,CAACiF,SAAS,EAAE,KAAA7F,cAAA,GAAAK,CAAA,WAAG,EAAE;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAACwE,IAAI,CAAC,CAAC;IACV;EAAC;IAAArE,GAAA;IAAAC,KAAA,EAKD,SAAQ8C,uBAAuBA,CAAC3C,OAA4B,EAAU;MAAAZ,cAAA,GAAAG,CAAA;MAAAH,cAAA,GAAAC,CAAA;MACpE,OAAO;AACX,WAAWW,OAAO,CAACkF,SAAS;AAC5B;AACA;AACA,WAAWlF,OAAO,CAACmF,WAAW,CAACC,KAAK;AACpC,WAAWpF,OAAO,CAACmF,WAAW,CAACE,KAAK,CAACrB,IAAI,CAAC,IAAI,CAAC;AAC/C,oBAAoBhE,OAAO,CAACmF,WAAW,CAACG,aAAa;AACrD,eAAetF,OAAO,CAACmF,WAAW,CAACI,SAAS,CAACvB,IAAI,CAAC,IAAI,CAAC;AACvD,gBAAgBhE,OAAO,CAACmF,WAAW,CAACK,UAAU,CAACxB,IAAI,CAAC,IAAI,CAAC;AACzD;AACA,eAAehE,OAAO,CAACyF,UAAU,CAACzB,IAAI,CAAC,IAAI,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAACC,IAAI,CAAC,CAAC;IACV;EAAC;IAAArE,GAAA;IAAAC,KAAA,EAKD,SAAQ2B,qBAAqBA,CAACV,OAAe,EAAoB;MAAA1B,cAAA,GAAAG,CAAA;MAE/D,IAAMmG,QAAQ,IAAAtG,cAAA,GAAAC,CAAA,QAAGyB,OAAO,CAAC6E,KAAK,CAAC,MAAM,CAAC;MAACvG,cAAA,GAAAC,CAAA;MAEvC,OAAO;QACLuG,iBAAiB,EAAE,CAAAxG,cAAA,GAAAK,CAAA,eAAI,CAACoG,cAAc,CAAC/E,OAAO,EAAE,YAAY,CAAC,MAAA1B,cAAA,GAAAK,CAAA,WAAI,2CAA2C;QAC5G8F,SAAS,EAAE,CAAAnG,cAAA,GAAAK,CAAA,eAAI,CAACqG,gBAAgB,CAAChF,OAAO,EAAE,UAAU,CAAC,MAAA1B,cAAA,GAAAK,CAAA,WAAI,CAAC,oBAAoB,EAAE,cAAc,CAAC;QAC/FsG,YAAY,EAAE,CAAA3G,cAAA,GAAAK,CAAA,eAAI,CAACqG,gBAAgB,CAAChF,OAAO,EAAE,aAAa,CAAC,MAAA1B,cAAA,GAAAK,CAAA,WAAI,CAAC,wBAAwB,EAAE,gBAAgB,CAAC;QAC3GuG,eAAe,EAAE,CAAA5G,cAAA,GAAAK,CAAA,eAAI,CAACqG,gBAAgB,CAAChF,OAAO,EAAE,gBAAgB,CAAC,MAAA1B,cAAA,GAAAK,CAAA,WAAI,CAAC,uBAAuB,EAAE,uBAAuB,CAAC;QACvHwG,gBAAgB,EAAE,CAChB;UAAEC,IAAI,EAAE,eAAe;UAAEC,WAAW,EAAE,qCAAqC;UAAEC,UAAU,EAAE,MAAM;UAAE9B,QAAQ,EAAE;QAAa,CAAC,EACzH;UAAE4B,IAAI,EAAE,eAAe;UAAEC,WAAW,EAAE,kCAAkC;UAAEC,UAAU,EAAE,QAAQ;UAAE9B,QAAQ,EAAE;QAAa,CAAC,CACzH;QACD+B,aAAa,EAAE,CAAAjH,cAAA,GAAAK,CAAA,eAAI,CAACqG,gBAAgB,CAAChF,OAAO,EAAE,WAAW,CAAC,MAAA1B,cAAA,GAAAK,CAAA,WAAI,CAAC,kBAAkB,EAAE,2BAA2B,CAAC;QAC/G6G,UAAU,EAAE,CAAAlH,cAAA,GAAAK,CAAA,eAAI,CAACqG,gBAAgB,CAAChF,OAAO,EAAE,QAAQ,CAAC,MAAA1B,cAAA,GAAAK,CAAA,WAAI,CAAC,cAAc,EAAE,sBAAsB,CAAC;MAClG,CAAC;IACH;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAKD,SAAQwC,0BAA0BA,CAACvB,OAAe,EAAyB;MAAA1B,cAAA,GAAAG,CAAA;MAAAH,cAAA,GAAAC,CAAA;MACzE,OAAO;QACLkH,mBAAmB,EAAE,CAAAnH,cAAA,GAAAK,CAAA,eAAI,CAACoG,cAAc,CAAC/E,OAAO,EAAE,aAAa,CAAC,MAAA1B,cAAA,GAAAK,CAAA,WAAI,kDAAkD;QACtH+G,WAAW,EAAE,CAAApH,cAAA,GAAAK,CAAA,eAAI,CAACqG,gBAAgB,CAAChF,OAAO,EAAE,SAAS,CAAC,MAAA1B,cAAA,GAAAK,CAAA,WAAI,CAAC,0BAA0B,EAAE,uBAAuB,CAAC;QAC/GgH,mBAAmB,EAAE,CAAArH,cAAA,GAAAK,CAAA,eAAI,CAACqG,gBAAgB,CAAChF,OAAO,EAAE,aAAa,CAAC,MAAA1B,cAAA,GAAAK,CAAA,WAAI,CAAC,iBAAiB,EAAE,UAAU,CAAC;QACrGiH,mBAAmB,EAAE,CAAAtH,cAAA,GAAAK,CAAA,eAAI,CAACqG,gBAAgB,CAAChF,OAAO,EAAE,UAAU,CAAC,MAAA1B,cAAA,GAAAK,CAAA,WAAI,CAAC,sBAAsB,EAAE,oBAAoB,CAAC;QACjHkH,iBAAiB,EAAE,CAAAvH,cAAA,GAAAK,CAAA,eAAI,CAACqG,gBAAgB,CAAChF,OAAO,EAAE,UAAU,CAAC,MAAA1B,cAAA,GAAAK,CAAA,WAAI,CAAC,sBAAsB,EAAE,mBAAmB,CAAC;MAChH,CAAC;IACH;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAKD,SAAQgD,yBAAyBA,CAAC/B,OAAe,EAAwB;MAAA1B,cAAA,GAAAG,CAAA;MAAAH,cAAA,GAAAC,CAAA;MACvE,OAAO;QACLuH,YAAY,EAAE,CAAAxH,cAAA,GAAAK,CAAA,eAAI,CAACoG,cAAc,CAAC/E,OAAO,EAAE,UAAU,CAAC,MAAA1B,cAAA,GAAAK,CAAA,WAAI,gEAAgE;QAC1HoH,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,YAAY;UACnBC,QAAQ,EAAE,CACR;YAAEC,GAAG,EAAE,QAAQ;YAAEC,IAAI,EAAE,WAAW;YAAE5C,QAAQ,EAAE,EAAE;YAAE6C,UAAU,EAAE,CAAC,mBAAmB,EAAE,iBAAiB;UAAE,CAAC,EACxG;YAAEF,GAAG,EAAE,WAAW;YAAEC,IAAI,EAAE,SAAS;YAAE5C,QAAQ,EAAE,EAAE;YAAE6C,UAAU,EAAE,CAAC,QAAQ,EAAE,kBAAkB;UAAE,CAAC,EAC/F;YAAEF,GAAG,EAAE,QAAQ;YAAEC,IAAI,EAAE,YAAY;YAAE5C,QAAQ,EAAE,EAAE;YAAE6C,UAAU,EAAE,CAAC,gBAAgB,EAAE,YAAY;UAAE,CAAC;QAErG,CAAC,CACF;QACDC,kBAAkB,EAAE,CAClB;UAAEN,IAAI,EAAE,CAAC;UAAEO,SAAS,EAAE,sBAAsB;UAAEC,kBAAkB,EAAE,CAAC,0BAA0B,EAAE,iBAAiB;QAAE,CAAC,EACnH;UAAER,IAAI,EAAE,CAAC;UAAEO,SAAS,EAAE,iBAAiB;UAAEC,kBAAkB,EAAE,CAAC,wBAAwB,EAAE,oBAAoB;QAAE,CAAC;MAEnH,CAAC;IACH;EAAC;IAAA1H,GAAA;IAAAC,KAAA,EAKD,SAAQgG,cAAcA,CAAC/E,OAAe,EAAEyG,OAAe,EAAiB;MAAAnI,cAAA,GAAAG,CAAA;MACtE,IAAMiI,KAAK,IAAApI,cAAA,GAAAC,CAAA,QAAGyB,OAAO,CAAC6E,KAAK,CAAC,IAAI,CAAC;MAACvG,cAAA,GAAAC,CAAA;MAClC,KAAK,IAAIoI,CAAC,IAAArI,cAAA,GAAAC,CAAA,QAAG,CAAC,GAAEoI,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;QAAArI,cAAA,GAAAC,CAAA;QACrC,IAAImI,KAAK,CAACC,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,OAAO,CAAC,EAAE;UAAAnI,cAAA,GAAAK,CAAA;UAAAL,cAAA,GAAAC,CAAA;UAC5C,OAAO,CAAAD,cAAA,GAAAK,CAAA,WAAA+H,KAAK,CAACC,CAAC,GAAG,CAAC,CAAC,MAAArI,cAAA,GAAAK,CAAA,WAAI,IAAI;QAC7B,CAAC;UAAAL,cAAA,GAAAK,CAAA;QAAA;MACH;MAACL,cAAA,GAAAC,CAAA;MACD,OAAO,IAAI;IACb;EAAC;IAAAO,GAAA;IAAAC,KAAA,EAKD,SAAQiG,gBAAgBA,CAAChF,OAAe,EAAEyG,OAAe,EAAmB;MAAAnI,cAAA,GAAAG,CAAA;MAC1E,IAAMiI,KAAK,IAAApI,cAAA,GAAAC,CAAA,QAAGyB,OAAO,CAAC6E,KAAK,CAAC,IAAI,CAAC;MACjC,IAAMkC,KAAe,IAAAzI,cAAA,GAAAC,CAAA,QAAG,EAAE;MAC1B,IAAIyI,SAAS,IAAA1I,cAAA,GAAAC,CAAA,QAAG,KAAK;MAACD,cAAA,GAAAC,CAAA;MAEtB,KAAK,IAAM0I,IAAI,IAAIP,KAAK,EAAE;QAAApI,cAAA,GAAAC,CAAA;QACxB,IAAI0I,IAAI,CAACJ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,OAAO,CAAC,EAAE;UAAAnI,cAAA,GAAAK,CAAA;UAAAL,cAAA,GAAAC,CAAA;UACxCyI,SAAS,GAAG,IAAI;UAAC1I,cAAA,GAAAC,CAAA;UACjB;QACF,CAAC;UAAAD,cAAA,GAAAK,CAAA;QAAA;QAAAL,cAAA,GAAAC,CAAA;QAED,IAAI,CAAAD,cAAA,GAAAK,CAAA,WAAAqI,SAAS,MAAK,CAAA1I,cAAA,GAAAK,CAAA,WAAAsI,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,MAAA5I,cAAA,GAAAK,CAAA,WAAIsI,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,MAAA5I,cAAA,GAAAK,CAAA,WAAIsI,IAAI,CAACE,KAAK,CAAC,QAAQ,CAAC,EAAC,EAAE;UAAA7I,cAAA,GAAAK,CAAA;UAAAL,cAAA,GAAAC,CAAA;UACvFwI,KAAK,CAACK,IAAI,CAACH,IAAI,CAACI,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAClE,IAAI,CAAC,CAAC,CAAC;QACpD,CAAC,MAAM;UAAA7E,cAAA,GAAAK,CAAA;UAAAL,cAAA,GAAAC,CAAA;UAAA,IAAI,CAAAD,cAAA,GAAAK,CAAA,WAAAqI,SAAS,MAAA1I,cAAA,GAAAK,CAAA,WAAIsI,IAAI,CAAC9D,IAAI,CAAC,CAAC,KAAK,EAAE,GAAE;YAAA7E,cAAA,GAAAK,CAAA;YAAAL,cAAA,GAAAC,CAAA;YAC1C;UACF,CAAC;YAAAD,cAAA,GAAAK,CAAA;UAAA;QAAD;MACF;MAACL,cAAA,GAAAC,CAAA;MAED,OAAOwI,KAAK,CAACH,MAAM,GAAG,CAAC,IAAAtI,cAAA,GAAAK,CAAA,WAAGoI,KAAK,KAAAzI,cAAA,GAAAK,CAAA,WAAG,IAAI;IACxC;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAKD,SAAQ+B,2BAA2BA,CAAC5B,OAA8B,EAAoB;MAAAZ,cAAA,GAAAG,CAAA;MAAAH,cAAA,GAAAC,CAAA;MACpF,OAAO;QACLuG,iBAAiB,EAAE,QAAQ5F,OAAO,CAACiD,UAAU,+EAA+E;QAC5HsC,SAAS,EAAE,CAAC,kBAAkB,EAAE,0BAA0B,CAAC;QAC3DQ,YAAY,EAAE,CAAC,wBAAwB,EAAE,gBAAgB,EAAE,iBAAiB,CAAC;QAC7EC,eAAe,EAAE,CAAC,uBAAuB,EAAE,kCAAkC,EAAE,kBAAkB,CAAC;QAClGC,gBAAgB,EAAE,CAChB;UAAEC,IAAI,EAAE,eAAe;UAAEC,WAAW,EAAE,qCAAqC;UAAEC,UAAU,EAAE,MAAM;UAAE9B,QAAQ,EAAE;QAAa,CAAC,EACzH;UAAE4B,IAAI,EAAE,eAAe;UAAEC,WAAW,EAAE,kCAAkC;UAAEC,UAAU,EAAE,QAAQ;UAAE9B,QAAQ,EAAE;QAAa,CAAC,CACzH;QACD+B,aAAa,EAAE,CAAC,2BAA2B,EAAE,8BAA8B,EAAE,eAAe,CAAC;QAC7FC,UAAU,EAAE,CAAC,cAAc,EAAE,sBAAsB,EAAE,+BAA+B;MACtF,CAAC;IACH;EAAC;IAAA1G,GAAA;IAAAC,KAAA,EAKD,SAAQyC,wBAAwBA,CAACtC,OAA6B,EAAyB;MAAAZ,cAAA,GAAAG,CAAA;MAAAH,cAAA,GAAAC,CAAA;MACrF,OAAO;QACLkH,mBAAmB,EAAE,GAAGvG,OAAO,CAACkE,SAAS,CAACC,MAAM,KAAK,KAAK,IAAA/E,cAAA,GAAAK,CAAA,WAAG,MAAM,KAAAL,cAAA,GAAAK,CAAA,WAAG,aAAa,+CAA8C;QACjI+G,WAAW,EAAE,CAAC,0BAA0B,EAAE,iCAAiC,CAAC;QAC5EC,mBAAmB,EAAE,CAAC,iBAAiB,EAAE,0BAA0B,EAAE,UAAU,CAAC;QAChFC,mBAAmB,EAAE,CAAC,sBAAsB,EAAE,mCAAmC,EAAE,2BAA2B,CAAC;QAC/GC,iBAAiB,EAAE,CAAC,sBAAsB,EAAE,wBAAwB,EAAE,qBAAqB;MAC7F,CAAC;IACH;EAAC;IAAA/G,GAAA;IAAAC,KAAA,EAKD,SAAQiD,uBAAuBA,CAAC9C,OAA4B,EAAwB;MAAAZ,cAAA,GAAAG,CAAA;MAAAH,cAAA,GAAAC,CAAA;MAClF,OAAO;QACLuH,YAAY,EAAE,GAAG5G,OAAO,CAACkF,SAAS,+CAA+ClF,OAAO,CAACyF,UAAU,CAACzB,IAAI,CAAC,OAAO,CAAC,GAAG;QACpH6C,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,qBAAqB;UAC5BC,QAAQ,EAAE,CACR;YAAEC,GAAG,EAAE,QAAQ;YAAEC,IAAI,EAAE,WAAW;YAAE5C,QAAQ,EAAE,EAAE;YAAE6C,UAAU,EAAE,CAAC,qBAAqB,EAAE,UAAU;UAAE,CAAC,EACnG;YAAEF,GAAG,EAAE,WAAW;YAAEC,IAAI,EAAE,SAAS;YAAE5C,QAAQ,EAAE,EAAE;YAAE6C,UAAU,EAAE,CAAC,iBAAiB,EAAE,SAAS;UAAE,CAAC,EAC/F;YAAEF,GAAG,EAAE,QAAQ;YAAEC,IAAI,EAAE,YAAY;YAAE5C,QAAQ,EAAE,EAAE;YAAE6C,UAAU,EAAE,CAAC,iBAAiB,EAAE,UAAU;UAAE,CAAC;QAEpG,CAAC,CACF;QACDC,kBAAkB,EAAE,CAClB;UAAEN,IAAI,EAAE,CAAC;UAAEO,SAAS,EAAE,6BAA6B;UAAEC,kBAAkB,EAAE,CAAC,eAAe,EAAE,cAAc;QAAE,CAAC,EAC5G;UAAER,IAAI,EAAE,CAAC;UAAEO,SAAS,EAAE,qBAAqB;UAAEC,kBAAkB,EAAE,CAAC,oBAAoB,EAAE,yBAAyB;QAAE,CAAC;MAExH,CAAC;IACH;EAAC;AAAA;AAIH,OAAO,IAAMc,aAAa,IAAAhJ,cAAA,GAAAC,CAAA,QAAG,IAAIJ,aAAa,CAAC,CAAC;AAChD,eAAemJ,aAAa", "ignoreList": []}