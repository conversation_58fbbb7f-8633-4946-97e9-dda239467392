6923452075565114440731f6576cfa6b
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_23ojc3dl8j() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\services\\database\\MigrationService.ts";
  var hash = "c2ed27ce9eb244e858420ff1f341dd62e1a47b82";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\services\\database\\MigrationService.ts",
    statementMap: {
      "0": {
        start: {
          line: 34,
          column: 47
        },
        end: {
          line: 34,
          column: 56
        }
      },
      "1": {
        start: {
          line: 35,
          column: 26
        },
        end: {
          line: 35,
          column: 31
        }
      },
      "2": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 38,
          column: 26
        }
      },
      "3": {
        start: {
          line: 45,
          column: 4
        },
        end: {
          line: 58,
          column: 5
        }
      },
      "4": {
        start: {
          line: 46,
          column: 6
        },
        end: {
          line: 46,
          column: 49
        }
      },
      "5": {
        start: {
          line: 49,
          column: 6
        },
        end: {
          line: 49,
          column: 41
        }
      },
      "6": {
        start: {
          line: 51,
          column: 6
        },
        end: {
          line: 51,
          column: 32
        }
      },
      "7": {
        start: {
          line: 52,
          column: 6
        },
        end: {
          line: 52,
          column: 47
        }
      },
      "8": {
        start: {
          line: 54,
          column: 6
        },
        end: {
          line: 54,
          column: 51
        }
      },
      "9": {
        start: {
          line: 56,
          column: 6
        },
        end: {
          line: 56,
          column: 71
        }
      },
      "10": {
        start: {
          line: 57,
          column: 6
        },
        end: {
          line: 57,
          column: 18
        }
      },
      "11": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 67,
          column: 5
        }
      },
      "12": {
        start: {
          line: 66,
          column: 6
        },
        end: {
          line: 66,
          column: 59
        }
      },
      "13": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 89,
          column: 5
        }
      },
      "14": {
        start: {
          line: 70,
          column: 42
        },
        end: {
          line: 74,
          column: 7
        }
      },
      "15": {
        start: {
          line: 76,
          column: 30
        },
        end: {
          line: 76,
          column: 82
        }
      },
      "16": {
        start: {
          line: 76,
          column: 72
        },
        end: {
          line: 76,
          column: 81
        }
      },
      "17": {
        start: {
          line: 77,
          column: 26
        },
        end: {
          line: 77,
          column: 67
        }
      },
      "18": {
        start: {
          line: 78,
          column: 32
        },
        end: {
          line: 78,
          column: 85
        }
      },
      "19": {
        start: {
          line: 78,
          column: 56
        },
        end: {
          line: 78,
          column: 84
        }
      },
      "20": {
        start: {
          line: 80,
          column: 6
        },
        end: {
          line: 85,
          column: 8
        }
      },
      "21": {
        start: {
          line: 87,
          column: 6
        },
        end: {
          line: 87,
          column: 62
        }
      },
      "22": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 88,
          column: 18
        }
      },
      "23": {
        start: {
          line: 96,
          column: 4
        },
        end: {
          line: 98,
          column: 5
        }
      },
      "24": {
        start: {
          line: 97,
          column: 6
        },
        end: {
          line: 97,
          column: 59
        }
      },
      "25": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 118,
          column: 5
        }
      },
      "26": {
        start: {
          line: 101,
          column: 21
        },
        end: {
          line: 101,
          column: 43
        }
      },
      "27": {
        start: {
          line: 102,
          column: 41
        },
        end: {
          line: 102,
          column: 43
        }
      },
      "28": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 112,
          column: 7
        }
      },
      "29": {
        start: {
          line: 105,
          column: 23
        },
        end: {
          line: 105,
          column: 57
        }
      },
      "30": {
        start: {
          line: 106,
          column: 8
        },
        end: {
          line: 106,
          column: 29
        }
      },
      "31": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 111,
          column: 9
        }
      },
      "32": {
        start: {
          line: 109,
          column: 10
        },
        end: {
          line: 109,
          column: 83
        }
      },
      "33": {
        start: {
          line: 110,
          column: 10
        },
        end: {
          line: 110,
          column: 16
        }
      },
      "34": {
        start: {
          line: 114,
          column: 6
        },
        end: {
          line: 114,
          column: 21
        }
      },
      "35": {
        start: {
          line: 116,
          column: 6
        },
        end: {
          line: 116,
          column: 56
        }
      },
      "36": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 117,
          column: 18
        }
      },
      "37": {
        start: {
          line: 125,
          column: 4
        },
        end: {
          line: 127,
          column: 5
        }
      },
      "38": {
        start: {
          line: 126,
          column: 6
        },
        end: {
          line: 126,
          column: 59
        }
      },
      "39": {
        start: {
          line: 129,
          column: 4
        },
        end: {
          line: 152,
          column: 5
        }
      },
      "40": {
        start: {
          line: 130,
          column: 21
        },
        end: {
          line: 130,
          column: 43
        }
      },
      "41": {
        start: {
          line: 131,
          column: 41
        },
        end: {
          line: 131,
          column: 43
        }
      },
      "42": {
        start: {
          line: 134,
          column: 35
        },
        end: {
          line: 136,
          column: 18
        }
      },
      "43": {
        start: {
          line: 135,
          column: 21
        },
        end: {
          line: 135,
          column: 63
        }
      },
      "44": {
        start: {
          line: 138,
          column: 6
        },
        end: {
          line: 146,
          column: 7
        }
      },
      "45": {
        start: {
          line: 139,
          column: 23
        },
        end: {
          line: 139,
          column: 60
        }
      },
      "46": {
        start: {
          line: 140,
          column: 8
        },
        end: {
          line: 140,
          column: 29
        }
      },
      "47": {
        start: {
          line: 142,
          column: 8
        },
        end: {
          line: 145,
          column: 9
        }
      },
      "48": {
        start: {
          line: 143,
          column: 10
        },
        end: {
          line: 143,
          column: 84
        }
      },
      "49": {
        start: {
          line: 144,
          column: 10
        },
        end: {
          line: 144,
          column: 16
        }
      },
      "50": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 148,
          column: 21
        }
      },
      "51": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 55
        }
      },
      "52": {
        start: {
          line: 151,
          column: 6
        },
        end: {
          line: 151,
          column: 18
        }
      },
      "53": {
        start: {
          line: 160,
          column: 4
        },
        end: {
          line: 162,
          column: 5
        }
      },
      "54": {
        start: {
          line: 161,
          column: 6
        },
        end: {
          line: 161,
          column: 86
        }
      },
      "55": {
        start: {
          line: 165,
          column: 4
        },
        end: {
          line: 167,
          column: 5
        }
      },
      "56": {
        start: {
          line: 166,
          column: 6
        },
        end: {
          line: 166,
          column: 79
        }
      },
      "57": {
        start: {
          line: 169,
          column: 4
        },
        end: {
          line: 169,
          column: 54
        }
      },
      "58": {
        start: {
          line: 170,
          column: 4
        },
        end: {
          line: 170,
          column: 75
        }
      },
      "59": {
        start: {
          line: 177,
          column: 29
        },
        end: {
          line: 177,
          column: 31
        }
      },
      "60": {
        start: {
          line: 178,
          column: 19
        },
        end: {
          line: 178,
          column: 41
        }
      },
      "61": {
        start: {
          line: 180,
          column: 4
        },
        end: {
          line: 188,
          column: 5
        }
      },
      "62": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 187,
          column: 7
        }
      },
      "63": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 186,
          column: 9
        }
      },
      "64": {
        start: {
          line: 183,
          column: 10
        },
        end: {
          line: 185,
          column: 11
        }
      },
      "65": {
        start: {
          line: 184,
          column: 12
        },
        end: {
          line: 184,
          column: 94
        }
      },
      "66": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 193,
          column: 6
        }
      },
      "67": {
        start: {
          line: 202,
          column: 22
        },
        end: {
          line: 202,
          column: 68
        }
      },
      "68": {
        start: {
          line: 203,
          column: 21
        },
        end: {
          line: 203,
          column: 42
        }
      },
      "69": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 230,
          column: 5
        }
      },
      "70": {
        start: {
          line: 207,
          column: 21
        },
        end: {
          line: 207,
          column: 83
        }
      },
      "71": {
        start: {
          line: 208,
          column: 48
        },
        end: {
          line: 208,
          column: 50
        }
      },
      "72": {
        start: {
          line: 210,
          column: 6
        },
        end: {
          line: 213,
          column: 7
        }
      },
      "73": {
        start: {
          line: 211,
          column: 25
        },
        end: {
          line: 211,
          column: 69
        }
      },
      "74": {
        start: {
          line: 212,
          column: 8
        },
        end: {
          line: 212,
          column: 39
        }
      },
      "75": {
        start: {
          line: 216,
          column: 6
        },
        end: {
          line: 223,
          column: 9
        }
      },
      "76": {
        start: {
          line: 225,
          column: 6
        },
        end: {
          line: 225,
          column: 58
        }
      },
      "77": {
        start: {
          line: 226,
          column: 6
        },
        end: {
          line: 226,
          column: 22
        }
      },
      "78": {
        start: {
          line: 228,
          column: 6
        },
        end: {
          line: 228,
          column: 55
        }
      },
      "79": {
        start: {
          line: 229,
          column: 6
        },
        end: {
          line: 229,
          column: 18
        }
      },
      "80": {
        start: {
          line: 236,
          column: 27
        },
        end: {
          line: 251,
          column: 5
        }
      },
      "81": {
        start: {
          line: 255,
          column: 4
        },
        end: {
          line: 255,
          column: 53
        }
      },
      "82": {
        start: {
          line: 259,
          column: 22
        },
        end: {
          line: 259,
          column: 50
        }
      },
      "83": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 267,
          column: 5
        }
      },
      "84": {
        start: {
          line: 261,
          column: 6
        },
        end: {
          line: 266,
          column: 8
        }
      },
      "85": {
        start: {
          line: 269,
          column: 22
        },
        end: {
          line: 269,
          column: 32
        }
      },
      "86": {
        start: {
          line: 271,
          column: 4
        },
        end: {
          line: 310,
          column: 5
        }
      },
      "87": {
        start: {
          line: 273,
          column: 6
        },
        end: {
          line: 280,
          column: 7
        }
      },
      "88": {
        start: {
          line: 274,
          column: 23
        },
        end: {
          line: 274,
          column: 45
        }
      },
      "89": {
        start: {
          line: 275,
          column: 8
        },
        end: {
          line: 279,
          column: 9
        }
      },
      "90": {
        start: {
          line: 276,
          column: 10
        },
        end: {
          line: 278,
          column: 11
        }
      },
      "91": {
        start: {
          line: 277,
          column: 12
        },
        end: {
          line: 277,
          column: 68
        }
      },
      "92": {
        start: {
          line: 283,
          column: 6
        },
        end: {
          line: 283,
          column: 70
        }
      },
      "93": {
        start: {
          line: 284,
          column: 6
        },
        end: {
          line: 284,
          column: 51
        }
      },
      "94": {
        start: {
          line: 287,
          column: 28
        },
        end: {
          line: 287,
          column: 50
        }
      },
      "95": {
        start: {
          line: 288,
          column: 6
        },
        end: {
          line: 296,
          column: 9
        }
      },
      "96": {
        start: {
          line: 298,
          column: 6
        },
        end: {
          line: 302,
          column: 8
        }
      },
      "97": {
        start: {
          line: 304,
          column: 6
        },
        end: {
          line: 309,
          column: 8
        }
      },
      "98": {
        start: {
          line: 314,
          column: 22
        },
        end: {
          line: 314,
          column: 50
        }
      },
      "99": {
        start: {
          line: 315,
          column: 4
        },
        end: {
          line: 322,
          column: 5
        }
      },
      "100": {
        start: {
          line: 316,
          column: 6
        },
        end: {
          line: 321,
          column: 8
        }
      },
      "101": {
        start: {
          line: 324,
          column: 22
        },
        end: {
          line: 324,
          column: 32
        }
      },
      "102": {
        start: {
          line: 326,
          column: 4
        },
        end: {
          line: 348,
          column: 5
        }
      },
      "103": {
        start: {
          line: 328,
          column: 6
        },
        end: {
          line: 328,
          column: 74
        }
      },
      "104": {
        start: {
          line: 329,
          column: 6
        },
        end: {
          line: 329,
          column: 53
        }
      },
      "105": {
        start: {
          line: 332,
          column: 6
        },
        end: {
          line: 334,
          column: 9
        }
      },
      "106": {
        start: {
          line: 336,
          column: 6
        },
        end: {
          line: 340,
          column: 8
        }
      },
      "107": {
        start: {
          line: 342,
          column: 6
        },
        end: {
          line: 347,
          column: 8
        }
      },
      "108": {
        start: {
          line: 354,
          column: 4
        },
        end: {
          line: 354,
          column: 59
        }
      },
      "109": {
        start: {
          line: 354,
          column: 33
        },
        end: {
          line: 354,
          column: 57
        }
      },
      "110": {
        start: {
          line: 355,
          column: 4
        },
        end: {
          line: 355,
          column: 61
        }
      },
      "111": {
        start: {
          line: 360,
          column: 15
        },
        end: {
          line: 360,
          column: 16
        }
      },
      "112": {
        start: {
          line: 361,
          column: 4
        },
        end: {
          line: 365,
          column: 5
        }
      },
      "113": {
        start: {
          line: 361,
          column: 17
        },
        end: {
          line: 361,
          column: 18
        }
      },
      "114": {
        start: {
          line: 362,
          column: 19
        },
        end: {
          line: 362,
          column: 36
        }
      },
      "115": {
        start: {
          line: 363,
          column: 6
        },
        end: {
          line: 363,
          column: 41
        }
      },
      "116": {
        start: {
          line: 364,
          column: 6
        },
        end: {
          line: 364,
          column: 25
        }
      },
      "117": {
        start: {
          line: 366,
          column: 4
        },
        end: {
          line: 366,
          column: 29
        }
      },
      "118": {
        start: {
          line: 370,
          column: 19
        },
        end: {
          line: 370,
          column: 43
        }
      },
      "119": {
        start: {
          line: 371,
          column: 19
        },
        end: {
          line: 371,
          column: 43
        }
      },
      "120": {
        start: {
          line: 373,
          column: 4
        },
        end: {
          line: 379,
          column: 5
        }
      },
      "121": {
        start: {
          line: 373,
          column: 17
        },
        end: {
          line: 373,
          column: 18
        }
      },
      "122": {
        start: {
          line: 374,
          column: 20
        },
        end: {
          line: 374,
          column: 34
        }
      },
      "123": {
        start: {
          line: 375,
          column: 20
        },
        end: {
          line: 375,
          column: 34
        }
      },
      "124": {
        start: {
          line: 377,
          column: 6
        },
        end: {
          line: 377,
          column: 34
        }
      },
      "125": {
        start: {
          line: 377,
          column: 25
        },
        end: {
          line: 377,
          column: 34
        }
      },
      "126": {
        start: {
          line: 378,
          column: 6
        },
        end: {
          line: 378,
          column: 35
        }
      },
      "127": {
        start: {
          line: 378,
          column: 25
        },
        end: {
          line: 378,
          column: 35
        }
      },
      "128": {
        start: {
          line: 381,
          column: 4
        },
        end: {
          line: 381,
          column: 13
        }
      },
      "129": {
        start: {
          line: 386,
          column: 4
        },
        end: {
          line: 414,
          column: 7
        }
      },
      "130": {
        start: {
          line: 416,
          column: 4
        },
        end: {
          line: 446,
          column: 7
        }
      },
      "131": {
        start: {
          line: 448,
          column: 4
        },
        end: {
          line: 480,
          column: 7
        }
      },
      "132": {
        start: {
          line: 485,
          column: 32
        },
        end: {
          line: 485,
          column: 54
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 37,
            column: 2
          },
          end: {
            line: 37,
            column: 3
          }
        },
        loc: {
          start: {
            line: 37,
            column: 16
          },
          end: {
            line: 39,
            column: 3
          }
        },
        line: 37
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 44,
            column: 2
          },
          end: {
            line: 44,
            column: 3
          }
        },
        loc: {
          start: {
            line: 44,
            column: 36
          },
          end: {
            line: 59,
            column: 3
          }
        },
        line: 44
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 64,
            column: 2
          },
          end: {
            line: 64,
            column: 3
          }
        },
        loc: {
          start: {
            line: 64,
            column: 46
          },
          end: {
            line: 90,
            column: 3
          }
        },
        line: 64
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 76,
            column: 60
          },
          end: {
            line: 76,
            column: 61
          }
        },
        loc: {
          start: {
            line: 76,
            column: 72
          },
          end: {
            line: 76,
            column: 81
          }
        },
        line: 76
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 78,
            column: 51
          },
          end: {
            line: 78,
            column: 52
          }
        },
        loc: {
          start: {
            line: 78,
            column: 56
          },
          end: {
            line: 78,
            column: 84
          }
        },
        line: 78
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 95,
            column: 2
          },
          end: {
            line: 95,
            column: 3
          }
        },
        loc: {
          start: {
            line: 95,
            column: 46
          },
          end: {
            line: 119,
            column: 3
          }
        },
        line: 95
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 124,
            column: 2
          },
          end: {
            line: 124,
            column: 3
          }
        },
        loc: {
          start: {
            line: 124,
            column: 68
          },
          end: {
            line: 153,
            column: 3
          }
        },
        line: 124
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 135,
            column: 16
          },
          end: {
            line: 135,
            column: 17
          }
        },
        loc: {
          start: {
            line: 135,
            column: 21
          },
          end: {
            line: 135,
            column: 63
          }
        },
        line: 135
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 158,
            column: 2
          },
          end: {
            line: 158,
            column: 3
          }
        },
        loc: {
          start: {
            line: 158,
            column: 43
          },
          end: {
            line: 171,
            column: 3
          }
        },
        line: 158
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 176,
            column: 2
          },
          end: {
            line: 176,
            column: 3
          }
        },
        loc: {
          start: {
            line: 176,
            column: 78
          },
          end: {
            line: 194,
            column: 3
          }
        },
        line: 176
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 199,
            column: 2
          },
          end: {
            line: 199,
            column: 3
          }
        },
        loc: {
          start: {
            line: 199,
            column: 40
          },
          end: {
            line: 231,
            column: 3
          }
        },
        line: 199
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 235,
            column: 2
          },
          end: {
            line: 235,
            column: 3
          }
        },
        loc: {
          start: {
            line: 235,
            column: 55
          },
          end: {
            line: 256,
            column: 3
          }
        },
        line: 235
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 258,
            column: 2
          },
          end: {
            line: 258,
            column: 3
          }
        },
        loc: {
          start: {
            line: 258,
            column: 74
          },
          end: {
            line: 311,
            column: 3
          }
        },
        line: 258
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 313,
            column: 2
          },
          end: {
            line: 313,
            column: 3
          }
        },
        loc: {
          start: {
            line: 313,
            column: 77
          },
          end: {
            line: 349,
            column: 3
          }
        },
        line: 313
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 351,
            column: 2
          },
          end: {
            line: 351,
            column: 3
          }
        },
        loc: {
          start: {
            line: 351,
            column: 64
          },
          end: {
            line: 356,
            column: 3
          }
        },
        line: 351
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 354,
            column: 22
          },
          end: {
            line: 354,
            column: 23
          }
        },
        loc: {
          start: {
            line: 354,
            column: 33
          },
          end: {
            line: 354,
            column: 57
          }
        },
        line: 354
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 358,
            column: 2
          },
          end: {
            line: 358,
            column: 3
          }
        },
        loc: {
          start: {
            line: 358,
            column: 49
          },
          end: {
            line: 367,
            column: 3
          }
        },
        line: 358
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 369,
            column: 2
          },
          end: {
            line: 369,
            column: 3
          }
        },
        loc: {
          start: {
            line: 369,
            column: 56
          },
          end: {
            line: 382,
            column: 3
          }
        },
        line: 369
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 384,
            column: 2
          },
          end: {
            line: 384,
            column: 3
          }
        },
        loc: {
          start: {
            line: 384,
            column: 33
          },
          end: {
            line: 481,
            column: 3
          }
        },
        line: 384
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 65,
            column: 4
          },
          end: {
            line: 67,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 65,
            column: 4
          },
          end: {
            line: 67,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 65
      },
      "1": {
        loc: {
          start: {
            line: 76,
            column: 31
          },
          end: {
            line: 76,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 76,
            column: 31
          },
          end: {
            line: 76,
            column: 48
          }
        }, {
          start: {
            line: 76,
            column: 52
          },
          end: {
            line: 76,
            column: 54
          }
        }],
        line: 76
      },
      "2": {
        loc: {
          start: {
            line: 81,
            column: 24
          },
          end: {
            line: 81,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 81,
            column: 24
          },
          end: {
            line: 81,
            column: 67
          }
        }, {
          start: {
            line: 81,
            column: 71
          },
          end: {
            line: 81,
            column: 78
          }
        }],
        line: 81
      },
      "3": {
        loc: {
          start: {
            line: 84,
            column: 23
          },
          end: {
            line: 84,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 23
          },
          end: {
            line: 84,
            column: 66
          }
        }, {
          start: {
            line: 84,
            column: 70
          },
          end: {
            line: 84,
            column: 74
          }
        }],
        line: 84
      },
      "4": {
        loc: {
          start: {
            line: 96,
            column: 4
          },
          end: {
            line: 98,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 96,
            column: 4
          },
          end: {
            line: 98,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 96
      },
      "5": {
        loc: {
          start: {
            line: 108,
            column: 8
          },
          end: {
            line: 111,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 8
          },
          end: {
            line: 111,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 108
      },
      "6": {
        loc: {
          start: {
            line: 125,
            column: 4
          },
          end: {
            line: 127,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 125,
            column: 4
          },
          end: {
            line: 127,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 125
      },
      "7": {
        loc: {
          start: {
            line: 142,
            column: 8
          },
          end: {
            line: 145,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 8
          },
          end: {
            line: 145,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 142
      },
      "8": {
        loc: {
          start: {
            line: 160,
            column: 4
          },
          end: {
            line: 162,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 160,
            column: 4
          },
          end: {
            line: 162,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 160
      },
      "9": {
        loc: {
          start: {
            line: 160,
            column: 8
          },
          end: {
            line: 160,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 160,
            column: 8
          },
          end: {
            line: 160,
            column: 26
          }
        }, {
          start: {
            line: 160,
            column: 30
          },
          end: {
            line: 160,
            column: 45
          }
        }, {
          start: {
            line: 160,
            column: 49
          },
          end: {
            line: 160,
            column: 62
          }
        }],
        line: 160
      },
      "10": {
        loc: {
          start: {
            line: 165,
            column: 4
          },
          end: {
            line: 167,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 165,
            column: 4
          },
          end: {
            line: 167,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 165
      },
      "11": {
        loc: {
          start: {
            line: 181,
            column: 6
          },
          end: {
            line: 187,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 181,
            column: 6
          },
          end: {
            line: 187,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 181
      },
      "12": {
        loc: {
          start: {
            line: 183,
            column: 10
          },
          end: {
            line: 185,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 183,
            column: 10
          },
          end: {
            line: 185,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 183
      },
      "13": {
        loc: {
          start: {
            line: 212,
            column: 28
          },
          end: {
            line: 212,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 212,
            column: 28
          },
          end: {
            line: 212,
            column: 32
          }
        }, {
          start: {
            line: 212,
            column: 36
          },
          end: {
            line: 212,
            column: 38
          }
        }],
        line: 212
      },
      "14": {
        loc: {
          start: {
            line: 260,
            column: 4
          },
          end: {
            line: 267,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 260,
            column: 4
          },
          end: {
            line: 267,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 260
      },
      "15": {
        loc: {
          start: {
            line: 273,
            column: 6
          },
          end: {
            line: 280,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 273,
            column: 6
          },
          end: {
            line: 280,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 273
      },
      "16": {
        loc: {
          start: {
            line: 276,
            column: 10
          },
          end: {
            line: 278,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 276,
            column: 10
          },
          end: {
            line: 278,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 276
      },
      "17": {
        loc: {
          start: {
            line: 307,
            column: 15
          },
          end: {
            line: 307,
            column: 71
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 307,
            column: 40
          },
          end: {
            line: 307,
            column: 53
          }
        }, {
          start: {
            line: 307,
            column: 56
          },
          end: {
            line: 307,
            column: 71
          }
        }],
        line: 307
      },
      "18": {
        loc: {
          start: {
            line: 315,
            column: 4
          },
          end: {
            line: 322,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 315,
            column: 4
          },
          end: {
            line: 322,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 315
      },
      "19": {
        loc: {
          start: {
            line: 345,
            column: 15
          },
          end: {
            line: 345,
            column: 71
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 345,
            column: 40
          },
          end: {
            line: 345,
            column: 53
          }
        }, {
          start: {
            line: 345,
            column: 56
          },
          end: {
            line: 345,
            column: 71
          }
        }],
        line: 345
      },
      "20": {
        loc: {
          start: {
            line: 374,
            column: 20
          },
          end: {
            line: 374,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 374,
            column: 20
          },
          end: {
            line: 374,
            column: 29
          }
        }, {
          start: {
            line: 374,
            column: 33
          },
          end: {
            line: 374,
            column: 34
          }
        }],
        line: 374
      },
      "21": {
        loc: {
          start: {
            line: 375,
            column: 20
          },
          end: {
            line: 375,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 375,
            column: 20
          },
          end: {
            line: 375,
            column: 29
          }
        }, {
          start: {
            line: 375,
            column: 33
          },
          end: {
            line: 375,
            column: 34
          }
        }],
        line: 375
      },
      "22": {
        loc: {
          start: {
            line: 377,
            column: 6
          },
          end: {
            line: 377,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 377,
            column: 6
          },
          end: {
            line: 377,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 377
      },
      "23": {
        loc: {
          start: {
            line: 378,
            column: 6
          },
          end: {
            line: 378,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 378,
            column: 6
          },
          end: {
            line: 378,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 378
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c2ed27ce9eb244e858420ff1f341dd62e1a47b82"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_23ojc3dl8j = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_23ojc3dl8j();
import { databaseService } from "./DatabaseService";
import { performanceMonitor } from "../../../utils/performance";
var MigrationService = function () {
  function MigrationService() {
    _classCallCheck(this, MigrationService);
    this.migrations = (cov_23ojc3dl8j().s[0]++, new Map());
    this.isInitialized = (cov_23ojc3dl8j().s[1]++, false);
    cov_23ojc3dl8j().f[0]++;
    cov_23ojc3dl8j().s[2]++;
    this.loadMigrations();
  }
  return _createClass(MigrationService, [{
    key: "initialize",
    value: (function () {
      var _initialize = _asyncToGenerator(function* () {
        cov_23ojc3dl8j().f[1]++;
        cov_23ojc3dl8j().s[3]++;
        try {
          cov_23ojc3dl8j().s[4]++;
          performanceMonitor.start('migration_init');
          cov_23ojc3dl8j().s[5]++;
          yield this.createMigrationsTable();
          cov_23ojc3dl8j().s[6]++;
          this.isInitialized = true;
          cov_23ojc3dl8j().s[7]++;
          performanceMonitor.end('migration_init');
          cov_23ojc3dl8j().s[8]++;
          console.log('Migration service initialized');
        } catch (error) {
          cov_23ojc3dl8j().s[9]++;
          console.error('Migration service initialization failed:', error);
          cov_23ojc3dl8j().s[10]++;
          throw error;
        }
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }())
  }, {
    key: "getStatus",
    value: (function () {
      var _getStatus = _asyncToGenerator(function* () {
        cov_23ojc3dl8j().f[2]++;
        cov_23ojc3dl8j().s[11]++;
        if (!this.isInitialized) {
          cov_23ojc3dl8j().b[0][0]++;
          cov_23ojc3dl8j().s[12]++;
          throw new Error('Migration service not initialized');
        } else {
          cov_23ojc3dl8j().b[0][1]++;
        }
        cov_23ojc3dl8j().s[13]++;
        try {
          var _ref = (cov_23ojc3dl8j().s[14]++, yield databaseService.query('schema_migrations', 'select', {
              select: 'version, applied_at',
              orderBy: {
                column: 'applied_at',
                ascending: true
              }
            })),
            appliedMigrations = _ref.data;
          var appliedVersions = (cov_23ojc3dl8j().s[15]++, ((cov_23ojc3dl8j().b[1][0]++, appliedMigrations) || (cov_23ojc3dl8j().b[1][1]++, [])).map(function (m) {
            cov_23ojc3dl8j().f[3]++;
            cov_23ojc3dl8j().s[16]++;
            return m.version;
          }));
          var allVersions = (cov_23ojc3dl8j().s[17]++, Array.from(this.migrations.keys()).sort());
          var pendingMigrations = (cov_23ojc3dl8j().s[18]++, allVersions.filter(function (v) {
            cov_23ojc3dl8j().f[4]++;
            cov_23ojc3dl8j().s[19]++;
            return !appliedVersions.includes(v);
          }));
          cov_23ojc3dl8j().s[20]++;
          return {
            currentVersion: (cov_23ojc3dl8j().b[2][0]++, appliedVersions[appliedVersions.length - 1]) || (cov_23ojc3dl8j().b[2][1]++, '0.0.0'),
            appliedMigrations: appliedVersions,
            pendingMigrations: pendingMigrations,
            lastMigration: (cov_23ojc3dl8j().b[3][0]++, appliedVersions[appliedVersions.length - 1]) || (cov_23ojc3dl8j().b[3][1]++, null)
          };
        } catch (error) {
          cov_23ojc3dl8j().s[21]++;
          console.error('Failed to get migration status:', error);
          cov_23ojc3dl8j().s[22]++;
          throw error;
        }
      });
      function getStatus() {
        return _getStatus.apply(this, arguments);
      }
      return getStatus;
    }())
  }, {
    key: "migrate",
    value: (function () {
      var _migrate = _asyncToGenerator(function* () {
        cov_23ojc3dl8j().f[5]++;
        cov_23ojc3dl8j().s[23]++;
        if (!this.isInitialized) {
          cov_23ojc3dl8j().b[4][0]++;
          cov_23ojc3dl8j().s[24]++;
          throw new Error('Migration service not initialized');
        } else {
          cov_23ojc3dl8j().b[4][1]++;
        }
        cov_23ojc3dl8j().s[25]++;
        try {
          var status = (cov_23ojc3dl8j().s[26]++, yield this.getStatus());
          var results = (cov_23ojc3dl8j().s[27]++, []);
          cov_23ojc3dl8j().s[28]++;
          for (var version of status.pendingMigrations) {
            var result = (cov_23ojc3dl8j().s[29]++, yield this.applyMigration(version));
            cov_23ojc3dl8j().s[30]++;
            results.push(result);
            cov_23ojc3dl8j().s[31]++;
            if (!result.success) {
              cov_23ojc3dl8j().b[5][0]++;
              cov_23ojc3dl8j().s[32]++;
              console.error(`Migration ${version} failed, stopping migration process`);
              cov_23ojc3dl8j().s[33]++;
              break;
            } else {
              cov_23ojc3dl8j().b[5][1]++;
            }
          }
          cov_23ojc3dl8j().s[34]++;
          return results;
        } catch (error) {
          cov_23ojc3dl8j().s[35]++;
          console.error('Migration process failed:', error);
          cov_23ojc3dl8j().s[36]++;
          throw error;
        }
      });
      function migrate() {
        return _migrate.apply(this, arguments);
      }
      return migrate;
    }())
  }, {
    key: "rollback",
    value: (function () {
      var _rollback = _asyncToGenerator(function* (targetVersion) {
        var _this = this;
        cov_23ojc3dl8j().f[6]++;
        cov_23ojc3dl8j().s[37]++;
        if (!this.isInitialized) {
          cov_23ojc3dl8j().b[6][0]++;
          cov_23ojc3dl8j().s[38]++;
          throw new Error('Migration service not initialized');
        } else {
          cov_23ojc3dl8j().b[6][1]++;
        }
        cov_23ojc3dl8j().s[39]++;
        try {
          var status = (cov_23ojc3dl8j().s[40]++, yield this.getStatus());
          var results = (cov_23ojc3dl8j().s[41]++, []);
          var migrationsToRollback = (cov_23ojc3dl8j().s[42]++, status.appliedMigrations.filter(function (v) {
            cov_23ojc3dl8j().f[7]++;
            cov_23ojc3dl8j().s[43]++;
            return _this.compareVersions(v, targetVersion) > 0;
          }).reverse());
          cov_23ojc3dl8j().s[44]++;
          for (var version of migrationsToRollback) {
            var result = (cov_23ojc3dl8j().s[45]++, yield this.rollbackMigration(version));
            cov_23ojc3dl8j().s[46]++;
            results.push(result);
            cov_23ojc3dl8j().s[47]++;
            if (!result.success) {
              cov_23ojc3dl8j().b[7][0]++;
              cov_23ojc3dl8j().s[48]++;
              console.error(`Rollback of ${version} failed, stopping rollback process`);
              cov_23ojc3dl8j().s[49]++;
              break;
            } else {
              cov_23ojc3dl8j().b[7][1]++;
            }
          }
          cov_23ojc3dl8j().s[50]++;
          return results;
        } catch (error) {
          cov_23ojc3dl8j().s[51]++;
          console.error('Rollback process failed:', error);
          cov_23ojc3dl8j().s[52]++;
          throw error;
        }
      });
      function rollback(_x) {
        return _rollback.apply(this, arguments);
      }
      return rollback;
    }())
  }, {
    key: "addMigration",
    value: function addMigration(migration) {
      cov_23ojc3dl8j().f[8]++;
      cov_23ojc3dl8j().s[53]++;
      if ((cov_23ojc3dl8j().b[9][0]++, !migration.version) || (cov_23ojc3dl8j().b[9][1]++, !migration.name) || (cov_23ojc3dl8j().b[9][2]++, !migration.up)) {
        cov_23ojc3dl8j().b[8][0]++;
        cov_23ojc3dl8j().s[54]++;
        throw new Error('Invalid migration: version, name, and up script are required');
      } else {
        cov_23ojc3dl8j().b[8][1]++;
      }
      cov_23ojc3dl8j().s[55]++;
      if (this.migrations.has(migration.version)) {
        cov_23ojc3dl8j().b[10][0]++;
        cov_23ojc3dl8j().s[56]++;
        throw new Error(`Migration version ${migration.version} already exists`);
      } else {
        cov_23ojc3dl8j().b[10][1]++;
      }
      cov_23ojc3dl8j().s[57]++;
      this.migrations.set(migration.version, migration);
      cov_23ojc3dl8j().s[58]++;
      console.log(`Added migration ${migration.version}: ${migration.name}`);
    }
  }, {
    key: "validateDependencies",
    value: (function () {
      var _validateDependencies = _asyncToGenerator(function* () {
        cov_23ojc3dl8j().f[9]++;
        var errors = (cov_23ojc3dl8j().s[59]++, []);
        var status = (cov_23ojc3dl8j().s[60]++, yield this.getStatus());
        cov_23ojc3dl8j().s[61]++;
        for (var _ref2 of this.migrations) {
          var _ref3 = _slicedToArray(_ref2, 2);
          var version = _ref3[0];
          var migration = _ref3[1];
          cov_23ojc3dl8j().s[62]++;
          if (migration.dependencies) {
            cov_23ojc3dl8j().b[11][0]++;
            cov_23ojc3dl8j().s[63]++;
            for (var dependency of migration.dependencies) {
              cov_23ojc3dl8j().s[64]++;
              if (!status.appliedMigrations.includes(dependency)) {
                cov_23ojc3dl8j().b[12][0]++;
                cov_23ojc3dl8j().s[65]++;
                errors.push(`Migration ${version} depends on ${dependency} which is not applied`);
              } else {
                cov_23ojc3dl8j().b[12][1]++;
              }
            }
          } else {
            cov_23ojc3dl8j().b[11][1]++;
          }
        }
        cov_23ojc3dl8j().s[66]++;
        return {
          valid: errors.length === 0,
          errors: errors
        };
      });
      function validateDependencies() {
        return _validateDependencies.apply(this, arguments);
      }
      return validateDependencies;
    }())
  }, {
    key: "createBackup",
    value: (function () {
      var _createBackup = _asyncToGenerator(function* () {
        cov_23ojc3dl8j().f[10]++;
        var timestamp = (cov_23ojc3dl8j().s[67]++, new Date().toISOString().replace(/[:.]/g, '-'));
        var backupId = (cov_23ojc3dl8j().s[68]++, `backup_${timestamp}`);
        cov_23ojc3dl8j().s[69]++;
        try {
          var tables = (cov_23ojc3dl8j().s[70]++, ['user_profiles', 'matches', 'match_sets', 'match_statistics']);
          var backupData = (cov_23ojc3dl8j().s[71]++, {});
          cov_23ojc3dl8j().s[72]++;
          for (var table of tables) {
            var _ref4 = (cov_23ojc3dl8j().s[73]++, yield databaseService.query(table, 'select')),
              data = _ref4.data;
            cov_23ojc3dl8j().s[74]++;
            backupData[table] = (cov_23ojc3dl8j().b[13][0]++, data) || (cov_23ojc3dl8j().b[13][1]++, []);
          }
          cov_23ojc3dl8j().s[75]++;
          yield databaseService.query('migration_backups', 'insert', {
            data: {
              backup_id: backupId,
              created_at: new Date().toISOString(),
              tables: tables,
              data_size: JSON.stringify(backupData).length
            }
          });
          cov_23ojc3dl8j().s[76]++;
          console.log(`Database backup created: ${backupId}`);
          cov_23ojc3dl8j().s[77]++;
          return backupId;
        } catch (error) {
          cov_23ojc3dl8j().s[78]++;
          console.error('Failed to create backup:', error);
          cov_23ojc3dl8j().s[79]++;
          throw error;
        }
      });
      function createBackup() {
        return _createBackup.apply(this, arguments);
      }
      return createBackup;
    }())
  }, {
    key: "createMigrationsTable",
    value: function () {
      var _createMigrationsTable = _asyncToGenerator(function* () {
        cov_23ojc3dl8j().f[11]++;
        var createTableSQL = (cov_23ojc3dl8j().s[80]++, `
      CREATE TABLE IF NOT EXISTS schema_migrations (
        version VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        execution_time INTEGER,
        checksum VARCHAR(255)
      );

      CREATE TABLE IF NOT EXISTS migration_backups (
        backup_id VARCHAR(255) PRIMARY KEY,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        tables TEXT[],
        data_size INTEGER
      );
    `);
        cov_23ojc3dl8j().s[81]++;
        console.log('Migration tables created/verified');
      });
      function createMigrationsTable() {
        return _createMigrationsTable.apply(this, arguments);
      }
      return createMigrationsTable;
    }()
  }, {
    key: "applyMigration",
    value: function () {
      var _applyMigration = _asyncToGenerator(function* (version) {
        cov_23ojc3dl8j().f[12]++;
        var migration = (cov_23ojc3dl8j().s[82]++, this.migrations.get(version));
        cov_23ojc3dl8j().s[83]++;
        if (!migration) {
          cov_23ojc3dl8j().b[14][0]++;
          cov_23ojc3dl8j().s[84]++;
          return {
            version: version,
            success: false,
            error: `Migration ${version} not found`,
            executionTime: 0
          };
        } else {
          cov_23ojc3dl8j().b[14][1]++;
        }
        var startTime = (cov_23ojc3dl8j().s[85]++, Date.now());
        cov_23ojc3dl8j().s[86]++;
        try {
          cov_23ojc3dl8j().s[87]++;
          if (migration.dependencies) {
            cov_23ojc3dl8j().b[15][0]++;
            var status = (cov_23ojc3dl8j().s[88]++, yield this.getStatus());
            cov_23ojc3dl8j().s[89]++;
            for (var dependency of migration.dependencies) {
              cov_23ojc3dl8j().s[90]++;
              if (!status.appliedMigrations.includes(dependency)) {
                cov_23ojc3dl8j().b[16][0]++;
                cov_23ojc3dl8j().s[91]++;
                throw new Error(`Dependency ${dependency} not applied`);
              } else {
                cov_23ojc3dl8j().b[16][1]++;
              }
            }
          } else {
            cov_23ojc3dl8j().b[15][1]++;
          }
          cov_23ojc3dl8j().s[92]++;
          console.log(`Applying migration ${version}: ${migration.name}`);
          cov_23ojc3dl8j().s[93]++;
          yield this.executeMigrationSQL(migration.up);
          var executionTime = (cov_23ojc3dl8j().s[94]++, Date.now() - startTime);
          cov_23ojc3dl8j().s[95]++;
          yield databaseService.query('schema_migrations', 'insert', {
            data: {
              version: migration.version,
              name: migration.name,
              applied_at: new Date().toISOString(),
              execution_time: executionTime,
              checksum: this.calculateChecksum(migration.up)
            }
          });
          cov_23ojc3dl8j().s[96]++;
          return {
            version: version,
            success: true,
            executionTime: executionTime
          };
        } catch (error) {
          cov_23ojc3dl8j().s[97]++;
          return {
            version: version,
            success: false,
            error: error instanceof Error ? (cov_23ojc3dl8j().b[17][0]++, error.message) : (cov_23ojc3dl8j().b[17][1]++, 'Unknown error'),
            executionTime: Date.now() - startTime
          };
        }
      });
      function applyMigration(_x2) {
        return _applyMigration.apply(this, arguments);
      }
      return applyMigration;
    }()
  }, {
    key: "rollbackMigration",
    value: function () {
      var _rollbackMigration = _asyncToGenerator(function* (version) {
        cov_23ojc3dl8j().f[13]++;
        var migration = (cov_23ojc3dl8j().s[98]++, this.migrations.get(version));
        cov_23ojc3dl8j().s[99]++;
        if (!migration) {
          cov_23ojc3dl8j().b[18][0]++;
          cov_23ojc3dl8j().s[100]++;
          return {
            version: version,
            success: false,
            error: `Migration ${version} not found`,
            executionTime: 0
          };
        } else {
          cov_23ojc3dl8j().b[18][1]++;
        }
        var startTime = (cov_23ojc3dl8j().s[101]++, Date.now());
        cov_23ojc3dl8j().s[102]++;
        try {
          cov_23ojc3dl8j().s[103]++;
          console.log(`Rolling back migration ${version}: ${migration.name}`);
          cov_23ojc3dl8j().s[104]++;
          yield this.executeMigrationSQL(migration.down);
          cov_23ojc3dl8j().s[105]++;
          yield databaseService.query('schema_migrations', 'delete', {
            filter: {
              version: version
            }
          });
          cov_23ojc3dl8j().s[106]++;
          return {
            version: version,
            success: true,
            executionTime: Date.now() - startTime
          };
        } catch (error) {
          cov_23ojc3dl8j().s[107]++;
          return {
            version: version,
            success: false,
            error: error instanceof Error ? (cov_23ojc3dl8j().b[19][0]++, error.message) : (cov_23ojc3dl8j().b[19][1]++, 'Unknown error'),
            executionTime: Date.now() - startTime
          };
        }
      });
      function rollbackMigration(_x3) {
        return _rollbackMigration.apply(this, arguments);
      }
      return rollbackMigration;
    }()
  }, {
    key: "executeMigrationSQL",
    value: function () {
      var _executeMigrationSQL = _asyncToGenerator(function* (sql) {
        cov_23ojc3dl8j().f[14]++;
        cov_23ojc3dl8j().s[108]++;
        yield new Promise(function (resolve) {
          cov_23ojc3dl8j().f[15]++;
          cov_23ojc3dl8j().s[109]++;
          return setTimeout(resolve, 100);
        });
        cov_23ojc3dl8j().s[110]++;
        console.log(`Executed SQL: ${sql.substring(0, 100)}...`);
      });
      function executeMigrationSQL(_x4) {
        return _executeMigrationSQL.apply(this, arguments);
      }
      return executeMigrationSQL;
    }()
  }, {
    key: "calculateChecksum",
    value: function calculateChecksum(sql) {
      cov_23ojc3dl8j().f[16]++;
      var hash = (cov_23ojc3dl8j().s[111]++, 0);
      cov_23ojc3dl8j().s[112]++;
      for (var i = (cov_23ojc3dl8j().s[113]++, 0); i < sql.length; i++) {
        var char = (cov_23ojc3dl8j().s[114]++, sql.charCodeAt(i));
        cov_23ojc3dl8j().s[115]++;
        hash = (hash << 5) - hash + char;
        cov_23ojc3dl8j().s[116]++;
        hash = hash & hash;
      }
      cov_23ojc3dl8j().s[117]++;
      return hash.toString(16);
    }
  }, {
    key: "compareVersions",
    value: function compareVersions(a, b) {
      cov_23ojc3dl8j().f[17]++;
      var aParts = (cov_23ojc3dl8j().s[118]++, a.split('.').map(Number));
      var bParts = (cov_23ojc3dl8j().s[119]++, b.split('.').map(Number));
      cov_23ojc3dl8j().s[120]++;
      for (var i = (cov_23ojc3dl8j().s[121]++, 0); i < Math.max(aParts.length, bParts.length); i++) {
        var aPart = (cov_23ojc3dl8j().s[122]++, (cov_23ojc3dl8j().b[20][0]++, aParts[i]) || (cov_23ojc3dl8j().b[20][1]++, 0));
        var bPart = (cov_23ojc3dl8j().s[123]++, (cov_23ojc3dl8j().b[21][0]++, bParts[i]) || (cov_23ojc3dl8j().b[21][1]++, 0));
        cov_23ojc3dl8j().s[124]++;
        if (aPart > bPart) {
          cov_23ojc3dl8j().b[22][0]++;
          cov_23ojc3dl8j().s[125]++;
          return 1;
        } else {
          cov_23ojc3dl8j().b[22][1]++;
        }
        cov_23ojc3dl8j().s[126]++;
        if (aPart < bPart) {
          cov_23ojc3dl8j().b[23][0]++;
          cov_23ojc3dl8j().s[127]++;
          return -1;
        } else {
          cov_23ojc3dl8j().b[23][1]++;
        }
      }
      cov_23ojc3dl8j().s[128]++;
      return 0;
    }
  }, {
    key: "loadMigrations",
    value: function loadMigrations() {
      cov_23ojc3dl8j().f[18]++;
      cov_23ojc3dl8j().s[129]++;
      this.addMigration({
        version: '1.0.0',
        name: 'Initial Schema',
        description: 'Create initial database schema with user profiles and matches',
        up: `
        CREATE TABLE user_profiles (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
          full_name TEXT,
          avatar_url TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE TABLE matches (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE NOT NULL,
          opponent_name TEXT NOT NULL,
          match_format TEXT NOT NULL,
          court_surface TEXT NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `,
        down: `
        DROP TABLE IF EXISTS matches;
        DROP TABLE IF EXISTS user_profiles;
      `,
        createdAt: '2024-01-01T00:00:00Z'
      });
      cov_23ojc3dl8j().s[130]++;
      this.addMigration({
        version: '1.1.0',
        name: 'Match Statistics',
        description: 'Add match statistics and sets tracking',
        up: `
        CREATE TABLE match_sets (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          match_id UUID REFERENCES matches(id) ON DELETE CASCADE NOT NULL,
          set_number INTEGER NOT NULL,
          user_games INTEGER DEFAULT 0,
          opponent_games INTEGER DEFAULT 0,
          is_tiebreak BOOLEAN DEFAULT FALSE,
          tiebreak_score JSONB
        );
        
        CREATE TABLE match_statistics (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          match_id UUID REFERENCES matches(id) ON DELETE CASCADE NOT NULL,
          aces INTEGER DEFAULT 0,
          double_faults INTEGER DEFAULT 0,
          winners INTEGER DEFAULT 0,
          unforced_errors INTEGER DEFAULT 0
        );
      `,
        down: `
        DROP TABLE IF EXISTS match_statistics;
        DROP TABLE IF EXISTS match_sets;
      `,
        dependencies: ['1.0.0'],
        createdAt: '2024-01-02T00:00:00Z'
      });
      cov_23ojc3dl8j().s[131]++;
      this.addMigration({
        version: '2.0.0',
        name: 'AI Analysis Tables',
        description: 'Add AI video analysis and coaching insights tables',
        up: `
        CREATE TABLE video_processing_jobs (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          match_id UUID REFERENCES matches(id) ON DELETE CASCADE NOT NULL,
          user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE NOT NULL,
          video_url TEXT NOT NULL,
          status TEXT DEFAULT 'queued',
          progress INTEGER DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE TABLE coaching_insights (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          job_id UUID REFERENCES video_processing_jobs(id) ON DELETE CASCADE,
          user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE NOT NULL,
          category TEXT NOT NULL,
          priority TEXT NOT NULL,
          title TEXT NOT NULL,
          description TEXT NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `,
        down: `
        DROP TABLE IF EXISTS coaching_insights;
        DROP TABLE IF EXISTS video_processing_jobs;
      `,
        dependencies: ['1.1.0'],
        createdAt: '2024-01-03T00:00:00Z'
      });
    }
  }]);
}();
export var migrationService = (cov_23ojc3dl8j().s[132]++, new MigrationService());
export default MigrationService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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