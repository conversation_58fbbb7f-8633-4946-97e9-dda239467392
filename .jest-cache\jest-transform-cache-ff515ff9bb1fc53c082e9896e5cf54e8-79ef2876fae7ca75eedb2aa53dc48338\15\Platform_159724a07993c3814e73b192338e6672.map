{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_Platform", "_default", "module"], "sources": ["Platform.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _Platform = _interopRequireDefault(require(\"../../../exports/Platform\"));\nvar _default = exports.default = _Platform.default;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,SAAS,GAAGL,sBAAsB,CAACC,OAAO,4BAA4B,CAAC,CAAC;AAC5E,IAAIK,QAAQ,GAAGH,OAAO,CAACD,OAAO,GAAGG,SAAS,CAACH,OAAO;AAClDK,MAAM,CAACJ,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}