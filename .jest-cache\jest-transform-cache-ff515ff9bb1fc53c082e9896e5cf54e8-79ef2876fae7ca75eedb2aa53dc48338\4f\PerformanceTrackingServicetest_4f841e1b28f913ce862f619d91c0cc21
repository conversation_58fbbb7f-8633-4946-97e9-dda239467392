0ccff251ff816d3a9b1e79acb3ba43d7
_getJestObj().mock("../../lib/supabase");
_getJestObj().mock("../../utils/performance");
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _api = require("../../services/api");
var _supabase = require("../../lib/supabase");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockSupabase = _supabase.supabase;
describe('PerformanceTrackingService (API Service)', function () {
  beforeEach(function () {
    jest.clearAllMocks();
  });
  describe('getPerformanceMetrics', function () {
    var mockUserId = 'user123';
    it('should calculate performance metrics from match data', (0, _asyncToGenerator2.default)(function* () {
      var mockMatches = [{
        id: 'match1',
        statistics: JSON.stringify({
          firstServePercentage: 65,
          aces: 5,
          doubleFaults: 2,
          forehandWinners: 8,
          forehandErrors: 3,
          backhandWinners: 4,
          backhandErrors: 5,
          netPointsAttempted: 10,
          netPointsWon: 7,
          totalPointsWon: 45,
          totalPointsPlayed: 80
        }),
        result: 'win',
        match_date: '2024-01-15',
        created_at: '2024-01-15T10:00:00Z'
      }, {
        id: 'match2',
        statistics: JSON.stringify({
          firstServePercentage: 70,
          aces: 3,
          doubleFaults: 1,
          forehandWinners: 6,
          forehandErrors: 2,
          backhandWinners: 5,
          backhandErrors: 4,
          netPointsAttempted: 8,
          netPointsWon: 6,
          totalPointsWon: 38,
          totalPointsPlayed: 75
        }),
        result: 'win',
        match_date: '2024-01-14',
        created_at: '2024-01-14T15:00:00Z'
      }];
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue({
                data: mockMatches,
                error: null
              })
            })
          })
        })
      });
      var metrics = yield _api.apiService.getPerformanceMetrics(mockUserId);
      expect(metrics).toBeDefined();
      expect(metrics.overallRating).toBeGreaterThan(0);
      expect(metrics.overallRating).toBeLessThanOrEqual(100);
      expect(metrics.serveRating).toBeGreaterThan(0);
      expect(metrics.forehandRating).toBeGreaterThan(0);
      expect(metrics.backhandRating).toBeGreaterThan(0);
      expect(metrics.volleyRating).toBeGreaterThan(0);
      expect(metrics.movementRating).toBeGreaterThan(0);
      expect(metrics.lastUpdated).toBeDefined();
    }));
    it('should handle empty match data gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue({
                data: [],
                error: null
              })
            })
          })
        })
      });
      var metrics = yield _api.apiService.getPerformanceMetrics(mockUserId);
      expect(metrics).toBeDefined();
      expect(metrics.overallRating).toBe(75);
      expect(metrics.serveRating).toBe(70);
      expect(metrics.forehandRating).toBe(80);
      expect(metrics.backhandRating).toBe(70);
    }));
    it('should handle database errors gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue({
                data: null,
                error: {
                  message: 'Database connection failed'
                }
              })
            })
          })
        })
      });
      var metrics = yield _api.apiService.getPerformanceMetrics(mockUserId);
      expect(metrics).toBeDefined();
      expect(metrics.overallRating).toBe(75);
    }));
  });
  describe('getWeeklyStatistics', function () {
    var mockUserId = 'user123';
    it('should calculate weekly statistics correctly', (0, _asyncToGenerator2.default)(function* () {
      var mockSessions = [{
        id: 'session1',
        duration_minutes: 60,
        overall_score: 85,
        session_date: '2024-01-15'
      }, {
        id: 'session2',
        duration_minutes: 45,
        overall_score: 78,
        session_date: '2024-01-14'
      }, {
        id: 'session3',
        duration_minutes: 90,
        overall_score: 82,
        session_date: '2024-01-13'
      }];
      var mockPreviousWeekSessions = [{
        overall_score: 75
      }, {
        overall_score: 70
      }];
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            gte: jest.fn().mockResolvedValue({
              data: mockSessions,
              error: null
            })
          })
        })
      });
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            gte: jest.fn().mockReturnValue({
              lt: jest.fn().mockResolvedValue({
                data: mockPreviousWeekSessions,
                error: null
              })
            })
          })
        })
      });
      var stats = yield _api.apiService.getWeeklyStatistics(mockUserId);
      expect(stats).toBeDefined();
      expect(stats.sessionsCompleted).toBe(3);
      expect(stats.totalPracticeTime).toBe(195);
      expect(stats.averageScore).toBe(82);
      expect(stats.improvement).toBeGreaterThan(0);
      expect(stats.sessionMetrics).toHaveLength(3);
    }));
    it('should handle no sessions gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            gte: jest.fn().mockResolvedValue({
              data: [],
              error: null
            })
          })
        })
      });
      var stats = yield _api.apiService.getWeeklyStatistics(mockUserId);
      expect(stats).toBeDefined();
      expect(stats.sessionsCompleted).toBe(0);
      expect(stats.totalPracticeTime).toBe(0);
      expect(stats.averageScore).toBe(0);
      expect(stats.improvement).toBe(0);
      expect(stats.sessionMetrics).toHaveLength(0);
    }));
  });
  describe('calculateServeRating', function () {
    it('should calculate serve rating correctly', (0, _asyncToGenerator2.default)(function* () {
      var mockStats = {
        firstServePercentage: 70,
        aces: 5,
        doubleFaults: 2
      };
      var rating = _api.apiService.calculateServeRating(mockStats);
      expect(rating).toBe(75);
      expect(rating).toBeGreaterThanOrEqual(0);
      expect(rating).toBeLessThanOrEqual(100);
    }));
    it('should handle missing statistics', (0, _asyncToGenerator2.default)(function* () {
      var mockStats = {};
      var rating = _api.apiService.calculateServeRating(mockStats);
      expect(rating).toBe(0);
    }));
  });
  describe('calculateStrokeRating', function () {
    it('should calculate forehand rating correctly', (0, _asyncToGenerator2.default)(function* () {
      var mockStats = {
        forehandWinners: 8,
        forehandErrors: 3
      };
      var rating = _api.apiService.calculateStrokeRating(mockStats, 'forehand');
      expect(rating).toBe(83);
      expect(rating).toBeGreaterThanOrEqual(0);
      expect(rating).toBeLessThanOrEqual(100);
    }));
    it('should calculate backhand rating correctly', (0, _asyncToGenerator2.default)(function* () {
      var mockStats = {
        backhandWinners: 4,
        backhandErrors: 5
      };
      var rating = _api.apiService.calculateStrokeRating(mockStats, 'backhand');
      expect(rating).toBe(73);
    }));
  });
  describe('calculateVolleyRating', function () {
    it('should calculate volley rating from net points', (0, _asyncToGenerator2.default)(function* () {
      var mockStats = {
        netPointsAttempted: 10,
        netPointsWon: 7
      };
      var rating = _api.apiService.calculateVolleyRating(mockStats);
      expect(rating).toBe(70);
    }));
    it('should return default rating when no net points attempted', (0, _asyncToGenerator2.default)(function* () {
      var mockStats = {
        netPointsAttempted: 0,
        netPointsWon: 0
      };
      var rating = _api.apiService.calculateVolleyRating(mockStats);
      expect(rating).toBe(70);
    }));
  });
  describe('calculateMovementRating', function () {
    it('should calculate movement rating from point efficiency', (0, _asyncToGenerator2.default)(function* () {
      var mockStats = {
        totalPointsWon: 45,
        totalPointsPlayed: 80
      };
      var rating = _api.apiService.calculateMovementRating(mockStats);
      expect(rating).toBe(87);
    }));
    it('should handle edge cases', (0, _asyncToGenerator2.default)(function* () {
      var mockStats = {
        totalPointsWon: 0,
        totalPointsPlayed: 0
      };
      var rating = _api.apiService.calculateMovementRating(mockStats);
      expect(rating).toBe(70);
    }));
  });
  describe('calculateImprovementTrend', function () {
    it('should calculate positive improvement trend', (0, _asyncToGenerator2.default)(function* () {
      var mockOldMatches = [{
        statistics: JSON.stringify({
          firstServePercentage: 60,
          aces: 2,
          doubleFaults: 3,
          forehandWinners: 4,
          forehandErrors: 6,
          backhandWinners: 2,
          backhandErrors: 7,
          netPointsAttempted: 5,
          netPointsWon: 2,
          totalPointsWon: 30,
          totalPointsPlayed: 70
        })
      }];
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            lt: jest.fn().mockReturnValue({
              order: jest.fn().mockReturnValue({
                limit: jest.fn().mockResolvedValue({
                  data: mockOldMatches,
                  error: null
                })
              })
            })
          })
        })
      });
      var currentRating = 80;
      var trend = yield _api.apiService.calculateImprovementTrend('user123', currentRating);
      expect(trend).toBeGreaterThan(0);
    }));
    it('should handle no historical data', (0, _asyncToGenerator2.default)(function* () {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            lt: jest.fn().mockReturnValue({
              order: jest.fn().mockReturnValue({
                limit: jest.fn().mockResolvedValue({
                  data: [],
                  error: null
                })
              })
            })
          })
        })
      });
      var trend = yield _api.apiService.calculateImprovementTrend('user123', 80);
      expect(trend).toBe(0);
    }));
  });
  describe('integration with dashboard', function () {
    it('should integrate performance metrics with dashboard data', (0, _asyncToGenerator2.default)(function* () {
      mockSupabase.from.mockImplementation(function (table) {
        var _mockData;
        var mockData = {
          users: [{
            id: 'user123',
            name: 'Test User'
          }],
          skill_stats: [{
            user_id: 'user123',
            overall_rating: 75
          }],
          training_sessions: [],
          match_results: [],
          achievements: [],
          notifications: [],
          ai_tips: [],
          matches: [{
            id: 'match1',
            statistics: JSON.stringify({
              firstServePercentage: 70,
              aces: 4,
              doubleFaults: 1
            })
          }]
        };
        return {
          select: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: ((_mockData = mockData[table]) == null ? void 0 : _mockData[0]) || null,
                error: null
              }),
              order: jest.fn().mockReturnValue({
                limit: jest.fn().mockResolvedValue({
                  data: mockData[table] || [],
                  error: null
                })
              })
            })
          })
        };
      });
      var dashboardData = yield _api.apiService.getDashboardData('user123');
      expect(dashboardData).toBeDefined();
      expect(dashboardData.user).toBeDefined();
      expect(dashboardData.skillStats).toBeDefined();
    }));
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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