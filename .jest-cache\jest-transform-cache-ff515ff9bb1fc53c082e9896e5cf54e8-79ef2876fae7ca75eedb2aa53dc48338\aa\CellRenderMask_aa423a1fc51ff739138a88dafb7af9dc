e7684ea9e15c6647db2d1401ca483386
"use strict";

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _toConsumableArray2 = _interopRequireDefault2(require("@babel/runtime/helpers/toConsumableArray"));
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.CellRenderMask = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var CellRenderMask = function () {
  function CellRenderMask(numCells) {
    (0, _classCallCheck2.default)(this, CellRenderMask);
    (0, _invariant.default)(numCells >= 0, 'CellRenderMask must contain a non-negative number os cells');
    this._numCells = numCells;
    if (numCells === 0) {
      this._regions = [];
    } else {
      this._regions = [{
        first: 0,
        last: numCells - 1,
        isSpacer: true
      }];
    }
  }
  return (0, _createClass2.default)(CellRenderMask, [{
    key: "enumerateRegions",
    value: function enumerateRegions() {
      return this._regions;
    }
  }, {
    key: "addCells",
    value: function addCells(cells) {
      var _this$_regions;
      (0, _invariant.default)(cells.first >= 0 && cells.first < this._numCells && cells.last >= -1 && cells.last < this._numCells && cells.last >= cells.first - 1, 'CellRenderMask.addCells called with invalid cell range');
      if (cells.last < cells.first) {
        return;
      }
      var _this$_findRegion = this._findRegion(cells.first),
        firstIntersect = _this$_findRegion[0],
        firstIntersectIdx = _this$_findRegion[1];
      var _this$_findRegion2 = this._findRegion(cells.last),
        lastIntersect = _this$_findRegion2[0],
        lastIntersectIdx = _this$_findRegion2[1];
      if (firstIntersectIdx === lastIntersectIdx && !firstIntersect.isSpacer) {
        return;
      }
      var newLeadRegion = [];
      var newTailRegion = [];
      var newMainRegion = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, cells), {}, {
        isSpacer: false
      });
      if (firstIntersect.first < newMainRegion.first) {
        if (firstIntersect.isSpacer) {
          newLeadRegion.push({
            first: firstIntersect.first,
            last: newMainRegion.first - 1,
            isSpacer: true
          });
        } else {
          newMainRegion.first = firstIntersect.first;
        }
      }
      if (lastIntersect.last > newMainRegion.last) {
        if (lastIntersect.isSpacer) {
          newTailRegion.push({
            first: newMainRegion.last + 1,
            last: lastIntersect.last,
            isSpacer: true
          });
        } else {
          newMainRegion.last = lastIntersect.last;
        }
      }
      var replacementRegions = [].concat(newLeadRegion, [newMainRegion], newTailRegion);
      var numRegionsToDelete = lastIntersectIdx - firstIntersectIdx + 1;
      (_this$_regions = this._regions).splice.apply(_this$_regions, [firstIntersectIdx, numRegionsToDelete].concat((0, _toConsumableArray2.default)(replacementRegions)));
    }
  }, {
    key: "numCells",
    value: function numCells() {
      return this._numCells;
    }
  }, {
    key: "equals",
    value: function equals(other) {
      return this._numCells === other._numCells && this._regions.length === other._regions.length && this._regions.every(function (region, i) {
        return region.first === other._regions[i].first && region.last === other._regions[i].last && region.isSpacer === other._regions[i].isSpacer;
      });
    }
  }, {
    key: "_findRegion",
    value: function _findRegion(cellIdx) {
      var firstIdx = 0;
      var lastIdx = this._regions.length - 1;
      while (firstIdx <= lastIdx) {
        var middleIdx = Math.floor((firstIdx + lastIdx) / 2);
        var middleRegion = this._regions[middleIdx];
        if (cellIdx >= middleRegion.first && cellIdx <= middleRegion.last) {
          return [middleRegion, middleIdx];
        } else if (cellIdx < middleRegion.first) {
          lastIdx = middleIdx - 1;
        } else if (cellIdx > middleRegion.last) {
          firstIdx = middleIdx + 1;
        }
      }
      (0, _invariant.default)(false, "A region was not found containing cellIdx " + cellIdx);
    }
  }]);
}();
exports.CellRenderMask = CellRenderMask;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0MiIsInJlcXVpcmUiLCJfdG9Db25zdW1hYmxlQXJyYXkyIiwiX2NsYXNzQ2FsbENoZWNrMiIsIl9jcmVhdGVDbGFzczIiLCJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwiZGVmYXVsdCIsImV4cG9ydHMiLCJfX2VzTW9kdWxlIiwiQ2VsbFJlbmRlck1hc2siLCJfb2JqZWN0U3ByZWFkMiIsIl9pbnZhcmlhbnQiLCJudW1DZWxscyIsIl9udW1DZWxscyIsIl9yZWdpb25zIiwiZmlyc3QiLCJsYXN0IiwiaXNTcGFjZXIiLCJrZXkiLCJ2YWx1ZSIsImVudW1lcmF0ZVJlZ2lvbnMiLCJhZGRDZWxscyIsImNlbGxzIiwiX3RoaXMkX3JlZ2lvbnMiLCJfdGhpcyRfZmluZFJlZ2lvbiIsIl9maW5kUmVnaW9uIiwiZmlyc3RJbnRlcnNlY3QiLCJmaXJzdEludGVyc2VjdElkeCIsIl90aGlzJF9maW5kUmVnaW9uMiIsImxhc3RJbnRlcnNlY3QiLCJsYXN0SW50ZXJzZWN0SWR4IiwibmV3TGVhZFJlZ2lvbiIsIm5ld1RhaWxSZWdpb24iLCJuZXdNYWluUmVnaW9uIiwicHVzaCIsInJlcGxhY2VtZW50UmVnaW9ucyIsImNvbmNhdCIsIm51bVJlZ2lvbnNUb0RlbGV0ZSIsInNwbGljZSIsImFwcGx5IiwiZXF1YWxzIiwib3RoZXIiLCJsZW5ndGgiLCJldmVyeSIsInJlZ2lvbiIsImkiLCJjZWxsSWR4IiwiZmlyc3RJZHgiLCJsYXN0SWR4IiwibWlkZGxlSWR4IiwiTWF0aCIsImZsb29yIiwibWlkZGxlUmVnaW9uIl0sInNvdXJjZXMiOlsiQ2VsbFJlbmRlck1hc2suanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0ID0gcmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0XCIpLmRlZmF1bHQ7XG5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlO1xuZXhwb3J0cy5DZWxsUmVuZGVyTWFzayA9IHZvaWQgMDtcbnZhciBfb2JqZWN0U3ByZWFkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0U3ByZWFkMlwiKSk7XG52YXIgX2ludmFyaWFudCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcImZianMvbGliL2ludmFyaWFudFwiKSk7XG4vKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogXG4gKiBAZm9ybWF0XG4gKi9cblxuY2xhc3MgQ2VsbFJlbmRlck1hc2sge1xuICBjb25zdHJ1Y3RvcihudW1DZWxscykge1xuICAgICgwLCBfaW52YXJpYW50LmRlZmF1bHQpKG51bUNlbGxzID49IDAsICdDZWxsUmVuZGVyTWFzayBtdXN0IGNvbnRhaW4gYSBub24tbmVnYXRpdmUgbnVtYmVyIG9zIGNlbGxzJyk7XG4gICAgdGhpcy5fbnVtQ2VsbHMgPSBudW1DZWxscztcbiAgICBpZiAobnVtQ2VsbHMgPT09IDApIHtcbiAgICAgIHRoaXMuX3JlZ2lvbnMgPSBbXTtcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy5fcmVnaW9ucyA9IFt7XG4gICAgICAgIGZpcnN0OiAwLFxuICAgICAgICBsYXN0OiBudW1DZWxscyAtIDEsXG4gICAgICAgIGlzU3BhY2VyOiB0cnVlXG4gICAgICB9XTtcbiAgICB9XG4gIH1cbiAgZW51bWVyYXRlUmVnaW9ucygpIHtcbiAgICByZXR1cm4gdGhpcy5fcmVnaW9ucztcbiAgfVxuICBhZGRDZWxscyhjZWxscykge1xuICAgICgwLCBfaW52YXJpYW50LmRlZmF1bHQpKGNlbGxzLmZpcnN0ID49IDAgJiYgY2VsbHMuZmlyc3QgPCB0aGlzLl9udW1DZWxscyAmJiBjZWxscy5sYXN0ID49IC0xICYmIGNlbGxzLmxhc3QgPCB0aGlzLl9udW1DZWxscyAmJiBjZWxscy5sYXN0ID49IGNlbGxzLmZpcnN0IC0gMSwgJ0NlbGxSZW5kZXJNYXNrLmFkZENlbGxzIGNhbGxlZCB3aXRoIGludmFsaWQgY2VsbCByYW5nZScpO1xuXG4gICAgLy8gVmlydHVhbGl6ZWRMaXN0IHVzZXMgaW5jbHVzaXZlIHJhbmdlcywgd2hlcmUgemVyby1jb3VudCBzdGF0ZXMgYXJlXG4gICAgLy8gcG9zc2libGUuIEUuZy4gWzAsIC0xXSBmb3Igbm8gY2VsbHMsIHN0YXJ0aW5nIGF0IDAuXG4gICAgaWYgKGNlbGxzLmxhc3QgPCBjZWxscy5maXJzdCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICB2YXIgX3RoaXMkX2ZpbmRSZWdpb24gPSB0aGlzLl9maW5kUmVnaW9uKGNlbGxzLmZpcnN0KSxcbiAgICAgIGZpcnN0SW50ZXJzZWN0ID0gX3RoaXMkX2ZpbmRSZWdpb25bMF0sXG4gICAgICBmaXJzdEludGVyc2VjdElkeCA9IF90aGlzJF9maW5kUmVnaW9uWzFdO1xuICAgIHZhciBfdGhpcyRfZmluZFJlZ2lvbjIgPSB0aGlzLl9maW5kUmVnaW9uKGNlbGxzLmxhc3QpLFxuICAgICAgbGFzdEludGVyc2VjdCA9IF90aGlzJF9maW5kUmVnaW9uMlswXSxcbiAgICAgIGxhc3RJbnRlcnNlY3RJZHggPSBfdGhpcyRfZmluZFJlZ2lvbjJbMV07XG5cbiAgICAvLyBGYXN0LXBhdGggaWYgdGhlIGNlbGxzIHRvIGFkZCBhcmUgYWxyZWFkeSBhbGwgcHJlc2VudCBpbiB0aGUgbWFzay4gV2VcbiAgICAvLyB3aWxsIG90aGVyd2lzZSBuZWVkIHRvIGRvIHNvbWUgbXV0YXRpb24uXG4gICAgaWYgKGZpcnN0SW50ZXJzZWN0SWR4ID09PSBsYXN0SW50ZXJzZWN0SWR4ICYmICFmaXJzdEludGVyc2VjdC5pc1NwYWNlcikge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIFdlIG5lZWQgdG8gcmVwbGFjZSB0aGUgZXhpc3RpbmcgY292ZXJlZCByZWdpb25zIHdpdGggMS0zIG5ldyByZWdpb25zXG4gICAgLy8gZGVwZW5kaW5nIHdoZXRoZXIgd2UgbmVlZCB0byBzcGxpdCBzcGFjZXJzIG91dCBvZiBvdmVybGFwcGluZyByZWdpb25zLlxuICAgIHZhciBuZXdMZWFkUmVnaW9uID0gW107XG4gICAgdmFyIG5ld1RhaWxSZWdpb24gPSBbXTtcbiAgICB2YXIgbmV3TWFpblJlZ2lvbiA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIGNlbGxzKSwge30sIHtcbiAgICAgIGlzU3BhY2VyOiBmYWxzZVxuICAgIH0pO1xuICAgIGlmIChmaXJzdEludGVyc2VjdC5maXJzdCA8IG5ld01haW5SZWdpb24uZmlyc3QpIHtcbiAgICAgIGlmIChmaXJzdEludGVyc2VjdC5pc1NwYWNlcikge1xuICAgICAgICBuZXdMZWFkUmVnaW9uLnB1c2goe1xuICAgICAgICAgIGZpcnN0OiBmaXJzdEludGVyc2VjdC5maXJzdCxcbiAgICAgICAgICBsYXN0OiBuZXdNYWluUmVnaW9uLmZpcnN0IC0gMSxcbiAgICAgICAgICBpc1NwYWNlcjogdHJ1ZVxuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5ld01haW5SZWdpb24uZmlyc3QgPSBmaXJzdEludGVyc2VjdC5maXJzdDtcbiAgICAgIH1cbiAgICB9XG4gICAgaWYgKGxhc3RJbnRlcnNlY3QubGFzdCA+IG5ld01haW5SZWdpb24ubGFzdCkge1xuICAgICAgaWYgKGxhc3RJbnRlcnNlY3QuaXNTcGFjZXIpIHtcbiAgICAgICAgbmV3VGFpbFJlZ2lvbi5wdXNoKHtcbiAgICAgICAgICBmaXJzdDogbmV3TWFpblJlZ2lvbi5sYXN0ICsgMSxcbiAgICAgICAgICBsYXN0OiBsYXN0SW50ZXJzZWN0Lmxhc3QsXG4gICAgICAgICAgaXNTcGFjZXI6IHRydWVcbiAgICAgICAgfSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBuZXdNYWluUmVnaW9uLmxhc3QgPSBsYXN0SW50ZXJzZWN0Lmxhc3Q7XG4gICAgICB9XG4gICAgfVxuICAgIHZhciByZXBsYWNlbWVudFJlZ2lvbnMgPSBbLi4ubmV3TGVhZFJlZ2lvbiwgbmV3TWFpblJlZ2lvbiwgLi4ubmV3VGFpbFJlZ2lvbl07XG4gICAgdmFyIG51bVJlZ2lvbnNUb0RlbGV0ZSA9IGxhc3RJbnRlcnNlY3RJZHggLSBmaXJzdEludGVyc2VjdElkeCArIDE7XG4gICAgdGhpcy5fcmVnaW9ucy5zcGxpY2UoZmlyc3RJbnRlcnNlY3RJZHgsIG51bVJlZ2lvbnNUb0RlbGV0ZSwgLi4ucmVwbGFjZW1lbnRSZWdpb25zKTtcbiAgfVxuICBudW1DZWxscygpIHtcbiAgICByZXR1cm4gdGhpcy5fbnVtQ2VsbHM7XG4gIH1cbiAgZXF1YWxzKG90aGVyKSB7XG4gICAgcmV0dXJuIHRoaXMuX251bUNlbGxzID09PSBvdGhlci5fbnVtQ2VsbHMgJiYgdGhpcy5fcmVnaW9ucy5sZW5ndGggPT09IG90aGVyLl9yZWdpb25zLmxlbmd0aCAmJiB0aGlzLl9yZWdpb25zLmV2ZXJ5KChyZWdpb24sIGkpID0+IHJlZ2lvbi5maXJzdCA9PT0gb3RoZXIuX3JlZ2lvbnNbaV0uZmlyc3QgJiYgcmVnaW9uLmxhc3QgPT09IG90aGVyLl9yZWdpb25zW2ldLmxhc3QgJiYgcmVnaW9uLmlzU3BhY2VyID09PSBvdGhlci5fcmVnaW9uc1tpXS5pc1NwYWNlcik7XG4gIH1cbiAgX2ZpbmRSZWdpb24oY2VsbElkeCkge1xuICAgIHZhciBmaXJzdElkeCA9IDA7XG4gICAgdmFyIGxhc3RJZHggPSB0aGlzLl9yZWdpb25zLmxlbmd0aCAtIDE7XG4gICAgd2hpbGUgKGZpcnN0SWR4IDw9IGxhc3RJZHgpIHtcbiAgICAgIHZhciBtaWRkbGVJZHggPSBNYXRoLmZsb29yKChmaXJzdElkeCArIGxhc3RJZHgpIC8gMik7XG4gICAgICB2YXIgbWlkZGxlUmVnaW9uID0gdGhpcy5fcmVnaW9uc1ttaWRkbGVJZHhdO1xuICAgICAgaWYgKGNlbGxJZHggPj0gbWlkZGxlUmVnaW9uLmZpcnN0ICYmIGNlbGxJZHggPD0gbWlkZGxlUmVnaW9uLmxhc3QpIHtcbiAgICAgICAgcmV0dXJuIFttaWRkbGVSZWdpb24sIG1pZGRsZUlkeF07XG4gICAgICB9IGVsc2UgaWYgKGNlbGxJZHggPCBtaWRkbGVSZWdpb24uZmlyc3QpIHtcbiAgICAgICAgbGFzdElkeCA9IG1pZGRsZUlkeCAtIDE7XG4gICAgICB9IGVsc2UgaWYgKGNlbGxJZHggPiBtaWRkbGVSZWdpb24ubGFzdCkge1xuICAgICAgICBmaXJzdElkeCA9IG1pZGRsZUlkeCArIDE7XG4gICAgICB9XG4gICAgfVxuICAgICgwLCBfaW52YXJpYW50LmRlZmF1bHQpKGZhbHNlLCBcIkEgcmVnaW9uIHdhcyBub3QgZm91bmQgY29udGFpbmluZyBjZWxsSWR4IFwiICsgY2VsbElkeCk7XG4gIH1cbn1cbmV4cG9ydHMuQ2VsbFJlbmRlck1hc2sgPSBDZWxsUmVuZGVyTWFzazsiXSwibWFwcGluZ3MiOiJBQUFBLFlBQVk7O0FBQUMsSUFBQUEsdUJBQUEsR0FBQUMsT0FBQTtBQUFBLElBQUFDLG1CQUFBLEdBQUFGLHVCQUFBLENBQUFDLE9BQUE7QUFBQSxJQUFBRSxnQkFBQSxHQUFBSCx1QkFBQSxDQUFBQyxPQUFBO0FBQUEsSUFBQUcsYUFBQSxHQUFBSix1QkFBQSxDQUFBQyxPQUFBO0FBRWIsSUFBSUksc0JBQXNCLEdBQUdKLE9BQU8sQ0FBQyw4Q0FBOEMsQ0FBQyxDQUFDSyxPQUFPO0FBQzVGQyxPQUFPLENBQUNDLFVBQVUsR0FBRyxJQUFJO0FBQ3pCRCxPQUFPLENBQUNFLGNBQWMsR0FBRyxLQUFLLENBQUM7QUFDL0IsSUFBSUMsY0FBYyxHQUFHTCxzQkFBc0IsQ0FBQ0osT0FBTyxDQUFDLHNDQUFzQyxDQUFDLENBQUM7QUFDNUYsSUFBSVUsVUFBVSxHQUFHTixzQkFBc0IsQ0FBQ0osT0FBTyxDQUFDLG9CQUFvQixDQUFDLENBQUM7QUFBQyxJQVdqRVEsY0FBYztFQUNsQixTQUFBQSxlQUFZRyxRQUFRLEVBQUU7SUFBQSxJQUFBVCxnQkFBQSxDQUFBRyxPQUFBLFFBQUFHLGNBQUE7SUFDcEIsQ0FBQyxDQUFDLEVBQUVFLFVBQVUsQ0FBQ0wsT0FBTyxFQUFFTSxRQUFRLElBQUksQ0FBQyxFQUFFLDREQUE0RCxDQUFDO0lBQ3BHLElBQUksQ0FBQ0MsU0FBUyxHQUFHRCxRQUFRO0lBQ3pCLElBQUlBLFFBQVEsS0FBSyxDQUFDLEVBQUU7TUFDbEIsSUFBSSxDQUFDRSxRQUFRLEdBQUcsRUFBRTtJQUNwQixDQUFDLE1BQU07TUFDTCxJQUFJLENBQUNBLFFBQVEsR0FBRyxDQUFDO1FBQ2ZDLEtBQUssRUFBRSxDQUFDO1FBQ1JDLElBQUksRUFBRUosUUFBUSxHQUFHLENBQUM7UUFDbEJLLFFBQVEsRUFBRTtNQUNaLENBQUMsQ0FBQztJQUNKO0VBQ0Y7RUFBQyxXQUFBYixhQUFBLENBQUFFLE9BQUEsRUFBQUcsY0FBQTtJQUFBUyxHQUFBO0lBQUFDLEtBQUEsRUFDRCxTQUFBQyxnQkFBZ0JBLENBQUEsRUFBRztNQUNqQixPQUFPLElBQUksQ0FBQ04sUUFBUTtJQUN0QjtFQUFDO0lBQUFJLEdBQUE7SUFBQUMsS0FBQSxFQUNELFNBQUFFLFFBQVFBLENBQUNDLEtBQUssRUFBRTtNQUFBLElBQUFDLGNBQUE7TUFDZCxDQUFDLENBQUMsRUFBRVosVUFBVSxDQUFDTCxPQUFPLEVBQUVnQixLQUFLLENBQUNQLEtBQUssSUFBSSxDQUFDLElBQUlPLEtBQUssQ0FBQ1AsS0FBSyxHQUFHLElBQUksQ0FBQ0YsU0FBUyxJQUFJUyxLQUFLLENBQUNOLElBQUksSUFBSSxDQUFDLENBQUMsSUFBSU0sS0FBSyxDQUFDTixJQUFJLEdBQUcsSUFBSSxDQUFDSCxTQUFTLElBQUlTLEtBQUssQ0FBQ04sSUFBSSxJQUFJTSxLQUFLLENBQUNQLEtBQUssR0FBRyxDQUFDLEVBQUUsd0RBQXdELENBQUM7TUFJdk4sSUFBSU8sS0FBSyxDQUFDTixJQUFJLEdBQUdNLEtBQUssQ0FBQ1AsS0FBSyxFQUFFO1FBQzVCO01BQ0Y7TUFDQSxJQUFJUyxpQkFBaUIsR0FBRyxJQUFJLENBQUNDLFdBQVcsQ0FBQ0gsS0FBSyxDQUFDUCxLQUFLLENBQUM7UUFDbkRXLGNBQWMsR0FBR0YsaUJBQWlCLENBQUMsQ0FBQyxDQUFDO1FBQ3JDRyxpQkFBaUIsR0FBR0gsaUJBQWlCLENBQUMsQ0FBQyxDQUFDO01BQzFDLElBQUlJLGtCQUFrQixHQUFHLElBQUksQ0FBQ0gsV0FBVyxDQUFDSCxLQUFLLENBQUNOLElBQUksQ0FBQztRQUNuRGEsYUFBYSxHQUFHRCxrQkFBa0IsQ0FBQyxDQUFDLENBQUM7UUFDckNFLGdCQUFnQixHQUFHRixrQkFBa0IsQ0FBQyxDQUFDLENBQUM7TUFJMUMsSUFBSUQsaUJBQWlCLEtBQUtHLGdCQUFnQixJQUFJLENBQUNKLGNBQWMsQ0FBQ1QsUUFBUSxFQUFFO1FBQ3RFO01BQ0Y7TUFJQSxJQUFJYyxhQUFhLEdBQUcsRUFBRTtNQUN0QixJQUFJQyxhQUFhLEdBQUcsRUFBRTtNQUN0QixJQUFJQyxhQUFhLEdBQUcsQ0FBQyxDQUFDLEVBQUV2QixjQUFjLENBQUNKLE9BQU8sRUFBRSxDQUFDLENBQUMsRUFBRUksY0FBYyxDQUFDSixPQUFPLEVBQUUsQ0FBQyxDQUFDLEVBQUVnQixLQUFLLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRTtRQUMxRkwsUUFBUSxFQUFFO01BQ1osQ0FBQyxDQUFDO01BQ0YsSUFBSVMsY0FBYyxDQUFDWCxLQUFLLEdBQUdrQixhQUFhLENBQUNsQixLQUFLLEVBQUU7UUFDOUMsSUFBSVcsY0FBYyxDQUFDVCxRQUFRLEVBQUU7VUFDM0JjLGFBQWEsQ0FBQ0csSUFBSSxDQUFDO1lBQ2pCbkIsS0FBSyxFQUFFVyxjQUFjLENBQUNYLEtBQUs7WUFDM0JDLElBQUksRUFBRWlCLGFBQWEsQ0FBQ2xCLEtBQUssR0FBRyxDQUFDO1lBQzdCRSxRQUFRLEVBQUU7VUFDWixDQUFDLENBQUM7UUFDSixDQUFDLE1BQU07VUFDTGdCLGFBQWEsQ0FBQ2xCLEtBQUssR0FBR1csY0FBYyxDQUFDWCxLQUFLO1FBQzVDO01BQ0Y7TUFDQSxJQUFJYyxhQUFhLENBQUNiLElBQUksR0FBR2lCLGFBQWEsQ0FBQ2pCLElBQUksRUFBRTtRQUMzQyxJQUFJYSxhQUFhLENBQUNaLFFBQVEsRUFBRTtVQUMxQmUsYUFBYSxDQUFDRSxJQUFJLENBQUM7WUFDakJuQixLQUFLLEVBQUVrQixhQUFhLENBQUNqQixJQUFJLEdBQUcsQ0FBQztZQUM3QkEsSUFBSSxFQUFFYSxhQUFhLENBQUNiLElBQUk7WUFDeEJDLFFBQVEsRUFBRTtVQUNaLENBQUMsQ0FBQztRQUNKLENBQUMsTUFBTTtVQUNMZ0IsYUFBYSxDQUFDakIsSUFBSSxHQUFHYSxhQUFhLENBQUNiLElBQUk7UUFDekM7TUFDRjtNQUNBLElBQUltQixrQkFBa0IsTUFBQUMsTUFBQSxDQUFPTCxhQUFhLEdBQUVFLGFBQWEsR0FBS0QsYUFBYSxDQUFDO01BQzVFLElBQUlLLGtCQUFrQixHQUFHUCxnQkFBZ0IsR0FBR0gsaUJBQWlCLEdBQUcsQ0FBQztNQUNqRSxDQUFBSixjQUFBLE9BQUksQ0FBQ1QsUUFBUSxFQUFDd0IsTUFBTSxDQUFBQyxLQUFBLENBQUFoQixjQUFBLEdBQUNJLGlCQUFpQixFQUFFVSxrQkFBa0IsRUFBQUQsTUFBQSxLQUFBbEMsbUJBQUEsQ0FBQUksT0FBQSxFQUFLNkIsa0JBQWtCLEdBQUM7SUFDcEY7RUFBQztJQUFBakIsR0FBQTtJQUFBQyxLQUFBLEVBQ0QsU0FBQVAsUUFBUUEsQ0FBQSxFQUFHO01BQ1QsT0FBTyxJQUFJLENBQUNDLFNBQVM7SUFDdkI7RUFBQztJQUFBSyxHQUFBO0lBQUFDLEtBQUEsRUFDRCxTQUFBcUIsTUFBTUEsQ0FBQ0MsS0FBSyxFQUFFO01BQ1osT0FBTyxJQUFJLENBQUM1QixTQUFTLEtBQUs0QixLQUFLLENBQUM1QixTQUFTLElBQUksSUFBSSxDQUFDQyxRQUFRLENBQUM0QixNQUFNLEtBQUtELEtBQUssQ0FBQzNCLFFBQVEsQ0FBQzRCLE1BQU0sSUFBSSxJQUFJLENBQUM1QixRQUFRLENBQUM2QixLQUFLLENBQUMsVUFBQ0MsTUFBTSxFQUFFQyxDQUFDO1FBQUEsT0FBS0QsTUFBTSxDQUFDN0IsS0FBSyxLQUFLMEIsS0FBSyxDQUFDM0IsUUFBUSxDQUFDK0IsQ0FBQyxDQUFDLENBQUM5QixLQUFLLElBQUk2QixNQUFNLENBQUM1QixJQUFJLEtBQUt5QixLQUFLLENBQUMzQixRQUFRLENBQUMrQixDQUFDLENBQUMsQ0FBQzdCLElBQUksSUFBSTRCLE1BQU0sQ0FBQzNCLFFBQVEsS0FBS3dCLEtBQUssQ0FBQzNCLFFBQVEsQ0FBQytCLENBQUMsQ0FBQyxDQUFDNUIsUUFBUTtNQUFBLEVBQUM7SUFDelE7RUFBQztJQUFBQyxHQUFBO0lBQUFDLEtBQUEsRUFDRCxTQUFBTSxXQUFXQSxDQUFDcUIsT0FBTyxFQUFFO01BQ25CLElBQUlDLFFBQVEsR0FBRyxDQUFDO01BQ2hCLElBQUlDLE9BQU8sR0FBRyxJQUFJLENBQUNsQyxRQUFRLENBQUM0QixNQUFNLEdBQUcsQ0FBQztNQUN0QyxPQUFPSyxRQUFRLElBQUlDLE9BQU8sRUFBRTtRQUMxQixJQUFJQyxTQUFTLEdBQUdDLElBQUksQ0FBQ0MsS0FBSyxDQUFDLENBQUNKLFFBQVEsR0FBR0MsT0FBTyxJQUFJLENBQUMsQ0FBQztRQUNwRCxJQUFJSSxZQUFZLEdBQUcsSUFBSSxDQUFDdEMsUUFBUSxDQUFDbUMsU0FBUyxDQUFDO1FBQzNDLElBQUlILE9BQU8sSUFBSU0sWUFBWSxDQUFDckMsS0FBSyxJQUFJK0IsT0FBTyxJQUFJTSxZQUFZLENBQUNwQyxJQUFJLEVBQUU7VUFDakUsT0FBTyxDQUFDb0MsWUFBWSxFQUFFSCxTQUFTLENBQUM7UUFDbEMsQ0FBQyxNQUFNLElBQUlILE9BQU8sR0FBR00sWUFBWSxDQUFDckMsS0FBSyxFQUFFO1VBQ3ZDaUMsT0FBTyxHQUFHQyxTQUFTLEdBQUcsQ0FBQztRQUN6QixDQUFDLE1BQU0sSUFBSUgsT0FBTyxHQUFHTSxZQUFZLENBQUNwQyxJQUFJLEVBQUU7VUFDdEMrQixRQUFRLEdBQUdFLFNBQVMsR0FBRyxDQUFDO1FBQzFCO01BQ0Y7TUFDQSxDQUFDLENBQUMsRUFBRXRDLFVBQVUsQ0FBQ0wsT0FBTyxFQUFFLEtBQUssRUFBRSw0Q0FBNEMsR0FBR3dDLE9BQU8sQ0FBQztJQUN4RjtFQUFDO0FBQUE7QUFFSHZDLE9BQU8sQ0FBQ0UsY0FBYyxHQUFHQSxjQUFjIiwiaWdub3JlTGlzdCI6W119