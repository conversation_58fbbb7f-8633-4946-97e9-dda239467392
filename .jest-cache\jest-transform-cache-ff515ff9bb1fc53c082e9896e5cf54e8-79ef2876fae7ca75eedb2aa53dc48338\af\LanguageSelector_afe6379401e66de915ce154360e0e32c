702d32b94df28f89c6cad309b05cadee
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_13bhjv7jh() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\LanguageSelector.tsx";
  var hash = "5114e5d2dceadb723f4c174880c4a16a6d10953a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\LanguageSelector.tsx",
    statementMap: {
      "0": {
        start: {
          line: 17,
          column: 15
        },
        end: {
          line: 23,
          column: 1
        }
      },
      "1": {
        start: {
          line: 31,
          column: 58
        },
        end: {
          line: 187,
          column: 1
        }
      },
      "2": {
        start: {
          line: 36,
          column: 78
        },
        end: {
          line: 36,
          column: 94
        }
      },
      "3": {
        start: {
          line: 37,
          column: 46
        },
        end: {
          line: 37,
          column: 63
        }
      },
      "4": {
        start: {
          line: 38,
          column: 38
        },
        end: {
          line: 38,
          column: 53
        }
      },
      "5": {
        start: {
          line: 40,
          column: 27
        },
        end: {
          line: 40,
          column: 48
        }
      },
      "6": {
        start: {
          line: 42,
          column: 29
        },
        end: {
          line: 57,
          column: 3
        }
      },
      "7": {
        start: {
          line: 43,
          column: 4
        },
        end: {
          line: 46,
          column: 5
        }
      },
      "8": {
        start: {
          line: 44,
          column: 6
        },
        end: {
          line: 44,
          column: 20
        }
      },
      "9": {
        start: {
          line: 45,
          column: 6
        },
        end: {
          line: 45,
          column: 13
        }
      },
      "10": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 48,
          column: 24
        }
      },
      "11": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 56,
          column: 5
        }
      },
      "12": {
        start: {
          line: 50,
          column: 6
        },
        end: {
          line: 50,
          column: 33
        }
      },
      "13": {
        start: {
          line: 51,
          column: 6
        },
        end: {
          line: 51,
          column: 20
        }
      },
      "14": {
        start: {
          line: 53,
          column: 6
        },
        end: {
          line: 53,
          column: 55
        }
      },
      "15": {
        start: {
          line: 55,
          column: 6
        },
        end: {
          line: 55,
          column: 27
        }
      },
      "16": {
        start: {
          line: 59,
          column: 22
        },
        end: {
          line: 62,
          column: 3
        }
      },
      "17": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 60,
          column: 29
        }
      },
      "18": {
        start: {
          line: 61,
          column: 4
        },
        end: {
          line: 61,
          column: 14
        }
      },
      "19": {
        start: {
          line: 64,
          column: 21
        },
        end: {
          line: 66,
          column: 3
        }
      },
      "20": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 65,
          column: 28
        }
      },
      "21": {
        start: {
          line: 68,
          column: 31
        },
        end: {
          line: 97,
          column: 3
        }
      },
      "22": {
        start: {
          line: 69,
          column: 23
        },
        end: {
          line: 69,
          column: 44
        }
      },
      "23": {
        start: {
          line: 70,
          column: 24
        },
        end: {
          line: 70,
          column: 56
        }
      },
      "24": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 96,
          column: 6
        }
      },
      "25": {
        start: {
          line: 76,
          column: 23
        },
        end: {
          line: 76,
          column: 53
        }
      },
      "26": {
        start: {
          line: 99,
          column: 24
        },
        end: {
          line: 115,
          column: 3
        }
      },
      "27": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 100,
          column: 34
        }
      },
      "28": {
        start: {
          line: 100,
          column: 22
        },
        end: {
          line: 100,
          column: 34
        }
      },
      "29": {
        start: {
          line: 102,
          column: 4
        },
        end: {
          line: 114,
          column: 6
        }
      },
      "30": {
        start: {
          line: 117,
          column: 2
        },
        end: {
          line: 186,
          column: 4
        }
      },
      "31": {
        start: {
          line: 189,
          column: 15
        },
        end: {
          line: 306,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 31,
            column: 58
          },
          end: {
            line: 31,
            column: 59
          }
        },
        loc: {
          start: {
            line: 35,
            column: 6
          },
          end: {
            line: 187,
            column: 1
          }
        },
        line: 35
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 42,
            column: 29
          },
          end: {
            line: 42,
            column: 30
          }
        },
        loc: {
          start: {
            line: 42,
            column: 58
          },
          end: {
            line: 57,
            column: 3
          }
        },
        line: 42
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 59,
            column: 22
          },
          end: {
            line: 59,
            column: 23
          }
        },
        loc: {
          start: {
            line: 59,
            column: 28
          },
          end: {
            line: 62,
            column: 3
          }
        },
        line: 59
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 64,
            column: 21
          },
          end: {
            line: 64,
            column: 22
          }
        },
        loc: {
          start: {
            line: 64,
            column: 27
          },
          end: {
            line: 66,
            column: 3
          }
        },
        line: 64
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 68,
            column: 31
          },
          end: {
            line: 68,
            column: 32
          }
        },
        loc: {
          start: {
            line: 68,
            column: 55
          },
          end: {
            line: 97,
            column: 3
          }
        },
        line: 68
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 76,
            column: 17
          },
          end: {
            line: 76,
            column: 18
          }
        },
        loc: {
          start: {
            line: 76,
            column: 23
          },
          end: {
            line: 76,
            column: 53
          }
        },
        line: 76
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 99,
            column: 24
          },
          end: {
            line: 99,
            column: 25
          }
        },
        loc: {
          start: {
            line: 99,
            column: 30
          },
          end: {
            line: 115,
            column: 3
          }
        },
        line: 99
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 34,
            column: 2
          },
          end: {
            line: 34,
            column: 21
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 21
          }
        }],
        line: 34
      },
      "1": {
        loc: {
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 46,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 46,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "2": {
        loc: {
          start: {
            line: 75,
            column: 39
          },
          end: {
            line: 75,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 75,
            column: 39
          },
          end: {
            line: 75,
            column: 49
          }
        }, {
          start: {
            line: 75,
            column: 53
          },
          end: {
            line: 75,
            column: 74
          }
        }],
        line: 75
      },
      "3": {
        loc: {
          start: {
            line: 79,
            column: 28
          },
          end: {
            line: 79,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 79,
            column: 41
          },
          end: {
            line: 79,
            column: 68
          }
        }, {
          start: {
            line: 79,
            column: 71
          },
          end: {
            line: 79,
            column: 73
          }
        }],
        line: 79
      },
      "4": {
        loc: {
          start: {
            line: 80,
            column: 10
          },
          end: {
            line: 80,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 80,
            column: 23
          },
          end: {
            line: 80,
            column: 32
          }
        }, {
          start: {
            line: 80,
            column: 35
          },
          end: {
            line: 80,
            column: 88
          }
        }],
        line: 80
      },
      "5": {
        loc: {
          start: {
            line: 84,
            column: 45
          },
          end: {
            line: 84,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 45
          },
          end: {
            line: 84,
            column: 55
          }
        }, {
          start: {
            line: 84,
            column: 59
          },
          end: {
            line: 84,
            column: 78
          }
        }],
        line: 84
      },
      "6": {
        loc: {
          start: {
            line: 87,
            column: 45
          },
          end: {
            line: 87,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 87,
            column: 45
          },
          end: {
            line: 87,
            column: 55
          }
        }, {
          start: {
            line: 87,
            column: 59
          },
          end: {
            line: 87,
            column: 81
          }
        }],
        line: 87
      },
      "7": {
        loc: {
          start: {
            line: 92,
            column: 9
          },
          end: {
            line: 94,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 92,
            column: 9
          },
          end: {
            line: 92,
            column: 19
          }
        }, {
          start: {
            line: 93,
            column: 10
          },
          end: {
            line: 93,
            column: 52
          }
        }],
        line: 92
      },
      "8": {
        loc: {
          start: {
            line: 100,
            column: 4
          },
          end: {
            line: 100,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 100,
            column: 4
          },
          end: {
            line: 100,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 100
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5114e5d2dceadb723f4c174880c4a16a6d10953a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_13bhjv7jh = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_13bhjv7jh();
import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, ScrollView, SafeAreaView } from 'react-native';
import { Check, Globe, X } from 'lucide-react-native';
import { useTranslation } from "../utils/i18n";
import Card from "./ui/Card";
import Button from "./ui/Button";
import { accessibilityHelpers } from "../utils/accessibility";
import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
var colors = (cov_13bhjv7jh().s[0]++, {
  primary: '#23ba16',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb'
});
cov_13bhjv7jh().s[1]++;
var LanguageSelector = function LanguageSelector(_ref) {
  var visible = _ref.visible,
    onClose = _ref.onClose,
    _ref$showTrigger = _ref.showTrigger,
    showTrigger = _ref$showTrigger === void 0 ? (cov_13bhjv7jh().b[0][0]++, false) : _ref$showTrigger;
  cov_13bhjv7jh().f[0]++;
  var _ref2 = (cov_13bhjv7jh().s[2]++, useTranslation()),
    t = _ref2.t,
    locale = _ref2.locale,
    setLocale = _ref2.setLocale,
    getAvailableLocales = _ref2.getAvailableLocales,
    getLocaleDisplayName = _ref2.getLocaleDisplayName;
  var _ref3 = (cov_13bhjv7jh().s[3]++, useState(visible)),
    _ref4 = _slicedToArray(_ref3, 2),
    isModalVisible = _ref4[0],
    setIsModalVisible = _ref4[1];
  var _ref5 = (cov_13bhjv7jh().s[4]++, useState(false)),
    _ref6 = _slicedToArray(_ref5, 2),
    isChanging = _ref6[0],
    setIsChanging = _ref6[1];
  var availableLocales = (cov_13bhjv7jh().s[5]++, getAvailableLocales());
  cov_13bhjv7jh().s[6]++;
  var handleLocaleChange = function () {
    var _ref7 = _asyncToGenerator(function* (newLocale) {
      cov_13bhjv7jh().f[1]++;
      cov_13bhjv7jh().s[7]++;
      if (newLocale === locale) {
        cov_13bhjv7jh().b[1][0]++;
        cov_13bhjv7jh().s[8]++;
        handleClose();
        cov_13bhjv7jh().s[9]++;
        return;
      } else {
        cov_13bhjv7jh().b[1][1]++;
      }
      cov_13bhjv7jh().s[10]++;
      setIsChanging(true);
      cov_13bhjv7jh().s[11]++;
      try {
        cov_13bhjv7jh().s[12]++;
        yield setLocale(newLocale);
        cov_13bhjv7jh().s[13]++;
        handleClose();
      } catch (error) {
        cov_13bhjv7jh().s[14]++;
        console.error('Failed to change locale:', error);
      } finally {
        cov_13bhjv7jh().s[15]++;
        setIsChanging(false);
      }
    });
    return function handleLocaleChange(_x) {
      return _ref7.apply(this, arguments);
    };
  }();
  cov_13bhjv7jh().s[16]++;
  var handleClose = function handleClose() {
    cov_13bhjv7jh().f[2]++;
    cov_13bhjv7jh().s[17]++;
    setIsModalVisible(false);
    cov_13bhjv7jh().s[18]++;
    onClose();
  };
  cov_13bhjv7jh().s[19]++;
  var handleOpen = function handleOpen() {
    cov_13bhjv7jh().f[3]++;
    cov_13bhjv7jh().s[20]++;
    setIsModalVisible(true);
  };
  cov_13bhjv7jh().s[21]++;
  var renderLanguageOption = function renderLanguageOption(localeCode) {
    cov_13bhjv7jh().f[4]++;
    var isSelected = (cov_13bhjv7jh().s[22]++, locale === localeCode);
    var displayName = (cov_13bhjv7jh().s[23]++, getLocaleDisplayName(localeCode));
    cov_13bhjv7jh().s[24]++;
    return _jsxs(TouchableOpacity, Object.assign({
      style: [styles.languageOption, (cov_13bhjv7jh().b[2][0]++, isSelected) && (cov_13bhjv7jh().b[2][1]++, styles.selectedOption)],
      onPress: function onPress() {
        cov_13bhjv7jh().f[5]++;
        cov_13bhjv7jh().s[25]++;
        return handleLocaleChange(localeCode);
      },
      disabled: isChanging
    }, accessibilityHelpers.button(`${displayName} ${isSelected ? (cov_13bhjv7jh().b[3][0]++, t('accessibility.selected')) : (cov_13bhjv7jh().b[3][1]++, '')}`, isSelected ? (cov_13bhjv7jh().b[4][0]++, undefined) : (cov_13bhjv7jh().b[4][1]++, t('common.selectLanguage', {
      language: displayName
    }))), {
      children: [_jsxs(View, {
        style: styles.languageInfo,
        children: [_jsx(Text, {
          style: [styles.languageName, (cov_13bhjv7jh().b[5][0]++, isSelected) && (cov_13bhjv7jh().b[5][1]++, styles.selectedText)],
          children: displayName
        }), _jsx(Text, {
          style: [styles.languageCode, (cov_13bhjv7jh().b[6][0]++, isSelected) && (cov_13bhjv7jh().b[6][1]++, styles.selectedSubtext)],
          children: localeCode.toUpperCase()
        })]
      }), (cov_13bhjv7jh().b[7][0]++, isSelected) && (cov_13bhjv7jh().b[7][1]++, _jsx(Check, {
        size: 20,
        color: colors.primary
      }))]
    }), localeCode);
  };
  cov_13bhjv7jh().s[26]++;
  var TriggerButton = function TriggerButton() {
    cov_13bhjv7jh().f[6]++;
    cov_13bhjv7jh().s[27]++;
    if (!showTrigger) {
      cov_13bhjv7jh().b[8][0]++;
      cov_13bhjv7jh().s[28]++;
      return null;
    } else {
      cov_13bhjv7jh().b[8][1]++;
    }
    cov_13bhjv7jh().s[29]++;
    return _jsxs(TouchableOpacity, Object.assign({
      style: styles.triggerButton,
      onPress: handleOpen
    }, accessibilityHelpers.button(t('settings.language'), t('accessibility.hint', {
      hint: 'Opens language selection'
    })), {
      children: [_jsx(Globe, {
        size: 20,
        color: colors.gray
      }), _jsx(Text, {
        style: styles.triggerText,
        children: getLocaleDisplayName(locale)
      })]
    }));
  };
  cov_13bhjv7jh().s[30]++;
  return _jsxs(_Fragment, {
    children: [_jsx(TriggerButton, {}), _jsx(Modal, {
      visible: isModalVisible,
      animationType: "slide",
      presentationStyle: "pageSheet",
      onRequestClose: handleClose,
      children: _jsxs(SafeAreaView, {
        style: styles.modalContainer,
        children: [_jsxs(View, {
          style: styles.header,
          children: [_jsx(Text, {
            style: styles.headerTitle,
            children: t('settings.language')
          }), _jsx(TouchableOpacity, Object.assign({
            onPress: handleClose,
            style: styles.closeButton
          }, accessibilityHelpers.button(t('common.close')), {
            children: _jsx(X, {
              size: 24,
              color: colors.gray
            })
          }))]
        }), _jsxs(ScrollView, {
          style: styles.content,
          showsVerticalScrollIndicator: false,
          children: [_jsxs(Card, {
            style: styles.languageCard,
            children: [_jsx(Text, {
              style: styles.sectionTitle,
              children: t('settings.selectLanguage')
            }), _jsx(Text, {
              style: styles.sectionDescription,
              children: t('settings.languageDescription')
            }), _jsx(View, {
              style: styles.languageList,
              children: availableLocales.map(renderLanguageOption)
            })]
          }), _jsxs(Card, {
            style: styles.infoCard,
            children: [_jsx(Text, {
              style: styles.infoTitle,
              children: t('settings.languageInfo')
            }), _jsxs(View, {
              style: styles.infoList,
              children: [_jsxs(Text, {
                style: styles.infoItem,
                children: ["\u2022 ", t('settings.languageAutoDetect')]
              }), _jsxs(Text, {
                style: styles.infoItem,
                children: ["\u2022 ", t('settings.languageRestart')]
              }), _jsxs(Text, {
                style: styles.infoItem,
                children: ["\u2022 ", t('settings.languageContribute')]
              })]
            })]
          })]
        }), _jsx(View, {
          style: styles.footer,
          children: _jsx(Button, {
            title: t('common.done'),
            onPress: handleClose,
            style: styles.doneButton,
            loading: isChanging
          })
        })]
      })
    })]
  });
};
var styles = (cov_13bhjv7jh().s[31]++, StyleSheet.create({
  triggerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: colors.lightGray,
    borderRadius: 8,
    gap: 8
  },
  triggerText: {
    fontSize: 14,
    color: colors.dark,
    fontWeight: '500'
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.white
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.dark
  },
  closeButton: {
    padding: 4
  },
  content: {
    flex: 1,
    padding: 20
  },
  languageCard: {
    padding: 20,
    marginBottom: 15
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.dark,
    marginBottom: 8
  },
  sectionDescription: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 20,
    lineHeight: 20
  },
  languageList: {
    gap: 2
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 8,
    backgroundColor: colors.lightGray,
    marginBottom: 8
  },
  selectedOption: {
    backgroundColor: 'rgba(35, 186, 22, 0.1)',
    borderWidth: 1,
    borderColor: colors.primary
  },
  languageInfo: {
    flex: 1
  },
  languageName: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.dark,
    marginBottom: 2
  },
  selectedText: {
    color: colors.primary
  },
  languageCode: {
    fontSize: 12,
    color: colors.gray,
    fontWeight: '400'
  },
  selectedSubtext: {
    color: colors.primary
  },
  infoCard: {
    padding: 20,
    marginBottom: 15
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.dark,
    marginBottom: 12
  },
  infoList: {
    gap: 8
  },
  infoItem: {
    fontSize: 14,
    color: colors.gray,
    lineHeight: 20
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: colors.lightGray
  },
  doneButton: {
    width: '100%'
  }
}));
export default LanguageSelector;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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