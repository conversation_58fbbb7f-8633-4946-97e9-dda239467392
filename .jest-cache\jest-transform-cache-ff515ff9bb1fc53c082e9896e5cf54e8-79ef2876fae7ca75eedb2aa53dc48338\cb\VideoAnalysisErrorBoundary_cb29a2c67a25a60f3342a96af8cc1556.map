{"version": 3, "names": ["React", "View", "Text", "StyleSheet", "Video", "AlertCircle", "RefreshCw", "Error<PERSON>ou<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "jsx", "_jsx", "jsxs", "_jsxs", "colors", "cov_181q53ux2z", "s", "primary", "white", "dark", "gray", "lightGray", "red", "yellow", "VideoAnalysisError<PERSON>allback", "_ref", "onRetry", "f", "style", "styles", "errorCard", "children", "errorContent", "iconContainer", "size", "color", "errorTitle", "errorMessage", "reasonsList", "reasonItem", "suggestions", "<PERSON><PERSON><PERSON>le", "suggestionItem", "actions", "b", "title", "onPress", "retryButton", "icon", "console", "log", "variant", "recordButton", "VideoAnalysisErrorBoundary", "_ref2", "handleError", "error", "errorInfo", "message", "stack", "componentStack", "timestamp", "Date", "toISOString", "context", "fallback", "onError", "showDetails", "__DEV__", "create", "margin", "padding", "alignItems", "marginBottom", "fontSize", "fontWeight", "textAlign", "lineHeight", "alignSelf", "backgroundColor", "borderRadius", "gap", "borderColor"], "sources": ["VideoAnalysisErrorBoundary.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n} from 'react-native';\nimport { Video, AlertCircle, RefreshCw } from 'lucide-react-native';\nimport ErrorBoundary from './ErrorBoundary';\nimport Button from '@/components/ui/Button';\nimport Card from '@/components/ui/Card';\n\nconst colors = {\n  primary: '#23ba16',\n  white: '#ffffff',\n  dark: '#171717',\n  gray: '#6b7280',\n  lightGray: '#f9fafb',\n  red: '#ef4444',\n  yellow: '#f59e0b',\n};\n\ninterface VideoAnalysisErrorBoundaryProps {\n  children: React.ReactNode;\n  onRetry?: () => void;\n}\n\nconst VideoAnalysisErrorFallback = ({ onRetry }: { onRetry?: () => void }) => (\n  <Card style={styles.errorCard}>\n    <View style={styles.errorContent}>\n      <View style={styles.iconContainer}>\n        <AlertCircle size={48} color={colors.red} />\n      </View>\n      \n      <Text style={styles.errorTitle}>Video Analysis Failed</Text>\n      <Text style={styles.errorMessage}>\n        We couldn't analyze your video at this time. This might be due to:\n      </Text>\n      \n      <View style={styles.reasonsList}>\n        <Text style={styles.reasonItem}>• Poor video quality or lighting</Text>\n        <Text style={styles.reasonItem}>• Network connectivity issues</Text>\n        <Text style={styles.reasonItem}>• Temporary service unavailability</Text>\n        <Text style={styles.reasonItem}>• Unsupported video format</Text>\n      </View>\n      \n      <View style={styles.suggestions}>\n        <Text style={styles.suggestionsTitle}>Try these solutions:</Text>\n        <Text style={styles.suggestionItem}>✓ Ensure good lighting when recording</Text>\n        <Text style={styles.suggestionItem}>✓ Check your internet connection</Text>\n        <Text style={styles.suggestionItem}>✓ Record in landscape orientation</Text>\n        <Text style={styles.suggestionItem}>✓ Keep the camera steady during recording</Text>\n      </View>\n      \n      <View style={styles.actions}>\n        {onRetry && (\n          <Button\n            title=\"Try Again\"\n            onPress={onRetry}\n            style={styles.retryButton}\n            icon={<RefreshCw size={20} color={colors.white} />}\n          />\n        )}\n        \n        <Button\n          title=\"Record New Video\"\n          onPress={() => {\n            // Navigate back to recording\n            console.log('Navigate to recording');\n          }}\n          variant=\"outline\"\n          style={styles.recordButton}\n          icon={<Video size={20} color={colors.primary} />}\n        />\n      </View>\n    </View>\n  </Card>\n);\n\nconst VideoAnalysisErrorBoundary: React.FC<VideoAnalysisErrorBoundaryProps> = ({ \n  children, \n  onRetry \n}) => {\n  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {\n    // Log specific video analysis errors\n    console.error('Video Analysis Error:', {\n      error: error.message,\n      stack: error.stack,\n      componentStack: errorInfo.componentStack,\n      timestamp: new Date().toISOString(),\n      context: 'video_analysis',\n    });\n\n    // Send to analytics/crash reporting\n    // Analytics.track('video_analysis_error', {\n    //   error_message: error.message,\n    //   error_type: 'component_error',\n    // });\n  };\n\n  return (\n    <ErrorBoundary\n      fallback={<VideoAnalysisErrorFallback onRetry={onRetry} />}\n      onError={handleError}\n      showDetails={__DEV__} // Show details only in development\n    >\n      {children}\n    </ErrorBoundary>\n  );\n};\n\nconst styles = StyleSheet.create({\n  errorCard: {\n    margin: 16,\n    padding: 20,\n  },\n  errorContent: {\n    alignItems: 'center',\n  },\n  iconContainer: {\n    marginBottom: 16,\n  },\n  errorTitle: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    color: colors.dark,\n    marginBottom: 8,\n    textAlign: 'center',\n  },\n  errorMessage: {\n    fontSize: 14,\n    color: colors.gray,\n    textAlign: 'center',\n    marginBottom: 16,\n    lineHeight: 20,\n  },\n  reasonsList: {\n    alignSelf: 'stretch',\n    marginBottom: 20,\n  },\n  reasonItem: {\n    fontSize: 14,\n    color: colors.gray,\n    marginBottom: 4,\n    lineHeight: 20,\n  },\n  suggestions: {\n    alignSelf: 'stretch',\n    backgroundColor: colors.lightGray,\n    padding: 16,\n    borderRadius: 8,\n    marginBottom: 20,\n  },\n  suggestionsTitle: {\n    fontSize: 14,\n    fontWeight: '600',\n    color: colors.dark,\n    marginBottom: 8,\n  },\n  suggestionItem: {\n    fontSize: 13,\n    color: colors.gray,\n    marginBottom: 4,\n    lineHeight: 18,\n  },\n  actions: {\n    alignSelf: 'stretch',\n    gap: 12,\n  },\n  retryButton: {\n    backgroundColor: colors.primary,\n  },\n  recordButton: {\n    borderColor: colors.primary,\n  },\n});\n\nexport default VideoAnalysisErrorBoundary;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,IAAI,EACJC,UAAU,QACL,cAAc;AACrB,SAASC,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,qBAAqB;AACnE,OAAOC,aAAa;AACpB,OAAOC,MAAM;AACb,OAAOC,IAAI;AAA6B,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAExC,IAAMC,MAAM,IAAAC,cAAA,GAAAC,CAAA,OAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE,SAAS;EACpBC,GAAG,EAAE,SAAS;EACdC,MAAM,EAAE;AACV,CAAC;AAACR,cAAA,GAAAC,CAAA;AAOF,IAAMQ,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAAC,IAAA,EAC9B;EAAA,IADoCC,OAAO,GAAAD,IAAA,CAAPC,OAAO;EAAAX,cAAA,GAAAY,CAAA;EAAAZ,cAAA,GAAAC,CAAA;EAC3C,OAAAL,IAAA,CAACF,IAAI;IAACmB,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,EAC5BlB,KAAA,CAACZ,IAAI;MAAC2B,KAAK,EAAEC,MAAM,CAACG,YAAa;MAAAD,QAAA,GAC/BpB,IAAA,CAACV,IAAI;QAAC2B,KAAK,EAAEC,MAAM,CAACI,aAAc;QAAAF,QAAA,EAChCpB,IAAA,CAACN,WAAW;UAAC6B,IAAI,EAAE,EAAG;UAACC,KAAK,EAAErB,MAAM,CAACQ;QAAI,CAAE;MAAC,CACxC,CAAC,EAEPX,IAAA,CAACT,IAAI;QAAC0B,KAAK,EAAEC,MAAM,CAACO,UAAW;QAAAL,QAAA,EAAC;MAAqB,CAAM,CAAC,EAC5DpB,IAAA,CAACT,IAAI;QAAC0B,KAAK,EAAEC,MAAM,CAACQ,YAAa;QAAAN,QAAA,EAAC;MAElC,CAAM,CAAC,EAEPlB,KAAA,CAACZ,IAAI;QAAC2B,KAAK,EAAEC,MAAM,CAACS,WAAY;QAAAP,QAAA,GAC9BpB,IAAA,CAACT,IAAI;UAAC0B,KAAK,EAAEC,MAAM,CAACU,UAAW;UAAAR,QAAA,EAAC;QAAgC,CAAM,CAAC,EACvEpB,IAAA,CAACT,IAAI;UAAC0B,KAAK,EAAEC,MAAM,CAACU,UAAW;UAAAR,QAAA,EAAC;QAA6B,CAAM,CAAC,EACpEpB,IAAA,CAACT,IAAI;UAAC0B,KAAK,EAAEC,MAAM,CAACU,UAAW;UAAAR,QAAA,EAAC;QAAkC,CAAM,CAAC,EACzEpB,IAAA,CAACT,IAAI;UAAC0B,KAAK,EAAEC,MAAM,CAACU,UAAW;UAAAR,QAAA,EAAC;QAA0B,CAAM,CAAC;MAAA,CAC7D,CAAC,EAEPlB,KAAA,CAACZ,IAAI;QAAC2B,KAAK,EAAEC,MAAM,CAACW,WAAY;QAAAT,QAAA,GAC9BpB,IAAA,CAACT,IAAI;UAAC0B,KAAK,EAAEC,MAAM,CAACY,gBAAiB;UAAAV,QAAA,EAAC;QAAoB,CAAM,CAAC,EACjEpB,IAAA,CAACT,IAAI;UAAC0B,KAAK,EAAEC,MAAM,CAACa,cAAe;UAAAX,QAAA,EAAC;QAAqC,CAAM,CAAC,EAChFpB,IAAA,CAACT,IAAI;UAAC0B,KAAK,EAAEC,MAAM,CAACa,cAAe;UAAAX,QAAA,EAAC;QAAgC,CAAM,CAAC,EAC3EpB,IAAA,CAACT,IAAI;UAAC0B,KAAK,EAAEC,MAAM,CAACa,cAAe;UAAAX,QAAA,EAAC;QAAiC,CAAM,CAAC,EAC5EpB,IAAA,CAACT,IAAI;UAAC0B,KAAK,EAAEC,MAAM,CAACa,cAAe;UAAAX,QAAA,EAAC;QAAyC,CAAM,CAAC;MAAA,CAChF,CAAC,EAEPlB,KAAA,CAACZ,IAAI;QAAC2B,KAAK,EAAEC,MAAM,CAACc,OAAQ;QAAAZ,QAAA,GACzB,CAAAhB,cAAA,GAAA6B,CAAA,UAAAlB,OAAO,MAAAX,cAAA,GAAA6B,CAAA,UACNjC,IAAA,CAACH,MAAM;UACLqC,KAAK,EAAC,WAAW;UACjBC,OAAO,EAAEpB,OAAQ;UACjBE,KAAK,EAAEC,MAAM,CAACkB,WAAY;UAC1BC,IAAI,EAAErC,IAAA,CAACL,SAAS;YAAC4B,IAAI,EAAE,EAAG;YAACC,KAAK,EAAErB,MAAM,CAACI;UAAM,CAAE;QAAE,CACpD,CAAC,CACH,EAEDP,IAAA,CAACH,MAAM;UACLqC,KAAK,EAAC,kBAAkB;UACxBC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;YAAA/B,cAAA,GAAAY,CAAA;YAAAZ,cAAA,GAAAC,CAAA;YAEbiC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;UACtC,CAAE;UACFC,OAAO,EAAC,SAAS;UACjBvB,KAAK,EAAEC,MAAM,CAACuB,YAAa;UAC3BJ,IAAI,EAAErC,IAAA,CAACP,KAAK;YAAC8B,IAAI,EAAE,EAAG;YAACC,KAAK,EAAErB,MAAM,CAACG;UAAQ,CAAE;QAAE,CAClD,CAAC;MAAA,CACE,CAAC;IAAA,CACH;EAAC,CACH,CAAC;AAAD,CACP;AAACF,cAAA,GAAAC,CAAA;AAEF,IAAMqC,0BAAqE,GAAG,SAAxEA,0BAAqEA,CAAAC,KAAA,EAGrE;EAAA,IAFJvB,QAAQ,GAAAuB,KAAA,CAARvB,QAAQ;IACRL,OAAO,GAAA4B,KAAA,CAAP5B,OAAO;EAAAX,cAAA,GAAAY,CAAA;EAAAZ,cAAA,GAAAC,CAAA;EAEP,IAAMuC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,KAAY,EAAEC,SAA0B,EAAK;IAAA1C,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAC,CAAA;IAEhEiC,OAAO,CAACO,KAAK,CAAC,uBAAuB,EAAE;MACrCA,KAAK,EAAEA,KAAK,CAACE,OAAO;MACpBC,KAAK,EAAEH,KAAK,CAACG,KAAK;MAClBC,cAAc,EAAEH,SAAS,CAACG,cAAc;MACxCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,OAAO,EAAE;IACX,CAAC,CAAC;EAOJ,CAAC;EAACjD,cAAA,GAAAC,CAAA;EAEF,OACEL,IAAA,CAACJ,aAAa;IACZ0D,QAAQ,EAAEtD,IAAA,CAACa,0BAA0B;MAACE,OAAO,EAAEA;IAAQ,CAAE,CAAE;IAC3DwC,OAAO,EAAEX,WAAY;IACrBY,WAAW,EAAEC,OAAQ;IAAArC,QAAA,EAEpBA;EAAQ,CACI,CAAC;AAEpB,CAAC;AAED,IAAMF,MAAM,IAAAd,cAAA,GAAAC,CAAA,OAAGb,UAAU,CAACkE,MAAM,CAAC;EAC/BvC,SAAS,EAAE;IACTwC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE;EACX,CAAC;EACDvC,YAAY,EAAE;IACZwC,UAAU,EAAE;EACd,CAAC;EACDvC,aAAa,EAAE;IACbwC,YAAY,EAAE;EAChB,CAAC;EACDrC,UAAU,EAAE;IACVsC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBxC,KAAK,EAAErB,MAAM,CAACK,IAAI;IAClBsD,YAAY,EAAE,CAAC;IACfG,SAAS,EAAE;EACb,CAAC;EACDvC,YAAY,EAAE;IACZqC,QAAQ,EAAE,EAAE;IACZvC,KAAK,EAAErB,MAAM,CAACM,IAAI;IAClBwD,SAAS,EAAE,QAAQ;IACnBH,YAAY,EAAE,EAAE;IAChBI,UAAU,EAAE;EACd,CAAC;EACDvC,WAAW,EAAE;IACXwC,SAAS,EAAE,SAAS;IACpBL,YAAY,EAAE;EAChB,CAAC;EACDlC,UAAU,EAAE;IACVmC,QAAQ,EAAE,EAAE;IACZvC,KAAK,EAAErB,MAAM,CAACM,IAAI;IAClBqD,YAAY,EAAE,CAAC;IACfI,UAAU,EAAE;EACd,CAAC;EACDrC,WAAW,EAAE;IACXsC,SAAS,EAAE,SAAS;IACpBC,eAAe,EAAEjE,MAAM,CAACO,SAAS;IACjCkD,OAAO,EAAE,EAAE;IACXS,YAAY,EAAE,CAAC;IACfP,YAAY,EAAE;EAChB,CAAC;EACDhC,gBAAgB,EAAE;IAChBiC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBxC,KAAK,EAAErB,MAAM,CAACK,IAAI;IAClBsD,YAAY,EAAE;EAChB,CAAC;EACD/B,cAAc,EAAE;IACdgC,QAAQ,EAAE,EAAE;IACZvC,KAAK,EAAErB,MAAM,CAACM,IAAI;IAClBqD,YAAY,EAAE,CAAC;IACfI,UAAU,EAAE;EACd,CAAC;EACDlC,OAAO,EAAE;IACPmC,SAAS,EAAE,SAAS;IACpBG,GAAG,EAAE;EACP,CAAC;EACDlC,WAAW,EAAE;IACXgC,eAAe,EAAEjE,MAAM,CAACG;EAC1B,CAAC;EACDmC,YAAY,EAAE;IACZ8B,WAAW,EAAEpE,MAAM,CAACG;EACtB;AACF,CAAC,CAAC;AAEF,eAAeoC,0BAA0B", "ignoreList": []}