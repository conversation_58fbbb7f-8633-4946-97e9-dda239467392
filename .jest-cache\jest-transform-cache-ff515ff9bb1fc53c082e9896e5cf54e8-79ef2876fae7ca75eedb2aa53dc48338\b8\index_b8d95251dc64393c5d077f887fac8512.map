{"version": 3, "names": ["_interopRequireDefault2", "require", "_toConsumableArray2", "_defineProperty2", "_PROPERTIES_FLIP", "_interopRequireDefault", "default", "exports", "__esModule", "atomic", "classic", "inline", "stringifyValueWithProperty", "_objectSpread2", "_objectWithoutPropertiesLoose2", "_createReactDOMStyle", "_hash", "_hyphenateStyleName", "_normalizeValueWithProperty", "_prefixStyles", "_excluded", "cache", "Map", "emptyObject", "classicGroup", "atomicGroup", "customGroup", "borderColor", "borderRadius", "borderStyle", "borderWidth", "display", "flex", "inset", "margin", "overflow", "overscroll<PERSON><PERSON><PERSON><PERSON>", "padding", "insetBlock", "insetInline", "marginInline", "marginBlock", "paddingInline", "paddingBlock", "borderBlockStartColor", "borderBlockStartStyle", "borderBlockStartWidth", "borderBlockEndColor", "borderBlockEndStyle", "borderBlockEndWidth", "borderInlineStartColor", "borderInlineStartStyle", "borderInlineStartWidth", "borderInlineEndColor", "borderInlineEndStyle", "borderInlineEndWidth", "borderEndStartRadius", "borderEndEndRadius", "borderStartStartRadius", "borderStartEndRadius", "insetBlockEnd", "insetBlockStart", "insetInlineEnd", "insetInlineStart", "marginBlockStart", "marginBlockEnd", "marginInlineStart", "marginInlineEnd", "paddingBlockStart", "paddingBlockEnd", "paddingInlineStart", "paddingInlineEnd", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "borderLeftColor", "borderLeftStyle", "borderLeftWidth", "borderRightColor", "borderRightStyle", "borderRightWidth", "right", "marginLeft", "marginRight", "paddingLeft", "paddingRight", "left", "PROPERTIES_FLIP", "PROPERTIES_I18N", "PROPERTIES_VALUE", "style", "compiledStyle", "$$css", "compiledRules", "atomicCompile", "srcProp", "prop", "value", "valueString", "cache<PERSON>ey", "cachedResult", "get", "identifier", "push", "v", "createIdentifier", "order", "rules", "createAtomicRules", "orderedRules", "set", "Object", "keys", "sort", "for<PERSON>ach", "localizeableValue", "indexOf", "_left", "_right", "propPolyfill", "ltr", "rtl", "values", "Array", "isArray", "polyfillIndices", "i", "length", "val", "ltrPolyfillValues", "rtlPolyfillValues", "ltrVal", "ltrPolyfill", "rtlPolyfill", "_ltr", "_rtl", "name", "animationKeyframes", "rest", "JSON", "stringify", "selector", "animationName", "_processKeyframesValu", "processKeyframesValue", "animationNames", "keyframesRules", "join", "apply", "block", "createDeclarationBlock", "originalStyle", "isRTL", "frozenProps", "nextStyle", "_loop", "originalValue", "originalProp", "prototype", "hasOwnProperty", "call", "originalValues", "valuePolyfill", "_ret", "property", "normalizedValue", "_processKeyframesValu2", "concat", "_block", "color", "opacity", "finalValue", "_block2", "pointerEvents", "_block3", "_block4", "_block5", "scrollbarWidth", "_block6", "domStyle", "declarationsString", "map", "prefix", "key", "hashedString", "process", "env", "NODE_ENV", "createKeyframes", "keyframes", "prefixes", "steps", "<PERSON><PERSON><PERSON>", "rule", "keyframesValue", "Error", "_createKeyframes"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.atomic = atomic;\nexports.classic = classic;\nexports.inline = inline;\nexports.stringifyValueWithProperty = stringifyValueWithProperty;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _createReactDOMStyle = _interopRequireDefault(require(\"./createReactDOMStyle\"));\nvar _hash = _interopRequireDefault(require(\"./hash\"));\nvar _hyphenateStyleName = _interopRequireDefault(require(\"./hyphenateStyleName\"));\nvar _normalizeValueWithProperty = _interopRequireDefault(require(\"./normalizeValueWithProperty\"));\nvar _prefixStyles = _interopRequireDefault(require(\"../../../modules/prefixStyles\"));\nvar _excluded = [\"animationKeyframes\"];\n/**\n * Copyright (c) Nicolas <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\nvar cache = new Map();\nvar emptyObject = {};\nvar classicGroup = 1;\nvar atomicGroup = 3;\nvar customGroup = {\n  borderColor: 2,\n  borderRadius: 2,\n  borderStyle: 2,\n  borderWidth: 2,\n  display: 2,\n  flex: 2,\n  inset: 2,\n  margin: 2,\n  overflow: 2,\n  overscrollBehavior: 2,\n  padding: 2,\n  insetBlock: 2.1,\n  insetInline: 2.1,\n  marginInline: 2.1,\n  marginBlock: 2.1,\n  paddingInline: 2.1,\n  paddingBlock: 2.1,\n  borderBlockStartColor: 2.2,\n  borderBlockStartStyle: 2.2,\n  borderBlockStartWidth: 2.2,\n  borderBlockEndColor: 2.2,\n  borderBlockEndStyle: 2.2,\n  borderBlockEndWidth: 2.2,\n  borderInlineStartColor: 2.2,\n  borderInlineStartStyle: 2.2,\n  borderInlineStartWidth: 2.2,\n  borderInlineEndColor: 2.2,\n  borderInlineEndStyle: 2.2,\n  borderInlineEndWidth: 2.2,\n  borderEndStartRadius: 2.2,\n  borderEndEndRadius: 2.2,\n  borderStartStartRadius: 2.2,\n  borderStartEndRadius: 2.2,\n  insetBlockEnd: 2.2,\n  insetBlockStart: 2.2,\n  insetInlineEnd: 2.2,\n  insetInlineStart: 2.2,\n  marginBlockStart: 2.2,\n  marginBlockEnd: 2.2,\n  marginInlineStart: 2.2,\n  marginInlineEnd: 2.2,\n  paddingBlockStart: 2.2,\n  paddingBlockEnd: 2.2,\n  paddingInlineStart: 2.2,\n  paddingInlineEnd: 2.2\n};\nvar borderTopLeftRadius = 'borderTopLeftRadius';\nvar borderTopRightRadius = 'borderTopRightRadius';\nvar borderBottomLeftRadius = 'borderBottomLeftRadius';\nvar borderBottomRightRadius = 'borderBottomRightRadius';\nvar borderLeftColor = 'borderLeftColor';\nvar borderLeftStyle = 'borderLeftStyle';\nvar borderLeftWidth = 'borderLeftWidth';\nvar borderRightColor = 'borderRightColor';\nvar borderRightStyle = 'borderRightStyle';\nvar borderRightWidth = 'borderRightWidth';\nvar right = 'right';\nvar marginLeft = 'marginLeft';\nvar marginRight = 'marginRight';\nvar paddingLeft = 'paddingLeft';\nvar paddingRight = 'paddingRight';\nvar left = 'left';\n\n// Map of LTR property names to their BiDi equivalent.\nvar PROPERTIES_FLIP = {\n  [borderTopLeftRadius]: borderTopRightRadius,\n  [borderTopRightRadius]: borderTopLeftRadius,\n  [borderBottomLeftRadius]: borderBottomRightRadius,\n  [borderBottomRightRadius]: borderBottomLeftRadius,\n  [borderLeftColor]: borderRightColor,\n  [borderLeftStyle]: borderRightStyle,\n  [borderLeftWidth]: borderRightWidth,\n  [borderRightColor]: borderLeftColor,\n  [borderRightStyle]: borderLeftStyle,\n  [borderRightWidth]: borderLeftWidth,\n  [left]: right,\n  [marginLeft]: marginRight,\n  [marginRight]: marginLeft,\n  [paddingLeft]: paddingRight,\n  [paddingRight]: paddingLeft,\n  [right]: left\n};\n\n// Map of I18N property names to their LTR equivalent.\nvar PROPERTIES_I18N = {\n  borderStartStartRadius: borderTopLeftRadius,\n  borderStartEndRadius: borderTopRightRadius,\n  borderEndStartRadius: borderBottomLeftRadius,\n  borderEndEndRadius: borderBottomRightRadius,\n  borderInlineStartColor: borderLeftColor,\n  borderInlineStartStyle: borderLeftStyle,\n  borderInlineStartWidth: borderLeftWidth,\n  borderInlineEndColor: borderRightColor,\n  borderInlineEndStyle: borderRightStyle,\n  borderInlineEndWidth: borderRightWidth,\n  insetInlineEnd: right,\n  insetInlineStart: left,\n  marginInlineStart: marginLeft,\n  marginInlineEnd: marginRight,\n  paddingInlineStart: paddingLeft,\n  paddingInlineEnd: paddingRight\n};\nvar PROPERTIES_VALUE = ['clear', 'float', 'textAlign'];\nfunction atomic(style) {\n  var compiledStyle = {\n    $$css: true\n  };\n  var compiledRules = [];\n  function atomicCompile(srcProp, prop, value) {\n    var valueString = stringifyValueWithProperty(value, prop);\n    var cacheKey = prop + valueString;\n    var cachedResult = cache.get(cacheKey);\n    var identifier;\n    if (cachedResult != null) {\n      identifier = cachedResult[0];\n      compiledRules.push(cachedResult[1]);\n    } else {\n      var v = srcProp !== prop ? cacheKey : valueString;\n      identifier = createIdentifier('r', srcProp, v);\n      var order = customGroup[srcProp] || atomicGroup;\n      var rules = createAtomicRules(identifier, prop, value);\n      var orderedRules = [rules, order];\n      compiledRules.push(orderedRules);\n      cache.set(cacheKey, [identifier, orderedRules]);\n    }\n    return identifier;\n  }\n  Object.keys(style).sort().forEach(srcProp => {\n    var value = style[srcProp];\n    if (value != null) {\n      var localizeableValue;\n      // BiDi flip values\n      if (PROPERTIES_VALUE.indexOf(srcProp) > -1) {\n        var _left = atomicCompile(srcProp, srcProp, 'left');\n        var _right = atomicCompile(srcProp, srcProp, 'right');\n        if (value === 'start') {\n          localizeableValue = [_left, _right];\n        } else if (value === 'end') {\n          localizeableValue = [_right, _left];\n        }\n      }\n      // BiDi flip properties\n      var propPolyfill = PROPERTIES_I18N[srcProp];\n      if (propPolyfill != null) {\n        var ltr = atomicCompile(srcProp, propPolyfill, value);\n        var rtl = atomicCompile(srcProp, PROPERTIES_FLIP[propPolyfill], value);\n        localizeableValue = [ltr, rtl];\n      }\n      // BiDi flip transitionProperty value\n      if (srcProp === 'transitionProperty') {\n        var values = Array.isArray(value) ? value : [value];\n        var polyfillIndices = [];\n        for (var i = 0; i < values.length; i++) {\n          var val = values[i];\n          if (typeof val === 'string' && PROPERTIES_I18N[val] != null) {\n            polyfillIndices.push(i);\n          }\n        }\n        if (polyfillIndices.length > 0) {\n          var ltrPolyfillValues = [...values];\n          var rtlPolyfillValues = [...values];\n          polyfillIndices.forEach(i => {\n            var ltrVal = ltrPolyfillValues[i];\n            if (typeof ltrVal === 'string') {\n              var ltrPolyfill = PROPERTIES_I18N[ltrVal];\n              var rtlPolyfill = PROPERTIES_FLIP[ltrPolyfill];\n              ltrPolyfillValues[i] = ltrPolyfill;\n              rtlPolyfillValues[i] = rtlPolyfill;\n              var _ltr = atomicCompile(srcProp, srcProp, ltrPolyfillValues);\n              var _rtl = atomicCompile(srcProp, srcProp, rtlPolyfillValues);\n              localizeableValue = [_ltr, _rtl];\n            }\n          });\n        }\n      }\n      if (localizeableValue == null) {\n        localizeableValue = atomicCompile(srcProp, srcProp, value);\n      } else {\n        compiledStyle['$$css$localize'] = true;\n      }\n      compiledStyle[srcProp] = localizeableValue;\n    }\n  });\n  return [compiledStyle, compiledRules];\n}\n\n/**\n * Compile simple style object to classic CSS rules.\n * No support for 'placeholderTextColor', 'scrollbarWidth', or 'pointerEvents'.\n */\nfunction classic(style, name) {\n  var compiledStyle = {\n    $$css: true\n  };\n  var compiledRules = [];\n  var animationKeyframes = style.animationKeyframes,\n    rest = (0, _objectWithoutPropertiesLoose2.default)(style, _excluded);\n  var identifier = createIdentifier('css', name, JSON.stringify(style));\n  var selector = \".\" + identifier;\n  var animationName;\n  if (animationKeyframes != null) {\n    var _processKeyframesValu = processKeyframesValue(animationKeyframes),\n      animationNames = _processKeyframesValu[0],\n      keyframesRules = _processKeyframesValu[1];\n    animationName = animationNames.join(',');\n    compiledRules.push(...keyframesRules);\n  }\n  var block = createDeclarationBlock((0, _objectSpread2.default)((0, _objectSpread2.default)({}, rest), {}, {\n    animationName\n  }));\n  compiledRules.push(\"\" + selector + block);\n  compiledStyle[identifier] = identifier;\n  return [compiledStyle, [[compiledRules, classicGroup]]];\n}\n\n/**\n * Compile simple style object to inline DOM styles.\n * No support for 'animationKeyframes', 'placeholderTextColor', 'scrollbarWidth', or 'pointerEvents'.\n */\nfunction inline(originalStyle, isRTL) {\n  var style = originalStyle || emptyObject;\n  var frozenProps = {};\n  var nextStyle = {};\n  var _loop = function _loop() {\n    var originalValue = style[originalProp];\n    var prop = originalProp;\n    var value = originalValue;\n    if (!Object.prototype.hasOwnProperty.call(style, originalProp) || originalValue == null) {\n      return \"continue\";\n    }\n\n    // BiDi flip values\n    if (PROPERTIES_VALUE.indexOf(originalProp) > -1) {\n      if (originalValue === 'start') {\n        value = isRTL ? 'right' : 'left';\n      } else if (originalValue === 'end') {\n        value = isRTL ? 'left' : 'right';\n      }\n    }\n    // BiDi flip properties\n    var propPolyfill = PROPERTIES_I18N[originalProp];\n    if (propPolyfill != null) {\n      prop = isRTL ? PROPERTIES_FLIP[propPolyfill] : propPolyfill;\n    }\n    // BiDi flip transitionProperty value\n    if (originalProp === 'transitionProperty') {\n      // $FlowFixMe\n      var originalValues = Array.isArray(originalValue) ? originalValue : [originalValue];\n      originalValues.forEach((val, i) => {\n        if (typeof val === 'string') {\n          var valuePolyfill = PROPERTIES_I18N[val];\n          if (valuePolyfill != null) {\n            originalValues[i] = isRTL ? PROPERTIES_FLIP[valuePolyfill] : valuePolyfill;\n            value = originalValues.join(' ');\n          }\n        }\n      });\n    }\n\n    // Create finalized style\n    if (!frozenProps[prop]) {\n      nextStyle[prop] = value;\n    }\n    if (prop === originalProp) {\n      frozenProps[prop] = true;\n    }\n\n    //    if (PROPERTIES_I18N.hasOwnProperty(originalProp)) {\n    //    frozenProps[prop] = true;\n    //}\n  };\n  for (var originalProp in style) {\n    var _ret = _loop();\n    if (_ret === \"continue\") continue;\n  }\n  return (0, _createReactDOMStyle.default)(nextStyle, true);\n}\n\n/**\n * Create a value string that normalizes different input values with a common\n * output.\n */\nfunction stringifyValueWithProperty(value, property) {\n  // e.g., 0 => '0px', 'black' => 'rgba(0,0,0,1)'\n  var normalizedValue = (0, _normalizeValueWithProperty.default)(value, property);\n  return typeof normalizedValue !== 'string' ? JSON.stringify(normalizedValue || '') : normalizedValue;\n}\n\n/**\n * Create the Atomic CSS rules needed for a given StyleSheet rule.\n * Translates StyleSheet declarations to CSS.\n */\nfunction createAtomicRules(identifier, property, value) {\n  var rules = [];\n  var selector = \".\" + identifier;\n\n  // Handle non-standard properties and object values that require multiple\n  // CSS rules to be created.\n  switch (property) {\n    case 'animationKeyframes':\n      {\n        var _processKeyframesValu2 = processKeyframesValue(value),\n          animationNames = _processKeyframesValu2[0],\n          keyframesRules = _processKeyframesValu2[1];\n        var block = createDeclarationBlock({\n          animationName: animationNames.join(',')\n        });\n        rules.push(\"\" + selector + block, ...keyframesRules);\n        break;\n      }\n\n    // Equivalent to using '::placeholder'\n    case 'placeholderTextColor':\n      {\n        var _block = createDeclarationBlock({\n          color: value,\n          opacity: 1\n        });\n        rules.push(selector + \"::-webkit-input-placeholder\" + _block, selector + \"::-moz-placeholder\" + _block, selector + \":-ms-input-placeholder\" + _block, selector + \"::placeholder\" + _block);\n        break;\n      }\n\n    // Polyfill for additional 'pointer-events' values\n    // See d13f78622b233a0afc0c7a200c0a0792c8ca9e58\n    case 'pointerEvents':\n      {\n        var finalValue = value;\n        if (value === 'auto' || value === 'box-only') {\n          finalValue = 'auto!important';\n          if (value === 'box-only') {\n            var _block2 = createDeclarationBlock({\n              pointerEvents: 'none'\n            });\n            rules.push(selector + \">*\" + _block2);\n          }\n        } else if (value === 'none' || value === 'box-none') {\n          finalValue = 'none!important';\n          if (value === 'box-none') {\n            var _block3 = createDeclarationBlock({\n              pointerEvents: 'auto'\n            });\n            rules.push(selector + \">*\" + _block3);\n          }\n        }\n        var _block4 = createDeclarationBlock({\n          pointerEvents: finalValue\n        });\n        rules.push(\"\" + selector + _block4);\n        break;\n      }\n\n    // Polyfill for draft spec\n    // https://drafts.csswg.org/css-scrollbars-1/\n    case 'scrollbarWidth':\n      {\n        if (value === 'none') {\n          rules.push(selector + \"::-webkit-scrollbar{display:none}\");\n        }\n        var _block5 = createDeclarationBlock({\n          scrollbarWidth: value\n        });\n        rules.push(\"\" + selector + _block5);\n        break;\n      }\n    default:\n      {\n        var _block6 = createDeclarationBlock({\n          [property]: value\n        });\n        rules.push(\"\" + selector + _block6);\n        break;\n      }\n  }\n  return rules;\n}\n\n/**\n * Creates a CSS declaration block from a StyleSheet object.\n */\nfunction createDeclarationBlock(style) {\n  var domStyle = (0, _prefixStyles.default)((0, _createReactDOMStyle.default)(style));\n  var declarationsString = Object.keys(domStyle).map(property => {\n    var value = domStyle[property];\n    var prop = (0, _hyphenateStyleName.default)(property);\n    // The prefixer may return an array of values:\n    // { display: [ '-webkit-flex', 'flex' ] }\n    // to represent \"fallback\" declarations\n    // { display: -webkit-flex; display: flex; }\n    if (Array.isArray(value)) {\n      return value.map(v => prop + \":\" + v).join(';');\n    } else {\n      return prop + \":\" + value;\n    }\n  })\n  // Once properties are hyphenated, this will put the vendor\n  // prefixed and short-form properties first in the list.\n  .sort().join(';');\n  return \"{\" + declarationsString + \";}\";\n}\n\n/**\n * An identifier is associated with a unique set of styles.\n */\nfunction createIdentifier(prefix, name, key) {\n  var hashedString = (0, _hash.default)(name + key);\n  return process.env.NODE_ENV !== 'production' ? prefix + \"-\" + name + \"-\" + hashedString : prefix + \"-\" + hashedString;\n}\n\n/**\n * Create individual CSS keyframes rules.\n */\nfunction createKeyframes(keyframes) {\n  var prefixes = ['-webkit-', ''];\n  var identifier = createIdentifier('r', 'animation', JSON.stringify(keyframes));\n  var steps = '{' + Object.keys(keyframes).map(stepName => {\n    var rule = keyframes[stepName];\n    var block = createDeclarationBlock(rule);\n    return \"\" + stepName + block;\n  }).join('') + '}';\n  var rules = prefixes.map(prefix => {\n    return \"@\" + prefix + \"keyframes \" + identifier + steps;\n  });\n  return [identifier, rules];\n}\n\n/**\n * Create CSS keyframes rules and names from a StyleSheet keyframes object.\n */\nfunction processKeyframesValue(keyframesValue) {\n  if (typeof keyframesValue === 'number') {\n    throw new Error(\"Invalid CSS keyframes type: \" + typeof keyframesValue);\n  }\n  var animationNames = [];\n  var rules = [];\n  var value = Array.isArray(keyframesValue) ? keyframesValue : [keyframesValue];\n  value.forEach(keyframes => {\n    if (typeof keyframes === 'string') {\n      // Support external animation libraries (identifiers only)\n      animationNames.push(keyframes);\n    } else {\n      // Create rules for each of the keyframes\n      var _createKeyframes = createKeyframes(keyframes),\n        identifier = _createKeyframes[0],\n        keyframesRules = _createKeyframes[1];\n      animationNames.push(identifier);\n      rules.push(...keyframesRules);\n    }\n  });\n  return [animationNames, rules];\n}"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,mBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,gBAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,gBAAA;AAEb,IAAIC,sBAAsB,GAAGJ,OAAO,CAAC,8CAA8C,CAAC,CAACK,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,MAAM,GAAGA,MAAM;AACvBF,OAAO,CAACG,OAAO,GAAGA,OAAO;AACzBH,OAAO,CAACI,MAAM,GAAGA,MAAM;AACvBJ,OAAO,CAACK,0BAA0B,GAAGA,0BAA0B;AAC/D,IAAIC,cAAc,GAAGR,sBAAsB,CAACJ,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAIa,8BAA8B,GAAGT,sBAAsB,CAACJ,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIc,oBAAoB,GAAGV,sBAAsB,CAACJ,OAAO,wBAAwB,CAAC,CAAC;AACnF,IAAIe,KAAK,GAAGX,sBAAsB,CAACJ,OAAO,SAAS,CAAC,CAAC;AACrD,IAAIgB,mBAAmB,GAAGZ,sBAAsB,CAACJ,OAAO,uBAAuB,CAAC,CAAC;AACjF,IAAIiB,2BAA2B,GAAGb,sBAAsB,CAACJ,OAAO,+BAA+B,CAAC,CAAC;AACjG,IAAIkB,aAAa,GAAGd,sBAAsB,CAACJ,OAAO,gCAAgC,CAAC,CAAC;AACpF,IAAImB,SAAS,GAAG,CAAC,oBAAoB,CAAC;AAStC,IAAIC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;AACrB,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,WAAW,GAAG,CAAC;AACnB,IAAIC,WAAW,GAAG;EAChBC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfC,WAAW,EAAE,CAAC;EACdC,WAAW,EAAE,CAAC;EACdC,OAAO,EAAE,CAAC;EACVC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE,CAAC;EACXC,kBAAkB,EAAE,CAAC;EACrBC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,GAAG;EACfC,WAAW,EAAE,GAAG;EAChBC,YAAY,EAAE,GAAG;EACjBC,WAAW,EAAE,GAAG;EAChBC,aAAa,EAAE,GAAG;EAClBC,YAAY,EAAE,GAAG;EACjBC,qBAAqB,EAAE,GAAG;EAC1BC,qBAAqB,EAAE,GAAG;EAC1BC,qBAAqB,EAAE,GAAG;EAC1BC,mBAAmB,EAAE,GAAG;EACxBC,mBAAmB,EAAE,GAAG;EACxBC,mBAAmB,EAAE,GAAG;EACxBC,sBAAsB,EAAE,GAAG;EAC3BC,sBAAsB,EAAE,GAAG;EAC3BC,sBAAsB,EAAE,GAAG;EAC3BC,oBAAoB,EAAE,GAAG;EACzBC,oBAAoB,EAAE,GAAG;EACzBC,oBAAoB,EAAE,GAAG;EACzBC,oBAAoB,EAAE,GAAG;EACzBC,kBAAkB,EAAE,GAAG;EACvBC,sBAAsB,EAAE,GAAG;EAC3BC,oBAAoB,EAAE,GAAG;EACzBC,aAAa,EAAE,GAAG;EAClBC,eAAe,EAAE,GAAG;EACpBC,cAAc,EAAE,GAAG;EACnBC,gBAAgB,EAAE,GAAG;EACrBC,gBAAgB,EAAE,GAAG;EACrBC,cAAc,EAAE,GAAG;EACnBC,iBAAiB,EAAE,GAAG;EACtBC,eAAe,EAAE,GAAG;EACpBC,iBAAiB,EAAE,GAAG;EACtBC,eAAe,EAAE,GAAG;EACpBC,kBAAkB,EAAE,GAAG;EACvBC,gBAAgB,EAAE;AACpB,CAAC;AACD,IAAIC,mBAAmB,GAAG,qBAAqB;AAC/C,IAAIC,oBAAoB,GAAG,sBAAsB;AACjD,IAAIC,sBAAsB,GAAG,wBAAwB;AACrD,IAAIC,uBAAuB,GAAG,yBAAyB;AACvD,IAAIC,eAAe,GAAG,iBAAiB;AACvC,IAAIC,eAAe,GAAG,iBAAiB;AACvC,IAAIC,eAAe,GAAG,iBAAiB;AACvC,IAAIC,gBAAgB,GAAG,kBAAkB;AACzC,IAAIC,gBAAgB,GAAG,kBAAkB;AACzC,IAAIC,gBAAgB,GAAG,kBAAkB;AACzC,IAAIC,KAAK,GAAG,OAAO;AACnB,IAAIC,UAAU,GAAG,YAAY;AAC7B,IAAIC,WAAW,GAAG,aAAa;AAC/B,IAAIC,WAAW,GAAG,aAAa;AAC/B,IAAIC,YAAY,GAAG,cAAc;AACjC,IAAIC,IAAI,GAAG,MAAM;AAGjB,IAAIC,eAAe,IAAApF,gBAAA,WAAAD,gBAAA,CAAAG,OAAA,MAAAH,gBAAA,CAAAG,OAAA,MAAAH,gBAAA,CAAAG,OAAA,MAAAH,gBAAA,CAAAG,OAAA,MAAAH,gBAAA,CAAAG,OAAA,MAAAH,gBAAA,CAAAG,OAAA,MAAAH,gBAAA,CAAAG,OAAA,MAAAH,gBAAA,CAAAG,OAAA,MAAAH,gBAAA,CAAAG,OAAA,MAAAH,gBAAA,CAAAG,OAAA,EAAAF,gBAAA,EAChBoE,mBAAmB,EAAGC,oBAAoB,GAC1CA,oBAAoB,EAAGD,mBAAmB,GAC1CE,sBAAsB,EAAGC,uBAAuB,GAChDA,uBAAuB,EAAGD,sBAAsB,GAChDE,eAAe,EAAGG,gBAAgB,GAClCF,eAAe,EAAGG,gBAAgB,GAClCF,eAAe,EAAGG,gBAAgB,GAClCF,gBAAgB,EAAGH,eAAe,GAClCI,gBAAgB,EAAGH,eAAe,GAClCI,gBAAgB,EAAGH,eAAe,OAAA3E,gBAAA,CAAAG,OAAA,MAAAH,gBAAA,CAAAG,OAAA,MAAAH,gBAAA,CAAAG,OAAA,MAAAH,gBAAA,CAAAG,OAAA,MAAAH,gBAAA,CAAAG,OAAA,MAAAH,gBAAA,CAAAG,OAAA,EAAAF,gBAAA,EAClCmF,IAAI,EAAGL,KAAK,GACZC,UAAU,EAAGC,WAAW,GACxBA,WAAW,EAAGD,UAAU,GACxBE,WAAW,EAAGC,YAAY,GAC1BA,YAAY,EAAGD,WAAW,GAC1BH,KAAK,EAAGK,IAAI,EACd;AAGD,IAAIE,eAAe,GAAG;EACpB/B,sBAAsB,EAAEc,mBAAmB;EAC3Cb,oBAAoB,EAAEc,oBAAoB;EAC1CjB,oBAAoB,EAAEkB,sBAAsB;EAC5CjB,kBAAkB,EAAEkB,uBAAuB;EAC3CzB,sBAAsB,EAAE0B,eAAe;EACvCzB,sBAAsB,EAAE0B,eAAe;EACvCzB,sBAAsB,EAAE0B,eAAe;EACvCzB,oBAAoB,EAAE0B,gBAAgB;EACtCzB,oBAAoB,EAAE0B,gBAAgB;EACtCzB,oBAAoB,EAAE0B,gBAAgB;EACtCnB,cAAc,EAAEoB,KAAK;EACrBnB,gBAAgB,EAAEwB,IAAI;EACtBrB,iBAAiB,EAAEiB,UAAU;EAC7BhB,eAAe,EAAEiB,WAAW;EAC5Bd,kBAAkB,EAAEe,WAAW;EAC/Bd,gBAAgB,EAAEe;AACpB,CAAC;AACD,IAAII,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC;AACtD,SAASjF,MAAMA,CAACkF,KAAK,EAAE;EACrB,IAAIC,aAAa,GAAG;IAClBC,KAAK,EAAE;EACT,CAAC;EACD,IAAIC,aAAa,GAAG,EAAE;EACtB,SAASC,aAAaA,CAACC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAE;IAC3C,IAAIC,WAAW,GAAGvF,0BAA0B,CAACsF,KAAK,EAAED,IAAI,CAAC;IACzD,IAAIG,QAAQ,GAAGH,IAAI,GAAGE,WAAW;IACjC,IAAIE,YAAY,GAAGhF,KAAK,CAACiF,GAAG,CAACF,QAAQ,CAAC;IACtC,IAAIG,UAAU;IACd,IAAIF,YAAY,IAAI,IAAI,EAAE;MACxBE,UAAU,GAAGF,YAAY,CAAC,CAAC,CAAC;MAC5BP,aAAa,CAACU,IAAI,CAACH,YAAY,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,MAAM;MACL,IAAII,CAAC,GAAGT,OAAO,KAAKC,IAAI,GAAGG,QAAQ,GAAGD,WAAW;MACjDI,UAAU,GAAGG,gBAAgB,CAAC,GAAG,EAAEV,OAAO,EAAES,CAAC,CAAC;MAC9C,IAAIE,KAAK,GAAGjF,WAAW,CAACsE,OAAO,CAAC,IAAIvE,WAAW;MAC/C,IAAImF,KAAK,GAAGC,iBAAiB,CAACN,UAAU,EAAEN,IAAI,EAAEC,KAAK,CAAC;MACtD,IAAIY,YAAY,GAAG,CAACF,KAAK,EAAED,KAAK,CAAC;MACjCb,aAAa,CAACU,IAAI,CAACM,YAAY,CAAC;MAChCzF,KAAK,CAAC0F,GAAG,CAACX,QAAQ,EAAE,CAACG,UAAU,EAAEO,YAAY,CAAC,CAAC;IACjD;IACA,OAAOP,UAAU;EACnB;EACAS,MAAM,CAACC,IAAI,CAACtB,KAAK,CAAC,CAACuB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,UAAAnB,OAAO,EAAI;IAC3C,IAAIE,KAAK,GAAGP,KAAK,CAACK,OAAO,CAAC;IAC1B,IAAIE,KAAK,IAAI,IAAI,EAAE;MACjB,IAAIkB,iBAAiB;MAErB,IAAI1B,gBAAgB,CAAC2B,OAAO,CAACrB,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;QAC1C,IAAIsB,KAAK,GAAGvB,aAAa,CAACC,OAAO,EAAEA,OAAO,EAAE,MAAM,CAAC;QACnD,IAAIuB,MAAM,GAAGxB,aAAa,CAACC,OAAO,EAAEA,OAAO,EAAE,OAAO,CAAC;QACrD,IAAIE,KAAK,KAAK,OAAO,EAAE;UACrBkB,iBAAiB,GAAG,CAACE,KAAK,EAAEC,MAAM,CAAC;QACrC,CAAC,MAAM,IAAIrB,KAAK,KAAK,KAAK,EAAE;UAC1BkB,iBAAiB,GAAG,CAACG,MAAM,EAAED,KAAK,CAAC;QACrC;MACF;MAEA,IAAIE,YAAY,GAAG/B,eAAe,CAACO,OAAO,CAAC;MAC3C,IAAIwB,YAAY,IAAI,IAAI,EAAE;QACxB,IAAIC,GAAG,GAAG1B,aAAa,CAACC,OAAO,EAAEwB,YAAY,EAAEtB,KAAK,CAAC;QACrD,IAAIwB,GAAG,GAAG3B,aAAa,CAACC,OAAO,EAAER,eAAe,CAACgC,YAAY,CAAC,EAAEtB,KAAK,CAAC;QACtEkB,iBAAiB,GAAG,CAACK,GAAG,EAAEC,GAAG,CAAC;MAChC;MAEA,IAAI1B,OAAO,KAAK,oBAAoB,EAAE;QACpC,IAAI2B,MAAM,GAAGC,KAAK,CAACC,OAAO,CAAC3B,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;QACnD,IAAI4B,eAAe,GAAG,EAAE;QACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;UACtC,IAAIE,GAAG,GAAGN,MAAM,CAACI,CAAC,CAAC;UACnB,IAAI,OAAOE,GAAG,KAAK,QAAQ,IAAIxC,eAAe,CAACwC,GAAG,CAAC,IAAI,IAAI,EAAE;YAC3DH,eAAe,CAACtB,IAAI,CAACuB,CAAC,CAAC;UACzB;QACF;QACA,IAAID,eAAe,CAACE,MAAM,GAAG,CAAC,EAAE;UAC9B,IAAIE,iBAAiB,OAAAhI,mBAAA,CAAAI,OAAA,EAAOqH,MAAM,CAAC;UACnC,IAAIQ,iBAAiB,OAAAjI,mBAAA,CAAAI,OAAA,EAAOqH,MAAM,CAAC;UACnCG,eAAe,CAACX,OAAO,CAAC,UAAAY,CAAC,EAAI;YAC3B,IAAIK,MAAM,GAAGF,iBAAiB,CAACH,CAAC,CAAC;YACjC,IAAI,OAAOK,MAAM,KAAK,QAAQ,EAAE;cAC9B,IAAIC,WAAW,GAAG5C,eAAe,CAAC2C,MAAM,CAAC;cACzC,IAAIE,WAAW,GAAG9C,eAAe,CAAC6C,WAAW,CAAC;cAC9CH,iBAAiB,CAACH,CAAC,CAAC,GAAGM,WAAW;cAClCF,iBAAiB,CAACJ,CAAC,CAAC,GAAGO,WAAW;cAClC,IAAIC,IAAI,GAAGxC,aAAa,CAACC,OAAO,EAAEA,OAAO,EAAEkC,iBAAiB,CAAC;cAC7D,IAAIM,IAAI,GAAGzC,aAAa,CAACC,OAAO,EAAEA,OAAO,EAAEmC,iBAAiB,CAAC;cAC7Df,iBAAiB,GAAG,CAACmB,IAAI,EAAEC,IAAI,CAAC;YAClC;UACF,CAAC,CAAC;QACJ;MACF;MACA,IAAIpB,iBAAiB,IAAI,IAAI,EAAE;QAC7BA,iBAAiB,GAAGrB,aAAa,CAACC,OAAO,EAAEA,OAAO,EAAEE,KAAK,CAAC;MAC5D,CAAC,MAAM;QACLN,aAAa,CAAC,gBAAgB,CAAC,GAAG,IAAI;MACxC;MACAA,aAAa,CAACI,OAAO,CAAC,GAAGoB,iBAAiB;IAC5C;EACF,CAAC,CAAC;EACF,OAAO,CAACxB,aAAa,EAAEE,aAAa,CAAC;AACvC;AAMA,SAASpF,OAAOA,CAACiF,KAAK,EAAE8C,IAAI,EAAE;EAC5B,IAAI7C,aAAa,GAAG;IAClBC,KAAK,EAAE;EACT,CAAC;EACD,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAI4C,kBAAkB,GAAG/C,KAAK,CAAC+C,kBAAkB;IAC/CC,IAAI,GAAG,CAAC,CAAC,EAAE7H,8BAA8B,CAACR,OAAO,EAAEqF,KAAK,EAAEvE,SAAS,CAAC;EACtE,IAAImF,UAAU,GAAGG,gBAAgB,CAAC,KAAK,EAAE+B,IAAI,EAAEG,IAAI,CAACC,SAAS,CAAClD,KAAK,CAAC,CAAC;EACrE,IAAImD,QAAQ,GAAG,GAAG,GAAGvC,UAAU;EAC/B,IAAIwC,aAAa;EACjB,IAAIL,kBAAkB,IAAI,IAAI,EAAE;IAC9B,IAAIM,qBAAqB,GAAGC,qBAAqB,CAACP,kBAAkB,CAAC;MACnEQ,cAAc,GAAGF,qBAAqB,CAAC,CAAC,CAAC;MACzCG,cAAc,GAAGH,qBAAqB,CAAC,CAAC,CAAC;IAC3CD,aAAa,GAAGG,cAAc,CAACE,IAAI,CAAC,GAAG,CAAC;IACxCtD,aAAa,CAACU,IAAI,CAAA6C,KAAA,CAAlBvD,aAAa,MAAA5F,mBAAA,CAAAI,OAAA,EAAS6I,cAAc,EAAC;EACvC;EACA,IAAIG,KAAK,GAAGC,sBAAsB,CAAC,CAAC,CAAC,EAAE1I,cAAc,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEO,cAAc,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEqI,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;IACxGI,aAAa,EAAbA;EACF,CAAC,CAAC,CAAC;EACHjD,aAAa,CAACU,IAAI,CAAC,EAAE,GAAGsC,QAAQ,GAAGQ,KAAK,CAAC;EACzC1D,aAAa,CAACW,UAAU,CAAC,GAAGA,UAAU;EACtC,OAAO,CAACX,aAAa,EAAE,CAAC,CAACE,aAAa,EAAEtE,YAAY,CAAC,CAAC,CAAC;AACzD;AAMA,SAASb,MAAMA,CAAC6I,aAAa,EAAEC,KAAK,EAAE;EACpC,IAAI9D,KAAK,GAAG6D,aAAa,IAAIjI,WAAW;EACxC,IAAImI,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3B,IAAIC,aAAa,GAAGlE,KAAK,CAACmE,YAAY,CAAC;IACvC,IAAI7D,IAAI,GAAG6D,YAAY;IACvB,IAAI5D,KAAK,GAAG2D,aAAa;IACzB,IAAI,CAAC7C,MAAM,CAAC+C,SAAS,CAACC,cAAc,CAACC,IAAI,CAACtE,KAAK,EAAEmE,YAAY,CAAC,IAAID,aAAa,IAAI,IAAI,EAAE;MACvF,OAAO,UAAU;IACnB;IAGA,IAAInE,gBAAgB,CAAC2B,OAAO,CAACyC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE;MAC/C,IAAID,aAAa,KAAK,OAAO,EAAE;QAC7B3D,KAAK,GAAGuD,KAAK,GAAG,OAAO,GAAG,MAAM;MAClC,CAAC,MAAM,IAAII,aAAa,KAAK,KAAK,EAAE;QAClC3D,KAAK,GAAGuD,KAAK,GAAG,MAAM,GAAG,OAAO;MAClC;IACF;IAEA,IAAIjC,YAAY,GAAG/B,eAAe,CAACqE,YAAY,CAAC;IAChD,IAAItC,YAAY,IAAI,IAAI,EAAE;MACxBvB,IAAI,GAAGwD,KAAK,GAAGjE,eAAe,CAACgC,YAAY,CAAC,GAAGA,YAAY;IAC7D;IAEA,IAAIsC,YAAY,KAAK,oBAAoB,EAAE;MAEzC,IAAII,cAAc,GAAGtC,KAAK,CAACC,OAAO,CAACgC,aAAa,CAAC,GAAGA,aAAa,GAAG,CAACA,aAAa,CAAC;MACnFK,cAAc,CAAC/C,OAAO,CAAC,UAACc,GAAG,EAAEF,CAAC,EAAK;QACjC,IAAI,OAAOE,GAAG,KAAK,QAAQ,EAAE;UAC3B,IAAIkC,aAAa,GAAG1E,eAAe,CAACwC,GAAG,CAAC;UACxC,IAAIkC,aAAa,IAAI,IAAI,EAAE;YACzBD,cAAc,CAACnC,CAAC,CAAC,GAAG0B,KAAK,GAAGjE,eAAe,CAAC2E,aAAa,CAAC,GAAGA,aAAa;YAC1EjE,KAAK,GAAGgE,cAAc,CAACd,IAAI,CAAC,GAAG,CAAC;UAClC;QACF;MACF,CAAC,CAAC;IACJ;IAGA,IAAI,CAACM,WAAW,CAACzD,IAAI,CAAC,EAAE;MACtB0D,SAAS,CAAC1D,IAAI,CAAC,GAAGC,KAAK;IACzB;IACA,IAAID,IAAI,KAAK6D,YAAY,EAAE;MACzBJ,WAAW,CAACzD,IAAI,CAAC,GAAG,IAAI;IAC1B;EAKF,CAAC;EACD,KAAK,IAAI6D,YAAY,IAAInE,KAAK,EAAE;IAC9B,IAAIyE,IAAI,GAAGR,KAAK,CAAC,CAAC;IAClB,IAAIQ,IAAI,KAAK,UAAU,EAAE;EAC3B;EACA,OAAO,CAAC,CAAC,EAAErJ,oBAAoB,CAACT,OAAO,EAAEqJ,SAAS,EAAE,IAAI,CAAC;AAC3D;AAMA,SAAS/I,0BAA0BA,CAACsF,KAAK,EAAEmE,QAAQ,EAAE;EAEnD,IAAIC,eAAe,GAAG,CAAC,CAAC,EAAEpJ,2BAA2B,CAACZ,OAAO,EAAE4F,KAAK,EAAEmE,QAAQ,CAAC;EAC/E,OAAO,OAAOC,eAAe,KAAK,QAAQ,GAAG1B,IAAI,CAACC,SAAS,CAACyB,eAAe,IAAI,EAAE,CAAC,GAAGA,eAAe;AACtG;AAMA,SAASzD,iBAAiBA,CAACN,UAAU,EAAE8D,QAAQ,EAAEnE,KAAK,EAAE;EACtD,IAAIU,KAAK,GAAG,EAAE;EACd,IAAIkC,QAAQ,GAAG,GAAG,GAAGvC,UAAU;EAI/B,QAAQ8D,QAAQ;IACd,KAAK,oBAAoB;MACvB;QACE,IAAIE,sBAAsB,GAAGtB,qBAAqB,CAAC/C,KAAK,CAAC;UACvDgD,cAAc,GAAGqB,sBAAsB,CAAC,CAAC,CAAC;UAC1CpB,cAAc,GAAGoB,sBAAsB,CAAC,CAAC,CAAC;QAC5C,IAAIjB,KAAK,GAAGC,sBAAsB,CAAC;UACjCR,aAAa,EAAEG,cAAc,CAACE,IAAI,CAAC,GAAG;QACxC,CAAC,CAAC;QACFxC,KAAK,CAACJ,IAAI,CAAA6C,KAAA,CAAVzC,KAAK,GAAM,EAAE,GAAGkC,QAAQ,GAAGQ,KAAK,EAAAkB,MAAA,KAAAtK,mBAAA,CAAAI,OAAA,EAAK6I,cAAc,GAAC;QACpD;MACF;IAGF,KAAK,sBAAsB;MACzB;QACE,IAAIsB,MAAM,GAAGlB,sBAAsB,CAAC;UAClCmB,KAAK,EAAExE,KAAK;UACZyE,OAAO,EAAE;QACX,CAAC,CAAC;QACF/D,KAAK,CAACJ,IAAI,CAACsC,QAAQ,GAAG,6BAA6B,GAAG2B,MAAM,EAAE3B,QAAQ,GAAG,oBAAoB,GAAG2B,MAAM,EAAE3B,QAAQ,GAAG,wBAAwB,GAAG2B,MAAM,EAAE3B,QAAQ,GAAG,eAAe,GAAG2B,MAAM,CAAC;QAC1L;MACF;IAIF,KAAK,eAAe;MAClB;QACE,IAAIG,UAAU,GAAG1E,KAAK;QACtB,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,UAAU,EAAE;UAC5C0E,UAAU,GAAG,gBAAgB;UAC7B,IAAI1E,KAAK,KAAK,UAAU,EAAE;YACxB,IAAI2E,OAAO,GAAGtB,sBAAsB,CAAC;cACnCuB,aAAa,EAAE;YACjB,CAAC,CAAC;YACFlE,KAAK,CAACJ,IAAI,CAACsC,QAAQ,GAAG,IAAI,GAAG+B,OAAO,CAAC;UACvC;QACF,CAAC,MAAM,IAAI3E,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,UAAU,EAAE;UACnD0E,UAAU,GAAG,gBAAgB;UAC7B,IAAI1E,KAAK,KAAK,UAAU,EAAE;YACxB,IAAI6E,OAAO,GAAGxB,sBAAsB,CAAC;cACnCuB,aAAa,EAAE;YACjB,CAAC,CAAC;YACFlE,KAAK,CAACJ,IAAI,CAACsC,QAAQ,GAAG,IAAI,GAAGiC,OAAO,CAAC;UACvC;QACF;QACA,IAAIC,OAAO,GAAGzB,sBAAsB,CAAC;UACnCuB,aAAa,EAAEF;QACjB,CAAC,CAAC;QACFhE,KAAK,CAACJ,IAAI,CAAC,EAAE,GAAGsC,QAAQ,GAAGkC,OAAO,CAAC;QACnC;MACF;IAIF,KAAK,gBAAgB;MACnB;QACE,IAAI9E,KAAK,KAAK,MAAM,EAAE;UACpBU,KAAK,CAACJ,IAAI,CAACsC,QAAQ,GAAG,mCAAmC,CAAC;QAC5D;QACA,IAAImC,OAAO,GAAG1B,sBAAsB,CAAC;UACnC2B,cAAc,EAAEhF;QAClB,CAAC,CAAC;QACFU,KAAK,CAACJ,IAAI,CAAC,EAAE,GAAGsC,QAAQ,GAAGmC,OAAO,CAAC;QACnC;MACF;IACF;MACE;QACE,IAAIE,OAAO,GAAG5B,sBAAsB,KAAApJ,gBAAA,CAAAG,OAAA,MACjC+J,QAAQ,EAAGnE,KAAK,CAClB,CAAC;QACFU,KAAK,CAACJ,IAAI,CAAC,EAAE,GAAGsC,QAAQ,GAAGqC,OAAO,CAAC;QACnC;MACF;EACJ;EACA,OAAOvE,KAAK;AACd;AAKA,SAAS2C,sBAAsBA,CAAC5D,KAAK,EAAE;EACrC,IAAIyF,QAAQ,GAAG,CAAC,CAAC,EAAEjK,aAAa,CAACb,OAAO,EAAE,CAAC,CAAC,EAAES,oBAAoB,CAACT,OAAO,EAAEqF,KAAK,CAAC,CAAC;EACnF,IAAI0F,kBAAkB,GAAGrE,MAAM,CAACC,IAAI,CAACmE,QAAQ,CAAC,CAACE,GAAG,CAAC,UAAAjB,QAAQ,EAAI;IAC7D,IAAInE,KAAK,GAAGkF,QAAQ,CAACf,QAAQ,CAAC;IAC9B,IAAIpE,IAAI,GAAG,CAAC,CAAC,EAAEhF,mBAAmB,CAACX,OAAO,EAAE+J,QAAQ,CAAC;IAKrD,IAAIzC,KAAK,CAACC,OAAO,CAAC3B,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAACoF,GAAG,CAAC,UAAA7E,CAAC;QAAA,OAAIR,IAAI,GAAG,GAAG,GAAGQ,CAAC;MAAA,EAAC,CAAC2C,IAAI,CAAC,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,OAAOnD,IAAI,GAAG,GAAG,GAAGC,KAAK;IAC3B;EACF,CAAC,CAAC,CAGDgB,IAAI,CAAC,CAAC,CAACkC,IAAI,CAAC,GAAG,CAAC;EACjB,OAAO,GAAG,GAAGiC,kBAAkB,GAAG,IAAI;AACxC;AAKA,SAAS3E,gBAAgBA,CAAC6E,MAAM,EAAE9C,IAAI,EAAE+C,GAAG,EAAE;EAC3C,IAAIC,YAAY,GAAG,CAAC,CAAC,EAAEzK,KAAK,CAACV,OAAO,EAAEmI,IAAI,GAAG+C,GAAG,CAAC;EACjD,OAAOE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGL,MAAM,GAAG,GAAG,GAAG9C,IAAI,GAAG,GAAG,GAAGgD,YAAY,GAAGF,MAAM,GAAG,GAAG,GAAGE,YAAY;AACvH;AAKA,SAASI,eAAeA,CAACC,SAAS,EAAE;EAClC,IAAIC,QAAQ,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC;EAC/B,IAAIxF,UAAU,GAAGG,gBAAgB,CAAC,GAAG,EAAE,WAAW,EAAEkC,IAAI,CAACC,SAAS,CAACiD,SAAS,CAAC,CAAC;EAC9E,IAAIE,KAAK,GAAG,GAAG,GAAGhF,MAAM,CAACC,IAAI,CAAC6E,SAAS,CAAC,CAACR,GAAG,CAAC,UAAAW,QAAQ,EAAI;IACvD,IAAIC,IAAI,GAAGJ,SAAS,CAACG,QAAQ,CAAC;IAC9B,IAAI3C,KAAK,GAAGC,sBAAsB,CAAC2C,IAAI,CAAC;IACxC,OAAO,EAAE,GAAGD,QAAQ,GAAG3C,KAAK;EAC9B,CAAC,CAAC,CAACF,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;EACjB,IAAIxC,KAAK,GAAGmF,QAAQ,CAACT,GAAG,CAAC,UAAAC,MAAM,EAAI;IACjC,OAAO,GAAG,GAAGA,MAAM,GAAG,YAAY,GAAGhF,UAAU,GAAGyF,KAAK;EACzD,CAAC,CAAC;EACF,OAAO,CAACzF,UAAU,EAAEK,KAAK,CAAC;AAC5B;AAKA,SAASqC,qBAAqBA,CAACkD,cAAc,EAAE;EAC7C,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;IACtC,MAAM,IAAIC,KAAK,CAAC,8BAA8B,GAAG,OAAOD,cAAc,CAAC;EACzE;EACA,IAAIjD,cAAc,GAAG,EAAE;EACvB,IAAItC,KAAK,GAAG,EAAE;EACd,IAAIV,KAAK,GAAG0B,KAAK,CAACC,OAAO,CAACsE,cAAc,CAAC,GAAGA,cAAc,GAAG,CAACA,cAAc,CAAC;EAC7EjG,KAAK,CAACiB,OAAO,CAAC,UAAA2E,SAAS,EAAI;IACzB,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;MAEjC5C,cAAc,CAAC1C,IAAI,CAACsF,SAAS,CAAC;IAChC,CAAC,MAAM;MAEL,IAAIO,gBAAgB,GAAGR,eAAe,CAACC,SAAS,CAAC;QAC/CvF,UAAU,GAAG8F,gBAAgB,CAAC,CAAC,CAAC;QAChClD,cAAc,GAAGkD,gBAAgB,CAAC,CAAC,CAAC;MACtCnD,cAAc,CAAC1C,IAAI,CAACD,UAAU,CAAC;MAC/BK,KAAK,CAACJ,IAAI,CAAA6C,KAAA,CAAVzC,KAAK,MAAA1G,mBAAA,CAAAI,OAAA,EAAS6I,cAAc,EAAC;IAC/B;EACF,CAAC,CAAC;EACF,OAAO,CAACD,cAAc,EAAEtC,KAAK,CAAC;AAChC", "ignoreList": []}