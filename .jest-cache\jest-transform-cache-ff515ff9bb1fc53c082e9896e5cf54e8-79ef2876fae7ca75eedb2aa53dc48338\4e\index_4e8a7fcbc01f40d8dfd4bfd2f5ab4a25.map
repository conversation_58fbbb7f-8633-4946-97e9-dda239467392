{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_PanResponder", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _PanResponder = _interopRequireDefault(require(\"../../vendor/react-native/PanResponder\"));\nvar _default = exports.default = _PanResponder.default;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,aAAa,GAAGL,sBAAsB,CAACC,OAAO,yCAAyC,CAAC,CAAC;AAC7F,IAAIK,QAAQ,GAAGH,OAAO,CAACD,OAAO,GAAGG,aAAa,CAACH,OAAO;AACtDK,MAAM,CAACJ,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}