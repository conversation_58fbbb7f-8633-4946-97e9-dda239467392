{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "usePressEvents", "_PressResponder", "_react", "hostRef", "config", "pressResponderRef", "useRef", "current", "pressResponder", "useEffect", "configure", "reset", "useDebugValue", "getEventHandlers", "module"], "sources": ["index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = usePressEvents;\nvar _PressResponder = _interopRequireDefault(require(\"./PressResponder\"));\nvar _react = require(\"react\");\nfunction usePressEvents(hostRef, config) {\n  var pressResponderRef = (0, _react.useRef)(null);\n  if (pressResponderRef.current == null) {\n    pressResponderRef.current = new _PressResponder.default(config);\n  }\n  var pressResponder = pressResponderRef.current;\n\n  // Re-configure to use the current node and configuration.\n  (0, _react.useEffect)(() => {\n    pressResponder.configure(config);\n  }, [config, pressResponder]);\n\n  // Reset the `pressResponder` when cleanup needs to occur. This is\n  // a separate effect because we do not want to rest the responder when `config` changes.\n  (0, _react.useEffect)(() => {\n    return () => {\n      pressResponder.reset();\n    };\n  }, [pressResponder]);\n  (0, _react.useDebugValue)(config);\n  return pressResponder.getEventHandlers();\n}\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAGG,cAAc;AAChC,IAAIC,eAAe,GAAGN,sBAAsB,CAACC,OAAO,mBAAmB,CAAC,CAAC;AACzE,IAAIM,MAAM,GAAGN,OAAO,CAAC,OAAO,CAAC;AAC7B,SAASI,cAAcA,CAACG,OAAO,EAAEC,MAAM,EAAE;EACvC,IAAIC,iBAAiB,GAAG,CAAC,CAAC,EAAEH,MAAM,CAACI,MAAM,EAAE,IAAI,CAAC;EAChD,IAAID,iBAAiB,CAACE,OAAO,IAAI,IAAI,EAAE;IACrCF,iBAAiB,CAACE,OAAO,GAAG,IAAIN,eAAe,CAACJ,OAAO,CAACO,MAAM,CAAC;EACjE;EACA,IAAII,cAAc,GAAGH,iBAAiB,CAACE,OAAO;EAG9C,CAAC,CAAC,EAAEL,MAAM,CAACO,SAAS,EAAE,YAAM;IAC1BD,cAAc,CAACE,SAAS,CAACN,MAAM,CAAC;EAClC,CAAC,EAAE,CAACA,MAAM,EAAEI,cAAc,CAAC,CAAC;EAI5B,CAAC,CAAC,EAAEN,MAAM,CAACO,SAAS,EAAE,YAAM;IAC1B,OAAO,YAAM;MACXD,cAAc,CAACG,KAAK,CAAC,CAAC;IACxB,CAAC;EACH,CAAC,EAAE,CAACH,cAAc,CAAC,CAAC;EACpB,CAAC,CAAC,EAAEN,MAAM,CAACU,aAAa,EAAER,MAAM,CAAC;EACjC,OAAOI,cAAc,CAACK,gBAAgB,CAAC,CAAC;AAC1C;AACAC,MAAM,CAAChB,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}