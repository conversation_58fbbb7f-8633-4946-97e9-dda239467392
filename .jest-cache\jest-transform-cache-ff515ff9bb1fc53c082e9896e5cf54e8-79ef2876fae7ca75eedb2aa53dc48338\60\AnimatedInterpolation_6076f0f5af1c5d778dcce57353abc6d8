7e30f475bce54f1d54c41214123e782d
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _AnimatedWithChildren = _interopRequireDefault(require("./AnimatedWithChildren"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../NativeAnimatedHelper"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var _normalizeColors = _interopRequireDefault(require("@react-native/normalize-colors"));
var __DEV__ = process.env.NODE_ENV !== 'production';
var linear = function linear(t) {
  return t;
};
function createInterpolation(config) {
  if (config.outputRange && typeof config.outputRange[0] === 'string') {
    return createInterpolationFromStringOutputRange(config);
  }
  var outputRange = config.outputRange;
  var inputRange = config.inputRange;
  if (__DEV__) {
    checkInfiniteRange('outputRange', outputRange);
    checkInfiniteRange('inputRange', inputRange);
    checkValidInputRange(inputRange);
    (0, _invariant.default)(inputRange.length === outputRange.length, 'inputRange (' + inputRange.length + ') and outputRange (' + outputRange.length + ') must have the same length');
  }
  var easing = config.easing || linear;
  var extrapolateLeft = 'extend';
  if (config.extrapolateLeft !== undefined) {
    extrapolateLeft = config.extrapolateLeft;
  } else if (config.extrapolate !== undefined) {
    extrapolateLeft = config.extrapolate;
  }
  var extrapolateRight = 'extend';
  if (config.extrapolateRight !== undefined) {
    extrapolateRight = config.extrapolateRight;
  } else if (config.extrapolate !== undefined) {
    extrapolateRight = config.extrapolate;
  }
  return function (input) {
    (0, _invariant.default)(typeof input === 'number', 'Cannot interpolation an input which is not a number');
    var range = findRange(input, inputRange);
    return interpolate(input, inputRange[range], inputRange[range + 1], outputRange[range], outputRange[range + 1], easing, extrapolateLeft, extrapolateRight);
  };
}
function interpolate(input, inputMin, inputMax, outputMin, outputMax, easing, extrapolateLeft, extrapolateRight) {
  var result = input;
  if (result < inputMin) {
    if (extrapolateLeft === 'identity') {
      return result;
    } else if (extrapolateLeft === 'clamp') {
      result = inputMin;
    } else if (extrapolateLeft === 'extend') {}
  }
  if (result > inputMax) {
    if (extrapolateRight === 'identity') {
      return result;
    } else if (extrapolateRight === 'clamp') {
      result = inputMax;
    } else if (extrapolateRight === 'extend') {}
  }
  if (outputMin === outputMax) {
    return outputMin;
  }
  if (inputMin === inputMax) {
    if (input <= inputMin) {
      return outputMin;
    }
    return outputMax;
  }
  if (inputMin === -Infinity) {
    result = -result;
  } else if (inputMax === Infinity) {
    result = result - inputMin;
  } else {
    result = (result - inputMin) / (inputMax - inputMin);
  }
  result = easing(result);
  if (outputMin === -Infinity) {
    result = -result;
  } else if (outputMax === Infinity) {
    result = result + outputMin;
  } else {
    result = result * (outputMax - outputMin) + outputMin;
  }
  return result;
}
function colorToRgba(input) {
  var normalizedColor = (0, _normalizeColors.default)(input);
  if (normalizedColor === null || typeof normalizedColor !== 'number') {
    return input;
  }
  normalizedColor = normalizedColor || 0;
  var r = (normalizedColor & 0xff000000) >>> 24;
  var g = (normalizedColor & 0x00ff0000) >>> 16;
  var b = (normalizedColor & 0x0000ff00) >>> 8;
  var a = (normalizedColor & 0x000000ff) / 255;
  return "rgba(" + r + ", " + g + ", " + b + ", " + a + ")";
}
var stringShapeRegex = /[+-]?(?:\d+\.?\d*|\.\d+)(?:[eE][+-]?\d+)?/g;
function createInterpolationFromStringOutputRange(config) {
  var outputRange = config.outputRange;
  (0, _invariant.default)(outputRange.length >= 2, 'Bad output range');
  outputRange = outputRange.map(colorToRgba);
  checkPattern(outputRange);
  var outputRanges = outputRange[0].match(stringShapeRegex).map(function () {
    return [];
  });
  outputRange.forEach(function (value) {
    value.match(stringShapeRegex).forEach(function (number, i) {
      outputRanges[i].push(+number);
    });
  });
  var interpolations = outputRange[0].match(stringShapeRegex).map(function (value, i) {
    return createInterpolation((0, _objectSpread2.default)((0, _objectSpread2.default)({}, config), {}, {
      outputRange: outputRanges[i]
    }));
  });
  var shouldRound = isRgbOrRgba(outputRange[0]);
  return function (input) {
    var i = 0;
    return outputRange[0].replace(stringShapeRegex, function () {
      var val = +interpolations[i++](input);
      if (shouldRound) {
        val = i < 4 ? Math.round(val) : Math.round(val * 1000) / 1000;
      }
      return String(val);
    });
  };
}
function isRgbOrRgba(range) {
  return typeof range === 'string' && range.startsWith('rgb');
}
function checkPattern(arr) {
  var pattern = arr[0].replace(stringShapeRegex, '');
  for (var i = 1; i < arr.length; ++i) {
    (0, _invariant.default)(pattern === arr[i].replace(stringShapeRegex, ''), 'invalid pattern ' + arr[0] + ' and ' + arr[i]);
  }
}
function findRange(input, inputRange) {
  var i;
  for (i = 1; i < inputRange.length - 1; ++i) {
    if (inputRange[i] >= input) {
      break;
    }
  }
  return i - 1;
}
function checkValidInputRange(arr) {
  (0, _invariant.default)(arr.length >= 2, 'inputRange must have at least 2 elements');
  var message = 'inputRange must be monotonically non-decreasing ' + String(arr);
  for (var i = 1; i < arr.length; ++i) {
    (0, _invariant.default)(arr[i] >= arr[i - 1], message);
  }
}
function checkInfiniteRange(name, arr) {
  (0, _invariant.default)(arr.length >= 2, name + ' must have at least 2 elements');
  (0, _invariant.default)(arr.length !== 2 || arr[0] !== -Infinity || arr[1] !== Infinity, name + 'cannot be ]-infinity;+infinity[ ' + arr);
}
var AnimatedInterpolation = function (_AnimatedWithChildren2) {
  function AnimatedInterpolation(parent, config) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedInterpolation);
    _this = _callSuper(this, AnimatedInterpolation);
    _this._parent = parent;
    _this._config = config;
    _this._interpolation = createInterpolation(config);
    return _this;
  }
  (0, _inherits2.default)(AnimatedInterpolation, _AnimatedWithChildren2);
  return (0, _createClass2.default)(AnimatedInterpolation, [{
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      this._parent.__makeNative(platformConfig);
      _superPropGet(AnimatedInterpolation, "__makeNative", this, 3)([platformConfig]);
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      var parentValue = this._parent.__getValue();
      (0, _invariant.default)(typeof parentValue === 'number', 'Cannot interpolate an input which is not a number.');
      return this._interpolation(parentValue);
    }
  }, {
    key: "interpolate",
    value: function interpolate(config) {
      return new AnimatedInterpolation(this, config);
    }
  }, {
    key: "__attach",
    value: function __attach() {
      this._parent.__addChild(this);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      this._parent.__removeChild(this);
      _superPropGet(AnimatedInterpolation, "__detach", this, 3)([]);
    }
  }, {
    key: "__transformDataType",
    value: function __transformDataType(range) {
      return range.map(_NativeAnimatedHelper.default.transformDataType);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      if (__DEV__) {
        _NativeAnimatedHelper.default.validateInterpolation(this._config);
      }
      return {
        inputRange: this._config.inputRange,
        outputRange: this.__transformDataType(this._config.outputRange),
        extrapolateLeft: this._config.extrapolateLeft || this._config.extrapolate || 'extend',
        extrapolateRight: this._config.extrapolateRight || this._config.extrapolate || 'extend',
        type: 'interpolation'
      };
    }
  }]);
}(_AnimatedWithChildren.default);
AnimatedInterpolation.__createInterpolation = createInterpolation;
var _default = exports.default = AnimatedInterpolation;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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